(()=>{var e={};e.id=3884,e.ids=[3884],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14341:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>N});var r=t(60687),a=t(43210),i=t(68082),o=t(16189),n=t(99208),d=t(27605),l=t(63442),c=t(45880),m=t(24934),p=t(68988),u=t(39390),h=t(18528),x=t(52581);let f=c.Ik({firstName:c.Yj().min(1),lastName:c.Yj().optional(),email:c.Yj().email(),password:c.Yj().min(6),confirmPassword:c.Yj().min(6)}).refine(e=>e.password===e.confirmPassword,{message:"Passwords do not match",path:["confirmPassword"]});function g(){let{t:e}=(0,i.B)(),s=(0,o.useRouter)(),t=(0,o.useSearchParams)().get("redirect_url")||"/dashboard/analytics";console.log("Redirect URL:",t);let[c,g]=(0,a.useState)(!1),{register:j,handleSubmit:v,formState:{errors:w}}=(0,d.mN)({resolver:(0,l.u)(f)});async function N(r){g(!0);try{let a=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({firstName:r.firstName,lastName:r.lastName,email:r.email,password:r.password})});if(!a.ok){let s=await a.json();x.oR.error(s.message||e("auth.registrationFailed")),g(!1);return}let i=await a.json();console.log("Registration response:",i);let o=await (0,n.Jv)("credentials",{email:r.email,password:r.password,redirect:!1,callbackUrl:t});if(o?.error){x.oR.error(e("auth.signInAfterRegistrationFailed")),g(!1);return}x.oR.success(e("auth.registrationSuccessful")),s.push(t),s.refresh()}catch(s){console.error("Sign up error:",s),x.oR.error(e("common.somethingWentWrong")),g(!1)}}return(0,r.jsxs)("div",{className:"grid gap-6",children:[(0,r.jsx)("form",{onSubmit:v(N),children:(0,r.jsxs)("div",{className:"grid gap-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"grid gap-2",children:[(0,r.jsx)(u.J,{htmlFor:"firstName",children:e("auth.firstName")}),(0,r.jsx)(p.p,{id:"firstName",type:"text",autoCapitalize:"words",autoComplete:"given-name",disabled:c,...j("firstName")}),w.firstName&&(0,r.jsx)("p",{className:"text-sm text-destructive",children:w.firstName.message})]}),(0,r.jsxs)("div",{className:"grid gap-2",children:[(0,r.jsx)(u.J,{htmlFor:"lastName",children:e("auth.lastName")}),(0,r.jsx)(p.p,{id:"lastName",type:"text",autoCapitalize:"words",autoComplete:"family-name",disabled:c,...j("lastName")}),w.lastName&&(0,r.jsx)("p",{className:"text-sm text-destructive",children:w.lastName.message})]})]}),(0,r.jsxs)("div",{className:"grid gap-2",children:[(0,r.jsx)(u.J,{htmlFor:"email",children:e("auth.email")}),(0,r.jsx)(p.p,{id:"email",type:"email",placeholder:"<EMAIL>",autoCapitalize:"none",autoComplete:"email",autoCorrect:"off",disabled:c,...j("email")}),w.email&&(0,r.jsx)("p",{className:"text-sm text-destructive",children:w.email.message})]}),(0,r.jsxs)("div",{className:"grid gap-2",children:[(0,r.jsx)(u.J,{htmlFor:"password",children:e("auth.password")}),(0,r.jsx)(p.p,{id:"password",type:"password",autoCapitalize:"none",autoComplete:"new-password",disabled:c,...j("password")}),w.password&&(0,r.jsx)("p",{className:"text-sm text-destructive",children:w.password.message})]}),(0,r.jsxs)("div",{className:"grid gap-2",children:[(0,r.jsx)(u.J,{htmlFor:"confirmPassword",children:e("auth.confirmPassword")}),(0,r.jsx)(p.p,{id:"confirmPassword",type:"password",autoCapitalize:"none",autoComplete:"new-password",disabled:c,...j("confirmPassword")}),w.confirmPassword&&(0,r.jsx)("p",{className:"text-sm text-destructive",children:w.confirmPassword.message})]}),(0,r.jsxs)(m.$,{type:"submit",disabled:c,children:[c&&(0,r.jsx)(h.F.spinner,{className:"mr-2 h-4 w-4 animate-spin"}),e("auth.signUp")]})]})}),(0,r.jsxs)("div",{className:"text-center text-sm",children:[e("auth.alreadyHaveAccount")," ",(0,r.jsx)(m.$,{variant:"link",className:"px-0",asChild:!0,children:(0,r.jsx)("a",{href:`/sign-in${t?`?redirect_url=${encodeURIComponent(t)}`:""}`,children:e("auth.signIn")})})]})]})}var j=t(7489),v=t(99563);function w(){let{t:e}=(0,i.B)();return(0,r.jsx)("div",{className:"container flex h-screen w-screen flex-col items-center justify-center",children:(0,r.jsxs)("div",{className:"mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]",children:[(0,r.jsxs)("div",{className:"flex flex-col space-y-2 text-center",children:[(0,r.jsx)(h.F.logo,{className:"mx-auto h-6 w-6"}),(0,r.jsx)("h1",{className:"text-2xl font-semibold tracking-tight",children:e("auth.createAccount")}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:e("auth.enterDetailsToCreateAccount")})]}),(0,r.jsx)(g,{}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)(j.c,{}),(0,r.jsx)(v.U,{})]})]})})}function N(){return(0,r.jsx)(a.Suspense,{fallback:(0,r.jsx)("div",{className:"flex h-screen items-center justify-center",children:"Loading..."}),children:(0,r.jsx)(w,{})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26496:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>l});var r=t(65239),a=t(48088),i=t(88170),o=t.n(i),n=t(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let l={children:["",{children:["sign-up",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,26911)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\sign-up\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,60566)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\sign-up\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/sign-up/page",pathname:"/sign-up",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},26911:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\sign-up\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\sign-up\\page.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},76844:(e,s,t)=>{Promise.resolve().then(t.bind(t,26911))},82924:(e,s,t)=>{Promise.resolve().then(t.bind(t,14341))}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[7719,9656,3903,3706,2105,8722,7524],()=>t(26496));module.exports=r})();