{"version": 3, "file": "patch.js", "names": ["Chunk", "_interopRequireWildcard", "require", "<PERSON><PERSON>", "Equal", "_Function", "HashSet", "supervisor", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "OP_EMPTY", "exports", "OP_ADD_SUPERVISOR", "OP_REMOVE_SUPERVISOR", "OP_AND_THEN", "empty", "_tag", "combine", "self", "that", "first", "second", "patch", "patchLoop", "of", "_supervisor", "_patches", "patches", "isNonEmpty", "head", "headNonEmpty", "tailNonEmpty", "zip", "removeSupervisor", "prepend", "equals", "none", "isZip", "left", "right", "toSet", "pipe", "union", "make", "diff", "oldValue", "newValue", "oldSupervisors", "newSupervisors", "added", "difference", "reduce", "removed", "differ"], "sources": ["../../../../src/internal/supervisor/patch.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAF,uBAAA,CAAAC,OAAA;AACA,IAAAE,KAAA,GAAAH,uBAAA,CAAAC,OAAA;AACA,IAAAG,SAAA,GAAAH,OAAA;AACA,IAAAI,OAAA,GAAAL,uBAAA,CAAAC,OAAA;AAEA,IAAAK,UAAA,GAAAN,uBAAA,CAAAC,OAAA;AAA8C,SAAAM,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAR,wBAAAQ,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAK9C;AACO,MAAMW,QAAQ,GAAAC,OAAA,CAAAD,QAAA,GAAG,OAAgB;AAKxC;AACO,MAAME,iBAAiB,GAAAD,OAAA,CAAAC,iBAAA,GAAG,eAAwB;AAKzD;AACO,MAAMC,oBAAoB,GAAAF,OAAA,CAAAE,oBAAA,GAAG,kBAA2B;AAK/D;AACO,MAAMC,WAAW,GAAAH,OAAA,CAAAG,WAAA,GAAG,SAAkB;AA6B7C;;;;;AAKO,MAAMC,KAAK,GAAAJ,OAAA,CAAAI,KAAA,GAAoB;EAAEC,IAAI,EAAEN;AAAQ,CAAE;AAExD;;;;;;AAMO,MAAMO,OAAO,GAAGA,CAACC,IAAqB,EAAEC,IAAqB,KAAqB;EACvF,OAAO;IACLH,IAAI,EAAEF,WAAW;IACjBM,KAAK,EAAEF,IAAI;IACXG,MAAM,EAAEF;GACT;AACH,CAAC;AAED;;;;;AAAAR,OAAA,CAAAM,OAAA,GAAAA,OAAA;AAKO,MAAMK,KAAK,GAAGA,CACnBJ,IAAqB,EACrB7B,UAAsC,KACR;EAC9B,OAAOkC,SAAS,CAAClC,UAAU,EAAEP,KAAK,CAAC0C,EAAE,CAACN,IAAI,CAAC,CAAC;AAC9C,CAAC;AAED;AAAAP,OAAA,CAAAW,KAAA,GAAAA,KAAA;AACA,MAAMC,SAAS,GAAGA,CAChBE,WAAuC,EACvCC,QAAsC,KACR;EAC9B,IAAIrC,UAAU,GAAGoC,WAAW;EAC5B,IAAIE,OAAO,GAAGD,QAAQ;EACtB,OAAO5C,KAAK,CAAC8C,UAAU,CAACD,OAAO,CAAC,EAAE;IAChC,MAAME,IAAI,GAAG/C,KAAK,CAACgD,YAAY,CAACH,OAAO,CAAC;IACxC,QAAQE,IAAI,CAACb,IAAI;MACf,KAAKN,QAAQ;QAAE;UACbiB,OAAO,GAAG7C,KAAK,CAACiD,YAAY,CAACJ,OAAO,CAAC;UACrC;QACF;MACA,KAAKf,iBAAiB;QAAE;UACtBvB,UAAU,GAAGA,UAAU,CAAC2C,GAAG,CAACH,IAAI,CAACxC,UAAU,CAAC;UAC5CsC,OAAO,GAAG7C,KAAK,CAACiD,YAAY,CAACJ,OAAO,CAAC;UACrC;QACF;MACA,KAAKd,oBAAoB;QAAE;UACzBxB,UAAU,GAAG4C,gBAAgB,CAAC5C,UAAU,EAAEwC,IAAI,CAACxC,UAAU,CAAC;UAC1DsC,OAAO,GAAG7C,KAAK,CAACiD,YAAY,CAACJ,OAAO,CAAC;UACrC;QACF;MACA,KAAKb,WAAW;QAAE;UAChBa,OAAO,GAAG7C,KAAK,CAACoD,OAAO,CAACL,IAAI,CAACT,KAAK,CAAC,CAACtC,KAAK,CAACoD,OAAO,CAACL,IAAI,CAACR,MAAM,CAAC,CAACvC,KAAK,CAACiD,YAAY,CAACJ,OAAO,CAAC,CAAC,CAAC;UAC5F;QACF;IACF;EACF;EACA,OAAOtC,UAAU;AACnB,CAAC;AAED;AACA,MAAM4C,gBAAgB,GAAGA,CACvBf,IAAgC,EAChCC,IAAgC,KACF;EAC9B,IAAIjC,KAAK,CAACiD,MAAM,CAACjB,IAAI,EAAEC,IAAI,CAAC,EAAE;IAC5B,OAAO9B,UAAU,CAAC+C,IAAI;EACxB,CAAC,MAAM;IACL,IAAI/C,UAAU,CAACgD,KAAK,CAACnB,IAAI,CAAC,EAAE;MAC1B,OAAOe,gBAAgB,CAACf,IAAI,CAACoB,IAAI,EAAEnB,IAAI,CAAC,CAACa,GAAG,CAACC,gBAAgB,CAACf,IAAI,CAACqB,KAAK,EAAEpB,IAAI,CAAC,CAAC;IAClF,CAAC,MAAM;MACL,OAAOD,IAAI;IACb;EACF;AACF,CAAC;AAED;AACA,MAAMsB,KAAK,GAAItB,IAAgC,IAAiD;EAC9F,IAAIhC,KAAK,CAACiD,MAAM,CAACjB,IAAI,EAAE7B,UAAU,CAAC+C,IAAI,CAAC,EAAE;IACvC,OAAOhD,OAAO,CAAC2B,KAAK,EAAE;EACxB,CAAC,MAAM;IACL,IAAI1B,UAAU,CAACgD,KAAK,CAACnB,IAAI,CAAC,EAAE;MAC1B,OAAO,IAAAuB,cAAI,EAACD,KAAK,CAACtB,IAAI,CAACoB,IAAI,CAAC,EAAElD,OAAO,CAACsD,KAAK,CAACF,KAAK,CAACtB,IAAI,CAACqB,KAAK,CAAC,CAAC,CAAC;IACjE,CAAC,MAAM;MACL,OAAOnD,OAAO,CAACuD,IAAI,CAACzB,IAAI,CAAC;IAC3B;EACF;AACF,CAAC;AAED;AACO,MAAM0B,IAAI,GAAGA,CAClBC,QAAoC,EACpCC,QAAoC,KACjB;EACnB,IAAI5D,KAAK,CAACiD,MAAM,CAACU,QAAQ,EAAEC,QAAQ,CAAC,EAAE;IACpC,OAAO/B,KAAK;EACd;EACA,MAAMgC,cAAc,GAAGP,KAAK,CAACK,QAAQ,CAAC;EACtC,MAAMG,cAAc,GAAGR,KAAK,CAACM,QAAQ,CAAC;EACtC,MAAMG,KAAK,GAAG,IAAAR,cAAI,EAChBO,cAAc,EACd5D,OAAO,CAAC8D,UAAU,CAACH,cAAc,CAAC,EAClC3D,OAAO,CAAC+D,MAAM,CACZpC,KAAwB,EACxB,CAACO,KAAK,EAAEjC,UAAU,KAAK4B,OAAO,CAACK,KAAK,EAAE;IAAEN,IAAI,EAAEJ,iBAAiB;IAAEvB;EAAU,CAAE,CAAC,CAC/E,CACF;EACD,MAAM+D,OAAO,GAAG,IAAAX,cAAI,EAClBM,cAAc,EACd3D,OAAO,CAAC8D,UAAU,CAACF,cAAc,CAAC,EAClC5D,OAAO,CAAC+D,MAAM,CACZpC,KAAwB,EACxB,CAACO,KAAK,EAAEjC,UAAU,KAAK4B,OAAO,CAACK,KAAK,EAAE;IAAEN,IAAI,EAAEH,oBAAoB;IAAExB;EAAU,CAAE,CAAC,CAClF,CACF;EACD,OAAO4B,OAAO,CAACgC,KAAK,EAAEG,OAAO,CAAC;AAChC,CAAC;AAED;AAAAzC,OAAA,CAAAiC,IAAA,GAAAA,IAAA;AACO,MAAMS,MAAM,GAAA1C,OAAA,CAAA0C,MAAA,gBAAGpE,MAAM,CAAC0D,IAAI,CAA8C;EAC7E5B,KAAK;EACLO,KAAK;EACLL,OAAO;EACP2B;CACD,CAAC", "ignoreList": []}