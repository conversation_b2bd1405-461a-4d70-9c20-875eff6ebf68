'use client';

import { useState, useEffect } from 'react';
import { initializeArabicEnvironment, safeLog } from '@/lib/cookieCleanup';

/**
 * Arabic-only language hook for Properties system
 * Pure Arabic interface with enhanced RTL support and cookie cleanup
 */
export function useSimpleLanguage() {
  const [language, setLanguage] = useState<'ar'>('ar');

  // Initialize Arabic-only interface with cleanup
  useEffect(() => {
    // Initialize Arabic environment and clean cookies
    initializeArabicEnvironment();

    safeLog('🏠 Arabic Properties system initialized');
  }, []);

  return {
    language: 'ar' as const,
    setLanguage: () => {}, // No language switching in Arabic-only mode
    isRTL: true,
    isArabicOnly: true,
  };
}
