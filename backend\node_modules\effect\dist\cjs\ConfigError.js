"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.reduceWithContext = exports.prefixed = exports.isUnsupported = exports.isSourceUnavailable = exports.isOr = exports.isMissingDataOnly = exports.isMissingData = exports.isInvalidData = exports.isConfigError = exports.isAnd = exports.Unsupported = exports.SourceUnavailable = exports.Or = exports.MissingData = exports.InvalidData = exports.ConfigErrorTypeId = exports.And = void 0;
var internal = _interopRequireWildcard(require("./internal/configError.js"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
/**
 * @since 2.0.0
 * @category symbols
 */
const ConfigErrorTypeId = exports.ConfigErrorTypeId = internal.ConfigErrorTypeId;
/**
 * @since 2.0.0
 * @category constructors
 */
const And = exports.And = internal.And;
/**
 * @since 2.0.0
 * @category constructors
 */
const Or = exports.Or = internal.Or;
/**
 * @since 2.0.0
 * @category constructors
 */
const MissingData = exports.MissingData = internal.MissingData;
/**
 * @since 2.0.0
 * @category constructors
 */
const InvalidData = exports.InvalidData = internal.InvalidData;
/**
 * @since 2.0.0
 * @category constructors
 */
const SourceUnavailable = exports.SourceUnavailable = internal.SourceUnavailable;
/**
 * @since 2.0.0
 * @category constructors
 */
const Unsupported = exports.Unsupported = internal.Unsupported;
/**
 * Returns `true` if the specified value is a `ConfigError`, `false` otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
const isConfigError = exports.isConfigError = internal.isConfigError;
/**
 * Returns `true` if the specified `ConfigError` is an `And`, `false` otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
const isAnd = exports.isAnd = internal.isAnd;
/**
 * Returns `true` if the specified `ConfigError` is an `Or`, `false` otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
const isOr = exports.isOr = internal.isOr;
/**
 * Returns `true` if the specified `ConfigError` is an `InvalidData`, `false`
 * otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
const isInvalidData = exports.isInvalidData = internal.isInvalidData;
/**
 * Returns `true` if the specified `ConfigError` is an `MissingData`, `false`
 * otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
const isMissingData = exports.isMissingData = internal.isMissingData;
/**
 * Returns `true` if the specified `ConfigError` contains only `MissingData` errors, `false` otherwise.
 *
 * @since 2.0.0
 * @categer getters
 */
const isMissingDataOnly = exports.isMissingDataOnly = internal.isMissingDataOnly;
/**
 * Returns `true` if the specified `ConfigError` is a `SourceUnavailable`,
 * `false` otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
const isSourceUnavailable = exports.isSourceUnavailable = internal.isSourceUnavailable;
/**
 * Returns `true` if the specified `ConfigError` is an `Unsupported`, `false`
 * otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
const isUnsupported = exports.isUnsupported = internal.isUnsupported;
/**
 * @since 2.0.0
 * @category utils
 */
const prefixed = exports.prefixed = internal.prefixed;
/**
 * @since 2.0.0
 * @category folding
 */
const reduceWithContext = exports.reduceWithContext = internal.reduceWithContext;
//# sourceMappingURL=ConfigError.js.map