"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.makeWith = exports.makeEntryStats = exports.makeCacheStats = exports.make = exports.ConsumerCacheTypeId = exports.CacheTypeId = void 0;
var internal = _interopRequireWildcard(require("./internal/cache.js"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
/**
 * @since 2.0.0
 * @category symbols
 */
const CacheTypeId = exports.CacheTypeId = internal.CacheTypeId;
/**
 * @since 3.6.4
 * @category symbols
 */
const ConsumerCacheTypeId = exports.ConsumerCacheTypeId = internal.ConsumerCacheTypeId;
/**
 * Constructs a new cache with the specified capacity, time to live, and
 * lookup function.
 *
 * @since 2.0.0
 * @category constructors
 */
const make = exports.make = internal.make;
/**
 * Constructs a new cache with the specified capacity, time to live, and
 * lookup function, where the time to live can depend on the `Exit` value
 * returned by the lookup function.
 *
 * @since 2.0.0
 * @category constructors
 */
const makeWith = exports.makeWith = internal.makeWith;
/**
 * Constructs a new `CacheStats` from the specified values.
 *
 * @since 2.0.0
 * @category constructors
 */
const makeCacheStats = exports.makeCacheStats = internal.makeCacheStats;
/**
 * Constructs a new `EntryStats` from the specified values.
 *
 * @since 2.0.0
 * @category constructors
 */
const makeEntryStats = exports.makeEntryStats = internal.makeEntryStats;
//# sourceMappingURL=Cache.js.map