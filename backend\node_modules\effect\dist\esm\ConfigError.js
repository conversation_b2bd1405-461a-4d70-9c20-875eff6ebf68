import * as internal from "./internal/configError.js";
/**
 * @since 2.0.0
 * @category symbols
 */
export const ConfigErrorTypeId = internal.ConfigErrorTypeId;
/**
 * @since 2.0.0
 * @category constructors
 */
export const And = internal.And;
/**
 * @since 2.0.0
 * @category constructors
 */
export const Or = internal.Or;
/**
 * @since 2.0.0
 * @category constructors
 */
export const MissingData = internal.MissingData;
/**
 * @since 2.0.0
 * @category constructors
 */
export const InvalidData = internal.InvalidData;
/**
 * @since 2.0.0
 * @category constructors
 */
export const SourceUnavailable = internal.SourceUnavailable;
/**
 * @since 2.0.0
 * @category constructors
 */
export const Unsupported = internal.Unsupported;
/**
 * Returns `true` if the specified value is a `ConfigError`, `false` otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
export const isConfigError = internal.isConfigError;
/**
 * Returns `true` if the specified `ConfigError` is an `And`, `false` otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
export const isAnd = internal.isAnd;
/**
 * Returns `true` if the specified `ConfigError` is an `Or`, `false` otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
export const isOr = internal.isOr;
/**
 * Returns `true` if the specified `ConfigError` is an `InvalidData`, `false`
 * otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
export const isInvalidData = internal.isInvalidData;
/**
 * Returns `true` if the specified `ConfigError` is an `MissingData`, `false`
 * otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
export const isMissingData = internal.isMissingData;
/**
 * Returns `true` if the specified `ConfigError` contains only `MissingData` errors, `false` otherwise.
 *
 * @since 2.0.0
 * @categer getters
 */
export const isMissingDataOnly = internal.isMissingDataOnly;
/**
 * Returns `true` if the specified `ConfigError` is a `SourceUnavailable`,
 * `false` otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
export const isSourceUnavailable = internal.isSourceUnavailable;
/**
 * Returns `true` if the specified `ConfigError` is an `Unsupported`, `false`
 * otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
export const isUnsupported = internal.isUnsupported;
/**
 * @since 2.0.0
 * @category utils
 */
export const prefixed = internal.prefixed;
/**
 * @since 2.0.0
 * @category folding
 */
export const reduceWithContext = internal.reduceWithContext;
//# sourceMappingURL=ConfigError.js.map