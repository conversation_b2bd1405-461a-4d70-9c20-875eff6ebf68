{"version": 3, "file": "node.d.ts", "sourceRoot": "", "sources": ["../../src/node.ts"], "names": [], "mappings": ";;;AACA,OAAO,KAAK,EAAE,mBAAmB,EAAE,MAAM,WAAW,CAAA;AACpD,OAAO,KAAK,EAAE,MAAM,YAAY,CAAA;AAChC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,aAAa,CAAA;AAE9C,YAAY,EAAE,cAAc,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAA;AAC1D,OAAO,EAAE,WAAW,EAAE,MAAM,YAAY,CAAA;AAExC,MAAM,MAAM,IAAI,GAAG,KAAK,GAAG,UAAU,CAAA;AAErC,MAAM,WAAW,KAAK;IACpB,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAA;IACtB,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAA;IAC1B,QAAQ,CAAC,KAAK,EAAE,UAAU,CAAA;CAC3B;AAED,MAAM,WAAW,gBAAiB,SAAQ,MAAM;IAC9C,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,qBAAqB,CAAC,IAAI,CAAC,CAAA;IAErD,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI,GAAG,IAAI,CAAA;IAC1D,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,IAAI,EAAE,UAAU,KAAK,IAAI,GAAG,IAAI,CAAA;IAC7D,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,IAAI,GAAG,IAAI,CAAA;IAC9C,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,KAAK,IAAI,GAAG,IAAI,CAAA;IACvD,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,IAAI,GAAG,IAAI,CAAA;IAC9C,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,IAAI,GAAG,IAAI,CAAA;IAC5C,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,cAAc,KAAK,IAAI,GAAG,IAAI,CAAA;IACpE,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,IAAI,GAAG,IAAI,CAAA;IAC/C,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,IAAI,GAAG,IAAI,CAAA;IAC9C,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,GAAG,EAAE,QAAQ,KAAK,IAAI,GAAG,IAAI,CAAA;IAC1D,EAAE,CAAC,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,IAAI,GAAG,IAAI,CAAA;IACjD,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,IAAI,GAAG,IAAI,CAAA;IAC/C,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,GAAG,EAAE,QAAQ,KAAK,IAAI,GAAG,IAAI,CAAA;IAC5D,EAAE,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,EAAE,QAAQ,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,IAAI,GAAG,IAAI,CAAA;IAEpE,IAAI,CAAC,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,IAAI,CAAA;CACjC;AAED,MAAM,MAAM,UAAU,GAAG,IAAI,CAAC,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC,GAAG;IACxD,QAAQ,CAAC,OAAO,EAAE,mBAAmB,CAAA;CACtC,CAAA;AAED,qBAAa,gBAAiB,SAAQ,MAAM;IAC1C,OAAO,CAAC,OAAO,CAAW;IAC1B,SAAS,UAAO;IAChB,OAAO,CAAC,cAAc,CAA0B;gBAEpC,MAAM,EAAE,UAAU;IA6B9B,OAAO;IASP,KAAK,CAAC,KAAK,EAAE,MAAM;IAEnB,MAAM,CACJ,KAAK,EAAE,GAAG,EACV,QAAQ,EAAE,cAAc,EACxB,QAAQ,EAAE,CAAC,KAAK,CAAC,EAAE,KAAK,GAAG,IAAI,GAAG,SAAS,KAAK,IAAI,GACnD,IAAI;IAWP,MAAM,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,EAAE,KAAK,GAAG,IAAI,GAAG,SAAS,KAAK,IAAI,GAAG,IAAI;CAInE;AAED,eAAO,MAAM,IAAI,WAAY,UAAU,KAAG,gBACZ,CAAA;AAE9B,qBAAa,UAAW,SAAQ,QAAQ;IAIpC,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,QAAQ;IAC1B,OAAO,CAAC,OAAO;IAJjB,QAAQ,CAAC,IAAI,UAAS;IACtB,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAA;gBAEd,IAAI,EAAE,EAAE,CAAC,QAAQ,EAClB,OAAO,EAAE,gBAAgB;IAKnC,KAAK,CAAC,KAAK,EAAE,MAAM;CAKpB"}