import { HttpClientResponse, HttpClientError, HttpBody } from '@effect/platform';
import * as Config from 'effect/Config';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';
import * as LogLevel from 'effect/LogLevel';
import { UploadThingError } from '@uploadthing/shared';

declare const withMinimalLogLevel: Layer.Layer<never, UploadThingError<{
    message: string;
}>, never>;
declare const LogFormat: Config.Config<"json" | "logFmt" | "structured" | "pretty">;
type LogFormat = Config.Config.Success<typeof LogFormat>;
declare const withLogFormat: Layer.Layer<never, UploadThingError<{
    message: string;
}>, never>;
type HttpClientResponseMixinMethod = "json" | "text" | "arrayBuffer" | "None";
declare const logHttpClientResponse: (message: string, opts?: {
    /** Level to log on, default "Debug" */
    level?: LogLevel.Literal;
    /** What body mixin to use to get the response body, default "json" */
    mixin?: HttpClientResponseMixinMethod;
}) => (response: HttpClientResponse.HttpClientResponse) => Effect.Effect<void, HttpClientError.ResponseError, never>;
declare const logHttpClientError: (message: string) => (err: HttpClientError.HttpClientError | HttpBody.HttpBodyError) => Effect.Effect<void, HttpClientError.ResponseError, never>;

export { LogFormat, logHttpClientError, logHttpClientResponse, withLogFormat, withMinimalLogLevel };
