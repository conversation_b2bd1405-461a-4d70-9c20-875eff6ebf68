var platform = require('@effect/platform');
var Config = require('effect/Config');
var Context = require('effect/Context');
var Effect = require('effect/Effect');
var Match = require('effect/Match');
var Redacted = require('effect/Redacted');
var Schema = require('effect/Schema');
var shared = require('@uploadthing/shared');
var config_cjs = require('./config.cjs');
var deprecations_cjs = require('./deprecations.cjs');
var errorFormatter_cjs = require('./error-formatter.cjs');
var jsonl_cjs = require('./jsonl.cjs');
var logger_cjs = require('./logger.cjs');
var parser_cjs = require('./parser.cjs');
var routeConfig_cjs = require('./route-config.cjs');
var runtime_cjs = require('./runtime.cjs');
var sharedSchemas_cjs = require('./shared-schemas.cjs');
var types_cjs = require('./types.cjs');

function _interopNamespace(e) {
  if (e && e.__esModule) return e;
  var n = Object.create(null);
  if (e) {
    Object.keys(e).forEach(function (k) {
      if (k !== 'default') {
        var d = Object.getOwnPropertyDescriptor(e, k);
        Object.defineProperty(n, k, d.get ? d : {
          enumerable: true,
          get: function () { return e[k]; }
        });
      }
    });
  }
  n.default = e;
  return n;
}

var Config__namespace = /*#__PURE__*/_interopNamespace(Config);
var Context__namespace = /*#__PURE__*/_interopNamespace(Context);
var Effect__namespace = /*#__PURE__*/_interopNamespace(Effect);
var Match__namespace = /*#__PURE__*/_interopNamespace(Match);
var Redacted__namespace = /*#__PURE__*/_interopNamespace(Redacted);
var Schema__namespace = /*#__PURE__*/_interopNamespace(Schema);

var version = "7.7.2";

class AdapterArguments extends Context__namespace.Tag("uploadthing/AdapterArguments")() {
}
/**
 * Create a request handler adapter for any framework or server library.
 * Refer to the existing adapters for examples on how to use this function.
 * @public
 *
 * @param makeAdapterArgs - Function that takes the args from your framework and returns an Effect that resolves to the adapter args.
 * These args are passed to the `.middleware`, `.onUploadComplete`, and `.onUploadError` hooks.
 * @param toRequest - Function that takes the args from your framework and returns an Effect that resolves to a web Request object.
 * @param opts - The router config and other options that are normally passed to `createRequestHandler` of official adapters
 * @param beAdapter - [Optional] The adapter name of the adapter, used for telemetry purposes
 * @returns A function that takes the args from your framework and returns a promise that resolves to a Response object.
 */ const makeAdapterHandler = (makeAdapterArgs, toRequest, opts, beAdapter)=>{
    const managed = runtime_cjs.makeRuntime(opts.config?.fetch, opts.config);
    const handle = Effect__namespace.promise(()=>managed.runtime().then(platform.HttpApp.toWebHandlerRuntime));
    const app = (...args)=>Effect__namespace.map(Effect__namespace.promise(()=>managed.runPromise(createRequestHandler(opts, beAdapter ?? "custom"))), Effect__namespace.provideServiceEffect(AdapterArguments, makeAdapterArgs(...args)));
    return async (...args)=>{
        const result = await handle.pipe(Effect__namespace.ap(app(...args)), Effect__namespace.ap(toRequest(...args)), Effect__namespace.withLogSpan("requestHandler"), managed.runPromise);
        return result;
    };
};
const createRequestHandler = (opts, beAdapter)=>Effect__namespace.gen(function*() {
        const isDevelopment = yield* config_cjs.IsDevelopment;
        const routerConfig = yield* routeConfig_cjs.extractRouterConfig(opts.router);
        const handleDaemon = (()=>{
            if (opts.config?.handleDaemonPromise) {
                return opts.config.handleDaemonPromise;
            }
            return isDevelopment ? "void" : "await";
        })();
        if (isDevelopment && handleDaemon === "await") {
            return yield* new shared.UploadThingError({
                code: "INVALID_SERVER_CONFIG",
                message: 'handleDaemonPromise: "await" is forbidden in development.'
            });
        }
        const GET = Effect__namespace.gen(function*() {
            return yield* platform.HttpServerResponse.json(routerConfig);
        });
        const POST = Effect__namespace.gen(function*() {
            const { "uploadthing-hook": uploadthingHook, "x-uploadthing-package": fePackage, "x-uploadthing-version": clientVersion } = yield* platform.HttpServerRequest.schemaHeaders(Schema__namespace.Struct({
                "uploadthing-hook": sharedSchemas_cjs.UploadThingHook.pipe(Schema__namespace.optional),
                "x-uploadthing-package": Schema__namespace.String.pipe(Schema__namespace.optionalWith({
                    default: ()=>"unknown"
                })),
                "x-uploadthing-version": Schema__namespace.String.pipe(Schema__namespace.optionalWith({
                    default: ()=>version
                }))
            }));
            if (clientVersion !== version) {
                const serverVersion = version;
                yield* Effect__namespace.logWarning("Client version mismatch. Things may not work as expected, please sync your versions to ensure compatibility.").pipe(Effect__namespace.annotateLogs({
                    clientVersion,
                    serverVersion
                }));
            }
            const { slug, actionType } = yield* platform.HttpRouter.schemaParams(Schema__namespace.Struct({
                actionType: sharedSchemas_cjs.ActionType.pipe(Schema__namespace.optional),
                slug: Schema__namespace.String
            }));
            const uploadable = opts.router[slug];
            if (!uploadable) {
                const msg = `No file route found for slug ${slug}`;
                yield* Effect__namespace.logError(msg);
                return yield* new shared.UploadThingError({
                    code: "NOT_FOUND",
                    message: msg
                });
            }
            const { body, fiber } = yield* Match__namespace.value({
                actionType,
                uploadthingHook
            }).pipe(Match__namespace.when({
                actionType: "upload",
                uploadthingHook: undefined
            }, ()=>handleUploadAction({
                    uploadable,
                    fePackage,
                    beAdapter,
                    slug
                })), Match__namespace.when({
                actionType: undefined,
                uploadthingHook: "callback"
            }, ()=>handleCallbackRequest({
                    uploadable,
                    fePackage,
                    beAdapter
                })), Match__namespace.when({
                actionType: undefined,
                uploadthingHook: "error"
            }, ()=>handleErrorRequest({
                    uploadable
                })), Match__namespace.orElse(()=>Effect__namespace.succeed({
                    body: null,
                    fiber: null
                })));
            if (fiber) {
                yield* Effect__namespace.logDebug("Running fiber as daemon").pipe(Effect__namespace.annotateLogs("handleDaemon", handleDaemon));
                if (handleDaemon === "void") ; else if (handleDaemon === "await") {
                    yield* fiber.await;
                } else if (typeof handleDaemon === "function") {
                    handleDaemon(Effect__namespace.runPromise(fiber.await));
                }
            }
            yield* Effect__namespace.logDebug("Sending response").pipe(Effect__namespace.annotateLogs("body", body));
            return yield* platform.HttpServerResponse.json(body);
        }).pipe(Effect__namespace.catchTags({
            ParseError: (e)=>platform.HttpServerResponse.json(errorFormatter_cjs.formatError(new shared.UploadThingError({
                    code: "BAD_REQUEST",
                    message: "Invalid input",
                    cause: e.message
                }), opts.router), {
                    status: 400
                }),
            UploadThingError: (e)=>// eslint-disable-next-line @typescript-eslint/no-unsafe-argument
                platform.HttpServerResponse.json(errorFormatter_cjs.formatError(e, opts.router), {
                    status: shared.getStatusCodeFromError(e)
                })
        }));
        const appendResponseHeaders = Effect__namespace.map(platform.HttpServerResponse.setHeader("x-uploadthing-version", version));
        return platform.HttpRouter.empty.pipe(platform.HttpRouter.get("*", GET), platform.HttpRouter.post("*", POST), platform.HttpRouter.use(appendResponseHeaders));
    }).pipe(Effect__namespace.withLogSpan("createRequestHandler"));
const handleErrorRequest = (opts)=>Effect__namespace.gen(function*() {
        const { uploadable } = opts;
        const request = yield* platform.HttpServerRequest.HttpServerRequest;
        const { apiKey } = yield* config_cjs.UTToken;
        const verified = yield* shared.verifySignature((yield* request.text), request.headers["x-uploadthing-signature"] ?? null, apiKey);
        yield* Effect__namespace.logDebug(`Signature verified: ${verified}`);
        if (!verified) {
            yield* Effect__namespace.logError("Invalid signature");
            return yield* new shared.UploadThingError({
                code: "BAD_REQUEST",
                message: "Invalid signature"
            });
        }
        const requestInput = yield* platform.HttpServerRequest.schemaBodyJson(Schema__namespace.Struct({
            fileKey: Schema__namespace.String,
            error: Schema__namespace.String
        }));
        yield* Effect__namespace.logDebug("Handling error callback request with input:").pipe(Effect__namespace.annotateLogs("json", requestInput));
        const adapterArgs = yield* AdapterArguments;
        const fiber = yield* Effect__namespace.tryPromise({
            try: async ()=>uploadable.onUploadError({
                    ...adapterArgs,
                    error: new shared.UploadThingError({
                        code: "UPLOAD_FAILED",
                        message: `Upload failed for ${requestInput.fileKey}: ${requestInput.error}`
                    }),
                    fileKey: requestInput.fileKey
                }),
            catch: (error)=>new shared.UploadThingError({
                    code: "INTERNAL_SERVER_ERROR",
                    message: "Failed to run onUploadError",
                    cause: error
                })
        }).pipe(Effect__namespace.tapError((error)=>Effect__namespace.logError("Failed to run onUploadError. You probably shouldn't be throwing errors here.").pipe(Effect__namespace.annotateLogs("error", error)))).pipe(Effect__namespace.ignoreLogged, Effect__namespace.forkDaemon);
        return {
            body: null,
            fiber
        };
    }).pipe(Effect__namespace.withLogSpan("handleErrorRequest"));
const handleCallbackRequest = (opts)=>Effect__namespace.gen(function*() {
        const { uploadable, fePackage, beAdapter } = opts;
        const request = yield* platform.HttpServerRequest.HttpServerRequest;
        const { apiKey } = yield* config_cjs.UTToken;
        const verified = yield* shared.verifySignature((yield* request.text), request.headers["x-uploadthing-signature"] ?? null, apiKey);
        yield* Effect__namespace.logDebug(`Signature verified: ${verified}`);
        if (!verified) {
            yield* Effect__namespace.logError("Invalid signature");
            return yield* new shared.UploadThingError({
                code: "BAD_REQUEST",
                message: "Invalid signature"
            });
        }
        const requestInput = yield* platform.HttpServerRequest.schemaBodyJson(Schema__namespace.Struct({
            status: Schema__namespace.String,
            file: sharedSchemas_cjs.UploadedFileData,
            origin: Schema__namespace.String,
            metadata: Schema__namespace.Record({
                key: Schema__namespace.String,
                value: Schema__namespace.Unknown
            })
        }));
        yield* Effect__namespace.logDebug("Handling callback request with input:").pipe(Effect__namespace.annotateLogs("json", requestInput));
        /**
     * Run `.onUploadComplete` as a daemon to prevent the
     * request from UT to potentially timeout.
     */ const fiber = yield* Effect__namespace.gen(function*() {
            const adapterArgs = yield* AdapterArguments;
            const serverData = yield* Effect__namespace.tryPromise({
                try: async ()=>uploadable.onUploadComplete({
                        ...adapterArgs,
                        file: {
                            ...requestInput.file,
                            get url () {
                                deprecations_cjs.logDeprecationWarning("`file.url` is deprecated and will be removed in uploadthing v9. Use `file.ufsUrl` instead.");
                                return requestInput.file.url;
                            },
                            get appUrl () {
                                deprecations_cjs.logDeprecationWarning("`file.appUrl` is deprecated and will be removed in uploadthing v9. Use `file.ufsUrl` instead.");
                                return requestInput.file.appUrl;
                            }
                        },
                        metadata: requestInput.metadata
                    }),
                catch: (error)=>new shared.UploadThingError({
                        code: "INTERNAL_SERVER_ERROR",
                        message: "Failed to run onUploadComplete. You probably shouldn't be throwing errors here.",
                        cause: error
                    })
            });
            const payload = {
                fileKey: requestInput.file.key,
                callbackData: serverData ?? null
            };
            yield* Effect__namespace.logDebug("'onUploadComplete' callback finished. Sending response to UploadThing:").pipe(Effect__namespace.annotateLogs("callbackData", payload));
            const httpClient = (yield* platform.HttpClient.HttpClient).pipe(platform.HttpClient.filterStatusOk);
            yield* platform.HttpClientRequest.post(`/callback-result`).pipe(platform.HttpClientRequest.prependUrl(requestInput.origin), platform.HttpClientRequest.setHeaders({
                "x-uploadthing-api-key": Redacted__namespace.value(apiKey),
                "x-uploadthing-version": version,
                "x-uploadthing-be-adapter": beAdapter,
                "x-uploadthing-fe-package": fePackage
            }), platform.HttpClientRequest.bodyJson(payload), Effect__namespace.flatMap(httpClient.execute), Effect__namespace.tapError(logger_cjs.logHttpClientError("Failed to register callback result")), Effect__namespace.flatMap(platform.HttpClientResponse.schemaBodyJson(sharedSchemas_cjs.CallbackResultResponse)), Effect__namespace.tap(Effect__namespace.log("Sent callback result to UploadThing")), Effect__namespace.scoped);
        }).pipe(Effect__namespace.ignoreLogged, Effect__namespace.forkDaemon);
        return {
            body: null,
            fiber
        };
    }).pipe(Effect__namespace.withLogSpan("handleCallbackRequest"));
const runRouteMiddleware = (opts)=>Effect__namespace.gen(function*() {
        const { json: { files, input }, uploadable } = opts;
        yield* Effect__namespace.logDebug("Running middleware");
        const adapterArgs = yield* AdapterArguments;
        const metadata = yield* Effect__namespace.tryPromise({
            try: async ()=>uploadable.middleware({
                    ...adapterArgs,
                    input,
                    files
                }),
            catch: (error)=>error instanceof shared.UploadThingError ? error : new shared.UploadThingError({
                    code: "INTERNAL_SERVER_ERROR",
                    message: "Failed to run middleware",
                    cause: error
                })
        });
        if (metadata[types_cjs.UTFiles] && metadata[types_cjs.UTFiles].length !== files.length) {
            const msg = `Expected files override to have the same length as original files, got ${metadata[types_cjs.UTFiles].length} but expected ${files.length}`;
            yield* Effect__namespace.logError(msg);
            return yield* new shared.UploadThingError({
                code: "BAD_REQUEST",
                message: "Files override must have the same length as files",
                cause: msg
            });
        }
        // Attach customIds from middleware to the files
        const filesWithCustomIds = yield* Effect__namespace.forEach(files, (file, idx)=>Effect__namespace.gen(function*() {
                const theirs = metadata[types_cjs.UTFiles]?.[idx];
                if (theirs && theirs.size !== file.size) {
                    yield* Effect__namespace.logWarning("File size mismatch. Reverting to original size");
                }
                return {
                    name: theirs?.name ?? file.name,
                    size: file.size,
                    type: file.type,
                    customId: theirs?.customId,
                    lastModified: theirs?.lastModified ?? Date.now()
                };
            }));
        return {
            metadata,
            filesWithCustomIds,
            preferredRegion: metadata[types_cjs.UTRegion]
        };
    }).pipe(Effect__namespace.withLogSpan("runRouteMiddleware"));
const handleUploadAction = (opts)=>Effect__namespace.gen(function*() {
        const httpClient = (yield* platform.HttpClient.HttpClient).pipe(platform.HttpClient.filterStatusOk);
        const { uploadable, fePackage, beAdapter, slug } = opts;
        const json = yield* platform.HttpServerRequest.schemaBodyJson(sharedSchemas_cjs.UploadActionPayload);
        yield* Effect__namespace.logDebug("Handling upload request").pipe(Effect__namespace.annotateLogs("json", json));
        // validate the input
        yield* Effect__namespace.logDebug("Parsing user input");
        const parsedInput = yield* Effect__namespace.tryPromise({
            try: ()=>parser_cjs.getParseFn(uploadable.inputParser)(json.input),
            catch: (error)=>new shared.UploadThingError({
                    code: "BAD_REQUEST",
                    message: "Invalid input",
                    cause: error
                })
        });
        yield* Effect__namespace.logDebug("Input parsed successfully").pipe(Effect__namespace.annotateLogs("input", parsedInput));
        const { metadata, filesWithCustomIds, preferredRegion } = yield* runRouteMiddleware({
            json: {
                input: parsedInput,
                files: json.files
            },
            uploadable
        });
        yield* Effect__namespace.logDebug("Parsing route config").pipe(Effect__namespace.annotateLogs("routerConfig", uploadable.routerConfig));
        const parsedConfig = yield* shared.fillInputRouteConfig(uploadable.routerConfig).pipe(Effect__namespace.catchTag("InvalidRouteConfig", (err)=>new shared.UploadThingError({
                code: "BAD_REQUEST",
                message: "Invalid route config",
                cause: err
            })));
        yield* Effect__namespace.logDebug("Route config parsed successfully").pipe(Effect__namespace.annotateLogs("routeConfig", parsedConfig));
        yield* Effect__namespace.logDebug("Validating files meet the config requirements").pipe(Effect__namespace.annotateLogs("files", json.files));
        yield* routeConfig_cjs.assertFilesMeetConfig(json.files, parsedConfig).pipe(Effect__namespace.mapError((e)=>new shared.UploadThingError({
                code: "BAD_REQUEST",
                message: `Invalid config: ${e._tag}`,
                cause: "reason" in e ? e.reason : e.message
            })));
        yield* Effect__namespace.logDebug("Files validated.");
        const fileUploadRequests = yield* Effect__namespace.forEach(filesWithCustomIds, (file)=>Effect__namespace.map(shared.matchFileType(file, shared.objectKeys(parsedConfig)), (type)=>({
                    name: file.name,
                    size: file.size,
                    type: file.type || type,
                    lastModified: file.lastModified,
                    customId: file.customId,
                    contentDisposition: parsedConfig[type]?.contentDisposition ?? "inline",
                    acl: parsedConfig[type]?.acl
                }))).pipe(Effect__namespace.catchTags({
            /** Shouldn't happen since config is validated above so just dying is fine I think */ InvalidFileType: (e)=>Effect__namespace.die(e),
            UnknownFileType: (e)=>Effect__namespace.die(e)
        }));
        const routeOptions = uploadable.routeOptions;
        const { apiKey, appId } = yield* config_cjs.UTToken;
        const ingestUrl = yield* config_cjs.IngestUrl(preferredRegion);
        const isDev = yield* config_cjs.IsDevelopment;
        yield* Effect__namespace.logDebug("Generating presigned URLs").pipe(Effect__namespace.annotateLogs("fileUploadRequests", fileUploadRequests), Effect__namespace.annotateLogs("ingestUrl", ingestUrl));
        const presignedUrls = yield* Effect__namespace.forEach(fileUploadRequests, (file)=>Effect__namespace.gen(function*() {
                const key = yield* shared.generateKey(file, appId, routeOptions.getFileHashParts);
                const url = yield* shared.generateSignedURL(`${ingestUrl}/${key}`, apiKey, {
                    ttlInSeconds: routeOptions.presignedURLTTL,
                    data: {
                        "x-ut-identifier": appId,
                        "x-ut-file-name": file.name,
                        "x-ut-file-size": file.size,
                        "x-ut-file-type": file.type,
                        "x-ut-slug": slug,
                        "x-ut-custom-id": file.customId,
                        "x-ut-content-disposition": file.contentDisposition,
                        "x-ut-acl": file.acl
                    }
                });
                return {
                    url,
                    key
                };
            }), {
            concurrency: "unbounded"
        });
        const serverReq = yield* platform.HttpServerRequest.HttpServerRequest;
        const requestUrl = yield* platform.HttpServerRequest.toURL(serverReq);
        const devHookRequest = yield* Config__namespace.string("callbackUrl").pipe(Config__namespace.withDefault(requestUrl.origin + requestUrl.pathname), Effect__namespace.map((url)=>platform.HttpClientRequest.post(url).pipe(platform.HttpClientRequest.appendUrlParam("slug", slug))));
        const metadataRequest = platform.HttpClientRequest.post("/route-metadata").pipe(platform.HttpClientRequest.prependUrl(ingestUrl), platform.HttpClientRequest.setHeaders({
            "x-uploadthing-api-key": Redacted__namespace.value(apiKey),
            "x-uploadthing-version": version,
            "x-uploadthing-be-adapter": beAdapter,
            "x-uploadthing-fe-package": fePackage
        }), platform.HttpClientRequest.bodyJson({
            fileKeys: presignedUrls.map(({ key })=>key),
            metadata: metadata,
            isDev,
            callbackUrl: devHookRequest.url,
            callbackSlug: slug,
            awaitServerData: routeOptions.awaitServerData ?? true
        }), Effect__namespace.flatMap(httpClient.execute));
        const handleDevStreamError = Effect__namespace.fn("handleDevStreamError")(function*(err, chunk) {
            const schema = Schema__namespace.parseJson(Schema__namespace.Struct({
                file: sharedSchemas_cjs.UploadedFileData
            }));
            const parsedChunk = yield* Schema__namespace.decodeUnknown(schema)(chunk);
            const key = parsedChunk.file.key;
            yield* Effect__namespace.logError("Failed to forward callback request from dev stream").pipe(Effect__namespace.annotateLogs({
                fileKey: key,
                error: err.message
            }));
            const httpResponse = yield* platform.HttpClientRequest.post("/callback-result").pipe(platform.HttpClientRequest.prependUrl(ingestUrl), platform.HttpClientRequest.setHeaders({
                "x-uploadthing-api-key": Redacted__namespace.value(apiKey),
                "x-uploadthing-version": version,
                "x-uploadthing-be-adapter": beAdapter,
                "x-uploadthing-fe-package": fePackage
            }), platform.HttpClientRequest.bodyJson({
                fileKey: key,
                error: `Failed to forward callback request from dev stream: ${err.message}`
            }), Effect__namespace.flatMap(httpClient.execute));
            yield* logger_cjs.logHttpClientResponse("Reported callback error to UploadThing")(httpResponse);
        });
        // Send metadata to UT server (non blocking as a daemon)
        // In dev, keep the stream open and simulate the callback requests as
        // files complete uploading
        const fiber = yield* Effect__namespace.if(isDev, {
            onTrue: ()=>metadataRequest.pipe(Effect__namespace.tapBoth({
                    onSuccess: logger_cjs.logHttpClientResponse("Registered metadata", {
                        mixin: "None"
                    }),
                    onFailure: logger_cjs.logHttpClientError("Failed to register metadata")
                }), platform.HttpClientResponse.stream, jsonl_cjs.handleJsonLineStream(sharedSchemas_cjs.MetadataFetchStreamPart, (chunk)=>devHookRequest.pipe(platform.HttpClientRequest.setHeaders({
                        "uploadthing-hook": chunk.hook,
                        "x-uploadthing-signature": chunk.signature
                    }), platform.HttpClientRequest.setBody(platform.HttpBody.text(chunk.payload, "application/json")), httpClient.execute, Effect__namespace.tap(logger_cjs.logHttpClientResponse("Successfully forwarded callback request from dev stream")), Effect__namespace.catchTag("ResponseError", (err)=>handleDevStreamError(err, chunk.payload)), Effect__namespace.annotateLogs(chunk), Effect__namespace.asVoid, Effect__namespace.ignoreLogged, Effect__namespace.scoped))),
            onFalse: ()=>metadataRequest.pipe(Effect__namespace.tapBoth({
                    onSuccess: logger_cjs.logHttpClientResponse("Registered metadata"),
                    onFailure: logger_cjs.logHttpClientError("Failed to register metadata")
                }), Effect__namespace.flatMap(platform.HttpClientResponse.schemaBodyJson(sharedSchemas_cjs.MetadataFetchResponse)), Effect__namespace.scoped)
        }).pipe(Effect__namespace.forkDaemon);
        const presigneds = presignedUrls.map((p, i)=>({
                url: p.url,
                key: p.key,
                name: fileUploadRequests[i].name,
                customId: fileUploadRequests[i].customId ?? null
            }));
        yield* Effect__namespace.logInfo("Sending presigned URLs to client").pipe(Effect__namespace.annotateLogs("presignedUrls", presigneds));
        return {
            body: presigneds,
            fiber
        };
    }).pipe(Effect__namespace.withLogSpan("handleUploadAction"));

exports.AdapterArguments = AdapterArguments;
exports.createRequestHandler = createRequestHandler;
exports.makeAdapterHandler = makeAdapterHandler;
