(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5538],{284:(e,t,s)=>{"use strict";s.d(t,{ME:()=>a,SX:()=>i,jz:()=>n});var r=s(31886);let a=async()=>{try{return await r.A.get("/marketing/campaigns")}catch(e){throw console.error("Error fetching campaigns:",e),e}},n=async e=>{try{return await r.A.get("/marketing/campaigns/".concat(e))}catch(t){throw console.error("Error fetching campaign with ID ".concat(e,":"),t),t}},i=async(e,t)=>{try{return await r.A.put("/marketing/campaigns/".concat(e),t)}catch(t){throw console.error("Error updating campaign with ID ".concat(e,":"),t),t}}},471:(e,t,s)=>{"use strict";s.d(t,{Lf:()=>a,UU:()=>n,zO:()=>i});var r=s(31886);let a=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50;try{return await r.A.get("/clients?page=".concat(e,"&limit=").concat(t))}catch(e){throw console.error("Error fetching clients:",e),e}},n=async e=>{try{return await r.A.post("/clients",e)}catch(e){throw console.error("Error creating client:",e),e}},i=async(e,t)=>{try{return await r.A.put("/clients/".concat(e),t)}catch(t){throw console.error("Error updating client with ID ".concat(e,":"),t),t}}},3081:(e,t,s)=>{"use strict";s.d(t,{QAPairsDataTable:()=>x});var r=s(95155),a=s(12115),n=s(88524),i=s(67133),l=s(97168),o=s(51154),c=s(5623),d=s(13717),u=s(74126),f=s(88145),h=s(31886);let m=async()=>{try{return await h.A.get("/qa-pairs")}catch(e){return console.error("Error fetching QA pairs:",e),[]}};function x(){let[e,t]=(0,a.useState)([]),[s,h]=(0,a.useState)(!0),[x,p]=(0,a.useState)(null);return((0,a.useEffect)(()=>{(async()=>{try{h(!0);let e=await m();t(e)}catch(e){console.error("Error fetching QA pairs:",e),p("Failed to load QA pairs. Please try again later.")}finally{h(!1)}})()},[]),s)?(0,r.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,r.jsx)(o.A,{className:"h-8 w-8 animate-spin text-primary"})}):x?(0,r.jsx)("div",{className:"text-center py-8 text-red-500",children:(0,r.jsx)("p",{children:x})}):(0,r.jsx)("div",{className:"rounded-md border",children:(0,r.jsxs)(n.XI,{children:[(0,r.jsx)(n.A0,{children:(0,r.jsxs)(n.Hj,{children:[(0,r.jsx)(n.nd,{children:"Question"}),(0,r.jsx)(n.nd,{children:"Answer"}),(0,r.jsx)(n.nd,{children:"Category"}),(0,r.jsx)(n.nd,{className:"w-[80px]"})]})}),(0,r.jsx)(n.BF,{children:0===e.length?(0,r.jsx)(n.Hj,{children:(0,r.jsx)(n.nA,{colSpan:4,className:"text-center py-8 text-muted-foreground",children:"No QA pairs found"})}):e.map(e=>(0,r.jsxs)(n.Hj,{children:[(0,r.jsx)(n.nA,{className:"font-medium max-w-[300px] truncate",children:e.question}),(0,r.jsx)(n.nA,{className:"max-w-[400px] truncate",children:e.answer}),(0,r.jsx)(n.nA,{children:(0,r.jsx)(f.E,{variant:"outline",children:e.category})}),(0,r.jsx)(n.nA,{children:(0,r.jsxs)(i.rI,{children:[(0,r.jsx)(i.ty,{asChild:!0,children:(0,r.jsxs)(l.$,{variant:"ghost",className:"h-8 w-8 p-0",children:[(0,r.jsx)("span",{className:"sr-only",children:"Open menu"}),(0,r.jsx)(c.A,{className:"h-4 w-4"})]})}),(0,r.jsxs)(i.SQ,{align:"end",children:[(0,r.jsxs)(i._2,{children:[(0,r.jsx)(d.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Edit"})]}),(0,r.jsxs)(i._2,{className:"text-red-600",children:[(0,r.jsx)(u.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Delete"})]})]})]})})]},e.id))})]})})}},11775:(e,t,s)=>{"use strict";s.d(t,{CampaignsDataTable:()=>h});var r=s(95155),a=s(12115),n=s(88524),i=s(67133),l=s(97168),o=s(51154),c=s(5623),d=s(92657),u=s(88145),f=s(284);function h(){let[e,t]=(0,a.useState)([]),[s,h]=(0,a.useState)(!0),[m,x]=(0,a.useState)(null);return((0,a.useEffect)(()=>{(async()=>{try{h(!0);let e=await (0,f.ME)();t(e)}catch(e){console.error("Error fetching campaigns:",e),x("Failed to load campaigns. Please try again later.")}finally{h(!1)}})()},[]),s)?(0,r.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,r.jsx)(o.A,{className:"h-8 w-8 animate-spin text-primary"})}):m?(0,r.jsx)("div",{className:"text-center py-8 text-red-500",children:(0,r.jsx)("p",{children:m})}):(0,r.jsx)("div",{className:"rounded-md border",children:(0,r.jsxs)(n.XI,{children:[(0,r.jsx)(n.A0,{children:(0,r.jsxs)(n.Hj,{children:[(0,r.jsx)(n.nd,{children:"Name"}),(0,r.jsx)(n.nd,{children:"Type"}),(0,r.jsx)(n.nd,{children:"Status"}),(0,r.jsx)(n.nd,{children:"Created At"}),(0,r.jsx)(n.nd,{children:"Scheduled At"}),(0,r.jsx)(n.nd,{className:"w-[80px]"})]})}),(0,r.jsx)(n.BF,{children:0===e.length?(0,r.jsx)(n.Hj,{children:(0,r.jsx)(n.nA,{colSpan:6,className:"text-center py-8 text-muted-foreground",children:"No campaigns found"})}):e.map(e=>(0,r.jsxs)(n.Hj,{children:[(0,r.jsx)(n.nA,{className:"font-medium",children:e.name}),(0,r.jsx)(n.nA,{children:e.type}),(0,r.jsx)(n.nA,{children:(0,r.jsx)(u.E,{variant:"active"===e.status.toLowerCase()?"default":"completed"===e.status.toLowerCase()?"secondary":"outline",children:e.status})}),(0,r.jsx)(n.nA,{children:new Date(e.createdAt).toLocaleDateString()}),(0,r.jsx)(n.nA,{children:e.scheduledAt?new Date(e.scheduledAt).toLocaleDateString():"Not scheduled"}),(0,r.jsx)(n.nA,{children:(0,r.jsxs)(i.rI,{children:[(0,r.jsx)(i.ty,{asChild:!0,children:(0,r.jsxs)(l.$,{variant:"ghost",className:"h-8 w-8 p-0",children:[(0,r.jsx)("span",{className:"sr-only",children:"Open menu"}),(0,r.jsx)(c.A,{className:"h-4 w-4"})]})}),(0,r.jsx)(i.SQ,{align:"end",children:(0,r.jsxs)(i._2,{children:[(0,r.jsx)(d.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"View Details"})]})})]})})]},e.id))})]})})}},13655:(e,t,s)=>{Promise.resolve().then(s.bind(s,11775)),Promise.resolve().then(s.bind(s,83799)),Promise.resolve().then(s.bind(s,41681)),Promise.resolve().then(s.bind(s,3081)),Promise.resolve().then(s.bind(s,34964))},31886:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var r=s(23464);class a{async get(e,t){try{if(this.isOfflineMode&&!e.includes("/health"))throw console.log("API Client: In offline mode, skipping GET request to ".concat(e)),Error("Network Error: Application is in offline mode");let s=await this.client.get(e,t);return this.isOfflineMode&&this.setOfflineMode(!1),s.data}catch(e){throw e}}async post(e,t,s){return(await this.client.post(e,t,s)).data}async put(e,t,s){return(await this.client.put(e,t,s)).data}async delete(e,t){try{console.log("Making DELETE request to: ".concat(e));let s=await this.client.delete(e,t);if(204===s.status)return console.log("DELETE request to ".concat(e," successful with 204 status")),null;return s.data}catch(t){throw console.error("DELETE request to ".concat(e," failed:"),t),t}}async patch(e,t,s){return(await this.client.patch(e,t,s)).data}async upload(e,t,s){let r={...s,headers:{...null==s?void 0:s.headers,"Content-Type":"multipart/form-data"}};return(await this.client.post(e,t,r)).data}getBaseURL(){return this.baseURL}isInOfflineMode(){return this.isOfflineMode}setOfflineMode(e){this.isOfflineMode!==e&&(console.log("API Client: Setting offline mode to ".concat(e)),this.isOfflineMode=e,window.dispatchEvent(new CustomEvent("api:offline",{detail:{isOffline:e}})))}async tryReconnect(){try{return console.log("API Client: Attempting to reconnect to the server..."),await this.client.get("/health",{timeout:5e3}),console.log("API Client: Reconnection successful"),this.setOfflineMode(!1),!0}catch(e){return console.log("API Client: Reconnection failed, still in offline mode"),!1}}constructor(){this.isOfflineMode=!1,this.baseURL="http://localhost:5000/api/v1",this.baseURL||console.warn("NEXT_PUBLIC_BACKEND_API_URL is not defined. API requests may fail."),this.client=r.A.create({baseURL:this.baseURL,headers:{"Content-Type":"application/json"},timeout:1e4}),this.client.interceptors.request.use(e=>e,e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,e=>{if(e.response){if(console.error("API Error Response:",e.response.status,e.response.data),e.response.data&&0!==Object.keys(e.response.data).length||(e.response.data={error:"Request failed with status code ".concat(e.response.status)}),404===e.response.status){var t;console.log("Resource not found:",null===(t=e.config)||void 0===t?void 0:t.url),e.response.data={error:"The requested resource was not found. Please refresh and try again."}}e.responseData=e.response.data}else e.request?(console.error("API No Response:",e.request),e.message&&(e.message.includes("Network Error")||e.message.includes("timeout"))?(console.error("Network Error detected. Server might be down or unreachable."),this.setOfflineMode(!0),e.isOffline=!0,e.responseData={error:"Network Error: Unable to connect to the server. Please check your connection or try again later.",isOffline:!0},console.warn("API Client: Server connection failed. If you're running in development mode, make sure your backend server is running."),window.dispatchEvent(new CustomEvent("api:offline",{detail:{isOffline:!0}}))):e.responseData={error:"No response received from server. Please check your connection."}):(console.error("API Request Error:",e.message),e.responseData={error:e.message||"An unexpected error occurred"});return Promise.reject(e)})}}let n=new a},34964:(e,t,s)=>{"use strict";s.d(t,{Tabs:()=>l,TabsContent:()=>d,TabsList:()=>o,TabsTrigger:()=>c});var r=s(95155),a=s(12115),n=s(60704),i=s(53999);let l=n.bL,o=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(n.B8,{ref:t,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",s),...a})});o.displayName=n.B8.displayName;let c=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(n.l9,{ref:t,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",s),...a})});c.displayName=n.l9.displayName;let d=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(n.UC,{ref:t,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",s),...a})});d.displayName=n.UC.displayName},41681:(e,t,s)=>{"use strict";s.d(t,{MessagesDataTable:()=>u});var r=s(95155),a=s(12115),n=s(88524),i=s(88145),l=s(51154),o=s(31886);let c=async()=>{try{return await o.A.get("/recent-activity")}catch(e){throw console.error("Error fetching recent activity:",e),e}};var d=s(29344);function u(){let[e,t]=(0,a.useState)([]),[s,o]=(0,a.useState)(!0),[u,f]=(0,a.useState)(null);return((0,a.useEffect)(()=>{(async()=>{try{o(!0);let e=await c();t(e.recentMessages)}catch(e){console.error("Error fetching messages:",e),f("Failed to load messages. Please try again later.")}finally{o(!1)}})()},[]),s)?(0,r.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,r.jsx)(l.A,{className:"h-8 w-8 animate-spin text-primary"})}):u?(0,r.jsx)("div",{className:"text-center py-8 text-red-500",children:(0,r.jsx)("p",{children:u})}):(0,r.jsx)("div",{className:"rounded-md border",children:(0,r.jsxs)(n.XI,{children:[(0,r.jsx)(n.A0,{children:(0,r.jsxs)(n.Hj,{children:[(0,r.jsx)(n.nd,{children:"Message"}),(0,r.jsx)(n.nd,{children:"Client"}),(0,r.jsx)(n.nd,{children:"Type"}),(0,r.jsx)(n.nd,{children:"Timestamp"})]})}),(0,r.jsx)(n.BF,{children:0===e.length?(0,r.jsx)(n.Hj,{children:(0,r.jsx)(n.nA,{colSpan:4,className:"text-center py-8 text-muted-foreground",children:"No messages found"})}):e.map(e=>{var t;return(0,r.jsxs)(n.Hj,{children:[(0,r.jsx)(n.nA,{className:"max-w-[400px] truncate",children:e.text}),(0,r.jsx)(n.nA,{children:(null===(t=e.client)||void 0===t?void 0:t.name)||"Unknown"}),(0,r.jsx)(n.nA,{children:(0,r.jsx)(i.E,{variant:e.isBot?"secondary":"default",children:e.isBot?"Bot":"User"})}),(0,r.jsx)(n.nA,{children:(0,d.m)(new Date(e.createdAt),{addSuffix:!0})})]},e.id)})})]})})}},53999:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n});var r=s(52596),a=s(39688);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.QP)((0,r.$)(t))}},67133:(e,t,s)=>{"use strict";s.d(t,{SQ:()=>f,_2:()=>h,mB:()=>m,rI:()=>d,ty:()=>u});var r=s(95155),a=s(12115),n=s(48698),i=s(13052),l=s(5196),o=s(9428),c=s(53999);let d=n.bL,u=n.l9;n.YJ,n.ZL,n.Pb,n.z6,a.forwardRef((e,t)=>{let{className:s,inset:a,children:l,...o}=e;return(0,r.jsxs)(n.ZP,{ref:t,className:(0,c.cn)("flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",a&&"pl-8",s),...o,children:[l,(0,r.jsx)(i.A,{className:"ml-auto"})]})}).displayName=n.ZP.displayName,a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(n.G5,{ref:t,className:(0,c.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",s),...a})}).displayName=n.G5.displayName;let f=a.forwardRef((e,t)=>{let{className:s,sideOffset:a=4,...i}=e;return(0,r.jsx)(n.ZL,{children:(0,r.jsx)(n.UC,{ref:t,sideOffset:a,className:(0,c.cn)("z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",s),...i})})});f.displayName=n.UC.displayName;let h=a.forwardRef((e,t)=>{let{className:s,inset:a,...i}=e;return(0,r.jsx)(n.q7,{ref:t,className:(0,c.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",a&&"pl-8",s),...i})});h.displayName=n.q7.displayName,a.forwardRef((e,t)=>{let{className:s,children:a,checked:i,...o}=e;return(0,r.jsxs)(n.H_,{ref:t,className:(0,c.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),checked:i,...o,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(n.VF,{children:(0,r.jsx)(l.A,{className:"h-4 w-4"})})}),a]})}).displayName=n.H_.displayName,a.forwardRef((e,t)=>{let{className:s,children:a,...i}=e;return(0,r.jsxs)(n.hN,{ref:t,className:(0,c.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...i,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(n.VF,{children:(0,r.jsx)(o.A,{className:"h-2 w-2 fill-current"})})}),a]})}).displayName=n.hN.displayName,a.forwardRef((e,t)=>{let{className:s,inset:a,...i}=e;return(0,r.jsx)(n.JU,{ref:t,className:(0,c.cn)("px-2 py-1.5 text-sm font-semibold",a&&"pl-8",s),...i})}).displayName=n.JU.displayName;let m=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(n.wv,{ref:t,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",s),...a})});m.displayName=n.wv.displayName},83799:(e,t,s)=>{"use strict";s.d(t,{ClientsDataTable:()=>A});var r=s(95155),a=s(12115),n=s(88524),i=s(67133),l=s(97168),o=s(51154),c=s(5623),d=s(92657),u=s(81497),f=s(88145),h=s(42355),m=s(13052),x=s(53999);let p=e=>{let{className:t,...s}=e;return(0,r.jsx)("nav",{role:"navigation","aria-label":"pagination",className:(0,x.cn)("mx-auto flex w-full justify-center",t),...s})};p.displayName="Pagination";let g=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("ul",{ref:t,className:(0,x.cn)("flex flex-row items-center gap-1",s),...a})});g.displayName="PaginationContent";let j=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("li",{ref:t,className:(0,x.cn)("",s),...a})});j.displayName="PaginationItem";let y=e=>{let{className:t,isActive:s,size:a="icon",...n}=e;return(0,r.jsx)("a",{"aria-current":s?"page":void 0,className:(0,x.cn)((0,l.r)({variant:s?"outline":"ghost",size:a}),t),...n})};y.displayName="PaginationLink";let v=e=>{let{className:t,...s}=e;return(0,r.jsxs)(y,{"aria-label":"Go to previous page",size:"default",className:(0,x.cn)("gap-1 pl-2.5",t),...s,children:[(0,r.jsx)(h.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Previous"})]})};v.displayName="PaginationPrevious";let N=e=>{let{className:t,...s}=e;return(0,r.jsxs)(y,{"aria-label":"Go to next page",size:"default",className:(0,x.cn)("gap-1 pr-2.5",t),...s,children:[(0,r.jsx)("span",{children:"Next"}),(0,r.jsx)(m.A,{className:"h-4 w-4"})]})};N.displayName="PaginationNext";var w=s(471),b=s(29344);function A(){let[e,t]=(0,a.useState)([]),[s,h]=(0,a.useState)(!0),[m,x]=(0,a.useState)(null),[A,E]=(0,a.useState)(1),[P,R]=(0,a.useState)(1),[C,k]=(0,a.useState)(!1),[S,D]=(0,a.useState)(!1);(0,a.useEffect)(()=>{(async()=>{try{h(!0);let e=await (0,w.Lf)(A);t(e.data),R(e.pagination.totalPages),k(e.pagination.hasNext),D(e.pagination.hasPrevious)}catch(e){console.error("Error fetching clients:",e),x("Failed to load clients. Please try again later.")}finally{h(!1)}})()},[A]);let T=e=>{E(e)};return s?(0,r.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,r.jsx)(o.A,{className:"h-8 w-8 animate-spin text-primary"})}):m?(0,r.jsx)("div",{className:"text-center py-8 text-red-500",children:(0,r.jsx)("p",{children:m})}):(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"rounded-md border",children:(0,r.jsxs)(n.XI,{children:[(0,r.jsx)(n.A0,{children:(0,r.jsxs)(n.Hj,{children:[(0,r.jsx)(n.nd,{children:"Name"}),(0,r.jsx)(n.nd,{children:"Phone"}),(0,r.jsx)(n.nd,{children:"Type"}),(0,r.jsx)(n.nd,{children:"Last Active"}),(0,r.jsx)(n.nd,{children:"Last Message"}),(0,r.jsx)(n.nd,{children:"Created At"}),(0,r.jsx)(n.nd,{className:"w-[80px]"})]})}),(0,r.jsx)(n.BF,{children:0===e.length?(0,r.jsx)(n.Hj,{children:(0,r.jsx)(n.nA,{colSpan:7,className:"text-center py-8 text-muted-foreground",children:"No clients found"})}):e.map(e=>(0,r.jsxs)(n.Hj,{children:[(0,r.jsx)(n.nA,{className:"font-medium",children:e.name}),(0,r.jsx)(n.nA,{children:e.phone}),(0,r.jsx)(n.nA,{children:(0,r.jsx)(f.E,{variant:"outline",children:e.type})}),(0,r.jsx)(n.nA,{children:(0,b.m)(new Date(e.lastActive),{addSuffix:!0})}),(0,r.jsx)(n.nA,{className:"max-w-[200px] truncate",children:e.lastMessage}),(0,r.jsx)(n.nA,{children:new Date(e.createdAt).toLocaleDateString()}),(0,r.jsx)(n.nA,{children:(0,r.jsxs)(i.rI,{children:[(0,r.jsx)(i.ty,{asChild:!0,children:(0,r.jsxs)(l.$,{variant:"ghost",className:"h-8 w-8 p-0",children:[(0,r.jsx)("span",{className:"sr-only",children:"Open menu"}),(0,r.jsx)(c.A,{className:"h-4 w-4"})]})}),(0,r.jsxs)(i.SQ,{align:"end",children:[(0,r.jsxs)(i._2,{children:[(0,r.jsx)(d.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"View Details"})]}),(0,r.jsxs)(i._2,{children:[(0,r.jsx)(u.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"View Messages"})]})]})]})})]},e.id))})]})}),P>1&&(0,r.jsx)(p,{children:(0,r.jsxs)(g,{children:[S&&(0,r.jsx)(j,{children:(0,r.jsx)(v,{href:"#",onClick:e=>{e.preventDefault(),T(A-1)}})}),Array.from({length:P},(e,t)=>t+1).map(e=>(0,r.jsx)(j,{children:(0,r.jsx)(y,{href:"#",isActive:e===A,onClick:t=>{t.preventDefault(),T(e)},children:e})},e)),C&&(0,r.jsx)(j,{children:(0,r.jsx)(N,{href:"#",onClick:e=>{e.preventDefault(),T(A+1)}})})]})})]})}},88145:(e,t,s)=>{"use strict";s.d(t,{E:()=>l});var r=s(95155);s(12115);var a=s(74466),n=s(53999);let i=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:s,...a}=e;return(0,r.jsx)("div",{className:(0,n.cn)(i({variant:s}),t),...a})}},88524:(e,t,s)=>{"use strict";s.d(t,{A0:()=>l,BF:()=>o,Hj:()=>c,XI:()=>i,nA:()=>u,nd:()=>d});var r=s(95155),a=s(12115),n=s(53999);let i=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{className:"relative w-full overflow-auto",children:(0,r.jsx)("table",{ref:t,className:(0,n.cn)("w-full caption-bottom text-sm",s),...a})})});i.displayName="Table";let l=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("thead",{ref:t,className:(0,n.cn)("[&_tr]:border-b",s),...a})});l.displayName="TableHeader";let o=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("tbody",{ref:t,className:(0,n.cn)("[&_tr:last-child]:border-0",s),...a})});o.displayName="TableBody",a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("tfoot",{ref:t,className:(0,n.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",s),...a})}).displayName="TableFooter";let c=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("tr",{ref:t,className:(0,n.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",s),...a})});c.displayName="TableRow";let d=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("th",{ref:t,className:(0,n.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",s),...a})});d.displayName="TableHead";let u=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("td",{ref:t,className:(0,n.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",s),...a})});u.displayName="TableCell",a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("caption",{ref:t,className:(0,n.cn)("mt-4 text-sm text-muted-foreground",s),...a})}).displayName="TableCaption"},97168:(e,t,s)=>{"use strict";s.d(t,{$:()=>c,r:()=>o});var r=s(95155),a=s(12115),n=s(99708),i=s(74466),l=s(53999);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,t)=>{let{className:s,variant:a,size:i,asChild:c=!1,...d}=e,u=c?n.DX:"button";return(0,r.jsx)(u,{className:(0,l.cn)(o({variant:a,size:i,className:s})),ref:t,...d})});c.displayName="Button"}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,6071,9509,3464,1118,4949,3027,8441,1684,7358],()=>t(13655)),_N_E=e.O()}]);