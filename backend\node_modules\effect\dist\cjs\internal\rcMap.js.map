{"version": 3, "file": "rcMap.js", "names": ["Context", "_interopRequireWildcard", "require", "Duration", "_Function", "MutableHashMap", "_Pipeable", "coreEffect", "core", "circular", "fiberRuntime", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "TypeId", "exports", "Symbol", "for", "variance", "_K", "identity", "_A", "_E", "RcMapImpl", "lookup", "context", "scope", "idleTimeToLive", "capacity", "state", "_tag", "map", "empty", "semaphore", "unsafeMakeSemaphore", "constructor", "pipe", "pipeArguments", "arguments", "make", "options", "withFiberRuntime", "fiber", "getFiberRef", "currentContext", "scopeTag", "self", "decode", "undefined", "Math", "max", "Number", "POSITIVE_INFINITY", "as", "addFinalizer", "suspend", "void", "forEachSequentialDiscard", "entry", "scopeClose", "exitVoid", "tap", "clear", "withPermits", "dual", "self_", "key", "uninterruptibleMask", "restore", "getImpl", "fnUntraced", "interrupt", "o", "value", "refCount", "isFinite", "size", "fail", "ExceededCapacityException", "acquire", "finalizer", "deferred<PERSON><PERSON><PERSON>", "deferred", "scopeMake", "deferred<PERSON><PERSON>", "contextMap", "Map", "unsafeMap", "mapInputContext", "inputContext", "for<PERSON>ach", "unsafeMake", "exit", "flatMap", "deferredDone", "forkIn", "expiresAt", "release", "clockWith", "clock", "remove", "unsafeCurrentTimeMillis", "<PERSON><PERSON><PERSON><PERSON>", "interruptibleMask", "loop", "now", "remaining", "sleep", "millis", "ensuring", "sync", "keys", "impl", "succeed", "invalidate", "interruptFiber", "touch"], "sources": ["../../../src/internal/rcMap.ts"], "sourcesContent": [null], "mappings": ";;;;;;AACA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;AAEA,IAAAC,QAAA,GAAAF,uBAAA,CAAAC,OAAA;AAGA,IAAAE,SAAA,GAAAF,OAAA;AACA,IAAAG,cAAA,GAAAJ,uBAAA,CAAAC,OAAA;AACA,IAAAI,SAAA,GAAAJ,OAAA;AAGA,IAAAK,UAAA,GAAAN,uBAAA,CAAAC,OAAA;AACA,IAAAM,IAAA,GAAAP,uBAAA,CAAAC,OAAA;AACA,IAAAO,QAAA,GAAAR,uBAAA,CAAAC,OAAA;AACA,IAAAQ,YAAA,GAAAT,uBAAA,CAAAC,OAAA;AAAiD,SAAAS,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAX,wBAAAW,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAEjD;AACO,MAAMW,MAAM,GAAAC,OAAA,CAAAD,MAAA,gBAAiBE,MAAM,CAACC,GAAG,CAAC,cAAc,CAAiB;AAwB9E,MAAMC,QAAQ,GAAwC;EACpDC,EAAE,EAAEC,kBAAQ;EACZC,EAAE,EAAED,kBAAQ;EACZE,EAAE,EAAEF;CACL;AAED,MAAMG,SAAS;EAUFC,MAAA;EACAC,OAAA;EACAC,KAAA;EACAC,cAAA;EACAC,QAAA;EAbF,CAACd,MAAM;EAEhBe,KAAK,GAAmB;IACtBC,IAAI,EAAE,MAAM;IACZC,GAAG,eAAE3C,cAAc,CAAC4C,KAAK;GAC1B;EACQC,SAAS,gBAAGzC,QAAQ,CAAC0C,mBAAmB,CAAC,CAAC,CAAC;EAEpDC,YACWX,MAA6C,EAC7CC,OAA+B,EAC/BC,KAAkB,EAClBC,cAA6C,EAC7CC,QAAgB;IAJhB,KAAAJ,MAAM,GAANA,MAAM;IACN,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,QAAQ,GAARA,QAAQ;IAEjB,IAAI,CAACd,MAAM,CAAC,GAAGI,QAAQ;EACzB;EAEAkB,IAAIA,CAAA;IACF,OAAO,IAAAC,uBAAa,EAAC,IAAI,EAAEC,SAAS,CAAC;EACvC;;AAGF;AACO,MAAMC,IAAI,GAWAC,OAIhB,IACCjD,IAAI,CAACkD,gBAAgB,CAAgDC,KAAK,IAAI;EAC5E,MAAMjB,OAAO,GAAGiB,KAAK,CAACC,WAAW,CAACpD,IAAI,CAACqD,cAAc,CAAqC;EAC1F,MAAMlB,KAAK,GAAG3C,OAAO,CAACmB,GAAG,CAACuB,OAAO,EAAEhC,YAAY,CAACoD,QAAQ,CAAC;EACzD,MAAMC,IAAI,GAAG,IAAIvB,SAAS,CACxBiB,OAAO,CAAChB,MAAa,EACrBC,OAAO,EACPC,KAAK,EACLc,OAAO,CAACb,cAAc,GAAGzC,QAAQ,CAAC6D,MAAM,CAACP,OAAO,CAACb,cAAc,CAAC,GAAGqB,SAAS,EAC5EC,IAAI,CAACC,GAAG,CAACV,OAAO,CAACZ,QAAQ,IAAIuB,MAAM,CAACC,iBAAiB,EAAE,CAAC,CAAC,CAC1D;EACD,OAAO7D,IAAI,CAAC8D,EAAE,CACZ3B,KAAK,CAAC4B,YAAY,CAAC,MACjB/D,IAAI,CAACgE,OAAO,CAAC,MAAK;IAChB,IAAIT,IAAI,CAACjB,KAAK,CAACC,IAAI,KAAK,QAAQ,EAAE;MAChC,OAAOvC,IAAI,CAACiE,IAAI;IAClB;IACA,MAAMzB,GAAG,GAAGe,IAAI,CAACjB,KAAK,CAACE,GAAG;IAC1Be,IAAI,CAACjB,KAAK,GAAG;MAAEC,IAAI,EAAE;IAAQ,CAAE;IAC/B,OAAOvC,IAAI,CAACkE,wBAAwB,CAClC1B,GAAG,EACH,CAAC,GAAG2B,KAAK,CAAC,KAAKnE,IAAI,CAACoE,UAAU,CAACD,KAAK,CAAChC,KAAK,EAAEnC,IAAI,CAACqE,QAAQ,CAAC,CAC3D,CAACxB,IAAI,CACJ7C,IAAI,CAACsE,GAAG,CAAC,MAAK;MACZzE,cAAc,CAAC0E,KAAK,CAAC/B,GAAG,CAAC;IAC3B,CAAC,CAAC,EACFe,IAAI,CAACb,SAAS,CAAC8B,WAAW,CAAC,CAAC,CAAC,CAC9B;EACH,CAAC,CAAC,CACH,EACDjB,IAAI,CACL;AACH,CAAC,CAAC;AAEJ;AAAA/B,OAAA,CAAAwB,IAAA,GAAAA,IAAA;AACO,MAAMrC,GAAG,GAAAa,OAAA,CAAAb,GAAA,gBAGZ,IAAA8D,cAAI,EAAC,CAAC,EAAE,CAAUC,KAA2B,EAAEC,GAAM,KAA+B;EACtF,MAAMpB,IAAI,GAAGmB,KAA2B;EACxC,OAAO1E,IAAI,CAAC4E,mBAAmB,CAAEC,OAAO,IAAKC,OAAO,CAACvB,IAAI,EAAEoB,GAAG,EAAEE,OAAc,CAAC,CAAC;AAClF,CAAC,CAAC;AAEF,MAAMC,OAAO,gBAAG9E,IAAI,CAAC+E,UAAU,CAAC,WAAmBxB,IAAwB,EAAEoB,GAAM,EAAEE,OAAuB;EAC1G,IAAItB,IAAI,CAACjB,KAAK,CAACC,IAAI,KAAK,QAAQ,EAAE;IAChC,OAAO,OAAOvC,IAAI,CAACgF,SAAS;EAC9B;EACA,MAAM1C,KAAK,GAAGiB,IAAI,CAACjB,KAAK;EACxB,MAAM2C,CAAC,GAAGpF,cAAc,CAACc,GAAG,CAAC2B,KAAK,CAACE,GAAG,EAAEmC,GAAG,CAAC;EAC5C,IAAIR,KAAwB;EAC5B,IAAIc,CAAC,CAAC1C,IAAI,KAAK,MAAM,EAAE;IACrB4B,KAAK,GAAGc,CAAC,CAACC,KAAK;IACff,KAAK,CAACgB,QAAQ,EAAE;EAClB,CAAC,MAAM,IAAIvB,MAAM,CAACwB,QAAQ,CAAC7B,IAAI,CAAClB,QAAQ,CAAC,IAAIxC,cAAc,CAACwF,IAAI,CAAC9B,IAAI,CAACjB,KAAK,CAACE,GAAG,CAAC,IAAIe,IAAI,CAAClB,QAAQ,EAAE;IACjG,OAAO,OAAOrC,IAAI,CAACsF,IAAI,CACrB,IAAItF,IAAI,CAACuF,yBAAyB,CAAC,yCAAyChC,IAAI,CAAClB,QAAQ,EAAE,CAAC,CAC5E;EACpB,CAAC,MAAM;IACL8B,KAAK,GAAG,OAAOZ,IAAI,CAACb,SAAS,CAAC8B,WAAW,CAAC,CAAC,CAAC,CAACgB,OAAO,CAACjC,IAAI,EAAEoB,GAAG,EAAEE,OAAO,CAAC,CAAC;EAC3E;EACA,MAAM1C,KAAK,GAAG,OAAOjC,YAAY,CAACoD,QAAQ;EAC1C,OAAOnB,KAAK,CAAC4B,YAAY,CAAC,MAAMI,KAAK,CAACsB,SAAS,CAAC;EAChD,OAAO,OAAOZ,OAAO,CAAC7E,IAAI,CAAC0F,aAAa,CAACvB,KAAK,CAACwB,QAAQ,CAAC,CAAC;AAC3D,CAAC,CAAC;AAEF,MAAMH,OAAO,gBAAGxF,IAAI,CAAC+E,UAAU,CAAC,WAAmBxB,IAAwB,EAAEoB,GAAM,EAAEE,OAAuB;EAC1G,MAAM1C,KAAK,GAAG,OAAOjC,YAAY,CAAC0F,SAAS,EAAE;EAC7C,MAAMD,QAAQ,GAAG,OAAO3F,IAAI,CAAC6F,YAAY,EAAQ;EACjD,MAAML,OAAO,GAAGjC,IAAI,CAACtB,MAAM,CAAC0C,GAAG,CAAC;EAChC,MAAMmB,UAAU,GAAG,IAAIC,GAAG,CAACxC,IAAI,CAACrB,OAAO,CAAC8D,SAAS,CAAC;EAClD,OAAOnB,OAAO,CAAC7E,IAAI,CAACiG,eAAe,CACjCT,OAAuB,EACtBU,YAAoC,IAAI;IACvCA,YAAY,CAACF,SAAS,CAACG,OAAO,CAAC,CAACjB,KAAK,EAAEP,GAAG,KAAI;MAC5CmB,UAAU,CAACxE,GAAG,CAACqD,GAAG,EAAEO,KAAK,CAAC;IAC5B,CAAC,CAAC;IACFY,UAAU,CAACxE,GAAG,CAACpB,YAAY,CAACoD,QAAQ,CAACqB,GAAG,EAAExC,KAAK,CAAC;IAChD,OAAO3C,OAAO,CAAC4G,UAAU,CAACN,UAAU,CAAC;EACvC,CAAC,CACF,CAAC,CAACjD,IAAI,CACL7C,IAAI,CAACqG,IAAI,EACTrG,IAAI,CAACsG,OAAO,CAAED,IAAI,IAAKrG,IAAI,CAACuG,YAAY,CAACZ,QAAQ,EAAEU,IAAI,CAAC,CAAC,EACzDpG,QAAQ,CAACuG,MAAM,CAACrE,KAAK,CAAC,CACvB;EACD,MAAMgC,KAAK,GAAsB;IAC/BwB,QAAQ;IACRxD,KAAK;IACLsD,SAAS,EAAEhC,SAAgB;IAC3BN,KAAK,EAAEM,SAAS;IAChBgD,SAAS,EAAE,CAAC;IACZtB,QAAQ,EAAE;GACX;EACChB,KAAa,CAACsB,SAAS,GAAGiB,OAAO,CAACnD,IAAI,EAAEoB,GAAG,EAAER,KAAK,CAAC;EACrD,IAAIZ,IAAI,CAACjB,KAAK,CAACC,IAAI,KAAK,MAAM,EAAE;IAC9B1C,cAAc,CAACyB,GAAG,CAACiC,IAAI,CAACjB,KAAK,CAACE,GAAG,EAAEmC,GAAG,EAAER,KAAK,CAAC;EAChD;EACA,OAAOA,KAAK;AACd,CAAC,CAAC;AAEF,MAAMuC,OAAO,GAAGA,CAAUnD,IAAwB,EAAEoB,GAAM,EAAER,KAAwB,KAClFpE,UAAU,CAAC4G,SAAS,CAAEC,KAAK,IAAI;EAC7BzC,KAAK,CAACgB,QAAQ,EAAE;EAChB,IAAIhB,KAAK,CAACgB,QAAQ,GAAG,CAAC,EAAE;IACtB,OAAOnF,IAAI,CAACiE,IAAI;EAClB,CAAC,MAAM,IACLV,IAAI,CAACjB,KAAK,CAACC,IAAI,KAAK,QAAQ,IACzB,CAAC1C,cAAc,CAACa,GAAG,CAAC6C,IAAI,CAACjB,KAAK,CAACE,GAAG,EAAEmC,GAAG,CAAC,IACxCpB,IAAI,CAACnB,cAAc,KAAKqB,SAAS,EACpC;IACA,IAAIF,IAAI,CAACjB,KAAK,CAACC,IAAI,KAAK,MAAM,EAAE;MAC9B1C,cAAc,CAACgH,MAAM,CAACtD,IAAI,CAACjB,KAAK,CAACE,GAAG,EAAEmC,GAAG,CAAC;IAC5C;IACA,OAAO3E,IAAI,CAACoE,UAAU,CAACD,KAAK,CAAChC,KAAK,EAAEnC,IAAI,CAACqE,QAAQ,CAAC;EACpD;EAEA,IAAI,CAAC1E,QAAQ,CAACyF,QAAQ,CAAC7B,IAAI,CAACnB,cAAc,CAAC,EAAE;IAC3C,OAAOpC,IAAI,CAACiE,IAAI;EAClB;EAEAE,KAAK,CAACsC,SAAS,GAAGG,KAAK,CAACE,uBAAuB,EAAE,GAAGnH,QAAQ,CAACoH,QAAQ,CAACxD,IAAI,CAACnB,cAAc,CAAC;EAC1F,IAAI+B,KAAK,CAAChB,KAAK,EAAE,OAAOnD,IAAI,CAACiE,IAAI;EAEjC,OAAOjE,IAAI,CAACgH,iBAAiB,CAAC,SAASC,IAAIA,CAACpC,OAAO;IACjD,MAAMqC,GAAG,GAAGN,KAAK,CAACE,uBAAuB,EAAE;IAC3C,MAAMK,SAAS,GAAGhD,KAAK,CAACsC,SAAS,GAAGS,GAAG;IACvC,IAAIC,SAAS,IAAI,CAAC,EAAE;MAClB,IAAI5D,IAAI,CAACjB,KAAK,CAACC,IAAI,KAAK,QAAQ,IAAI4B,KAAK,CAACgB,QAAQ,GAAG,CAAC,EAAE,OAAOnF,IAAI,CAACiE,IAAI;MACxEpE,cAAc,CAACgH,MAAM,CAACtD,IAAI,CAACjB,KAAK,CAACE,GAAG,EAAEmC,GAAG,CAAC;MAC1C,OAAOE,OAAO,CAAC7E,IAAI,CAACoE,UAAU,CAACD,KAAK,CAAChC,KAAK,EAAEnC,IAAI,CAACqE,QAAQ,CAAC,CAAC;IAC7D;IACA,OAAOrE,IAAI,CAACsG,OAAO,CAACM,KAAK,CAACQ,KAAK,CAACzH,QAAQ,CAAC0H,MAAM,CAACF,SAAS,CAAC,CAAC,EAAE,MAAMF,IAAI,CAACpC,OAAO,CAAC,CAAC;EACnF,CAAC,CAAC,CAAChC,IAAI,CACL3C,YAAY,CAACoH,QAAQ,CAACtH,IAAI,CAACuH,IAAI,CAAC,MAAK;IACnCpD,KAAK,CAAChB,KAAK,GAAGM,SAAS;EACzB,CAAC,CAAC,CAAC,EACHxD,QAAQ,CAACuG,MAAM,CAACjD,IAAI,CAACpB,KAAK,CAAC,EAC3BnC,IAAI,CAACsE,GAAG,CAAEnB,KAAK,IAAI;IACjBgB,KAAK,CAAChB,KAAK,GAAGA,KAAK;EACrB,CAAC,CAAC,EACFI,IAAI,CAACb,SAAS,CAAC8B,WAAW,CAAC,CAAC,CAAC,CAC9B;AACH,CAAC,CAAC;AAEJ;AACO,MAAMgD,IAAI,GAAajE,IAA0B,IAAsB;EAC5E,MAAMkE,IAAI,GAAGlE,IAA0B;EACvC,OAAOvD,IAAI,CAACgE,OAAO,CAAC,MAClByD,IAAI,CAACnF,KAAK,CAACC,IAAI,KAAK,QAAQ,GAAGvC,IAAI,CAACgF,SAAS,GAAGhF,IAAI,CAAC0H,OAAO,CAAC7H,cAAc,CAAC2H,IAAI,CAACC,IAAI,CAACnF,KAAK,CAACE,GAAG,CAAC,CAAC,CAClG;AACH,CAAC;AAED;AAAAhB,OAAA,CAAAgG,IAAA,GAAAA,IAAA;AACO,MAAMG,UAAU,GAAAnG,OAAA,CAAAmG,UAAA,gBAGnB,IAAAlD,cAAI,EACN,CAAC,eACDzE,IAAI,CAAC+E,UAAU,CAAC,WAAmBL,KAA2B,EAAEC,GAAM;EACpE,MAAMpB,IAAI,GAAGmB,KAA2B;EACxC,IAAInB,IAAI,CAACjB,KAAK,CAACC,IAAI,KAAK,QAAQ,EAAE;EAClC,MAAM0C,CAAC,GAAGpF,cAAc,CAACc,GAAG,CAAC4C,IAAI,CAACjB,KAAK,CAACE,GAAG,EAAEmC,GAAG,CAAC;EACjD,IAAIM,CAAC,CAAC1C,IAAI,KAAK,MAAM,EAAE;EACvB,MAAM4B,KAAK,GAAGc,CAAC,CAACC,KAAK;EACrBrF,cAAc,CAACgH,MAAM,CAACtD,IAAI,CAACjB,KAAK,CAACE,GAAG,EAAEmC,GAAG,CAAC;EAC1C,IAAIR,KAAK,CAACgB,QAAQ,GAAG,CAAC,EAAE;EACxB,OAAOnF,IAAI,CAACoE,UAAU,CAACD,KAAK,CAAChC,KAAK,EAAEnC,IAAI,CAACqE,QAAQ,CAAC;EAClD,IAAIF,KAAK,CAAChB,KAAK,EAAE,OAAOnD,IAAI,CAAC4H,cAAc,CAACzD,KAAK,CAAChB,KAAK,CAAC;AAC1D,CAAC,CAAC,CACH;AAED;AACO,MAAM0E,KAAK,GAAArG,OAAA,CAAAqG,KAAA,gBAGd,IAAApD,cAAI,EACN,CAAC,EACD,CAAUC,KAA2B,EAAEC,GAAM,KAC3C5E,UAAU,CAAC4G,SAAS,CAAEC,KAAK,IAAI;EAC7B,MAAMrD,IAAI,GAAGmB,KAA2B;EACxC,IAAI,CAACnB,IAAI,CAACnB,cAAc,IAAImB,IAAI,CAACjB,KAAK,CAACC,IAAI,KAAK,QAAQ,EAAE,OAAOvC,IAAI,CAACiE,IAAI;EAC1E,MAAMgB,CAAC,GAAGpF,cAAc,CAACc,GAAG,CAAC4C,IAAI,CAACjB,KAAK,CAACE,GAAG,EAAEmC,GAAG,CAAC;EACjD,IAAIM,CAAC,CAAC1C,IAAI,KAAK,MAAM,EAAE,OAAOvC,IAAI,CAACiE,IAAI;EACvCgB,CAAC,CAACC,KAAK,CAACuB,SAAS,GAAGG,KAAK,CAACE,uBAAuB,EAAE,GAAGnH,QAAQ,CAACoH,QAAQ,CAACxD,IAAI,CAACnB,cAAc,CAAC;EAC5F,OAAOpC,IAAI,CAACiE,IAAI;AAClB,CAAC,CAAC,CACL", "ignoreList": []}