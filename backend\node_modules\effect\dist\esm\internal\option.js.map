{"version": 3, "file": "option.js", "names": ["Equal", "Hash", "format", "NodeInspectSymbol", "toJSON", "hasProperty", "EffectPrototype", "TypeId", "Symbol", "for", "CommonProto", "_A", "_", "toString", "SomeProto", "Object", "assign", "create", "_tag", "_op", "symbol", "that", "isOption", "isSome", "equals", "value", "cached", "combine", "hash", "_id", "NoneHash", "NoneProto", "isNone", "input", "fa", "none", "some", "a"], "sources": ["../../../src/internal/option.ts"], "sourcesContent": [null], "mappings": "AAAA;;;AAIA,OAAO,KAAKA,KAAK,MAAM,aAAa;AACpC,OAAO,KAAKC,IAAI,MAAM,YAAY;AAClC,SAASC,MAAM,EAAEC,iBAAiB,EAAEC,MAAM,QAAQ,mBAAmB;AAErE,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,SAASC,eAAe,QAAQ,iBAAiB;AAEjD,MAAMC,MAAM,gBAAkBC,MAAM,CAACC,GAAG,CAAC,eAAe,CAAkB;AAE1E,MAAMC,WAAW,GAAG;EAClB,GAAGJ,eAAe;EAClB,CAACC,MAAM,GAAG;IACRI,EAAE,EAAGC,CAAQ,IAAKA;GACnB;EACD,CAACT,iBAAiB,IAAC;IACjB,OAAO,IAAI,CAACC,MAAM,EAAE;EACtB,CAAC;EACDS,QAAQA,CAAA;IACN,OAAOX,MAAM,CAAC,IAAI,CAACE,MAAM,EAAE,CAAC;EAC9B;CACD;AAED,MAAMU,SAAS,gBAAGC,MAAM,CAACC,MAAM,eAACD,MAAM,CAACE,MAAM,CAACP,WAAW,CAAC,EAAE;EAC1DQ,IAAI,EAAE,MAAM;EACZC,GAAG,EAAE,MAAM;EACX,CAACnB,KAAK,CAACoB,MAAM,EAA2BC,IAAa;IACnD,OAAOC,QAAQ,CAACD,IAAI,CAAC,IAAIE,MAAM,CAACF,IAAI,CAAC,IAAIrB,KAAK,CAACwB,MAAM,CAAC,IAAI,CAACC,KAAK,EAAEJ,IAAI,CAACI,KAAK,CAAC;EAC/E,CAAC;EACD,CAACxB,IAAI,CAACmB,MAAM,IAAC;IACX,OAAOnB,IAAI,CAACyB,MAAM,CAAC,IAAI,EAAEzB,IAAI,CAAC0B,OAAO,CAAC1B,IAAI,CAAC2B,IAAI,CAAC,IAAI,CAACV,IAAI,CAAC,CAAC,CAACjB,IAAI,CAAC2B,IAAI,CAAC,IAAI,CAACH,KAAK,CAAC,CAAC,CAAC;EACrF,CAAC;EACDrB,MAAMA,CAAA;IACJ,OAAO;MACLyB,GAAG,EAAE,QAAQ;MACbX,IAAI,EAAE,IAAI,CAACA,IAAI;MACfO,KAAK,EAAErB,MAAM,CAAC,IAAI,CAACqB,KAAK;KACzB;EACH;CACD,CAAC;AAEF,MAAMK,QAAQ,gBAAG7B,IAAI,CAAC2B,IAAI,CAAC,MAAM,CAAC;AAClC,MAAMG,SAAS,gBAAGhB,MAAM,CAACC,MAAM,eAACD,MAAM,CAACE,MAAM,CAACP,WAAW,CAAC,EAAE;EAC1DQ,IAAI,EAAE,MAAM;EACZC,GAAG,EAAE,MAAM;EACX,CAACnB,KAAK,CAACoB,MAAM,EAA2BC,IAAa;IACnD,OAAOC,QAAQ,CAACD,IAAI,CAAC,IAAIW,MAAM,CAACX,IAAI,CAAC;EACvC,CAAC;EACD,CAACpB,IAAI,CAACmB,MAAM,IAAC;IACX,OAAOU,QAAQ;EACjB,CAAC;EACD1B,MAAMA,CAAA;IACJ,OAAO;MACLyB,GAAG,EAAE,QAAQ;MACbX,IAAI,EAAE,IAAI,CAACA;KACZ;EACH;CACD,CAAC;AAEF;AACA,OAAO,MAAMI,QAAQ,GAAIW,KAAc,IAAsC5B,WAAW,CAAC4B,KAAK,EAAE1B,MAAM,CAAC;AAEvG;AACA,OAAO,MAAMyB,MAAM,GAAOE,EAAoB,IAA2BA,EAAE,CAAChB,IAAI,KAAK,MAAM;AAE3F;AACA,OAAO,MAAMK,MAAM,GAAOW,EAAoB,IAA2BA,EAAE,CAAChB,IAAI,KAAK,MAAM;AAE3F;AACA,OAAO,MAAMiB,IAAI,gBAAyBpB,MAAM,CAACE,MAAM,CAACc,SAAS,CAAC;AAElE;AACA,OAAO,MAAMK,IAAI,GAAOX,KAAQ,IAAsB;EACpD,MAAMY,CAAC,GAAGtB,MAAM,CAACE,MAAM,CAACH,SAAS,CAAC;EAClCuB,CAAC,CAACZ,KAAK,GAAGA,KAAK;EACf,OAAOY,CAAC;AACV,CAAC", "ignoreList": []}