import { HttpRouter, HttpServerRequest } from '@effect/platform';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';
import { configProvider } from '../dist/_internal/config.js';
import { createRequestHandler, AdapterArguments } from '../dist/_internal/handler.js';
import { createBuilder } from '../dist/_internal/upload-builder.js';
export { UTFiles, UTRegion as experimental_UTRegion } from '../dist/_internal/types.js';

const createUploadthing = (opts)=>createBuilder(opts);
const createRouteHandler = (opts)=>{
    const router = Effect.runSync(createRequestHandler(opts, "effect-platform"));
    return HttpRouter.provideServiceEffect(router, AdapterArguments, Effect.map(HttpServerRequest.HttpServerRequest, (serverRequest)=>({
            req: serverRequest
        }))).pipe(Effect.provide(Layer.setConfigProvider(configProvider(opts.config))));
};

export { createRouteHandler, createUploadthing };
