{"version": 3, "file": "RequestResolver.d.ts", "sourceRoot": "", "sources": ["../../src/RequestResolver.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,YAAY,CAAA;AAC/C,OAAO,KAAK,OAAO,MAAM,cAAc,CAAA;AACvC,OAAO,KAAK,MAAM,MAAM,aAAa,CAAA;AACrC,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAC1C,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AACxC,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAA;AAG7C,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAA;AAC7C,OAAO,KAAK,KAAK,OAAO,MAAM,cAAc,CAAA;AAC5C,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AAExC;;;GAGG;AACH,eAAO,MAAM,qBAAqB,EAAE,OAAO,MAAmC,CAAA;AAE9E;;;GAGG;AACH,MAAM,MAAM,qBAAqB,GAAG,OAAO,qBAAqB,CAAA;AAEhE;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACH,MAAM,WAAW,eAAe,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,KAAK,CAAE,SAAQ,eAAe,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,EAAE,QAAQ;IACjH;;;;OAIG;IACH,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,CAAA;IAE/E;;OAEG;IACH,UAAU,CAAC,GAAG,WAAW,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;CAClE;AAED;;GAEG;AACH,MAAM,CAAC,OAAO,WAAW,eAAe,CAAC;IACvC;;;OAGG;IACH,UAAiB,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QACnC,QAAQ,CAAC,CAAC,qBAAqB,CAAC,EAAE;YAChC,QAAQ,CAAC,EAAE,EAAE,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAA;YACnC,QAAQ,CAAC,EAAE,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;SAChC,CAAA;KACF;CACF;AAED;;;GAGG;AACH,eAAO,MAAM,iBAAiB,GAAI,CAAC,EAAE,CAAC,SAAS,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,QAAQ,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,uDAC7B,CAAA;AAExE;;;GAGG;AACH,eAAO,MAAM,mBAAmB,GAC7B,QAAQ,SAAS,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,eAAe,QAAQ,MACpE,CAAC,EAAE,CAAC,SAAS,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,QAC/B,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,KAC1B,MAAM,CAAC,MAAM,CACd,eAAe,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EACrG,KAAK,EACL,GAAG,CAAC,IAAI,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAE,CAAC,MAAM,CAAC,CACyB,CAAA;AAElG;;;;;GAKG;AACH,eAAO,MAAM,iBAAiB,EAAE,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,IAAI,eAAe,CAAC,OAAO,EAAE,OAAO,CAA0B,CAAA;AAE/G;;;;;;GAMG;AACH,eAAO,MAAM,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EACtB,MAAM,EAAE,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,KACjE,eAAe,CAAC,CAAC,EAAE,CAAC,CAAiB,CAAA;AAE1C;;;;;;GAMG;AACH,eAAO,MAAM,aAAa,EAAE,CAAC,CAAC,EAAE,CAAC,EAC/B,MAAM,EAAE,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,KAChF,eAAe,CAAC,CAAC,EAAE,CAAC,CAA0B,CAAA;AAEnD;;;;;GAKG;AACH,eAAO,MAAM,WAAW,EAAE,CAAC,CAAC,SAAS,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,EAC/D,GAAG,EAAE,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,KAC/D,eAAe,CAAC,CAAC,EAAE,CAAC,CAAwB,CAAA;AAEjD;;;;;;GAMG;AACH,eAAO,MAAM,MAAM,EAAE;IACnB;;;;;;OAMG;IACH,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EACZ,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC,EACpC,KAAK,EAAE,CAAC,CAAC,EAAE,EAAE,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC,GAC5C,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,eAAe,CAAC,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAA;IACzE;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAClB,IAAI,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,EAC3B,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC,EACpC,KAAK,EAAE,CAAC,CAAC,EAAE,EAAE,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC,GAC5C,eAAe,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAA;CACjB,CAAA;AAEnB;;;;;;;;;;;;;;;;;;;;;;;;;;GA0BG;AACH,eAAO,MAAM,cAAc,EAAE;IAC3B;;;;;;;;;;;;;;;;;;;;;;;;;;OA0BG;IACH,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EACf,MAAM,EAAE,CAAC,QAAQ,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC,EACnF,KAAK,EAAE,CAAC,QAAQ,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC,GACvF,CAAC,CAAC,EAAE,IAAI,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,eAAe,CAAC,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAA;IACtE;;;;;;;;;;;;;;;;;;;;;;;;;;OA0BG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAClB,IAAI,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,EAC3B,MAAM,EAAE,CAAC,QAAQ,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC,EACnF,KAAK,EAAE,CAAC,QAAQ,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC,GACvF,eAAe,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAA;CACT,CAAA;AAE3B;;;;;GAKG;AACH,eAAO,MAAM,MAAM,EAAE;IACnB;;;;;OAKG;IACH,CAAC,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;IACzE;;;;;OAKG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,GAAG,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;CACpD,CAAA;AAEnB;;;;;GAKG;AACH,eAAO,MAAM,eAAe,EAAE;IAC5B;;;;;OAKG;IACH,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,eAAe,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;IAC9J;;;;;OAKG;IACH,CAAC,CAAC,EAAE,CAAC,SAAS,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,EAAE,EACzC,IAAI,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,EAC3B,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,GACtD,eAAe,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;CACC,CAAA;AAE5B;;;;;;;GAOG;AACH,eAAO,MAAM,UAAU,EAAE;IACvB;;;;;;;OAOG;IACH,CAAC,CAAC,SAAS,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,SAAS,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,SAAS,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAChH,IAAI,EAAE,eAAe,CAAC,CAAC,EAAE,EAAE,CAAC,EAC5B,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAC5E,CAAC,CAAC,EAAE,IAAI,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,eAAe,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAA;IACjE;;;;;;;OAOG;IACH,CACE,CAAC,EACD,CAAC,SAAS,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EACnC,EAAE,EACF,CAAC,SAAS,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EACnC,CAAC,SAAS,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAEnC,IAAI,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,EAC3B,IAAI,EAAE,eAAe,CAAC,CAAC,EAAE,EAAE,CAAC,EAC5B,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAC5E,eAAe,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAA;CACR,CAAA;AAEvB;;;;;GAKG;AACH,eAAO,MAAM,YAAY,EAAE,CAAC,CAAC,SAAS,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,EACxD,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,KAC1C,eAAe,CAAC,CAAC,CAAyB,CAAA;AAE/C;;;;;;;GAOG;AACH,eAAO,MAAM,mBAAmB,EAAE,CAAC,CAAC,SAAS,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,EAC/D,CAAC,EAAE,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KACjE,eAAe,CAAC,CAAC,CAAgC,CAAA;AAEtD;;;;;GAKG;AACH,eAAO,MAAM,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,SAAS,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAC9D,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAChF,eAAe,CAAC,CAAC,EAAE,CAAC,CAAuB,CAAA;AAEhD;;;;;;;;GAQG;AACH,eAAO,MAAM,gBAAgB,EAAE,CAAC,CAAC,SAAS,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG;IAAE,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAA;CAAE,OAAO,CACpG,GAAG,SAAS;IACV,QAAQ,EAAE,GAAG,IAAI,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE;QAAE,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAA;KAAE,CAAC,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,GACjF,GAAG,SAAS,OAAO,CAAC,OAAO,CAAC,MAAM,IAAI,EAAE,MAAM,IAAI,CAAC,GACjD,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,CAAC,GACpE,KAAK,GACL,KAAK;CACV,EAED,GAAG,EAAE,GAAG,KACL,eAAe,CAAC,CAAC,EAAE,UAAU,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,SAAS,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CACtF,CAAA;AAE3B;;;;;GAKG;AACH,eAAO,MAAM,KAAK,EAAE,eAAe,CAAC,KAAK,CAAkB,CAAA;AAE3D;;;;;GAKG;AACH,eAAO,MAAM,cAAc,EAAE;IAC3B;;;;;OAKG;IACH,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,eAAe,CAAC,CAAC,CAAC,CAAA;IAC1H;;;;;OAKG;IACH,CAAC,CAAC,EAAE,CAAC,SAAS,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,CAAA;CAC7F,CAAA;AAE3B;;;;;;;GAOG;AACH,eAAO,MAAM,IAAI,EAAE;IACjB;;;;;;;OAOG;IACH,CAAC,EAAE,SAAS,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,eAAe,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,SAAS,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,eAAe,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAA;IACnL;;;;;;;OAOG;IACH,CAAC,CAAC,SAAS,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,SAAS,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,eAAe,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,eAAe,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,CAAA;CAChK,CAAA;AAEjB;;;;;GAKG;AACH,eAAO,MAAM,OAAO,EAAE;IACpB;;;;;OAKG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,SAAS,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;IAC/H;;;;;OAKG;IACH,CAAC,CAAC,EAAE,CAAC,SAAS,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;CACrG,CAAA"}