{"version": 3, "file": "PrimaryKey.js", "names": ["symbol", "exports", "Symbol", "for", "value", "self"], "sources": ["../../src/PrimaryKey.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA;;;AAIA;;;;AAIO,MAAMA,MAAM,GAAAC,OAAA,CAAAD,MAAA,gBAAkBE,MAAM,CAACC,GAAG,CAAC,mBAAmB,CAAC;AAUpE;;;;AAIO,MAAMC,KAAK,GAAIC,IAAgB,IAAaA,IAAI,CAACL,MAAM,CAAC,EAAE;AAAAC,OAAA,CAAAG,KAAA,GAAAA,KAAA", "ignoreList": []}