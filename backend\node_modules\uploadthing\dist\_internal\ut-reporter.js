import { unsafe<PERSON><PERSON><PERSON>e } from 'effect/Function';
import * as Micro from 'effect/Micro';
import { fetchEff, parseResponse<PERSON>son, UploadThingError, getErrorTypeFromStatusCode } from '@uploadthing/shared';

var version = "7.7.2";

const createAPIRequestUrl = (config)=>{
    const url = new URL(config.url);
    const queryParams = new URLSearchParams(url.search);
    queryParams.set("actionType", config.actionType);
    queryParams.set("slug", config.slug);
    url.search = queryParams.toString();
    return url;
};
/**
 * Creates a "client" for reporting events to the UploadThing server via the user's API endpoint.
 * Events are handled in "./handler.ts starting at L112"
 */ const createUTReporter = (cfg)=>(type, payload)=>Micro.gen(function*() {
            const url = createAPIRequestUrl({
                url: cfg.url,
                slug: cfg.endpoint,
                actionType: type
            });
            const headers = new Headers((yield* Micro.promise(async ()=>typeof cfg.headers === "function" ? await cfg.headers() : cfg.headers)));
            if (cfg.package) {
                headers.set("x-uploadthing-package", cfg.package);
            }
            headers.set("x-uploadthing-version", version);
            headers.set("Content-Type", "application/json");
            const response = yield* fetchEff(url, {
                method: "POST",
                body: JSON.stringify(payload),
                headers
            }).pipe(Micro.andThen(parseResponseJson), /**
         * We don't _need_ to validate the response here, just cast it for now.
         * As of now, @effect/schema includes quite a few bytes we cut out by this...
         * We have "strong typing" on the backend that ensures the shape should match.
         */ Micro.map(unsafeCoerce), Micro.catchTag("FetchError", (e)=>Micro.fail(new UploadThingError({
                    code: "INTERNAL_CLIENT_ERROR",
                    message: `Failed to report event "${type}" to UploadThing server`,
                    cause: e
                }))), Micro.catchTag("BadRequestError", (e)=>Micro.fail(new UploadThingError({
                    code: getErrorTypeFromStatusCode(e.status),
                    message: e.getMessage(),
                    cause: e.json
                }))), Micro.catchTag("InvalidJson", (e)=>Micro.fail(new UploadThingError({
                    code: "INTERNAL_CLIENT_ERROR",
                    message: "Failed to parse response from UploadThing server",
                    cause: e
                }))));
            return response;
        });

export { createUTReporter };
