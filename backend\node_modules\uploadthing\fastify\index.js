import * as Effect from 'effect/Effect';
import { makeAdapterHandler } from '../dist/_internal/handler.js';
import { toWebRequest } from '../dist/_internal/to-web-request.js';
import { createBuilder } from '../dist/_internal/upload-builder.js';
export { UTFiles, UTRegion as experimental_UTRegion } from '../dist/_internal/types.js';

const createUploadthing = (opts)=>createBuilder(opts);
const createRouteHandler = (fastify, opts, done)=>{
    const handler = makeAdapterHandler((req, res)=>Effect.succeed({
            req,
            res
        }), (req)=>toWebRequest(req), opts, "fastify");
    fastify.all("/api/uploadthing", async (req, res)=>{
        const response = await handler(req, res);
        return res.status(response.status).headers(Object.fromEntries(response.headers)).send(response.body);
    });
    done();
};

export { createRouteHandler, createUploadthing };
