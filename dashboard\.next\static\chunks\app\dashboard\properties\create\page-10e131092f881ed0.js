(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6108],{40619:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>h});var t=a(95155),s=a(12115),l=a(35695),d=a(57340),i=a(35169),o=a(97168),n=a(86132),c=a(92802),p=a(83315),m=a(56671),x=a(17681);function h(){let e=(0,l.useRouter)(),{language:r}=(0,n.Y)(),[a,h]=(0,s.useState)(!1),u={ar:{createProperty:"إنشاء عقار سعودي جديد",backToProperties:"العودة إلى العقارات",subtitle:"أضف عقار جديد في المملكة العربية السعودية مع معلومات مفصلة وصور عالية الجودة",properties:"العقارات",home:"الرئيسية",welcome:"مرحباً بك",newProperty:"عقار سعودي جديد",fillDetails:"املأ التفاصيل المطلوبة لإضافة عقار جديد في المملكة",ready:"جاهز للبدء",saudiInterface:"نظام العقارات السعودي",clickToReturn:"اضغط للعودة",dashboard:"لوحة التحكم",saudiProperties:"العقارات السعودية"},en:{createProperty:"Create New Saudi Property",backToProperties:"Back to Properties",subtitle:"Add a new property in Saudi Arabia with detailed information and high-quality images",properties:"Properties",home:"Home",welcome:"Welcome",newProperty:"New Saudi Property",fillDetails:"Fill in the required details to add a new property in Saudi Arabia",ready:"Ready to Start",saudiInterface:"Saudi Properties System",clickToReturn:"Click to Return",dashboard:"Dashboard",saudiProperties:"Saudi Properties"}}[r],b=async e=>{h(!0);try{let a={...e,price:parseFloat(e.price),bedrooms:e.bedrooms?parseInt(e.bedrooms):void 0,bathrooms:e.bathrooms?parseInt(e.bathrooms):void 0,area:e.area?parseFloat(e.area):void 0,yearBuilt:e.yearBuilt?parseInt(e.yearBuilt):void 0,parking:e.parking?parseInt(e.parking):void 0},t=await fetch("/api/v1/properties",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(t.ok)m.oR.success("ar"===r?"تم إنشاء العقار بنجاح ✨":"Property created successfully ✨"),setTimeout(()=>{window.location.href="/dashboard/properties"},1e3);else{let e=await t.json();m.oR.error(e.message||("ar"===r?"فشل في إنشاء العقار ❌":"Failed to create property ❌"))}}catch(e){console.error("Error creating property:",e),m.oR.error("فشل في إنشاء العقار ❌")}finally{h(!1)}};return(0,t.jsx)("div",{className:"min-h-screen bg-slate-900 ".concat("ar"===r?"rtl":"ltr"),children:(0,t.jsxs)("div",{className:"container mx-auto px-8 py-12",children:[(0,t.jsxs)("nav",{className:"flex items-center gap-4 text-sm mb-12 ".concat("ar"===r?"flex-row-reverse":"flex-row"),dir:"ar"===r?"rtl":"ltr",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 px-4 py-3 bg-slate-800 rounded-lg border border-slate-700",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-emerald-600 rounded-lg flex items-center justify-center",children:(0,t.jsx)(d.A,{className:"h-4 w-4 text-white"})}),(0,t.jsx)("span",{className:"font-medium text-white",children:u.home})]}),(0,t.jsx)("div",{className:"w-1 h-1 bg-slate-500 rounded-full"}),(0,t.jsx)("button",{onClick:()=>e.push("/dashboard/properties"),className:"flex items-center gap-3 px-4 py-3 bg-slate-800 rounded-lg border border-slate-700 hover:border-emerald-600",children:(0,t.jsx)("span",{className:"font-medium text-slate-300 hover:text-emerald-400",children:u.properties})}),(0,t.jsx)("div",{className:"w-1 h-1 bg-slate-500 rounded-full"}),(0,t.jsx)("div",{className:"flex items-center gap-3 px-4 py-3 bg-emerald-600 rounded-lg",children:(0,t.jsx)("span",{className:"font-medium text-white",children:u.newProperty})})]}),(0,t.jsx)("div",{className:"mb-12",children:(0,t.jsx)("div",{className:"bg-slate-800 rounded-lg border border-slate-700 p-8",children:(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6 ".concat("ar"===r?"lg:flex-row-reverse":""),children:[(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-4 ".concat("ar"===r?"flex-row-reverse":""),dir:"ar"===r?"rtl":"ltr",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-emerald-600 rounded-lg flex items-center justify-center",children:(0,t.jsx)(d.A,{className:"h-6 w-6 text-white"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-white",children:u.createProperty}),(0,t.jsx)("p",{className:"text-slate-400 mt-1",children:u.fillDetails})]})]})}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(c.c,{}),(0,t.jsx)(p.H,{}),(0,t.jsxs)(o.$,{variant:"outline",onClick:()=>e.push("/dashboard/properties"),className:"flex items-center gap-2 px-4 py-2 bg-slate-700 border-slate-600 hover:bg-slate-600 text-white rounded-lg",children:[(0,t.jsx)(i.A,{className:"h-4 w-4 ".concat("ar"===r?"rotate-180":"")}),(0,t.jsx)("span",{children:u.backToProperties})]})]})]})})}),(0,t.jsx)(x.V,{onSave:b,loading:a})]})})}},83315:(e,r,a)=>{"use strict";a.d(r,{H:()=>d});var t=a(95155),s=a(93509),l=a(86132);function d(){let{language:e,isArabic:r}=(0,l.Y)(),a={ar:{dark:"الوضع المظلم",active:"مُفعل"},en:{dark:"Dark Mode",active:"Active"}}[e];return(0,t.jsx)("div",{className:"\n        flex items-center gap-2 px-4 py-2 h-10\n        bg-slate-800/90 backdrop-blur-md\n        border-2 border-slate-700\n        shadow-lg\n        text-slate-300\n        rounded-xl font-medium\n        ".concat(r?"flex-row-reverse":"flex-row","\n      "),dir:r?"rtl":"ltr",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(s.A,{className:"h-4 w-4 text-amber-400"}),(0,t.jsx)("span",{className:"font-bold",children:a.dark}),(0,t.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full animate-pulse"}),(0,t.jsx)("span",{className:"text-xs text-green-400",children:a.active})]})})}},91160:(e,r,a)=>{Promise.resolve().then(a.bind(a,40619))},92802:(e,r,a)=>{"use strict";a.d(r,{c:()=>i});var t=a(95155),s=a(97168);let l=(0,a(19946).A)("Languages",[["path",{d:"m5 8 6 6",key:"1wu5hv"}],["path",{d:"m4 14 6-6 2-3",key:"1k1g8d"}],["path",{d:"M2 5h12",key:"or177f"}],["path",{d:"M7 2h1",key:"1t2jsx"}],["path",{d:"m22 22-5-10-5 10",key:"don7ne"}],["path",{d:"M14 18h6",key:"1m8k6r"}]]);var d=a(86132);function i(){let{language:e,setLanguage:r,isArabic:a}=(0,d.Y)();return(0,t.jsxs)(s.$,{variant:"outline",size:"sm",onClick:()=>{r("ar"===e?"en":"ar")},className:"\n        flex items-center gap-2 px-4 py-2 h-10\n        bg-white/80 dark:bg-slate-800/80 backdrop-blur-md\n        border-2 border-slate-200 dark:border-slate-700\n        hover:bg-white dark:hover:bg-slate-800\n        hover:border-emerald-300 dark:hover:border-emerald-600\n        hover:shadow-lg transition-all duration-300\n        text-slate-700 dark:text-slate-300\n        rounded-xl font-medium\n        ".concat(a?"flex-row-reverse":"flex-row","\n      "),dir:a?"rtl":"ltr",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(l,{className:"h-4 w-4 text-emerald-600 dark:text-emerald-400"}),(0,t.jsx)("span",{className:"font-bold",children:"ar"===e?"العربية":"English"})]}),(0,t.jsx)("div",{className:"w-px h-4 bg-slate-300 dark:bg-slate-600"}),(0,t.jsx)("span",{className:"text-xs text-slate-500 dark:text-slate-400",children:"ar"===e?"EN":"عر"})]})}},93509:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(19946).A)("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])}},e=>{var r=r=>e(e.s=r);e.O(0,[4277,6071,9509,9855,1721,2842,7681,8441,1684,7358],()=>r(91160)),_N_E=e.O()}]);