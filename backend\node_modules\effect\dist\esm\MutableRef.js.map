{"version": 3, "file": "MutableRef.js", "names": ["Equal", "Dual", "format", "NodeInspectSymbol", "toJSON", "pipeArguments", "TypeId", "Symbol", "for", "MutableRefProto", "toString", "_id", "current", "pipe", "arguments", "make", "value", "ref", "Object", "create", "compareAndSet", "dual", "self", "oldValue", "newValue", "equals", "decrement", "update", "n", "decrementAndGet", "updateAndGet", "get", "getAndDecrement", "getAndUpdate", "getAndIncrement", "getAndSet", "ret", "f", "increment", "incrementAndGet", "set", "setAndGet", "toggle", "_"], "sources": ["../../src/MutableRef.ts"], "sourcesContent": [null], "mappings": "AAAA;;;AAGA,OAAO,KAAKA,KAAK,MAAM,YAAY;AACnC,OAAO,KAAKC,IAAI,MAAM,eAAe;AACrC,SAASC,MAAM,EAAoBC,iBAAiB,EAAEC,MAAM,QAAQ,kBAAkB;AAEtF,SAASC,aAAa,QAAQ,eAAe;AAE7C,MAAMC,MAAM,gBAAkBC,MAAM,CAACC,GAAG,CAAC,mBAAmB,CAAW;AAiBvE,MAAMC,eAAe,GAAyC;EAC5D,CAACH,MAAM,GAAGA,MAAM;EAChBI,QAAQA,CAAA;IACN,OAAOR,MAAM,CAAC,IAAI,CAACE,MAAM,EAAE,CAAC;EAC9B,CAAC;EACDA,MAAMA,CAAA;IACJ,OAAO;MACLO,GAAG,EAAE,YAAY;MACjBC,OAAO,EAAER,MAAM,CAAC,IAAI,CAACQ,OAAO;KAC7B;EACH,CAAC;EACD,CAACT,iBAAiB,IAAC;IACjB,OAAO,IAAI,CAACC,MAAM,EAAE;EACtB,CAAC;EACDS,IAAIA,CAAA;IACF,OAAOR,aAAa,CAAC,IAAI,EAAES,SAAS,CAAC;EACvC;CACD;AAED;;;;AAIA,OAAO,MAAMC,IAAI,GAAOC,KAAQ,IAAmB;EACjD,MAAMC,GAAG,GAAGC,MAAM,CAACC,MAAM,CAACV,eAAe,CAAC;EAC1CQ,GAAG,CAACL,OAAO,GAAGI,KAAK;EACnB,OAAOC,GAAG;AACZ,CAAC;AAED;;;;AAIA,OAAO,MAAMG,aAAa,gBAWtBnB,IAAI,CAACoB,IAAI,CAGX,CAAC,EAAE,CAACC,IAAI,EAAEC,QAAQ,EAAEC,QAAQ,KAAI;EAChC,IAAIxB,KAAK,CAACyB,MAAM,CAACF,QAAQ,EAAED,IAAI,CAACV,OAAO,CAAC,EAAE;IACxCU,IAAI,CAACV,OAAO,GAAGY,QAAQ;IACvB,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd,CAAC,CAAC;AAEF;;;;AAIA,OAAO,MAAME,SAAS,GAAIJ,IAAwB,IAAyBK,MAAM,CAACL,IAAI,EAAGM,CAAC,IAAKA,CAAC,GAAG,CAAC,CAAC;AAErG;;;;AAIA,OAAO,MAAMC,eAAe,GAAIP,IAAwB,IAAaQ,YAAY,CAACR,IAAI,EAAGM,CAAC,IAAKA,CAAC,GAAG,CAAC,CAAC;AAErG;;;;AAIA,OAAO,MAAMG,GAAG,GAAOT,IAAmB,IAAQA,IAAI,CAACV,OAAO;AAE9D;;;;AAIA,OAAO,MAAMoB,eAAe,GAAIV,IAAwB,IAAaW,YAAY,CAACX,IAAI,EAAGM,CAAC,IAAKA,CAAC,GAAG,CAAC,CAAC;AAErG;;;;AAIA,OAAO,MAAMM,eAAe,GAAIZ,IAAwB,IAAaW,YAAY,CAACX,IAAI,EAAGM,CAAC,IAAKA,CAAC,GAAG,CAAC,CAAC;AAErG;;;;AAIA,OAAO,MAAMO,SAAS,gBAWlBlC,IAAI,CAACoB,IAAI,CAGX,CAAC,EAAE,CAACC,IAAI,EAAEN,KAAK,KAAI;EACnB,MAAMoB,GAAG,GAAGd,IAAI,CAACV,OAAO;EACxBU,IAAI,CAACV,OAAO,GAAGI,KAAK;EACpB,OAAOoB,GAAG;AACZ,CAAC,CAAC;AAEF;;;;AAIA,OAAO,MAAMH,YAAY,gBAWrBhC,IAAI,CAACoB,IAAI,CAGX,CAAC,EAAE,CAACC,IAAI,EAAEe,CAAC,KAAKF,SAAS,CAACb,IAAI,EAAEe,CAAC,CAACN,GAAG,CAACT,IAAI,CAAC,CAAC,CAAC,CAAC;AAEhD;;;;AAIA,OAAO,MAAMgB,SAAS,GAAIhB,IAAwB,IAAyBK,MAAM,CAACL,IAAI,EAAGM,CAAC,IAAKA,CAAC,GAAG,CAAC,CAAC;AAErG;;;;AAIA,OAAO,MAAMW,eAAe,GAAIjB,IAAwB,IAAaQ,YAAY,CAACR,IAAI,EAAGM,CAAC,IAAKA,CAAC,GAAG,CAAC,CAAC;AAErG;;;;AAIA,OAAO,MAAMY,GAAG,gBAWZvC,IAAI,CAACoB,IAAI,CAGX,CAAC,EAAE,CAACC,IAAI,EAAEN,KAAK,KAAI;EACnBM,IAAI,CAACV,OAAO,GAAGI,KAAK;EACpB,OAAOM,IAAI;AACb,CAAC,CAAC;AAEF;;;;AAIA,OAAO,MAAMmB,SAAS,gBAWlBxC,IAAI,CAACoB,IAAI,CAGX,CAAC,EAAE,CAACC,IAAI,EAAEN,KAAK,KAAI;EACnBM,IAAI,CAACV,OAAO,GAAGI,KAAK;EACpB,OAAOM,IAAI,CAACV,OAAO;AACrB,CAAC,CAAC;AAEF;;;;AAIA,OAAO,MAAMe,MAAM,gBAWf1B,IAAI,CAACoB,IAAI,CAGX,CAAC,EAAE,CAACC,IAAI,EAAEe,CAAC,KAAKG,GAAG,CAAClB,IAAI,EAAEe,CAAC,CAACN,GAAG,CAACT,IAAI,CAAC,CAAC,CAAC,CAAC;AAE1C;;;;AAIA,OAAO,MAAMQ,YAAY,gBAWrB7B,IAAI,CAACoB,IAAI,CAGX,CAAC,EAAE,CAACC,IAAI,EAAEe,CAAC,KAAKI,SAAS,CAACnB,IAAI,EAAEe,CAAC,CAACN,GAAG,CAACT,IAAI,CAAC,CAAC,CAAC,CAAC;AAEhD;;;;AAIA,OAAO,MAAMoB,MAAM,GAAIpB,IAAyB,IAA0BK,MAAM,CAACL,IAAI,EAAGqB,CAAC,IAAK,CAACA,CAAC,CAAC", "ignoreList": []}