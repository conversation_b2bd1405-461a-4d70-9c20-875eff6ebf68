{"version": 3, "file": "random.js", "names": ["Chunk", "Context", "pipe", "Hash", "PCGRandom", "core", "RandomSymbolKey", "RandomTypeId", "Symbol", "for", "randomTag", "GenericTag", "RandomImpl", "seed", "PRNG", "constructor", "next", "sync", "number", "nextBoolean", "map", "n", "nextInt", "integer", "Number", "MAX_SAFE_INTEGER", "nextRange", "min", "max", "nextIntBetween", "shuffle", "elements", "shuffle<PERSON>ith", "nextIntBounded", "suspend", "Array", "from", "flatMap", "buffer", "numbers", "i", "length", "push", "forEachSequentialDiscard", "k", "swap", "as", "fromIterable", "index1", "index2", "tmp", "make", "hash"], "sources": ["../../../src/internal/random.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,aAAa;AACpC,OAAO,KAAKC,OAAO,MAAM,eAAe;AAExC,SAASC,IAAI,QAAQ,gBAAgB;AACrC,OAAO,KAAKC,IAAI,MAAM,YAAY;AAElC,OAAO,KAAKC,SAAS,MAAM,aAAa;AACxC,OAAO,KAAKC,IAAI,MAAM,WAAW;AAEjC;AACA,MAAMC,eAAe,GAAG,eAAe;AAEvC;AACA,OAAO,MAAMC,YAAY,gBAAwBC,MAAM,CAACC,GAAG,CACzDH,eAAe,CACO;AAExB;AACA,OAAO,MAAMI,SAAS,gBAA8CT,OAAO,CAACU,UAAU,CAAC,eAAe,CAAC;AACvG;AACA,MAAMC,UAAU;EAKOC,IAAA;EAJZ,CAACN,YAAY,IAAyBA,YAAY;EAElDO,IAAI;EAEbC,YAAqBF,IAAY;IAAZ,KAAAA,IAAI,GAAJA,IAAI;IACvB,IAAI,CAACC,IAAI,GAAG,IAAIV,SAAS,CAACA,SAAS,CAACS,IAAI,CAAC;EAC3C;EAEA,IAAIG,IAAIA,CAAA;IACN,OAAOX,IAAI,CAACY,IAAI,CAAC,MAAM,IAAI,CAACH,IAAI,CAACI,MAAM,EAAE,CAAC;EAC5C;EAEA,IAAIC,WAAWA,CAAA;IACb,OAAOd,IAAI,CAACe,GAAG,CAAC,IAAI,CAACJ,IAAI,EAAGK,CAAC,IAAKA,CAAC,GAAG,GAAG,CAAC;EAC5C;EAEA,IAAIC,OAAOA,CAAA;IACT,OAAOjB,IAAI,CAACY,IAAI,CAAC,MAAM,IAAI,CAACH,IAAI,CAACS,OAAO,CAACC,MAAM,CAACC,gBAAgB,CAAC,CAAC;EACpE;EAEAC,SAASA,CAACC,GAAW,EAAEC,GAAW;IAChC,OAAOvB,IAAI,CAACe,GAAG,CAAC,IAAI,CAACJ,IAAI,EAAGK,CAAC,IAAK,CAACO,GAAG,GAAGD,GAAG,IAAIN,CAAC,GAAGM,GAAG,CAAC;EAC1D;EAEAE,cAAcA,CAACF,GAAW,EAAEC,GAAW;IACrC,OAAOvB,IAAI,CAACY,IAAI,CAAC,MAAM,IAAI,CAACH,IAAI,CAACS,OAAO,CAACK,GAAG,GAAGD,GAAG,CAAC,GAAGA,GAAG,CAAC;EAC5D;EAEAG,OAAOA,CAAIC,QAAqB;IAC9B,OAAOC,WAAW,CAACD,QAAQ,EAAGV,CAAC,IAAK,IAAI,CAACQ,cAAc,CAAC,CAAC,EAAER,CAAC,CAAC,CAAC;EAChE;;AAGF,MAAMW,WAAW,GAAGA,CAClBD,QAAqB,EACrBE,cAAoD,KACnB;EACjC,OAAO5B,IAAI,CAAC6B,OAAO,CAAC,MAClBhC,IAAI,CACFG,IAAI,CAACY,IAAI,CAAC,MAAMkB,KAAK,CAACC,IAAI,CAACL,QAAQ,CAAC,CAAC,EACrC1B,IAAI,CAACgC,OAAO,CAAEC,MAAM,IAAI;IACtB,MAAMC,OAAO,GAAkB,EAAE;IACjC,KAAK,IAAIC,CAAC,GAAGF,MAAM,CAACG,MAAM,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,GAAGA,CAAC,GAAG,CAAC,EAAE;MAC7CD,OAAO,CAACG,IAAI,CAACF,CAAC,CAAC;IACjB;IACA,OAAOtC,IAAI,CACTqC,OAAO,EACPlC,IAAI,CAACsC,wBAAwB,CAAEtB,CAAC,IAC9BnB,IAAI,CACF+B,cAAc,CAACZ,CAAC,CAAC,EACjBhB,IAAI,CAACe,GAAG,CAAEwB,CAAC,IAAKC,IAAI,CAACP,MAAM,EAAEjB,CAAC,GAAG,CAAC,EAAEuB,CAAC,CAAC,CAAC,CACxC,CACF,EACDvC,IAAI,CAACyC,EAAE,CAAC9C,KAAK,CAAC+C,YAAY,CAACT,MAAM,CAAC,CAAC,CACpC;EACH,CAAC,CAAC,CACH,CACF;AACH,CAAC;AAED,MAAMO,IAAI,GAAGA,CAAIP,MAAgB,EAAEU,MAAc,EAAEC,MAAc,KAAc;EAC7E,MAAMC,GAAG,GAAGZ,MAAM,CAACU,MAAM,CAAE;EAC3BV,MAAM,CAACU,MAAM,CAAC,GAAGV,MAAM,CAACW,MAAM,CAAE;EAChCX,MAAM,CAACW,MAAM,CAAC,GAAGC,GAAG;EACpB,OAAOZ,MAAM;AACf,CAAC;AAED,OAAO,MAAMa,IAAI,GAAOtC,IAAO,IAAoB,IAAID,UAAU,CAACT,IAAI,CAACiD,IAAI,CAACvC,IAAI,CAAC,CAAC", "ignoreList": []}