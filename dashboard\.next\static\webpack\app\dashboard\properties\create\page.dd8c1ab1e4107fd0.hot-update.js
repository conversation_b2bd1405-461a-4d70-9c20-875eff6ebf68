"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/properties/create/page",{

/***/ "(app-pages-browser)/./components/ThemeSwitcher.tsx":
/*!**************************************!*\
  !*** ./components/ThemeSwitcher.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CompactThemeSwitcher: () => (/* binding */ CompactThemeSwitcher),\n/* harmony export */   ThemeSwitcher: () => (/* binding */ ThemeSwitcher)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Moon_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Moon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/useSimpleLanguage */ \"(app-pages-browser)/./hooks/useSimpleLanguage.tsx\");\n/* __next_internal_client_entry_do_not_use__ ThemeSwitcher,CompactThemeSwitcher auto */ \nvar _s = $RefreshSig$();\n\n\n/**\n * Dark mode indicator component (dark mode only)\n * Shows current dark mode status without toggle option\n */ function ThemeSwitcher() {\n    _s();\n    const { language, isArabic } = (0,_hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_1__.useSimpleLanguage)();\n    const themeText = {\n        ar: {\n            dark: 'الوضع المظلم',\n            active: 'مُفعل'\n        },\n        en: {\n            dark: 'Dark Mode',\n            active: 'Active'\n        }\n    };\n    const t = themeText[language];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\\n        flex items-center gap-2 px-4 py-2 h-10\\n        bg-slate-800/90 backdrop-blur-md\\n        border-2 border-slate-700\\n        shadow-lg\\n        text-slate-300\\n        rounded-xl font-medium\\n        \".concat(isArabic ? 'flex-row-reverse' : 'flex-row', \"\\n      \"),\n        dir: isArabic ? 'rtl' : 'ltr',\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"h-4 w-4 text-amber-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\ThemeSwitcher.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"font-bold\",\n                    children: t.dark\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\ThemeSwitcher.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\ThemeSwitcher.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-xs text-green-400\",\n                    children: t.active\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\ThemeSwitcher.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\ThemeSwitcher.tsx\",\n            lineNumber: 41,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\ThemeSwitcher.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n_s(ThemeSwitcher, \"2rjfvwuffGHCSE4Q4plIAiVYgTc=\", false, function() {\n    return [\n        _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_1__.useSimpleLanguage\n    ];\n});\n_c = ThemeSwitcher;\n/**\n * Compact dark mode indicator for smaller spaces\n */ function CompactThemeSwitcher() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \" w-12 h-12 p-0 rounded-xl bg-slate-800/80 backdrop-blur-sm border border-slate-700 flex items-center justify-center shadow-lg \",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            className: \"h-5 w-5 text-amber-400\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\ThemeSwitcher.tsx\",\n            lineNumber: 69,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\ThemeSwitcher.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\n_c1 = CompactThemeSwitcher;\nvar _c, _c1;\n$RefreshReg$(_c, \"ThemeSwitcher\");\n$RefreshReg$(_c1, \"CompactThemeSwitcher\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ThemeSwitcher.tsx\n"));

/***/ })

});