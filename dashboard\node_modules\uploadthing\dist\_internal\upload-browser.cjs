var Function = require('effect/Function');
var Micro = require('effect/Micro');
var Predicate = require('effect/Predicate');
var shared = require('@uploadthing/shared');
var deprecations_cjs = require('./deprecations.cjs');
var utReporter_cjs = require('./ut-reporter.cjs');

function _interopNamespace(e) {
  if (e && e.__esModule) return e;
  var n = Object.create(null);
  if (e) {
    Object.keys(e).forEach(function (k) {
      if (k !== 'default') {
        var d = Object.getOwnPropertyDescriptor(e, k);
        Object.defineProperty(n, k, d.get ? d : {
          enumerable: true,
          get: function () { return e[k]; }
        });
      }
    });
  }
  n.default = e;
  return n;
}

var Micro__namespace = /*#__PURE__*/_interopNamespace(Micro);

var version = "7.7.2";

const uploadWithProgress = (file, rangeStart, presigned, onUploadProgress)=>Micro__namespace.async((resume)=>{
        const xhr = new XMLHttpRequest();
        xhr.open("PUT", presigned.url, true);
        xhr.setRequestHeader("Range", `bytes=${rangeStart}-`);
        xhr.setRequestHeader("x-uploadthing-version", version);
        xhr.responseType = "json";
        let previousLoaded = 0;
        xhr.upload.addEventListener("progress", ({ loaded })=>{
            const delta = loaded - previousLoaded;
            onUploadProgress?.({
                loaded,
                delta
            });
            previousLoaded = loaded;
        });
        xhr.addEventListener("load", ()=>{
            if (xhr.status >= 200 && xhr.status < 300 && Predicate.isRecord(xhr.response)) {
                if (Predicate.hasProperty(xhr.response, "error")) {
                    resume(new shared.UploadThingError({
                        code: "UPLOAD_FAILED",
                        message: String(xhr.response.error),
                        data: xhr.response
                    }));
                } else {
                    resume(Micro__namespace.succeed(xhr.response));
                }
            } else {
                resume(new shared.UploadThingError({
                    code: "UPLOAD_FAILED",
                    message: `XHR failed ${xhr.status} ${xhr.statusText}`,
                    data: xhr.response
                }));
            }
        });
        // Is there a case when the client would throw and
        // ingest server not knowing about it? idts?
        xhr.addEventListener("error", ()=>{
            resume(new shared.UploadThingError({
                code: "UPLOAD_FAILED"
            }));
        });
        const formData = new FormData();
        /**
     * iOS/React Native FormData handling requires special attention:
     *
     * Issue: In React Native, iOS crashes with "attempt to insert nil object" when appending File directly
     * to FormData. This happens because iOS tries to create NSDictionary from the file object and expects
     * specific structure {uri, type, name}.
     *
     *
     * Note: Don't try to use Blob or modify File object - iOS specifically needs plain object
     * with these properties to create valid NSDictionary.
     */ if ("uri" in file) {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
            formData.append("file", {
                uri: file.uri,
                type: file.type,
                name: file.name,
                ...rangeStart > 0 && {
                    range: rangeStart
                }
            });
        } else {
            formData.append("file", rangeStart > 0 ? file.slice(rangeStart) : file);
        }
        xhr.send(formData);
        return Micro__namespace.sync(()=>xhr.abort());
    });
const uploadFile = (file, presigned, opts)=>shared.fetchEff(presigned.url, {
        method: "HEAD"
    }).pipe(Micro__namespace.map(({ headers })=>parseInt(headers.get("x-ut-range-start") ?? "0", 10)), Micro__namespace.tap((start)=>opts.onUploadProgress?.({
            delta: start,
            loaded: start
        })), Micro__namespace.flatMap((start)=>uploadWithProgress(file, start, presigned, (progressEvent)=>opts.onUploadProgress?.({
                delta: progressEvent.delta,
                loaded: progressEvent.loaded + start
            }))), Micro__namespace.map(Function.unsafeCoerce), Micro__namespace.map((uploadResponse)=>({
            name: file.name,
            size: file.size,
            key: presigned.key,
            lastModified: file.lastModified,
            serverData: uploadResponse.serverData,
            get url () {
                deprecations_cjs.logDeprecationWarning("`file.url` is deprecated and will be removed in uploadthing v9. Use `file.ufsUrl` instead.");
                return uploadResponse.url;
            },
            get appUrl () {
                deprecations_cjs.logDeprecationWarning("`file.appUrl` is deprecated and will be removed in uploadthing v9. Use `file.ufsUrl` instead.");
                return uploadResponse.appUrl;
            },
            ufsUrl: uploadResponse.ufsUrl,
            customId: presigned.customId,
            type: file.type,
            fileHash: uploadResponse.fileHash
        })));
const uploadFilesInternal = (endpoint, opts)=>{
    // classic service right here
    const reportEventToUT = utReporter_cjs.createUTReporter({
        endpoint: String(endpoint),
        package: opts.package,
        url: opts.url,
        headers: opts.headers
    });
    const totalSize = opts.files.reduce((acc, f)=>acc + f.size, 0);
    let totalLoaded = 0;
    return Micro__namespace.flatMap(reportEventToUT("upload", {
        input: "input" in opts ? opts.input : null,
        files: opts.files.map((f)=>({
                name: f.name,
                size: f.size,
                type: f.type,
                lastModified: f.lastModified
            }))
    }), (presigneds)=>Micro__namespace.forEach(presigneds, (presigned, i)=>Micro__namespace.flatMap(Micro__namespace.sync(()=>opts.onUploadBegin?.({
                    file: opts.files[i].name
                })), ()=>uploadFile(opts.files[i], presigned, {
                    onUploadProgress: (ev)=>{
                        totalLoaded += ev.delta;
                        opts.onUploadProgress?.({
                            file: opts.files[i],
                            progress: ev.loaded / opts.files[i].size * 100,
                            loaded: ev.loaded,
                            delta: ev.delta,
                            totalLoaded,
                            totalProgress: totalLoaded / totalSize
                        });
                    }
                })), {
            concurrency: 6
        }));
};

exports.uploadFile = uploadFile;
exports.uploadFilesInternal = uploadFilesInternal;
