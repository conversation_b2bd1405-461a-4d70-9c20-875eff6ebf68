declare const text: {
    readonly "text/cache-manifest": {
        readonly source: "iana";
        readonly extensions: readonly ["appcache", "manifest"];
    };
    readonly "text/calendar": {
        readonly source: "iana";
        readonly extensions: readonly ["ics", "ifb"];
    };
    readonly "text/css": {
        readonly source: "iana";
        readonly charset: "UTF-8";
        readonly extensions: readonly ["css"];
    };
    readonly "text/csv": {
        readonly source: "iana";
        readonly extensions: readonly ["csv"];
    };
    readonly "text/html": {
        readonly source: "iana";
        readonly extensions: readonly ["html", "htm", "shtml"];
    };
    readonly "text/markdown": {
        readonly source: "iana";
        readonly extensions: readonly ["markdown", "md"];
    };
    readonly "text/mathml": {
        readonly source: "nginx";
        readonly extensions: readonly ["mml"];
    };
    readonly "text/n3": {
        readonly source: "iana";
        readonly charset: "UTF-8";
        readonly extensions: readonly ["n3"];
    };
    readonly "text/plain": {
        readonly source: "iana";
        readonly extensions: readonly ["txt", "text", "conf", "def", "list", "log", "in", "ini"];
    };
    readonly "text/prs.lines.tag": {
        readonly source: "iana";
        readonly extensions: readonly ["dsc"];
    };
    readonly "text/richtext": {
        readonly source: "iana";
        readonly extensions: readonly ["rtx"];
    };
    readonly "text/rtf": {
        readonly source: "iana";
        readonly extensions: readonly ["rtf"];
    };
    readonly "text/sgml": {
        readonly source: "iana";
        readonly extensions: readonly ["sgml", "sgm"];
    };
    readonly "text/shex": {
        readonly source: "iana";
        readonly extensions: readonly ["shex"];
    };
    readonly "text/spdx": {
        readonly source: "iana";
        readonly extensions: readonly ["spdx"];
    };
    readonly "text/tab-separated-values": {
        readonly source: "iana";
        readonly extensions: readonly ["tsv"];
    };
    readonly "text/troff": {
        readonly source: "iana";
        readonly extensions: readonly ["t", "tr", "roff", "man", "me", "ms"];
    };
    readonly "text/turtle": {
        readonly source: "iana";
        readonly charset: "UTF-8";
        readonly extensions: readonly ["ttl"];
    };
    readonly "text/uri-list": {
        readonly source: "iana";
        readonly extensions: readonly ["uri", "uris", "urls"];
    };
    readonly "text/vcard": {
        readonly source: "iana";
        readonly extensions: readonly ["vcard"];
    };
    readonly "text/vnd.curl": {
        readonly source: "iana";
        readonly extensions: readonly ["curl"];
    };
    readonly "text/vnd.curl.dcurl": {
        readonly source: "apache";
        readonly extensions: readonly ["dcurl"];
    };
    readonly "text/vnd.curl.mcurl": {
        readonly source: "apache";
        readonly extensions: readonly ["mcurl"];
    };
    readonly "text/vnd.curl.scurl": {
        readonly source: "apache";
        readonly extensions: readonly ["scurl"];
    };
    readonly "text/vnd.dvb.subtitle": {
        readonly source: "iana";
        readonly extensions: readonly ["sub"];
    };
    readonly "text/vnd.familysearch.gedcom": {
        readonly source: "iana";
        readonly extensions: readonly ["ged"];
    };
    readonly "text/vnd.fly": {
        readonly source: "iana";
        readonly extensions: readonly ["fly"];
    };
    readonly "text/vnd.fmi.flexstor": {
        readonly source: "iana";
        readonly extensions: readonly ["flx"];
    };
    readonly "text/vnd.graphviz": {
        readonly source: "iana";
        readonly extensions: readonly ["gv"];
    };
    readonly "text/vnd.in3d.3dml": {
        readonly source: "iana";
        readonly extensions: readonly ["3dml"];
    };
    readonly "text/vnd.in3d.spot": {
        readonly source: "iana";
        readonly extensions: readonly ["spot"];
    };
    readonly "text/vnd.sun.j2me.app-descriptor": {
        readonly source: "iana";
        readonly charset: "UTF-8";
        readonly extensions: readonly ["jad"];
    };
    readonly "text/vnd.wap.wml": {
        readonly source: "iana";
        readonly extensions: readonly ["wml"];
    };
    readonly "text/vnd.wap.wmlscript": {
        readonly source: "iana";
        readonly extensions: readonly ["wmls"];
    };
    readonly "text/vtt": {
        readonly source: "iana";
        readonly charset: "UTF-8";
        readonly extensions: readonly ["vtt"];
    };
    readonly "text/x-asm": {
        readonly source: "apache";
        readonly extensions: readonly ["s", "asm"];
    };
    readonly "text/x-c": {
        readonly source: "apache";
        readonly extensions: readonly ["c", "cc", "cxx", "cpp", "h", "hh", "dic"];
    };
    readonly "text/x-component": {
        readonly source: "nginx";
        readonly extensions: readonly ["htc"];
    };
    readonly "text/x-fortran": {
        readonly source: "apache";
        readonly extensions: readonly ["f", "for", "f77", "f90"];
    };
    readonly "text/x-java-source": {
        readonly source: "apache";
        readonly extensions: readonly ["java"];
    };
    readonly "text/x-nfo": {
        readonly source: "apache";
        readonly extensions: readonly ["nfo"];
    };
    readonly "text/x-opml": {
        readonly source: "apache";
        readonly extensions: readonly ["opml"];
    };
    readonly "text/x-pascal": {
        readonly source: "apache";
        readonly extensions: readonly ["p", "pas"];
    };
    readonly "text/x-setext": {
        readonly source: "apache";
        readonly extensions: readonly ["etx"];
    };
    readonly "text/x-sfv": {
        readonly source: "apache";
        readonly extensions: readonly ["sfv"];
    };
    readonly "text/x-uuencode": {
        readonly source: "apache";
        readonly extensions: readonly ["uu"];
    };
    readonly "text/x-vcalendar": {
        readonly source: "apache";
        readonly extensions: readonly ["vcs"];
    };
    readonly "text/x-vcard": {
        readonly source: "apache";
        readonly extensions: readonly ["vcf"];
    };
    readonly "text/xml": {
        readonly source: "iana";
        readonly extensions: readonly ["xml"];
    };
};

export { text };
