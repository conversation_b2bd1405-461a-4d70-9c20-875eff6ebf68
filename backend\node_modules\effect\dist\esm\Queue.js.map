{"version": 3, "file": "Queue.js", "names": ["internal", "EnqueueTypeId", "DequeueTypeId", "QueueStrategyTypeId", "BackingQueueTypeId", "isQueue", "isDequeue", "isEnqueue", "backPressureStrategy", "droppingStrategy", "slidingStrategy", "make", "bounded", "dropping", "sliding", "unbounded", "capacity", "size", "isEmpty", "isFull", "isShutdown", "await<PERSON><PERSON><PERSON>down", "shutdown", "offer", "unsafeOffer", "offerAll", "poll", "take", "takeAll", "takeUpTo", "takeBetween", "takeN"], "sources": ["../../src/Queue.ts"], "sourcesContent": [null], "mappings": "AAMA,OAAO,KAAKA,QAAQ,MAAM,qBAAqB;AAQ/C;;;;AAIA,OAAO,MAAMC,aAAa,GAAkBD,QAAQ,CAACC,aAAa;AAQlE;;;;AAIA,OAAO,MAAMC,aAAa,GAAkBF,QAAQ,CAACE,aAAa;AAQlE;;;;AAIA,OAAO,MAAMC,mBAAmB,GAAkBH,QAAQ,CAACG,mBAAmB;AAQ9E;;;;AAIA,OAAO,MAAMC,kBAAkB,GAAkBJ,QAAQ,CAACI,kBAAkB;AAgU5E;;;;;;AAMA,OAAO,MAAMC,OAAO,GAAwCL,QAAQ,CAACK,OAAO;AAE5E;;;;;;AAMA,OAAO,MAAMC,SAAS,GAA0CN,QAAQ,CAACM,SAAS;AAElF;;;;;;AAMA,OAAO,MAAMC,SAAS,GAA0CP,QAAQ,CAACO,SAAS;AAElF;;;;AAIA,OAAO,MAAMC,oBAAoB,GAAyBR,QAAQ,CAACQ,oBAAoB;AAEvF;;;;AAIA,OAAO,MAAMC,gBAAgB,GAAyBT,QAAQ,CAACS,gBAAgB;AAE/E;;;;AAIA,OAAO,MAAMC,eAAe,GAAyBV,QAAQ,CAACU,eAAe;AAE7E;;;;AAIA,OAAO,MAAMC,IAAI,GAAkFX,QAAQ,CAACW,IAAI;AAEhH;;;;;;;;;;;;AAYA,OAAO,MAAMC,OAAO,GAA8DZ,QAAQ,CAACY,OAAO;AAElG;;;;;;;;;;;;;AAaA,OAAO,MAAMC,QAAQ,GAA8Db,QAAQ,CAACa,QAAQ;AAEpG;;;;;;;;;;;;;AAaA,OAAO,MAAMC,OAAO,GAA8Dd,QAAQ,CAACc,OAAO;AAElG;;;;;;AAMA,OAAO,MAAMC,SAAS,GAAqCf,QAAQ,CAACe,SAAS;AAE7E;;;;;;AAMA,OAAO,MAAMC,QAAQ,GAAiDhB,QAAQ,CAACgB,QAAQ;AAEvF;;;;;;;;AAQA,OAAO,MAAMC,IAAI,GAAgEjB,QAAQ,CAACiB,IAAI;AAE9F;;;;;;AAMA,OAAO,MAAMC,OAAO,GAAiElB,QAAQ,CAACkB,OAAO;AAErG;;;;;;;AAOA,OAAO,MAAMC,MAAM,GAAiEnB,QAAQ,CAACmB,MAAM;AAEnG;;;;;;AAMA,OAAO,MAAMC,UAAU,GAAiEpB,QAAQ,CAACoB,UAAU;AAE3G;;;;;;;;AAQA,OAAO,MAAMC,aAAa,GAA8DrB,QAAQ,CAACqB,aAAa;AAE9G;;;;;;;AAOA,OAAO,MAAMC,QAAQ,GAA8DtB,QAAQ,CAACsB,QAAQ;AAEpG;;;;;;AAMA,OAAO,MAAMC,KAAK,GAedvB,QAAQ,CAACuB,KAAK;AAElB;;;;;;AAMA,OAAO,MAAMC,WAAW,GAepBxB,QAAQ,CAACwB,WAAW;AAExB;;;;;;;;;;;;;;;;;;AAkBA,OAAO,MAAMC,QAAQ,GAuCjBzB,QAAQ,CAACyB,QAAQ;AAErB;;;;;;;AAOA,OAAO,MAAMC,IAAI,GAA6D1B,QAAQ,CAAC0B,IAAI;AAE3F;;;;;;;AAOA,OAAO,MAAMC,IAAI,GAA8C3B,QAAQ,CAAC2B,IAAI;AAE5E;;;;;;;AAOA,OAAO,MAAMC,OAAO,GAA2D5B,QAAQ,CAAC4B,OAAO;AAE/F;;;;;;AAMA,OAAO,MAAMC,QAAQ,GAejB7B,QAAQ,CAAC6B,QAAQ;AAErB;;;;;;;;AAQA,OAAO,MAAMC,WAAW,GAmBpB9B,QAAQ,CAAC8B,WAAW;AAExB;;;;;;;;AAQA,OAAO,MAAMC,KAAK,GAmBd/B,QAAQ,CAAC+B,KAAK", "ignoreList": []}