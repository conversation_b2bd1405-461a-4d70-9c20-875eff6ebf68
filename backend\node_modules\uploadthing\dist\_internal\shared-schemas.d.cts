import * as S from 'effect/Schema';
import { <PERSON><PERSON> } from '@uploadthing/shared';

declare const ContentDispositionSchema: S.Literal<["inline", "attachment"]>;
declare const ACLSchema: S.Literal<["public-read", "private"]>;
/**
 * Valid options for the `?actionType` query param
 */
declare const ActionType: S.Literal<["upload"]>;
/**
 * Valid options for the `uploadthing-hook` header
 * for requests coming from UT server
 */
declare const UploadThingHook: S.Literal<["callback", "error"]>;
declare const ParsedToken: S.Struct<{
    apiKey: S.Redacted<S.filter<typeof S.String>>;
    appId: typeof S.String;
    regions: S.NonEmptyArray<typeof S.String>;
    ingestHost: S.optionalWith<typeof S.String, {
        default: () => string;
    }>;
}>;
declare const UploadThingToken: S.transform<S.transform<S.Schema<Uint8Array<ArrayBufferLike>, string, never>, S.transform<typeof S.Uint8ArrayFromSelf, typeof S.String>>, S.transform<S.SchemaClass<unknown, string, never>, S.Struct<{
    apiKey: S.Redacted<S.filter<typeof S.String>>;
    appId: typeof S.String;
    regions: S.NonEmptyArray<typeof S.String>;
    ingestHost: S.optionalWith<typeof S.String, {
        default: () => string;
    }>;
}>>>;
declare const FileUploadData_base: S.Class<FileUploadData, {
    name: typeof S.String;
    size: typeof S.Number;
    type: typeof S.String;
    lastModified: S.optional<typeof S.Number>;
}, S.Struct.Encoded<{
    name: typeof S.String;
    size: typeof S.Number;
    type: typeof S.String;
    lastModified: S.optional<typeof S.Number>;
}>, never, {
    readonly name: string;
} & {
    readonly size: number;
} & {
    readonly type: string;
} & {
    readonly lastModified?: number | undefined;
}, {}, {}>;
/**
 * =============================================================================
 * ======================== File Type Hierarchy ===============================
 * =============================================================================
 */
/**
 * Properties from the web File object, this is what the client sends when initiating an upload
 */
declare class FileUploadData extends FileUploadData_base {
}
declare const FileUploadDataWithCustomId_base: S.Class<FileUploadDataWithCustomId, {
    name: typeof S.String;
    size: typeof S.Number;
    type: typeof S.String;
    lastModified: S.optional<typeof S.Number>;
} & {
    customId: S.NullOr<typeof S.String>;
}, {
    readonly name: string;
    readonly size: number;
    readonly type: string;
} & {
    readonly lastModified?: number | undefined;
} & {
    readonly customId: string | null;
} & {}, never, {
    readonly name: string;
} & {
    readonly size: number;
} & {
    readonly type: string;
} & {
    readonly lastModified?: number | undefined;
} & {
    readonly customId: string | null;
}, FileUploadData, {}>;
/**
 * `.middleware()` can add a customId to the incoming file data
 */
declare class FileUploadDataWithCustomId extends FileUploadDataWithCustomId_base {
}
declare const UploadedFileData_base: S.Class<UploadedFileData, {
    name: typeof S.String;
    size: typeof S.Number;
    type: typeof S.String;
    lastModified: S.optional<typeof S.Number>;
} & {
    customId: S.NullOr<typeof S.String>;
} & {
    key: typeof S.String;
    /**
     * @deprecated
     * This field will be removed in uploadthing v9. Use `ufsUrl` instead.
     */
    url: typeof S.String;
    /**
     * @deprecated
     * This field will be removed in uploadthing v9. Use `ufsUrl` instead.
     */
    appUrl: typeof S.String;
    ufsUrl: typeof S.String;
    fileHash: typeof S.String;
}, {
    readonly name: string;
    readonly size: number;
    readonly type: string;
} & {
    readonly lastModified?: number | undefined;
} & {
    readonly customId: string | null;
} & {} & {
    readonly key: string;
    readonly url: string;
    readonly appUrl: string;
    readonly ufsUrl: string;
    readonly fileHash: string;
} & {}, never, {
    readonly name: string;
} & {
    readonly size: number;
} & {
    readonly type: string;
} & {
    readonly lastModified?: number | undefined;
} & {
    readonly customId: string | null;
} & {
    readonly key: string;
} & {
    readonly url: string;
} & {
    readonly appUrl: string;
} & {
    readonly ufsUrl: string;
} & {
    readonly fileHash: string;
}, FileUploadDataWithCustomId, {}>;
/**
 * When files are uploaded, we get back
 * - a key
 * - URLs for the file
 * - the hash (md5-hex) of the uploaded file's contents
 */
declare class UploadedFileData extends UploadedFileData_base {
}
/**
 * When the client has uploaded a file and polled for data returned by `.onUploadComplete()`
 */
interface ClientUploadedFileData<T> extends UploadedFileData {
    /**
     * Matches what's returned from the serverside `onUploadComplete` callback
     */
    readonly serverData: T;
}
declare const NewPresignedUrl_base: S.Class<NewPresignedUrl, {
    url: typeof S.String;
    key: typeof S.String;
    customId: S.NullOr<typeof S.String>;
    name: typeof S.String;
}, S.Struct.Encoded<{
    url: typeof S.String;
    key: typeof S.String;
    customId: S.NullOr<typeof S.String>;
    name: typeof S.String;
}>, never, {
    readonly name: string;
} & {
    readonly customId: string | null;
} & {
    readonly key: string;
} & {
    readonly url: string;
}, {}, {}>;
/**
 * =============================================================================
 * ======================== Server Response Schemas ============================
 * =============================================================================
 */
declare class NewPresignedUrl extends NewPresignedUrl_base {
}
declare const MetadataFetchStreamPart_base: S.Class<MetadataFetchStreamPart, {
    payload: typeof S.String;
    signature: typeof S.String;
    hook: S.Literal<["callback", "error"]>;
}, S.Struct.Encoded<{
    payload: typeof S.String;
    signature: typeof S.String;
    hook: S.Literal<["callback", "error"]>;
}>, never, {
    readonly payload: string;
} & {
    readonly signature: string;
} & {
    readonly hook: "callback" | "error";
}, {}, {}>;
declare class MetadataFetchStreamPart extends MetadataFetchStreamPart_base {
}
declare const MetadataFetchResponse_base: S.Class<MetadataFetchResponse, {
    ok: typeof S.Boolean;
}, S.Struct.Encoded<{
    ok: typeof S.Boolean;
}>, never, {
    readonly ok: boolean;
}, {}, {}>;
declare class MetadataFetchResponse extends MetadataFetchResponse_base {
}
declare const CallbackResultResponse_base: S.Class<CallbackResultResponse, {
    ok: typeof S.Boolean;
}, S.Struct.Encoded<{
    ok: typeof S.Boolean;
}>, never, {
    readonly ok: boolean;
}, {}, {}>;
declare class CallbackResultResponse extends CallbackResultResponse_base {
}
declare const UploadActionPayload_base: S.Class<UploadActionPayload, {
    files: S.Array$<typeof FileUploadData>;
    input: S.Schema<Json>;
}, S.Struct.Encoded<{
    files: S.Array$<typeof FileUploadData>;
    input: S.Schema<Json>;
}>, never, {
    readonly files: readonly FileUploadData[];
} & {
    readonly input: Json;
}, {}, {}>;
/**
 * =============================================================================
 * ======================== Client Action Payloads ============================
 * =============================================================================
 */
declare class UploadActionPayload extends UploadActionPayload_base {
}

export { ACLSchema, ActionType, CallbackResultResponse, ContentDispositionSchema, FileUploadData, FileUploadDataWithCustomId, MetadataFetchResponse, MetadataFetchStreamPart, NewPresignedUrl, ParsedToken, UploadActionPayload, UploadThingHook, UploadThingToken, UploadedFileData };
export type { ClientUploadedFileData };
