declare const audio: {
    readonly "audio/3gpp": {
        readonly source: "iana";
        readonly extensions: readonly ["3gpp"];
    };
    readonly "audio/adpcm": {
        readonly source: "apache";
        readonly extensions: readonly ["adp"];
    };
    readonly "audio/amr": {
        readonly source: "iana";
        readonly extensions: readonly ["amr"];
    };
    readonly "audio/basic": {
        readonly source: "iana";
        readonly extensions: readonly ["au", "snd"];
    };
    readonly "audio/midi": {
        readonly source: "apache";
        readonly extensions: readonly ["mid", "midi", "kar", "rmi"];
    };
    readonly "audio/mobile-xmf": {
        readonly source: "iana";
        readonly extensions: readonly ["mxmf"];
    };
    readonly "audio/mp4": {
        readonly source: "iana";
        readonly extensions: readonly ["m4a", "mp4a"];
    };
    readonly "audio/mpeg": {
        readonly source: "iana";
        readonly extensions: readonly ["mpga", "mp2", "mp2a", "mp3", "m2a", "m3a"];
    };
    readonly "audio/ogg": {
        readonly source: "iana";
        readonly extensions: readonly ["oga", "ogg", "spx", "opus"];
    };
    readonly "audio/s3m": {
        readonly source: "apache";
        readonly extensions: readonly ["s3m"];
    };
    readonly "audio/silk": {
        readonly source: "apache";
        readonly extensions: readonly ["sil"];
    };
    readonly "audio/vnd.dece.audio": {
        readonly source: "iana";
        readonly extensions: readonly ["uva", "uvva"];
    };
    readonly "audio/vnd.digital-winds": {
        readonly source: "iana";
        readonly extensions: readonly ["eol"];
    };
    readonly "audio/vnd.dra": {
        readonly source: "iana";
        readonly extensions: readonly ["dra"];
    };
    readonly "audio/vnd.dts": {
        readonly source: "iana";
        readonly extensions: readonly ["dts"];
    };
    readonly "audio/vnd.dts.hd": {
        readonly source: "iana";
        readonly extensions: readonly ["dtshd"];
    };
    readonly "audio/vnd.lucent.voice": {
        readonly source: "iana";
        readonly extensions: readonly ["lvp"];
    };
    readonly "audio/vnd.ms-playready.media.pya": {
        readonly source: "iana";
        readonly extensions: readonly ["pya"];
    };
    readonly "audio/vnd.nuera.ecelp4800": {
        readonly source: "iana";
        readonly extensions: readonly ["ecelp4800"];
    };
    readonly "audio/vnd.nuera.ecelp7470": {
        readonly source: "iana";
        readonly extensions: readonly ["ecelp7470"];
    };
    readonly "audio/vnd.nuera.ecelp9600": {
        readonly source: "iana";
        readonly extensions: readonly ["ecelp9600"];
    };
    readonly "audio/vnd.rip": {
        readonly source: "iana";
        readonly extensions: readonly ["rip"];
    };
    readonly "audio/webm": {
        readonly source: "apache";
        readonly extensions: readonly ["weba"];
    };
    readonly "audio/x-aac": {
        readonly source: "apache";
        readonly extensions: readonly ["aac"];
    };
    readonly "audio/x-aiff": {
        readonly source: "apache";
        readonly extensions: readonly ["aif", "aiff", "aifc"];
    };
    readonly "audio/x-caf": {
        readonly source: "apache";
        readonly extensions: readonly ["caf"];
    };
    readonly "audio/x-flac": {
        readonly source: "apache";
        readonly extensions: readonly ["flac"];
    };
    readonly "audio/x-m4a": {
        readonly source: "nginx";
        readonly extensions: readonly ["m4a"];
    };
    readonly "audio/x-matroska": {
        readonly source: "apache";
        readonly extensions: readonly ["mka"];
    };
    readonly "audio/x-mpegurl": {
        readonly source: "apache";
        readonly extensions: readonly ["m3u"];
    };
    readonly "audio/x-ms-wax": {
        readonly source: "apache";
        readonly extensions: readonly ["wax"];
    };
    readonly "audio/x-ms-wma": {
        readonly source: "apache";
        readonly extensions: readonly ["wma"];
    };
    readonly "audio/x-pn-realaudio": {
        readonly source: "apache";
        readonly extensions: readonly ["ram", "ra"];
    };
    readonly "audio/x-pn-realaudio-plugin": {
        readonly source: "apache";
        readonly extensions: readonly ["rmp"];
    };
    readonly "audio/x-realaudio": {
        readonly source: "nginx";
        readonly extensions: readonly ["ra"];
    };
    readonly "audio/x-wav": {
        readonly source: "apache";
        readonly extensions: readonly ["wav"];
    };
    readonly "audio/x-gsm": {
        readonly source: "apache";
        readonly extensions: readonly ["gsm"];
    };
    readonly "audio/xm": {
        readonly source: "apache";
        readonly extensions: readonly ["xm"];
    };
};

export { audio };
