{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio", "prisma:seed": "ts-node --compiler-options \"{\\\"module\\\":\\\"CommonJS\\\"}\" prisma/seed.ts"}, "prisma": {"seed": "ts-node --compiler-options \"{\\\"module\\\":\\\"CommonJS\\\"}\" prisma/seed.ts"}, "dependencies": {"@auth/core": "^0.39.1", "@auth/prisma-adapter": "^2.9.1", "@hookform/resolvers": "^3.9.1", "@prisma/client": "^6.7.0", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@uploadthing/react": "^7.3.1", "autoprefixer": "^10.4.20", "axios": "^1.9.0", "bcrypt": "^6.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "4.1.0", "embla-carousel-react": "8.5.1", "i18next": "^25.1.2", "input-otp": "1.4.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.454.0", "next": "15.2.4", "next-auth": "^5.0.0-beta.28", "next-themes": "^0.4.6", "node-fetch": "^2.7.0", "prisma": "^6.7.0", "react": "^19", "react-day-picker": "8.10.1", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-i18next": "^15.5.1", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.3", "sonner": "^1.7.1", "svix": "^1.65.0", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uploadthing": "^7.7.2", "vaul": "^0.9.6", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^22.15.17", "@types/react": "^19.1.3", "@types/react-dom": "^19", "postcss": "^8", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "typescript": "^5"}}