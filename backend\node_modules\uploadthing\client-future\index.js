import { Array } from 'effect';
import * as Arr from 'effect/Array';
import * as Micro from 'effect/Micro';
import { createIdentityProxy, resolveMaybeUrlArg, FetchContext, UploadPausedError, UploadAbortedError } from '@uploadthing/shared';
export { UploadAbortedError, UploadPausedError, generateClientDropzoneAccept, generateMimeTypes, generatePermittedFileTypes } from '@uploadthing/shared';
import { requestPresignedUrls, makePendingFile, uploadFile } from '../dist/_internal/client-future.js';
export * from '../dist/_internal/client-future.js';
import { createDeferred } from '../dist/_internal/deferred.js';

var version$1 = "7.7.2";

const version = version$1;
/**
 * Generate a typed uploader for a given FileRouter
 * @public
 * @remarks This API is not covered by semver
 */ const future_genUploader = (initOpts)=>{
    const routeRegistry = createIdentityProxy();
    const controllableUpload = async (slug, options)=>{
        const endpoint = typeof slug === "function" ? slug(routeRegistry) : slug;
        const fetchFn = initOpts?.fetch ?? window.fetch;
        const pExit = await requestPresignedUrls({
            endpoint: String(endpoint),
            files: options.files,
            url: resolveMaybeUrlArg(initOpts?.url),
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            input: options.input,
            headers: options.headers
        }).pipe(Micro.provideService(FetchContext, fetchFn), (effect)=>Micro.runPromiseExit(effect, options.signal && {
                signal: options.signal
            }));
        if (pExit._tag === "Failure") throw Micro.causeSquash(pExit.cause);
        const presigneds = pExit.value;
        const pendingFiles = options.files.map(makePendingFile);
        options.onEvent({
            type: "presigned-received",
            files: pendingFiles
        });
        const uploads = new Map();
        const uploadEffect = (file, presigned)=>uploadFile(presigned.url, {
                file,
                files: pendingFiles,
                input: options.input,
                onEvent: options.onEvent,
                XHRImpl: globalThis.XMLHttpRequest
            }).pipe(Micro.provideService(FetchContext, fetchFn));
        for (const [presigned, file] of Array.zip(presigneds, pendingFiles)){
            file.key = presigned.key;
            file.customId = presigned.customId;
            const deferred = createDeferred();
            uploads.set(file, {
                presigned,
                deferred
            });
            void Micro.runPromiseExit(uploadEffect(file, presigned), {
                signal: deferred.ac.signal
            }).then((result)=>{
                if (result._tag === "Success") {
                    return deferred.resolve(result.value);
                } else if (result.cause._tag === "Interrupt") {
                    throw new UploadPausedError();
                }
                throw Micro.causeSquash(result.cause);
            }).catch((err)=>{
                if (err instanceof UploadPausedError) return;
                deferred.reject(err);
            });
        }
        /**
     * Pause an ongoing upload
     * @param file The file upload you want to pause. Can be omitted to pause all files
     */ const pauseUpload = (file)=>{
            const files = Arr.ensure(file ?? options.files);
            for (const file of files){
                const upload = uploads.get(file);
                if (!upload) return;
                if (upload.deferred.ac.signal.aborted) {
                    // Noop if it's already paused
                    return;
                }
                upload.deferred.ac.abort();
            }
        };
        /**
     * Abort an upload
     * @param file The file upload you want to abort. Can be omitted to abort all files
     */ const abortUpload = (file)=>{
            const files = Arr.ensure(file ?? options.files);
            for (const file of files){
                const upload = uploads.get(file);
                if (!upload) throw "No upload found";
                if (upload.deferred.ac.signal.aborted === false) {
                    // Ensure the upload is paused
                    upload.deferred.ac.abort();
                }
            }
            // Abort the upload
            throw new UploadAbortedError();
        };
        /**
     * Resume a paused upload
     * @param file The file upload you want to resume. Can be omitted to resume all files
     */ const resumeUpload = (file)=>{
            const files = Arr.ensure(file ?? options.files);
            for (const file of files){
                const upload = uploads.get(file);
                if (!upload) throw "No upload found";
                upload.deferred.ac = new AbortController();
                void Micro.runPromiseExit(uploadEffect(file, upload.presigned), {
                    signal: upload.deferred.ac.signal
                }).then((result)=>{
                    if (result._tag === "Success") {
                        return upload.deferred.resolve(result.value);
                    } else if (result.cause._tag === "Interrupt") {
                        throw new UploadPausedError();
                    }
                    throw Micro.causeSquash(result.cause);
                }).catch((err)=>{
                    if (err instanceof UploadPausedError) return;
                    upload.deferred.reject(err);
                });
            }
        };
        /**
     * Wait for an upload to complete
     * @param file The file upload you want to wait for. Can be omitted to wait for all files
     */ const done = async (file)=>{
            const promises = [];
            const files = Arr.ensure(file ?? options.files);
            for (const file of files){
                const upload = uploads.get(file);
                if (!upload) throw "No upload found";
                promises.push(upload.deferred.promise);
            }
            const results = await Promise.all(promises);
            return file ? results[0] : results;
        };
        return {
            pauseUpload,
            abortUpload,
            resumeUpload,
            done
        };
    };
    const uploadFiles = (slug, opts)=>controllableUpload(slug, opts).then((_)=>_.done());
    return {
        uploadFiles,
        createUpload: controllableUpload,
        /**
     * Identity object that can be used instead of raw strings
     * that allows "Go to definition" in your IDE to bring you
     * to the backend definition of a route.
     */ routeRegistry
    };
};

export { future_genUploader, version };
