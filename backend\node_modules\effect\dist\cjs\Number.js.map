{"version": 3, "file": "Number.js", "names": ["equivalence", "_interopRequireWildcard", "require", "_Function", "option", "_Iterable", "order", "predicate", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "isNumber", "exports", "negate", "multiply", "sum", "dual", "self", "that", "sumAll", "collection", "reduce", "subtract", "minuend", "subtrahend", "multiplier", "multiplicand", "multiplyAll", "out", "divide", "dividend", "divisor", "none", "some", "unsafeDivide", "increment", "decrement", "Equivalence", "number", "Order", "lessThan", "lessThanOrEqualTo", "greaterThan", "greaterThanOrEqualTo", "between", "clamp", "min", "max", "sign", "remainder", "selfDecCount", "toString", "split", "length", "divisorDecCount", "decCount", "selfInt", "parseInt", "toFixed", "replace", "divisorInt", "Math", "pow", "nextPow2", "nextPow", "ceil", "log", "parse", "s", "NaN", "Infinity", "trim", "Number", "isNaN", "round", "precision", "factor"], "sources": ["../../src/Number.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAiGA,IAAAA,WAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAH,uBAAA,CAAAC,OAAA;AACA,IAAAG,SAAA,GAAAJ,uBAAA,CAAAC,OAAA;AAEA,IAAAI,KAAA,GAAAL,uBAAA,CAAAC,OAAA;AAEA,IAAAK,SAAA,GAAAN,uBAAA,CAAAC,OAAA;AAA2C,SAAAM,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAR,wBAAAQ,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAxG3C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0GA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqDO,MAAMW,QAAQ,GAAAC,OAAA,CAAAD,QAAA,GAAwCrB,SAAS,CAACqB,QAAQ;AAE/E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCO,MAAME,MAAM,GAAIb,CAAS,IAAac,QAAQ,CAACd,CAAC,EAAE,CAAC,CAAC,CAAC;AAE5D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAAY,OAAA,CAAAC,MAAA,GAAAA,MAAA;AA6CO,MAAME,GAAG,GAAAH,OAAA,CAAAG,GAAA,gBAoBZ,IAAAC,cAAI,EAAC,CAAC,EAAE,CAACC,IAAY,EAAEC,IAAY,KAAaD,IAAI,GAAGC,IAAI,CAAC;AAEhE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiDO,MAAMC,MAAM,GAAIC,UAA4B,IAAahC,SAAS,CAACiC,MAAM,CAACD,UAAU,EAAE,CAAC,EAAEL,GAAG,CAAC;AAEpG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAAH,OAAA,CAAAO,MAAA,GAAAA,MAAA;AAoDO,MAAMG,QAAQ,GAAAV,OAAA,CAAAU,QAAA,gBAqBjB,IAAAN,cAAI,EACN,CAAC,EACD,CAACO,OAAe,EAAEC,UAAkB,KAAaD,OAAO,GAAGC,UAAU,CACtE;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0DO,MAAMV,QAAQ,GAAAF,OAAA,CAAAE,QAAA,gBAqBjB,IAAAE,cAAI,EACN,CAAC,EACD,CAACS,UAAkB,EAAEC,YAAoB,KAAaD,UAAU,GAAGC,YAAY,CAChF;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8CO,MAAMC,WAAW,GAAIP,UAA4B,IAAY;EAClE,IAAIQ,GAAG,GAAG,CAAC;EACX,KAAK,MAAM5B,CAAC,IAAIoB,UAAU,EAAE;IAC1B,IAAIpB,CAAC,KAAK,CAAC,EAAE;MACX,OAAO,CAAC;IACV;IACA4B,GAAG,IAAI5B,CAAC;EACV;EACA,OAAO4B,GAAG;AACZ,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAAhB,OAAA,CAAAe,WAAA,GAAAA,WAAA;AAwDO,MAAME,MAAM,GAAAjB,OAAA,CAAAiB,MAAA,gBAqBf,IAAAb,cAAI,EAAC,CAAC,EAAE,CAACc,QAAgB,EAAEC,OAAe,KAAKA,OAAO,KAAK,CAAC,GAAG5C,MAAM,CAAC6C,IAAI,GAAG7C,MAAM,CAAC8C,IAAI,CAACH,QAAQ,GAAGC,OAAO,CAAC,CAAC;AAEjH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0DO,MAAMG,YAAY,GAAAtB,OAAA,CAAAsB,YAAA,gBAuBrB,IAAAlB,cAAI,EAAC,CAAC,EAAE,CAACc,QAAgB,EAAEC,OAAe,KAAaD,QAAQ,GAAGC,OAAO,CAAC;AAE9E;;;;;;;;;;;;;;;AAeO,MAAMI,SAAS,GAAInC,CAAS,IAAae,GAAG,CAACf,CAAC,EAAE,CAAC,CAAC;AAEzD;;;;;;;;;;;;;;;AAAAY,OAAA,CAAAuB,SAAA,GAAAA,SAAA;AAeO,MAAMC,SAAS,GAAIpC,CAAS,IAAasB,QAAQ,CAACtB,CAAC,EAAE,CAAC,CAAC;AAE9D;;;;;AAAAY,OAAA,CAAAwB,SAAA,GAAAA,SAAA;AAKO,MAAMC,WAAW,GAAAzB,OAAA,CAAAyB,WAAA,GAAoCtD,WAAW,CAACuD,MAAM;AAE9E;;;;;AAKO,MAAMC,KAAK,GAAA3B,OAAA,CAAA2B,KAAA,GAAwBlD,KAAK,CAACiD,MAAM;AAEtD;;;;;;;;;;;;;;;;;;AAkBO,MAAME,QAAQ,GAAA5B,OAAA,CAAA4B,QAAA,gBAuCjBnD,KAAK,CAACmD,QAAQ,CAACD,KAAK,CAAC;AAEzB;;;;;;;;;;;;;;;;;;AAkBO,MAAME,iBAAiB,GAAA7B,OAAA,CAAA6B,iBAAA,gBAuC1BpD,KAAK,CAACoD,iBAAiB,CAACF,KAAK,CAAC;AAElC;;;;;;;;;;;;;;;;;;AAkBO,MAAMG,WAAW,GAAA9B,OAAA,CAAA8B,WAAA,gBAuCpBrD,KAAK,CAACqD,WAAW,CAACH,KAAK,CAAC;AAE5B;;;;;;;;;;;;;;;;;;AAkBO,MAAMI,oBAAoB,GAAA/B,OAAA,CAAA+B,oBAAA,gBAuC7BtD,KAAK,CAACsD,oBAAoB,CAACJ,KAAK,CAAC;AAErC;;;;;;;;;;;;;;;;;;;AAmBO,MAAMK,OAAO,GAAAhC,OAAA,CAAAgC,OAAA,gBA+ChBvD,KAAK,CAACuD,OAAO,CAACL,KAAK,CAAC;AAExB;;;;;;;;;;;;;;;;;;;;;;;;;AAyBO,MAAMM,KAAK,GAAAjC,OAAA,CAAAiC,KAAA,gBA2DdxD,KAAK,CAACwD,KAAK,CAACN,KAAK,CAAC;AAEtB;;;;;;;;;;;;;;AAcO,MAAMO,GAAG,GAAAlC,OAAA,CAAAkC,GAAA,gBA+BZzD,KAAK,CAACyD,GAAG,CAACP,KAAK,CAAC;AAEpB;;;;;;;;;;;;;;AAcO,MAAMQ,GAAG,GAAAnC,OAAA,CAAAmC,GAAA,gBA+BZ1D,KAAK,CAAC0D,GAAG,CAACR,KAAK,CAAC;AAEpB;;;;;;;;;;;;;;;;;AAiBO,MAAMS,IAAI,GAAIhD,CAAS,IAAeuC,KAAK,CAACvC,CAAC,EAAE,CAAC,CAAC;AAExD;;;;;;;;;;;;;;;;;;;;AAAAY,OAAA,CAAAoC,IAAA,GAAAA,IAAA;AAoBO,MAAMC,SAAS,GAAArC,OAAA,CAAAqC,SAAA,gBA2ClB,IAAAjC,cAAI,EAAC,CAAC,EAAE,CAACc,QAAgB,EAAEC,OAAe,KAAY;EACxD;EACA,MAAMmB,YAAY,GAAG,CAACpB,QAAQ,CAACqB,QAAQ,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAEC,MAAM;EACrE,MAAMC,eAAe,GAAG,CAACvB,OAAO,CAACoB,QAAQ,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAEC,MAAM;EACvE,MAAME,QAAQ,GAAGL,YAAY,GAAGI,eAAe,GAAGJ,YAAY,GAAGI,eAAe;EAChF,MAAME,OAAO,GAAGC,QAAQ,CAAC3B,QAAQ,CAAC4B,OAAO,CAACH,QAAQ,CAAC,CAACI,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;EACrE,MAAMC,UAAU,GAAGH,QAAQ,CAAC1B,OAAO,CAAC2B,OAAO,CAACH,QAAQ,CAAC,CAACI,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;EACvE,OAAQH,OAAO,GAAGI,UAAU,GAAIC,IAAI,CAACC,GAAG,CAAC,EAAE,EAAEP,QAAQ,CAAC;AACxD,CAAC,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;;;;;;AAyBO,MAAMQ,QAAQ,GAAI/D,CAAS,IAAY;EAC5C,MAAMgE,OAAO,GAAGH,IAAI,CAACI,IAAI,CAACJ,IAAI,CAACK,GAAG,CAAClE,CAAC,CAAC,GAAG6D,IAAI,CAACK,GAAG,CAAC,CAAC,CAAC,CAAC;EACpD,OAAOL,IAAI,CAACd,GAAG,CAACc,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEE,OAAO,CAAC,EAAE,CAAC,CAAC;AAC1C,CAAC;AAED;;;;;;;;;AAAApD,OAAA,CAAAmD,QAAA,GAAAA,QAAA;AASO,MAAMI,KAAK,GAWbC,CAAC,IAAI;EACR,IAAIA,CAAC,KAAK,KAAK,EAAE;IACf,OAAOjF,MAAM,CAAC8C,IAAI,CAACoC,GAAG,CAAC;EACzB;EACA,IAAID,CAAC,KAAK,UAAU,EAAE;IACpB,OAAOjF,MAAM,CAAC8C,IAAI,CAACqC,QAAQ,CAAC;EAC9B;EACA,IAAIF,CAAC,KAAK,WAAW,EAAE;IACrB,OAAOjF,MAAM,CAAC8C,IAAI,CAAC,CAACqC,QAAQ,CAAC;EAC/B;EACA,IAAIF,CAAC,CAACG,IAAI,EAAE,KAAK,EAAE,EAAE;IACnB,OAAOpF,MAAM,CAAC6C,IAAI;EACpB;EACA,MAAMhC,CAAC,GAAGwE,MAAM,CAACJ,CAAC,CAAC;EACnB,OAAOI,MAAM,CAACC,KAAK,CAACzE,CAAC,CAAC,GAAGb,MAAM,CAAC6C,IAAI,GAAG7C,MAAM,CAAC8C,IAAI,CAACjC,CAAC,CAAC;AACvD,CAAC;AAED;;;;;;;;;;;;;;;;AAAAY,OAAA,CAAAuD,KAAA,GAAAA,KAAA;AAgBO,MAAMO,KAAK,GAAA9D,OAAA,CAAA8D,KAAA,gBAmCd,IAAA1D,cAAI,EAAC,CAAC,EAAE,CAACC,IAAY,EAAE0D,SAAiB,KAAY;EACtD,MAAMC,MAAM,GAAGf,IAAI,CAACC,GAAG,CAAC,EAAE,EAAEa,SAAS,CAAC;EACtC,OAAOd,IAAI,CAACa,KAAK,CAACzD,IAAI,GAAG2D,MAAM,CAAC,GAAGA,MAAM;AAC3C,CAAC,CAAC", "ignoreList": []}