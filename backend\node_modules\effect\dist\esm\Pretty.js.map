{"version": 3, "file": "Pretty.js", "names": ["Arr", "errors_", "util_", "Option", "ParseResult", "AST", "make", "schema", "compile", "ast", "getPrettyAnnotation", "getAnnotation", "PrettyAnnotationId", "getMatcher", "defaultPretty", "match", "onNone", "onSome", "handler", "toString", "a", "String", "stringify", "JSON", "formatUnknown", "Declaration", "go", "path", "annotation", "isSome", "value", "typeParameters", "map", "tp", "Error", "getPrettyMissingAnnotationErrorMessage", "getPrettyNeverErrorMessage", "literal", "TupleType", "hook", "elements", "e", "i", "type", "concat", "rest", "annotatedAST", "input", "output", "length", "isOptional", "push", "isNonEmptyReadonlyArray", "head", "tail", "j", "join", "TypeLiteral", "propertySignaturesTypes", "propertySignatures", "ps", "name", "indexSignatureTypes", "indexSignatures", "is", "<PERSON><PERSON><PERSON><PERSON>", "Object", "prototype", "hasOwnProperty", "call", "formatPropertyKey", "keys", "getKeysForIndexSignature", "parameter", "key", "Union", "types", "index", "findIndex", "getPrettyNoMatchingSchemaErrorMessage", "Suspend", "get", "memoizeThunk", "f", "Refinement", "from", "Transformation", "to", "getCompiler"], "sources": ["../../src/Pretty.ts"], "sourcesContent": [null], "mappings": "AAAA;;;AAGA,OAAO,KAAKA,GAAG,MAAM,YAAY;AACjC,OAAO,KAAKC,OAAO,MAAM,6BAA6B;AACtD,OAAO,KAAKC,KAAK,MAAM,2BAA2B;AAClD,OAAO,KAAKC,MAAM,MAAM,aAAa;AACrC,OAAO,KAAKC,WAAW,MAAM,kBAAkB;AAE/C,OAAO,KAAKC,GAAG,MAAM,gBAAgB;AAkBrC;;;;AAIA,OAAO,MAAMC,IAAI,GAAaC,MAA8B,IAAuBC,OAAO,CAACD,MAAM,CAACE,GAAG,EAAE,EAAE,CAAC;AAE1G,MAAMC,mBAAmB,gBAAGL,GAAG,CAACM,aAAa,CAA6BN,GAAG,CAACO,kBAAkB,CAAC;AAEjG,MAAMC,UAAU,GAAIC,aAA0B,IAAML,GAAY,IAC9DN,MAAM,CAACY,KAAK,CAACL,mBAAmB,CAACD,GAAG,CAAC,EAAE;EACrCO,MAAM,EAAEA,CAAA,KAAMF,aAAa;EAC3BG,MAAM,EAAGC,OAAO,IAAKA,OAAO;CAC7B,CAAC;AAEJ,MAAMC,QAAQ,gBAAGN,UAAU,CAAEO,CAAC,IAAKC,MAAM,CAACD,CAAC,CAAC,CAAC;AAE7C,MAAME,SAAS,gBAAGT,UAAU,CAAEO,CAAC,IAAKG,IAAI,CAACD,SAAS,CAACF,CAAC,CAAC,CAAC;AAEtD,MAAMI,aAAa,gBAAGX,UAAU,CAACX,KAAK,CAACsB,aAAa,CAAC;AAErD;;;AAGA,OAAO,MAAMT,KAAK,GAA2B;EAC3C,aAAa,EAAEU,CAAChB,GAAG,EAAEiB,EAAE,EAAEC,IAAI,KAAI;IAC/B,MAAMC,UAAU,GAAGlB,mBAAmB,CAACD,GAAG,CAAC;IAC3C,IAAIN,MAAM,CAAC0B,MAAM,CAACD,UAAU,CAAC,EAAE;MAC7B,OAAOA,UAAU,CAACE,KAAK,CAAC,GAAGrB,GAAG,CAACsB,cAAc,CAACC,GAAG,CAAEC,EAAE,IAAKP,EAAE,CAACO,EAAE,EAAEN,IAAI,CAAC,CAAC,CAAC;IAC1E;IACA,MAAM,IAAIO,KAAK,CAACjC,OAAO,CAACkC,sCAAsC,CAACR,IAAI,EAAElB,GAAG,CAAC,CAAC;EAC5E,CAAC;EACD,aAAa,eAAEI,UAAU,CAAC,MAAM,SAAS,CAAC;EAC1C,cAAc,eAAEA,UAAU,CAAC,MAAK;IAC9B,MAAM,IAAIqB,KAAK,CAACjC,OAAO,CAACmC,0BAA0B,CAAC;EACrD,CAAC,CAAC;EACF,SAAS,eAAEvB,UAAU,CAAEwB,OAAyB,IAC9C,OAAOA,OAAO,KAAK,QAAQ,GACzB,GAAGhB,MAAM,CAACgB,OAAO,CAAC,GAAG,GACrBd,IAAI,CAACD,SAAS,CAACe,OAAO,CAAC,CAC1B;EACD,eAAe,EAAElB,QAAQ;EACzB,cAAc,EAAEA,QAAQ;EACxB,iBAAiB,EAAEG,SAAS;EAC5B,kBAAkB,EAAEH,QAAQ;EAC5B,gBAAgB,EAAEK,aAAa;EAC/B,YAAY,EAAEA,aAAa;EAC3B,eAAe,EAAEA,aAAa;EAC9B,eAAe,EAAEF,SAAS;EAC1B,eAAe,EAAEH,QAAQ;EACzB,gBAAgB,EAAEA,QAAQ;EAC1B,eAAe,eAAEN,UAAU,CAAEO,CAAC,IAAK,GAAGC,MAAM,CAACD,CAAC,CAAC,GAAG,CAAC;EACnD,OAAO,EAAEE,SAAS;EAClB,WAAW,EAAEgB,CAAC7B,GAAG,EAAEiB,EAAE,EAAEC,IAAI,KAAI;IAC7B,MAAMY,IAAI,GAAG7B,mBAAmB,CAACD,GAAG,CAAC;IACrC,IAAIN,MAAM,CAAC0B,MAAM,CAACU,IAAI,CAAC,EAAE;MACvB,OAAOA,IAAI,CAACT,KAAK,EAAE;IACrB;IACA,MAAMU,QAAQ,GAAG/B,GAAG,CAAC+B,QAAQ,CAACR,GAAG,CAAC,CAACS,CAAC,EAAEC,CAAC,KAAKhB,EAAE,CAACe,CAAC,CAACE,IAAI,EAAEhB,IAAI,CAACiB,MAAM,CAACF,CAAC,CAAC,CAAC,CAAC;IACvE,MAAMG,IAAI,GAAGpC,GAAG,CAACoC,IAAI,CAACb,GAAG,CAAEc,YAAY,IAAKpB,EAAE,CAACoB,YAAY,CAACH,IAAI,EAAEhB,IAAI,CAAC,CAAC;IACxE,OAAQoB,KAA6B,IAAI;MACvC,MAAMC,MAAM,GAAkB,EAAE;MAChC,IAAIN,CAAC,GAAG,CAAC;MACT;MACA;MACA;MACA,OAAOA,CAAC,GAAGF,QAAQ,CAACS,MAAM,EAAEP,CAAC,EAAE,EAAE;QAC/B,IAAIK,KAAK,CAACE,MAAM,GAAGP,CAAC,GAAG,CAAC,EAAE;UACxB,IAAIjC,GAAG,CAAC+B,QAAQ,CAACE,CAAC,CAAC,CAACQ,UAAU,EAAE;YAC9B;UACF;QACF,CAAC,MAAM;UACLF,MAAM,CAACG,IAAI,CAACX,QAAQ,CAACE,CAAC,CAAC,CAACK,KAAK,CAACL,CAAC,CAAC,CAAC,CAAC;QACpC;MACF;MACA;MACA;MACA;MACA,IAAI1C,GAAG,CAACoD,uBAAuB,CAACP,IAAI,CAAC,EAAE;QACrC,MAAM,CAACQ,IAAI,EAAE,GAAGC,IAAI,CAAC,GAAGT,IAAI;QAC5B,OAAOH,CAAC,GAAGK,KAAK,CAACE,MAAM,GAAGK,IAAI,CAACL,MAAM,EAAEP,CAAC,EAAE,EAAE;UAC1CM,MAAM,CAACG,IAAI,CAACE,IAAI,CAACN,KAAK,CAACL,CAAC,CAAC,CAAC,CAAC;QAC7B;QACA;QACA;QACA;QACA,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,IAAI,CAACL,MAAM,EAAEM,CAAC,EAAE,EAAE;UACpCb,CAAC,IAAIa,CAAC;UACNP,MAAM,CAACG,IAAI,CAACG,IAAI,CAACC,CAAC,CAAC,CAACR,KAAK,CAACL,CAAC,CAAC,CAAC,CAAC;QAChC;MACF;MAEA,OAAO,GAAG,GAAGM,MAAM,CAACQ,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG;IACtC,CAAC;EACH,CAAC;EACD,aAAa,EAAEC,CAAChD,GAAG,EAAEiB,EAAE,EAAEC,IAAI,KAAI;IAC/B,MAAMY,IAAI,GAAG7B,mBAAmB,CAACD,GAAG,CAAC;IACrC,IAAIN,MAAM,CAAC0B,MAAM,CAACU,IAAI,CAAC,EAAE;MACvB,OAAOA,IAAI,CAACT,KAAK,EAAE;IACrB;IACA,MAAM4B,uBAAuB,GAAGjD,GAAG,CAACkD,kBAAkB,CAAC3B,GAAG,CAAE4B,EAAE,IAAKlC,EAAE,CAACkC,EAAE,CAACjB,IAAI,EAAEhB,IAAI,CAACiB,MAAM,CAACgB,EAAE,CAACC,IAAI,CAAC,CAAC,CAAC;IACrG,MAAMC,mBAAmB,GAAGrD,GAAG,CAACsD,eAAe,CAAC/B,GAAG,CAAEgC,EAAE,IAAKtC,EAAE,CAACsC,EAAE,CAACrB,IAAI,EAAEhB,IAAI,CAAC,CAAC;IAC9E,MAAMsC,YAAY,GAAQ,EAAE;IAC5B,KAAK,IAAIvB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgB,uBAAuB,CAACT,MAAM,EAAEP,CAAC,EAAE,EAAE;MACvDuB,YAAY,CAACxD,GAAG,CAACkD,kBAAkB,CAACjB,CAAC,CAAC,CAACmB,IAAI,CAAC,GAAG,IAAI;IACrD;IACA,OAAQd,KAA6C,IAAI;MACvD,MAAMC,MAAM,GAAkB,EAAE;MAChC;MACA;MACA;MACA,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgB,uBAAuB,CAACT,MAAM,EAAEP,CAAC,EAAE,EAAE;QACvD,MAAMkB,EAAE,GAAGnD,GAAG,CAACkD,kBAAkB,CAACjB,CAAC,CAAC;QACpC,MAAMmB,IAAI,GAAGD,EAAE,CAACC,IAAI;QACpB,IAAID,EAAE,CAACV,UAAU,IAAI,CAACgB,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACtB,KAAK,EAAEc,IAAI,CAAC,EAAE;UACvE;QACF;QACAb,MAAM,CAACG,IAAI,CACT,GAAGjD,KAAK,CAACoE,iBAAiB,CAACT,IAAI,CAAC,KAAKH,uBAAuB,CAAChB,CAAC,CAAC,CAACK,KAAK,CAACc,IAAI,CAAC,CAAC,EAAE,CAC/E;MACH;MACA;MACA;MACA;MACA,IAAIC,mBAAmB,CAACb,MAAM,GAAG,CAAC,EAAE;QAClC,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoB,mBAAmB,CAACb,MAAM,EAAEP,CAAC,EAAE,EAAE;UACnD,MAAMC,IAAI,GAAGmB,mBAAmB,CAACpB,CAAC,CAAC;UACnC,MAAM6B,IAAI,GAAGrE,KAAK,CAACsE,wBAAwB,CAACzB,KAAK,EAAEtC,GAAG,CAACsD,eAAe,CAACrB,CAAC,CAAC,CAAC+B,SAAS,CAAC;UACpF,KAAK,MAAMC,GAAG,IAAIH,IAAI,EAAE;YACtB,IAAIL,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,YAAY,EAAES,GAAG,CAAC,EAAE;cAC3D;YACF;YACA1B,MAAM,CAACG,IAAI,CAAC,GAAGjD,KAAK,CAACoE,iBAAiB,CAACI,GAAG,CAAC,KAAK/B,IAAI,CAACI,KAAK,CAAC2B,GAAG,CAAC,CAAC,EAAE,CAAC;UACrE;QACF;MACF;MAEA,OAAO1E,GAAG,CAACoD,uBAAuB,CAACJ,MAAM,CAAC,GAAG,IAAI,GAAGA,MAAM,CAACQ,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,IAAI;IACrF,CAAC;EACH,CAAC;EACD,OAAO,EAAEmB,CAAClE,GAAG,EAAEiB,EAAE,EAAEC,IAAI,KAAI;IACzB,MAAMY,IAAI,GAAG7B,mBAAmB,CAACD,GAAG,CAAC;IACrC,IAAIN,MAAM,CAAC0B,MAAM,CAACU,IAAI,CAAC,EAAE;MACvB,OAAOA,IAAI,CAACT,KAAK,EAAE;IACrB;IACA,MAAM8C,KAAK,GAAGnE,GAAG,CAACmE,KAAK,CAAC5C,GAAG,CAAEvB,GAAG,IAAK,CAACL,WAAW,CAAC4D,EAAE,CAAC;MAAEvD;IAAG,CAAS,CAAC,EAAEiB,EAAE,CAACjB,GAAG,EAAEkB,IAAI,CAAC,CAAU,CAAC;IAC9F,OAAQP,CAAC,IAAI;MACX,MAAMyD,KAAK,GAAGD,KAAK,CAACE,SAAS,CAAC,CAAC,CAACd,EAAE,CAAC,KAAKA,EAAE,CAAC5C,CAAC,CAAC,CAAC;MAC9C,IAAIyD,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB,MAAM,IAAI3C,KAAK,CAACjC,OAAO,CAAC8E,qCAAqC,CAAC3D,CAAC,EAAEO,IAAI,EAAElB,GAAG,CAAC,CAAC;MAC9E;MACA,OAAOmE,KAAK,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACzD,CAAC,CAAC;IAC3B,CAAC;EACH,CAAC;EACD,SAAS,EAAE4D,CAACvE,GAAG,EAAEiB,EAAE,EAAEC,IAAI,KAAI;IAC3B,OAAOxB,MAAM,CAACY,KAAK,CAACL,mBAAmB,CAACD,GAAG,CAAC,EAAE;MAC5CO,MAAM,EAAEA,CAAA,KAAK;QACX,MAAMiE,GAAG,GAAG/E,KAAK,CAACgF,YAAY,CAAC,MAAMxD,EAAE,CAACjB,GAAG,CAAC0E,CAAC,EAAE,EAAExD,IAAI,CAAC,CAAC;QACvD,OAAQP,CAAC,IAAK6D,GAAG,EAAE,CAAC7D,CAAC,CAAC;MACxB,CAAC;MACDH,MAAM,EAAGC,OAAO,IAAKA,OAAO;KAC7B,CAAC;EACJ,CAAC;EACD,YAAY,EAAEkE,CAAC3E,GAAG,EAAEiB,EAAE,EAAEC,IAAI,KAAI;IAC9B,OAAOxB,MAAM,CAACY,KAAK,CAACL,mBAAmB,CAACD,GAAG,CAAC,EAAE;MAC5CO,MAAM,EAAEA,CAAA,KAAMU,EAAE,CAACjB,GAAG,CAAC4E,IAAI,EAAE1D,IAAI,CAAC;MAChCV,MAAM,EAAGC,OAAO,IAAKA,OAAO;KAC7B,CAAC;EACJ,CAAC;EACD,gBAAgB,EAAEoE,CAAC7E,GAAG,EAAEiB,EAAE,EAAEC,IAAI,KAAI;IAClC,OAAOxB,MAAM,CAACY,KAAK,CAACL,mBAAmB,CAACD,GAAG,CAAC,EAAE;MAC5CO,MAAM,EAAEA,CAAA,KAAMU,EAAE,CAACjB,GAAG,CAAC8E,EAAE,EAAE5D,IAAI,CAAC;MAC9BV,MAAM,EAAGC,OAAO,IAAKA,OAAO;KAC7B,CAAC;EACJ;CACD;AAED,MAAMV,OAAO,gBAAGH,GAAG,CAACmF,WAAW,CAACzE,KAAK,CAAC", "ignoreList": []}