{"version": 3, "file": "workerRunner.js", "names": ["Cause", "_interopRequireWildcard", "require", "Chunk", "Context", "Deferred", "Effect", "Either", "Fiber", "FiberId", "_Function", "Layer", "<PERSON><PERSON><PERSON>", "Stream", "Transferable", "_WorkerError", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "PlatformRunnerTypeId", "exports", "Symbol", "for", "PlatformRunner", "GenericTag", "CloseLatch", "Reference", "defaultValue", "unsafeMake", "none", "layerCloseLatch", "effect", "make", "fnUntraced", "process", "options", "fiber", "withFiberRuntime", "succeed", "platform", "closeLatch", "backing", "start", "fiberMap", "Map", "await", "pipe", "onExit", "currentScheduler", "scheduleTask", "unsafeInterruptAsFork", "id", "void", "forkScoped", "run", "portId", "kind", "data", "span", "interrupt", "decode", "flatMap", "input", "collector", "unsafeMakeCollector", "stream", "isEffect", "out", "encodeOutput", "provideService", "Collector", "payload", "send", "unsafeRead", "runForEachChunk", "chunk", "undefined", "toReadonlyArray", "unsafeClear", "for<PERSON>ach", "and<PERSON><PERSON>", "withParentSpan", "_tag", "traceId", "spanId", "sampled", "context", "empty", "uninterruptibleMask", "restore", "catchIf", "isWorkerError", "error", "WorkerError", "encodeCause", "fail", "catchAllCause", "cause", "match", "failureOrCause", "onLeft", "encodeError", "onRight", "ensuring", "sync", "delete", "layer", "scopedDiscard", "provide", "makeSerialized", "schema", "handlers", "gen", "scope", "parseRequest", "decodeUnknown", "request", "result", "<PERSON><PERSON><PERSON><PERSON>", "buildWithScope", "_", "merge", "provideContext", "message", "mapError", "reason", "serializeFailure", "serializeSuccess", "layerSerialized", "launch", "scopedWith", "provideMerge"], "sources": ["../../../src/internal/workerRunner.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,KAAA,GAAAF,uBAAA,CAAAC,OAAA;AACA,IAAAE,OAAA,GAAAH,uBAAA,CAAAC,OAAA;AACA,IAAAG,QAAA,GAAAJ,uBAAA,CAAAC,OAAA;AACA,IAAAI,MAAA,GAAAL,uBAAA,CAAAC,OAAA;AACA,IAAAK,MAAA,GAAAN,uBAAA,CAAAC,OAAA;AACA,IAAAM,KAAA,GAAAP,uBAAA,CAAAC,OAAA;AACA,IAAAO,OAAA,GAAAR,uBAAA,CAAAC,OAAA;AACA,IAAAQ,SAAA,GAAAR,OAAA;AACA,IAAAS,KAAA,GAAAV,uBAAA,CAAAC,OAAA;AACA,IAAAU,MAAA,GAAAX,uBAAA,CAAAC,OAAA;AAEA,IAAAW,MAAA,GAAAZ,uBAAA,CAAAC,OAAA;AACA,IAAAY,YAAA,GAAAb,uBAAA,CAAAC,OAAA;AAEA,IAAAa,YAAA,GAAAb,OAAA;AAA8D,SAAAc,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAhB,wBAAAgB,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAG9D;AACO,MAAMW,oBAAoB,GAAAC,OAAA,CAAAD,oBAAA,gBAAsCE,MAAM,CAACC,GAAG,CAC/E,wCAAwC,CACJ;AAEtC;AACO,MAAMC,cAAc,GAAAH,OAAA,CAAAG,cAAA,gBAAGpC,OAAO,CAACqC,UAAU,CAC9C,wCAAwC,CACzC;AAED;AACO,MAAMC,UAAU,GAAAL,OAAA,CAAAK,UAAA,gBAAGtC,OAAO,CAACuC,SAAS,EAA2B,CAAC,0CAA0C,EAAE;EACjHC,YAAY,EAAEA,CAAA,KAAMvC,QAAQ,CAACwC,UAAU,CAAoBpC,OAAO,CAACqC,IAAI;CACxE,CAAC;AAEF;AACO,MAAMC,eAAe,GAAAV,OAAA,CAAAU,eAAA,gBAAGpC,KAAK,CAACqC,MAAM,CAACN,UAAU,eAAErC,QAAQ,CAAC4C,IAAI,EAAE,CAAC;AAExE;AACO,MAAMA,IAAI,GAAAZ,OAAA,CAAAY,IAAA,gBAAG3C,MAAM,CAAC4C,UAAU,CAAC,WACpCC,OAAwE,EACxEC,OAA8C;EAE9C,MAAMC,KAAK,GAAG,OAAO/C,MAAM,CAACgD,gBAAgB,CAA2BhD,MAAM,CAACiD,OAAc,CAAC;EAC7F,MAAMC,QAAQ,GAAG,OAAOhB,cAAc;EACtC,MAAMiB,UAAU,GAAG,OAAOf,UAAU;EACpC,MAAMgB,OAAO,GAAG,OAAOF,QAAQ,CAACG,KAAK,CAAsDF,UAAU,CAAC;EACtG,MAAMG,QAAQ,GAAG,IAAIC,GAAG,EAAyC;EAEjE,OAAOxD,QAAQ,CAACyD,KAAK,CAACL,UAAU,CAAC,CAACM,IAAI,CACpCzD,MAAM,CAAC0D,MAAM,CAAC,MAAK;IACjBX,KAAK,CAACY,gBAAgB,CAACC,YAAY,CAAC,MAAK;MACvCb,KAAK,CAACc,qBAAqB,CAACd,KAAK,CAACe,EAAE,EAAE,CAAC;IACzC,CAAC,EAAE,CAAC,CAAC;IACL,OAAO9D,MAAM,CAAC+D,IAAI;EACpB,CAAC,CAAC,EACF/D,MAAM,CAACgE,UAAU,CAClB;EAED,OAAOZ,OAAO,CAACa,GAAG,CAAC,CAACC,MAAM,EAAE,CAACJ,EAAE,EAAEK,IAAI,EAAEC,IAAI,EAAEC,IAAI,CAAC,KAAyC;IACzF,IAAIF,IAAI,KAAK,CAAC,EAAE;MACd,MAAMpB,KAAK,GAAGO,QAAQ,CAACpC,GAAG,CAAC4C,EAAE,CAAC;MAC9B,IAAI,CAACf,KAAK,EAAE,OAAO/C,MAAM,CAAC+D,IAAI;MAC9B,OAAO7D,KAAK,CAACoE,SAAS,CAACvB,KAAK,CAAC;IAC/B;IAEA,OAAO/C,MAAM,CAACgD,gBAAgB,CAAkBD,KAAK,IAAI;MACvDO,QAAQ,CAACzB,GAAG,CAACiC,EAAE,EAAEf,KAAK,CAAC;MACvB,OAAOD,OAAO,EAAEyB,MAAM,GAAGzB,OAAO,CAACyB,MAAM,CAACH,IAAI,CAAC,GAAGpE,MAAM,CAACiD,OAAO,CAACmB,IAAI,CAAC;IACtE,CAAC,CAAC,CAACX,IAAI,CACLzD,MAAM,CAACwE,OAAO,CAAEC,KAAK,IAAI;MACvB,MAAMC,SAAS,GAAGlE,YAAY,CAACmE,mBAAmB,EAAE;MACpD,MAAMC,MAAM,GAAG/B,OAAO,CAAC4B,KAAK,CAAC;MAC7B,IAAI/B,MAAM,GAAG1C,MAAM,CAAC6E,QAAQ,CAACD,MAAM,CAAC,GAClC5E,MAAM,CAACwE,OAAO,CAACI,MAAM,EAAGE,GAAG,IACzB,IAAArB,cAAI,EACFX,OAAO,EAAEiC,YAAY,GACjB/E,MAAM,CAACgF,cAAc,CAAClC,OAAO,CAACiC,YAAY,CAACN,KAAK,EAAEK,GAAG,CAAC,EAAEtE,YAAY,CAACyE,SAAS,EAAEP,SAAS,CAAC,GAC1F1E,MAAM,CAACiD,OAAO,CAAC6B,GAAG,CAAC,EACvB9E,MAAM,CAACwE,OAAO,CAAEU,OAAO,IAAK9B,OAAO,CAAC+B,IAAI,CAACjB,MAAM,EAAE,CAACJ,EAAE,EAAE,CAAC,EAAE,CAACoB,OAAO,CAAC,CAAC,EAAER,SAAS,CAACU,UAAU,EAAE,CAAC,CAAC,CAC9F,CAAC,GACJ,IAAA3B,cAAI,EACFmB,MAAM,EACNrE,MAAM,CAAC8E,eAAe,CAAEC,KAAK,IAAI;QAC/B,IAAIxC,OAAO,EAAEiC,YAAY,KAAKQ,SAAS,EAAE;UACvC,MAAML,OAAO,GAAGrF,KAAK,CAAC2F,eAAe,CAACF,KAAK,CAAC;UAC5C,OAAOlC,OAAO,CAAC+B,IAAI,CAACjB,MAAM,EAAE,CAACJ,EAAE,EAAE,CAAC,EAAEoB,OAAO,CAAC,CAAC;QAC/C;QAEAR,SAAS,CAACe,WAAW,EAAE;QACvB,OAAO,IAAAhC,cAAI,EACTzD,MAAM,CAAC0F,OAAO,CAACJ,KAAK,EAAGlB,IAAI,IAAKtB,OAAO,CAACiC,YAAa,CAACN,KAAK,EAAEL,IAAI,CAAC,CAAC,EACnEpE,MAAM,CAACgF,cAAc,CAACxE,YAAY,CAACyE,SAAS,EAAEP,SAAS,CAAC,EACxD1E,MAAM,CAACwE,OAAO,CAAEU,OAAO,IAAK9B,OAAO,CAAC+B,IAAI,CAACjB,MAAM,EAAE,CAACJ,EAAE,EAAE,CAAC,EAAEoB,OAAO,CAAC,EAAER,SAAS,CAACU,UAAU,EAAE,CAAC,CAAC,CAC5F;MACH,CAAC,CAAC,EACFpF,MAAM,CAAC2F,OAAO,CAACvC,OAAO,CAAC+B,IAAI,CAACjB,MAAM,EAAE,CAACJ,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAC9C;MAEH,IAAIO,IAAI,EAAE;QACR3B,MAAM,GAAG1C,MAAM,CAAC4F,cAAc,CAAClD,MAAM,EAAE;UACrCmD,IAAI,EAAE,cAAc;UACpBC,OAAO,EAAEzB,IAAI,CAAC,CAAC,CAAC;UAChB0B,MAAM,EAAE1B,IAAI,CAAC,CAAC,CAAC;UACf2B,OAAO,EAAE3B,IAAI,CAAC,CAAC,CAAC;UAChB4B,OAAO,EAAEnG,OAAO,CAACoG,KAAK;SACvB,CAAC;MACJ;MAEA,OAAOlG,MAAM,CAACmG,mBAAmB,CAAEC,OAAO,IACxCA,OAAO,CAAC1D,MAAM,CAAC,CAACe,IAAI,CAClBzD,MAAM,CAACqG,OAAO,CACZC,0BAAa,EACZC,KAAK,IAAKnD,OAAO,CAAC+B,IAAI,CAACjB,MAAM,EAAE,CAACJ,EAAE,EAAE,CAAC,EAAE0C,wBAAW,CAACC,WAAW,CAAC/G,KAAK,CAACgH,IAAI,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,CACrF,EACDvG,MAAM,CAAC2G,aAAa,CAAEC,KAAK,IACzB3G,MAAM,CAAC4G,KAAK,CAACnH,KAAK,CAACoH,cAAc,CAACF,KAAK,CAAC,EAAE;QACxCG,MAAM,EAAGR,KAAK,IAAI;UAChB7B,SAAS,CAACe,WAAW,EAAE;UACvB,OAAO,IAAAhC,cAAI,EACTX,OAAO,EAAEkE,WAAW,GAChBhH,MAAM,CAACgF,cAAc,CACrBlC,OAAO,CAACkE,WAAW,CAACvC,KAAK,EAAE8B,KAAK,CAAC,EACjC/F,YAAY,CAACyE,SAAS,EACtBP,SAAS,CACV,GACC1E,MAAM,CAACiD,OAAO,CAACsD,KAAK,CAAC,EACzBvG,MAAM,CAACwE,OAAO,CAAEU,OAAO,IAAK9B,OAAO,CAAC+B,IAAI,CAACjB,MAAM,EAAE,CAACJ,EAAE,EAAE,CAAC,EAAEoB,OAAc,CAAC,EAAER,SAAS,CAACU,UAAU,EAAE,CAAC,CAAC,EAClGpF,MAAM,CAAC2G,aAAa,CAAEC,KAAK,IAAKxD,OAAO,CAAC+B,IAAI,CAACjB,MAAM,EAAE,CAACJ,EAAE,EAAE,CAAC,EAAE0C,wBAAW,CAACC,WAAW,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC,CAC/F;QACH,CAAC;QACDK,OAAO,EAAGL,KAAK,IAAKxD,OAAO,CAAC+B,IAAI,CAACjB,MAAM,EAAE,CAACJ,EAAE,EAAE,CAAC,EAAE0C,wBAAW,CAACC,WAAW,CAACG,KAAK,CAAC,CAAC;OACjF,CAAC,CACH,CACF,CACF;IACH,CAAC,CAAC,EACF5G,MAAM,CAACkH,QAAQ,CAAClH,MAAM,CAACmH,IAAI,CAAC,MAAM7D,QAAQ,CAAC8D,MAAM,CAACtD,EAAE,CAAC,CAAC,CAAC,CACxD;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF;AACO,MAAMuD,KAAK,GAAGA,CACnBxE,OAAwE,EACxEC,OAA8C,KAE9CzC,KAAK,CAACiH,aAAa,CAAC3E,IAAI,CAACE,OAAO,EAAEC,OAAO,CAAC,CAAC,CAACW,IAAI,CAC9CpD,KAAK,CAACkH,OAAO,CAAC9E,eAAe,CAAC,CAC/B;AAEH;AAAAV,OAAA,CAAAsF,KAAA,GAAAA,KAAA;AACO,MAAMG,cAAc,GAAGA,CAM5BC,MAA8B,EAC9BC,QAAkB,KASlB1H,MAAM,CAAC2H,GAAG,CAAC,aAAS;EAClB,MAAMC,KAAK,GAAG,OAAO5H,MAAM,CAAC4H,KAAK;EACjC,IAAI3B,OAAO,GAAGnG,OAAO,CAACoG,KAAK,EAA0B;EACrD,MAAM2B,YAAY,GAAGvH,MAAM,CAACwH,aAAa,CAACL,MAAM,CAAqC;EAErF,OAAO,OAAO9E,IAAI,CAAEoF,OAAU,IAAI;IAChC,MAAMC,MAAM,GAAIN,QAAgB,CAACK,OAAO,CAAClC,IAAI,CAAC,CAACkC,OAAO,CAAC;IACvD,IAAI1H,KAAK,CAAC4H,OAAO,CAACD,MAAM,CAAC,EAAE;MACzB,OAAOhI,MAAM,CAACwE,OAAO,CAACnE,KAAK,CAAC6H,cAAc,CAACF,MAAM,EAAEJ,KAAK,CAAC,EAAGO,CAAC,IAC3DnI,MAAM,CAACmH,IAAI,CAAC,MAAK;QACflB,OAAO,GAAGnG,OAAO,CAACsI,KAAK,CAACnC,OAAO,EAAEkC,CAAC,CAAC;MACrC,CAAC,CAAC,CAAC;IACP,CAAC,MAAM,IAAInI,MAAM,CAAC6E,QAAQ,CAACmD,MAAM,CAAC,EAAE;MAClC,OAAOhI,MAAM,CAACuH,OAAO,CAACS,MAAM,EAAE/B,OAAO,CAAC;IACxC;IACA,OAAO1F,MAAM,CAAC8H,cAAc,CAACL,MAAa,EAAE/B,OAAO,CAAC;EACtD,CAAC,EAAE;IACD1B,MAAMA,CAAC+D,OAAO;MACZ,OAAOtI,MAAM,CAACuI,QAAQ,CACpBV,YAAY,CAACS,OAAO,CAAC,EACpB1B,KAAK,IAAK,IAAIJ,wBAAW,CAAC;QAAEgC,MAAM,EAAE,QAAQ;QAAE5B;MAAK,CAAE,CAAC,CACxD;IACH,CAAC;IACDI,WAAWA,CAACe,OAAO,EAAEO,OAAO;MAC1B,OAAOtI,MAAM,CAACuI,QAAQ,CACpBjI,MAAM,CAACmI,gBAAgB,CAACV,OAAc,EAAEO,OAAO,CAAC,EAC/C1B,KAAK,IAAK,IAAIJ,wBAAW,CAAC;QAAEgC,MAAM,EAAE,QAAQ;QAAE5B;MAAK,CAAE,CAAC,CACxD;IACH,CAAC;IACD7B,YAAYA,CAACgD,OAAO,EAAEO,OAAO;MAC3B,OAAOtI,MAAM,CAAC2G,aAAa,CACzBrG,MAAM,CAACoI,gBAAgB,CAACX,OAAc,EAAEO,OAAO,CAAC,EAC/C1B,KAAK,IAAK,IAAIJ,wBAAW,CAAC;QAAEgC,MAAM,EAAE,QAAQ;QAAE5B;MAAK,CAAE,CAAC,CACxD;IACH;GACD,CAAC;AACJ,CAAC,CAAQ;AAEX;AAAA7E,OAAA,CAAAyF,cAAA,GAAAA,cAAA;AACO,MAAMmB,eAAe,GAAGA,CAM7BlB,MAA8B,EAC9BC,QAAkB,KAOfrH,KAAK,CAACiH,aAAa,CAACE,cAAc,CAACC,MAAM,EAAEC,QAAQ,CAAC,CAAC,CAACjE,IAAI,CAACpD,KAAK,CAACkH,OAAO,CAAC9E,eAAe,CAAC,CAAC;AAE/F;AAAAV,OAAA,CAAA4G,eAAA,GAAAA,eAAA;AACO,MAAMC,MAAM,GAAavB,KAA2B,IACzDrH,MAAM,CAAC6I,UAAU,CAAC7I,MAAM,CAAC4C,UAAU,CAAC,WAAUgF,KAAK;EACjD,MAAM3B,OAAO,GAAG,OAAO5F,KAAK,CAAC6H,cAAc,CAAC7H,KAAK,CAACyI,YAAY,CAACzB,KAAK,EAAE5E,eAAe,CAAC,EAAEmF,KAAK,CAAC;EAC9F,MAAMzE,UAAU,GAAGrD,OAAO,CAACoB,GAAG,CAAC+E,OAAO,EAAE7D,UAAU,CAAC;EACnD,OAAO,OAAOrC,QAAQ,CAACyD,KAAK,CAACL,UAAU,CAAC;AAC1C,CAAC,CAAC,CAAC;AAAApB,OAAA,CAAA6G,MAAA,GAAAA,MAAA", "ignoreList": []}