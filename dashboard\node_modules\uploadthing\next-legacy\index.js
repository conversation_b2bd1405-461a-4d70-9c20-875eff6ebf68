import * as Effect from 'effect/Effect';
import { makeAdapterHandler } from '../dist/_internal/handler.js';
import { toWebRequest } from '../dist/_internal/to-web-request.js';
import { createBuilder } from '../dist/_internal/upload-builder.js';
export { UTFiles, UTRegion as experimental_UTRegion } from '../dist/_internal/types.js';

const createUploadthing = (opts)=>createBuilder(opts);
const createRouteHandler = (opts)=>{
    const handler = makeAdapterHandler((req, res)=>Effect.succeed({
            req,
            res
        }), (req)=>toWebRequest(req), opts, "nextjs-pages");
    return async (req, res)=>{
        const response = await handler(req, res);
        res.status(response.status);
        for (const [name, value] of response.headers){
            res.setHeader(name, value);
        }
        // FIXME: Should be able to just forward it instead of consuming it first
        return res.json(await response.json());
    };
};

export { createRouteHandler, createUploadthing };
