{"version": 3, "file": "RuntimeFlags.js", "names": ["circular", "internal", "None", "Interruption", "OpSupervision", "RuntimeMetrics", "WindDown", "CooperativeYielding", "cooperativeYielding", "diff", "differ", "disable", "disableAll", "disableCooperativeYielding", "disableInterruption", "disableOpSupervision", "disableRuntimeMetrics", "disableWindDown", "enable", "enableAll", "enableCooperativeYielding", "enableInterruption", "enableOpSupervision", "enableRuntimeMetrics", "enableWindDown", "interruptible", "interruption", "isEnabled", "isDisabled", "make", "none", "opSupervision", "patch", "render", "runtimeMetrics", "toSet", "windDown"], "sources": ["../../src/RuntimeFlags.ts"], "sourcesContent": [null], "mappings": "AAAA;;;AAKA,OAAO,KAAKA,QAAQ,MAAM,8BAA8B;AACxD,OAAO,KAAKC,QAAQ,MAAM,4BAA4B;AA2BtD;;;;;;AAMA,OAAO,MAAMC,IAAI,GAAgBD,QAAQ,CAACC,IAAI;AAE9C;;;;;;;AAOA,OAAO,MAAMC,YAAY,GAAgBF,QAAQ,CAACE,YAAY;AAE9D;;;;;;;;;AASA,OAAO,MAAMC,aAAa,GAAgBH,QAAQ,CAACG,aAAa;AAEhE;;;;;;;;;;AAUA,OAAO,MAAMC,cAAc,GAAgBJ,QAAQ,CAACI,cAAc;AAElE;;;;;;;;;AASA,OAAO,MAAMC,QAAQ,GAAgBL,QAAQ,CAACK,QAAQ;AAEtD;;;;;;;AAOA,OAAO,MAAMC,mBAAmB,GAAgBN,QAAQ,CAACM,mBAAmB;AAE5E;;;;;;;AAOA,OAAO,MAAMC,mBAAmB,GAAoCP,QAAQ,CAACO,mBAAmB;AAEhG;;;;;;;AAOA,OAAO,MAAMC,IAAI,GAiBbR,QAAQ,CAACQ,IAAI;AAEjB;;;;;;AAMA,OAAO,MAAMC,MAAM,GAAqET,QAAQ,CAACS,MAAM;AAEvG;;;;;;AAMA,OAAO,MAAMC,OAAO,GAehBV,QAAQ,CAACU,OAAO;AAEpB;;;;;;AAMA,OAAO,MAAMC,UAAU,GAenBX,QAAQ,CAACW,UAAU;AAEvB;;;;AAIA,OAAO,MAAMC,0BAA0B,GAAuBb,QAAQ,CAACa,0BAA0B;AAEjG;;;;AAIA,OAAO,MAAMC,mBAAmB,GAAuBd,QAAQ,CAACc,mBAAmB;AAEnF;;;;AAIA,OAAO,MAAMC,oBAAoB,GAAuBf,QAAQ,CAACe,oBAAoB;AAErF;;;;AAIA,OAAO,MAAMC,qBAAqB,GAAuBhB,QAAQ,CAACgB,qBAAqB;AAEvF;;;;AAIA,OAAO,MAAMC,eAAe,GAAuBjB,QAAQ,CAACiB,eAAe;AAE3E;;;;;;AAMA,OAAO,MAAMC,MAAM,GAefjB,QAAQ,CAACiB,MAAM;AAEnB;;;;;;AAMA,OAAO,MAAMC,SAAS,GAelBlB,QAAQ,CAACkB,SAAS;AAEtB;;;;AAIA,OAAO,MAAMC,yBAAyB,GAAuBpB,QAAQ,CAACoB,yBAAyB;AAE/F;;;;AAIA,OAAO,MAAMC,kBAAkB,GAAuBrB,QAAQ,CAACqB,kBAAkB;AAEjF;;;;AAIA,OAAO,MAAMC,mBAAmB,GAAuBtB,QAAQ,CAACsB,mBAAmB;AAEnF;;;;AAIA,OAAO,MAAMC,oBAAoB,GAAuBvB,QAAQ,CAACuB,oBAAoB;AAErF;;;;AAIA,OAAO,MAAMC,cAAc,GAAuBxB,QAAQ,CAACwB,cAAc;AAEzE;;;;;;;;;;;AAWA,OAAO,MAAMC,aAAa,GAAoCxB,QAAQ,CAACwB,aAAa;AAEpF;;;;;;;AAOA,OAAO,MAAMC,YAAY,GAAoCzB,QAAQ,CAACyB,YAAY;AAElF;;;;;;AAMA,OAAO,MAAMC,SAAS,GAelB1B,QAAQ,CAAC0B,SAAS;AAEtB;;;;;;AAMA,OAAO,MAAMC,UAAU,GAenB3B,QAAQ,CAAC2B,UAAU;AAEvB;;;;AAIA,OAAO,MAAMC,IAAI,GAA2D5B,QAAQ,CAAC4B,IAAI;AAEzF;;;;AAIA,OAAO,MAAMC,IAAI,GAAiB7B,QAAQ,CAAC6B,IAAI;AAE/C;;;;;;;AAOA,OAAO,MAAMC,aAAa,GAAoC9B,QAAQ,CAAC8B,aAAa;AAEpF;;;;;;;AAOA,OAAO,MAAMC,KAAK,GAiBd/B,QAAQ,CAAC+B,KAAK;AAElB;;;;;;AAMA,OAAO,MAAMC,MAAM,GAAmChC,QAAQ,CAACgC,MAAM;AAErE;;;;;;;AAOA,OAAO,MAAMC,cAAc,GAAoCjC,QAAQ,CAACiC,cAAc;AAEtF;;;;;;AAMA,OAAO,MAAMC,KAAK,GAAqDlC,QAAQ,CAACkC,KAAK;AAErF;;;;;;;AAOA,OAAO,MAAMC,QAAQ,GAAoCnC,QAAQ,CAACmC,QAAQ", "ignoreList": []}