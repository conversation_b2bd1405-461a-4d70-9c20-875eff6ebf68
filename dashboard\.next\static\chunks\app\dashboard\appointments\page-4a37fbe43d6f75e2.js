(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8876],{28965:(e,s,t)=>{Promise.resolve().then(t.bind(t,45810))},45810:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>P});var a=t(95155),l=t(97168),r=t(89852),n=t(88482),i=t(88145),d=t(69074),c=t(53904),o=t(49103),m=t(14186),x=t(43453),u=t(17580),h=t(85339),p=t(47924),f=t(12115),g=t(69663),j=t(92657),N=t(71007),v=t(40646),w=t(4516),b=t(5623),y=t(54861),C=t(13717),A=t(62525),S=t(67133),D=t(82714),E=t(99474),k=t(95784),L=t(99840),R=t(14503),F=t(57434),O=t(19420),_=t(28883),I=t(51154),T=t(4229),q=t(31886);function Z(e){let{open:s,onOpenChange:t,appointment:o,onSuccess:x}=e,[h,b]=(0,f.useState)(!1),[y,C]=(0,f.useState)([]),[A,S]=(0,f.useState)([]),[Z,U]=(0,f.useState)(!1),[P,z]=(0,f.useState)(""),[H,W]=(0,f.useState)(!1),[M,V]=(0,f.useState)({title:"",description:"",scheduledAt:"",location:"",type:"meeting",clientId:""}),{toast:J}=(0,R.dj)(),B=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";try{U(!0);let s=new URLSearchParams({simple:"true",limit:"50",...e&&{search:e}}),t=await q.A.get("/clients?".concat(s)),a=Array.isArray(t)?t:[];console.log("Fetched clients for appointment dialog:",a.length),e||C(a),S(a)}catch(e){console.error("Error fetching clients:",e),C([]),S([])}finally{U(!1)}},$=e=>{if(z(e),!e.trim()){S(y);return}let s=y.filter(s=>s.name.toLowerCase().includes(e.toLowerCase())||s.phone.includes(e)||s.email&&s.email.toLowerCase().includes(e.toLowerCase()));S(s),0===s.length&&e.length>=3&&B(e)};(0,f.useEffect)(()=>{if(s){if(B(),o){let e=new Date(o.scheduledAt).toISOString().slice(0,16);V({title:o.title,description:o.description||"",scheduledAt:e,location:o.location||"",type:o.type,clientId:o.clientId})}else{let e=new Date;e.setDate(e.getDate()+1),e.setHours(9,0,0,0),V({title:"",description:"",scheduledAt:e.toISOString().slice(0,16),location:"",type:"meeting",clientId:""})}}},[s,o]);let G=async e=>{e.preventDefault();let s=[];if((M.title.trim()||s.push("Title is required • العنوان مطلوب"),M.clientId||s.push("Client selection is required • اختيار العميل مطلوب"),M.scheduledAt)?new Date(M.scheduledAt)<new Date&&s.push("Cannot schedule appointments in the past • لا يمكن جدولة مواعيد في الماضي"):s.push("Date and time are required • التاريخ والوقت مطلوبان"),s.length>0){J({title:"Validation Error • خطأ في التحقق",description:s.join(" | "),variant:"destructive"});return}try{b(!0);let e={title:M.title.trim(),description:M.description.trim()||void 0,scheduledAt:new Date(M.scheduledAt).toISOString(),location:M.location.trim()||void 0,type:M.type,clientId:M.clientId};console.log("Submitting appointment:",e),o?(await q.A.put("/appointments/".concat(o.id),e),J({title:"✅ Success • نجح",description:"Appointment updated successfully • تم تحديث الموعد بنجاح"})):(await q.A.post("/appointments",e),J({title:"✅ Success • نجح",description:"Appointment created successfully • تم إنشاء الموعد بنجاح"})),x(),t(!1)}catch(e){var a,l,r;console.error("Error saving appointment:",e),J({title:"❌ Error • خطأ",description:(null===(l=e.response)||void 0===l?void 0:null===(a=l.data)||void 0===a?void 0:a.error)||(null===(r=e.responseData)||void 0===r?void 0:r.error)||e.message||"Failed to save appointment • فشل في حفظ الموعد",variant:"destructive"})}finally{b(!1)}},Q=(e,s)=>{V(t=>({...t,[e]:s}))},K=y.find(e=>e.id===M.clientId);return(0,a.jsx)(L.lG,{open:s,onOpenChange:t,children:(0,a.jsxs)(L.Cf,{className:"sm:max-w-[700px] max-h-[95vh] overflow-y-auto",children:[(0,a.jsxs)(L.c7,{className:"space-y-3 pb-6 border-b",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"p-2 rounded-lg bg-primary/10",children:(0,a.jsx)(d.A,{className:"h-6 w-6 text-primary"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)(L.L3,{className:"text-xl font-semibold",children:o?"Edit Appointment":"Create Appointment"}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground mt-1",children:[o?"تعديل الموعد":"إنشاء موعد"," • Schedule client meetings efficiently"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-xs text-muted-foreground",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 rounded-full bg-primary"}),(0,a.jsx)("span",{children:"Details"})]}),(0,a.jsx)("div",{className:"w-4 h-px bg-border"}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 rounded-full bg-muted"}),(0,a.jsx)("span",{children:"Client"})]}),(0,a.jsx)("div",{className:"w-4 h-px bg-border"}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 rounded-full bg-muted"}),(0,a.jsx)("span",{children:"Schedule"})]})]})]}),(0,a.jsxs)("form",{onSubmit:G,className:"space-y-6 py-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(D.J,{htmlFor:"title",className:"flex items-center gap-2 text-sm font-medium",children:[(0,a.jsx)(F.A,{className:"h-4 w-4"}),"Title • العنوان *"]}),(0,a.jsx)(r.p,{id:"title",placeholder:"Enter appointment title • أدخل عنوان الموعد",value:M.title,onChange:e=>Q("title",e.target.value),className:"".concat(M.title.trim()?"":"border-red-300 focus:border-red-500"),required:!0,autoFocus:!0}),!M.title.trim()&&(0,a.jsx)("p",{className:"text-xs text-red-600 dark:text-red-400",children:"Title is required • العنوان مطلوب"})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)(D.J,{htmlFor:"client",className:"flex items-center gap-2 text-sm font-medium",children:[(0,a.jsx)(u.A,{className:"h-4 w-4"}),"Client • العميل *",y.length>0&&(0,a.jsxs)(i.E,{variant:"outline",className:"ml-2 text-xs",children:[y.length," clients loaded"]})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(r.p,{placeholder:"Search clients by name, phone, or email • ابحث عن العملاء...",value:P,onChange:e=>$(e.target.value),className:"pl-10"}),Z&&(0,a.jsx)(c.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin text-muted-foreground"})]}),(0,a.jsxs)(k.l6,{value:M.clientId,onValueChange:e=>Q("clientId",e),children:[(0,a.jsx)(k.bq,{className:"h-12 ".concat(M.clientId?"":"border-red-300 focus:border-red-500"),children:(0,a.jsx)(k.yv,{placeholder:Z?"Loading clients... • جاري تحميل العملاء...":"Select a client • اختر عميل"})}),(0,a.jsxs)(k.gC,{className:"max-h-[300px]",children:[0===A.length?(0,a.jsx)(k.eb,{value:"no-clients",disabled:!0,children:P?'No clients found for "'.concat(P,'" • لم يتم العثور على عملاء'):"No clients available • لا يوجد عملاء متاحون"}):A.map(e=>(0,a.jsx)(k.eb,{value:e.id,className:"py-3",children:(0,a.jsxs)("div",{className:"flex items-center gap-3 w-full",children:[(0,a.jsx)(g.eu,{className:"h-8 w-8 flex-shrink-0",children:(0,a.jsx)(g.q5,{className:"text-sm",children:e.name.split(" ").map(e=>e[0]).join("").toUpperCase()})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("div",{className:"font-medium truncate",children:e.name}),(0,a.jsxs)("div",{className:"text-sm text-muted-foreground flex items-center gap-1",children:[(0,a.jsx)(O.A,{className:"h-3 w-3 flex-shrink-0"}),(0,a.jsx)("span",{className:"truncate",children:e.phone}),e.email&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(_.A,{className:"h-3 w-3 flex-shrink-0 ml-2"}),(0,a.jsx)("span",{className:"truncate",children:e.email})]})]})]})]})},e.id)),!P&&A.length>=50&&(0,a.jsx)(k.eb,{value:"load-more",disabled:!0,children:(0,a.jsx)("div",{className:"text-center text-muted-foreground py-2",children:"Use search to find more clients • استخدم البحث للعثور على المزيد"})})]})]}),!M.clientId&&(0,a.jsx)("p",{className:"text-xs text-red-600 dark:text-red-400",children:"Client selection is required • اختيار العميل مطلوب"}),P&&(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:A.length>0?"Found ".concat(A.length," client").concat(1!==A.length?"s":""," • تم العثور على ").concat(A.length," عميل"):"No results found • لم يتم العثور على نتائج"}),K&&(0,a.jsx)(n.Zp,{className:"bg-muted/50 border-muted",children:(0,a.jsx)(n.Wu,{className:"p-3",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(g.eu,{className:"h-10 w-10",children:(0,a.jsx)(g.q5,{children:K.name.split(" ").map(e=>e[0]).join("").toUpperCase()})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:K.name}),(0,a.jsxs)("div",{className:"text-sm text-muted-foreground flex items-center gap-2",children:[(0,a.jsx)(O.A,{className:"h-3 w-3"}),K.phone,K.email&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(_.A,{className:"h-3 w-3 ml-2"}),K.email]})]})]})]})})})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(D.J,{htmlFor:"scheduledAt",className:"flex items-center gap-2 text-sm font-medium",children:[(0,a.jsx)(m.A,{className:"h-4 w-4"}),"Date & Time • التاريخ والوقت *"]}),(0,a.jsx)(r.p,{id:"scheduledAt",type:"datetime-local",value:M.scheduledAt,onChange:e=>Q("scheduledAt",e.target.value),className:"".concat(M.scheduledAt?"":"border-red-300 focus:border-red-500"),min:new Date().toISOString().slice(0,16),required:!0}),!M.scheduledAt&&(0,a.jsx)("p",{className:"text-xs text-red-600 dark:text-red-400",children:"Date and time are required • التاريخ والوقت مطلوبان"}),M.scheduledAt&&new Date(M.scheduledAt)<new Date&&(0,a.jsx)("p",{className:"text-xs text-red-600 dark:text-red-400",children:"Cannot schedule appointments in the past • لا يمكن جدولة مواعيد في الماضي"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(D.J,{htmlFor:"type",className:"flex items-center gap-2",children:[(0,a.jsx)(j.A,{className:"h-4 w-4"}),"Type • النوع"]}),(0,a.jsxs)(k.l6,{value:M.type,onValueChange:e=>Q("type",e),children:[(0,a.jsx)(k.bq,{className:"h-12",children:(0,a.jsx)(k.yv,{})}),(0,a.jsxs)(k.gC,{children:[(0,a.jsx)(k.eb,{value:"viewing",className:"py-3",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(j.A,{className:"h-4 w-4 text-blue-600"}),"Property Viewing • معاينة عقار"]})}),(0,a.jsx)(k.eb,{value:"meeting",className:"py-3",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(N.A,{className:"h-4 w-4 text-green-600"}),"Client Meeting • اجتماع عميل"]})}),(0,a.jsx)(k.eb,{value:"signing",className:"py-3",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(v.A,{className:"h-4 w-4 text-purple-600"}),"Contract Signing • توقيع عقد"]})}),(0,a.jsx)(k.eb,{value:"valuation",className:"py-3",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(d.A,{className:"h-4 w-4 text-orange-600"}),"Property Valuation • تقييم عقار"]})}),(0,a.jsx)(k.eb,{value:"other",className:"py-3",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(d.A,{className:"h-4 w-4 text-gray-600"}),"Other • أخرى"]})})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(D.J,{htmlFor:"location",className:"flex items-center gap-2",children:[(0,a.jsx)(w.A,{className:"h-4 w-4"}),"Location • الموقع"]}),(0,a.jsx)(r.p,{id:"location",placeholder:"Enter location • أدخل الموقع",value:M.location,onChange:e=>Q("location",e.target.value)})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(D.J,{htmlFor:"description",className:"flex items-center gap-2",children:[(0,a.jsx)(F.A,{className:"h-4 w-4"}),"Description • الوصف"]}),(0,a.jsx)(E.T,{id:"description",placeholder:"Enter description (optional) • أدخل الوصف (اختياري)",value:M.description,onChange:e=>Q("description",e.target.value),rows:3,className:"resize-none"})]}),(0,a.jsxs)(L.Es,{className:"border-t pt-4",children:[(0,a.jsx)(l.$,{type:"button",variant:"outline",onClick:()=>t(!1),children:"Cancel • إلغاء"}),(0,a.jsx)(l.$,{type:"submit",disabled:h,children:h?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(I.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Saving... • جاري الحفظ..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(T.A,{className:"h-4 w-4 mr-2"}),o?"Update • تحديث":"Create • إنشاء"]})})]})]})]})})}function U(e){let{appointments:s,loading:t,onRefresh:r}=e,[o,x]=(0,f.useState)(null),[u,h]=(0,f.useState)(!1),[p,D]=(0,f.useState)(null),[E,k]=(0,f.useState)(null),{toast:L}=(0,R.dj)(),F=e=>{x(e),h(!0)},O=async e=>{if(confirm('Delete "'.concat(e.title,'"? • حذف "').concat(e.title,'"؟')))try{D(e.id),await q.A.delete("/appointments/".concat(e.id)),L({title:"Success",description:"Appointment deleted • تم حذف الموعد"}),r()}catch(e){console.error("Error deleting appointment:",e),L({title:"Error",description:"Failed to delete • فشل في الحذف",variant:"destructive"})}finally{D(null)}},_=async(e,s)=>{try{k(e.id),await q.A.patch("/appointments/".concat(e.id,"/status"),{status:s}),L({title:"Success",description:{CONFIRMED:"Confirmed • تم التأكيد",CANCELLED:"Cancelled • تم الإلغاء",COMPLETED:"Completed • تم الإنجاز",NO_SHOW:"No Show • لم يحضر"}[s]}),r()}catch(e){console.error("Error updating status:",e),L({title:"Error",description:"Failed to update • فشل في التحديث",variant:"destructive"})}finally{k(null)}},I=e=>{let s={SCHEDULED:{label:"Scheduled • مجدول",variant:"secondary",className:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400"},CONFIRMED:{label:"Confirmed • مؤكد",variant:"default",className:"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"},COMPLETED:{label:"Completed • مكتمل",variant:"outline",className:"bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400"},CANCELLED:{label:"Cancelled • ملغي",variant:"destructive",className:"bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"},NO_SHOW:{label:"No Show • لم يحضر",variant:"outline",className:"bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400"}},t=s[e]||s.SCHEDULED;return(0,a.jsx)(i.E,{variant:t.variant,className:t.className,children:t.label})},T=e=>{let s={viewing:{label:"Property Viewing • معاينة عقار",icon:j.A},meeting:{label:"Client Meeting • اجتماع عميل",icon:N.A},signing:{label:"Contract Signing • توقيع عقد",icon:v.A},valuation:{label:"Property Valuation • تقييم عقار",icon:d.A},other:{label:"Other • أخرى",icon:d.A}};return s[e]||s.other},U=e=>{let s=new Date(e),t=new Date,a=s.toDateString()===t.toDateString(),l=s.toDateString()===new Date(t.getTime()+864e5).toDateString(),r=s.toLocaleDateString();return a?r="Today • اليوم":l&&(r="Tomorrow • غداً"),{date:r,time:s.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}),isPast:s<t,isToday:a,isTomorrow:l}};return t?(0,a.jsx)(n.Zp,{children:(0,a.jsx)(n.Wu,{className:"flex items-center justify-center py-12",children:(0,a.jsxs)("div",{className:"text-center space-y-4",children:[(0,a.jsx)(c.A,{className:"h-8 w-8 animate-spin mx-auto text-primary"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:"Loading appointments..."}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"جاري تحميل المواعيد..."})]})]})})}):0===s.length?(0,a.jsx)(n.Zp,{children:(0,a.jsx)(n.Wu,{className:"text-center py-12",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(d.A,{className:"h-16 w-16 mx-auto text-muted-foreground"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"No appointments found"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"لا توجد مواعيد"})]}),(0,a.jsxs)(l.$,{onClick:r,variant:"outline",children:[(0,a.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Refresh • تحديث"]})]})})}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(d.A,{className:"h-5 w-5"}),"Appointments List • قائمة المواعيد"]})}),(0,a.jsx)(n.Wu,{className:"p-0",children:(0,a.jsx)("div",{className:"space-y-0",children:s.map(e=>{let{date:s,time:t,isPast:r,isToday:n,isTomorrow:i}=U(e.scheduledAt),d=T(e.type),c=d.icon;return e.id,e.id,(0,a.jsx)("div",{className:"\n                    p-4 border-b last:border-b-0 hover:bg-muted/50 transition-colors\n                    ".concat(r?"opacity-60":"","\n                    ").concat(n?"bg-blue-50 dark:bg-blue-950/20 border-l-4 border-l-blue-500":"","\n                    ").concat(i?"bg-green-50 dark:bg-green-950/20 border-l-4 border-l-green-500":"","\n                  "),children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,a.jsx)(g.eu,{className:"h-10 w-10",children:(0,a.jsx)(g.q5,{children:e.client.name.split(" ").map(e=>e[0]).join("").toUpperCase()})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,a.jsx)(c,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("h3",{className:"font-semibold",children:e.title}),I(e.status)]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:d.label})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-3 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsxs)("span",{children:[s," at ",t]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(N.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("span",{children:e.client.name})]}),e.location&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(w.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("span",{children:e.location})]})]}),e.description&&(0,a.jsx)("p",{className:"text-sm text-muted-foreground mt-2 p-2 bg-muted rounded",children:e.description})]}),(0,a.jsx)("div",{className:"ml-4",children:(0,a.jsxs)(S.rI,{children:[(0,a.jsx)(S.ty,{asChild:!0,children:(0,a.jsx)(l.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(b.A,{className:"h-4 w-4"})})}),(0,a.jsxs)(S.SQ,{align:"end",children:["SCHEDULED"===e.status&&(0,a.jsxs)(S._2,{onClick:()=>_(e,"CONFIRMED"),children:[(0,a.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"Confirm • تأكيد"]}),("SCHEDULED"===e.status||"CONFIRMED"===e.status)&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(S._2,{onClick:()=>_(e,"COMPLETED"),children:[(0,a.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"Complete • إنجاز"]}),(0,a.jsxs)(S._2,{onClick:()=>_(e,"CANCELLED"),children:[(0,a.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Cancel • إلغاء"]})]}),(0,a.jsx)(S.mB,{}),(0,a.jsxs)(S._2,{onClick:()=>F(e),children:[(0,a.jsx)(C.A,{className:"h-4 w-4 mr-2"}),"Edit • تعديل"]}),(0,a.jsxs)(S._2,{onClick:()=>O(e),className:"text-destructive focus:text-destructive",children:[(0,a.jsx)(A.A,{className:"h-4 w-4 mr-2"}),"Delete • حذف"]})]})]})})]})},e.id)})})})]}),(0,a.jsx)(Z,{open:u,onOpenChange:h,appointment:o,onSuccess:()=>{r(),h(!1),x(null)}})]})}function P(){let{appointments:e,loading:s,searchTerm:t,setSearchTerm:g,refreshAppointments:j}=function(){let[e,s]=(0,f.useState)([]),[t,a]=(0,f.useState)(!0),[l,r]=(0,f.useState)(""),n=async()=>{try{a(!0);let e=await q.A.get("/appointments"),t=(Array.isArray(e)?e:[]).sort((e,s)=>{let t=new Date(e.scheduledAt),a=new Date(s.scheduledAt);return t.getTime()-a.getTime()});s(t)}catch(e){console.error("Error fetching appointments:",e),s([])}finally{a(!1)}},i=(0,f.useMemo)(()=>{if(!l)return e;let s=l.toLowerCase();return e.filter(e=>{var t,a;return e.title.toLowerCase().includes(s)||e.client.name.toLowerCase().includes(s)||(null===(t=e.location)||void 0===t?void 0:t.toLowerCase().includes(s))||e.type.toLowerCase().includes(s)||e.status.toLowerCase().includes(s)||(null===(a=e.description)||void 0===a?void 0:a.toLowerCase().includes(s))})},[e,l]);return(0,f.useEffect)(()=>{n()},[]),{appointments:i,loading:t,searchTerm:l,setSearchTerm:r,refreshAppointments:()=>{n()}}}(),[N,v]=(0,f.useState)(!1),w={total:e.length,scheduled:e.filter(e=>"SCHEDULED"===e.status).length,confirmed:e.filter(e=>"CONFIRMED"===e.status).length,completed:e.filter(e=>"COMPLETED"===e.status).length,upcoming:e.filter(e=>new Date(e.scheduledAt)>new Date).length};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"relative overflow-hidden rounded-xl bg-card border shadow-sm",children:(0,a.jsx)("div",{className:"p-6",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"p-2 rounded-lg bg-primary",children:(0,a.jsx)(d.A,{className:"h-6 w-6 text-primary-foreground"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-foreground",children:"\uD83D\uDCC5 Appointments"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"مواعيد العملاء • Client Scheduling"})]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(l.$,{variant:"outline",onClick:j,disabled:s,size:"sm",children:[(0,a.jsx)(c.A,{className:"h-4 w-4 mr-2 ".concat(s?"animate-spin":"")}),"Refresh"]}),(0,a.jsxs)(l.$,{onClick:()=>v(!0),size:"sm",children:[(0,a.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"New Appointment"]})]})]})})}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4",children:[(0,a.jsx)(n.Zp,{className:"hover:shadow-md transition-shadow",children:(0,a.jsx)(n.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Total"}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:w.total}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"المجموع"})]}),(0,a.jsx)("div",{className:"p-2 rounded-lg bg-primary/10",children:(0,a.jsx)(d.A,{className:"h-5 w-5 text-primary"})})]})})}),(0,a.jsx)(n.Zp,{className:"hover:shadow-md transition-shadow",children:(0,a.jsx)(n.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Scheduled"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-yellow-600 dark:text-yellow-400",children:w.scheduled}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"مجدولة"})]}),(0,a.jsx)("div",{className:"p-2 rounded-lg bg-yellow-100 dark:bg-yellow-900/20",children:(0,a.jsx)(m.A,{className:"h-5 w-5 text-yellow-600 dark:text-yellow-400"})})]})})}),(0,a.jsx)(n.Zp,{className:"hover:shadow-md transition-shadow",children:(0,a.jsx)(n.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Confirmed"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-green-600 dark:text-green-400",children:w.confirmed}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"مؤكدة"})]}),(0,a.jsx)("div",{className:"p-2 rounded-lg bg-green-100 dark:bg-green-900/20",children:(0,a.jsx)(x.A,{className:"h-5 w-5 text-green-600 dark:text-green-400"})})]})})}),(0,a.jsx)(n.Zp,{className:"hover:shadow-md transition-shadow",children:(0,a.jsx)(n.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Completed"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-600 dark:text-blue-400",children:w.completed}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"مكتملة"})]}),(0,a.jsx)("div",{className:"p-2 rounded-lg bg-blue-100 dark:bg-blue-900/20",children:(0,a.jsx)(u.A,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"})})]})})}),(0,a.jsx)(n.Zp,{className:"hover:shadow-md transition-shadow",children:(0,a.jsx)(n.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Upcoming"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-orange-600 dark:text-orange-400",children:w.upcoming}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"قادمة"})]}),(0,a.jsx)("div",{className:"p-2 rounded-lg bg-orange-100 dark:bg-orange-900/20",children:(0,a.jsx)(h.A,{className:"h-5 w-5 text-orange-600 dark:text-orange-400"})})]})})})]}),(0,a.jsx)(n.Zp,{children:(0,a.jsx)(n.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row items-center gap-4",children:[(0,a.jsxs)("div",{className:"relative flex-1 max-w-lg",children:[(0,a.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(r.p,{placeholder:"Search appointments... • البحث في المواعيد...",value:t,onChange:e=>g(e.target.value),className:"pl-10"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(i.E,{variant:"outline",className:"text-sm",children:[(0,a.jsx)(d.A,{className:"h-3 w-3 mr-1"}),e.length," appointments"]}),(0,a.jsxs)(i.E,{variant:"outline",className:"text-sm",children:["موعد ",e.length]})]})]})})}),(0,a.jsx)(U,{appointments:e,loading:s,onRefresh:j}),(0,a.jsx)(Z,{open:N,onOpenChange:v,onSuccess:j})]})}},67133:(e,s,t)=>{"use strict";t.d(s,{SQ:()=>x,_2:()=>u,mB:()=>h,rI:()=>o,ty:()=>m});var a=t(95155),l=t(12115),r=t(48698),n=t(13052),i=t(5196),d=t(9428),c=t(53999);let o=r.bL,m=r.l9;r.YJ,r.ZL,r.Pb,r.z6,l.forwardRef((e,s)=>{let{className:t,inset:l,children:i,...d}=e;return(0,a.jsxs)(r.ZP,{ref:s,className:(0,c.cn)("flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",l&&"pl-8",t),...d,children:[i,(0,a.jsx)(n.A,{className:"ml-auto"})]})}).displayName=r.ZP.displayName,l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.G5,{ref:s,className:(0,c.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",t),...l})}).displayName=r.G5.displayName;let x=l.forwardRef((e,s)=>{let{className:t,sideOffset:l=4,...n}=e;return(0,a.jsx)(r.ZL,{children:(0,a.jsx)(r.UC,{ref:s,sideOffset:l,className:(0,c.cn)("z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",t),...n})})});x.displayName=r.UC.displayName;let u=l.forwardRef((e,s)=>{let{className:t,inset:l,...n}=e;return(0,a.jsx)(r.q7,{ref:s,className:(0,c.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",l&&"pl-8",t),...n})});u.displayName=r.q7.displayName,l.forwardRef((e,s)=>{let{className:t,children:l,checked:n,...d}=e;return(0,a.jsxs)(r.H_,{ref:s,className:(0,c.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),checked:n,...d,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(i.A,{className:"h-4 w-4"})})}),l]})}).displayName=r.H_.displayName,l.forwardRef((e,s)=>{let{className:t,children:l,...n}=e;return(0,a.jsxs)(r.hN,{ref:s,className:(0,c.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...n,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(d.A,{className:"h-2 w-2 fill-current"})})}),l]})}).displayName=r.hN.displayName,l.forwardRef((e,s)=>{let{className:t,inset:l,...n}=e;return(0,a.jsx)(r.JU,{ref:s,className:(0,c.cn)("px-2 py-1.5 text-sm font-semibold",l&&"pl-8",t),...n})}).displayName=r.JU.displayName;let h=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.wv,{ref:s,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",t),...l})});h.displayName=r.wv.displayName},69663:(e,s,t)=>{"use strict";t.d(s,{BK:()=>d,eu:()=>i,q5:()=>c});var a=t(95155),l=t(12115),r=t(85977),n=t(53999);let i=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.bL,{ref:s,className:(0,n.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",t),...l})});i.displayName=r.bL.displayName;let d=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r._V,{ref:s,className:(0,n.cn)("aspect-square h-full w-full",t),...l})});d.displayName=r._V.displayName;let c=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.H4,{ref:s,className:(0,n.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",t),...l})});c.displayName=r.H4.displayName},82714:(e,s,t)=>{"use strict";t.d(s,{J:()=>c});var a=t(95155),l=t(12115),r=t(40968),n=t(74466),i=t(53999);let d=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.b,{ref:s,className:(0,i.cn)(d(),t),...l})});c.displayName=r.b.displayName},88482:(e,s,t)=>{"use strict";t.d(s,{BT:()=>c,Wu:()=>o,ZB:()=>d,Zp:()=>n,aR:()=>i,wL:()=>m});var a=t(95155),l=t(12115),r=t(53999);let n=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("div",{ref:s,className:(0,r.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...l})});n.displayName="Card";let i=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("div",{ref:s,className:(0,r.cn)("flex flex-col space-y-1.5 p-6",t),...l})});i.displayName="CardHeader";let d=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("div",{ref:s,className:(0,r.cn)("text-2xl font-semibold leading-none tracking-tight",t),...l})});d.displayName="CardTitle";let c=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("div",{ref:s,className:(0,r.cn)("text-sm text-muted-foreground",t),...l})});c.displayName="CardDescription";let o=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("div",{ref:s,className:(0,r.cn)("p-6 pt-0",t),...l})});o.displayName="CardContent";let m=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("div",{ref:s,className:(0,r.cn)("flex items-center p-6 pt-0",t),...l})});m.displayName="CardFooter"},99474:(e,s,t)=>{"use strict";t.d(s,{T:()=>n});var a=t(95155),l=t(12115),r=t(53999);let n=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("textarea",{className:(0,r.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:s,...l})});n.displayName="Textarea"}},e=>{var s=s=>e(e.s=s);e.O(0,[4277,6071,9509,9855,3464,1071,1118,8438,6308,8441,1684,7358],()=>s(28965)),_N_E=e.O()}]);