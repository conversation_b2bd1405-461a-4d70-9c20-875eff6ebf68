"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7196],{4229:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},14353:(e,t,r)=>{r.d(t,{U:()=>m});var a=r(12115),n=r(31886);let s=e=>!!e&&/[\u0600-\u06FF]/.test(e),o=e=>"ar"===e.language||e.id&&e.id.includes("_ar")||e.name&&s(e.name)||e.content&&s(e.content)?"ar":"en",l=e=>"ar"===e||"en"===e?e:"en",i={en:[{id:"hello_world_en",name:"Hello World",content:"Hello, {{1}}! Welcome to our service.",category:"greeting",variables:["name"],language:"en",createdAt:new Date().toISOString()},{id:"appointment_reminder_en",name:"Appointment Reminder",content:"Hi {{1}}, this is a reminder about your appointment on {{2}} at {{3}}.",category:"reminder",variables:["name","date","time"],language:"en",createdAt:new Date().toISOString()}],ar:[{id:"hello_world_ar",name:"مرحبا بالعالم",content:"مرحبًا، {{1}}! مرحبًا بك في خدمتنا.",category:"greeting",variables:["name"],language:"ar",createdAt:new Date().toISOString()},{id:"appointment_reminder_ar",name:"تذكير بالموعد",content:"مرحبًا {{1}}، هذا تذكير بموعدك في {{2}} الساعة {{3}}.",category:"reminder",variables:["name","date","time"],language:"ar",createdAt:new Date().toISOString()}]},d=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"en";try{if("all"===e)try{let e=await n.A.get("/marketing/templates",{params:{language:"en"}}),t=await n.A.get("/marketing/templates",{params:{language:"ar"}});return[...e,...t].map(e=>{let t=Array.isArray(e.variables)?e.variables:[],r=o(e);return{...e,language:r,variables:t,createdAt:e.createdAt||new Date().toISOString(),updatedAt:e.updatedAt||new Date().toISOString()}})}catch(e){if(e.message&&e.message.includes("Network Error"))return console.warn("Network error detected, using mock templates"),[...i.en,...i.ar];throw e}let t=l(e);try{return(await n.A.get("/marketing/templates",{params:{language:t}})).map(e=>{let t=Array.isArray(e.variables)?e.variables:[],r=o(e);return{...e,language:r,variables:t,createdAt:e.createdAt||new Date().toISOString(),updatedAt:e.updatedAt||new Date().toISOString()}})}catch(e){if(e.message&&e.message.includes("Network Error"))return console.warn("Network error detected, using mock templates"),i[t]||[];throw e}}catch(e){throw console.error("Error fetching templates:",e),e}},c=async e=>{try{let t=l(e.language),r={...e,language:t,name:e.name.trim(),variables:Array.isArray(e.variables)?e.variables:[]};return await n.A.post("/marketing/templates",r)}catch(e){throw console.error("Error creating template:",e),e}},u=async(e,t)=>{try{let r={...t};return t.language&&(r.language=l(t.language)),t.name&&(r.name=t.name.trim()),t.variables&&(r.variables=Array.isArray(t.variables)?t.variables:[]),await n.A.put("/marketing/templates/".concat(e),r)}catch(t){throw console.error("Error updating template with ID ".concat(e,":"),t),t}},f=async e=>{try{if(!e)throw console.error("Invalid template ID for deletion:",e),Error("Invalid template ID");let t=e.trim();return console.log("Deleting template with ID: ".concat(t)),await n.A.delete("/marketing/templates/".concat(t)),console.log("Template with ID ".concat(t," deleted successfully")),!0}catch(t){if(console.error("Error deleting template with ID ".concat(e,":"),t),t.response&&404===t.response.status)return console.warn("Template with ID ".concat(e," not found, it may have been already deleted")),!0;if(t.response&&t.response.data&&t.response.data.error)throw console.error("Server error: ".concat(t.response.data.error)),Error(t.response.data.error);throw t}};var p=r(14503);function m(){let[e,t]=(0,a.useState)([]),[r,n]=(0,a.useState)(!0),[s,o]=(0,a.useState)(null),[l,i]=(0,a.useState)({}),[m,g]=(0,a.useState)(!1),[h,y]=(0,a.useState)(null),{toast:v}=(0,p.dj)(),w=(0,a.useCallback)(async()=>{try{n(!0),o(null);let[e,r]=await Promise.all([d("en").catch(()=>[]),d("ar").catch(()=>[])]),a=[...e,...r].sort((e,t)=>e.createdAt&&t.createdAt?new Date(t.createdAt).getTime()-new Date(e.createdAt).getTime():0);t(a)}catch(e){console.error("Error fetching templates:",e),o(e.message||"Failed to load templates"),t([])}finally{n(!1)}},[]),b=(0,a.useMemo)(()=>{let t=[...e];if(l.search){let e=l.search.toLowerCase();t=t.filter(t=>t.name.toLowerCase().includes(e)||t.content.toLowerCase().includes(e)||t.category&&t.category.toLowerCase().includes(e))}return l.category&&"all"!==l.category&&(t=t.filter(e=>e.category===l.category)),l.language&&"all"!==l.language&&(t=t.filter(e=>e.language===l.language)),t},[e,l]);(0,a.useEffect)(()=>(m&&!h?y(setInterval(()=>{w()},3e4)):!m&&h&&(clearInterval(h),y(null)),()=>{h&&clearInterval(h)}),[m,h,w]),(0,a.useEffect)(()=>{let t=!0;return(async()=>{t&&0===e.length&&await w()})(),()=>{t=!1}},[]);let x=(0,a.useCallback)(e=>{i(t=>({...t,...e}))},[]),A=(0,a.useCallback)(()=>{i({})},[]),N=(0,a.useCallback)(()=>{g(e=>!e)},[]),E=(0,a.useCallback)(()=>{w()},[w]),S=async e=>{try{n(!0);let r=await c(e);return t(e=>[r,...e]),v({title:"Success",description:"Template created successfully"}),!0}catch(e){return console.error("Error adding template:",e),o(e.message||"Failed to add template"),v({title:"Error",description:e.message||"Failed to create template",variant:"destructive"}),!1}finally{n(!1)}},k=async(e,r)=>{try{n(!0);let a=await u(e,r);return t(t=>t.map(t=>t.id===e?a:t)),v({title:"Success",description:"Template updated successfully"}),!0}catch(t){return console.error("Error updating template with ID ".concat(e,":"),t),o(t.message||"Failed to update template"),v({title:"Error",description:t.message||"Failed to update template",variant:"destructive"}),!1}finally{n(!1)}},I=async e=>{try{if(n(!0),o(null),await f(e))return t(t=>t.filter(t=>t.id!==e)),v({title:"Success",description:"Template deleted successfully"}),!0;throw Error("Failed to delete template")}catch(t){return console.error("Error deleting template with ID ".concat(e,":"),t),o(t.message||"Failed to delete template"),v({title:"Error",description:t.message||"Failed to delete template",variant:"destructive"}),!1}finally{n(!1)}},T=(0,a.useMemo)(()=>["all",...new Set(e.map(e=>e.category).filter(Boolean))],[e]),j=(0,a.useMemo)(()=>["all",...new Set(e.map(e=>e.language).filter(Boolean))],[e]);return{templates:b,allTemplates:e,loading:r,error:s,filters:l,updateFilters:x,clearFilters:A,categories:T,languages:j,autoRefresh:m,toggleAutoRefresh:N,refreshTemplates:E,addTemplate:S,editTemplate:k,removeTemplate:I,fetchTemplates:w,currentLanguage:"all",displayLanguage:"all",showAllLanguages:!0}}},14503:(e,t,r)=>{r.d(t,{dj:()=>f,oR:()=>u});var a=r(12115);let n=0,s=new Map,o=e=>{if(s.has(e))return;let t=setTimeout(()=>{s.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);s.set(e,t)},l=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?o(r):e.toasts.forEach(e=>{o(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},i=[],d={toasts:[]};function c(e){d=l(d,e),i.forEach(e=>{e(d)})}function u(e){let{...t}=e,r=(n=(n+1)%Number.MAX_SAFE_INTEGER).toString(),a=()=>c({type:"DISMISS_TOAST",toastId:r});return c({type:"ADD_TOAST",toast:{...t,id:r,open:!0,onOpenChange:e=>{e||a()}}}),{id:r,dismiss:a,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:r}})}}function f(){let[e,t]=a.useState(d);return a.useEffect(()=>(i.push(t),()=>{let e=i.indexOf(t);e>-1&&i.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},31886:(e,t,r)=>{r.d(t,{A:()=>s});var a=r(23464);class n{async get(e,t){try{if(this.isOfflineMode&&!e.includes("/health"))throw console.log("API Client: In offline mode, skipping GET request to ".concat(e)),Error("Network Error: Application is in offline mode");let r=await this.client.get(e,t);return this.isOfflineMode&&this.setOfflineMode(!1),r.data}catch(e){throw e}}async post(e,t,r){return(await this.client.post(e,t,r)).data}async put(e,t,r){return(await this.client.put(e,t,r)).data}async delete(e,t){try{console.log("Making DELETE request to: ".concat(e));let r=await this.client.delete(e,t);if(204===r.status)return console.log("DELETE request to ".concat(e," successful with 204 status")),null;return r.data}catch(t){throw console.error("DELETE request to ".concat(e," failed:"),t),t}}async patch(e,t,r){return(await this.client.patch(e,t,r)).data}async upload(e,t,r){let a={...r,headers:{...null==r?void 0:r.headers,"Content-Type":"multipart/form-data"}};return(await this.client.post(e,t,a)).data}getBaseURL(){return this.baseURL}isInOfflineMode(){return this.isOfflineMode}setOfflineMode(e){this.isOfflineMode!==e&&(console.log("API Client: Setting offline mode to ".concat(e)),this.isOfflineMode=e,window.dispatchEvent(new CustomEvent("api:offline",{detail:{isOffline:e}})))}async tryReconnect(){try{return console.log("API Client: Attempting to reconnect to the server..."),await this.client.get("/health",{timeout:5e3}),console.log("API Client: Reconnection successful"),this.setOfflineMode(!1),!0}catch(e){return console.log("API Client: Reconnection failed, still in offline mode"),!1}}constructor(){this.isOfflineMode=!1,this.baseURL="http://localhost:5000/api/v1",this.baseURL||console.warn("NEXT_PUBLIC_BACKEND_API_URL is not defined. API requests may fail."),this.client=a.A.create({baseURL:this.baseURL,headers:{"Content-Type":"application/json"},timeout:1e4}),this.client.interceptors.request.use(e=>e,e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,e=>{if(e.response){if(console.error("API Error Response:",e.response.status,e.response.data),e.response.data&&0!==Object.keys(e.response.data).length||(e.response.data={error:"Request failed with status code ".concat(e.response.status)}),404===e.response.status){var t;console.log("Resource not found:",null===(t=e.config)||void 0===t?void 0:t.url),e.response.data={error:"The requested resource was not found. Please refresh and try again."}}e.responseData=e.response.data}else e.request?(console.error("API No Response:",e.request),e.message&&(e.message.includes("Network Error")||e.message.includes("timeout"))?(console.error("Network Error detected. Server might be down or unreachable."),this.setOfflineMode(!0),e.isOffline=!0,e.responseData={error:"Network Error: Unable to connect to the server. Please check your connection or try again later.",isOffline:!0},console.warn("API Client: Server connection failed. If you're running in development mode, make sure your backend server is running."),window.dispatchEvent(new CustomEvent("api:offline",{detail:{isOffline:!0}}))):e.responseData={error:"No response received from server. Please check your connection."}):(console.error("API Request Error:",e.message),e.responseData={error:e.message||"An unexpected error occurred"});return Promise.reject(e)})}}let s=new n},35169:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},35695:(e,t,r)=>{var a=r(18999);r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},40968:(e,t,r)=>{r.d(t,{b:()=>l});var a=r(12115),n=r(63655),s=r(95155),o=a.forwardRef((e,t)=>(0,s.jsx)(n.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null===(r=e.onMouseDown)||void 0===r||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var l=o},51154:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},53999:(e,t,r)=>{r.d(t,{cn:()=>s});var a=r(52596),n=r(39688);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.QP)((0,a.$)(t))}},54416:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},82714:(e,t,r)=>{r.d(t,{J:()=>d});var a=r(95155),n=r(12115),s=r(40968),o=r(74466),l=r(53999);let i=(0,o.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(s.b,{ref:t,className:(0,l.cn)(i(),r),...n})});d.displayName=s.b.displayName},84616:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},88145:(e,t,r)=>{r.d(t,{E:()=>l});var a=r(95155);r(12115);var n=r(74466),s=r(53999);let o=(0,n.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:r,...n}=e;return(0,a.jsx)("div",{className:(0,s.cn)(o({variant:r}),t),...n})}},88482:(e,t,r)=>{r.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>i,Zp:()=>o,aR:()=>l,wL:()=>u});var a=r(95155),n=r(12115),s=r(53999);let o=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,s.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...n})});o.displayName="Card";let l=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",r),...n})});l.displayName="CardHeader";let i=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,s.cn)("text-2xl font-semibold leading-none tracking-tight",r),...n})});i.displayName="CardTitle";let d=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,s.cn)("text-sm text-muted-foreground",r),...n})});d.displayName="CardDescription";let c=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,s.cn)("p-6 pt-0",r),...n})});c.displayName="CardContent";let u=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,s.cn)("flex items-center p-6 pt-0",r),...n})});u.displayName="CardFooter"},89852:(e,t,r)=>{r.d(t,{p:()=>o});var a=r(95155),n=r(12115),s=r(53999);let o=n.forwardRef((e,t)=>{let{className:r,type:n,...o}=e;return(0,a.jsx)("input",{type:n,className:(0,s.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,...o})});o.displayName="Input"},95784:(e,t,r)=>{r.d(t,{bq:()=>f,eb:()=>h,gC:()=>g,l6:()=>c,yv:()=>u});var a=r(95155),n=r(12115),s=r(31992),o=r(66474),l=r(47863),i=r(5196),d=r(53999);let c=s.bL;s.YJ;let u=s.WT,f=n.forwardRef((e,t)=>{let{className:r,children:n,...l}=e;return(0,a.jsxs)(s.l9,{ref:t,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",r),...l,children:[n,(0,a.jsx)(s.In,{asChild:!0,children:(0,a.jsx)(o.A,{className:"h-4 w-4 opacity-50"})})]})});f.displayName=s.l9.displayName;let p=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(s.PP,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",r),...n,children:(0,a.jsx)(l.A,{className:"h-4 w-4"})})});p.displayName=s.PP.displayName;let m=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(s.wn,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",r),...n,children:(0,a.jsx)(o.A,{className:"h-4 w-4"})})});m.displayName=s.wn.displayName;let g=n.forwardRef((e,t)=>{let{className:r,children:n,position:o="popper",...l}=e;return(0,a.jsx)(s.ZL,{children:(0,a.jsxs)(s.UC,{ref:t,className:(0,d.cn)("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]","popper"===o&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",r),position:o,...l,children:[(0,a.jsx)(p,{}),(0,a.jsx)(s.LM,{className:(0,d.cn)("p-1","popper"===o&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:n}),(0,a.jsx)(m,{})]})})});g.displayName=s.UC.displayName,n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(s.JU,{ref:t,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",r),...n})}).displayName=s.JU.displayName;let h=n.forwardRef((e,t)=>{let{className:r,children:n,...o}=e;return(0,a.jsxs)(s.q7,{ref:t,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),...o,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(s.VF,{children:(0,a.jsx)(i.A,{className:"h-4 w-4"})})}),(0,a.jsx)(s.p4,{children:n})]})});h.displayName=s.q7.displayName,n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(s.wv,{ref:t,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",r),...n})}).displayName=s.wv.displayName},97168:(e,t,r)=>{r.d(t,{$:()=>d,r:()=>i});var a=r(95155),n=r(12115),s=r(99708),o=r(74466),l=r(53999);let i=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=n.forwardRef((e,t)=>{let{className:r,variant:n,size:o,asChild:d=!1,...c}=e,u=d?s.DX:"button";return(0,a.jsx)(u,{className:(0,l.cn)(i({variant:n,size:o,className:r})),ref:t,...c})});d.displayName="Button"},99474:(e,t,r)=>{r.d(t,{T:()=>o});var a=r(95155),n=r(12115),s=r(53999);let o=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("textarea",{className:(0,s.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,...n})});o.displayName="Textarea"}}]);