{"version": 3, "file": "Ordering.js", "names": ["_Function", "require", "reverse", "o", "exports", "match", "dual", "self", "onEqual", "onG<PERSON>r<PERSON><PERSON>", "onLessThan", "combine", "that", "combineMany", "collection", "ordering", "combineAll"], "sources": ["../../src/Ordering.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAIA,IAAAA,SAAA,GAAAC,OAAA;AAQA;;;;;;;;;;;;;;;AAeO,MAAMC,OAAO,GAAIC,CAAW,IAAgBA,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,GAAGA,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAE;AAEnF;;;;;;;;;;;;;;;;;;;;;;;AAAAC,OAAA,CAAAF,OAAA,GAAAA,OAAA;AAuBO,MAAMG,KAAK,GAAAD,OAAA,CAAAC,KAAA,gBA8Dd,IAAAC,cAAI,EAAC,CAAC,EAAE,CACVC,IAAc,EACd;EAAEC,OAAO;EAAEC,aAAa;EAAEC;AAAU,CAInC,KACaH,IAAI,KAAK,CAAC,CAAC,GAAGG,UAAU,EAAE,GAAGH,IAAI,KAAK,CAAC,GAAGC,OAAO,EAAE,GAAGC,aAAa,EAAE,CAAC;AAEtF;;;;AAIO,MAAME,OAAO,GAAAP,OAAA,CAAAO,OAAA,gBAWhB,IAAAL,cAAI,EAAC,CAAC,EAAE,CAACC,IAAc,EAAEK,IAAc,KAAeL,IAAI,KAAK,CAAC,GAAGA,IAAI,GAAGK,IAAI,CAAC;AAEnF;;;;AAIO,MAAMC,WAAW,GAAAT,OAAA,CAAAS,WAAA,gBAWpB,IAAAP,cAAI,EAAC,CAAC,EAAE,CAACC,IAAc,EAAEO,UAA8B,KAAc;EACvE,IAAIC,QAAQ,GAAGR,IAAI;EACnB,IAAIQ,QAAQ,KAAK,CAAC,EAAE;IAClB,OAAOA,QAAQ;EACjB;EACA,KAAKA,QAAQ,IAAID,UAAU,EAAE;IAC3B,IAAIC,QAAQ,KAAK,CAAC,EAAE;MAClB,OAAOA,QAAQ;IACjB;EACF;EACA,OAAOA,QAAQ;AACjB,CAAC,CAAC;AAEF;;;;AAIO,MAAMC,UAAU,GAAIF,UAA8B,IAAeD,WAAW,CAAC,CAAC,EAAEC,UAAU,CAAC;AAAAV,OAAA,CAAAY,UAAA,GAAAA,UAAA", "ignoreList": []}