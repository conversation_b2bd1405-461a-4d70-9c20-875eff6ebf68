globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/dashboard/properties/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./components/auth/auth-provider.tsx":{"*":{"id":"(ssr)/./components/auth/auth-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/DarkModeProvider.tsx":{"*":{"id":"(ssr)/./components/DarkModeProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/hydration-fix.tsx":{"*":{"id":"(ssr)/./components/hydration-fix.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/theme-provider.tsx":{"*":{"id":"(ssr)/./components/theme-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/toaster.tsx":{"*":{"id":"(ssr)/./components/ui/toaster.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/sidebar.tsx":{"*":{"id":"(ssr)/./components/sidebar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/topbar.tsx":{"*":{"id":"(ssr)/./components/topbar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/analytics/campaigns-chart.tsx":{"*":{"id":"(ssr)/./components/analytics/campaigns-chart.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/analytics/clients-chart.tsx":{"*":{"id":"(ssr)/./components/analytics/clients-chart.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/analytics/overview-chart.tsx":{"*":{"id":"(ssr)/./components/analytics/overview-chart.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/analytics/properties-chart.tsx":{"*":{"id":"(ssr)/./components/analytics/properties-chart.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/analytics/stats-cards.tsx":{"*":{"id":"(ssr)/./components/analytics/stats-cards.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/tabs.tsx":{"*":{"id":"(ssr)/./components/ui/tabs.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/dashboard/clients/page.tsx":{"*":{"id":"(ssr)/./app/dashboard/clients/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/messaging/chat-list.tsx":{"*":{"id":"(ssr)/./components/messaging/chat-list.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/dashboard/campaigns/page.tsx":{"*":{"id":"(ssr)/./app/dashboard/campaigns/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/marketing/marketing-nav.tsx":{"*":{"id":"(ssr)/./components/marketing/marketing-nav.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/dashboard/marketing/templates/page.tsx":{"*":{"id":"(ssr)/./app/dashboard/marketing/templates/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/dashboard/appointments/page.tsx":{"*":{"id":"(ssr)/./app/dashboard/appointments/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/dashboard/ai-chatbot/page.tsx":{"*":{"id":"(ssr)/./app/dashboard/ai-chatbot/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/dashboard/properties/page.tsx":{"*":{"id":"(ssr)/./app/dashboard/properties/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/dashboard/properties/edit/[id]/page.tsx":{"*":{"id":"(ssr)/./app/dashboard/properties/edit/[id]/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\components\\auth\\auth-provider.tsx":{"id":"(app-pages-browser)/./components/auth/auth-provider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\components\\DarkModeProvider.tsx":{"id":"(app-pages-browser)/./components/DarkModeProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\components\\hydration-fix.tsx":{"id":"(app-pages-browser)/./components/hydration-fix.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\components\\theme-provider.tsx":{"id":"(app-pages-browser)/./components/theme-provider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\components\\ui\\toaster.tsx":{"id":"(app-pages-browser)/./components/ui/toaster.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Cairo\",\"arguments\":[{\"subsets\":[\"latin\",\"arabic\"],\"variable\":\"--font-cairo\"}],\"variableName\":\"cairo\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Cairo\",\"arguments\":[{\"subsets\":[\"latin\",\"arabic\"],\"variable\":\"--font-cairo\"}],\"variableName\":\"cairo\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\components\\sidebar.tsx":{"id":"(app-pages-browser)/./components/sidebar.tsx","name":"*","chunks":["app/dashboard/layout","static/chunks/app/dashboard/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\components\\topbar.tsx":{"id":"(app-pages-browser)/./components/topbar.tsx","name":"*","chunks":["app/dashboard/layout","static/chunks/app/dashboard/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\components\\analytics\\campaigns-chart.tsx":{"id":"(app-pages-browser)/./components/analytics/campaigns-chart.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\components\\analytics\\clients-chart.tsx":{"id":"(app-pages-browser)/./components/analytics/clients-chart.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\components\\analytics\\overview-chart.tsx":{"id":"(app-pages-browser)/./components/analytics/overview-chart.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\components\\analytics\\properties-chart.tsx":{"id":"(app-pages-browser)/./components/analytics/properties-chart.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\components\\analytics\\stats-cards.tsx":{"id":"(app-pages-browser)/./components/analytics/stats-cards.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\components\\ui\\tabs.tsx":{"id":"(app-pages-browser)/./components/ui/tabs.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\clients\\page.tsx":{"id":"(app-pages-browser)/./app/dashboard/clients/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\components\\messaging\\chat-list.tsx":{"id":"(app-pages-browser)/./components/messaging/chat-list.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\campaigns\\page.tsx":{"id":"(app-pages-browser)/./app/dashboard/campaigns/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\components\\marketing\\marketing-nav.tsx":{"id":"(app-pages-browser)/./components/marketing/marketing-nav.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\marketing\\templates\\page.tsx":{"id":"(app-pages-browser)/./app/dashboard/marketing/templates/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\appointments\\page.tsx":{"id":"(app-pages-browser)/./app/dashboard/appointments/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\ai-chatbot\\page.tsx":{"id":"(app-pages-browser)/./app/dashboard/ai-chatbot/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\properties\\page.tsx":{"id":"(app-pages-browser)/./app/dashboard/properties/page.tsx","name":"*","chunks":["app/dashboard/properties/page","static/chunks/app/dashboard/properties/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\properties\\edit\\[id]\\page.tsx":{"id":"(app-pages-browser)/./app/dashboard/properties/edit/[id]/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\":[],"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\layout":[],"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\properties\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./app/globals.css":{"*":{"id":"(rsc)/./app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/auth/auth-provider.tsx":{"*":{"id":"(rsc)/./components/auth/auth-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/DarkModeProvider.tsx":{"*":{"id":"(rsc)/./components/DarkModeProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/hydration-fix.tsx":{"*":{"id":"(rsc)/./components/hydration-fix.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/theme-provider.tsx":{"*":{"id":"(rsc)/./components/theme-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/toaster.tsx":{"*":{"id":"(rsc)/./components/ui/toaster.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/sidebar.tsx":{"*":{"id":"(rsc)/./components/sidebar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/topbar.tsx":{"*":{"id":"(rsc)/./components/topbar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/analytics/campaigns-chart.tsx":{"*":{"id":"(rsc)/./components/analytics/campaigns-chart.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/analytics/clients-chart.tsx":{"*":{"id":"(rsc)/./components/analytics/clients-chart.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/analytics/overview-chart.tsx":{"*":{"id":"(rsc)/./components/analytics/overview-chart.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/analytics/properties-chart.tsx":{"*":{"id":"(rsc)/./components/analytics/properties-chart.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/analytics/stats-cards.tsx":{"*":{"id":"(rsc)/./components/analytics/stats-cards.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/tabs.tsx":{"*":{"id":"(rsc)/./components/ui/tabs.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/dashboard/clients/page.tsx":{"*":{"id":"(rsc)/./app/dashboard/clients/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/messaging/chat-list.tsx":{"*":{"id":"(rsc)/./components/messaging/chat-list.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/dashboard/campaigns/page.tsx":{"*":{"id":"(rsc)/./app/dashboard/campaigns/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/marketing/marketing-nav.tsx":{"*":{"id":"(rsc)/./components/marketing/marketing-nav.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/dashboard/marketing/templates/page.tsx":{"*":{"id":"(rsc)/./app/dashboard/marketing/templates/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/dashboard/appointments/page.tsx":{"*":{"id":"(rsc)/./app/dashboard/appointments/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/dashboard/ai-chatbot/page.tsx":{"*":{"id":"(rsc)/./app/dashboard/ai-chatbot/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/dashboard/properties/page.tsx":{"*":{"id":"(rsc)/./app/dashboard/properties/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/dashboard/properties/edit/[id]/page.tsx":{"*":{"id":"(rsc)/./app/dashboard/properties/edit/[id]/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}