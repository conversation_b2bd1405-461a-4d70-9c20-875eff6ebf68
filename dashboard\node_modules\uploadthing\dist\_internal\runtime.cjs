var platform = require('@effect/platform');
var FiberRef = require('effect/FiberRef');
var Layer = require('effect/Layer');
var ManagedRuntime = require('effect/ManagedRuntime');
var config_cjs = require('./config.cjs');
var logger_cjs = require('./logger.cjs');

function _interopNamespace(e) {
  if (e && e.__esModule) return e;
  var n = Object.create(null);
  if (e) {
    Object.keys(e).forEach(function (k) {
      if (k !== 'default') {
        var d = Object.getOwnPropertyDescriptor(e, k);
        Object.defineProperty(n, k, d.get ? d : {
          enumerable: true,
          get: function () { return e[k]; }
        });
      }
    });
  }
  n.default = e;
  return n;
}

var FiberRef__namespace = /*#__PURE__*/_interopNamespace(FiberRef);
var Layer__namespace = /*#__PURE__*/_interopNamespace(Layer);
var ManagedRuntime__namespace = /*#__PURE__*/_interopNamespace(ManagedRuntime);

const makeRuntime = (fetch, config)=>{
    const fetchHttpClient = Layer__namespace.provideMerge(platform.FetchHttpClient.layer, Layer__namespace.succeed(platform.FetchHttpClient.Fetch, fetch));
    const withRedactedHeaders = Layer__namespace.effectDiscard(FiberRef__namespace.update(platform.Headers.currentRedactedNames, (_)=>_.concat([
            "x-uploadthing-api-key"
        ])));
    const layer = Layer__namespace.provide(Layer__namespace.mergeAll(logger_cjs.withLogFormat, logger_cjs.withMinimalLogLevel, fetchHttpClient, withRedactedHeaders), Layer__namespace.setConfigProvider(config_cjs.configProvider(config)));
    return ManagedRuntime__namespace.make(layer);
};

exports.makeRuntime = makeRuntime;
