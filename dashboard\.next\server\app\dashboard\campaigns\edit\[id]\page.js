(()=>{var e={};e.id=24,e.ids=[24],e.modules={1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5486:e=>{"use strict";e.exports=require("bcrypt")},7066:e=>{"use strict";e.exports=require("node:tty")},8819:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},15616:(e,t,s)=>{"use strict";s.d(t,{T:()=>n});var a=s(60687),r=s(43210),i=s(96241);let n=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:s,...t}));n.displayName="Textarea"},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26269:(e,t,s)=>{"use strict";s.d(t,{Tabs:()=>T,TabsContent:()=>P,TabsList:()=>q,TabsTrigger:()=>D});var a=s(60687),r=s(43210),i=s(70569),n=s(11273),o=s(72942),l=s(46059),d=s(14163),c=s(43),u=s(65551),p=s(96963),m="Tabs",[h,f]=(0,n.A)(m,[o.RG]),x=(0,o.RG)(),[g,y]=h(m),v=r.forwardRef((e,t)=>{let{__scopeTabs:s,value:r,onValueChange:i,defaultValue:n,orientation:o="horizontal",dir:l,activationMode:m="automatic",...h}=e,f=(0,c.jH)(l),[x,y]=(0,u.i)({prop:r,onChange:i,defaultProp:n});return(0,a.jsx)(g,{scope:s,baseId:(0,p.B)(),value:x,onValueChange:y,orientation:o,dir:f,activationMode:m,children:(0,a.jsx)(d.sG.div,{dir:f,"data-orientation":o,...h,ref:t})})});v.displayName=m;var j="TabsList",b=r.forwardRef((e,t)=>{let{__scopeTabs:s,loop:r=!0,...i}=e,n=y(j,s),l=x(s);return(0,a.jsx)(o.bL,{asChild:!0,...l,orientation:n.orientation,dir:n.dir,loop:r,children:(0,a.jsx)(d.sG.div,{role:"tablist","aria-orientation":n.orientation,...i,ref:t})})});b.displayName=j;var w="TabsTrigger",N=r.forwardRef((e,t)=>{let{__scopeTabs:s,value:r,disabled:n=!1,...l}=e,c=y(w,s),u=x(s),p=k(c.baseId,r),m=E(c.baseId,r),h=r===c.value;return(0,a.jsx)(o.q7,{asChild:!0,...u,focusable:!n,active:h,children:(0,a.jsx)(d.sG.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":m,"data-state":h?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:p,...l,ref:t,onMouseDown:(0,i.m)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(r)}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(r)}),onFocus:(0,i.m)(e.onFocus,()=>{let e="manual"!==c.activationMode;h||n||!e||c.onValueChange(r)})})})});N.displayName=w;var C="TabsContent",A=r.forwardRef((e,t)=>{let{__scopeTabs:s,value:i,forceMount:n,children:o,...c}=e,u=y(C,s),p=k(u.baseId,i),m=E(u.baseId,i),h=i===u.value,f=r.useRef(h);return r.useEffect(()=>{let e=requestAnimationFrame(()=>f.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,a.jsx)(l.C,{present:n||h,children:({present:s})=>(0,a.jsx)(d.sG.div,{"data-state":h?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":p,hidden:!s,id:m,tabIndex:0,...c,ref:t,style:{...e.style,animationDuration:f.current?"0s":void 0},children:s&&o})})});function k(e,t){return`${e}-trigger-${t}`}function E(e,t){return`${e}-content-${t}`}A.displayName=C;var R=s(96241);let T=v,q=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(b,{ref:s,className:(0,R.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));q.displayName=b.displayName;let D=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(N,{ref:s,className:(0,R.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));D.displayName=N.displayName;let P=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(A,{ref:s,className:(0,R.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));P.displayName=A.displayName},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},37826:(e,t,s)=>{"use strict";s.d(t,{Cf:()=>p,Es:()=>h,L3:()=>f,c7:()=>m,lG:()=>l,rr:()=>x,zM:()=>d});var a=s(60687),r=s(43210),i=s(26134),n=s(11860),o=s(96241);let l=i.bL,d=i.l9,c=i.ZL;i.bm;let u=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(i.hJ,{ref:s,className:(0,o.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));u.displayName=i.hJ.displayName;let p=r.forwardRef(({className:e,children:t,...s},r)=>(0,a.jsxs)(c,{children:[(0,a.jsx)(u,{}),(0,a.jsxs)(i.UC,{ref:r,className:(0,o.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...s,children:[t,(0,a.jsxs)(i.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(n.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));p.displayName=i.UC.displayName;let m=({className:e,...t})=>(0,a.jsx)("div",{className:(0,o.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});m.displayName="DialogHeader";let h=({className:e,...t})=>(0,a.jsx)("div",{className:(0,o.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});h.displayName="DialogFooter";let f=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(i.hE,{ref:s,className:(0,o.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));f.displayName=i.hE.displayName;let x=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(i.VY,{ref:s,className:(0,o.cn)("text-sm text-muted-foreground",e),...t}));x.displayName=i.VY.displayName},43649:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},47044:(e,t,s)=>{"use strict";s.d(t,{CampaignEditor:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call CampaignEditor() from the server but CampaignEditor is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\campaigns\\edit\\[id]\\campaign-editor.tsx","CampaignEditor")},48161:e=>{"use strict";e.exports=require("node:os")},51089:(e,t,s)=>{Promise.resolve().then(s.bind(s,71898))},51455:e=>{"use strict";e.exports=require("node:fs/promises")},55192:(e,t,s)=>{"use strict";s.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>n,aR:()=>o,wL:()=>u});var a=s(60687),r=s(43210),i=s(96241);let n=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));n.displayName="Card";let o=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...t}));o.displayName="CardHeader";let l=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle";let d=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let c=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,i.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent";let u=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,i.cn)("flex items-center p-6 pt-0",e),...t}));u.displayName="CardFooter"},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57975:e=>{"use strict";e.exports=require("node:util")},59556:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(51060);class r{constructor(){this.isOfflineMode=!1,this.baseURL="http://localhost:5000/api/v1",this.baseURL||console.warn("NEXT_PUBLIC_BACKEND_API_URL is not defined. API requests may fail."),this.client=a.A.create({baseURL:this.baseURL,headers:{"Content-Type":"application/json"},timeout:1e4}),this.client.interceptors.request.use(e=>e,e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,e=>(e.response?(console.error("API Error Response:",e.response.status,e.response.data),e.response.data&&0!==Object.keys(e.response.data).length||(e.response.data={error:`Request failed with status code ${e.response.status}`}),404===e.response.status&&(console.log("Resource not found:",e.config?.url),e.response.data={error:"The requested resource was not found. Please refresh and try again."}),e.responseData=e.response.data):e.request?(console.error("API No Response:",e.request),e.message&&(e.message.includes("Network Error")||e.message.includes("timeout"))?(console.error("Network Error detected. Server might be down or unreachable."),this.setOfflineMode(!0),e.isOffline=!0,e.responseData={error:"Network Error: Unable to connect to the server. Please check your connection or try again later.",isOffline:!0},console.warn("API Client: Server connection failed. If you're running in development mode, make sure your backend server is running."),window.dispatchEvent(new CustomEvent("api:offline",{detail:{isOffline:!0}}))):e.responseData={error:"No response received from server. Please check your connection."}):(console.error("API Request Error:",e.message),e.responseData={error:e.message||"An unexpected error occurred"}),Promise.reject(e)))}async get(e,t){try{if(this.isOfflineMode&&!e.includes("/health"))throw console.log(`API Client: In offline mode, skipping GET request to ${e}`),Error("Network Error: Application is in offline mode");let s=await this.client.get(e,t);return this.isOfflineMode&&this.setOfflineMode(!1),s.data}catch(e){throw e}}async post(e,t,s){return(await this.client.post(e,t,s)).data}async put(e,t,s){return(await this.client.put(e,t,s)).data}async delete(e,t){try{console.log(`Making DELETE request to: ${e}`);let s=await this.client.delete(e,t);if(204===s.status)return console.log(`DELETE request to ${e} successful with 204 status`),null;return s.data}catch(t){throw console.error(`DELETE request to ${e} failed:`,t),t}}async patch(e,t,s){return(await this.client.patch(e,t,s)).data}async upload(e,t,s){let a={...s,headers:{...s?.headers,"Content-Type":"multipart/form-data"}};return(await this.client.post(e,t,a)).data}getBaseURL(){return this.baseURL}isInOfflineMode(){return this.isOfflineMode}setOfflineMode(e){this.isOfflineMode!==e&&(console.log(`API Client: Setting offline mode to ${e}`),this.isOfflineMode=e,window.dispatchEvent(new CustomEvent("api:offline",{detail:{isOffline:e}})))}async tryReconnect(){try{return console.log("API Client: Attempting to reconnect to the server..."),await this.client.get("/health",{timeout:5e3}),console.log("API Client: Reconnection successful"),this.setOfflineMode(!1),!0}catch(e){return console.log("API Client: Reconnection failed, still in offline mode"),!1}}}let i=new r},60675:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var a=s(37413),r=s(47044);function i({params:e}){return(0,a.jsxs)("div",{className:"container mx-auto py-6 px-4",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-1",children:"Edit Campaign"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-6",children:"Update your campaign details and settings"}),(0,a.jsx)(r.CampaignEditor,{campaignId:e.id})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63974:(e,t,s)=>{"use strict";s.d(t,{bq:()=>p,eb:()=>x,gC:()=>f,l6:()=>c,yv:()=>u});var a=s(60687),r=s(43210),i=s(22670),n=s(78272),o=s(3589),l=s(13964),d=s(96241);let c=i.bL;i.YJ;let u=i.WT,p=r.forwardRef(({className:e,children:t,...s},r)=>(0,a.jsxs)(i.l9,{ref:r,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...s,children:[t,(0,a.jsx)(i.In,{asChild:!0,children:(0,a.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]}));p.displayName=i.l9.displayName;let m=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(i.PP,{ref:s,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(o.A,{className:"h-4 w-4"})}));m.displayName=i.PP.displayName;let h=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(i.wn,{ref:s,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(n.A,{className:"h-4 w-4"})}));h.displayName=i.wn.displayName;let f=r.forwardRef(({className:e,children:t,position:s="popper",...r},n)=>(0,a.jsx)(i.ZL,{children:(0,a.jsxs)(i.UC,{ref:n,className:(0,d.cn)("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...r,children:[(0,a.jsx)(m,{}),(0,a.jsx)(i.LM,{className:(0,d.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,a.jsx)(h,{})]})}));f.displayName=i.UC.displayName,r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(i.JU,{ref:s,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=i.JU.displayName;let x=r.forwardRef(({className:e,children:t,...s},r)=>(0,a.jsxs)(i.q7,{ref:r,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(i.VF,{children:(0,a.jsx)(l.A,{className:"h-4 w-4"})})}),(0,a.jsx)(i.p4,{children:t})]}));x.displayName=i.q7.displayName,r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(i.wv,{ref:s,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=i.wv.displayName},68988:(e,t,s)=>{"use strict";s.d(t,{p:()=>n});var a=s(60687),r=s(43210),i=s(96241);let n=r.forwardRef(({className:e,type:t,...s},r)=>(0,a.jsx)("input",{type:t,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:r,...s}));n.displayName="Input"},71898:(e,t,s)=>{"use strict";s.d(t,{CampaignEditor:()=>D});var a=s(60687),r=s(43210),i=s(16189),n=s(70333),o=s(59556),l=s(94792),d=s(24934),c=s(68988),u=s(39390),p=s(15616),m=s(63974),h=s(55192),f=s(26269),x=s(59821),g=s(41862),y=s(43649),v=s(28559),j=s(62688);let b=(0,j.A)("Pause",[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]]),w=(0,j.A)("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]);var N=s(96362),C=s(8819),A=s(27900),k=s(89849);function E({campaignId:e,campaignName:t,onSuccess:s,variant:r="default",size:i="default",...n}){return(0,a.jsx)(k.T,{campaignId:e,campaignName:t,onSuccess:s,children:(0,a.jsxs)(d.$,{variant:r,size:i,...n,children:[(0,a.jsx)(A.A,{className:"mr-2 h-4 w-4"}),"Send Campaign"]})})}var R=s(95033),T=s(85814),q=s.n(T);function D({campaignId:e}){let t=(0,i.useRouter)(),{campaign:s,loading:j,error:A,updateCampaign:k,updateCampaignStatus:T,deleteCampaign:D}=function(e){let[t,s]=(0,r.useState)(null),[a,d]=(0,r.useState)(!0),[c,u]=(0,r.useState)(null),p=(0,i.useRouter)(),{toast:m}=(0,n.dj)();return{campaign:t,loading:a,error:c,fetchCampaign:(0,r.useCallback)(async()=>{if(e)try{d(!0),u(null),console.log(`Fetching campaign with ID: ${e}`);let t=await (0,l.jz)(e);t?(console.log("Campaign data received:",t),s(t)):(u("Campaign not found"),m({title:"Error",description:"Campaign not found",variant:"destructive"}))}catch(t){console.error(`Error fetching campaign with ID ${e}:`,t),u(t.message||"Failed to load campaign details"),m({title:"Error",description:t.message||"Failed to load campaign details",variant:"destructive"})}finally{d(!1)}},[e,m]),updateCampaign:async t=>{if(!e)return!1;try{d(!0),console.log(`Updating campaign with ID: ${e}`,t);let a=await (0,l.SX)(e,t);if(a)return console.log("Campaign updated successfully:",a),s(a),m({title:"Success",description:"Campaign updated successfully"}),!0;throw Error("Failed to update campaign")}catch(t){return console.error(`Error updating campaign with ID ${e}:`,t),m({title:"Error",description:t.message||"Failed to update campaign",variant:"destructive"}),!1}finally{d(!1)}},updateCampaignStatus:async t=>{if(!e)return!1;try{d(!0),console.log(`Updating campaign status with ID: ${e} to ${t}`);let a=await o.A.put(`/marketing/campaigns/${e}/status`,{status:t});if(a)return console.log("Campaign status updated successfully:",a),s(e=>e?{...e,status:t}:null),m({title:"Success",description:`Campaign status updated to ${t}`}),!0;throw Error("Failed to update campaign status")}catch(t){return console.error(`Error updating campaign status with ID ${e}:`,t),m({title:"Error",description:t.message||"Failed to update campaign status",variant:"destructive"}),!1}finally{d(!1)}},deleteCampaign:async()=>{if(!e)return!1;try{return d(!0),console.log(`Deleting campaign with ID: ${e}`),await o.A.delete(`/marketing/campaigns/${e}`),m({title:"Success",description:"Campaign deleted successfully"}),p.push("/dashboard/campaigns"),!0}catch(t){return console.error(`Error deleting campaign with ID ${e}:`,t),m({title:"Error",description:t.message||"Failed to delete campaign",variant:"destructive"}),!1}finally{d(!1)}}}}(e),[P,I]=(0,r.useState)({name:"",type:"",content:"",clientTypes:[],scheduledAt:""}),[L,$]=(0,r.useState)(!1),[M,S]=(0,r.useState)(!1),U=e=>{let{name:t,value:s}=e.target;I(e=>({...e,[t]:s}))},z=(e,t)=>{I(s=>({...s,[e]:t}))},F=async e=>{e.preventDefault(),$(!0);try{let e={...P,scheduledAt:P.scheduledAt?new Date(P.scheduledAt).toISOString():void 0};await k(e)}catch(e){console.error("Error updating campaign:",e)}finally{$(!1)}},_=async e=>{S(!0),await T(e),S(!1)},O=async()=>{await D()};return j?(0,a.jsxs)("div",{className:"flex justify-center items-center py-12",children:[(0,a.jsx)(g.A,{className:"h-8 w-8 animate-spin text-primary"}),(0,a.jsx)("span",{className:"ml-2",children:"Loading campaign details..."})]}):A?(0,a.jsxs)(h.Zp,{className:"border-destructive",children:[(0,a.jsx)(h.aR,{children:(0,a.jsxs)(h.ZB,{className:"flex items-center text-destructive",children:[(0,a.jsx)(y.A,{className:"mr-2 h-5 w-5"}),"Error Loading Campaign"]})}),(0,a.jsxs)(h.Wu,{children:[(0,a.jsx)("p",{children:A}),(0,a.jsx)("p",{className:"mt-2",children:"The campaign might have been deleted or you don't have permission to view it."})]}),(0,a.jsx)(h.wL,{children:(0,a.jsxs)(d.$,{variant:"outline",onClick:()=>t.push("/dashboard/campaigns"),children:[(0,a.jsx)(v.A,{className:"mr-2 h-4 w-4"}),"Back to Campaigns"]})})]}):s?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(q(),{href:"/dashboard/campaigns",children:(0,a.jsxs)(d.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(v.A,{className:"mr-2 h-4 w-4"}),"Back"]})}),(0,a.jsx)(x.E,{variant:"active"===s.status.toLowerCase()?"default":"completed"===s.status.toLowerCase()?"secondary":"scheduled"===s.status.toLowerCase()?"outline":"destructive",children:s.status})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(E,{campaignId:e,campaignName:s.name,variant:"outline",onSuccess:()=>{setTimeout(()=>{t.refresh()},1e3)}}),"active"===s.status.toLowerCase()?(0,a.jsxs)(d.$,{variant:"outline",onClick:()=>_("paused"),disabled:M,children:[M?(0,a.jsx)(g.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,a.jsx)(b,{className:"mr-2 h-4 w-4"}),"Pause Campaign"]}):"paused"===s.status.toLowerCase()?(0,a.jsxs)(d.$,{variant:"outline",onClick:()=>_("active"),disabled:M,children:[M?(0,a.jsx)(g.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,a.jsx)(w,{className:"mr-2 h-4 w-4"}),"Resume Campaign"]}):null,(0,a.jsxs)(R.Lt,{children:[(0,a.jsx)(R.tv,{asChild:!0,children:(0,a.jsxs)(d.$,{variant:"destructive",children:[(0,a.jsx)(N.A,{className:"mr-2 h-4 w-4"}),"Delete"]})}),(0,a.jsxs)(R.EO,{children:[(0,a.jsxs)(R.wd,{children:[(0,a.jsx)(R.r7,{children:"Are you sure?"}),(0,a.jsx)(R.$v,{children:"This action cannot be undone. This will permanently delete the campaign and all associated data."})]}),(0,a.jsxs)(R.ck,{children:[(0,a.jsx)(R.Zr,{children:"Cancel"}),(0,a.jsx)(R.Rx,{onClick:O,children:"Delete"})]})]})]})]})]}),(0,a.jsxs)(f.Tabs,{defaultValue:"details",children:[(0,a.jsxs)(f.TabsList,{children:[(0,a.jsx)(f.TabsTrigger,{value:"details",children:"Campaign Details"}),(0,a.jsx)(f.TabsTrigger,{value:"content",children:"Message Content"}),(0,a.jsx)(f.TabsTrigger,{value:"audience",children:"Audience"}),(0,a.jsx)(f.TabsTrigger,{value:"schedule",children:"Schedule"})]}),(0,a.jsxs)("form",{onSubmit:F,children:[(0,a.jsx)(f.TabsContent,{value:"details",className:"space-y-4 mt-4",children:(0,a.jsxs)(h.Zp,{children:[(0,a.jsx)(h.aR,{children:(0,a.jsx)(h.ZB,{children:"Campaign Details"})}),(0,a.jsxs)(h.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{htmlFor:"name",children:"Campaign Name"}),(0,a.jsx)(c.p,{id:"name",name:"name",value:P.name,onChange:U,placeholder:"Enter campaign name"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{htmlFor:"type",children:"Campaign Type"}),(0,a.jsxs)(m.l6,{value:P.type,onValueChange:e=>z("type",e),children:[(0,a.jsx)(m.bq,{id:"type",children:(0,a.jsx)(m.yv,{placeholder:"Select campaign type"})}),(0,a.jsxs)(m.gC,{children:[(0,a.jsx)(m.eb,{value:"whatsapp",children:"WhatsApp Template"}),(0,a.jsx)(m.eb,{value:"connect",children:"Connect With Us"}),(0,a.jsx)(m.eb,{value:"promotional",children:"Promotional"}),(0,a.jsx)(m.eb,{value:"informational",children:"Informational"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{children:"Created At"}),(0,a.jsx)("div",{className:"p-2 border rounded-md bg-muted/50",children:new Date(s.createdAt).toLocaleString()})]})]})]})}),(0,a.jsx)(f.TabsContent,{value:"content",className:"space-y-4 mt-4",children:(0,a.jsxs)(h.Zp,{children:[(0,a.jsx)(h.aR,{children:(0,a.jsx)(h.ZB,{children:"Message Content"})}),(0,a.jsx)(h.Wu,{children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{htmlFor:"content",children:"Message Template"}),(0,a.jsx)(p.T,{id:"content",name:"content",value:P.content,onChange:U,placeholder:"Enter your campaign message",rows:8}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Use ",{variable}," syntax for personalization variables."]})]})})]})}),(0,a.jsx)(f.TabsContent,{value:"audience",className:"space-y-4 mt-4",children:(0,a.jsxs)(h.Zp,{children:[(0,a.jsx)(h.aR,{children:(0,a.jsx)(h.ZB,{children:"Audience Selection"})}),(0,a.jsxs)(h.Wu,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"This campaign is currently targeting the following client types:"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:P.clientTypes.length>0?P.clientTypes.map((e,t)=>(0,a.jsx)(x.E,{variant:"secondary",children:e},t)):(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"No client types selected"})}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground mt-4",children:"Note: Changing audience selection for an active campaign may affect its delivery."})]})]})}),(0,a.jsx)(f.TabsContent,{value:"schedule",className:"space-y-4 mt-4",children:(0,a.jsxs)(h.Zp,{children:[(0,a.jsx)(h.aR,{children:(0,a.jsx)(h.ZB,{children:"Schedule"})}),(0,a.jsx)(h.Wu,{children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(u.J,{htmlFor:"scheduledAt",children:"Scheduled Date and Time"}),(0,a.jsx)(c.p,{id:"scheduledAt",name:"scheduledAt",type:"datetime-local",value:P.scheduledAt,onChange:U}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Leave empty for immediate sending when activated."})]})})]})}),(0,a.jsx)("div",{className:"mt-6 flex justify-end",children:(0,a.jsx)(d.$,{type:"submit",disabled:L,children:L?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Saving..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(C.A,{className:"mr-2 h-4 w-4"}),"Save Changes"]})})})]})]})]}):(0,a.jsxs)(h.Zp,{className:"border-destructive",children:[(0,a.jsx)(h.aR,{children:(0,a.jsxs)(h.ZB,{className:"flex items-center text-destructive",children:[(0,a.jsx)(y.A,{className:"mr-2 h-5 w-5"}),"Campaign Not Found"]})}),(0,a.jsx)(h.Wu,{children:(0,a.jsxs)("p",{children:["The campaign with ID ",e," could not be found."]})}),(0,a.jsx)(h.wL,{children:(0,a.jsxs)(d.$,{variant:"outline",onClick:()=>t.push("/dashboard/campaigns"),children:[(0,a.jsx)(v.A,{className:"mr-2 h-4 w-4"}),"Back to Campaigns"]})})]})}},73024:e=>{"use strict";e.exports=require("node:fs")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83840:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var a=s(65239),r=s(48088),i=s(88170),n=s.n(i),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let d={children:["",{children:["dashboard",{children:["campaigns",{children:["edit",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,60675)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\campaigns\\edit\\[id]\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,83249)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,60566)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\campaigns\\edit\\[id]\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/dashboard/campaigns/edit/[id]/page",pathname:"/dashboard/campaigns/edit/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},83997:e=>{"use strict";e.exports=require("tty")},85937:(e,t,s)=>{Promise.resolve().then(s.bind(s,47044))},94735:e=>{"use strict";e.exports=require("events")},94792:(e,t,s)=>{"use strict";s.d(t,{ME:()=>r,SX:()=>n,jz:()=>i});var a=s(59556);let r=async()=>{try{return await a.A.get("/marketing/campaigns")}catch(e){throw console.error("Error fetching campaigns:",e),e}},i=async e=>{try{return await a.A.get(`/marketing/campaigns/${e}`)}catch(t){throw console.error(`Error fetching campaign with ID ${e}:`,t),t}},n=async(e,t)=>{try{return await a.A.put(`/marketing/campaigns/${e}`,t)}catch(t){throw console.error(`Error updating campaign with ID ${e}:`,t),t}}},96330:e=>{"use strict";e.exports=require("@prisma/client")},96362:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("Trash",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]])}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[7719,9656,2190,3903,5153,3555,4097,1060,9067,8722,9464,381,8017],()=>s(83840));module.exports=a})();