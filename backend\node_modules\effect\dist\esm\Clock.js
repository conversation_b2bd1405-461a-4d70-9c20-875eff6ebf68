import * as internal from "./internal/clock.js";
import * as defaultServices from "./internal/defaultServices.js";
/**
 * @since 2.0.0
 * @category symbols
 */
export const ClockTypeId = internal.ClockTypeId;
/**
 * @since 2.0.0
 * @category constructors
 */
export const make = internal.make;
/**
 * @since 2.0.0
 * @category constructors
 */
export const sleep = defaultServices.sleep;
/**
 * @since 2.0.0
 * @category constructors
 */
export const currentTimeMillis = defaultServices.currentTimeMillis;
/**
 * @since 2.0.0
 * @category constructors
 */
export const currentTimeNanos = defaultServices.currentTimeNanos;
/**
 * @since 2.0.0
 * @category constructors
 */
export const clockWith = defaultServices.clockWith;
/**
 * @since 2.0.0
 * @category context
 */
export const Clock = internal.clockTag;
//# sourceMappingURL=Clock.js.map