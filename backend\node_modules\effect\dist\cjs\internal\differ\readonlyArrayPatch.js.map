{"version": 3, "file": "readonlyArrayPatch.js", "names": ["Arr", "_interopRequireWildcard", "require", "Equal", "Dual", "Data", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "ReadonlyArrayPatchTypeId", "exports", "Symbol", "for", "variance", "PatchProto", "Structural", "prototype", "_Value", "_Patch", "EmptyProto", "assign", "create", "_tag", "_empty", "empty", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "makeAndThen", "first", "second", "o", "AppendProto", "makeAppend", "values", "SliceProto", "makeSlice", "from", "until", "UpdateProto", "makeUpdate", "index", "patch", "diff", "options", "oldValue", "length", "newValue", "oldElement", "newElement", "valuePatch", "differ", "equals", "combine", "drop", "dual", "self", "that", "readon<PERSON><PERSON><PERSON><PERSON>", "slice", "patches", "of", "isNonEmptyArray", "head", "headNonEmpty", "tail", "tailNonEmpty", "unshift", "value", "push"], "sources": ["../../../../src/internal/differ/readonlyArrayPatch.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA,IAAAA,GAAA,GAAAC,uBAAA,CAAAC,OAAA;AAEA,IAAAC,KAAA,GAAAF,uBAAA,CAAAC,OAAA;AACA,IAAAE,IAAA,GAAAH,uBAAA,CAAAC,OAAA;AACA,IAAAG,IAAA,GAAAJ,uBAAA,CAAAC,OAAA;AAAkC,SAAAI,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAN,wBAAAM,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAElC;AACO,MAAMW,wBAAwB,GAAAC,OAAA,CAAAD,wBAAA,gBAAuCE,MAAM,CAACC,GAAG,CACpF,iCAAiC,CACI;AAEvC,SAASC,QAAQA,CAAOb,CAAI;EAC1B,OAAOA,CAAiB;AAC1B;AAEA,MAAMc,UAAU,GAAG;EACjB,GAAG1B,IAAI,CAAC2B,UAAU,CAACC,SAAS;EAC5B,CAACP,wBAAwB,GAAG;IAC1BQ,MAAM,EAAEJ,QAAQ;IAChBK,MAAM,EAAEL;;CAEX;AAMD,MAAMM,UAAU,gBAAGlB,MAAM,CAACmB,MAAM,eAACnB,MAAM,CAACoB,MAAM,CAACP,UAAU,CAAC,EAAE;EAC1DQ,IAAI,EAAE;CACP,CAAC;AAEF,MAAMC,MAAM,gBAAGtB,MAAM,CAACoB,MAAM,CAACF,UAAU,CAAC;AAExC;;;AAGO,MAAMK,KAAK,GAAGA,CAAA,KAAqED,MAAM;AAAAb,OAAA,CAAAc,KAAA,GAAAA,KAAA;AAQhG,MAAMC,YAAY,gBAAGxB,MAAM,CAACmB,MAAM,eAACnB,MAAM,CAACoB,MAAM,CAACP,UAAU,CAAC,EAAE;EAC5DQ,IAAI,EAAE;CACP,CAAC;AAEF,MAAMI,WAAW,GAAGA,CAClBC,KAAsD,EACtDC,MAAuD,KACJ;EACnD,MAAMC,CAAC,GAAG5B,MAAM,CAACoB,MAAM,CAACI,YAAY,CAAC;EACrCI,CAAC,CAACF,KAAK,GAAGA,KAAK;EACfE,CAAC,CAACD,MAAM,GAAGA,MAAM;EACjB,OAAOC,CAAC;AACV,CAAC;AAOD,MAAMC,WAAW,gBAAG7B,MAAM,CAACmB,MAAM,eAACnB,MAAM,CAACoB,MAAM,CAACP,UAAU,CAAC,EAAE;EAC3DQ,IAAI,EAAE;CACP,CAAC;AAEF,MAAMS,UAAU,GAAkBC,MAA4B,IAAqD;EACjH,MAAMH,CAAC,GAAG5B,MAAM,CAACoB,MAAM,CAACS,WAAW,CAAC;EACpCD,CAAC,CAACG,MAAM,GAAGA,MAAM;EACjB,OAAOH,CAAC;AACV,CAAC;AAQD,MAAMI,UAAU,gBAAGhC,MAAM,CAACmB,MAAM,eAACnB,MAAM,CAACoB,MAAM,CAACP,UAAU,CAAC,EAAE;EAC1DQ,IAAI,EAAE;CACP,CAAC;AAEF,MAAMY,SAAS,GAAGA,CAAeC,IAAY,EAAEC,KAAa,KAAqD;EAC/G,MAAMP,CAAC,GAAG5B,MAAM,CAACoB,MAAM,CAACY,UAAU,CAAC;EACnCJ,CAAC,CAACM,IAAI,GAAGA,IAAI;EACbN,CAAC,CAACO,KAAK,GAAGA,KAAK;EACf,OAAOP,CAAC;AACV,CAAC;AAQD,MAAMQ,WAAW,gBAAGpC,MAAM,CAACmB,MAAM,eAACnB,MAAM,CAACoB,MAAM,CAACP,UAAU,CAAC,EAAE;EAC3DQ,IAAI,EAAE;CACP,CAAC;AAEF,MAAMgB,UAAU,GAAGA,CAAeC,KAAa,EAAEC,KAAY,KAAqD;EAChH,MAAMX,CAAC,GAAG5B,MAAM,CAACoB,MAAM,CAACgB,WAAW,CAAC;EACpCR,CAAC,CAACU,KAAK,GAAGA,KAAK;EACfV,CAAC,CAACW,KAAK,GAAGA,KAAK;EACf,OAAOX,CAAC;AACV,CAAC;AASD;AACO,MAAMY,IAAI,GACfC,OAIC,IACkD;EACnD,IAAInC,CAAC,GAAG,CAAC;EACT,IAAIiC,KAAK,GAAGhB,KAAK,EAAgB;EACjC,OAAOjB,CAAC,GAAGmC,OAAO,CAACC,QAAQ,CAACC,MAAM,IAAIrC,CAAC,GAAGmC,OAAO,CAACG,QAAQ,CAACD,MAAM,EAAE;IACjE,MAAME,UAAU,GAAGJ,OAAO,CAACC,QAAQ,CAACpC,CAAC,CAAE;IACvC,MAAMwC,UAAU,GAAGL,OAAO,CAACG,QAAQ,CAACtC,CAAC,CAAE;IACvC,MAAMyC,UAAU,GAAGN,OAAO,CAACO,MAAM,CAACR,IAAI,CAACK,UAAU,EAAEC,UAAU,CAAC;IAC9D,IAAI,CAAC7D,KAAK,CAACgE,MAAM,CAACF,UAAU,EAAEN,OAAO,CAACO,MAAM,CAACzB,KAAK,CAAC,EAAE;MACnDgB,KAAK,GAAGW,OAAO,CAACX,KAAK,EAAEF,UAAU,CAAC/B,CAAC,EAAEyC,UAAU,CAAC,CAAC;IACnD;IACAzC,CAAC,GAAGA,CAAC,GAAG,CAAC;EACX;EACA,IAAIA,CAAC,GAAGmC,OAAO,CAACC,QAAQ,CAACC,MAAM,EAAE;IAC/BJ,KAAK,GAAGW,OAAO,CAACX,KAAK,EAAEN,SAAS,CAAC,CAAC,EAAE3B,CAAC,CAAC,CAAC;EACzC;EACA,IAAIA,CAAC,GAAGmC,OAAO,CAACG,QAAQ,CAACD,MAAM,EAAE;IAC/BJ,KAAK,GAAGW,OAAO,CAACX,KAAK,EAAET,UAAU,CAAChD,GAAG,CAACqE,IAAI,CAAC7C,CAAC,CAAC,CAACmC,OAAO,CAACG,QAAQ,CAAC,CAAC,CAAC;EACnE;EACA,OAAOL,KAAK;AACd,CAAC;AAED;AAAA9B,OAAA,CAAA+B,IAAA,GAAAA,IAAA;AACO,MAAMU,OAAO,GAAAzC,OAAA,CAAAyC,OAAA,gBAAGhE,IAAI,CAACkE,IAAI,CAU9B,CAAC,EAAE,CAACC,IAAI,EAAEC,IAAI,KAAK7B,WAAW,CAAC4B,IAAI,EAAEC,IAAI,CAAC,CAAC;AAE7C;AACO,MAAMf,KAAK,GAAA9B,OAAA,CAAA8B,KAAA,gBAAGrD,IAAI,CAACkE,IAAI,CAU5B,CAAC,EAAE,CACHC,IAAqD,EACrDX,QAA8B,EAC9BM,MAAmC,KACjC;EACF,IAAKK,IAAoB,CAAChC,IAAI,KAAK,OAAO,EAAE;IAC1C,OAAOqB,QAAQ;EACjB;EACA,IAAIa,aAAa,GAAGb,QAAQ,CAACc,KAAK,EAAE;EACpC,IAAIC,OAAO,GAA2D3E,GAAG,CAAC4E,EAAE,CAACL,IAAI,CAAC;EAClF,OAAOvE,GAAG,CAAC6E,eAAe,CAACF,OAAO,CAAC,EAAE;IACnC,MAAMG,IAAI,GAAgB9E,GAAG,CAAC+E,YAAY,CAACJ,OAAO,CAAgB;IAClE,MAAMK,IAAI,GAAGhF,GAAG,CAACiF,YAAY,CAACN,OAAO,CAAC;IACtC,QAAQG,IAAI,CAACvC,IAAI;MACf,KAAK,OAAO;QAAE;UACZoC,OAAO,GAAGK,IAAI;UACd;QACF;MACA,KAAK,SAAS;QAAE;UACdA,IAAI,CAACE,OAAO,CAACJ,IAAI,CAAClC,KAAK,EAAEkC,IAAI,CAACjC,MAAM,CAAC;UACrC8B,OAAO,GAAGK,IAAI;UACd;QACF;MACA,KAAK,QAAQ;QAAE;UACb,KAAK,MAAMG,KAAK,IAAIL,IAAI,CAAC7B,MAAM,EAAE;YAC/BwB,aAAa,CAACW,IAAI,CAACD,KAAK,CAAC;UAC3B;UACAR,OAAO,GAAGK,IAAI;UACd;QACF;MACA,KAAK,OAAO;QAAE;UACZP,aAAa,GAAGA,aAAa,CAACC,KAAK,CAACI,IAAI,CAAC1B,IAAI,EAAE0B,IAAI,CAACzB,KAAK,CAAC;UAC1DsB,OAAO,GAAGK,IAAI;UACd;QACF;MACA,KAAK,QAAQ;QAAE;UACbP,aAAa,CAACK,IAAI,CAACtB,KAAK,CAAC,GAAGU,MAAM,CAACT,KAAK,CAACqB,IAAI,CAACrB,KAAK,EAAEgB,aAAa,CAACK,IAAI,CAACtB,KAAK,CAAE,CAAC;UAChFmB,OAAO,GAAGK,IAAI;UACd;QACF;IACF;EACF;EACA,OAAOP,aAAa;AACtB,CAAC,CAAC", "ignoreList": []}