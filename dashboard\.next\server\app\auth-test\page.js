(()=>{var e={};e.id=5692,e.ids=[5692],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13892:(e,r,t)=>{Promise.resolve().then(t.bind(t,84985))},16189:(e,r,t)=>{"use strict";var s=t(65773);t.o(s,"useParams")&&t.d(r,{useParams:function(){return s.useParams}}),t.o(s,"usePathname")&&t.d(r,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65328:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>p,tree:()=>l});var s=t(65239),o=t(48088),a=t(88170),n=t.n(a),d=t(30893),i={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>d[e]);t.d(r,i);let l={children:["",{children:["auth-test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,86031)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\auth-test\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,60566)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\auth-test\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/auth-test/page",pathname:"/auth-test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},84985:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var s=t(60687),o=t(43210),a=t(99208),n=t(16189);function d(){let{data:e,status:r}=(0,a.wV)(),[t,d]=(0,o.useState)("<EMAIL>"),[i,l]=(0,o.useState)("password123"),[u,c]=(0,o.useState)(!1),[p,m]=(0,o.useState)(""),h=(0,n.useRouter)(),x=async e=>{e.preventDefault(),c(!0),m("");try{console.log("Attempting to sign in with:",{email:t,password:i});let e=await (0,a.Jv)("credentials",{email:t,password:i,redirect:!1});console.log("Sign in result:",e),e?.error?m(`Error: ${e.error}`):(m("Login successful! Refreshing session..."),setTimeout(()=>{window.location.reload()},1e3))}catch(e){console.error("Login error:",e),m("An unexpected error occurred")}finally{c(!1)}},b=async()=>{c(!0),m("");try{await (0,a.CI)({redirect:!1}),m("Logout successful! Refreshing session..."),setTimeout(()=>{window.location.reload()},1e3)}catch(e){console.error("Logout error:",e),m("An unexpected error occurred during logout")}finally{c(!1)}};return(0,s.jsx)("div",{className:"flex min-h-screen flex-col items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"w-full max-w-md rounded-lg border p-6 shadow-md",children:[(0,s.jsx)("h1",{className:"mb-6 text-2xl font-bold",children:"Authentication Test Page"}),(0,s.jsxs)("div",{className:"mb-4 rounded-md bg-gray-50 p-4",children:[(0,s.jsxs)("h2",{className:"font-bold",children:["Session Status: ",r]}),e&&(0,s.jsxs)("div",{className:"mt-2",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"User:"})," ",e.user?.name||e.user?.email]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Email:"})," ",e.user?.email]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Role:"})," ",e.user?.role||"Not available"]})]})]}),p&&(0,s.jsx)("div",{className:"mb-4 rounded-md bg-blue-50 p-4 text-blue-700",children:p}),"authenticated"===r?(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("button",{onClick:b,disabled:u,className:"w-full rounded-md bg-red-600 px-4 py-2 text-white hover:bg-red-700 disabled:opacity-50",children:u?"Logging out...":"Logout"}),(0,s.jsx)("button",{onClick:()=>{h.push("/dashboard/analytics")},className:"w-full rounded-md bg-green-600 px-4 py-2 text-white hover:bg-green-700",children:"Go to Dashboard"})]}):(0,s.jsxs)("form",{onSubmit:x,className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium",children:"Email"}),(0,s.jsx)("input",{id:"email",type:"email",value:t,onChange:e=>d(e.target.value),className:"mt-1 block w-full rounded-md border border-gray-300 p-2",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium",children:"Password"}),(0,s.jsx)("input",{id:"password",type:"password",value:i,onChange:e=>l(e.target.value),className:"mt-1 block w-full rounded-md border border-gray-300 p-2",required:!0})]}),(0,s.jsx)("button",{type:"submit",disabled:u,className:"w-full rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700 disabled:opacity-50",children:u?"Logging in...":"Login"})]})]})})}},86031:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\auth-test\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\auth-test\\page.tsx","default")},89924:(e,r,t)=>{Promise.resolve().then(t.bind(t,86031))}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7719,9656,8722],()=>t(65328));module.exports=s})();