"use strict";exports.id=4775,exports.ids=[4775],exports.modules={8819:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},16023:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},28559:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},35308:(e,t,n)=>{let r,a;n.d(t,{Jt:()=>rh,Wi:()=>rg});var s=n(60687);let i=function(e,t){if("function"==typeof e)return function(){return e(arguments)?t.apply(this,arguments):e=>t(e,...arguments)};switch(e){case 0:case 1:throw RangeError(`Invalid arity ${e}`);case 2:return function(e,n){return arguments.length>=2?t(e,n):function(n){return t(n,e)}};case 3:return function(e,n,r){return arguments.length>=3?t(e,n,r):function(r){return t(r,e,n)}};case 4:return function(e,n,r,a){return arguments.length>=4?t(e,n,r,a):function(a){return t(a,e,n,r)}};case 5:return function(e,n,r,a,s){return arguments.length>=5?t(e,n,r,a,s):function(s){return t(s,e,n,r,a)}};default:return function(){if(arguments.length>=e)return t.apply(this,arguments);let n=arguments;return function(e){return t(e,...n)}}}},o=e=>e,u=(a=void 0,()=>a),c="effect/GlobalValue/globalStoreId/3.14.21",l=(e,t)=>(r||(globalThis[c]??=new Map,r=globalThis[c]),r.has(e)||r.set(e,t()),r.get(e)),d=e=>"string"==typeof e,p=e=>"number"==typeof e,f=e=>"function"==typeof e,h=e=>"object"==typeof e&&null!==e,g=e=>h(e)||f(e),m=i(2,(e,t)=>g(e)&&t in e),x=i(2,(e,t)=>m(e,"_tag")&&e._tag===t),v=e=>h(e)&&!Array.isArray(e),b=e=>`BUG: ${e} - please report an issue at https://github.com/Effect-TS/effect/issues`;Symbol.iterator;class y{constructor(e){this.called=!1,this.self=e}next(e){return this.called?{value:e,done:!0}:(this.called=!0,{value:this.self,done:!1})}return(e){return{value:e,done:!0}}throw(e){throw e}[Symbol.iterator](){return new y(this.self)}}let w=Symbol.for("effect/Utils/YieldWrap");class _{#e;constructor(e){this.#e=e}[w](){return this.#e}}let j=l("effect/Utils/isStructuralRegion",()=>({enabled:!1,tester:void 0})),k={effect_internal_function:e=>e()};k.effect_internal_function(()=>Error().stack)?.includes("effect_internal_function")===!0&&k.effect_internal_function;let E=l(Symbol.for("effect/Hash/randomHashCache"),()=>new WeakMap),S=Symbol.for("effect/Hash"),O=e=>{if(!0===j.enabled)return 0;switch(typeof e){case"number":return U(e);case"bigint":return N(e.toString(10));case"boolean":case"symbol":return N(String(e));case"string":return N(e);case"undefined":return N("undefined");case"function":case"object":if(null===e)return N("null");if(e instanceof Date)return O(e.toISOString());if(e instanceof URL)return O(e.href);else if(T(e))return e[S]();else return C(e);default:throw Error(`BUG: unhandled typeof ${typeof e} - please report an issue at https://github.com/Effect-TS/effect/issues`)}},C=e=>(E.has(e)||E.set(e,U(Math.floor(Math.random()*Number.MAX_SAFE_INTEGER))),E.get(e)),R=e=>t=>53*t^e,A=e=>0xbfffffff&e|e>>>1&0x40000000,T=e=>m(e,S),U=e=>{if(e!=e||e===1/0)return 0;let t=0|e;for(t!==e&&(t^=0xffffffff*e);e>0xffffffff;)t^=e/=0xffffffff;return A(t)},N=e=>{let t=5381,n=e.length;for(;n;)t=33*t^e.charCodeAt(--n);return A(t)},I=(e,t)=>{let n=12289;for(let r=0;r<t.length;r++)n^=function(e,t,n,r,a,s,i,o,u){switch(arguments.length){case 1:return e;case 2:return t(e);case 3:return n(t(e));case 4:return r(n(t(e)));case 5:return a(r(n(t(e))));case 6:return s(a(r(n(t(e)))));case 7:return i(s(a(r(n(t(e))))));case 8:return o(i(s(a(r(n(t(e)))))));case 9:return u(o(i(s(a(r(n(t(e))))))));default:{let e=arguments[0];for(let t=1;t<arguments.length;t++)e=arguments[t](e);return e}}}(N(t[r]),R(O(e[t[r]])));return A(n)},M=e=>I(e,Object.keys(e)),L=function(){if(1==arguments.length){let e=arguments[0];return function(t){return Object.defineProperty(e,S,{value:()=>t,enumerable:!1}),t}}let e=arguments[0],t=arguments[1];return Object.defineProperty(e,S,{value:()=>t,enumerable:!1}),t},P=Symbol.for("effect/Equal");function F(){return 1==arguments.length?e=>D(e,arguments[0]):D(arguments[0],arguments[1])}function D(e,t){if(e===t)return!0;let n=typeof e;if(n!==typeof t)return!1;if("object"===n||"function"===n){if(null!==e&&null!==t){if($(e)&&$(t))return!!(O(e)===O(t)&&e[P](t))||!!j.enabled&&!!j.tester&&j.tester(e,t);if(e instanceof Date&&t instanceof Date)return e.toISOString()===t.toISOString();if(e instanceof URL&&t instanceof URL)return e.href===t.href}if(j.enabled){if(Array.isArray(e)&&Array.isArray(t))return e.length===t.length&&e.every((e,n)=>D(e,t[n]));if(Object.getPrototypeOf(e)===Object.prototype&&Object.getPrototypeOf(e)===Object.prototype){let n=Object.keys(e),r=Object.keys(t);if(n.length===r.length){for(let r of n)if(!(r in t&&D(e[r],t[r])))return!!j.tester&&j.tester(e,t);return!0}}return!!j.tester&&j.tester(e,t)}}return!!j.enabled&&!!j.tester&&j.tester(e,t)}let $=e=>m(e,P),z=Symbol.for("nodejs.util.inspect.custom"),B=e=>{try{if(m(e,"toJSON")&&f(e.toJSON)&&0===e.toJSON.length)return e.toJSON();if(Array.isArray(e))return e.map(B)}catch(e){return{}}return Y(e)},H=e=>JSON.stringify(e,null,2),V=(e,t=2)=>{if("string"==typeof e)return e;try{return"object"==typeof e?J(e,t):String(e)}catch(t){return String(e)}},J=(e,t)=>{let n=[],r=JSON.stringify(e,(e,t)=>"object"==typeof t&&null!==t?n.includes(t)?void 0:n.push(t)&&(void 0!==W.fiberRefs&&q(t)?t[G](W.fiberRefs):t):t,t);return n=void 0,r},G=Symbol.for("effect/Inspectable/Redactable"),q=e=>"object"==typeof e&&null!==e&&G in e,W=l("effect/Inspectable/redactableState",()=>({fiberRefs:void 0})),Y=e=>q(e)&&void 0!==W.fiberRefs?e[G](W.fiberRefs):e,X=(e,t)=>{switch(t.length){case 0:return e;case 1:return t[0](e);case 2:return t[1](t[0](e));case 3:return t[2](t[1](t[0](e)));case 4:return t[3](t[2](t[1](t[0](e))));case 5:return t[4](t[3](t[2](t[1](t[0](e)))));case 6:return t[5](t[4](t[3](t[2](t[1](t[0](e))))));case 7:return t[6](t[5](t[4](t[3](t[2](t[1](t[0](e)))))));case 8:return t[7](t[6](t[5](t[4](t[3](t[2](t[1](t[0](e))))))));case 9:return t[8](t[7](t[6](t[5](t[4](t[3](t[2](t[1](t[0](e)))))))));default:{let n=e;for(let e=0,r=t.length;e<r;e++)n=t[e](n);return n}}},Z=Symbol.for("effect/Effect"),K=Symbol.for("effect/Stream"),Q=Symbol.for("effect/Sink"),ee=Symbol.for("effect/Channel"),et={_R:e=>e,_E:e=>e,_A:e=>e,_V:"3.14.21"},en={[Z]:et,[K]:et,[Q]:{_A:e=>e,_In:e=>e,_L:e=>e,_E:e=>e,_R:e=>e},[ee]:{_Env:e=>e,_InErr:e=>e,_InElem:e=>e,_InDone:e=>e,_OutErr:e=>e,_OutElem:e=>e,_OutDone:e=>e},[P](e){return this===e},[S](){return L(this,C(this))},[Symbol.iterator](){return new y(new _(this))},pipe(){return X(this,arguments)}},er={[S](){return L(this,M(this))},[P](e){let t=Object.keys(this),n=Object.keys(e);if(t.length!==n.length)return!1;for(let n of t)if(!(n in e&&F(this[n],e[n])))return!1;return!0}},ea=Symbol.for("effect/Option"),es={...en,[ea]:{_A:e=>e},[z](){return this.toJSON()},toString(){return H(this.toJSON())}},ei=Object.assign(Object.create(es),{_tag:"Some",_op:"Some",[P](e){return ec(e)&&ed(e)&&F(this.value,e.value)},[S](){return L(this,R(O(this._tag))(O(this.value)))},toJSON(){return{_id:"Option",_tag:this._tag,value:B(this.value)}}}),eo=O("None"),eu=Object.assign(Object.create(es),{_tag:"None",_op:"None",[P]:e=>ec(e)&&el(e),[S]:()=>eo,toJSON(){return{_id:"Option",_tag:this._tag}}}),ec=e=>m(e,ea),el=e=>"None"===e._tag,ed=e=>"Some"===e._tag,ep=Object.create(eu);Symbol.iterator,()=>ef;let ef={next:()=>({done:!0,value:void 0})};Object.fromEntries,(e,t)=>{let n=[];for(let r of eh(e))n.push(t(r,e[r]));return n};let eh=e=>Object.keys(e),eg=e=>Array.isArray(e)?e:Array.from(e),em=e=>Array.isArray(e)?e:[e],ex=Symbol.for("effect/Context/Tag"),ev=Symbol.for("effect/Context/Reference"),eb=Symbol.for("effect/STM"),ey={...en,_op:"Tag",[eb]:et,[ex]:{_Service:e=>e,_Identifier:e=>e},toString(){return H(this.toJSON())},toJSON(){return{_id:"Tag",key:this.key,stack:this.stack}},[z](){return this.toJSON()},of:e=>e,context(e){return eO(this,e)}},ew={...ey,[ev]:ev},e_=Symbol.for("effect/Context"),ej={[e_]:{_Services:e=>e},[P](e){if(eS(e)&&this.unsafeMap.size===e.unsafeMap.size){for(let t of this.unsafeMap.keys())if(!e.unsafeMap.has(t)||!F(this.unsafeMap.get(t),e.unsafeMap.get(t)))return!1;return!0}return!1},[S](){return L(this,U(this.unsafeMap.size))},pipe(){return X(this,arguments)},toString(){return H(this.toJSON())},toJSON(){return{_id:"Context",services:Array.from(this.unsafeMap).map(B)}},[z](){return this.toJSON()}},ek=e=>{let t=Object.create(ej);return t.unsafeMap=e,t},eE=e=>{let t=Error(`Service not found${e.key?`: ${String(e.key)}`:""}`);if(e.stack){let n=e.stack.split("\n");if(n.length>2){let e=n[2].match(/at (.*)/);e&&(t.message=t.message+` (defined at ${e[1]})`)}}if(t.stack){let e=t.stack.split("\n");e.splice(1,3),t.stack=e.join("\n")}return t},eS=e=>m(e,e_),eO=(e,t)=>ek(new Map([[e.key,t]])),eC=i(3,(e,t,n)=>{let r=new Map(e.unsafeMap);return r.set(t.key,n),ek(r)}),eR=l("effect/Context/defaultValueCache",()=>new Map),eA=e=>{if(eR.has(e.key))return eR.get(e.key);let t=e.defaultValue();return eR.set(e.key,t),t},eT=(e,t)=>e.unsafeMap.has(t.key)?e.unsafeMap.get(t.key):eA(t),eU=i(2,(e,t)=>{if(!e.unsafeMap.has(t.key)){if(ev in t)return eA(t);throw eE(t)}return e.unsafeMap.get(t.key)}),eN=e=>()=>{let t=Error.stackTraceLimit;Error.stackTraceLimit=2;let n=Error();function r(){}return Error.stackTraceLimit=t,Object.setPrototypeOf(r,ey),r.key=e,Object.defineProperty(r,"stack",{get:()=>n.stack}),r},eI=()=>(e,t)=>{let n=Error.stackTraceLimit;Error.stackTraceLimit=2;let r=Error();function a(){}return Error.stackTraceLimit=n,Object.setPrototypeOf(a,ew),a.key=e,a.defaultValue=t.defaultValue,Object.defineProperty(a,"stack",{get:()=>r.stack}),a},eM=Symbol.for("effect/Micro"),eL=Symbol.for("effect/Micro/MicroExit"),eP=e=>"object"==typeof e&&null!==e&&eM in e,eF=Symbol.for("effect/Micro/MicroCause"),eD={_E:o};class e$ extends globalThis.Error{constructor(e,t,n){let r,a,s;let i=`MicroCause.${e}`;if(t instanceof globalThis.Error){r=`(${i}) ${t.name}`;let e=(a=t.message).split("\n").length;s=t.stack?`(${i}) ${t.stack.split("\n").slice(0,e+3).join("\n")}`:`${r}: ${a}`}else r=i,a=V(t,0),s=`${r}: ${a}`;n.length>0&&(s+=`
    ${n.join("\n    ")}`),super(a),this._tag=e,this.traces=n,this[eF]=eD,this.name=r,this.stack=s}pipe(){return X(this,arguments)}toString(){return this.stack}[z](){return this.stack}}class ez extends e${constructor(e,t=[]){super("Fail",e,t),this.error=e}}let eB=(e,t=[])=>new ez(e,t);class eH extends e${constructor(e,t=[]){super("Die",e,t),this.defect=e}}let eV=(e,t=[])=>new eH(e,t);class eJ extends e${constructor(e=[]){super("Interrupt","interrupted",e)}}let eG=(e=[])=>new eJ(e),eq=e=>"Fail"===e._tag,eW=e=>"Interrupt"===e._tag,eY=e=>"Fail"===e._tag?e.error:"Die"===e._tag?e.defect:e,eX=i(2,(e,t)=>{let n=[...e.traces,t];switch(e._tag){case"Die":return eV(e.defect,n);case"Interrupt":return eG(n);case"Fail":return eB(e.error,n)}}),eZ=Symbol.for("effect/Micro/MicroFiber"),eK={_A:o,_E:o};class eQ{constructor(e,t=!0){this._stack=[],this._observers=[],this.currentOpCount=0,this._interrupted=!1,this._yielded=void 0,this.context=e,this.interruptible=t,this[eZ]=eK}getRef(e){return eT(this.context,e)}addObserver(e){return this._exit?(e(this._exit),u):(this._observers.push(e),()=>{let t=this._observers.indexOf(e);t>=0&&this._observers.splice(t,1)})}unsafeInterrupt(){!this._exit&&(this._interrupted=!0,this.interruptible&&this.evaluate(tR))}unsafePoll(){return this._exit}evaluate(e){if(this._exit)return;if(void 0!==this._yielded){let e=this._yielded;this._yielded=void 0,e()}let t=this.runLoop(e);if(t===e8)return;let n=e0.interruptChildren&&e0.interruptChildren(this);if(void 0!==n)return this.evaluate(tk(n,()=>t));this._exit=t;for(let e=0;e<this._observers.length;e++)this._observers[e](t);this._observers.length=0}runLoop(e){let t=!1,n=e;this.currentOpCount=0;try{for(;;){if(this.currentOpCount++,!t&&this.getRef(t$).shouldYield(this)){t=!0;let e=n;n=tk(tc,()=>e)}if((n=n[e4](this))===e8){let e=this._yielded;if(eL in e)return this._yielded=void 0,e;return e8}}}catch(e){if(!m(n,e4))return tA(`MicroFiber.runLoop: Not a valid effect: ${String(n)}`);return tA(e)}}getCont(e){for(;;){let t=this._stack.pop();if(!t)return;let n=t[e7]&&t[e7](this);if(n)return{[e]:n};if(t[e])return t}}yieldWith(e){return this._yielded=e,e8}children(){return this._children??=new Set}}let e0=l("effect/Micro/fiberMiddleware",()=>({interruptChildren:void 0})),e1=e=>tu(()=>{for(let t of e)t.unsafeInterrupt();let t=e[Symbol.iterator](),n=tu(()=>{let e=t.next();for(;!e.done;){if(e.value.unsafePoll()){e=t.next();continue}let r=e.value;return tv(e=>{r.addObserver(t=>{e(n)})})}return tU});return n}),e2=Symbol.for("effect/Micro/identifier"),e3=Symbol.for("effect/Micro/args"),e4=Symbol.for("effect/Micro/evaluate"),e5=Symbol.for("effect/Micro/successCont"),e6=Symbol.for("effect/Micro/failureCont"),e7=Symbol.for("effect/Micro/ensureCont"),e8=Symbol.for("effect/Micro/Yield"),e9={...en,_op:"Micro",[eM]:{_A:o,_E:o,_R:o},pipe(){return X(this,arguments)},[Symbol.iterator](){return new y(new _(this))},toJSON(){return{_id:"Micro",op:this[e2],...e3 in this?{args:this[e3]}:void 0}},toString(){return H(this)},[z](){return H(this)}};function te(e){return tA("Micro.evaluate: Not implemented")}let tt=e=>({...e9,[e2]:e.op,[e4]:e.eval??te,[e5]:e.contA,[e6]:e.contE,[e7]:e.ensure}),tn=e=>{let t=tt(e);return function(){let n=Object.create(t);return n[e3]=!1===e.single?arguments:arguments[0],n}},tr=e=>{let t={...tt(e),[eL]:eL,_tag:e.op,get[e.prop](){return this[e3]},toJSON(){return{_id:"MicroExit",_tag:e.op,[e.prop]:this[e3]}},[P](t){return tO(t)&&t._tag===e.op&&F(this[e3],t[e3])},[S](){return L(this,R(N(e.op))(O(this[e3])))}};return function(e){let n=Object.create(t);return n[e3]=e,n[e5]=void 0,n[e6]=void 0,n[e7]=void 0,n}},ta=tr({op:"Success",prop:"value",eval(e){let t=e.getCont(e5);return t?t[e5](this[e3],e):e.yieldWith(this)}}),ts=tr({op:"Failure",prop:"cause",eval(e){let t=e.getCont(e6);for(;eW(this[e3])&&t&&e.interruptible;)t=e.getCont(e6);return t?t[e6](this[e3],e):e.yieldWith(this)}}),ti=e=>ts(eB(e)),to=tn({op:"Sync",eval(e){let t=this[e3](),n=e.getCont(e5);return n?n[e5](t,e):e.yieldWith(tC(t))}}),tu=tn({op:"Suspend",eval(e){return this[e3]()}}),tc=tn({op:"Yield",eval(e){let t=!1;return e.getRef(t$).scheduleTask(()=>{t||e.evaluate(tU)},this[e3]??0),e.yieldWith(()=>{t=!0})}})(0),tl=e=>tA(e),td=ta(void 0),tp=e=>tu(()=>{try{return ta(e.try())}catch(t){return ti(e.catch(t))}}),tf=e=>tm(function(t,n){e(n).then(e=>t(ta(e)),e=>t(tl(e)))},0!==e.length),th=e=>tm(function(t,n){try{e.try(n).then(e=>t(ta(e)),n=>t(ti(e.catch(n))))}catch(n){t(ti(e.catch(n)))}},0!==e.try.length),tg=tn({op:"WithMicroFiber",eval(e){return this[e3](e)}}),tm=tn({op:"Async",single:!1,eval(e){let t=this[e3][0],n=!1,r=!1,a=this[e3][1]?new AbortController:void 0,s=t(t=>{n||(n=!0,r?e.evaluate(t):r=t)},a?.signal);return!1!==r?r:(r=!0,e._yielded=()=>{n=!0},void 0===a&&void 0===s||e._stack.push(tx(()=>(n=!0,a?.abort(),s??tU))),e8)}}),tx=tn({op:"AsyncFinalizer",ensure(e){e.interruptible&&(e.interruptible=!1,e._stack.push(t1(!0)))},contE(e,t){return eW(e)?tk(this[e3](),()=>ts(e)):ts(e)}}),tv=e=>tm(e,e.length>=2),tb=(...e)=>tu(()=>ty(1===e.length?e[0]():e[1].call(e[0]))),ty=tn({op:"Iterator",contA(e,t){let n=this[e3].next(e);return n.done?ta(n.value):(t._stack.push(this),function(e){if("object"==typeof e&&null!==e&&w in e)return e[w]();throw Error(b("yieldWrapGet"))}(n.value))},eval(e){return this[e5](void 0,e)}}),tw=i(2,(e,t)=>tS(e,e=>t)),t_=i(2,(e,t)=>tk(e,e=>{let n=eP(t)?t:"function"==typeof t?t(e):t;return eP(n)?n:ta(n)})),tj=i(2,(e,t)=>tk(e,e=>{let n=eP(t)?t:"function"==typeof t?t(e):t;return eP(n)?tw(n,e):ta(e)})),tk=i(2,(e,t)=>{let n=Object.create(tE);return n[e3]=e,n[e5]=t,n}),tE=tt({op:"OnSuccess",eval(e){return e._stack.push(this),this[e3]}}),tS=i(2,(e,t)=>tk(e,e=>ta(t(e)))),tO=e=>m(e,eL),tC=ta,tR=ts(eG()),tA=e=>ts(eV(e)),tT=e=>"Failure"===e._tag,tU=tC(void 0),tN="setImmediate"in globalThis?globalThis.setImmediate:e=>setTimeout(e,0);class tI{scheduleTask(e,t){this.tasks.push(e),this.running||(this.running=!0,tN(this.afterScheduled))}runTasks(){let e=this.tasks;this.tasks=[];for(let t=0,n=e.length;t<n;t++)e[t]()}shouldYield(e){return e.currentOpCount>=e.getRef(tF)}flush(){for(;this.tasks.length>0;)this.runTasks()}constructor(){this.tasks=[],this.running=!1,this.afterScheduled=()=>{this.running=!1,this.runTasks()}}}let tM=e=>tg(t=>ta(eU(t.context,e))),tL=i(2,(e,t)=>tg(n=>{let r=n.context;return n.context=t(r),tK(e,()=>(n.context=r,td))})),tP=i(3,(e,t,n)=>tL(e,eC(t,n)));class tF extends eI()("effect/Micro/currentMaxOpsBeforeYield",{defaultValue:()=>2048}){}class tD extends eI()("effect/Micro/currentConcurrency",{defaultValue:()=>"unbounded"}){}class t$ extends eI()("effect/Micro/currentScheduler",{defaultValue:()=>new tI}){}let tz=i(e=>eP(e[0]),(e,t,n)=>tk(e,e=>t(e)?ta(e):ti(n(e)))),tB=i(2,(e,t)=>{let n=Object.create(tH);return n[e3]=e,n[e6]=t,n}),tH=tt({op:"OnFailure",eval(e){return e._stack.push(this),this[e3]}}),tV=i(3,(e,t,n)=>tB(e,e=>t(e)?n(e):ts(e))),tJ=i(3,(e,t,n)=>tV(e,t,e=>t_(n(e),ts(e)))),tG=i(2,(e,t)=>tJ(e,eq,e=>t(e.error))),tq=i(3,(e,t,n)=>tV(e,e=>eq(e)&&t(e.error),e=>n(e.error))),tW=i(3,(e,t,n)=>tq(e,x(t),n)),tY=function(){let e=globalThis.Error.stackTraceLimit;globalThis.Error.stackTraceLimit=2;let t=new globalThis.Error;globalThis.Error.stackTraceLimit=e;let n=e=>n=>t0(n,n=>ts(function(e,n){let r=t.stack;if(!r)return n;let a=r.split("\n")[2]?.trim().replace(/^at /,"");if(!a)return n;let s=a.match(/\((.*)\)$/);return eX(n,`at ${e} (${s?s[1]:a})`)}(e,n)));return 2==arguments.length?n(arguments[1])(arguments[0]):n(arguments[0])},tX=i(2,(e,t)=>{let n=Object.create(tZ);return n[e3]=e,n[e5]=t.onSuccess,n[e6]=t.onFailure,n}),tZ=tt({op:"OnSuccessAndFailure",eval(e){return e._stack.push(this),this[e3]}}),tK=i(2,(e,t)=>t3(n=>tX(n(e),{onFailure:e=>tk(t(ts(e)),()=>ts(e)),onSuccess:e=>tk(t(tC(e)),()=>ta(e))}))),tQ=i(3,(e,t,n)=>tK(e,e=>t(e)?n(e):tU)),t0=i(2,(e,t)=>tQ(e,tT,e=>t(e.cause))),t1=tn({op:"SetInterruptible",ensure(e){if(e.interruptible=this[e3],e._interrupted&&e.interruptible)return()=>tR}}),t2=e=>tg(t=>t.interruptible?e:(t.interruptible=!0,t._stack.push(t1(!1)),t._interrupted)?tR:e),t3=e=>tg(t=>t.interruptible?(t.interruptible=!1,t._stack.push(t1(!0)),e(t2)):e(o)),t4=tn({op:"While",contA(e,t){return(this[e3].step(e),this[e3].while())?(t._stack.push(this),this[e3].body()):tU},eval(e){return this[e3].while()?(e._stack.push(this),this[e3].body()):tU}}),t5=(e,t,n)=>tg(r=>{let a=n?.concurrency==="inherit"?r.getRef(tD):n?.concurrency??1,s="unbounded"===a?Number.POSITIVE_INFINITY:Math.max(1,a),i=eg(e),o=i.length;if(0===o)return n?.discard?td:ta([]);let u=n?.discard?void 0:Array(o),c=0;return 1===s?tw(t4({while:()=>c<i.length,body:()=>t(i[c],c),step:u?e=>u[c++]=e:e=>c++}),u):tv(e=>{let n;let a=new Set,l=0,d=0,p=!1,f=!1;return function h(){for(p=!0;l<s&&c<o;){let g=c,m=i[g];c++,l++;try{let i=t6(r,t(m,g),!0,!0);a.add(i),i.addObserver(t=>{a.delete(i),!f&&("Failure"===t._tag?void 0===n&&(n=t,o=c,a.forEach(e=>e.unsafeInterrupt())):void 0!==u&&(u[g]=t.value),d++,l--,d===o?e(n??ta(u)):!p&&l<s&&h())})}catch(e){n=tA(e),o=c,a.forEach(e=>e.unsafeInterrupt())}}p=!1}(),tu(()=>(f=!0,c=o,e1(a)))})}),t6=(e,t,n=!1,r=!1)=>{let a=new eQ(e.context,e.interruptible);return r||(e.children().add(a),a.addObserver(()=>e.children().delete(a))),n?a.evaluate(t):e.getRef(t$).scheduleTask(()=>a.evaluate(t),0),a},t7=(e,t)=>{let n=new eQ(t$.context(t?.scheduler??new tI));if(n.evaluate(e),t?.signal){if(t.signal.aborted)n.unsafeInterrupt();else{let e=()=>n.unsafeInterrupt();t.signal.addEventListener("abort",e,{once:!0}),n.addObserver(()=>t.signal.removeEventListener("abort",e))}}return n},t8=(e,t)=>new Promise((n,r)=>{t7(e,t).addObserver(n)}),t9=(e,t)=>t8(e,t).then(e=>{if("Failure"===e._tag)throw e.cause;return e.value}),ne=e=>{let t=new tI,n=t7(e,{scheduler:t});return t.flush(),n._exit??tA(n)},nt=e=>{let t=ne(e);if("Failure"===t._tag)throw t.cause;return t.value},nn=function(){class e extends globalThis.Error{}return Object.assign(e.prototype,e9,er,{[e2]:"Failure",[e4](){return ti(this)},toString(){return this.message?`${this.name}: ${this.message}`:this.name},toJSON(){return{...this}},[z](){let e=this.stack;return e?`${this.toString()}
${e.split("\n").slice(1).join("\n")}`:this.toString()}}),e}(),nr=class extends nn{constructor(e){super(),e&&Object.assign(this,e)}},na=e=>{class t extends nr{constructor(...t){super(...t),this._tag=e}}return t.prototype.name=e,t},ns={"audio/3gpp":{source:"iana",extensions:["3gpp"]},"audio/adpcm":{source:"apache",extensions:["adp"]},"audio/amr":{source:"iana",extensions:["amr"]},"audio/basic":{source:"iana",extensions:["au","snd"]},"audio/midi":{source:"apache",extensions:["mid","midi","kar","rmi"]},"audio/mobile-xmf":{source:"iana",extensions:["mxmf"]},"audio/mp4":{source:"iana",extensions:["m4a","mp4a"]},"audio/mpeg":{source:"iana",extensions:["mpga","mp2","mp2a","mp3","m2a","m3a"]},"audio/ogg":{source:"iana",extensions:["oga","ogg","spx","opus"]},"audio/s3m":{source:"apache",extensions:["s3m"]},"audio/silk":{source:"apache",extensions:["sil"]},"audio/vnd.dece.audio":{source:"iana",extensions:["uva","uvva"]},"audio/vnd.digital-winds":{source:"iana",extensions:["eol"]},"audio/vnd.dra":{source:"iana",extensions:["dra"]},"audio/vnd.dts":{source:"iana",extensions:["dts"]},"audio/vnd.dts.hd":{source:"iana",extensions:["dtshd"]},"audio/vnd.lucent.voice":{source:"iana",extensions:["lvp"]},"audio/vnd.ms-playready.media.pya":{source:"iana",extensions:["pya"]},"audio/vnd.nuera.ecelp4800":{source:"iana",extensions:["ecelp4800"]},"audio/vnd.nuera.ecelp7470":{source:"iana",extensions:["ecelp7470"]},"audio/vnd.nuera.ecelp9600":{source:"iana",extensions:["ecelp9600"]},"audio/vnd.rip":{source:"iana",extensions:["rip"]},"audio/webm":{source:"apache",extensions:["weba"]},"audio/x-aac":{source:"apache",extensions:["aac"]},"audio/x-aiff":{source:"apache",extensions:["aif","aiff","aifc"]},"audio/x-caf":{source:"apache",extensions:["caf"]},"audio/x-flac":{source:"apache",extensions:["flac"]},"audio/x-m4a":{source:"nginx",extensions:["m4a"]},"audio/x-matroska":{source:"apache",extensions:["mka"]},"audio/x-mpegurl":{source:"apache",extensions:["m3u"]},"audio/x-ms-wax":{source:"apache",extensions:["wax"]},"audio/x-ms-wma":{source:"apache",extensions:["wma"]},"audio/x-pn-realaudio":{source:"apache",extensions:["ram","ra"]},"audio/x-pn-realaudio-plugin":{source:"apache",extensions:["rmp"]},"audio/x-realaudio":{source:"nginx",extensions:["ra"]},"audio/x-wav":{source:"apache",extensions:["wav"]},"audio/x-gsm":{source:"apache",extensions:["gsm"]},"audio/xm":{source:"apache",extensions:["xm"]}},ni={"image/aces":{source:"iana",extensions:["exr"]},"image/avci":{source:"iana",extensions:["avci"]},"image/avcs":{source:"iana",extensions:["avcs"]},"image/avif":{source:"iana",extensions:["avif"]},"image/bmp":{source:"iana",extensions:["bmp"]},"image/cgm":{source:"iana",extensions:["cgm"]},"image/dicom-rle":{source:"iana",extensions:["drle"]},"image/emf":{source:"iana",extensions:["emf"]},"image/fits":{source:"iana",extensions:["fits"]},"image/g3fax":{source:"iana",extensions:["g3"]},"image/gif":{source:"iana",extensions:["gif"]},"image/heic":{source:"iana",extensions:["heic"]},"image/heic-sequence":{source:"iana",extensions:["heics"]},"image/heif":{source:"iana",extensions:["heif"]},"image/heif-sequence":{source:"iana",extensions:["heifs"]},"image/hej2k":{source:"iana",extensions:["hej2"]},"image/hsj2":{source:"iana",extensions:["hsj2"]},"image/ief":{source:"iana",extensions:["ief"]},"image/jls":{source:"iana",extensions:["jls"]},"image/jp2":{source:"iana",extensions:["jp2","jpg2"]},"image/jpeg":{source:"iana",extensions:["jpeg","jpg","jpe","jfif","pjpeg","pjp"]},"image/jph":{source:"iana",extensions:["jph"]},"image/jphc":{source:"iana",extensions:["jhc"]},"image/jpm":{source:"iana",extensions:["jpm"]},"image/jpx":{source:"iana",extensions:["jpx","jpf"]},"image/jxr":{source:"iana",extensions:["jxr"]},"image/jxra":{source:"iana",extensions:["jxra"]},"image/jxrs":{source:"iana",extensions:["jxrs"]},"image/jxs":{source:"iana",extensions:["jxs"]},"image/jxsc":{source:"iana",extensions:["jxsc"]},"image/jxsi":{source:"iana",extensions:["jxsi"]},"image/jxss":{source:"iana",extensions:["jxss"]},"image/ktx":{source:"iana",extensions:["ktx"]},"image/ktx2":{source:"iana",extensions:["ktx2"]},"image/png":{source:"iana",extensions:["png"]},"image/prs.btif":{source:"iana",extensions:["btif"]},"image/prs.pti":{source:"iana",extensions:["pti"]},"image/sgi":{source:"apache",extensions:["sgi"]},"image/svg+xml":{source:"iana",extensions:["svg","svgz"]},"image/t38":{source:"iana",extensions:["t38"]},"image/tiff":{source:"iana",extensions:["tif","tiff"]},"image/tiff-fx":{source:"iana",extensions:["tfx"]},"image/vnd.adobe.photoshop":{source:"iana",extensions:["psd"]},"image/vnd.airzip.accelerator.azv":{source:"iana",extensions:["azv"]},"image/vnd.dece.graphic":{source:"iana",extensions:["uvi","uvvi","uvg","uvvg"]},"image/vnd.djvu":{source:"iana",extensions:["djvu","djv"]},"image/vnd.dvb.subtitle":{source:"iana",extensions:["sub"]},"image/vnd.dwg":{source:"iana",extensions:["dwg"]},"image/vnd.dxf":{source:"iana",extensions:["dxf"]},"image/vnd.fastbidsheet":{source:"iana",extensions:["fbs"]},"image/vnd.fpx":{source:"iana",extensions:["fpx"]},"image/vnd.fst":{source:"iana",extensions:["fst"]},"image/vnd.fujixerox.edmics-mmr":{source:"iana",extensions:["mmr"]},"image/vnd.fujixerox.edmics-rlc":{source:"iana",extensions:["rlc"]},"image/vnd.microsoft.icon":{source:"iana",extensions:["ico"]},"image/vnd.ms-modi":{source:"iana",extensions:["mdi"]},"image/vnd.ms-photo":{source:"apache",extensions:["wdp"]},"image/vnd.net-fpx":{source:"iana",extensions:["npx"]},"image/vnd.pco.b16":{source:"iana",extensions:["b16"]},"image/vnd.tencent.tap":{source:"iana",extensions:["tap"]},"image/vnd.valve.source.texture":{source:"iana",extensions:["vtf"]},"image/vnd.wap.wbmp":{source:"iana",extensions:["wbmp"]},"image/vnd.xiff":{source:"iana",extensions:["xif"]},"image/vnd.zbrush.pcx":{source:"iana",extensions:["pcx"]},"image/webp":{source:"apache",extensions:["webp"]},"image/wmf":{source:"iana",extensions:["wmf"]},"image/x-3ds":{source:"apache",extensions:["3ds"]},"image/x-cmu-raster":{source:"apache",extensions:["ras"]},"image/x-cmx":{source:"apache",extensions:["cmx"]},"image/x-freehand":{source:"apache",extensions:["fh","fhc","fh4","fh5","fh7"]},"image/x-icon":{source:"apache",extensions:["ico"]},"image/x-jng":{source:"nginx",extensions:["jng"]},"image/x-mrsid-image":{source:"apache",extensions:["sid"]},"image/x-ms-bmp":{source:"nginx",extensions:["bmp"]},"image/x-pcx":{source:"apache",extensions:["pcx"]},"image/x-pict":{source:"apache",extensions:["pic","pct"]},"image/x-portable-anymap":{source:"apache",extensions:["pnm"]},"image/x-portable-bitmap":{source:"apache",extensions:["pbm"]},"image/x-portable-graymap":{source:"apache",extensions:["pgm"]},"image/x-portable-pixmap":{source:"apache",extensions:["ppm"]},"image/x-rgb":{source:"apache",extensions:["rgb"]},"image/x-tga":{source:"apache",extensions:["tga"]},"image/x-xbitmap":{source:"apache",extensions:["xbm"]},"image/x-xpixmap":{source:"apache",extensions:["xpm"]},"image/x-xwindowdump":{source:"apache",extensions:["xwd"]}},no={"text/cache-manifest":{source:"iana",extensions:["appcache","manifest"]},"text/calendar":{source:"iana",extensions:["ics","ifb"]},"text/css":{source:"iana",charset:"UTF-8",extensions:["css"]},"text/csv":{source:"iana",extensions:["csv"]},"text/html":{source:"iana",extensions:["html","htm","shtml"]},"text/markdown":{source:"iana",extensions:["markdown","md"]},"text/mathml":{source:"nginx",extensions:["mml"]},"text/n3":{source:"iana",charset:"UTF-8",extensions:["n3"]},"text/plain":{source:"iana",extensions:["txt","text","conf","def","list","log","in","ini"]},"text/prs.lines.tag":{source:"iana",extensions:["dsc"]},"text/richtext":{source:"iana",extensions:["rtx"]},"text/rtf":{source:"iana",extensions:["rtf"]},"text/sgml":{source:"iana",extensions:["sgml","sgm"]},"text/shex":{source:"iana",extensions:["shex"]},"text/spdx":{source:"iana",extensions:["spdx"]},"text/tab-separated-values":{source:"iana",extensions:["tsv"]},"text/troff":{source:"iana",extensions:["t","tr","roff","man","me","ms"]},"text/turtle":{source:"iana",charset:"UTF-8",extensions:["ttl"]},"text/uri-list":{source:"iana",extensions:["uri","uris","urls"]},"text/vcard":{source:"iana",extensions:["vcard"]},"text/vnd.curl":{source:"iana",extensions:["curl"]},"text/vnd.curl.dcurl":{source:"apache",extensions:["dcurl"]},"text/vnd.curl.mcurl":{source:"apache",extensions:["mcurl"]},"text/vnd.curl.scurl":{source:"apache",extensions:["scurl"]},"text/vnd.dvb.subtitle":{source:"iana",extensions:["sub"]},"text/vnd.familysearch.gedcom":{source:"iana",extensions:["ged"]},"text/vnd.fly":{source:"iana",extensions:["fly"]},"text/vnd.fmi.flexstor":{source:"iana",extensions:["flx"]},"text/vnd.graphviz":{source:"iana",extensions:["gv"]},"text/vnd.in3d.3dml":{source:"iana",extensions:["3dml"]},"text/vnd.in3d.spot":{source:"iana",extensions:["spot"]},"text/vnd.sun.j2me.app-descriptor":{source:"iana",charset:"UTF-8",extensions:["jad"]},"text/vnd.wap.wml":{source:"iana",extensions:["wml"]},"text/vnd.wap.wmlscript":{source:"iana",extensions:["wmls"]},"text/vtt":{source:"iana",charset:"UTF-8",extensions:["vtt"]},"text/x-asm":{source:"apache",extensions:["s","asm"]},"text/x-c":{source:"apache",extensions:["c","cc","cxx","cpp","h","hh","dic"]},"text/x-component":{source:"nginx",extensions:["htc"]},"text/x-fortran":{source:"apache",extensions:["f","for","f77","f90"]},"text/x-java-source":{source:"apache",extensions:["java"]},"text/x-nfo":{source:"apache",extensions:["nfo"]},"text/x-opml":{source:"apache",extensions:["opml"]},"text/x-pascal":{source:"apache",extensions:["p","pas"]},"text/x-setext":{source:"apache",extensions:["etx"]},"text/x-sfv":{source:"apache",extensions:["sfv"]},"text/x-uuencode":{source:"apache",extensions:["uu"]},"text/x-vcalendar":{source:"apache",extensions:["vcs"]},"text/x-vcard":{source:"apache",extensions:["vcf"]},"text/xml":{source:"iana",extensions:["xml"]}},nu={"video/3gpp":{source:"iana",extensions:["3gp","3gpp"]},"video/3gpp2":{source:"iana",extensions:["3g2"]},"video/h261":{source:"iana",extensions:["h261"]},"video/h263":{source:"iana",extensions:["h263"]},"video/h264":{source:"iana",extensions:["h264"]},"video/iso.segment":{source:"iana",extensions:["m4s"]},"video/jpeg":{source:"iana",extensions:["jpgv"]},"video/jpm":{source:"apache",extensions:["jpm","jpgm"]},"video/mj2":{source:"iana",extensions:["mj2","mjp2"]},"video/mp2t":{source:"iana",extensions:["ts"]},"video/mp4":{source:"iana",extensions:["mp4","mp4v","mpg4"]},"video/mpeg":{source:"iana",extensions:["mpeg","mpg","mpe","m1v","m2v"]},"video/ogg":{source:"iana",extensions:["ogv"]},"video/quicktime":{source:"iana",extensions:["qt","mov"]},"video/vnd.dece.hd":{source:"iana",extensions:["uvh","uvvh"]},"video/vnd.dece.mobile":{source:"iana",extensions:["uvm","uvvm"]},"video/vnd.dece.pd":{source:"iana",extensions:["uvp","uvvp"]},"video/vnd.dece.sd":{source:"iana",extensions:["uvs","uvvs"]},"video/vnd.dece.video":{source:"iana",extensions:["uvv","uvvv"]},"video/vnd.dvb.file":{source:"iana",extensions:["dvb"]},"video/vnd.fvt":{source:"iana",extensions:["fvt"]},"video/vnd.mpegurl":{source:"iana",extensions:["mxu","m4u"]},"video/vnd.ms-playready.media.pyv":{source:"iana",extensions:["pyv"]},"video/vnd.uvvu.mp4":{source:"iana",extensions:["uvu","uvvu"]},"video/vnd.vivo":{source:"iana",extensions:["viv"]},"video/webm":{source:"apache",extensions:["webm"]},"video/x-f4v":{source:"apache",extensions:["f4v"]},"video/x-fli":{source:"apache",extensions:["fli"]},"video/x-flv":{source:"apache",extensions:["flv"]},"video/x-m4v":{source:"apache",extensions:["m4v"]},"video/x-matroska":{source:"apache",extensions:["mkv","mk3d","mks"]},"video/x-mng":{source:"apache",extensions:["mng"]},"video/x-ms-asf":{source:"apache",extensions:["asf","asx"]},"video/x-ms-vob":{source:"apache",extensions:["vob"]},"video/x-ms-wm":{source:"apache",extensions:["wm"]},"video/x-ms-wmv":{source:"apache",extensions:["wmv"]},"video/x-ms-wmx":{source:"apache",extensions:["wmx"]},"video/x-ms-wvx":{source:"apache",extensions:["wvx"]},"video/x-msvideo":{source:"apache",extensions:["avi"]},"video/x-sgi-movie":{source:"apache",extensions:["movie"]},"video/x-smv":{source:"apache",extensions:["smv"]}};class nc extends na("InvalidURL"){constructor(e){super({reason:`Failed to parse '${e}' as a URL.`})}}class nl extends na("FetchError"){}class nd extends na("InvalidJson"){}class np extends na("BadRequestError"){getMessage(){return v(this.json)&&"string"==typeof this.json.message?this.json.message:this.message}}class nf extends na("UploadAborted"){}class nh extends na("UploadAborted"){}function ng(e){return Object.keys(e)}function nm(e,t,n){!function(e,t){let n=/(\d+)\.?(\d+)?\.?(\d+)?/,r=n.exec(e);if(!r?.[0])throw Error(`Invalid semver requirement: ${e}`);let a=n.exec(t);if(!a?.[0])throw Error(`Invalid semver to check: ${t}`);let[s,i,o,u]=r,[c,l,d,p]=a;return e.startsWith("^")?i===l&&(!o||!d||!(o>d)):e.startsWith("~")?i===l&&o===d:i===l&&o===d&&u===p}(t,n)&&console.warn(`!!!WARNING::: ${e} requires "uploadthing@${t}", but version "${n}" is installed`)}let nx=e=>tb(function*(){let t="undefined"!=typeof window?window.location.origin:process.env.VERCEL_URL?`https://${process.env.VERCEL_URL}`:"http://localhost:3000",n=yield*tp({try:()=>new URL(e??"/api/uploadthing",t),catch:()=>new nc(e??"/api/uploadthing")});return"/"===n.pathname&&(n.pathname="/api/uploadthing"),n}),nv=e=>e instanceof URL?e:nt(nx(e));function nb(){}let ny={BAD_REQUEST:400,NOT_FOUND:404,FORBIDDEN:403,INTERNAL_SERVER_ERROR:500,INTERNAL_CLIENT_ERROR:500,TOO_LARGE:413,TOO_SMALL:400,TOO_MANY_FILES:400,KEY_TOO_LONG:400,URL_GENERATION_FAILED:500,UPLOAD_FAILED:500,MISSING_ENV:500,INVALID_SERVER_CONFIG:500,FILE_LIMIT_EXCEEDED:500};class nw extends nr{constructor(e){let t="string"==typeof e?{code:"INTERNAL_SERVER_ERROR",message:e}:e;super({message:t.message??function(e,t){return"string"==typeof e?e:e instanceof Error||e&&"object"==typeof e&&"message"in e&&"string"==typeof e.message?e.message:t??"An unknown error occurred"}(t.cause,t.code)}),this._tag="UploadThingError",this.name="UploadThingError",this.code=t.code,this.data=t.data,t.cause instanceof Error?this.cause=t.cause:v(t.cause)&&p(t.cause.status)&&d(t.cause.statusText)?this.cause=Error(`Response ${t.cause.status} ${t.cause.statusText}`):d(t.cause)?this.cause=Error(t.cause):this.cause=t.cause}static toObject(e){return{code:e.code,message:e.message,data:e.data}}static serialize(e){return JSON.stringify(nw.toObject(e))}}let n_=e=>new nw({code:"INTERNAL_CLIENT_ERROR",message:"Something went wrong. Please report this to UploadThing.",cause:e});class nj extends eN("uploadthing/Fetch")(){}let nk=(e,t)=>tk(tM(nj),n=>{let r=new Headers(t?.headers??[]),a={url:e.toString(),method:t?.method,body:t?.body,headers:Object.fromEntries(r)};return th({try:a=>n(e,{...t,headers:r,signal:a}),catch:e=>new nl({error:e instanceof Error?{...e,name:e.name,message:e.message,stack:e.stack}:e,input:a})}).pipe(tG(e=>to(()=>console.error(e.input))),tS(e=>Object.assign(e,{requestUrl:a.url})),tY("fetch"))}),nE=e=>th({try:async()=>({json:await e.json(),ok:e.ok,status:e.status}),catch:t=>new nd({error:t,input:e.requestUrl})}).pipe(tz(({ok:e})=>e,({json:t,status:n})=>new np({status:n,message:`Request to ${e.requestUrl} failed with status ${n}`,json:t})),tS(({json:e})=>e),tY("parseJson")),nS=(e,t)=>"all"===t?e:"fine"===t?Math.round(e):10*Math.floor(e/10),nO=e=>{let t=Array.isArray(e)?e:ng(e);return t.includes("blob")?[]:t.map(e=>"pdf"===e?"application/pdf":e.includes("/")?e:"audio"===e?["audio/*",...ng(ns)].join(", "):"image"===e?["image/*",...ng(ni)].join(", "):"text"===e?["text/*",...ng(no)].join(", "):"video"===e?["video/*",...ng(nu)].join(", "):`${e}/*`)},nC=e=>Object.fromEntries(nO(e).map(e=>[e,[]]));function nR(e){let t=e.clipboardData?.items;if(t)return Array.from(t).reduce((e,t)=>{let n=t.getAsFile();return n?[...e,n]:e},[])}let nA=e=>({fileTypes:e?ng(e):[],multiple:(e?Object.values(e).map(e=>e.maxFileCount):[]).some(e=>e&&e>1)}),nT=e=>e.charAt(0).toUpperCase()+e.slice(1),nU=e=>{if(!e)return"";let t=ng(e),n=t.map(e=>"blob"===e?"file":e);if(n.length>1){let e=n.pop();return`${n.join("s, ")} and ${e}s`}let r=t[0],a=n[0];if(!r||!a)return"";let{maxFileSize:s,maxFileCount:i,minFileCount:o}=e[r];return i&&i>1?o>1?`${o} - ${i} ${a}s up to ${s}`:`${a}s up to ${s}, max ${i}`:`${a} (${s})`},nN=e=>nT(nU(e)),nI=(e,t)=>{if("string"==typeof e)return e;if("function"==typeof e){let n=e(t);if("string"==typeof n)return n}return""},nM=(e,t)=>{if("object"==typeof e)return e;if("function"==typeof e){let n=e(t);if("object"==typeof n)return n}return{}},nL=(e,t)=>e?"function"!=typeof e?e:"function"==typeof e?e(t):void 0:null,nP=(...e)=>e.filter(Boolean).join(" ");function nF(e,t){return"application/x-moz-file"===e.type||function(e,t){if(t){let n=Array.isArray(t)?t:t.split(","),r=e.name,a=e.type.toLowerCase(),s=a.replace(/\/.*$/,"");return n.some(e=>{let t=e.trim().toLowerCase();return t.startsWith(".")?r.toLowerCase().endsWith(t):t.endsWith("/*")?s===t.replace(/\/.*$/,""):a===t})}return!0}(e,t)}new TextEncoder;let nD=e=>null!=e;function n$(e,t,n){return!nD(e.size)||(nD(t)&&nD(n)?e.size>=t&&e.size<=n:!(nD(t)&&e.size<t||nD(n)&&e.size>n))}function nz(e,t,n){return(!!t||!(e.length>1))&&(!t||!(n>=1)||!(e.length>n))}function nB(e){return"dataTransfer"in e&&null!==e.dataTransfer?Array.prototype.some.call(e.dataTransfer?.types,e=>"Files"===e||"application/x-moz-file"===e):!!e.target&&"files"in e.target&&!!e.target.files}let nH={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,acceptedFiles:[]};function nV(e,t){switch(t.type){case"focus":return{...e,isFocused:!0};case"blur":return{...e,isFocused:!1};case"openDialog":return{...nH,isFileDialogActive:!0};case"closeDialog":return{...e,isFileDialogActive:!1};case"setDraggedFiles":case"setFiles":return{...e,...t.payload};case"reset":return nH;default:return e}}let nJ=()=>{let e,t;let n=new AbortController;return{promise:new Promise((n,r)=>{e=n,t=r}),ac:n,resolve:e,reject:t}},nG=e=>{console.warn(`⚠️ [uploadthing][deprecated] ${e}`)},nq=e=>{let t=new URL(e.url),n=new URLSearchParams(t.search);return n.set("actionType",e.actionType),n.set("slug",e.slug),t.search=n.toString(),t},nW=e=>(t,n)=>tb(function*(){let r=nq({url:e.url,slug:e.endpoint,actionType:t}),a=new Headers((yield*tf(async()=>"function"==typeof e.headers?await e.headers():e.headers)));return e.package&&a.set("x-uploadthing-package",e.package),a.set("x-uploadthing-version","7.7.2"),a.set("Content-Type","application/json"),yield*nk(r,{method:"POST",body:JSON.stringify(n),headers:a}).pipe(t_(nE),tS(o),tW("FetchError",e=>ti(new nw({code:"INTERNAL_CLIENT_ERROR",message:`Failed to report event "${t}" to UploadThing server`,cause:e}))),tW("BadRequestError",e=>ti(new nw({code:function(e){for(let[t,n]of Object.entries(ny))if(n===e)return t;return"INTERNAL_SERVER_ERROR"}(e.status),message:e.getMessage(),cause:e.json}))),tW("InvalidJson",e=>ti(new nw({code:"INTERNAL_CLIENT_ERROR",message:"Failed to parse response from UploadThing server",cause:e}))))}),nY=(e,t,n,r)=>tv(a=>{let s=new XMLHttpRequest;s.open("PUT",n.url,!0),s.setRequestHeader("Range",`bytes=${t}-`),s.setRequestHeader("x-uploadthing-version","7.7.2"),s.responseType="json";let i=0;s.upload.addEventListener("progress",({loaded:e})=>{let t=e-i;r?.({loaded:e,delta:t}),i=e}),s.addEventListener("load",()=>{s.status>=200&&s.status<300&&v(s.response)?m(s.response,"error")?a(new nw({code:"UPLOAD_FAILED",message:String(s.response.error),data:s.response})):a(ta(s.response)):a(new nw({code:"UPLOAD_FAILED",message:`XHR failed ${s.status} ${s.statusText}`,data:s.response}))}),s.addEventListener("error",()=>{a(new nw({code:"UPLOAD_FAILED"}))});let o=new FormData;return"uri"in e?o.append("file",{uri:e.uri,type:e.type,name:e.name,...t>0&&{range:t}}):o.append("file",t>0?e.slice(t):e),s.send(o),to(()=>s.abort())}),nX=(e,t,n)=>nk(t.url,{method:"HEAD"}).pipe(tS(({headers:e})=>parseInt(e.get("x-ut-range-start")??"0",10)),tj(e=>n.onUploadProgress?.({delta:e,loaded:e})),tk(r=>nY(e,r,t,e=>n.onUploadProgress?.({delta:e.delta,loaded:e.loaded+r}))),tS(o),tS(n=>({name:e.name,size:e.size,key:t.key,lastModified:e.lastModified,serverData:n.serverData,get url(){return nG("`file.url` is deprecated and will be removed in uploadthing v9. Use `file.ufsUrl` instead."),n.url},get appUrl(){return nG("`file.appUrl` is deprecated and will be removed in uploadthing v9. Use `file.ufsUrl` instead."),n.appUrl},ufsUrl:n.ufsUrl,customId:t.customId,type:e.type,fileHash:n.fileHash}))),nZ=(e,t)=>{let n=nW({endpoint:String(e),package:t.package,url:t.url,headers:t.headers}),r=t.files.reduce((e,t)=>e+t.size,0),a=0;return tk(n("upload",{input:"input"in t?t.input:null,files:t.files.map(e=>({name:e.name,size:e.size,type:e.type,lastModified:e.lastModified}))}),e=>t5(e,(e,n)=>tk(to(()=>t.onUploadBegin?.({file:t.files[n].name})),()=>nX(t.files[n],e,{onUploadProgress:e=>{a+=e.delta,t.onUploadProgress?.({file:t.files[n],progress:e.loaded/t.files[n].size*100,loaded:e.loaded,delta:e.delta,totalLoaded:a,totalProgress:a/r})}})),{concurrency:6}))},nK="7.7.2",nQ=e=>{let t=new Proxy(nb,{get:(e,t)=>t});return{uploadFiles:(n,r)=>{let a="function"==typeof n?n(t):n,s=e?.fetch??window.fetch;return nZ(a,{...r,skipPolling:{},url:nv(e?.url),package:e?.package??"uploadthing/client",input:r.input}).pipe(tP(nj,s),e=>t8(e,r.signal&&{signal:r.signal})).then(e=>{if("Success"===e._tag)return e.value;if("Interrupt"===e.cause._tag)throw new nh;throw eY(e.cause)})},createUpload:async(n,r)=>{let a=new Map,s=nW({endpoint:String("function"==typeof n?n(t):n),package:e?.package??"uploadthing/client",url:nv(e?.url),headers:r.headers}),i=e?.fetch??window.fetch,o=await t9(s("upload",{input:"input"in r?r.input:null,files:r.files.map(e=>({name:e.name,size:e.size,type:e.type,lastModified:e.lastModified}))}).pipe(tP(nj,i))),u=r.files.reduce((e,t)=>e+t.size,0),c=0,l=(e,t)=>nX(e,t,{onUploadProgress:t=>{c+=t.delta,r.onUploadProgress?.({...t,file:e,progress:Math.round(t.loaded/e.size*100),totalLoaded:c,totalProgress:Math.round(c/u*100)})}}).pipe(tP(nj,i));for(let[e,t]of o.entries()){let n=r.files[e];if(!n)continue;let s=nJ();a.set(n,{deferred:s,presigned:t}),t8(l(n,t),{signal:s.ac.signal}).then(e=>{if("Success"===e._tag)return s.resolve(e.value);if("Interrupt"===e.cause._tag)throw new nf;throw eY(e.cause)}).catch(e=>{e instanceof nf||s.reject(e)})}return{pauseUpload:e=>{for(let t of em(e??r.files)){let e=a.get(t);if(!e)return;if(e.deferred.ac.signal.aborted)throw new nh;e.deferred.ac.abort()}},resumeUpload:e=>{for(let t of em(e??r.files)){let e=a.get(t);if(!e)throw"No upload found";e.deferred.ac=new AbortController,t8(l(t,e.presigned),{signal:e.deferred.ac.signal}).then(t=>{if("Success"===t._tag)return e.deferred.resolve(t.value);if("Interrupt"===t.cause._tag)throw new nf;throw eY(t.cause)}).catch(t=>{t instanceof nf||e.deferred.reject(t)})}},done:async e=>{let t=[];for(let n of em(e??r.files)){let e=a.get(n);if(!e)throw"No upload found";t.push(e.deferred.promise)}let n=await Promise.all(t);return e?n[0]:n}}},routeRegistry:t}};var n0=n(43210),n1={uploadthing:"^7.2.0"};let n2=()=>void 0;function n3(e){let t=n0.useRef(n4);n2(()=>{t.current=e},[e]);let n=n0.useRef(null);return n.current??=function(){return t.current.apply(this,arguments)},n.current}function n4(){throw Error("INVALID_USEEVENT_INVOCATION: the callback from useEvent cannot be invoked before the component has mounted.")}let n5=(e,t,n)=>{let r=globalThis.__UPLOADTHING,{data:a}=function(e,t,n){(0,n0.useRef)({}),(0,n0.useRef)(!1);let r={error:void 0,data:void 0},[a,s]=(0,n0.useReducer)((e,t)=>{switch(t.type){case"loading":return{...r};case"fetched":return{...r,data:t.payload};case"error":return{...r,error:t.payload};default:return e}},r);return a}(0,r||t.href);return(r??a)?.find(e=>e.slug===n)?.config},n6=function(e,t,n,r){let a=r?.uploadProgressGranularity??"coarse",{uploadFiles:s,routeRegistry:i}=nQ({fetch:n,url:e,package:"@uploadthing/react"}),[o,u]=(0,n0.useState)(!1),c=(0,n0.useRef)(0),l=(0,n0.useRef)(new Map);return{startUpload:n3(async(...e)=>{let n=await r?.onBeforeUploadBegin?.(e[0])??e[0],i=e[1];u(!0),n.forEach(e=>l.current.set(e,0)),r?.onUploadProgress?.(0);try{let e=await s(t,{signal:r?.signal,headers:r?.headers,files:n,onUploadProgress:e=>{if(!r?.onUploadProgress)return;l.current.set(e.file,e.progress);let t=0;l.current.forEach(e=>{t+=e});let n=nS(Math.min(100,t/l.current.size),a);n!==c.current&&(r.onUploadProgress(n),c.current=n)},onUploadBegin({file:e}){r?.onUploadBegin&&r.onUploadBegin(e)},input:i});return await r?.onClientUploadComplete?.(e),e}catch(t){let e;if(t instanceof nh)throw t;t instanceof nw?e=t:console.error("Something went wrong. Please contact UploadThing and provide the following cause:",(e=n_(t)).cause instanceof Error?e.cause.toString():e.cause),await r?.onUploadError?.(e)}finally{u(!1),l.current=new Map,c.current=0}}),isUploading:o,routeConfig:n5(n,e,function(e,...t){return"function"==typeof e?e(...t):e}(t,i))}},n7=e=>{let t=n3(e);(0,n0.useEffect)(()=>{let e=new AbortController;return window.addEventListener("paste",t,{signal:e.signal}),()=>{e.abort()}},[t])};function n8(){return(0,s.jsx)("svg",{className:"z-10 block h-5 w-5 animate-spin align-middle text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 576 512",children:(0,s.jsx)("path",{fill:"currentColor",d:"M256 32C256 14.33 270.3 0 288 0C429.4 0 544 114.6 544 256C544 302.6 531.5 346.4 509.7 384C500.9 399.3 481.3 404.6 465.1 395.7C450.7 386.9 445.5 367.3 454.3 351.1C470.6 323.8 480 291 480 255.1C480 149.1 394 63.1 288 63.1C270.3 63.1 256 49.67 256 31.1V32z"})})}function n9({className:e,cn:t,...n}){return(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",strokeLinecap:"round",strokeLinejoin:"round",className:t("fill-none stroke-current stroke-2",e),...n,children:[(0,s.jsx)("circle",{cx:"12",cy:"12",r:"10"}),(0,s.jsx)("path",{d:"m4.9 4.9 14.2 14.2"})]})}function re(e){let{mode:t="auto",appendOnPaste:n=!1,cn:r=nP}=e.config??{},a=(0,n0.useRef)(new AbortController),i=(0,n0.useRef)(null),[o,u]=(0,n0.useState)(e.__internal_upload_progress??0),[c,l]=(0,n0.useState)([]),{startUpload:d,isUploading:p,routeConfig:f}=n6(nv(e.url),e.endpoint,e.fetch??globalThis.fetch,{signal:a.current.signal,headers:e.headers,onClientUploadComplete:t=>{i.current&&(i.current.value=""),l([]),e.onClientUploadComplete?.(t),u(0)},uploadProgressGranularity:e.uploadProgressGranularity,onUploadProgress:t=>{u(t),e.onUploadProgress?.(t)},onUploadError:e.onUploadError,onUploadBegin:e.onUploadBegin,onBeforeUploadBegin:e.onBeforeUploadBegin}),{fileTypes:h,multiple:g}=nA(f),m=!!(e.__internal_button_disabled??e.disabled),x=(()=>{let t="ready"===e.__internal_state||h.length>0;return e.__internal_state?e.__internal_state:m?"disabled":t?p?"uploading":"ready":"readying"})(),v=(0,n0.useCallback)(t=>{d(t,"input"in e?e.input:void 0).catch(t=>{if(t instanceof nh)e.onUploadAborted?.();else throw t})},[e,d]),b=(0,n0.useMemo)(()=>({type:"file",ref:i,multiple:g,accept:nO(h).join(", "),onChange:n=>{if(!n.target.files)return;let r=Array.from(n.target.files);if(e.onChange?.(r),"manual"===t){l(r);return}v(r)},disabled:m,tabIndex:m?-1:0}),[e,m,h,t,g,v]);n7(r=>{if(!n||document.activeElement!==i.current)return;let a=nR(r);if(!a)return;let s=a;l(t=>(s=[...t,...a],e.onChange?.(s),s)),"auto"===t&&v(c)});let y=(0,n0.useMemo)(()=>({ready:"readying"!==x,isUploading:"uploading"===x,uploadProgress:o,fileTypes:h,files:c}),[h,c,x,o]);return(0,s.jsxs)("div",{className:r("flex flex-col items-center justify-center gap-1",e.className,nI(e.appearance?.container,y)),style:{"--progress-width":`${o}%`,...nM(e.appearance?.container,y)},"data-state":x,children:[(0,s.jsxs)("label",{className:r("group relative flex h-10 w-36 cursor-pointer items-center justify-center overflow-hidden rounded-md text-white after:transition-[width] after:duration-500 focus-within:ring-2 focus-within:ring-blue-600 focus-within:ring-offset-2","disabled:pointer-events-none","data-[state=disabled]:cursor-not-allowed data-[state=readying]:cursor-not-allowed","data-[state=disabled]:bg-blue-400 data-[state=ready]:bg-blue-600 data-[state=readying]:bg-blue-400 data-[state=uploading]:bg-blue-400","after:absolute after:left-0 after:h-full after:w-[var(--progress-width)] after:content-[''] data-[state=uploading]:after:bg-blue-600",nI(e.appearance?.button,y)),style:nM(e.appearance?.button,y),"data-state":x,"data-ut-element":"button",onClick:e=>{if("uploading"===x){e.preventDefault(),e.stopPropagation(),a.current.abort(),a.current=new AbortController;return}"manual"===t&&c.length>0&&(e.preventDefault(),e.stopPropagation(),v(c))},children:[(0,s.jsx)("input",{...b,className:"sr-only"}),(()=>{let n=nL(e.content?.button,y);if(n)return n;switch(x){case"readying":return"Loading...";case"uploading":if(o>=100)return(0,s.jsx)(n8,{});return(0,s.jsxs)("span",{className:"z-50",children:[(0,s.jsxs)("span",{className:"block group-hover:hidden",children:[Math.round(o),"%"]}),(0,s.jsx)(n9,{cn:r,className:"hidden size-4 group-hover:block"})]});default:if("manual"===t&&c.length>0)return`Upload ${c.length} file${1===c.length?"":"s"}`;return`Choose File${b.multiple?"(s)":""}`}})()]}),"manual"===t&&c.length>0?(0,s.jsx)("button",{onClick:()=>{l([]),i.current&&(i.current.value=""),e.onChange?.([])},className:r("h-[1.25rem] cursor-pointer rounded border-none bg-transparent text-gray-500 transition-colors hover:bg-slate-200 hover:text-gray-600",nI(e.appearance?.clearBtn,y)),style:nM(e.appearance?.clearBtn,y),"data-state":x,"data-ut-element":"clear-btn",children:nL(e.content?.clearBtn,y)??"Clear"}):(0,s.jsx)("div",{className:r("h-[1.25rem] text-xs leading-5 text-gray-600",nI(e.appearance?.allowedContent,y)),style:nM(e.appearance?.allowedContent,y),"data-state":x,"data-ut-element":"allowed-content",children:nL(e.content?.allowedContent,y)??nN(f)})]})}var rt=n(4363),rn=new Map([["aac","audio/aac"],["abw","application/x-abiword"],["arc","application/x-freearc"],["avif","image/avif"],["avi","video/x-msvideo"],["azw","application/vnd.amazon.ebook"],["bin","application/octet-stream"],["bmp","image/bmp"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["cda","application/x-cdf"],["csh","application/x-csh"],["css","text/css"],["csv","text/csv"],["doc","application/msword"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["eot","application/vnd.ms-fontobject"],["epub","application/epub+zip"],["gz","application/gzip"],["gif","image/gif"],["heic","image/heic"],["heif","image/heif"],["htm","text/html"],["html","text/html"],["ico","image/vnd.microsoft.icon"],["ics","text/calendar"],["jar","application/java-archive"],["jpeg","image/jpeg"],["jpg","image/jpeg"],["js","text/javascript"],["json","application/json"],["jsonld","application/ld+json"],["mid","audio/midi"],["midi","audio/midi"],["mjs","text/javascript"],["mp3","audio/mpeg"],["mp4","video/mp4"],["mpeg","video/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["opus","audio/opus"],["otf","font/otf"],["png","image/png"],["pdf","application/pdf"],["php","application/x-httpd-php"],["ppt","application/vnd.ms-powerpoint"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["rar","application/vnd.rar"],["rtf","application/rtf"],["sh","application/x-sh"],["svg","image/svg+xml"],["swf","application/x-shockwave-flash"],["tar","application/x-tar"],["tif","image/tiff"],["tiff","image/tiff"],["ts","video/mp2t"],["ttf","font/ttf"],["txt","text/plain"],["vsd","application/vnd.visio"],["wav","audio/wav"],["weba","audio/webm"],["webm","video/webm"],["webp","image/webp"],["woff","font/woff"],["woff2","font/woff2"],["xhtml","application/xhtml+xml"],["xls","application/vnd.ms-excel"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xml","application/xml"],["xul","application/vnd.mozilla.xul+xml"],["zip","application/zip"],["7z","application/x-7z-compressed"],["mkv","video/x-matroska"],["mov","video/quicktime"],["msg","application/vnd.ms-outlook"]]);function rr(e,t){var n=function(e){var t=e.name;if(t&&-1!==t.lastIndexOf(".")&&!e.type){var n=t.split(".").pop().toLowerCase(),r=rn.get(n);r&&Object.defineProperty(e,"type",{value:r,writable:!1,configurable:!1,enumerable:!0})}return e}(e);if("string"!=typeof n.path){var r=e.webkitRelativePath;Object.defineProperty(n,"path",{value:"string"==typeof t?t:"string"==typeof r&&r.length>0?r:e.name,writable:!1,configurable:!1,enumerable:!0})}return n}var ra=[".DS_Store","Thumbs.db"];function rs(e){return(0,rt.sH)(this,void 0,void 0,function(){return(0,rt.YH)(this,function(t){var n;if(ri(e)&&ri(e.dataTransfer))return[2,function(e,t){return(0,rt.sH)(this,void 0,void 0,function(){var n;return(0,rt.YH)(this,function(r){switch(r.label){case 0:if(!e.items)return[3,2];if(n=ru(e.items).filter(function(e){return"file"===e.kind}),"drop"!==t)return[2,n];return[4,Promise.all(n.map(rc))];case 1:return[2,ro(function e(t){return t.reduce(function(t,n){return(0,rt.fX)((0,rt.fX)([],(0,rt.zs)(t),!1),(0,rt.zs)(Array.isArray(n)?e(n):[n]),!1)},[])}(r.sent()))];case 2:return[2,ro(ru(e.files).map(function(e){return rr(e)}))]}})})}(e.dataTransfer,e.type)];if(ri(n=e)&&ri(n.target))return[2,ru(e.target.files).map(function(e){return rr(e)})];return Array.isArray(e)&&e.every(function(e){return"getFile"in e&&"function"==typeof e.getFile})?[2,function(e){return(0,rt.sH)(this,void 0,void 0,function(){return(0,rt.YH)(this,function(t){switch(t.label){case 0:return[4,Promise.all(e.map(function(e){return e.getFile()}))];case 1:return[2,t.sent().map(function(e){return rr(e)})]}})})}(e)]:[2,[]]})})}function ri(e){return"object"==typeof e&&null!==e}function ro(e){return e.filter(function(e){return -1===ra.indexOf(e.name)})}function ru(e){if(null===e)return[];for(var t=[],n=0;n<e.length;n++){var r=e[n];t.push(r)}return t}function rc(e){if("function"!=typeof e.webkitGetAsEntry)return rl(e);var t=e.webkitGetAsEntry();return t&&t.isDirectory?rp(t):rl(e)}function rl(e){var t=e.getAsFile();return t?Promise.resolve(rr(t)):Promise.reject("".concat(e," is not a File"))}function rd(e){return(0,rt.sH)(this,void 0,void 0,function(){return(0,rt.YH)(this,function(t){return[2,e.isDirectory?rp(e):function(e){return(0,rt.sH)(this,void 0,void 0,function(){return(0,rt.YH)(this,function(t){return[2,new Promise(function(t,n){e.file(function(n){t(rr(n,e.fullPath))},function(e){n(e)})})]})})}(e)]})})}function rp(e){var t=e.createReader();return new Promise(function(e,n){var r=[];!function a(){var s=this;t.readEntries(function(t){return(0,rt.sH)(s,void 0,void 0,function(){var s;return(0,rt.YH)(this,function(i){switch(i.label){case 0:if(t.length)return[3,5];i.label=1;case 1:return i.trys.push([1,3,,4]),[4,Promise.all(r)];case 2:return e(i.sent()),[3,4];case 3:return n(i.sent()),[3,4];case 4:return[3,6];case 5:s=Promise.all(t.map(rd)),r.push(s),a(),i.label=6;case 6:return[2]}})})},function(e){n(e)})}()})}function rf(e){let{mode:t="manual",appendOnPaste:n=!1,cn:r=nP}=e.config??{},a=(0,n0.useRef)(new AbortController),[i,o]=(0,n0.useState)([]),[u,c]=(0,n0.useState)(e.__internal_upload_progress??0),{startUpload:l,isUploading:d,routeConfig:p}=n6(nv(e.url),e.endpoint,e.fetch??globalThis.fetch,{signal:a.current.signal,headers:e.headers,onClientUploadComplete:t=>{o([]),e.onClientUploadComplete?.(t),c(0)},uploadProgressGranularity:e.uploadProgressGranularity,onUploadProgress:t=>{c(t),e.onUploadProgress?.(t)},onUploadError:e.onUploadError,onUploadBegin:e.onUploadBegin,onBeforeUploadBegin:e.onBeforeUploadBegin}),{fileTypes:f,multiple:h}=nA(p),g=!!(e.__internal_dropzone_disabled??e.disabled),m=(()=>{let t=e.__internal_ready??("ready"===e.__internal_state||f.length>0);return e.__internal_state?e.__internal_state:g?"disabled":t?d?"uploading":"ready":"readying"})(),x=(0,n0.useCallback)(t=>{l(t,"input"in e?e.input:void 0).catch(t=>{if(t instanceof nh)e.onUploadAborted?.();else throw t})},[e,l]),{getRootProps:v,getInputProps:b,isDragActive:y,rootRef:w}=function({accept:e,disabled:t=!1,maxSize:n=Number.POSITIVE_INFINITY,minSize:r=0,multiple:a=!0,maxFiles:s=0,onDrop:i}){let o=(0,n0.useMemo)(()=>(function(e){if(nD(e))return Object.entries(e).reduce((e,[t,n])=>[...e,t,...n],[]).filter(e=>"audio/*"===e||"video/*"===e||"image/*"===e||"text/*"===e||/\w+\/[-+.\w]+/g.test(e)||/^.*\.[\w]+$/.test(e)).join(",")})(e),[e]),u=(0,n0.useRef)(null),c=(0,n0.useRef)(null),l=(0,n0.useRef)([]),[d,p]=(0,n0.useReducer)(nV,nH),f=(0,n0.useCallback)(e=>{e.preventDefault(),e.persist(),l.current=[...l.current,e.target],nB(e)&&Promise.resolve(rs(e)).then(t=>{if(e.isPropagationStopped())return;let i=t.length,u=i>0&&function({files:e,accept:t,minSize:n,maxSize:r,multiple:a,maxFiles:s}){return!!nz(e,a,s)&&e.every(e=>nF(e,t)&&n$(e,n,r))}({files:t,accept:o,minSize:r,maxSize:n,multiple:a,maxFiles:s});p({type:"setDraggedFiles",payload:{isDragAccept:u,isDragReject:i>0&&!u,isDragActive:!0}})}).catch(nb)},[o,s,n,r,a]),h=(0,n0.useCallback)(e=>{if(e.preventDefault(),e.persist(),nB(e))try{e.dataTransfer.dropEffect="copy"}catch{}return!1},[]),g=(0,n0.useCallback)(e=>{e.preventDefault(),e.persist();let t=l.current.filter(e=>u.current?.contains(e)),n=t.indexOf(e.target);-1!==n&&t.splice(n,1),l.current=t,t.length>0||p({type:"setDraggedFiles",payload:{isDragActive:!1,isDragAccept:!1,isDragReject:!1}})},[]),m=(0,n0.useCallback)(e=>{let t=[];e.forEach(e=>{let a=nF(e,o),s=n$(e,r,n);a&&s&&t.push(e)}),nz(t,a,s)||t.splice(0),p({type:"setFiles",payload:{acceptedFiles:t}}),i(t)},[o,s,n,r,a,i]),x=(0,n0.useCallback)(e=>{e.preventDefault(),e.persist(),l.current=[],nB(e)&&Promise.resolve(rs(e)).then(t=>{e.isPropagationStopped()||m(t)}).catch(nb),p({type:"reset"})},[m]),v=(0,n0.useCallback)(()=>{c.current&&(p({type:"openDialog"}),c.current.value="",c.current.click())},[]),b=(0,n0.useCallback)(e=>{if(u.current?.isEqualNode(e.target))("key"in e&&(" "===e.key||"Enter"===e.key)||"keyCode"in e&&(32===e.keyCode||13===e.keyCode))&&(e.preventDefault(),v())},[v]),y=(0,n0.useCallback)(e=>{e.stopPropagation(),d.isFileDialogActive&&e.preventDefault()},[d.isFileDialogActive]),w=(0,n0.useCallback)(()=>p({type:"focus"}),[]),_=(0,n0.useCallback)(()=>p({type:"blur"}),[]),j=(0,n0.useCallback)(()=>{(function(e=window.navigator.userAgent){return e.includes("MSIE ")||e.includes("Trident/")||e.includes("Edge/")})()?setTimeout(v,0):v()},[v]),k=(0,n0.useMemo)(()=>()=>({ref:u,role:"presentation",...t?{}:{tabIndex:0,onKeyDown:b,onFocus:w,onBlur:_,onClick:j,onDragEnter:f,onDragOver:h,onDragLeave:g,onDrop:x}}),[t,_,j,f,g,h,x,w,b]),E=(0,n0.useMemo)(()=>()=>({ref:c,type:"file",style:{display:"none"},accept:o,multiple:a,tabIndex:-1,...t?{}:{onChange:x,onClick:y}}),[o,a,x,y,t]);return{...d,getRootProps:k,getInputProps:E,rootRef:u}}({onDrop:(0,n0.useCallback)(n=>{e.onDrop?.(n),e.onChange?.(n),o(n),"auto"===t&&x(n)},[e,t,x]),multiple:h,accept:nC(f),disabled:g});n7(r=>{if(!n||document.activeElement!==w.current)return;let a=nR(r);if(!a?.length)return;let s=a;o(t=>(s=[...t,...a],e.onChange?.(s),s)),e.onChange?.(s),"auto"===t&&x(s)});let _=(0,n0.useMemo)(()=>({ready:"readying"!==m,isUploading:"uploading"===m,uploadProgress:u,fileTypes:f,files:i,isDragActive:y}),[f,i,m,u,y]);return(0,s.jsxs)("div",{className:r("mt-2 flex flex-col items-center justify-center rounded-lg border border-dashed border-gray-900/25 px-6 py-10 text-center",y&&"bg-blue-600/10",e.className,nI(e.appearance?.container,_)),...v(),style:nM(e.appearance?.container,_),"data-state":m,children:[nL(e.content?.uploadIcon,_)??(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",className:r("mx-auto block h-12 w-12 align-middle text-gray-400",nI(e.appearance?.uploadIcon,_)),style:nM(e.appearance?.uploadIcon,_),"data-ut-element":"upload-icon","data-state":m,children:(0,s.jsx)("path",{fill:"currentColor",fillRule:"evenodd",d:"M5.5 17a4.5 4.5 0 0 1-1.44-8.765a4.5 4.5 0 0 1 8.302-3.046a3.5 3.5 0 0 1 4.504 4.272A4 4 0 0 1 15 17H5.5Zm3.75-2.75a.75.75 0 0 0 1.5 0V9.66l1.95 2.1a.75.75 0 1 0 1.1-1.02l-3.25-3.5a.75.75 0 0 0-1.1 0l-3.25 3.5a.75.75 0 1 0 1.1 1.02l1.95-2.1v4.59Z",clipRule:"evenodd"})}),(0,s.jsxs)("label",{className:r("relative mt-4 flex w-64 cursor-pointer items-center justify-center text-sm font-semibold leading-6 text-gray-600 focus-within:outline-none focus-within:ring-2 focus-within:ring-blue-600 focus-within:ring-offset-2 hover:text-blue-500","ready"===m?"text-blue-600":"text-gray-500",nI(e.appearance?.label,_)),style:nM(e.appearance?.label,_),"data-ut-element":"label","data-state":m,children:[(0,s.jsx)("input",{className:"sr-only",...b()}),nL(e.content?.label,_)??("ready"===m?`Choose ${h?"file(s)":"a file"} or drag and drop`:"Loading...")]}),(0,s.jsx)("div",{className:r("m-0 h-[1.25rem] text-xs leading-5 text-gray-600",nI(e.appearance?.allowedContent,_)),style:nM(e.appearance?.allowedContent,_),"data-ut-element":"allowed-content","data-state":m,children:nL(e.content?.allowedContent,_)??nN(p)}),(0,s.jsx)("button",{className:r("group relative mt-4 flex h-10 w-36 items-center justify-center overflow-hidden rounded-md border-none text-base text-white","after:absolute after:left-0 after:h-full after:w-[var(--progress-width)] after:bg-blue-600 after:transition-[width] after:duration-500 after:content-['']","focus-within:ring-2 focus-within:ring-blue-600 focus-within:ring-offset-2","disabled:pointer-events-none","data-[state=disabled]:cursor-not-allowed data-[state=readying]:cursor-not-allowed","data-[state=disabled]:bg-blue-400 data-[state=ready]:bg-blue-600 data-[state=readying]:bg-blue-400 data-[state=uploading]:bg-blue-400",nI(e.appearance?.button,_)),style:{"--progress-width":`${u}%`,...nM(e.appearance?.button,_)},onClick:e=>{if("uploading"===m){e.preventDefault(),e.stopPropagation(),a.current.abort(),a.current=new AbortController;return}"manual"===t&&i.length>0&&(e.preventDefault(),e.stopPropagation(),x(i))},"data-ut-element":"button","data-state":m,type:"button",disabled:0===i.length||"disabled"===m,children:(()=>{let n=nL(e.content?.button,_);if(n)return n;switch(m){case"readying":return"Loading...";case"uploading":if(u>=100)return(0,s.jsx)(n8,{});return(0,s.jsxs)("span",{className:"z-50",children:[(0,s.jsxs)("span",{className:"block group-hover:hidden",children:[Math.round(u),"%"]}),(0,s.jsx)(n9,{cn:r,className:"hidden size-4 group-hover:block"})]});default:if("manual"===t&&i.length>0)return`Upload ${i.length} file${1===i.length?"":"s"}`;return`Choose File${h?"(s)":""}`}})()})]})}let rh=e=>{nm("@uploadthing/react",n1.uploadthing,nK);let t=nv(e?.url),n=e?.fetch??globalThis.fetch;return e=>(0,s.jsx)(re,{...e,url:t,fetch:n})},rg=e=>{nm("@uploadthing/react",n1.uploadthing,nK);let t=nv(e?.url),n=e?.fetch??globalThis.fetch;return e=>(0,s.jsx)(rf,{...e,url:t,fetch:n})}},62369:(e,t,n)=>{n.d(t,{b:()=>c});var r=n(43210),a=n(14163),s=n(60687),i="horizontal",o=["horizontal","vertical"],u=r.forwardRef((e,t)=>{var n;let{decorative:r,orientation:u=i,...c}=e,l=(n=u,o.includes(n))?u:i;return(0,s.jsx)(a.sG.div,{"data-orientation":l,...r?{role:"none"}:{"aria-orientation":"vertical"===l?l:void 0,role:"separator"},...c,ref:t})});u.displayName="Separator";var c=u},90270:(e,t,n)=>{n.d(t,{bL:()=>_,zi:()=>j});var r=n(43210),a=n(70569),s=n(98599),i=n(11273),o=n(65551),u=n(83721),c=n(18853),l=n(14163),d=n(60687),p="Switch",[f,h]=(0,i.A)(p),[g,m]=f(p),x=r.forwardRef((e,t)=>{let{__scopeSwitch:n,name:i,checked:u,defaultChecked:c,required:p,disabled:f,value:h="on",onCheckedChange:m,form:x,...v}=e,[b,_]=r.useState(null),j=(0,s.s)(t,e=>_(e)),k=r.useRef(!1),E=!b||x||!!b.closest("form"),[S=!1,O]=(0,o.i)({prop:u,defaultProp:c,onChange:m});return(0,d.jsxs)(g,{scope:n,checked:S,disabled:f,children:[(0,d.jsx)(l.sG.button,{type:"button",role:"switch","aria-checked":S,"aria-required":p,"data-state":w(S),"data-disabled":f?"":void 0,disabled:f,value:h,...v,ref:j,onClick:(0,a.m)(e.onClick,e=>{O(e=>!e),E&&(k.current=e.isPropagationStopped(),k.current||e.stopPropagation())})}),E&&(0,d.jsx)(y,{control:b,bubbles:!k.current,name:i,value:h,checked:S,required:p,disabled:f,form:x,style:{transform:"translateX(-100%)"}})]})});x.displayName=p;var v="SwitchThumb",b=r.forwardRef((e,t)=>{let{__scopeSwitch:n,...r}=e,a=m(v,n);return(0,d.jsx)(l.sG.span,{"data-state":w(a.checked),"data-disabled":a.disabled?"":void 0,...r,ref:t})});b.displayName=v;var y=e=>{let{control:t,checked:n,bubbles:a=!0,...s}=e,i=r.useRef(null),o=(0,u.Z)(n),l=(0,c.X)(t);return r.useEffect(()=>{let e=i.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(o!==n&&t){let r=new Event("click",{bubbles:a});t.call(e,n),e.dispatchEvent(r)}},[o,n,a]),(0,d.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:n,...s,tabIndex:-1,ref:i,style:{...e.style,...l,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function w(e){return e?"checked":"unchecked"}var _=x,j=b}};