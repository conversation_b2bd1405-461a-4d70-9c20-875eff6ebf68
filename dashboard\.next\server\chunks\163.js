"use strict";exports.id=163,exports.ids=[163],exports.modules={4068:(e,t,a)=>{a.d(t,{U:()=>f});var r=a(43210),s=a(59556);let n=e=>!!e&&/[\u0600-\u06FF]/.test(e),o=e=>"ar"===e.language||e.id&&e.id.includes("_ar")||e.name&&n(e.name)||e.content&&n(e.content)?"ar":"en",l=e=>"ar"===e||"en"===e?e:"en",i={en:[{id:"hello_world_en",name:"Hello World",content:"Hello, {{1}}! Welcome to our service.",category:"greeting",variables:["name"],language:"en",createdAt:new Date().toISOString()},{id:"appointment_reminder_en",name:"Appointment Reminder",content:"Hi {{1}}, this is a reminder about your appointment on {{2}} at {{3}}.",category:"reminder",variables:["name","date","time"],language:"en",createdAt:new Date().toISOString()}],ar:[{id:"hello_world_ar",name:"مرحبا بالعالم",content:"مرحبًا، {{1}}! مرحبًا بك في خدمتنا.",category:"greeting",variables:["name"],language:"ar",createdAt:new Date().toISOString()},{id:"appointment_reminder_ar",name:"تذكير بالموعد",content:"مرحبًا {{1}}، هذا تذكير بموعدك في {{2}} الساعة {{3}}.",category:"reminder",variables:["name","date","time"],language:"ar",createdAt:new Date().toISOString()}]},d=async(e="en")=>{try{if("all"===e)try{let e=await s.A.get("/marketing/templates",{params:{language:"en"}}),t=await s.A.get("/marketing/templates",{params:{language:"ar"}});return[...e,...t].map(e=>{let t=Array.isArray(e.variables)?e.variables:[],a=o(e);return{...e,language:a,variables:t,createdAt:e.createdAt||new Date().toISOString(),updatedAt:e.updatedAt||new Date().toISOString()}})}catch(e){if(e.message&&e.message.includes("Network Error"))return console.warn("Network error detected, using mock templates"),[...i.en,...i.ar];throw e}let t=l(e);try{return(await s.A.get("/marketing/templates",{params:{language:t}})).map(e=>{let t=Array.isArray(e.variables)?e.variables:[],a=o(e);return{...e,language:a,variables:t,createdAt:e.createdAt||new Date().toISOString(),updatedAt:e.updatedAt||new Date().toISOString()}})}catch(e){if(e.message&&e.message.includes("Network Error"))return console.warn("Network error detected, using mock templates"),i[t]||[];throw e}}catch(e){throw console.error("Error fetching templates:",e),e}},c=async e=>{try{let t=l(e.language),a={...e,language:t,name:e.name.trim(),variables:Array.isArray(e.variables)?e.variables:[]};return await s.A.post("/marketing/templates",a)}catch(e){throw console.error("Error creating template:",e),e}},u=async(e,t)=>{try{let a={...t};return t.language&&(a.language=l(t.language)),t.name&&(a.name=t.name.trim()),t.variables&&(a.variables=Array.isArray(t.variables)?t.variables:[]),await s.A.put(`/marketing/templates/${e}`,a)}catch(t){throw console.error(`Error updating template with ID ${e}:`,t),t}},m=async e=>{try{if(!e)throw console.error("Invalid template ID for deletion:",e),Error("Invalid template ID");let t=e.trim();return console.log(`Deleting template with ID: ${t}`),await s.A.delete(`/marketing/templates/${t}`),console.log(`Template with ID ${t} deleted successfully`),!0}catch(t){if(console.error(`Error deleting template with ID ${e}:`,t),t.response&&404===t.response.status)return console.warn(`Template with ID ${e} not found, it may have been already deleted`),!0;if(t.response&&t.response.data&&t.response.data.error)throw console.error(`Server error: ${t.response.data.error}`),Error(t.response.data.error);throw t}};var p=a(70333);function f(){let[e,t]=(0,r.useState)([]),[a,s]=(0,r.useState)(!0),[n,o]=(0,r.useState)(null),[l,i]=(0,r.useState)({}),[f,g]=(0,r.useState)(!1),[h,y]=(0,r.useState)(null),{toast:w}=(0,p.dj)(),b=(0,r.useCallback)(async()=>{try{s(!0),o(null);let[e,a]=await Promise.all([d("en").catch(()=>[]),d("ar").catch(()=>[])]),r=[...e,...a].sort((e,t)=>e.createdAt&&t.createdAt?new Date(t.createdAt).getTime()-new Date(e.createdAt).getTime():0);t(r)}catch(e){console.error("Error fetching templates:",e),o(e.message||"Failed to load templates"),t([])}finally{s(!1)}},[]),v=(0,r.useMemo)(()=>{let t=[...e];if(l.search){let e=l.search.toLowerCase();t=t.filter(t=>t.name.toLowerCase().includes(e)||t.content.toLowerCase().includes(e)||t.category&&t.category.toLowerCase().includes(e))}return l.category&&"all"!==l.category&&(t=t.filter(e=>e.category===l.category)),l.language&&"all"!==l.language&&(t=t.filter(e=>e.language===l.language)),t},[e,l]),x=(0,r.useCallback)(e=>{i(t=>({...t,...e}))},[]),A=(0,r.useCallback)(()=>{i({})},[]),N=(0,r.useCallback)(()=>{g(e=>!e)},[]),k=(0,r.useCallback)(()=>{b()},[b]),E=async e=>{try{s(!0);let a=await c(e);return t(e=>[a,...e]),w({title:"Success",description:"Template created successfully"}),!0}catch(e){return console.error("Error adding template:",e),o(e.message||"Failed to add template"),w({title:"Error",description:e.message||"Failed to create template",variant:"destructive"}),!1}finally{s(!1)}},j=async(e,a)=>{try{s(!0);let r=await u(e,a);return t(t=>t.map(t=>t.id===e?r:t)),w({title:"Success",description:"Template updated successfully"}),!0}catch(t){return console.error(`Error updating template with ID ${e}:`,t),o(t.message||"Failed to update template"),w({title:"Error",description:t.message||"Failed to update template",variant:"destructive"}),!1}finally{s(!1)}},I=async e=>{try{if(s(!0),o(null),await m(e))return t(t=>t.filter(t=>t.id!==e)),w({title:"Success",description:"Template deleted successfully"}),!0;throw Error("Failed to delete template")}catch(t){return console.error(`Error deleting template with ID ${e}:`,t),o(t.message||"Failed to delete template"),w({title:"Error",description:t.message||"Failed to delete template",variant:"destructive"}),!1}finally{s(!1)}},R=(0,r.useMemo)(()=>["all",...new Set(e.map(e=>e.category).filter(Boolean))],[e]),C=(0,r.useMemo)(()=>["all",...new Set(e.map(e=>e.language).filter(Boolean))],[e]);return{templates:v,allTemplates:e,loading:a,error:n,filters:l,updateFilters:x,clearFilters:A,categories:R,languages:C,autoRefresh:f,toggleAutoRefresh:N,refreshTemplates:k,addTemplate:E,editTemplate:j,removeTemplate:I,fetchTemplates:b,currentLanguage:"all",displayLanguage:"all",showAllLanguages:!0}}},8819:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},15616:(e,t,a)=>{a.d(t,{T:()=>o});var r=a(60687),s=a(43210),n=a(96241);let o=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...t}));o.displayName="Textarea"},28559:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},39390:(e,t,a)=>{a.d(t,{J:()=>d});var r=a(60687),s=a(43210),n=a(78148),o=a(24224),l=a(96241);let i=(0,o.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(n.b,{ref:a,className:(0,l.cn)(i(),e),...t}));d.displayName=n.b.displayName},41862:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},55192:(e,t,a)=>{a.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>i,Zp:()=>o,aR:()=>l,wL:()=>u});var r=a(60687),s=a(43210),n=a(96241);let o=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));o.displayName="Card";let l=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...t}));l.displayName="CardHeader";let i=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));i.displayName="CardTitle";let d=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,n.cn)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let c=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,n.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent";let u=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,n.cn)("flex items-center p-6 pt-0",e),...t}));u.displayName="CardFooter"},59556:(e,t,a)=>{a.d(t,{A:()=>n});var r=a(51060);class s{constructor(){this.isOfflineMode=!1,this.baseURL="http://localhost:5000/api/v1",this.baseURL||console.warn("NEXT_PUBLIC_BACKEND_API_URL is not defined. API requests may fail."),this.client=r.A.create({baseURL:this.baseURL,headers:{"Content-Type":"application/json"},timeout:1e4}),this.client.interceptors.request.use(e=>e,e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,e=>(e.response?(console.error("API Error Response:",e.response.status,e.response.data),e.response.data&&0!==Object.keys(e.response.data).length||(e.response.data={error:`Request failed with status code ${e.response.status}`}),404===e.response.status&&(console.log("Resource not found:",e.config?.url),e.response.data={error:"The requested resource was not found. Please refresh and try again."}),e.responseData=e.response.data):e.request?(console.error("API No Response:",e.request),e.message&&(e.message.includes("Network Error")||e.message.includes("timeout"))?(console.error("Network Error detected. Server might be down or unreachable."),this.setOfflineMode(!0),e.isOffline=!0,e.responseData={error:"Network Error: Unable to connect to the server. Please check your connection or try again later.",isOffline:!0},console.warn("API Client: Server connection failed. If you're running in development mode, make sure your backend server is running."),window.dispatchEvent(new CustomEvent("api:offline",{detail:{isOffline:!0}}))):e.responseData={error:"No response received from server. Please check your connection."}):(console.error("API Request Error:",e.message),e.responseData={error:e.message||"An unexpected error occurred"}),Promise.reject(e)))}async get(e,t){try{if(this.isOfflineMode&&!e.includes("/health"))throw console.log(`API Client: In offline mode, skipping GET request to ${e}`),Error("Network Error: Application is in offline mode");let a=await this.client.get(e,t);return this.isOfflineMode&&this.setOfflineMode(!1),a.data}catch(e){throw e}}async post(e,t,a){return(await this.client.post(e,t,a)).data}async put(e,t,a){return(await this.client.put(e,t,a)).data}async delete(e,t){try{console.log(`Making DELETE request to: ${e}`);let a=await this.client.delete(e,t);if(204===a.status)return console.log(`DELETE request to ${e} successful with 204 status`),null;return a.data}catch(t){throw console.error(`DELETE request to ${e} failed:`,t),t}}async patch(e,t,a){return(await this.client.patch(e,t,a)).data}async upload(e,t,a){let r={...a,headers:{...a?.headers,"Content-Type":"multipart/form-data"}};return(await this.client.post(e,t,r)).data}getBaseURL(){return this.baseURL}isInOfflineMode(){return this.isOfflineMode}setOfflineMode(e){this.isOfflineMode!==e&&(console.log(`API Client: Setting offline mode to ${e}`),this.isOfflineMode=e,window.dispatchEvent(new CustomEvent("api:offline",{detail:{isOffline:e}})))}async tryReconnect(){try{return console.log("API Client: Attempting to reconnect to the server..."),await this.client.get("/health",{timeout:5e3}),console.log("API Client: Reconnection successful"),this.setOfflineMode(!1),!0}catch(e){return console.log("API Client: Reconnection failed, still in offline mode"),!1}}}let n=new s},63974:(e,t,a)=>{a.d(t,{bq:()=>m,eb:()=>h,gC:()=>g,l6:()=>c,yv:()=>u});var r=a(60687),s=a(43210),n=a(22670),o=a(78272),l=a(3589),i=a(13964),d=a(96241);let c=n.bL;n.YJ;let u=n.WT,m=s.forwardRef(({className:e,children:t,...a},s)=>(0,r.jsxs)(n.l9,{ref:s,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...a,children:[t,(0,r.jsx)(n.In,{asChild:!0,children:(0,r.jsx)(o.A,{className:"h-4 w-4 opacity-50"})})]}));m.displayName=n.l9.displayName;let p=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(n.PP,{ref:a,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(l.A,{className:"h-4 w-4"})}));p.displayName=n.PP.displayName;let f=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(n.wn,{ref:a,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(o.A,{className:"h-4 w-4"})}));f.displayName=n.wn.displayName;let g=s.forwardRef(({className:e,children:t,position:a="popper",...s},o)=>(0,r.jsx)(n.ZL,{children:(0,r.jsxs)(n.UC,{ref:o,className:(0,d.cn)("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...s,children:[(0,r.jsx)(p,{}),(0,r.jsx)(n.LM,{className:(0,d.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,r.jsx)(f,{})]})}));g.displayName=n.UC.displayName,s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(n.JU,{ref:a,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=n.JU.displayName;let h=s.forwardRef(({className:e,children:t,...a},s)=>(0,r.jsxs)(n.q7,{ref:s,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...a,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(n.VF,{children:(0,r.jsx)(i.A,{className:"h-4 w-4"})})}),(0,r.jsx)(n.p4,{children:t})]}));h.displayName=n.q7.displayName,s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(n.wv,{ref:a,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=n.wv.displayName},68988:(e,t,a)=>{a.d(t,{p:()=>o});var r=a(60687),s=a(43210),n=a(96241);let o=s.forwardRef(({className:e,type:t,...a},s)=>(0,r.jsx)("input",{type:t,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:s,...a}));o.displayName="Input"},78148:(e,t,a)=>{a.d(t,{b:()=>l});var r=a(43210),s=a(14163),n=a(60687),o=r.forwardRef((e,t)=>(0,n.jsx)(s.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var l=o},96474:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};