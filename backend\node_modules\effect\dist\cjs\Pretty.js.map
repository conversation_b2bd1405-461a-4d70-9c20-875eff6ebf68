{"version": 3, "file": "Pretty.js", "names": ["Arr", "_interopRequireWildcard", "require", "errors_", "util_", "Option", "ParseResult", "AST", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "make", "schema", "compile", "ast", "exports", "getPrettyAnnotation", "getAnnotation", "PrettyAnnotationId", "getMatcher", "defaultPretty", "match", "onNone", "onSome", "handler", "toString", "String", "stringify", "JSON", "formatUnknown", "Declaration", "go", "path", "annotation", "isSome", "value", "typeParameters", "map", "tp", "Error", "getPrettyMissingAnnotationErrorMessage", "getPrettyNeverErrorMessage", "literal", "TupleType", "hook", "elements", "type", "concat", "rest", "annotatedAST", "input", "output", "length", "isOptional", "push", "isNonEmptyReadonlyArray", "head", "tail", "j", "join", "TypeLiteral", "propertySignaturesTypes", "propertySignatures", "ps", "name", "indexSignatureTypes", "indexSignatures", "is", "<PERSON><PERSON><PERSON><PERSON>", "prototype", "formatPropertyKey", "keys", "getKeysForIndexSignature", "parameter", "key", "Union", "types", "index", "findIndex", "getPrettyNoMatchingSchemaErrorMessage", "Suspend", "memoizeThunk", "f", "Refinement", "from", "Transformation", "to", "getCompiler"], "sources": ["../../src/Pretty.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAGA,IAAAA,GAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,OAAA,GAAAF,uBAAA,CAAAC,OAAA;AACA,IAAAE,KAAA,GAAAH,uBAAA,CAAAC,OAAA;AACA,IAAAG,MAAA,GAAAJ,uBAAA,CAAAC,OAAA;AACA,IAAAI,WAAA,GAAAL,uBAAA,CAAAC,OAAA;AAEA,IAAAK,GAAA,GAAAN,uBAAA,CAAAC,OAAA;AAAqC,SAAAM,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAR,wBAAAQ,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AATrC;;;;AA2BA;;;;AAIO,MAAMW,IAAI,GAAaC,MAA8B,IAAuBC,OAAO,CAACD,MAAM,CAACE,GAAG,EAAE,EAAE,CAAC;AAAAC,OAAA,CAAAJ,IAAA,GAAAA,IAAA;AAE1G,MAAMK,mBAAmB,gBAAG1B,GAAG,CAAC2B,aAAa,CAA6B3B,GAAG,CAAC4B,kBAAkB,CAAC;AAEjG,MAAMC,UAAU,GAAIC,aAA0B,IAAMN,GAAY,IAC9D1B,MAAM,CAACiC,KAAK,CAACL,mBAAmB,CAACF,GAAG,CAAC,EAAE;EACrCQ,MAAM,EAAEA,CAAA,KAAMF,aAAa;EAC3BG,MAAM,EAAGC,OAAO,IAAKA,OAAO;CAC7B,CAAC;AAEJ,MAAMC,QAAQ,gBAAGN,UAAU,CAAEjB,CAAC,IAAKwB,MAAM,CAACxB,CAAC,CAAC,CAAC;AAE7C,MAAMyB,SAAS,gBAAGR,UAAU,CAAEjB,CAAC,IAAK0B,IAAI,CAACD,SAAS,CAACzB,CAAC,CAAC,CAAC;AAEtD,MAAM2B,aAAa,gBAAGV,UAAU,CAAChC,KAAK,CAAC0C,aAAa,CAAC;AAErD;;;AAGO,MAAMR,KAAK,GAAAN,OAAA,CAAAM,KAAA,GAA2B;EAC3C,aAAa,EAAES,CAAChB,GAAG,EAAEiB,EAAE,EAAEC,IAAI,KAAI;IAC/B,MAAMC,UAAU,GAAGjB,mBAAmB,CAACF,GAAG,CAAC;IAC3C,IAAI1B,MAAM,CAAC8C,MAAM,CAACD,UAAU,CAAC,EAAE;MAC7B,OAAOA,UAAU,CAACE,KAAK,CAAC,GAAGrB,GAAG,CAACsB,cAAc,CAACC,GAAG,CAAEC,EAAE,IAAKP,EAAE,CAACO,EAAE,EAAEN,IAAI,CAAC,CAAC,CAAC;IAC1E;IACA,MAAM,IAAIO,KAAK,CAACrD,OAAO,CAACsD,sCAAsC,CAACR,IAAI,EAAElB,GAAG,CAAC,CAAC;EAC5E,CAAC;EACD,aAAa,eAAEK,UAAU,CAAC,MAAM,SAAS,CAAC;EAC1C,cAAc,eAAEA,UAAU,CAAC,MAAK;IAC9B,MAAM,IAAIoB,KAAK,CAACrD,OAAO,CAACuD,0BAA0B,CAAC;EACrD,CAAC,CAAC;EACF,SAAS,eAAEtB,UAAU,CAAEuB,OAAyB,IAC9C,OAAOA,OAAO,KAAK,QAAQ,GACzB,GAAGhB,MAAM,CAACgB,OAAO,CAAC,GAAG,GACrBd,IAAI,CAACD,SAAS,CAACe,OAAO,CAAC,CAC1B;EACD,eAAe,EAAEjB,QAAQ;EACzB,cAAc,EAAEA,QAAQ;EACxB,iBAAiB,EAAEE,SAAS;EAC5B,kBAAkB,EAAEF,QAAQ;EAC5B,gBAAgB,EAAEI,aAAa;EAC/B,YAAY,EAAEA,aAAa;EAC3B,eAAe,EAAEA,aAAa;EAC9B,eAAe,EAAEF,SAAS;EAC1B,eAAe,EAAEF,QAAQ;EACzB,gBAAgB,EAAEA,QAAQ;EAC1B,eAAe,eAAEN,UAAU,CAAEjB,CAAC,IAAK,GAAGwB,MAAM,CAACxB,CAAC,CAAC,GAAG,CAAC;EACnD,OAAO,EAAEyB,SAAS;EAClB,WAAW,EAAEgB,CAAC7B,GAAG,EAAEiB,EAAE,EAAEC,IAAI,KAAI;IAC7B,MAAMY,IAAI,GAAG5B,mBAAmB,CAACF,GAAG,CAAC;IACrC,IAAI1B,MAAM,CAAC8C,MAAM,CAACU,IAAI,CAAC,EAAE;MACvB,OAAOA,IAAI,CAACT,KAAK,EAAE;IACrB;IACA,MAAMU,QAAQ,GAAG/B,GAAG,CAAC+B,QAAQ,CAACR,GAAG,CAAC,CAAC7C,CAAC,EAAEiB,CAAC,KAAKsB,EAAE,CAACvC,CAAC,CAACsD,IAAI,EAAEd,IAAI,CAACe,MAAM,CAACtC,CAAC,CAAC,CAAC,CAAC;IACvE,MAAMuC,IAAI,GAAGlC,GAAG,CAACkC,IAAI,CAACX,GAAG,CAAEY,YAAY,IAAKlB,EAAE,CAACkB,YAAY,CAACH,IAAI,EAAEd,IAAI,CAAC,CAAC;IACxE,OAAQkB,KAA6B,IAAI;MACvC,MAAMC,MAAM,GAAkB,EAAE;MAChC,IAAI1C,CAAC,GAAG,CAAC;MACT;MACA;MACA;MACA,OAAOA,CAAC,GAAGoC,QAAQ,CAACO,MAAM,EAAE3C,CAAC,EAAE,EAAE;QAC/B,IAAIyC,KAAK,CAACE,MAAM,GAAG3C,CAAC,GAAG,CAAC,EAAE;UACxB,IAAIK,GAAG,CAAC+B,QAAQ,CAACpC,CAAC,CAAC,CAAC4C,UAAU,EAAE;YAC9B;UACF;QACF,CAAC,MAAM;UACLF,MAAM,CAACG,IAAI,CAACT,QAAQ,CAACpC,CAAC,CAAC,CAACyC,KAAK,CAACzC,CAAC,CAAC,CAAC,CAAC;QACpC;MACF;MACA;MACA;MACA;MACA,IAAI1B,GAAG,CAACwE,uBAAuB,CAACP,IAAI,CAAC,EAAE;QACrC,MAAM,CAACQ,IAAI,EAAE,GAAGC,IAAI,CAAC,GAAGT,IAAI;QAC5B,OAAOvC,CAAC,GAAGyC,KAAK,CAACE,MAAM,GAAGK,IAAI,CAACL,MAAM,EAAE3C,CAAC,EAAE,EAAE;UAC1C0C,MAAM,CAACG,IAAI,CAACE,IAAI,CAACN,KAAK,CAACzC,CAAC,CAAC,CAAC,CAAC;QAC7B;QACA;QACA;QACA;QACA,KAAK,IAAIiD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,IAAI,CAACL,MAAM,EAAEM,CAAC,EAAE,EAAE;UACpCjD,CAAC,IAAIiD,CAAC;UACNP,MAAM,CAACG,IAAI,CAACG,IAAI,CAACC,CAAC,CAAC,CAACR,KAAK,CAACzC,CAAC,CAAC,CAAC,CAAC;QAChC;MACF;MAEA,OAAO,GAAG,GAAG0C,MAAM,CAACQ,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG;IACtC,CAAC;EACH,CAAC;EACD,aAAa,EAAEC,CAAC9C,GAAG,EAAEiB,EAAE,EAAEC,IAAI,KAAI;IAC/B,MAAMY,IAAI,GAAG5B,mBAAmB,CAACF,GAAG,CAAC;IACrC,IAAI1B,MAAM,CAAC8C,MAAM,CAACU,IAAI,CAAC,EAAE;MACvB,OAAOA,IAAI,CAACT,KAAK,EAAE;IACrB;IACA,MAAM0B,uBAAuB,GAAG/C,GAAG,CAACgD,kBAAkB,CAACzB,GAAG,CAAE0B,EAAE,IAAKhC,EAAE,CAACgC,EAAE,CAACjB,IAAI,EAAEd,IAAI,CAACe,MAAM,CAACgB,EAAE,CAACC,IAAI,CAAC,CAAC,CAAC;IACrG,MAAMC,mBAAmB,GAAGnD,GAAG,CAACoD,eAAe,CAAC7B,GAAG,CAAE8B,EAAE,IAAKpC,EAAE,CAACoC,EAAE,CAACrB,IAAI,EAAEd,IAAI,CAAC,CAAC;IAC9E,MAAMoC,YAAY,GAAQ,EAAE;IAC5B,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoD,uBAAuB,CAACT,MAAM,EAAE3C,CAAC,EAAE,EAAE;MACvD2D,YAAY,CAACtD,GAAG,CAACgD,kBAAkB,CAACrD,CAAC,CAAC,CAACuD,IAAI,CAAC,GAAG,IAAI;IACrD;IACA,OAAQd,KAA6C,IAAI;MACvD,MAAMC,MAAM,GAAkB,EAAE;MAChC;MACA;MACA;MACA,KAAK,IAAI1C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoD,uBAAuB,CAACT,MAAM,EAAE3C,CAAC,EAAE,EAAE;QACvD,MAAMsD,EAAE,GAAGjD,GAAG,CAACgD,kBAAkB,CAACrD,CAAC,CAAC;QACpC,MAAMuD,IAAI,GAAGD,EAAE,CAACC,IAAI;QACpB,IAAID,EAAE,CAACV,UAAU,IAAI,CAAClD,MAAM,CAACkE,SAAS,CAAC9D,cAAc,CAACC,IAAI,CAAC0C,KAAK,EAAEc,IAAI,CAAC,EAAE;UACvE;QACF;QACAb,MAAM,CAACG,IAAI,CACT,GAAGnE,KAAK,CAACmF,iBAAiB,CAACN,IAAI,CAAC,KAAKH,uBAAuB,CAACpD,CAAC,CAAC,CAACyC,KAAK,CAACc,IAAI,CAAC,CAAC,EAAE,CAC/E;MACH;MACA;MACA;MACA;MACA,IAAIC,mBAAmB,CAACb,MAAM,GAAG,CAAC,EAAE;QAClC,KAAK,IAAI3C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwD,mBAAmB,CAACb,MAAM,EAAE3C,CAAC,EAAE,EAAE;UACnD,MAAMqC,IAAI,GAAGmB,mBAAmB,CAACxD,CAAC,CAAC;UACnC,MAAM8D,IAAI,GAAGpF,KAAK,CAACqF,wBAAwB,CAACtB,KAAK,EAAEpC,GAAG,CAACoD,eAAe,CAACzD,CAAC,CAAC,CAACgE,SAAS,CAAC;UACpF,KAAK,MAAMC,GAAG,IAAIH,IAAI,EAAE;YACtB,IAAIpE,MAAM,CAACkE,SAAS,CAAC9D,cAAc,CAACC,IAAI,CAAC4D,YAAY,EAAEM,GAAG,CAAC,EAAE;cAC3D;YACF;YACAvB,MAAM,CAACG,IAAI,CAAC,GAAGnE,KAAK,CAACmF,iBAAiB,CAACI,GAAG,CAAC,KAAK5B,IAAI,CAACI,KAAK,CAACwB,GAAG,CAAC,CAAC,EAAE,CAAC;UACrE;QACF;MACF;MAEA,OAAO3F,GAAG,CAACwE,uBAAuB,CAACJ,MAAM,CAAC,GAAG,IAAI,GAAGA,MAAM,CAACQ,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,IAAI;IACrF,CAAC;EACH,CAAC;EACD,OAAO,EAAEgB,CAAC7D,GAAG,EAAEiB,EAAE,EAAEC,IAAI,KAAI;IACzB,MAAMY,IAAI,GAAG5B,mBAAmB,CAACF,GAAG,CAAC;IACrC,IAAI1B,MAAM,CAAC8C,MAAM,CAACU,IAAI,CAAC,EAAE;MACvB,OAAOA,IAAI,CAACT,KAAK,EAAE;IACrB;IACA,MAAMyC,KAAK,GAAG9D,GAAG,CAAC8D,KAAK,CAACvC,GAAG,CAAEvB,GAAG,IAAK,CAACzB,WAAW,CAAC8E,EAAE,CAAC;MAAErD;IAAG,CAAS,CAAC,EAAEiB,EAAE,CAACjB,GAAG,EAAEkB,IAAI,CAAC,CAAU,CAAC;IAC9F,OAAQ9B,CAAC,IAAI;MACX,MAAM2E,KAAK,GAAGD,KAAK,CAACE,SAAS,CAAC,CAAC,CAACX,EAAE,CAAC,KAAKA,EAAE,CAACjE,CAAC,CAAC,CAAC;MAC9C,IAAI2E,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB,MAAM,IAAItC,KAAK,CAACrD,OAAO,CAAC6F,qCAAqC,CAAC7E,CAAC,EAAE8B,IAAI,EAAElB,GAAG,CAAC,CAAC;MAC9E;MACA,OAAO8D,KAAK,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC3E,CAAC,CAAC;IAC3B,CAAC;EACH,CAAC;EACD,SAAS,EAAE8E,CAAClE,GAAG,EAAEiB,EAAE,EAAEC,IAAI,KAAI;IAC3B,OAAO5C,MAAM,CAACiC,KAAK,CAACL,mBAAmB,CAACF,GAAG,CAAC,EAAE;MAC5CQ,MAAM,EAAEA,CAAA,KAAK;QACX,MAAMvB,GAAG,GAAGZ,KAAK,CAAC8F,YAAY,CAAC,MAAMlD,EAAE,CAACjB,GAAG,CAACoE,CAAC,EAAE,EAAElD,IAAI,CAAC,CAAC;QACvD,OAAQ9B,CAAC,IAAKH,GAAG,EAAE,CAACG,CAAC,CAAC;MACxB,CAAC;MACDqB,MAAM,EAAGC,OAAO,IAAKA,OAAO;KAC7B,CAAC;EACJ,CAAC;EACD,YAAY,EAAE2D,CAACrE,GAAG,EAAEiB,EAAE,EAAEC,IAAI,KAAI;IAC9B,OAAO5C,MAAM,CAACiC,KAAK,CAACL,mBAAmB,CAACF,GAAG,CAAC,EAAE;MAC5CQ,MAAM,EAAEA,CAAA,KAAMS,EAAE,CAACjB,GAAG,CAACsE,IAAI,EAAEpD,IAAI,CAAC;MAChCT,MAAM,EAAGC,OAAO,IAAKA,OAAO;KAC7B,CAAC;EACJ,CAAC;EACD,gBAAgB,EAAE6D,CAACvE,GAAG,EAAEiB,EAAE,EAAEC,IAAI,KAAI;IAClC,OAAO5C,MAAM,CAACiC,KAAK,CAACL,mBAAmB,CAACF,GAAG,CAAC,EAAE;MAC5CQ,MAAM,EAAEA,CAAA,KAAMS,EAAE,CAACjB,GAAG,CAACwE,EAAE,EAAEtD,IAAI,CAAC;MAC9BT,MAAM,EAAGC,OAAO,IAAKA,OAAO;KAC7B,CAAC;EACJ;CACD;AAED,MAAMX,OAAO,gBAAGvB,GAAG,CAACiG,WAAW,CAAClE,KAAK,CAAC", "ignoreList": []}