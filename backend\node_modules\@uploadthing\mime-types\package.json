{"name": "@uploadthing/mime-types", "version": "0.3.5", "type": "module", "sideEffects": false, "license": "MIT", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "./application": {"import": {"types": "./application/index.d.ts", "default": "./application/index.js"}, "require": {"types": "./application/index.d.cts", "default": "./application/index.cjs"}}, "./audio": {"import": {"types": "./audio/index.d.ts", "default": "./audio/index.js"}, "require": {"types": "./audio/index.d.cts", "default": "./audio/index.cjs"}}, "./image": {"import": {"types": "./image/index.d.ts", "default": "./image/index.js"}, "require": {"types": "./image/index.d.cts", "default": "./image/index.cjs"}}, "./text": {"import": {"types": "./text/index.d.ts", "default": "./text/index.js"}, "require": {"types": "./text/index.d.cts", "default": "./text/index.cjs"}}, "./video": {"import": {"types": "./video/index.d.ts", "default": "./video/index.js"}, "require": {"types": "./video/index.d.cts", "default": "./video/index.cjs"}}}, "files": ["application", "audio", "dist", "image", "text", "video"], "publishConfig": {"access": "public"}}