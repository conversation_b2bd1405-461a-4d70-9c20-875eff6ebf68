{"version": 3, "file": "sqids.js", "mappings": "CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,QAAS,GAAIH,GACM,iBAAZC,QACdA,QAAe,MAAID,IAEnBD,EAAY,MAAIC,GACjB,CATD,CASGK,MAAM,I,mBCRT,IAAIC,EAAsB,CCA1BA,EAAwB,CAACL,EAASM,KACjC,IAAI,IAAIC,KAAOD,EACXD,EAAoBG,EAAEF,EAAYC,KAASF,EAAoBG,EAAER,EAASO,IAC5EE,OAAOC,eAAeV,EAASO,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,IAE1E,ECNDF,EAAwB,CAACQ,EAAKC,IAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,I,4BCM3E,MAAMI,EAAiB,CAC5BC,SAAU,iEACVC,UAAW,EACXC,UAAW,IAAIC,IAAY,CACzB,SACA,QACA,QACA,QACA,QACA,YACA,YACA,YACA,YACA,WACA,WACA,WACA,WACA,WACA,WACA,WACA,WACA,OACA,QACA,QACA,QACA,QACA,WACA,WACA,WACA,WACA,OACA,QACA,OACA,QACA,OACA,WACA,WACA,QACA,OACA,MACA,OACA,QACA,SACA,SACA,SACA,OACA,QACA,QACA,QACA,OACA,QACA,WACA,WACA,WACA,WACA,UACA,UACA,QACA,OACA,QACA,OACA,QACA,SACA,SACA,SACA,OACA,QACA,QACA,WACA,UACA,YACA,WACA,YACA,WACA,UACA,YACA,WACA,YACA,OACA,WACA,WACA,WACA,WACA,WACA,WACA,WACA,WACA,OACA,UACA,WACA,QACA,WACA,WACA,WACA,WACA,WACA,WACA,WACA,WACA,OACA,QACA,QACA,OACA,QACA,QACA,SACA,SACA,SACA,SACA,OACA,QACA,SACA,UACA,QACA,SACA,QACA,UACA,UACA,WACA,WACA,WACA,WACA,UACA,UACA,QACA,WACA,UACA,WACA,QACA,UACA,WACA,UACA,WACA,OACA,UACA,WACA,UACA,WACA,UACA,WACA,QACA,SACA,cACA,cACA,eACA,eACA,UACA,SACA,WACA,QACA,SACA,UACA,WACA,QACA,SACA,cACA,cACA,eACA,eACA,UACA,SACA,WACA,QACA,UACA,WACA,UACA,WACA,OACA,UACA,WACA,UACA,WACA,OACA,QACA,QACA,OACA,QACA,QACA,OACA,WACA,WACA,WACA,WACA,WACA,WACA,WACA,WACA,OACA,UACA,WACA,QACA,WACA,WACA,WACA,WACA,WACA,WACA,WACA,WACA,UACA,OACA,OACA,YACA,YACA,SACA,SACA,OACA,OACA,YACA,YACA,SACA,SACA,OACA,MACA,OACA,QACA,QACA,OACA,QACA,QACA,OACA,QACA,QACA,OACA,QACA,QACA,OACA,QACA,QACA,OACA,SACA,SACA,QACA,UACA,UACA,UACA,UACA,WACA,WACA,WACA,WACA,QACA,QACA,OACA,QACA,UACA,WACA,WACA,QACA,SACA,OACA,SACA,OACA,MACA,OACA,SACA,OACA,OACA,QACA,UACA,WACA,WACA,QACA,SACA,SACA,SACA,SACA,SACA,UACA,UACA,UACA,UACA,SACA,SACA,SACA,SACA,UACA,UACA,UACA,UACA,OACA,MACA,MACA,SACA,SACA,QACA,MACA,MACA,SACA,SACA,YACA,YACA,SACA,SACA,YACA,SACA,SACA,QACA,QACA,QACA,QACA,WACA,WACA,WACA,WACA,OACA,OACA,OACA,OACA,SACA,SACA,OACA,YACA,YACA,YACA,YACA,SACA,WACA,WACA,UACA,UACA,OACA,QACA,YACA,YACA,QACA,eACA,eACA,aACA,eACA,eACA,UACA,UACA,QACA,QACA,UACA,UACA,SACA,WACA,WACA,UACA,UACA,OACA,SACA,SACA,SACA,QACA,QACA,QACA,QACA,UACA,UACA,SACA,SACA,OACA,QACA,QACA,QACA,QACA,UACA,UACA,UACA,UACA,OACA,QACA,OACA,QACA,aACA,aACA,OACA,OACA,QACA,QACA,SACA,SACA,SACA,SACA,SACA,QACA,QACA,QACA,QACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,YACA,UACA,WACA,WACA,WACA,WACA,QACA,UACA,UACA,QACA,OACA,OACA,QACA,QACA,SACA,SACA,SACA,SACA,SACA,OACA,QACA,QACA,QACA,QACA,UACA,UACA,UACA,UACA,OACA,QACA,OACA,QACA,aACA,aACA,QACA,QACA,QACA,SACA,OACA,SACA,SACA,OACA,SACA,UACA,QACA,aACA,aACA,aACA,aACA,aACA,aACA,aACA,aACA,QACA,QACA,OACA,YACA,YACA,YACA,YACA,SACA,aACA,aACA,aACA,aACA,aACA,aACA,aACA,aACA,WACA,WACA,WACA,WACA,OACA,SACA,SACA,SACA,YACA,SACA,SACA,SACA,WACA,WACA,SACA,WACA,WACA,YACA,YACA,WACA,SACA,WACA,UACA,UACA,WACA,UACA,WACA,UACA,WACA,WACA,gBACA,gBACA,gBACA,gBACA,UACA,UACA,UACA,OACA,OACA,OACA,OACA,aACA,aACA,aACA,aACA,UACA,UACA,UACA,UACA,UACA,UACA,SACA,SACA,YACA,YACA,YACA,YACA,SACA,OACA,UACA,WACA,WACA,WACA,WACA,QACA,OACA,QACA,QACA,WACA,WACA,WACA,WACA,WACA,QACA,QACA,WACA,OACA,OACA,aACA,aACA,aACA,aACA,SACA,SACA,WACA,QACA,UACA,OACA,UACA,UACA,UACA,OACA,UACA,UACA,UACA,UACA,UACA,UACA,OACA,OACA,OACA,OACA,UACA,UACA,UACA,aAIW,MAAMC,EAKnB,WAAAC,CAAYC,G,UACV,MAAMN,EAA4B,QAAjB,EAAAM,aAAO,EAAPA,EAASN,gBAAQ,QAAID,EAAeC,SAC/CC,EAA8B,QAAlB,EAAAK,aAAO,EAAPA,EAASL,iBAAS,QAAIF,EAAeE,UACjDC,EAA8B,QAAlB,EAAAI,aAAO,EAAPA,EAASJ,iBAAS,QAAIH,EAAeG,UAEvD,GAAI,IAAIK,KAAK,CAACP,IAAWQ,OAASR,EAASS,OACzC,MAAM,IAAIC,MAAM,gDAIlB,GAAIV,EAASS,OADa,EAExB,MAAM,IAAIC,MAAM,sCAGlB,GAAI,IAAIP,IAAIH,GAAUQ,OAASR,EAASS,OACtC,MAAM,IAAIC,MAAM,2CAIlB,GACuB,iBAAdT,GACPA,EAAY,GACZA,EAJqB,IAMrB,MAAM,IAAIS,MACR,8CAIJ,MAAMC,EAAoB,IAAIR,IACxBS,EAAgBZ,EAASa,cAAcC,MAAM,IACnD,IAAK,MAAMC,KAAQb,EACjB,GAAIa,EAAKN,QAAU,EAAG,CACpB,MAAMO,EAAiBD,EAAKF,cACtBI,EAAYD,EAAeF,MAAM,IAClBG,EAAUC,QAAQC,GAAMP,EAAcQ,SAASD,KACnDV,SAAWQ,EAAUR,QACpCE,EAAkBU,IAAIL,E,CAK5BM,KAAKtB,SAAWsB,KAAKC,QAAQvB,GAC7BsB,KAAKrB,UAAYA,EACjBqB,KAAKpB,UAAYS,CACnB,CAEA,MAAAa,CAAOC,GACL,GAAuB,IAAnBA,EAAQhB,OACV,MAAO,GAIT,GADuBgB,EAAQP,QAAQQ,GAAMA,GAAK,GAAKA,GAAKJ,KAAKK,aAC9ClB,SAAWgB,EAAQhB,OACpC,MAAM,IAAIC,MACR,2CAA2CY,KAAKK,cAIpD,OAAOL,KAAKM,cAAcH,EAC5B,CAEA,MAAAI,CAAOC,GACL,MAAMC,EAAgB,GAEtB,GAAW,KAAPD,EACF,OAAOC,EAGT,MAAMnB,EAAgBU,KAAKtB,SAASc,MAAM,IAC1C,IAAK,MAAMK,KAAKW,EAAGhB,MAAM,IACvB,IAAKF,EAAcQ,SAASD,GAC1B,OAAOY,EAIX,MAAMC,EAASF,EAAGG,OAAO,GACnBC,EAASZ,KAAKtB,SAASmC,QAAQH,GACrC,IAAIhC,EAAWsB,KAAKtB,SAASoC,MAAMF,GAAUZ,KAAKtB,SAASoC,MAAM,EAAGF,GACpElC,EAAWA,EAASc,MAAM,IAAIuB,UAAUC,KAAK,IAC7C,IAAIC,EAAWT,EAAGM,MAAM,GAExB,KAAOG,EAAS9B,OAAS,GAAG,CAC1B,MAAM+B,EAAYxC,EAASoC,MAAM,EAAG,GAE9BK,EAASF,EAASzB,MAAM0B,GAC9B,GAAIC,EAAOhC,OAAS,EAAG,CACrB,GAAkB,KAAdgC,EAAO,GACT,OAAOV,EAGTA,EAAIW,KAAKpB,KAAKqB,SAASF,EAAO,GAAKzC,EAASoC,MAAM,KAC9CK,EAAOhC,OAAS,IAClBT,EAAWsB,KAAKC,QAAQvB,G,CAI5BuC,EAAWE,EAAOL,MAAM,GAAGE,KAAKE,E,CAGlC,OAAOT,CACT,CAEQ,aAAAH,CAAcH,EAAmBmB,EAAY,GACnD,GAAIA,EAAYtB,KAAKtB,SAASS,OAC5B,MAAM,IAAIC,MAAM,8CAGlB,IAAIwB,EACFT,EAAQoB,QACN,CAACC,EAAGC,EAAGC,IACL1B,KAAKtB,SAAS+C,EAAIzB,KAAKtB,SAASS,QAASwC,YAAY,GAAMD,EAAIF,GACjErB,EAAQhB,QACNa,KAAKtB,SAASS,OAEpByB,GAAUA,EAASU,GAAatB,KAAKtB,SAASS,OAC9C,IAAIT,EAAWsB,KAAKtB,SAASoC,MAAMF,GAAUZ,KAAKtB,SAASoC,MAAM,EAAGF,GACpE,MAAMF,EAAShC,EAASiC,OAAO,GAC/BjC,EAAWA,EAASc,MAAM,IAAIuB,UAAUC,KAAK,IAC7C,MAAMP,EAAM,CAACC,GAEb,IAAK,IAAIgB,EAAI,EAAGA,IAAMvB,EAAQhB,OAAQuC,IAAK,CACzC,MAAME,EAAMzB,EAAQuB,GAEpBjB,EAAIW,KAAKpB,KAAK6B,KAAKD,EAAKlD,EAASoC,MAAM,KACnCY,EAAIvB,EAAQhB,OAAS,IACvBsB,EAAIW,KAAK1C,EAASoC,MAAM,EAAG,IAC3BpC,EAAWsB,KAAKC,QAAQvB,G,CAI5B,IAAI8B,EAAKC,EAAIO,KAAK,IAElB,GAAIhB,KAAKrB,UAAY6B,EAAGrB,OAGtB,IAFAqB,GAAM9B,EAASoC,MAAM,EAAG,GAEjBd,KAAKrB,UAAY6B,EAAGrB,OAAS,GAClCT,EAAWsB,KAAKC,QAAQvB,GACxB8B,GAAM9B,EAASoC,MACb,EACAgB,KAAKC,IAAI/B,KAAKrB,UAAY6B,EAAGrB,OAAQT,EAASS,SASpD,OAJIa,KAAKgC,YAAYxB,KACnBA,EAAKR,KAAKM,cAAcH,EAASmB,EAAY,IAGxCd,CACT,CAEQ,OAAAP,CAAQvB,GACd,MAAMuD,EAAQvD,EAASc,MAAM,IAE7B,IAAK,IAAIkC,EAAI,EAAGQ,EAAID,EAAM9C,OAAS,EAAG+C,EAAI,EAAGR,IAAKQ,IAAK,CACrD,MAAMC,GACHT,EAAIQ,EAAID,EAAMP,GAAIC,YAAY,GAAMM,EAAMC,GAAIP,YAAY,IAC3DM,EAAM9C,QACN8C,EAAMP,GAAIO,EAAME,IAAM,CAACF,EAAME,GAAKF,EAAMP,G,CAG5C,OAAOO,EAAMjB,KAAK,GACpB,CAEQ,IAAAa,CAAKD,EAAalD,GACxB,MAAM8B,EAAK,GACLyB,EAAQvD,EAASc,MAAM,IAE7B,IAAI4C,EAASR,EAEb,GACEpB,EAAG6B,QAAQJ,EAAMG,EAASH,EAAM9C,SAChCiD,EAASN,KAAKQ,MAAMF,EAASH,EAAM9C,cAC5BiD,EAAS,GAElB,OAAO5B,EAAGQ,KAAK,GACjB,CAEQ,QAAAK,CAASb,EAAY9B,GAC3B,MAAMuD,EAAQvD,EAASc,MAAM,IAC7B,OAAOgB,EAAGhB,MAAM,IAAI+B,QAAO,CAACC,EAAGC,IAAMD,EAAIS,EAAM9C,OAAS8C,EAAMpB,QAAQY,IAAI,EAC5E,CAEQ,WAAAO,CAAYxB,GAClB,MAAM+B,EAAc/B,EAAGjB,cAEvB,IAAK,MAAME,KAAQO,KAAKpB,UACtB,GAAIa,EAAKN,QAAUoD,EAAYpD,OAC7B,GAAIoD,EAAYpD,QAAU,GAAKM,EAAKN,QAAU,GAC5C,GAAIoD,IAAgB9C,EAClB,OAAO,OAEJ,GAAI,KAAK+C,KAAK/C,IACnB,GAAI8C,EAAYE,WAAWhD,IAAS8C,EAAYG,SAASjD,GACvD,OAAO,OAEJ,GAAI8C,EAAYzC,SAASL,GAC9B,OAAO,EAKb,OAAO,CACT,CAEQ,QAAAY,GACN,OAAOsC,OAAOC,gBAChB,E", "sources": ["webpack://Sqids/webpack/universalModuleDefinition", "webpack://Sqids/webpack/bootstrap", "webpack://Sqids/webpack/runtime/define property getters", "webpack://Sqids/webpack/runtime/hasOwnProperty shorthand", "webpack://Sqids/./src/sqids.ts"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"Sqids\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Sqids\"] = factory();\n\telse\n\t\troot[\"Sqids\"] = factory();\n})(self, () => {\nreturn ", "// The require scope\nvar __webpack_require__ = {};\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "interface SqidsOptions {\n  alphabet?: string\n  minLength?: number\n  blocklist?: Set<string>\n}\n\nexport const defaultOptions = {\n  alphabet: 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789',\n  minLength: 0,\n  blocklist: new Set<string>([\n    '0rgasm',\n    '1d10t',\n    '1d1ot',\n    '1di0t',\n    '1diot',\n    '1eccacu10',\n    '1eccacu1o',\n    '1eccacul0',\n    '1eccaculo',\n    '1mbec11e',\n    '1mbec1le',\n    '1mbeci1e',\n    '1mbecile',\n    'a11upat0',\n    'a11upato',\n    'a1lupat0',\n    'a1lupato',\n    'aand',\n    'ah01e',\n    'ah0le',\n    'aho1e',\n    'ahole',\n    'al1upat0',\n    'al1upato',\n    'allupat0',\n    'allupato',\n    'ana1',\n    'ana1e',\n    'anal',\n    'anale',\n    'anus',\n    'arrapat0',\n    'arrapato',\n    'arsch',\n    'arse',\n    'ass',\n    'b00b',\n    'b00be',\n    'b01ata',\n    'b0ceta',\n    'b0iata',\n    'b0ob',\n    'b0obe',\n    'b0sta',\n    'b1tch',\n    'b1te',\n    'b1tte',\n    'ba1atkar',\n    'balatkar',\n    'bastard0',\n    'bastardo',\n    'batt0na',\n    'battona',\n    'bitch',\n    'bite',\n    'bitte',\n    'bo0b',\n    'bo0be',\n    'bo1ata',\n    'boceta',\n    'boiata',\n    'boob',\n    'boobe',\n    'bosta',\n    'bran1age',\n    'bran1er',\n    'bran1ette',\n    'bran1eur',\n    'bran1euse',\n    'branlage',\n    'branler',\n    'branlette',\n    'branleur',\n    'branleuse',\n    'c0ck',\n    'c0g110ne',\n    'c0g11one',\n    'c0g1i0ne',\n    'c0g1ione',\n    'c0gl10ne',\n    'c0gl1one',\n    'c0gli0ne',\n    'c0glione',\n    'c0na',\n    'c0nnard',\n    'c0nnasse',\n    'c0nne',\n    'c0u111es',\n    'c0u11les',\n    'c0u1l1es',\n    'c0u1lles',\n    'c0ui11es',\n    'c0ui1les',\n    'c0uil1es',\n    'c0uilles',\n    'c11t',\n    'c11t0',\n    'c11to',\n    'c1it',\n    'c1it0',\n    'c1ito',\n    'cabr0n',\n    'cabra0',\n    'cabrao',\n    'cabron',\n    'caca',\n    'cacca',\n    'cacete',\n    'cagante',\n    'cagar',\n    'cagare',\n    'cagna',\n    'cara1h0',\n    'cara1ho',\n    'caracu10',\n    'caracu1o',\n    'caracul0',\n    'caraculo',\n    'caralh0',\n    'caralho',\n    'cazz0',\n    'cazz1mma',\n    'cazzata',\n    'cazzimma',\n    'cazzo',\n    'ch00t1a',\n    'ch00t1ya',\n    'ch00tia',\n    'ch00tiya',\n    'ch0d',\n    'ch0ot1a',\n    'ch0ot1ya',\n    'ch0otia',\n    'ch0otiya',\n    'ch1asse',\n    'ch1avata',\n    'ch1er',\n    'ch1ng0',\n    'ch1ngadaz0s',\n    'ch1ngadazos',\n    'ch1ngader1ta',\n    'ch1ngaderita',\n    'ch1ngar',\n    'ch1ngo',\n    'ch1ngues',\n    'ch1nk',\n    'chatte',\n    'chiasse',\n    'chiavata',\n    'chier',\n    'ching0',\n    'chingadaz0s',\n    'chingadazos',\n    'chingader1ta',\n    'chingaderita',\n    'chingar',\n    'chingo',\n    'chingues',\n    'chink',\n    'cho0t1a',\n    'cho0t1ya',\n    'cho0tia',\n    'cho0tiya',\n    'chod',\n    'choot1a',\n    'choot1ya',\n    'chootia',\n    'chootiya',\n    'cl1t',\n    'cl1t0',\n    'cl1to',\n    'clit',\n    'clit0',\n    'clito',\n    'cock',\n    'cog110ne',\n    'cog11one',\n    'cog1i0ne',\n    'cog1ione',\n    'cogl10ne',\n    'cogl1one',\n    'cogli0ne',\n    'coglione',\n    'cona',\n    'connard',\n    'connasse',\n    'conne',\n    'cou111es',\n    'cou11les',\n    'cou1l1es',\n    'cou1lles',\n    'coui11es',\n    'coui1les',\n    'couil1es',\n    'couilles',\n    'cracker',\n    'crap',\n    'cu10',\n    'cu1att0ne',\n    'cu1attone',\n    'cu1er0',\n    'cu1ero',\n    'cu1o',\n    'cul0',\n    'culatt0ne',\n    'culattone',\n    'culer0',\n    'culero',\n    'culo',\n    'cum',\n    'cunt',\n    'd11d0',\n    'd11do',\n    'd1ck',\n    'd1ld0',\n    'd1ldo',\n    'damn',\n    'de1ch',\n    'deich',\n    'depp',\n    'di1d0',\n    'di1do',\n    'dick',\n    'dild0',\n    'dildo',\n    'dyke',\n    'encu1e',\n    'encule',\n    'enema',\n    'enf01re',\n    'enf0ire',\n    'enfo1re',\n    'enfoire',\n    'estup1d0',\n    'estup1do',\n    'estupid0',\n    'estupido',\n    'etr0n',\n    'etron',\n    'f0da',\n    'f0der',\n    'f0ttere',\n    'f0tters1',\n    'f0ttersi',\n    'f0tze',\n    'f0utre',\n    'f1ca',\n    'f1cker',\n    'f1ga',\n    'fag',\n    'fica',\n    'ficker',\n    'figa',\n    'foda',\n    'foder',\n    'fottere',\n    'fotters1',\n    'fottersi',\n    'fotze',\n    'foutre',\n    'fr0c10',\n    'fr0c1o',\n    'fr0ci0',\n    'fr0cio',\n    'fr0sc10',\n    'fr0sc1o',\n    'fr0sci0',\n    'fr0scio',\n    'froc10',\n    'froc1o',\n    'froci0',\n    'frocio',\n    'frosc10',\n    'frosc1o',\n    'frosci0',\n    'froscio',\n    'fuck',\n    'g00',\n    'g0o',\n    'g0u1ne',\n    'g0uine',\n    'gandu',\n    'go0',\n    'goo',\n    'gou1ne',\n    'gouine',\n    'gr0gnasse',\n    'grognasse',\n    'haram1',\n    'harami',\n    'haramzade',\n    'hund1n',\n    'hundin',\n    'id10t',\n    'id1ot',\n    'idi0t',\n    'idiot',\n    'imbec11e',\n    'imbec1le',\n    'imbeci1e',\n    'imbecile',\n    'j1zz',\n    'jerk',\n    'jizz',\n    'k1ke',\n    'kam1ne',\n    'kamine',\n    'kike',\n    'leccacu10',\n    'leccacu1o',\n    'leccacul0',\n    'leccaculo',\n    'm1erda',\n    'm1gn0tta',\n    'm1gnotta',\n    'm1nch1a',\n    'm1nchia',\n    'm1st',\n    'mam0n',\n    'mamahuev0',\n    'mamahuevo',\n    'mamon',\n    'masturbat10n',\n    'masturbat1on',\n    'masturbate',\n    'masturbati0n',\n    'masturbation',\n    'merd0s0',\n    'merd0so',\n    'merda',\n    'merde',\n    'merdos0',\n    'merdoso',\n    'mierda',\n    'mign0tta',\n    'mignotta',\n    'minch1a',\n    'minchia',\n    'mist',\n    'musch1',\n    'muschi',\n    'n1gger',\n    'neger',\n    'negr0',\n    'negre',\n    'negro',\n    'nerch1a',\n    'nerchia',\n    'nigger',\n    'orgasm',\n    'p00p',\n    'p011a',\n    'p01la',\n    'p0l1a',\n    'p0lla',\n    'p0mp1n0',\n    'p0mp1no',\n    'p0mpin0',\n    'p0mpino',\n    'p0op',\n    'p0rca',\n    'p0rn',\n    'p0rra',\n    'p0uff1asse',\n    'p0uffiasse',\n    'p1p1',\n    'p1pi',\n    'p1r1a',\n    'p1rla',\n    'p1sc10',\n    'p1sc1o',\n    'p1sci0',\n    'p1scio',\n    'p1sser',\n    'pa11e',\n    'pa1le',\n    'pal1e',\n    'palle',\n    'pane1e1r0',\n    'pane1e1ro',\n    'pane1eir0',\n    'pane1eiro',\n    'panele1r0',\n    'panele1ro',\n    'paneleir0',\n    'paneleiro',\n    'patakha',\n    'pec0r1na',\n    'pec0rina',\n    'pecor1na',\n    'pecorina',\n    'pen1s',\n    'pendej0',\n    'pendejo',\n    'penis',\n    'pip1',\n    'pipi',\n    'pir1a',\n    'pirla',\n    'pisc10',\n    'pisc1o',\n    'pisci0',\n    'piscio',\n    'pisser',\n    'po0p',\n    'po11a',\n    'po1la',\n    'pol1a',\n    'polla',\n    'pomp1n0',\n    'pomp1no',\n    'pompin0',\n    'pompino',\n    'poop',\n    'porca',\n    'porn',\n    'porra',\n    'pouff1asse',\n    'pouffiasse',\n    'pr1ck',\n    'prick',\n    'pussy',\n    'put1za',\n    'puta',\n    'puta1n',\n    'putain',\n    'pute',\n    'putiza',\n    'puttana',\n    'queca',\n    'r0mp1ba11e',\n    'r0mp1ba1le',\n    'r0mp1bal1e',\n    'r0mp1balle',\n    'r0mpiba11e',\n    'r0mpiba1le',\n    'r0mpibal1e',\n    'r0mpiballe',\n    'rand1',\n    'randi',\n    'rape',\n    'recch10ne',\n    'recch1one',\n    'recchi0ne',\n    'recchione',\n    'retard',\n    'romp1ba11e',\n    'romp1ba1le',\n    'romp1bal1e',\n    'romp1balle',\n    'rompiba11e',\n    'rompiba1le',\n    'rompibal1e',\n    'rompiballe',\n    'ruff1an0',\n    'ruff1ano',\n    'ruffian0',\n    'ruffiano',\n    's1ut',\n    'sa10pe',\n    'sa1aud',\n    'sa1ope',\n    'sacanagem',\n    'sal0pe',\n    'salaud',\n    'salope',\n    'saugnapf',\n    'sb0rr0ne',\n    'sb0rra',\n    'sb0rrone',\n    'sbattere',\n    'sbatters1',\n    'sbattersi',\n    'sborr0ne',\n    'sborra',\n    'sborrone',\n    'sc0pare',\n    'sc0pata',\n    'sch1ampe',\n    'sche1se',\n    'sche1sse',\n    'scheise',\n    'scheisse',\n    'schlampe',\n    'schwachs1nn1g',\n    'schwachs1nnig',\n    'schwachsinn1g',\n    'schwachsinnig',\n    'schwanz',\n    'scopare',\n    'scopata',\n    'sexy',\n    'sh1t',\n    'shit',\n    'slut',\n    'sp0mp1nare',\n    'sp0mpinare',\n    'spomp1nare',\n    'spompinare',\n    'str0nz0',\n    'str0nza',\n    'str0nzo',\n    'stronz0',\n    'stronza',\n    'stronzo',\n    'stup1d',\n    'stupid',\n    'succh1am1',\n    'succh1ami',\n    'succhiam1',\n    'succhiami',\n    'sucker',\n    't0pa',\n    'tapette',\n    'test1c1e',\n    'test1cle',\n    'testic1e',\n    'testicle',\n    'tette',\n    'topa',\n    'tr01a',\n    'tr0ia',\n    'tr0mbare',\n    'tr1ng1er',\n    'tr1ngler',\n    'tring1er',\n    'tringler',\n    'tro1a',\n    'troia',\n    'trombare',\n    'turd',\n    'twat',\n    'vaffancu10',\n    'vaffancu1o',\n    'vaffancul0',\n    'vaffanculo',\n    'vag1na',\n    'vagina',\n    'verdammt',\n    'verga',\n    'w1chsen',\n    'wank',\n    'wichsen',\n    'x0ch0ta',\n    'x0chota',\n    'xana',\n    'xoch0ta',\n    'xochota',\n    'z0cc01a',\n    'z0cc0la',\n    'z0cco1a',\n    'z0ccola',\n    'z1z1',\n    'z1zi',\n    'ziz1',\n    'zizi',\n    'zocc01a',\n    'zocc0la',\n    'zocco1a',\n    'zoccola',\n  ]),\n}\n\nexport default class Sqids {\n  private alphabet: string\n  private minLength: number\n  private blocklist: Set<string>\n\n  constructor(options?: SqidsOptions) {\n    const alphabet = options?.alphabet ?? defaultOptions.alphabet\n    const minLength = options?.minLength ?? defaultOptions.minLength\n    const blocklist = options?.blocklist ?? defaultOptions.blocklist\n\n    if (new Blob([alphabet]).size !== alphabet.length) {\n      throw new Error('Alphabet cannot contain multibyte characters')\n    }\n\n    const minAlphabetLength = 3\n    if (alphabet.length < minAlphabetLength) {\n      throw new Error(`Alphabet length must be at least ${minAlphabetLength}`)\n    }\n\n    if (new Set(alphabet).size !== alphabet.length) {\n      throw new Error('Alphabet must contain unique characters')\n    }\n\n    const minLengthLimit = 255\n    if (\n      typeof minLength !== 'number' ||\n      minLength < 0 ||\n      minLength > minLengthLimit\n    ) {\n      throw new Error(\n        `Minimum length has to be between 0 and ${minLengthLimit}`,\n      )\n    }\n\n    const filteredBlocklist = new Set<string>()\n    const alphabetChars = alphabet.toLowerCase().split('')\n    for (const word of blocklist) {\n      if (word.length >= 3) {\n        const wordLowercased = word.toLowerCase()\n        const wordChars = wordLowercased.split('')\n        const intersection = wordChars.filter((c) => alphabetChars.includes(c))\n        if (intersection.length === wordChars.length) {\n          filteredBlocklist.add(wordLowercased)\n        }\n      }\n    }\n\n    this.alphabet = this.shuffle(alphabet)\n    this.minLength = minLength\n    this.blocklist = filteredBlocklist\n  }\n\n  encode(numbers: number[]): string {\n    if (numbers.length === 0) {\n      return ''\n    }\n\n    const inRangeNumbers = numbers.filter((n) => n >= 0 && n <= this.maxValue())\n    if (inRangeNumbers.length !== numbers.length) {\n      throw new Error(\n        `Encoding supports numbers between 0 and ${this.maxValue()}`,\n      )\n    }\n\n    return this.encodeNumbers(numbers)\n  }\n\n  decode(id: string): number[] {\n    const ret: number[] = []\n\n    if (id === '') {\n      return ret\n    }\n\n    const alphabetChars = this.alphabet.split('')\n    for (const c of id.split('')) {\n      if (!alphabetChars.includes(c)) {\n        return ret\n      }\n    }\n\n    const prefix = id.charAt(0)\n    const offset = this.alphabet.indexOf(prefix)\n    let alphabet = this.alphabet.slice(offset) + this.alphabet.slice(0, offset)\n    alphabet = alphabet.split('').reverse().join('')\n    let slicedId = id.slice(1)\n\n    while (slicedId.length > 0) {\n      const separator = alphabet.slice(0, 1)\n\n      const chunks = slicedId.split(separator)\n      if (chunks.length > 0) {\n        if (chunks[0] === '') {\n          return ret\n        }\n\n        ret.push(this.toNumber(chunks[0]!, alphabet.slice(1)))\n        if (chunks.length > 1) {\n          alphabet = this.shuffle(alphabet)\n        }\n      }\n\n      slicedId = chunks.slice(1).join(separator)\n    }\n\n    return ret\n  }\n\n  private encodeNumbers(numbers: number[], increment = 0): string {\n    if (increment > this.alphabet.length) {\n      throw new Error('Reached max attempts to re-generate the ID')\n    }\n\n    let offset =\n      numbers.reduce(\n        (a, v, i) =>\n          this.alphabet[v % this.alphabet.length]!.codePointAt(0)! + i + a,\n        numbers.length,\n      ) % this.alphabet.length\n\n    offset = (offset + increment) % this.alphabet.length\n    let alphabet = this.alphabet.slice(offset) + this.alphabet.slice(0, offset)\n    const prefix = alphabet.charAt(0)\n    alphabet = alphabet.split('').reverse().join('')\n    const ret = [prefix]\n\n    for (let i = 0; i !== numbers.length; i++) {\n      const num = numbers[i]!\n\n      ret.push(this.toId(num, alphabet.slice(1)))\n      if (i < numbers.length - 1) {\n        ret.push(alphabet.slice(0, 1))\n        alphabet = this.shuffle(alphabet)\n      }\n    }\n\n    let id = ret.join('')\n\n    if (this.minLength > id.length) {\n      id += alphabet.slice(0, 1)\n\n      while (this.minLength - id.length > 0) {\n        alphabet = this.shuffle(alphabet)\n        id += alphabet.slice(\n          0,\n          Math.min(this.minLength - id.length, alphabet.length),\n        )\n      }\n    }\n\n    if (this.isBlockedId(id)) {\n      id = this.encodeNumbers(numbers, increment + 1)\n    }\n\n    return id\n  }\n\n  private shuffle(alphabet: string): string {\n    const chars = alphabet.split('')\n\n    for (let i = 0, j = chars.length - 1; j > 0; i++, j--) {\n      const r =\n        (i * j + chars[i]!.codePointAt(0)! + chars[j]!.codePointAt(0)!) %\n        chars.length\n      ;[chars[i], chars[r]] = [chars[r]!, chars[i]!]\n    }\n\n    return chars.join('')\n  }\n\n  private toId(num: number, alphabet: string): string {\n    const id = []\n    const chars = alphabet.split('')\n\n    let result = num\n\n    do {\n      id.unshift(chars[result % chars.length])\n      result = Math.floor(result / chars.length)\n    } while (result > 0)\n\n    return id.join('')\n  }\n\n  private toNumber(id: string, alphabet: string): number {\n    const chars = alphabet.split('')\n    return id.split('').reduce((a, v) => a * chars.length + chars.indexOf(v), 0)\n  }\n\n  private isBlockedId(id: string): boolean {\n    const lowercaseId = id.toLowerCase()\n\n    for (const word of this.blocklist) {\n      if (word.length <= lowercaseId.length) {\n        if (lowercaseId.length <= 3 || word.length <= 3) {\n          if (lowercaseId === word) {\n            return true\n          }\n        } else if (/\\d/.test(word)) {\n          if (lowercaseId.startsWith(word) || lowercaseId.endsWith(word)) {\n            return true\n          }\n        } else if (lowercaseId.includes(word)) {\n          return true\n        }\n      }\n    }\n\n    return false\n  }\n\n  private maxValue() {\n    return Number.MAX_SAFE_INTEGER\n  }\n}\n"], "names": ["root", "factory", "exports", "module", "define", "amd", "self", "__webpack_require__", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "defaultOptions", "alphabet", "<PERSON><PERSON><PERSON><PERSON>", "blocklist", "Set", "Sqids", "constructor", "options", "Blob", "size", "length", "Error", "filteredBlocklist", "alphabetChars", "toLowerCase", "split", "word", "wordLowercased", "wordChars", "filter", "c", "includes", "add", "this", "shuffle", "encode", "numbers", "n", "maxValue", "encodeNumbers", "decode", "id", "ret", "prefix", "char<PERSON>t", "offset", "indexOf", "slice", "reverse", "join", "slicedId", "separator", "chunks", "push", "toNumber", "increment", "reduce", "a", "v", "i", "codePointAt", "num", "toId", "Math", "min", "isBlockedId", "chars", "j", "r", "result", "unshift", "floor", "lowercaseId", "test", "startsWith", "endsWith", "Number", "MAX_SAFE_INTEGER"], "sourceRoot": ""}