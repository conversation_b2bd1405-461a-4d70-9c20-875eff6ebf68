{"version": 3, "file": "SortedSet.d.ts", "sourceRoot": "", "sources": ["../../src/SortedSet.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,OAAO,KAAK,KAAK,MAAM,YAAY,CAAA;AACnC,OAAO,KAAK,KAAK,WAAW,MAAM,kBAAkB,CAAA;AAIpD,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAA;AAEnD,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,YAAY,CAAA;AACvC,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAA;AAE7C,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAA;AAG/C,OAAO,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,YAAY,CAAA;AAEpD,QAAA,MAAM,MAAM,EAAE,OAAO,MAAuC,CAAA;AAE5D;;;GAGG;AACH,MAAM,MAAM,MAAM,GAAG,OAAO,MAAM,CAAA;AAElC;;;GAGG;AACH,MAAM,WAAW,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAE,SAAQ,QAAQ,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,EAAE,QAAQ,EAAE,WAAW;IAC1F,QAAQ,CAAC,CAAC,MAAM,CAAC,EAAE;QACjB,QAAQ,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC,CAAC,CAAA;KAC1B,CAAA;CAGF;AA0CD;;;GAGG;AACH,eAAO,MAAM,WAAW,EAAE;IACxB;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,CAAA;IACtC;;;OAGG;IACH,CAAC,CAAC,EAAE,OAAO,GAAG,CAAC,IAAI,SAAS,CAAC,OAAO,CAAC,CAAA;CAC4B,CAAA;AAEnE;;;GAGG;AACH,eAAO,MAAM,KAAK,GAAI,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,KAAG,SAAS,CAAC,CAAC,CAA2B,CAAA;AAE7E;;;;;GAKG;AACH,eAAO,MAAM,YAAY,EAAE;IACzB;;;;;OAKG;IACH,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,CAAA;IACxE;;;;;OAKG;IACH,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAA;CAKrE,CAAA;AAED;;;GAGG;AACH,eAAO,MAAM,IAAI,GACd,CAAC,OAAO,KAAK,CAAC,CAAC,CAAC,MAAM,OAAO,SAAS,aAAa,CAAC,CAAC,CAAC,cAAc,OAAO,KAAG,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAC5E,CAAA;AAE9B;;;GAGG;AACH,eAAO,MAAM,GAAG,EAAE;IAChB;;;OAGG;IACH,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,CAAA;IACnD;;;OAGG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAA;CAOK,CAAA;AAEtD;;GAEG;AACH,eAAO,MAAM,UAAU,EAAE;IACvB;;OAEG;IACH,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,CAAA;IACzE;;OAEG;IACH,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAA;CAUrE,CAAA;AAEF;;;;;GAKG;AACH,eAAO,MAAM,KAAK,EAAE;IAClB;;;;;OAKG;IACH,CAAC,CAAC,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,KAAK,OAAO,CAAA;IAC7D;;;;;OAKG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC,GAAG,OAAO,CAAA;CAQzD,CAAA;AAEF;;;GAGG;AACH,eAAO,MAAM,MAAM,EAAE;IACnB;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,CAAA;IAC/E;;;OAGG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAA;CAU9D,CAAA;AAEF;;;GAGG;AACH,eAAO,MAAM,OAAO,EAAE;IACpB;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,CAAA;IACnF;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAA;CAY/E,CAAA;AAEF;;;GAGG;AACH,eAAO,MAAM,OAAO,EAAE;IACpB;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,IAAI,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,KAAK,IAAI,CAAA;IACpD;;;OAGG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,IAAI,GAAG,IAAI,CAAA;CAIH,CAAA;AAE/C;;;GAGG;AACH,eAAO,MAAM,GAAG,EAAE;IAChB;;;OAGG;IACH,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,KAAK,OAAO,CAAA;IAC9C;;;OAGG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,OAAO,CAAA;CAIO,CAAA;AAEnD;;GAEG;AACH,eAAO,MAAM,YAAY,EAAE;IACzB;;OAEG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,CAAA;IAC5D;;OAEG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAA;CAaxD,CAAA;AAEF;;;GAGG;AACH,eAAO,MAAM,QAAQ,EAAE;IACrB;;;OAGG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,KAAK,OAAO,CAAA;IACxD;;;OAGG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,GAAG,OAAO,CAAA;CAIA,CAAA;AAEtD;;;GAGG;AACH,eAAO,MAAM,GAAG,EAAE;IAChB;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,CAAA;IACzE;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAA;CAarE,CAAA;AAEF;;;GAGG;AACH,eAAO,MAAM,SAAS,EAAE;IACtB;;;OAGG;IACH,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,OAAO,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAA;IACtH;;;OAGG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,OAAO,GAAG,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAA;CAgB1G,CAAA;AAED;;;GAGG;AACH,eAAO,MAAM,MAAM,EAAE;IACnB;;;OAGG;IACH,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,CAAA;IACnD;;;OAGG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAA;CAIoB,CAAA;AAErE;;;GAGG;AACH,eAAO,MAAM,IAAI,GAAI,CAAC,QAAQ,SAAS,CAAC,CAAC,CAAC,KAAG,MAAgC,CAAA;AAE7E;;;;;GAKG;AACH,eAAO,MAAM,IAAI,EAAE;IACjB;;;;;OAKG;IACH,CAAC,CAAC,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,KAAK,OAAO,CAAA;IAC7D;;;;;OAKG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC,GAAG,OAAO,CAAA;CAWzD,CAAA;AAEF;;;GAGG;AACH,eAAO,MAAM,MAAM,EAAE;IACnB;;;OAGG;IACH,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,CAAA;IACnD;;;OAGG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAA;CAI+B,CAAA;AAEhF;;GAEG;AACH,eAAO,MAAM,KAAK,EAAE;IAClB;;OAEG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,CAAA;IAC5D;;OAEG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAA;CAcxD,CAAA;AAEF;;;GAGG;AACH,eAAO,MAAM,MAAM,GAAI,CAAC,QAAQ,SAAS,CAAC,CAAC,CAAC,KAAG,gBAAgB,CAAC,CAAC,CAA2B,CAAA;AAE5F;;;GAGG;AACH,eAAO,MAAM,cAAc,GAAI,CAAC,OAAK,WAAW,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAA+C,CAAA"}