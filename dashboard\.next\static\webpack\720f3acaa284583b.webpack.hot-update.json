{"c": ["app/layout", "app/dashboard/properties/create/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./app/dashboard/properties/create/page.tsx", "(app-pages-browser)/./components/LanguageSwitcher.tsx", "(app-pages-browser)/./components/ThemeSwitcher.tsx", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/languages.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Capp%5C%5Cdashboard%5C%5Cproperties%5C%5Ccreate%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"]}