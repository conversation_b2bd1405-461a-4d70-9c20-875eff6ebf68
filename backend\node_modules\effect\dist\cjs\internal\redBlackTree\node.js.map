{"version": 3, "file": "node.js", "names": ["Color", "exports", "Red", "Black", "clone", "color", "count", "key", "left", "right", "value", "swap", "n", "v", "repaint", "recount", "node"], "sources": ["../../../../src/internal/redBlackTree/node.ts"], "sourcesContent": [null], "mappings": ";;;;;;;AAAA;AACO,MAAMA,KAAK,GAAAC,OAAA,CAAAD,KAAA,GAAG;EACnBE,GAAG,EAAE,CAAe;EACpBC,KAAK,EAAE,CAAC,IAAI;CACJ;AAiBV;AACO,MAAMC,KAAK,GAAGA,CAAO;EAC1BC,KAAK;EACLC,KAAK;EACLC,GAAG;EACHC,IAAI;EACJC,KAAK;EACLC;AAAK,CACM,MAAM;EACjBL,KAAK;EACLE,GAAG;EACHG,KAAK;EACLF,IAAI;EACJC,KAAK;EACLH;CACD,CAAC;AAEF;AAAAL,OAAA,CAAAG,KAAA,GAAAA,KAAA;AACM,SAAUO,IAAIA,CAAOC,CAAa,EAAEC,CAAa;EACrDD,CAAC,CAACL,GAAG,GAAGM,CAAC,CAACN,GAAG;EACbK,CAAC,CAACF,KAAK,GAAGG,CAAC,CAACH,KAAK;EACjBE,CAAC,CAACJ,IAAI,GAAGK,CAAC,CAACL,IAAI;EACfI,CAAC,CAACH,KAAK,GAAGI,CAAC,CAACJ,KAAK;EACjBG,CAAC,CAACP,KAAK,GAAGQ,CAAC,CAACR,KAAK;EACjBO,CAAC,CAACN,KAAK,GAAGO,CAAC,CAACP,KAAK;AACnB;AAEA;AACO,MAAMQ,OAAO,GAAGA,CAAO;EAC5BR,KAAK;EACLC,GAAG;EACHC,IAAI;EACJC,KAAK;EACLC;AAAK,CACM,EAAEL,KAAiB,MAAM;EACpCA,KAAK;EACLE,GAAG;EACHG,KAAK;EACLF,IAAI;EACJC,KAAK;EACLH;CACD,CAAC;AAEF;AAAAL,OAAA,CAAAa,OAAA,GAAAA,OAAA;AACO,MAAMC,OAAO,GAAUC,IAAgB,IAAI;EAChDA,IAAI,CAACV,KAAK,GAAG,CAAC,IAAIU,IAAI,CAACR,IAAI,EAAEF,KAAK,IAAI,CAAC,CAAC,IAAIU,IAAI,CAACP,KAAK,EAAEH,KAAK,IAAI,CAAC,CAAC;AACrE,CAAC;AAAAL,OAAA,CAAAc,OAAA,GAAAA,OAAA", "ignoreList": []}