"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7681],{17681:(e,r,a)=>{a.d(r,{V:()=>F});var t=a(95155),s=a(12115),l=a(5196),d=a(84616),i=a(54416),o=a(42355),n=a(13052),c=a(4229),m=a(97168),u=a(89852),g=a(82714),x=a(99474),h=a(95784),p=a(90088),b=a(88482),f=a(88145),y=a(87489),v=a(53999);let j=s.forwardRef((e,r)=>{let{className:a,orientation:s="horizontal",decorative:l=!0,...d}=e;return(0,t.jsx)(y.b,{ref:r,decorative:l,orientation:s,className:(0,v.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",a),...d})});j.displayName=y.b.displayName;var N=a(86132),k=a(29869),w=a(51154),A=a(58471);(0,A.Jt)();let C=(0,A.Wi)();var I=a(56671);function E(e){let{images:r,onImagesChange:a,onAutoSave:d,onUploadStatusChange:o,propertyId:n,maxImages:c=10,disabled:u=!1}=e,{language:g,isArabic:x}=(0,N.Y)(),[h,p]=(0,s.useState)(!1),[b,f]=(0,s.useState)("idle"),y={ar:{uploadImages:"رفع الصور",dragDrop:"اسحب الصور هنا أو انقر للاختيار",uploading:"جاري الرفع...",autoSaving:"حفظ تلقائي...",saved:"تم الحفظ",remove:"حذف",mainImage:"الصورة الرئيسية",images:"صور",of:"من"},en:{uploadImages:"Upload Images",dragDrop:"Drag images here or click to select",uploading:"Uploading...",autoSaving:"Auto-saving...",saved:"Saved",remove:"Remove",mainImage:"Main Image",images:"images",of:"of"}}[g];(0,s.useEffect)(()=>{if(d&&r.length>0&&"idle"===b){let e=setTimeout(async()=>{f("saving");try{await d(r),f("saved"),setTimeout(()=>f("idle"),2e3)}catch(e){console.error("Auto-save failed:",e),f("idle")}},500);return()=>clearTimeout(e)}},[r,d,b]);let v=(0,s.useCallback)(e=>{if(e&&e.length>0){let t=[...r,...e.map(e=>e.url)].slice(0,c);a(t),p(!1),null==o||o(!1),I.oR.success("".concat(e.length," ").concat(y.images," ").concat(y.saved)),d&&t.length>0&&setTimeout(()=>{d(t)},500)}},[r,a,c,o,d,y.images,y.saved]),j=(0,s.useCallback)(e=>{console.error("Upload error:",e),p(!1),null==o||o(!1),I.oR.error("Upload failed: ".concat(e.message))},[o]),A=(0,s.useCallback)(()=>{p(!0),null==o||o(!0)},[o]),E=(0,s.useCallback)(e=>{a(r.filter((r,a)=>a!==e))},[r,a]),F=(0,s.useCallback)(e=>{if(0===e)return;let t=[...r],[s]=t.splice(e,1);t.unshift(s),a(t)},[r,a]),P=r.length<c;return(0,t.jsxs)("div",{className:"space-y-4 ".concat(x?"rtl":"ltr"),dir:x?"rtl":"ltr",children:[P&&!u&&(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsxs)("div",{className:"border-2 border-dashed border-slate-600 rounded-lg p-6 text-center hover:border-emerald-500 bg-slate-800/50",children:[(0,t.jsx)(k.A,{className:"mx-auto h-8 w-8 text-slate-400 mb-3"}),(0,t.jsx)("p",{className:"text-sm text-slate-400 mb-4",children:y.dragDrop}),(0,t.jsxs)("p",{className:"text-xs text-slate-500",children:[r.length," ",y.of," ",c," ",y.images]})]}),(0,t.jsx)(C,{endpoint:"propertyImageUploader",onClientUploadComplete:v,onUploadError:j,onUploadBegin:A,className:"absolute inset-0 opacity-0 cursor-pointer"}),h&&(0,t.jsx)("div",{className:"absolute inset-0 bg-white/90 dark:bg-slate-900/90 rounded-lg flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(w.A,{className:"h-6 w-6 animate-spin mx-auto mb-2 text-emerald-500"}),(0,t.jsx)("p",{className:"text-sm text-slate-600 dark:text-slate-400",children:y.uploading})]})})]}),"idle"!==b&&(0,t.jsxs)("div",{className:"flex items-center gap-2 text-sm ".concat("saving"===b?"text-blue-600 dark:text-blue-400":"text-green-600 dark:text-green-400"," ").concat(x?"flex-row-reverse":""),children:["saving"===b?(0,t.jsx)(w.A,{className:"h-4 w-4 animate-spin"}):(0,t.jsx)(l.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"saving"===b?y.autoSaving:y.saved})]}),r.length>0&&(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("div",{className:"relative",children:(0,t.jsxs)("div",{className:"aspect-video bg-slate-100 dark:bg-slate-800 rounded-lg overflow-hidden",children:[(0,t.jsx)("img",{src:r[0],alt:"Main property image",className:"w-full h-full object-cover"}),(0,t.jsx)("div",{className:"absolute top-2 ".concat(x?"right-2":"left-2"," bg-emerald-600 text-white text-xs px-2 py-1 rounded"),children:y.mainImage}),(0,t.jsx)(m.$,{type:"button",variant:"destructive",size:"sm",className:"absolute top-2 ".concat(x?"left-2":"right-2"," h-6 w-6 p-0 rounded"),onClick:()=>E(0),children:(0,t.jsx)(i.A,{className:"h-3 w-3"})})]})}),r.length>1&&(0,t.jsx)("div",{className:"grid grid-cols-4 gap-2",children:r.slice(1).map((e,r)=>(0,t.jsx)("div",{className:"relative group",children:(0,t.jsxs)("div",{className:"aspect-square bg-slate-100 dark:bg-slate-800 rounded overflow-hidden cursor-pointer hover:opacity-75 transition-opacity",onClick:()=>F(r+1),children:[(0,t.jsx)("img",{src:e,alt:"Property image ".concat(r+2),className:"w-full h-full object-cover"}),(0,t.jsx)(m.$,{type:"button",variant:"destructive",size:"sm",className:"absolute -top-1 -right-1 h-5 w-5 p-0 rounded-full opacity-0 group-hover:opacity-100 transition-opacity",onClick:e=>{e.stopPropagation(),E(r+1)},children:(0,t.jsx)(i.A,{className:"h-2 w-2"})})]})},r+1))})]}),r.length>=c&&(0,t.jsx)("div",{className:"text-center p-3 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg",children:(0,t.jsx)("p",{className:"text-sm text-amber-800 dark:text-amber-200",children:"ar"===g?"الحد الأقصى ".concat(c," صور"):"Maximum ".concat(c," images")})})]})}function F(e){let{onSave:r,loading:a,initialData:y,isEdit:v=!1,propertyId:k}=e,{language:w}=(0,N.Y)(),[A,C]=(0,s.useState)(1),[I,F]=(0,s.useState)(!1),[P,S]=(0,s.useState)(y||{title:"",titleAr:"",description:"",descriptionAr:"",price:"",currency:"SAR",type:"APARTMENT",status:"AVAILABLE",bedrooms:"",bathrooms:"",area:"",location:"",locationAr:"",address:"",addressAr:"",city:"",cityAr:"",country:"Saudi Arabia",countryAr:"المملكة العربية السعودية",images:[],features:[],featuresAr:[],amenities:[],amenitiesAr:[],yearBuilt:"",parking:"",furnished:!1,petFriendly:!1,utilities:"",utilitiesAr:"",contactInfo:"",isFeatured:!1,isActive:!0}),[_,T]=(0,s.useState)(""),[R,D]=(0,s.useState)(""),[J,U]=(0,s.useState)(""),[L,B]=(0,s.useState)("");(0,s.useEffect)(()=>{if(!v&&!y){let e=localStorage.getItem("property-draft");if(e)try{let r=JSON.parse(e);S(r)}catch(e){console.error("Error loading draft:",e)}}},[v,y]),(0,s.useEffect)(()=>{if(!v){let e=setTimeout(()=>{localStorage.setItem("property-draft",JSON.stringify(P))},1e3);return()=>clearTimeout(e)}},[P,v]),(0,s.useEffect)(()=>{y&&v&&S(y)},[y,v]);let O={ar:{step:"الخطوة",of:"من",next:"التالي",previous:"السابق",save:"حفظ العقار",required:"مطلوب",optional:"اختياري",basicInfo:"المعلومات الأساسية",propertyDetails:"تفاصيل العقار",locationInfo:"معلومات الموقع",additionalInfo:"معلومات إضافية",title:"عنوان العقار",description:"وصف العقار",price:"السعر",currency:"العملة",propertyType:"نوع العقار",status:"حالة العقار",bedrooms:"غرف النوم",bathrooms:"دورات المياه",area:"المساحة (متر مربع)",yearBuilt:"سنة البناء",parking:"مواقف السيارات",location:"الموقع",address:"العنوان",city:"المدينة",country:"الدولة",images:"صور العقار",features:"مميزات العقار",amenities:"المرافق والخدمات",utilities:"الخدمات المشمولة",contactInfo:"معلومات التواصل",furnished:"مفروش",petFriendly:"يسمح بالحيوانات الأليفة",featured:"عقار مميز",active:"نشط",addFeature:"إضافة ميزة",addAmenity:"إضافة مرفق",uploadImages:"رفع صور العقار",dragDropImages:"اسحب وأفلت الصور هنا، أو انقر للاختيار",titlePlaceholder:"أدخل عنوان العقار...",descriptionPlaceholder:"اكتب وصفاً مفصلاً للعقار...",locationPlaceholder:"أدخل موقع العقار...",addressPlaceholder:"أدخل العنوان الكامل...",cityPlaceholder:"أدخل اسم المدينة...",featurePlaceholder:"أضف ميزة جديدة...",amenityPlaceholder:"أضف مرفق جديد...",utilitiesPlaceholder:"اذكر الخدمات المشمولة...",contactPlaceholder:"أدخل معلومات التواصل...",stepDescription1:"أدخل المعلومات الأساسية للعقار",stepDescription2:"حدد تفاصيل ومواصفات العقار",stepDescription3:"أضف معلومات الموقع والعنوان",stepDescription4:"أضف الصور والمعلومات الإضافية",completed:"مكتمل",current:"الحالي",pending:"في الانتظار",imageGallery:"معرض الصور",mainImage:"الصورة الرئيسية",additionalImages:"الصور الإضافية",imageTips:"نصائح للصور",noFeatures:"لا توجد مميزات مضافة",noAmenities:"لا توجد مرافق مضافة",noImages:"لم يتم رفع صور بعد",setAsMain:"تعيين كصورة رئيسية",removeImage:"حذف الصورة",saving:"جاري الحفظ...",success:"تم بنجاح",error:"حدث خطأ"},en:{step:"Step",of:"of",next:"Next",previous:"Previous",save:"Save Property",required:"Required",optional:"Optional",basicInfo:"Basic Information",propertyDetails:"Property Details",locationInfo:"Location Information",additionalInfo:"Additional Information",title:"Property Title",description:"Property Description",price:"Price",currency:"Currency",propertyType:"Property Type",status:"Property Status",bedrooms:"Bedrooms",bathrooms:"Bathrooms",area:"Area (sqm)",yearBuilt:"Year Built",parking:"Parking Spaces",location:"Location",address:"Address",city:"City",country:"Country",images:"Property Images",features:"Property Features",amenities:"Amenities & Services",utilities:"Included Utilities",contactInfo:"Contact Information",furnished:"Furnished",petFriendly:"Pet Friendly",featured:"Featured Property",active:"Active",addFeature:"Add Feature",addAmenity:"Add Amenity",uploadImages:"Upload Property Images",dragDropImages:"Drag and drop images here, or click to select",titlePlaceholder:"Enter property title...",descriptionPlaceholder:"Write a detailed property description...",locationPlaceholder:"Enter property location...",addressPlaceholder:"Enter full address...",cityPlaceholder:"Enter city name...",featurePlaceholder:"Add new feature...",amenityPlaceholder:"Add new amenity...",utilitiesPlaceholder:"List included utilities...",contactPlaceholder:"Enter contact information...",stepDescription1:"Enter basic property information",stepDescription2:"Specify property details and specifications",stepDescription3:"Add location and address information",stepDescription4:"Add images and additional information",completed:"Completed",current:"Current",pending:"Pending",imageGallery:"Image Gallery",mainImage:"Main Image",additionalImages:"Additional Images",imageTips:"Image Tips",noFeatures:"No features added",noAmenities:"No amenities added",noImages:"No images uploaded yet",setAsMain:"Set as Main Image",removeImage:"Remove Image",saving:"Saving...",success:"Success",error:"Error"}}[w],M=[O.basicInfo,O.propertyDetails,O.locationInfo,O.additionalInfo],q=async e=>{if(e.preventDefault(),!I)try{await r(P),localStorage.removeItem("property-draft"),setTimeout(()=>{window.location.href="/dashboard/properties"},1500)}catch(e){console.error("Save failed:",e)}},z=async e=>{if(v&&k)try{let r=await fetch("/api/v1/properties/".concat(k,"/images"),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({images:e})});if(!r.ok){let e=await r.json();throw Error(e.message||"Failed to auto-save images")}let a=await r.json();console.log("Images auto-saved successfully:",a)}catch(e){throw console.error("Auto-save error:",e),e}},V=()=>{_.trim()&&(S(e=>({...e,features:[...e.features,_.trim()]})),T(""))},H=()=>{J.trim()&&(S(e=>({...e,amenities:[...e.amenities,J.trim()]})),U(""))},W=e=>{S(r=>({...r,features:r.features.filter((r,a)=>a!==e)}))},Y=e=>{S(r=>({...r,amenities:r.amenities.filter((r,a)=>a!==e)}))},Z=e=>{switch(e){case 1:return P.title&&P.description&&P.price&&P.type;case 2:case 4:return!0;case 3:return P.location&&P.address&&P.city;default:return!1}};return(0,t.jsxs)("div",{className:"".concat("ar"===w?"rtl":"ltr"),dir:"ar"===w?"rtl":"ltr",children:[(0,t.jsxs)("div",{className:"mb-16",children:[(0,t.jsxs)("div",{className:"relative mb-8",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-emerald-100/50 to-teal-100/50 dark:from-emerald-900/20 dark:to-teal-900/20 rounded-2xl"}),(0,t.jsx)("div",{className:"relative p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between ".concat("ar"===w?"flex-row-reverse":""),children:[(0,t.jsxs)("div",{className:"flex items-center gap-4 ".concat("ar"===w?"flex-row-reverse":""),children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"w-14 h-14 bg-gradient-to-br from-emerald-500 via-teal-500 to-cyan-600 rounded-2xl flex items-center justify-center shadow-xl shadow-emerald-200 dark:shadow-emerald-900/50",children:(0,t.jsx)("span",{className:"text-white font-black text-lg",children:A})}),(0,t.jsx)("div",{className:"absolute -top-1 ".concat("ar"===w?"-right-1":"-left-1"," w-6 h-6 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center animate-pulse"),children:(0,t.jsx)("span",{className:"text-white text-xs font-bold",children:"✦"})})]}),(0,t.jsxs)("div",{className:"space-y-1 ".concat("ar"===w?"text-right":"text-left"),children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 ".concat("ar"===w?"flex-row-reverse":""),children:[(0,t.jsxs)("span",{className:"text-xl font-black text-slate-800 dark:text-white",children:[O.step," ",A," ",O.of," ",4]}),(0,t.jsx)("div",{className:"px-3 py-1 bg-emerald-100 dark:bg-emerald-900/30 rounded-full",children:(0,t.jsx)("span",{className:"text-sm font-bold text-emerald-700 dark:text-emerald-300",children:O.current})})]}),(0,t.jsx)("div",{className:"text-lg font-bold text-slate-700 dark:text-slate-300",children:M[A-1]}),(0,t.jsxs)("div",{className:"text-sm text-slate-600 dark:text-slate-400",children:[1===A&&O.stepDescription1,2===A&&O.stepDescription2,3===A&&O.stepDescription3,4===A&&O.stepDescription4]})]})]}),(0,t.jsxs)("div",{className:"".concat("ar"===w?"text-right":"text-left"),children:[(0,t.jsxs)("div",{className:"text-4xl font-black bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent",children:[Math.round(A/4*100),"%"]}),(0,t.jsx)("div",{className:"text-sm font-bold text-emerald-600 dark:text-emerald-400",children:O.completed})]})]})})]}),(0,t.jsx)("div",{className:"relative mb-8",children:(0,t.jsx)("div",{className:"w-full bg-slate-200 dark:bg-slate-700 rounded-full h-4 shadow-inner",children:(0,t.jsxs)("div",{className:"bg-gradient-to-r from-emerald-500 via-teal-500 to-cyan-500 h-4 rounded-full transition-all duration-1000 ease-out shadow-lg relative overflow-hidden",style:{width:"".concat(A/4*100,"%")},children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-pulse"}),(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-emerald-400/50 to-teal-400/50 animate-pulse delay-300"})]})})}),(0,t.jsx)("div",{className:"grid grid-cols-4 gap-4",children:M.map((e,r)=>(0,t.jsxs)("div",{className:"flex flex-col items-center",children:[(0,t.jsx)("div",{className:"w-16 h-16 rounded-2xl flex items-center justify-center text-lg font-black transition-all duration-500 shadow-xl ".concat(r+1<=A?"bg-gradient-to-br from-emerald-500 via-teal-500 to-cyan-600 text-white shadow-emerald-200 dark:shadow-emerald-900/50 scale-110":r+1===A+1?"bg-gradient-to-br from-slate-300 to-slate-400 text-slate-700 shadow-slate-200 dark:shadow-slate-800 scale-105":"bg-slate-200 dark:bg-slate-700 text-slate-500 dark:text-slate-400"),children:r+1<A?(0,t.jsx)(l.A,{className:"h-7 w-7"}):r+1===A?(0,t.jsx)("div",{className:"w-3 h-3 bg-white rounded-full animate-pulse"}):(0,t.jsx)("span",{className:"font-black",children:r+1})}),(0,t.jsxs)("div",{className:"mt-4 text-center",children:[(0,t.jsx)("div",{className:"text-sm font-bold leading-tight ".concat(r+1<=A?"text-emerald-700 dark:text-emerald-300":r+1===A+1?"text-slate-600 dark:text-slate-400":"text-slate-500 dark:text-slate-500"),children:e}),(0,t.jsx)("div",{className:"text-xs mt-1 ".concat(r+1<=A?"text-emerald-600 dark:text-emerald-400":"text-slate-400 dark:text-slate-500"),children:r+1<=A?O.completed:O.pending})]})]},r))})]}),(0,t.jsxs)("form",{onSubmit:q,className:"space-y-8",children:[1===A&&(0,t.jsxs)(b.Zp,{className:"border border-slate-700 bg-slate-800",children:[(0,t.jsx)(b.aR,{className:"pb-6 border-b border-slate-700",children:(0,t.jsxs)(b.ZB,{className:"text-xl font-bold text-white flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-10 h-10 bg-emerald-600 text-white rounded-lg flex items-center justify-center text-lg font-bold",children:"1"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-xl",children:O.basicInfo}),(0,t.jsx)("div",{className:"text-sm font-normal text-slate-400 mt-1",children:"ar"===w?"المعلومات الأساسية":"Basic Information"})]})]})}),(0,t.jsxs)(b.Wu,{className:"space-y-8 p-8",children:[(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)(g.J,{htmlFor:"title",className:"text-sm font-medium text-white",children:[O.title," ",(0,t.jsx)("span",{className:"text-red-400",children:"*"})]}),(0,t.jsx)(u.p,{id:"title",value:P.title,onChange:e=>S(r=>({...r,title:e.target.value})),placeholder:O.titlePlaceholder,required:!0,dir:"ar"===w?"rtl":"ltr",className:"h-12 border border-slate-600 focus:border-emerald-500 bg-slate-700 text-white placeholder:text-slate-400 rounded-lg"})]}),"en"===w&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)(g.J,{htmlFor:"titleEn",className:"text-base font-semibold text-slate-700 dark:text-slate-300 flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-6 h-6 bg-slate-100 dark:bg-slate-700 rounded-lg flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-slate-600 dark:text-slate-400 text-sm",children:"EN"})}),O.titleEn,(0,t.jsxs)("span",{className:"text-slate-400 text-sm",children:["(",O.optional,")"]})]}),(0,t.jsx)(u.p,{id:"titleEn",value:P.titleAr,onChange:e=>S(r=>({...r,titleAr:e.target.value})),placeholder:O.titleEnPlaceholder,dir:"ltr",className:"h-12 border-2 border-slate-200 dark:border-slate-600 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-300 rounded-xl bg-white/30 dark:bg-slate-800/30"})]})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)(g.J,{htmlFor:"description",className:"text-base font-bold text-slate-800 dark:text-slate-200 flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-6 h-6 bg-emerald-100 dark:bg-emerald-900/30 rounded-lg flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-emerald-600 dark:text-emerald-400 text-sm font-bold",children:"2"})}),O.description,(0,t.jsx)("span",{className:"text-red-500 text-lg",children:"*"})]}),(0,t.jsx)(x.T,{id:"description",value:P.description,onChange:e=>S(r=>({...r,description:e.target.value})),placeholder:O.descriptionPlaceholder,required:!0,rows:6,dir:"rtl",className:"border-2 border-slate-200 dark:border-slate-600 focus:border-emerald-500 dark:focus:border-emerald-400 transition-all duration-300 resize-none rounded-xl text-lg bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm shadow-sm hover:shadow-md focus:shadow-lg"})]})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)(g.J,{htmlFor:"price",className:"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2",children:[O.price,(0,t.jsx)("span",{className:"text-red-500 text-lg",children:"*"})]}),(0,t.jsx)(u.p,{id:"price",type:"number",value:P.price,onChange:e=>S(r=>({...r,price:e.target.value})),placeholder:"0",required:!0,className:"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-200 rounded-lg"})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(g.J,{htmlFor:"currency",className:"text-sm font-semibold text-gray-700 dark:text-gray-300",children:O.currency}),(0,t.jsxs)(h.l6,{value:P.currency,onValueChange:e=>S(r=>({...r,currency:e})),children:[(0,t.jsx)(h.bq,{className:"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 rounded-lg",children:(0,t.jsx)(h.yv,{})}),(0,t.jsxs)(h.gC,{children:[(0,t.jsx)(h.eb,{value:"SAR",children:"SAR - ريال سعودي"}),(0,t.jsx)(h.eb,{value:"AED",children:"AED - درهم إماراتي"}),(0,t.jsx)(h.eb,{value:"USD",children:"USD - دولار أمريكي"}),(0,t.jsx)(h.eb,{value:"EUR",children:"EUR - يورو"}),(0,t.jsx)(h.eb,{value:"GBP",children:"GBP - جنيه إسترليني"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)(g.J,{htmlFor:"type",className:"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2",children:[O.propertyType,(0,t.jsx)("span",{className:"text-red-500 text-lg",children:"*"})]}),(0,t.jsxs)(h.l6,{value:P.type,onValueChange:e=>S(r=>({...r,type:e})),children:[(0,t.jsx)(h.bq,{className:"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 rounded-lg",children:(0,t.jsx)(h.yv,{})}),(0,t.jsx)(h.gC,{children:Object.entries({ar:{APARTMENT:"شقة سكنية",VILLA:"فيلا",TOWNHOUSE:"تاون هاوس",PENTHOUSE:"بنتهاوس",STUDIO:"استوديو",OFFICE:"مكتب تجاري",SHOP:"محل تجاري",WAREHOUSE:"مستودع",LAND:"قطعة أرض",BUILDING:"مبنى كامل"},en:{APARTMENT:"Apartment",VILLA:"Villa",TOWNHOUSE:"Townhouse",PENTHOUSE:"Penthouse",STUDIO:"Studio",OFFICE:"Office",SHOP:"Shop",WAREHOUSE:"Warehouse",LAND:"Land",BUILDING:"Building"}}[w]).map(e=>{let[r,a]=e;return(0,t.jsx)(h.eb,{value:r,children:a},r)})})]})]})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(g.J,{htmlFor:"status",className:"text-sm font-semibold text-gray-700 dark:text-gray-300",children:O.status}),(0,t.jsxs)(h.l6,{value:P.status,onValueChange:e=>S(r=>({...r,status:e})),children:[(0,t.jsx)(h.bq,{className:"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 rounded-lg",children:(0,t.jsx)(h.yv,{})}),(0,t.jsx)(h.gC,{children:Object.entries({ar:{AVAILABLE:"متاح للبيع",SOLD:"تم البيع",RENTED:"مؤجر",RESERVED:"محجوز",OFF_MARKET:"غير متاح"},en:{AVAILABLE:"Available",SOLD:"Sold",RENTED:"Rented",RESERVED:"Reserved",OFF_MARKET:"Off Market"}}[w]).map(e=>{let[r,a]=e;return(0,t.jsx)(h.eb,{value:r,children:a},r)})})]})]})})]})]}),2===A&&(0,t.jsxs)(b.Zp,{className:"shadow-xl border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm",children:[(0,t.jsx)(b.aR,{className:"pb-6 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-t-lg",children:(0,t.jsxs)(b.ZB,{className:"text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-10 h-10 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg",children:"2"}),O.propertyDetails]})}),(0,t.jsxs)(b.Wu,{className:"space-y-8 p-8",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(g.J,{htmlFor:"bedrooms",className:"text-sm font-semibold text-gray-700 dark:text-gray-300",children:O.bedrooms}),(0,t.jsx)(u.p,{id:"bedrooms",type:"number",value:P.bedrooms,onChange:e=>S(r=>({...r,bedrooms:e.target.value})),placeholder:"0",min:"0",className:"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg"})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(g.J,{htmlFor:"bathrooms",className:"text-sm font-semibold text-gray-700 dark:text-gray-300",children:O.bathrooms}),(0,t.jsx)(u.p,{id:"bathrooms",type:"number",value:P.bathrooms,onChange:e=>S(r=>({...r,bathrooms:e.target.value})),placeholder:"0",min:"0",className:"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg"})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(g.J,{htmlFor:"area",className:"text-sm font-semibold text-gray-700 dark:text-gray-300",children:O.area}),(0,t.jsx)(u.p,{id:"area",type:"number",value:P.area,onChange:e=>S(r=>({...r,area:e.target.value})),placeholder:"0",min:"0",className:"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg"})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(g.J,{htmlFor:"yearBuilt",className:"text-sm font-semibold text-gray-700 dark:text-gray-300",children:O.yearBuilt}),(0,t.jsx)(u.p,{id:"yearBuilt",type:"number",value:P.yearBuilt,onChange:e=>S(r=>({...r,yearBuilt:e.target.value})),placeholder:"2024",min:"1900",max:"2030",className:"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg"})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(g.J,{htmlFor:"parking",className:"text-sm font-semibold text-gray-700 dark:text-gray-300",children:O.parking}),(0,t.jsx)(u.p,{id:"parking",type:"number",value:P.parking,onChange:e=>S(r=>({...r,parking:e.target.value})),placeholder:"0",min:"0",className:"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg"})]})]}),(0,t.jsx)(j,{className:"my-8"}),(0,t.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(g.J,{className:"text-sm font-semibold text-gray-700 dark:text-gray-300",children:O.features}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(u.p,{value:_,onChange:e=>T(e.target.value),placeholder:O.featurePlaceholder,onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),V()),className:"h-10 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg"}),(0,t.jsx)(m.$,{type:"button",onClick:V,size:"sm",className:"bg-green-600 hover:bg-green-700 px-4",children:(0,t.jsx)(d.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{className:"flex flex-wrap gap-2 min-h-[40px] p-3 border-2 border-dashed border-gray-200 dark:border-gray-600 rounded-lg",children:[P.features.map((e,r)=>(0,t.jsxs)(f.E,{variant:"secondary",className:"flex items-center gap-1 px-3 py-1 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",children:[e,(0,t.jsx)(i.A,{className:"h-3 w-3 cursor-pointer hover:text-red-600",onClick:()=>W(r)})]},r)),0===P.features.length&&(0,t.jsx)("span",{className:"text-gray-400 text-sm",children:O.noFeatures})]})]})}),(0,t.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(g.J,{className:"text-sm font-semibold text-gray-700 dark:text-gray-300",children:O.amenities}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(u.p,{value:J,onChange:e=>U(e.target.value),placeholder:O.amenityPlaceholder,onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),H()),className:"h-10 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg"}),(0,t.jsx)(m.$,{type:"button",onClick:H,size:"sm",className:"bg-green-600 hover:bg-green-700 px-4",children:(0,t.jsx)(d.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{className:"flex flex-wrap gap-2 min-h-[40px] p-3 border-2 border-dashed border-gray-200 dark:border-gray-600 rounded-lg",children:[P.amenities.map((e,r)=>(0,t.jsxs)(f.E,{variant:"secondary",className:"flex items-center gap-1 px-3 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",children:[e,(0,t.jsx)(i.A,{className:"h-3 w-3 cursor-pointer hover:text-red-600",onClick:()=>Y(r)})]},r)),0===P.amenities.length&&(0,t.jsx)("span",{className:"text-gray-400 text-sm",children:O.noAmenities})]})]})})]})]}),3===A&&(0,t.jsxs)(b.Zp,{className:"shadow-xl border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm",children:[(0,t.jsx)(b.aR,{className:"pb-6 bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-t-lg",children:(0,t.jsxs)(b.ZB,{className:"text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-10 h-10 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg",children:"3"}),O.locationInfo]})}),(0,t.jsxs)(b.Wu,{className:"space-y-8 p-8",children:[(0,t.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)(g.J,{htmlFor:"location",className:"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2",children:[O.location,(0,t.jsx)("span",{className:"text-red-500 text-lg",children:"*"})]}),(0,t.jsx)(u.p,{id:"location",value:P.location,onChange:e=>S(r=>({...r,location:e.target.value})),placeholder:O.locationPlaceholder,required:!0,className:"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg"})]})}),(0,t.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)(g.J,{htmlFor:"address",className:"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2",children:[O.address,(0,t.jsx)("span",{className:"text-red-500 text-lg",children:"*"})]}),(0,t.jsx)(u.p,{id:"address",value:P.address,onChange:e=>S(r=>({...r,address:e.target.value})),placeholder:O.addressPlaceholder,required:!0,className:"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg"})]})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)(g.J,{htmlFor:"city",className:"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2",children:[O.city,(0,t.jsx)("span",{className:"text-red-500 text-lg",children:"*"})]}),(0,t.jsxs)(h.l6,{value:P.city,onValueChange:e=>S(r=>({...r,city:e})),children:[(0,t.jsx)(h.bq,{className:"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg",children:(0,t.jsx)(h.yv,{placeholder:"ar"===w?"اختر المدينة السعودية":"Select Saudi City"})}),(0,t.jsxs)(h.gC,{children:[(0,t.jsx)(h.eb,{value:"Riyadh",children:"Riyadh - الرياض"}),(0,t.jsx)(h.eb,{value:"Jeddah",children:"Jeddah - جدة"}),(0,t.jsx)(h.eb,{value:"Mecca",children:"Mecca - مكة المكرمة"}),(0,t.jsx)(h.eb,{value:"Medina",children:"Medina - المدينة المنورة"}),(0,t.jsx)(h.eb,{value:"Dammam",children:"Dammam - الدمام"}),(0,t.jsx)(h.eb,{value:"Khobar",children:"Khobar - الخبر"}),(0,t.jsx)(h.eb,{value:"Dhahran",children:"Dhahran - الظهران"}),(0,t.jsx)(h.eb,{value:"Taif",children:"Taif - الطائف"}),(0,t.jsx)(h.eb,{value:"Buraidah",children:"Buraidah - بريدة"}),(0,t.jsx)(h.eb,{value:"Tabuk",children:"Tabuk - تبوك"}),(0,t.jsx)(h.eb,{value:"Hail",children:"Hail - حائل"}),(0,t.jsx)(h.eb,{value:"Abha",children:"Abha - أبها"}),(0,t.jsx)(h.eb,{value:"Yanbu",children:"Yanbu - ينبع"}),(0,t.jsx)(h.eb,{value:"Jubail",children:"Jubail - الجبيل"}),(0,t.jsx)(h.eb,{value:"Najran",children:"Najran - نجران"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(g.J,{htmlFor:"country",className:"text-sm font-semibold text-gray-700 dark:text-gray-300",children:O.country}),(0,t.jsxs)(h.l6,{value:P.country,onValueChange:e=>S(r=>({...r,country:e})),children:[(0,t.jsx)(h.bq,{className:"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 rounded-lg",children:(0,t.jsx)(h.yv,{})}),(0,t.jsxs)(h.gC,{children:[(0,t.jsx)(h.eb,{value:"Saudi Arabia",children:"Saudi Arabia - المملكة العربية السعودية"}),(0,t.jsx)(h.eb,{value:"UAE",children:"UAE - الإمارات العربية المتحدة"}),(0,t.jsx)(h.eb,{value:"Qatar",children:"Qatar - قطر"}),(0,t.jsx)(h.eb,{value:"Kuwait",children:"Kuwait - الكويت"}),(0,t.jsx)(h.eb,{value:"Bahrain",children:"Bahrain - البحرين"}),(0,t.jsx)(h.eb,{value:"Oman",children:"Oman - عُمان"}),(0,t.jsx)(h.eb,{value:"Jordan",children:"Jordan - الأردن"}),(0,t.jsx)(h.eb,{value:"Egypt",children:"Egypt - مصر"})]})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)(g.J,{htmlFor:"cityAr",className:"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2",children:[O.cityAr,(0,t.jsxs)("span",{className:"text-gray-400 text-sm",children:["(",O.optional,")"]})]}),(0,t.jsx)(u.p,{id:"cityAr",value:P.cityAr,onChange:e=>S(r=>({...r,cityAr:e.target.value})),placeholder:O.cityArPlaceholder,dir:"rtl",className:"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg"})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(g.J,{htmlFor:"countryAr",className:"text-sm font-semibold text-gray-700 dark:text-gray-300",children:O.countryAr}),(0,t.jsx)(u.p,{id:"countryAr",value:P.countryAr,onChange:e=>S(r=>({...r,countryAr:e.target.value})),placeholder:"أدخل اسم البلد بالعربية",dir:"rtl",className:"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg"})]})]})]})]})]}),4===A&&(0,t.jsxs)(b.Zp,{className:"shadow-xl border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm",children:[(0,t.jsx)(b.aR,{className:"pb-6 bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-900/20 dark:to-amber-900/20 rounded-t-lg",children:(0,t.jsxs)(b.ZB,{className:"text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-10 h-10 bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg",children:"4"}),O.additionalInfo]})}),(0,t.jsxs)(b.Wu,{className:"space-y-8 p-8",children:[(0,t.jsx)(E,{images:P.images,onImagesChange:e=>S(r=>({...r,images:e})),onAutoSave:v?z:void 0,onUploadStatusChange:F,propertyId:k,maxImages:10,disabled:a}),(0,t.jsx)(j,{className:"my-8"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(g.J,{htmlFor:"utilities",className:"text-sm font-semibold text-gray-700 dark:text-gray-300",children:O.utilities}),(0,t.jsx)(x.T,{id:"utilities",value:P.utilities,onChange:e=>S(r=>({...r,utilities:e.target.value})),placeholder:O.utilitiesPlaceholder,rows:3,className:"border-2 border-gray-200 dark:border-gray-600 focus:border-orange-500 dark:focus:border-orange-400 transition-all duration-200 resize-none rounded-lg"})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(g.J,{htmlFor:"utilitiesAr",className:"text-sm font-semibold text-gray-700 dark:text-gray-300",children:O.utilitiesAr}),(0,t.jsx)(x.T,{id:"utilitiesAr",value:P.utilitiesAr,onChange:e=>S(r=>({...r,utilitiesAr:e.target.value})),placeholder:O.utilitiesArPlaceholder,dir:"rtl",rows:3,className:"border-2 border-gray-200 dark:border-gray-600 focus:border-orange-500 dark:focus:border-orange-400 transition-all duration-200 resize-none rounded-lg"})]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(g.J,{htmlFor:"contactInfo",className:"text-sm font-semibold text-gray-700 dark:text-gray-300",children:O.contactInfo}),(0,t.jsx)(x.T,{id:"contactInfo",value:P.contactInfo,onChange:e=>S(r=>({...r,contactInfo:e.target.value})),placeholder:O.contactPlaceholder,rows:3,className:"border-2 border-gray-200 dark:border-gray-600 focus:border-orange-500 dark:focus:border-orange-400 transition-all duration-200 resize-none rounded-lg"})]}),(0,t.jsx)(j,{className:"my-8"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-8",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-orange-400 transition-colors",children:[(0,t.jsx)(p.d,{id:"furnished",checked:P.furnished,onCheckedChange:e=>S(r=>({...r,furnished:e})),className:"data-[state=checked]:bg-orange-600"}),(0,t.jsx)(g.J,{htmlFor:"furnished",className:"text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer",children:O.furnished})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3 p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-orange-400 transition-colors",children:[(0,t.jsx)(p.d,{id:"petFriendly",checked:P.petFriendly,onCheckedChange:e=>S(r=>({...r,petFriendly:e})),className:"data-[state=checked]:bg-orange-600"}),(0,t.jsx)(g.J,{htmlFor:"petFriendly",className:"text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer",children:O.petFriendly})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3 p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-orange-400 transition-colors",children:[(0,t.jsx)(p.d,{id:"isFeatured",checked:P.isFeatured,onCheckedChange:e=>S(r=>({...r,isFeatured:e})),className:"data-[state=checked]:bg-orange-600"}),(0,t.jsx)(g.J,{htmlFor:"isFeatured",className:"text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer",children:O.featured})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3 p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-orange-400 transition-colors",children:[(0,t.jsx)(p.d,{id:"isActive",checked:P.isActive,onCheckedChange:e=>S(r=>({...r,isActive:e})),className:"data-[state=checked]:bg-orange-600"}),(0,t.jsx)(g.J,{htmlFor:"isActive",className:"text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer",children:O.active})]})]})]})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center pt-8",children:[(0,t.jsxs)(m.$,{type:"button",variant:"outline",onClick:()=>{A>1&&C(A-1)},disabled:1===A,className:"flex items-center gap-2 px-6 py-3 h-12 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors",children:[(0,t.jsx)(o.A,{className:"h-4 w-4"}),O.previous]}),(0,t.jsx)("div",{className:"flex gap-4",children:A<4?(0,t.jsxs)(m.$,{type:"button",onClick:()=>{A<4&&C(A+1)},disabled:!Z(A),className:"flex items-center gap-2 px-8 py-3 h-12 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl",children:[O.next,(0,t.jsx)(n.A,{className:"h-4 w-4"})]}):(0,t.jsxs)(m.$,{type:"submit",disabled:a||I||!Z(A),className:"flex items-center gap-2 px-8 py-3 h-12 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl",children:[a||I?(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}):(0,t.jsx)(c.A,{className:"h-4 w-4"}),I?"ar"===w?"جاري رفع الصور...":"Uploading images...":O.save]})})]})]})]})}},45300:(e,r,a)=>{function t(){["__next_hmr_refresh_hash__","__clerk_db_jwt_NFfxy5s4","__refresh_NFfxy5s4","__session_NFfxy5s4","__client_uat_NFfxy5s4","__clerk_db_jwt_TYLMw0H7","__refresh_TYLMw0H7","__session_TYLMw0H7","__client_uat_TYLMw0H7","__clerk_db_jwt","__clerk_db_jwt_kCaGdcWF","__client_uat_kCaGdcWF","__client_uat","NEXT_LOCALE","authjs.csrf-token","authjs.callback-url"].forEach(e=>{document.cookie="".concat(e,"=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;"),[window.location.hostname,".".concat(window.location.hostname),"localhost",".localhost"].forEach(r=>{document.cookie="".concat(e,"=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=").concat(r,";")})}),document.cookie="language=ar; path=/; max-age=31536000",console.log("\uD83E\uDDF9 Cookies cleaned up for Arabic Properties system")}function s(e){for(var r=arguments.length,a=Array(r>1?r-1:0),t=1;t<r;t++)a[t-1]=arguments[t]}a.d(r,{Ei:()=>s,ss:()=>t})},53999:(e,r,a)=>{a.d(r,{cn:()=>l});var t=a(52596),s=a(39688);function l(){for(var e=arguments.length,r=Array(e),a=0;a<e;a++)r[a]=arguments[a];return(0,s.QP)((0,t.$)(r))}},82714:(e,r,a)=>{a.d(r,{J:()=>n});var t=a(95155),s=a(12115),l=a(40968),d=a(74466),i=a(53999);let o=(0,d.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),n=s.forwardRef((e,r)=>{let{className:a,...s}=e;return(0,t.jsx)(l.b,{ref:r,className:(0,i.cn)(o(),a),...s})});n.displayName=l.b.displayName},86132:(e,r,a)=>{a.d(r,{Y:()=>l});var t=a(12115),s=a(45300);function l(){let[e,r]=(0,t.useState)("ar");return(0,t.useEffect)(()=>{(0,s.ss)();let e=localStorage.getItem("properties-language");("en"===e||"ar"===e)&&r(e),(0,s.Ei)("\uD83C\uDFE0 Bilingual Properties system initialized")},[]),(0,t.useEffect)(()=>{localStorage.setItem("properties-language",e),document.documentElement.lang=e,document.documentElement.dir="ar"===e?"rtl":"ltr",document.documentElement.className="ar"===e?"rtl arabic-interface":"ltr english-interface","ar"===e?document.body.style.fontFamily="'Cairo', 'Noto Sans Arabic', 'Tajawal', 'Amiri', sans-serif":document.body.style.fontFamily="'Inter', 'Segoe UI', 'Roboto', sans-serif",(0,s.Ei)("\uD83C\uDF10 Language switched to: ".concat(e))},[e]),{language:e,setLanguage:e=>{r(e)},isRTL:"ar"===e,isArabic:"ar"===e,isEnglish:"en"===e}}},88145:(e,r,a)=>{a.d(r,{E:()=>i});var t=a(95155);a(12115);var s=a(74466),l=a(53999);let d=(0,s.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:r,variant:a,...s}=e;return(0,t.jsx)("div",{className:(0,l.cn)(d({variant:a}),r),...s})}},88482:(e,r,a)=>{a.d(r,{BT:()=>n,Wu:()=>c,ZB:()=>o,Zp:()=>d,aR:()=>i,wL:()=>m});var t=a(95155),s=a(12115),l=a(53999);let d=s.forwardRef((e,r)=>{let{className:a,...s}=e;return(0,t.jsx)("div",{ref:r,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...s})});d.displayName="Card";let i=s.forwardRef((e,r)=>{let{className:a,...s}=e;return(0,t.jsx)("div",{ref:r,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",a),...s})});i.displayName="CardHeader";let o=s.forwardRef((e,r)=>{let{className:a,...s}=e;return(0,t.jsx)("div",{ref:r,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",a),...s})});o.displayName="CardTitle";let n=s.forwardRef((e,r)=>{let{className:a,...s}=e;return(0,t.jsx)("div",{ref:r,className:(0,l.cn)("text-sm text-muted-foreground",a),...s})});n.displayName="CardDescription";let c=s.forwardRef((e,r)=>{let{className:a,...s}=e;return(0,t.jsx)("div",{ref:r,className:(0,l.cn)("p-6 pt-0",a),...s})});c.displayName="CardContent";let m=s.forwardRef((e,r)=>{let{className:a,...s}=e;return(0,t.jsx)("div",{ref:r,className:(0,l.cn)("flex items-center p-6 pt-0",a),...s})});m.displayName="CardFooter"},89852:(e,r,a)=>{a.d(r,{p:()=>d});var t=a(95155),s=a(12115),l=a(53999);let d=s.forwardRef((e,r)=>{let{className:a,type:s,...d}=e;return(0,t.jsx)("input",{type:s,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:r,...d})});d.displayName="Input"},90088:(e,r,a)=>{a.d(r,{d:()=>i});var t=a(95155),s=a(12115),l=a(4884),d=a(53999);let i=s.forwardRef((e,r)=>{let{className:a,...s}=e;return(0,t.jsx)(l.bL,{className:(0,d.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",a),...s,ref:r,children:(0,t.jsx)(l.zi,{className:(0,d.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});i.displayName=l.bL.displayName},95784:(e,r,a)=>{a.d(r,{bq:()=>u,eb:()=>p,gC:()=>h,l6:()=>c,yv:()=>m});var t=a(95155),s=a(12115),l=a(31992),d=a(66474),i=a(47863),o=a(5196),n=a(53999);let c=l.bL;l.YJ;let m=l.WT,u=s.forwardRef((e,r)=>{let{className:a,children:s,...i}=e;return(0,t.jsxs)(l.l9,{ref:r,className:(0,n.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...i,children:[s,(0,t.jsx)(l.In,{asChild:!0,children:(0,t.jsx)(d.A,{className:"h-4 w-4 opacity-50"})})]})});u.displayName=l.l9.displayName;let g=s.forwardRef((e,r)=>{let{className:a,...s}=e;return(0,t.jsx)(l.PP,{ref:r,className:(0,n.cn)("flex cursor-default items-center justify-center py-1",a),...s,children:(0,t.jsx)(i.A,{className:"h-4 w-4"})})});g.displayName=l.PP.displayName;let x=s.forwardRef((e,r)=>{let{className:a,...s}=e;return(0,t.jsx)(l.wn,{ref:r,className:(0,n.cn)("flex cursor-default items-center justify-center py-1",a),...s,children:(0,t.jsx)(d.A,{className:"h-4 w-4"})})});x.displayName=l.wn.displayName;let h=s.forwardRef((e,r)=>{let{className:a,children:s,position:d="popper",...i}=e;return(0,t.jsx)(l.ZL,{children:(0,t.jsxs)(l.UC,{ref:r,className:(0,n.cn)("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]","popper"===d&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:d,...i,children:[(0,t.jsx)(g,{}),(0,t.jsx)(l.LM,{className:(0,n.cn)("p-1","popper"===d&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,t.jsx)(x,{})]})})});h.displayName=l.UC.displayName,s.forwardRef((e,r)=>{let{className:a,...s}=e;return(0,t.jsx)(l.JU,{ref:r,className:(0,n.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...s})}).displayName=l.JU.displayName;let p=s.forwardRef((e,r)=>{let{className:a,children:s,...d}=e;return(0,t.jsxs)(l.q7,{ref:r,className:(0,n.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...d,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(l.VF,{children:(0,t.jsx)(o.A,{className:"h-4 w-4"})})}),(0,t.jsx)(l.p4,{children:s})]})});p.displayName=l.q7.displayName,s.forwardRef((e,r)=>{let{className:a,...s}=e;return(0,t.jsx)(l.wv,{ref:r,className:(0,n.cn)("-mx-1 my-1 h-px bg-muted",a),...s})}).displayName=l.wv.displayName},97168:(e,r,a)=>{a.d(r,{$:()=>n,r:()=>o});var t=a(95155),s=a(12115),l=a(99708),d=a(74466),i=a(53999);let o=(0,d.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),n=s.forwardRef((e,r)=>{let{className:a,variant:s,size:d,asChild:n=!1,...c}=e,m=n?l.DX:"button";return(0,t.jsx)(m,{className:(0,i.cn)(o({variant:s,size:d,className:a})),ref:r,...c})});n.displayName="Button"},99474:(e,r,a)=>{a.d(r,{T:()=>d});var t=a(95155),s=a(12115),l=a(53999);let d=s.forwardRef((e,r)=>{let{className:a,...s}=e;return(0,t.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:r,...s})});d.displayName="Textarea"}}]);