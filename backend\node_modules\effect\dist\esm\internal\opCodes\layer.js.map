{"version": 3, "file": "layer.js", "names": ["OP_EXTEND_SCOPE", "OP_FOLD", "OP_FRESH", "OP_FROM_EFFECT", "OP_SCOPED", "OP_SUSPEND", "OP_PROVIDE", "OP_PROVIDE_MERGE", "OP_ZIP_WITH"], "sources": ["../../../../src/internal/opCodes/layer.ts"], "sourcesContent": [null], "mappings": "AAAA;AACA,OAAO,MAAMA,eAAe,GAAG,aAAsB;AAKrD;AACA,OAAO,MAAMC,OAAO,GAAG,MAAe;AAKtC;AACA,OAAO,MAAMC,QAAQ,GAAG,OAAgB;AAKxC;AACA,OAAO,MAAMC,cAAc,GAAG,YAAqB;AAKnD;AACA,OAAO,MAAMC,SAAS,GAAG,QAAiB;AAK1C;AACA,OAAO,MAAMC,UAAU,GAAG,SAAkB;AAK5C;AACA,OAAO,MAAMC,UAAU,GAAG,SAAkB;AAK5C;AACA,OAAO,MAAMC,gBAAgB,GAAG,cAAuB;AAKvD;AACA,OAAO,MAAMC,WAAW,GAAG,SAAkB", "ignoreList": []}