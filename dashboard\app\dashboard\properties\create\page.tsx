'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Save, Home, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useSimpleLanguage } from '@/hooks/useSimpleLanguage';
import { LanguageSwitcher } from '@/components/LanguageSwitcher';
import { ThemeSwitcher } from '@/components/ThemeSwitcher';
import { toast } from 'sonner';
import { PropertyFormSteps } from './property-form-steps';

export default function CreatePropertyPage() {
  const router = useRouter();
  const { language } = useSimpleLanguage();
  const [loading, setLoading] = useState(false);

  // Saudi-focused bilingual translations
  const translations = {
    ar: {
      createProperty: 'إنشاء عقار سعودي جديد',
      backToProperties: 'العودة إلى العقارات',
      subtitle: 'أضف عقار جديد في المملكة العربية السعودية مع معلومات مفصلة وصور عالية الجودة',
      properties: 'العقارات',
      home: 'الرئيسية',
      welcome: 'مرحباً بك',
      newProperty: 'عقار سعودي جديد',
      fillDetails: 'املأ التفاصيل المطلوبة لإضافة عقار جديد في المملكة',
      ready: 'جاهز للبدء',
      saudiInterface: 'نظام العقارات السعودي',
      clickToReturn: 'اضغط للعودة',
      dashboard: 'لوحة التحكم',
      saudiProperties: 'العقارات السعودية',
    },
    en: {
      createProperty: 'Create New Saudi Property',
      backToProperties: 'Back to Properties',
      subtitle: 'Add a new property in Saudi Arabia with detailed information and high-quality images',
      properties: 'Properties',
      home: 'Home',
      welcome: 'Welcome',
      newProperty: 'New Saudi Property',
      fillDetails: 'Fill in the required details to add a new property in Saudi Arabia',
      ready: 'Ready to Start',
      saudiInterface: 'Saudi Properties System',
      clickToReturn: 'Click to Return',
      dashboard: 'Dashboard',
      saudiProperties: 'Saudi Properties',
    }
  };

  const t = translations[language];

  const handleSave = async (formData: any) => {
    setLoading(true);
    try {
      const payload = {
        ...formData,
        price: parseFloat(formData.price),
        bedrooms: formData.bedrooms ? parseInt(formData.bedrooms) : undefined,
        bathrooms: formData.bathrooms ? parseInt(formData.bathrooms) : undefined,
        area: formData.area ? parseFloat(formData.area) : undefined,
        yearBuilt: formData.yearBuilt ? parseInt(formData.yearBuilt) : undefined,
        parking: formData.parking ? parseInt(formData.parking) : undefined,
      };

      const response = await fetch('/api/v1/properties', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (response.ok) {
        toast.success('تم إنشاء العقار بنجاح ✨');
        router.push('/dashboard/properties');
      } else {
        const error = await response.json();
        toast.error(error.message || 'فشل في إنشاء العقار ❌');
      }
    } catch (error) {
      console.error('Error creating property:', error);
      toast.error('فشل في إنشاء العقار ❌');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={`min-h-screen bg-gradient-to-br from-slate-900 via-emerald-900/20 to-teal-900/20 ${language === 'ar' ? 'rtl' : 'ltr'}`}>
      <div className="container mx-auto px-8 py-12">
        {/* Enhanced Bilingual Breadcrumb */}
        <nav className={`flex items-center gap-4 text-sm mb-12 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`} dir={language === 'ar' ? 'rtl' : 'ltr'}>
          <div className="flex items-center gap-3 px-4 py-3 bg-slate-800/80 rounded-xl backdrop-blur-md shadow-lg border border-slate-700/20">
            <div className="w-8 h-8 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-lg flex items-center justify-center">
              <Home className="h-4 w-4 text-white" />
            </div>
            <span className="font-bold text-white">{t.home}</span>
          </div>
          <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
          <button
            onClick={() => router.push('/dashboard/properties')}
            className="flex items-center gap-3 px-4 py-3 bg-slate-800/60 rounded-xl backdrop-blur-md hover:bg-slate-800/90 transition-all duration-300 hover:shadow-lg border border-slate-700/20 hover:border-emerald-600"
          >
            <span className="font-bold text-slate-300 hover:text-emerald-400">{t.properties}</span>
          </button>
          <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
          <div className="flex items-center gap-3 px-4 py-3 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-xl shadow-lg">
            <span className="font-bold text-white">{t.newProperty}</span>
          </div>
        </nav>

        {/* Enhanced Bilingual Header */}
        <div className="relative mb-16">
          {/* Background Pattern */}
          <div className="absolute inset-0 bg-gradient-to-r from-emerald-100/50 to-teal-100/50 dark:from-emerald-900/20 dark:to-teal-900/20 rounded-3xl"></div>
          <div className="absolute inset-0 opacity-30">
            <div className="w-full h-full bg-gradient-to-br from-emerald-50/30 via-transparent to-teal-50/30 dark:from-emerald-900/10 dark:via-transparent dark:to-teal-900/10 rounded-3xl"></div>
          </div>

          <div className="relative p-8 lg:p-12">
            <div className={`flex flex-col lg:flex-row lg:items-center lg:justify-between gap-8 ${language === 'ar' ? 'lg:flex-row-reverse' : ''}`}>
              <div className="space-y-6">
                <div className={`flex items-center gap-4 ${language === 'ar' ? 'flex-row-reverse' : ''}`} dir={language === 'ar' ? 'rtl' : 'ltr'}>
                  <div className="relative">
                    <div className="w-16 h-16 bg-gradient-to-br from-emerald-500 via-teal-500 to-cyan-600 rounded-2xl flex items-center justify-center shadow-2xl shadow-emerald-200 dark:shadow-emerald-900/50">
                      <Home className="h-8 w-8 text-white" />
                    </div>
                    <div className="absolute -top-1 -right-1 w-6 h-6 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">+</span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium text-emerald-600 dark:text-emerald-400 bg-emerald-100 dark:bg-emerald-900/30 px-3 py-1 rounded-full">
                        {t.welcome}
                      </span>
                    </div>
                    <h1 className="text-5xl lg:text-6xl font-black bg-gradient-to-r from-emerald-600 via-teal-600 to-cyan-600 bg-clip-text text-transparent leading-tight">
                      {t.createProperty}
                    </h1>
                    <div className="flex items-center gap-2 mt-3">
                      <div className="h-1 w-12 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full"></div>
                      <div className="h-1 w-8 bg-gradient-to-r from-teal-500 to-cyan-500 rounded-full"></div>
                      <div className="h-1 w-4 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-full"></div>
                    </div>
                  </div>
                </div>
                <p className={`text-xl text-slate-700 dark:text-slate-300 max-w-3xl leading-relaxed font-medium ${language === 'ar' ? 'text-right' : 'text-left'}`} dir={language === 'ar' ? 'rtl' : 'ltr'}>
                  {t.fillDetails}
                </p>
                <div className={`flex items-center gap-4 ${language === 'ar' ? 'flex-row-reverse' : ''}`} dir={language === 'ar' ? 'rtl' : 'ltr'}>
                  <div className="flex items-center gap-2 text-emerald-600 dark:text-emerald-400">
                    <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
                    <span className="text-sm font-medium">{t.ready}</span>
                  </div>
                  <div className="flex items-center gap-2 text-teal-600 dark:text-teal-400">
                    <div className="w-2 h-2 bg-teal-500 rounded-full animate-pulse delay-100"></div>
                    <span className="text-sm font-medium">{t.arabicInterface}</span>
                  </div>
                </div>
              </div>

              <div className="flex flex-col gap-4">
                {/* Language and Theme Switchers */}
                <div className={`flex items-center gap-3 ${language === 'ar' ? 'flex-row-reverse' : ''}`}>
                  <LanguageSwitcher />
                  <ThemeSwitcher />
                </div>

                <Button
                  variant="outline"
                  onClick={() => router.push('/dashboard/properties')}
                  className={`flex items-center gap-3 px-8 py-4 h-14 bg-white/90 dark:bg-slate-800/90 backdrop-blur-md border-2 border-emerald-200 dark:border-emerald-700 hover:bg-white dark:hover:bg-slate-800 hover:shadow-xl hover:border-emerald-300 dark:hover:border-emerald-600 transition-all duration-300 text-slate-800 dark:text-slate-200 rounded-xl ${language === 'ar' ? 'flex-row-reverse' : ''}`}
                  dir={language === 'ar' ? 'rtl' : 'ltr'}
                >
                  <span className="font-bold text-lg">{t.backToProperties}</span>
                  <ArrowLeft className={`h-6 w-6 ${language === 'ar' ? 'rotate-180' : ''}`} />
                </Button>
                <div className="text-center">
                  <span className="text-xs text-slate-500 dark:text-slate-400">{t.clickToReturn}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Property Form */}
        <PropertyFormSteps onSave={handleSave} loading={loading} />
      </div>
    </div>
  );
}
