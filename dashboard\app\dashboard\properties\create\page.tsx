'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Save, Home, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useLanguage } from '@/hooks/useLanguage';
import { toast } from 'sonner';
import { PropertyFormSteps } from './property-form-steps';

export default function CreatePropertyPage() {
  const router = useRouter();
  const { language } = useLanguage();
  const [loading, setLoading] = useState(false);

  const translations = {
    en: {
      createProperty: 'Create New Property',
      backToProperties: 'Back to Properties',
      subtitle: 'Add a new property to your listings with detailed information and images',
      properties: 'Properties',
    },
    ar: {
      createProperty: 'إنشاء عقار جديد',
      backToProperties: 'العودة إلى العقارات',
      subtitle: 'أضف عقار جديد إلى قائمة العقارات الخاصة بك مع معلومات مفصلة وصور',
      properties: 'العقارات',
    }
  };

  const t = translations[language as keyof typeof translations];

  const handleSave = async (formData: any) => {
    setLoading(true);
    try {
      const payload = {
        ...formData,
        price: parseFloat(formData.price),
        bedrooms: formData.bedrooms ? parseInt(formData.bedrooms) : undefined,
        bathrooms: formData.bathrooms ? parseInt(formData.bathrooms) : undefined,
        area: formData.area ? parseFloat(formData.area) : undefined,
        yearBuilt: formData.yearBuilt ? parseInt(formData.yearBuilt) : undefined,
        parking: formData.parking ? parseInt(formData.parking) : undefined,
      };

      const response = await fetch('/api/v1/properties', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (response.ok) {
        toast.success(language === 'ar' ? 'تم إنشاء العقار بنجاح' : 'Property created successfully');
        router.push('/dashboard/properties');
      } else {
        const error = await response.json();
        toast.error(error.message || (language === 'ar' ? 'فشل في إنشاء العقار' : 'Failed to create property'));
      }
    } catch (error) {
      console.error('Error creating property:', error);
      toast.error(language === 'ar' ? 'فشل في إنشاء العقار' : 'Failed to create property');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={`min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 ${language === 'ar' ? 'rtl' : 'ltr'}`}>
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400 mb-8" dir={language === 'ar' ? 'rtl' : 'ltr'}>
          <Home className="h-4 w-4" />
          <ChevronRight className="h-4 w-4" />
          <button
            onClick={() => router.push('/dashboard/properties')}
            className="hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
          >
            {t.properties}
          </button>
          <ChevronRight className="h-4 w-4" />
          <span className="text-gray-900 dark:text-gray-100">{t.createProperty}</span>
        </nav>

        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              {t.createProperty}
            </h1>
            <p className="text-gray-600 dark:text-gray-400 max-w-2xl">
              {t.subtitle}
            </p>
          </div>
          <Button
            variant="outline"
            onClick={() => router.push('/dashboard/properties')}
            className="flex items-center gap-2 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
            {t.backToProperties}
          </Button>
        </div>

        {/* Property Form */}
        <PropertyFormSteps onSave={handleSave} loading={loading} />
      </div>
    </div>
  );
}
