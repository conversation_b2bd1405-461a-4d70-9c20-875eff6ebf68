'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Save, Home, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useSimpleLanguage } from '@/hooks/useSimpleLanguage';
import { LanguageSwitcher } from '@/components/LanguageSwitcher';
import { ThemeSwitcher } from '@/components/ThemeSwitcher';
import { toast } from 'sonner';
import { PropertyFormSteps } from './property-form-steps';

export default function CreatePropertyPage() {
  const router = useRouter();
  const { language } = useSimpleLanguage();
  const [loading, setLoading] = useState(false);

  // Saudi-focused bilingual translations
  const translations = {
    ar: {
      createProperty: 'إنشاء عقار سعودي جديد',
      backToProperties: 'العودة إلى العقارات',
      subtitle: 'أضف عقار جديد في المملكة العربية السعودية مع معلومات مفصلة وصور عالية الجودة',
      properties: 'العقارات',
      home: 'الرئيسية',
      welcome: 'مرحباً بك',
      newProperty: 'عقار سعودي جديد',
      fillDetails: 'املأ التفاصيل المطلوبة لإضافة عقار جديد في المملكة',
      ready: 'جاهز للبدء',
      saudiInterface: 'نظام العقارات السعودي',
      clickToReturn: 'اضغط للعودة',
      dashboard: 'لوحة التحكم',
      saudiProperties: 'العقارات السعودية',
    },
    en: {
      createProperty: 'Create New Saudi Property',
      backToProperties: 'Back to Properties',
      subtitle: 'Add a new property in Saudi Arabia with detailed information and high-quality images',
      properties: 'Properties',
      home: 'Home',
      welcome: 'Welcome',
      newProperty: 'New Saudi Property',
      fillDetails: 'Fill in the required details to add a new property in Saudi Arabia',
      ready: 'Ready to Start',
      saudiInterface: 'Saudi Properties System',
      clickToReturn: 'Click to Return',
      dashboard: 'Dashboard',
      saudiProperties: 'Saudi Properties',
    }
  };

  const t = translations[language];

  const handleSave = async (formData: any) => {
    setLoading(true);
    try {
      const payload = {
        ...formData,
        price: parseFloat(formData.price),
        bedrooms: formData.bedrooms ? parseInt(formData.bedrooms) : undefined,
        bathrooms: formData.bathrooms ? parseInt(formData.bathrooms) : undefined,
        area: formData.area ? parseFloat(formData.area) : undefined,
        yearBuilt: formData.yearBuilt ? parseInt(formData.yearBuilt) : undefined,
        parking: formData.parking ? parseInt(formData.parking) : undefined,
      };

      const response = await fetch('/api/v1/properties', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (response.ok) {
        toast.success(language === 'ar' ? 'تم إنشاء العقار بنجاح ✨' : 'Property created successfully ✨');
        // Auto-redirect after success
        setTimeout(() => {
          window.location.href = '/dashboard/properties';
        }, 1000);
      } else {
        const error = await response.json();
        toast.error(error.message || (language === 'ar' ? 'فشل في إنشاء العقار ❌' : 'Failed to create property ❌'));
      }
    } catch (error) {
      console.error('Error creating property:', error);
      toast.error('فشل في إنشاء العقار ❌');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={`min-h-screen bg-slate-900 ${language === 'ar' ? 'rtl' : 'ltr'}`}>
      <div className="container mx-auto px-8 py-12">
        {/* Enhanced Bilingual Breadcrumb */}
        <nav className={`flex items-center gap-4 text-sm mb-12 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`} dir={language === 'ar' ? 'rtl' : 'ltr'}>
          <div className="flex items-center gap-3 px-4 py-3 bg-slate-800 rounded-lg border border-slate-700">
            <div className="w-8 h-8 bg-emerald-600 rounded-lg flex items-center justify-center">
              <Home className="h-4 w-4 text-white" />
            </div>
            <span className="font-medium text-white">{t.home}</span>
          </div>
          <div className="w-1 h-1 bg-slate-500 rounded-full"></div>
          <button
            onClick={() => router.push('/dashboard/properties')}
            className="flex items-center gap-3 px-4 py-3 bg-slate-800 rounded-lg border border-slate-700 hover:border-emerald-600"
          >
            <span className="font-medium text-slate-300 hover:text-emerald-400">{t.properties}</span>
          </button>
          <div className="w-1 h-1 bg-slate-500 rounded-full"></div>
          <div className="flex items-center gap-3 px-4 py-3 bg-emerald-600 rounded-lg">
            <span className="font-medium text-white">{t.newProperty}</span>
          </div>
        </nav>

        {/* Simple Header */}
        <div className="mb-12">
          <div className="bg-slate-800 rounded-lg border border-slate-700 p-8">
            <div className={`flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6 ${language === 'ar' ? 'lg:flex-row-reverse' : ''}`}>
              <div className="space-y-4">
                <div className={`flex items-center gap-4 ${language === 'ar' ? 'flex-row-reverse' : ''}`} dir={language === 'ar' ? 'rtl' : 'ltr'}>
                  <div className="w-12 h-12 bg-emerald-600 rounded-lg flex items-center justify-center">
                    <Home className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h1 className="text-3xl font-bold text-white">
                      {t.createProperty}
                    </h1>
                    <p className="text-slate-400 mt-1">
                      {t.fillDetails}
                    </p>
                  </div>
              </div>

              <div className="flex items-center gap-3">
                {/* Language and Theme Switchers */}
                <LanguageSwitcher />
                <ThemeSwitcher />

                <Button
                  variant="outline"
                  onClick={() => router.push('/dashboard/properties')}
                  className="flex items-center gap-2 px-4 py-2 bg-slate-700 border-slate-600 hover:bg-slate-600 text-white rounded-lg"
                >
                  <ArrowLeft className={`h-4 w-4 ${language === 'ar' ? 'rotate-180' : ''}`} />
                  <span>{t.backToProperties}</span>
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Property Form */}
        <PropertyFormSteps onSave={handleSave} loading={loading} />
      </div>
    </div>
  );
}
