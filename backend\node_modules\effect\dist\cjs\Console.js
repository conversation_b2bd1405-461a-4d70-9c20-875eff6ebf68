"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.withTime = exports.withGroup = exports.withConsole = exports.warn = exports.trace = exports.timeLog = exports.time = exports.table = exports.setConsole = exports.log = exports.info = exports.group = exports.error = exports.dirxml = exports.dir = exports.debug = exports.countReset = exports.count = exports.consoleWith = exports.clear = exports.assert = exports.TypeId = exports.Console = void 0;
var internal = _interopRequireWildcard(require("./internal/console.js"));
var defaultConsole = _interopRequireWildcard(require("./internal/defaultServices/console.js"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
/**
 * @since 2.0.0
 * @category type ids
 */
const TypeId = exports.TypeId = defaultConsole.TypeId;
/**
 * @since 2.0.0
 * @category context
 */
const Console = exports.Console = defaultConsole.consoleTag;
/**
 * @since 2.0.0
 * @category default services
 */
const withConsole = exports.withConsole = internal.withConsole;
/**
 * @since 2.0.0
 * @category default services
 */
const setConsole = exports.setConsole = internal.setConsole;
/**
 * @since 2.0.0
 * @category accessor
 */
const consoleWith = exports.consoleWith = internal.consoleWith;
/**
 * @since 2.0.0
 * @category accessor
 */
const assert = exports.assert = internal.assert;
/**
 * @since 2.0.0
 * @category accessor
 */
const clear = exports.clear = internal.clear;
/**
 * @since 2.0.0
 * @category accessor
 */
const count = exports.count = internal.count;
/**
 * @since 2.0.0
 * @category accessor
 */
const countReset = exports.countReset = internal.countReset;
/**
 * @since 2.0.0
 * @category accessor
 */
const debug = exports.debug = internal.debug;
/**
 * @since 2.0.0
 * @category accessor
 */
const dir = exports.dir = internal.dir;
/**
 * @since 2.0.0
 * @category accessor
 */
const dirxml = exports.dirxml = internal.dirxml;
/**
 * @since 2.0.0
 * @category accessor
 */
const error = exports.error = internal.error;
/**
 * @since 2.0.0
 * @category accessor
 */
const group = exports.group = internal.group;
/**
 * @since 2.0.0
 * @category accessor
 */
const info = exports.info = internal.info;
/**
 * @since 2.0.0
 * @category accessor
 */
const log = exports.log = internal.log;
/**
 * @since 2.0.0
 * @category accessor
 */
const table = exports.table = internal.table;
/**
 * @since 2.0.0
 * @category accessor
 */
const time = exports.time = internal.time;
/**
 * @since 2.0.0
 * @category accessor
 */
const timeLog = exports.timeLog = internal.timeLog;
/**
 * @since 2.0.0
 * @category accessor
 */
const trace = exports.trace = internal.trace;
/**
 * @since 2.0.0
 * @category accessor
 */
const warn = exports.warn = internal.warn;
/**
 * @since 2.0.0
 * @category accessor
 */
const withGroup = exports.withGroup = internal.withGroup;
/**
 * @since 2.0.0
 * @category accessor
 */
const withTime = exports.withTime = internal.withTime;
//# sourceMappingURL=Console.js.map