(()=>{var e={};e.id=3594,e.ids=[3594],e.modules={1708:e=>{"use strict";e.exports=require("node:process")},3121:(e,r,t)=>{Promise.resolve().then(t.bind(t,96891))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5486:e=>{"use strict";e.exports=require("bcrypt")},7066:e=>{"use strict";e.exports=require("node:tty")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12849:(e,r,t)=>{Promise.resolve().then(t.bind(t,44473))},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23356:(e,r,t)=>{Promise.resolve().then(t.bind(t,87230))},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},44473:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\marketing\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\marketing\\page.tsx","default")},48161:e=>{"use strict";e.exports=require("node:os")},5e4:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>c,tree:()=>p});var s=t(65239),o=t(48088),a=t(88170),n=t.n(a),i=t(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(r,d);let p={children:["",{children:["dashboard",{children:["marketing",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,44473)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\marketing\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,58018)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\marketing\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,83249)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,60566)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,l=["C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\marketing\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/dashboard/marketing/page",pathname:"/dashboard/marketing",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},51455:e=>{"use strict";e.exports=require("node:fs/promises")},55511:e=>{"use strict";e.exports=require("crypto")},57975:e=>{"use strict";e.exports=require("node:util")},58018:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n,metadata:()=>a});var s=t(37413),o=t(71644);let a={title:"Marketing",description:"Marketing tools for your real estate business"};function n({children:e}){return(0,s.jsxs)("div",{className:"flex flex-col space-y-6 p-6",children:[(0,s.jsxs)("div",{className:"flex flex-col space-y-4",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Marketing"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Create and manage your marketing campaigns and templates"})]}),(0,s.jsx)(o.MarketingNav,{}),(0,s.jsx)("div",{children:e})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65212:(e,r,t)=>{Promise.resolve().then(t.bind(t,71644))},71644:(e,r,t)=>{"use strict";t.d(r,{MarketingNav:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call MarketingNav() from the server but MarketingNav is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\components\\marketing\\marketing-nav.tsx","MarketingNav")},73024:e=>{"use strict";e.exports=require("node:fs")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},87230:(e,r,t)=>{"use strict";t.d(r,{MarketingNav:()=>d});var s=t(60687),o=t(85814),a=t.n(o),n=t(16189),i=t(96241);function d(){let e=(0,n.usePathname)();return(0,s.jsx)("div",{className:"flex space-x-4 border-b pb-2",children:[{name:"Campaigns",href:"/dashboard/campaigns"},{name:"Templates",href:"/dashboard/marketing/templates"}].map(r=>(0,s.jsx)(a(),{href:r.href,className:(0,i.cn)("px-4 py-2 rounded-md transition-colors",e===r.href||e.startsWith(`${r.href}/`)?"bg-primary/10 text-primary font-medium":"hover:bg-primary/5 hover:text-primary"),children:r.name},r.href))})}},96330:e=>{"use strict";e.exports=require("@prisma/client")},96891:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var s=t(16189);function o(){return(0,s.useRouter)(),null}t(43210)}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7719,9656,2190,3903,5153,3555,8722,9464,381],()=>t(5e4));module.exports=s})();