import * as tailwindcss_types_config from 'tailwindcss/types/config';
import { Config } from 'tailwindcss';

/**
 * UploadThing Tailwind plugin which injects custom variants
 * for the built-in UI components
 * @see https://docs.uploadthing.com/concepts/theming#theming-with-tailwind-css
 *
 * When using this, you need to specify `content` manually. For automatic
 * detection, see {@link withUt}.
 */
declare const uploadthingPlugin: {
    handler: tailwindcss_types_config.PluginCreator;
    config?: Partial<tailwindcss_types_config.Config>;
};

/**
 * HOF for Tailwind config that adds the
 * {@link uploadthingPlugin} to the Tailwind config
 * as well as adds content paths to detect the necessary
 * classnames
 */
declare function withUt(twConfig: Config): tailwindcss_types_config.Config;

export { uploadthingPlugin, withUt };
