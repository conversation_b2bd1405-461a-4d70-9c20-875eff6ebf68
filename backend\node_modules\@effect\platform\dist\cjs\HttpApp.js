"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.withPreResponseHandler = exports.toWebHandlerRuntime = exports.toWebHandlerLayer = exports.toWebHandler = exports.toHandled = exports.currentPreResponseHandlers = exports.appendPreResponseHandler = void 0;
var Context = _interopRequireWildcard(require("effect/Context"));
var Effect = _interopRequireWildcard(require("effect/Effect"));
var Exit = _interopRequireWildcard(require("effect/Exit"));
var FiberRef = _interopRequireWildcard(require("effect/FiberRef"));
var Layer = _interopRequireWildcard(require("effect/Layer"));
var Runtime = _interopRequireWildcard(require("effect/Runtime"));
var Scope = _interopRequireWildcard(require("effect/Scope"));
var _Unify = require("effect/Unify");
var ServerError = _interopRequireWildcard(require("./HttpServerError.js"));
var ServerRequest = _interopRequireWildcard(require("./HttpServerRequest.js"));
var ServerResponse = _interopRequireWildcard(require("./HttpServerResponse.js"));
var internal = _interopRequireWildcard(require("./internal/httpApp.js"));
var internalMiddleware = _interopRequireWildcard(require("./internal/httpMiddleware.js"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
/**
 * @since 1.0.0
 */

const handledSymbol = /*#__PURE__*/Symbol.for("@effect/platform/HttpApp/handled");
/**
 * @since 1.0.0
 * @category combinators
 */
const toHandled = (self, handleResponse, middleware) => {
  const responded = Effect.withFiberRuntime(fiber => Effect.flatMap(self, response => {
    const request = Context.unsafeGet(fiber.currentContext, ServerRequest.HttpServerRequest);
    const handler = fiber.getFiberRef(currentPreResponseHandlers);
    if (handler._tag === "None") {
      ;
      request[handledSymbol] = true;
      return Effect.as(handleResponse(request, response), response);
    }
    return Effect.tap(handler.value(request, response), response => {
      ;
      request[handledSymbol] = true;
      return handleResponse(request, response);
    });
  }));
  const withErrorHandling = Effect.catchAllCause(responded, cause => Effect.withFiberRuntime(fiber => Effect.flatMap(ServerError.causeResponse(cause), ([response, cause]) => {
    const request = Context.unsafeGet(fiber.currentContext, ServerRequest.HttpServerRequest);
    const handler = fiber.getFiberRef(currentPreResponseHandlers);
    if (handler._tag === "None") {
      ;
      request[handledSymbol] = true;
      return Effect.zipRight(handleResponse(request, response), Effect.failCause(cause));
    }
    return Effect.zipRight(Effect.tap(handler.value(request, response), response => {
      ;
      request[handledSymbol] = true;
      return handleResponse(request, response);
    }), Effect.failCause(cause));
  })));
  const withMiddleware = (0, _Unify.unify)(middleware === undefined ? internalMiddleware.tracer(withErrorHandling) : Effect.matchCauseEffect(middleware(internalMiddleware.tracer(withErrorHandling)), {
    onFailure: cause => Effect.withFiberRuntime(fiber => {
      const request = Context.unsafeGet(fiber.currentContext, ServerRequest.HttpServerRequest);
      if (handledSymbol in request) {
        return Effect.void;
      }
      return Effect.matchCauseEffect(ServerError.causeResponse(cause), {
        onFailure: _cause => handleResponse(request, ServerResponse.empty({
          status: 500
        })),
        onSuccess: ([response]) => handleResponse(request, response)
      });
    }),
    onSuccess: response => Effect.withFiberRuntime(fiber => {
      const request = Context.unsafeGet(fiber.currentContext, ServerRequest.HttpServerRequest);
      return handledSymbol in request ? Effect.void : handleResponse(request, response);
    })
  }));
  return Effect.uninterruptible(Effect.scoped(withMiddleware));
};
/**
 * @since 1.0.0
 * @category fiber refs
 */
exports.toHandled = toHandled;
const currentPreResponseHandlers = exports.currentPreResponseHandlers = internal.currentPreResponseHandlers;
/**
 * @since 1.0.0
 * @category fiber refs
 */
const appendPreResponseHandler = exports.appendPreResponseHandler = internal.appendPreResponseHandler;
/**
 * @since 1.0.0
 * @category fiber refs
 */
const withPreResponseHandler = exports.withPreResponseHandler = internal.withPreResponseHandler;
/**
 * @since 1.0.0
 * @category conversions
 */
const toWebHandlerRuntime = runtime => {
  const run = Runtime.runFork(runtime);
  return (self, middleware) => {
    const resolveSymbol = Symbol.for("@effect/platform/HttpApp/resolve");
    const httpApp = toHandled(self, (request, response) => {
      ;
      request[resolveSymbol](ServerResponse.toWeb(response, {
        withoutBody: request.method === "HEAD",
        runtime
      }));
      return Effect.void;
    }, middleware);
    return (request, context) => new Promise(resolve => {
      const contextMap = new Map(runtime.context.unsafeMap);
      if (Context.isContext(context)) {
        for (const [key, value] of context.unsafeMap) {
          contextMap.set(key, value);
        }
      }
      const httpServerRequest = ServerRequest.fromWeb(request);
      contextMap.set(ServerRequest.HttpServerRequest.key, httpServerRequest);
      httpServerRequest[resolveSymbol] = resolve;
      const fiber = run(Effect.locally(httpApp, FiberRef.currentContext, Context.unsafeMake(contextMap)));
      request.signal?.addEventListener("abort", () => {
        fiber.unsafeInterruptAsFork(ServerError.clientAbortFiberId);
      }, {
        once: true
      });
    });
  };
};
/**
 * @since 1.0.0
 * @category conversions
 */
exports.toWebHandlerRuntime = toWebHandlerRuntime;
const toWebHandler = exports.toWebHandler = /*#__PURE__*/toWebHandlerRuntime(Runtime.defaultRuntime);
/**
 * @since 1.0.0
 * @category conversions
 */
const toWebHandlerLayer = (self, layer, middleware) => {
  const scope = Effect.runSync(Scope.make());
  const close = () => Effect.runPromise(Scope.close(scope, Exit.void));
  const build = Effect.map(Layer.toRuntime(layer), _ => toWebHandlerRuntime(_)(self, middleware));
  const runner = Effect.runPromise(Scope.extend(build, scope));
  const handler = (request, context) => runner.then(handler => handler(request, context));
  return {
    close,
    handler
  };
};
exports.toWebHandlerLayer = toWebHandlerLayer;
//# sourceMappingURL=HttpApp.js.map