{"version": 3, "file": "Random.js", "names": ["defaultServices", "internal", "RandomTypeId", "next", "nextInt", "nextBoolean", "nextRange", "nextIntBetween", "shuffle", "choice", "randomWith", "Random", "randomTag", "make"], "sources": ["../../src/Random.ts"], "sourcesContent": [null], "mappings": "AAQA,OAAO,KAAKA,eAAe,MAAM,+BAA+B;AAChE,OAAO,KAAKC,QAAQ,MAAM,sBAAsB;AAGhD;;;;AAIA,OAAO,MAAMC,YAAY,GAAkBD,QAAQ,CAACC,YAAY;AA0ChE;;;;;;AAMA,OAAO,MAAMC,IAAI,GAA0BH,eAAe,CAACG,IAAI;AAE/D;;;;;;AAMA,OAAO,MAAMC,OAAO,GAA0BJ,eAAe,CAACI,OAAO;AAErE;;;;;;AAMA,OAAO,MAAMC,WAAW,GAA2BL,eAAe,CAACK,WAAW;AAE9E;;;;;;;AAOA,OAAO,MAAMC,SAAS,GAAwDN,eAAe,CAACM,SAAS;AAEvG;;;;;;;AAOA,OAAO,MAAMC,cAAc,GAAwDP,eAAe,CAACO,cAAc;AAEjH;;;;;;AAMA,OAAO,MAAMC,OAAO,GAAgER,eAAe,CAACQ,OAAO;AAE3G;;;;;;;;;;;;;;;;AAgBA,OAAO,MAAMC,MAAM,GAKPT,eAAe,CAACS,MAAM;AAElC;;;;;;;AAOA,OAAO,MAAMC,UAAU,GACrBV,eAAe,CAACU,UAAU;AAE5B;;;;AAIA,OAAO,MAAMC,MAAM,GAAgCV,QAAQ,CAACW,SAAS;AAErE;;;;;;;;;;;;;;;;;;;;;;;AAuBA,OAAO,MAAMC,IAAI,GAA2BZ,QAAQ,CAACY,IAAI", "ignoreList": []}