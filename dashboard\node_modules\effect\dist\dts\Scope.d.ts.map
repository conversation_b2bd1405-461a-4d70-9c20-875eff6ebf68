{"version": 3, "file": "Scope.d.ts", "sourceRoot": "", "sources": ["../../src/Scope.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,KAAK,KAAK,OAAO,MAAM,cAAc,CAAA;AAC5C,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAC1C,OAAO,KAAK,KAAK,iBAAiB,MAAM,wBAAwB,CAAA;AAChE,OAAO,KAAK,KAAK,IAAI,MAAM,WAAW,CAAA;AAGtC,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAA;AAE7C;;;;;GAKG;AACH,eAAO,MAAM,WAAW,EAAE,OAAO,MAAyB,CAAA;AAE1D;;;;;GAKG;AACH,MAAM,MAAM,WAAW,GAAG,OAAO,WAAW,CAAA;AAE5C;;;;;GAKG;AACH,eAAO,MAAM,oBAAoB,EAAE,OAAO,MAAkC,CAAA;AAE5E;;;;;GAKG;AACH,MAAM,MAAM,oBAAoB,GAAG,OAAO,oBAAoB,CAAA;AAE9D;;;;;GAKG;AACH,MAAM,WAAW,KAAM,SAAQ,QAAQ;IACrC,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE,WAAW,CAAA;IACnC;;OAEG;IACH,QAAQ,CAAC,QAAQ,EAAE,iBAAiB,CAAC,iBAAiB,CAAA;CAcvD;AAED;;;;;GAKG;AACH,MAAM,WAAW,cAAe,SAAQ,KAAK,EAAE,QAAQ;IACrD,QAAQ,CAAC,CAAC,oBAAoB,CAAC,EAAE,oBAAoB,CAAA;CAQtD;AAED;;;;;GAKG;AACH,eAAO,MAAM,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,CAAyB,CAAA;AAErE;;GAEG;AACH,MAAM,CAAC,OAAO,WAAW,KAAK,CAAC;IAC7B;;;;;OAKG;IACH,KAAY,SAAS,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;IAClF;;;;;OAKG;IACH,KAAY,SAAS,GAAG,cAAc,CAAA;CACvC;AAED;;;;;;;;;GASG;AACH,eAAO,MAAM,YAAY,EAAE,CACzB,IAAI,EAAE,KAAK,EACX,SAAS,EAAE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,KAC9B,MAAM,CAAC,MAAM,CAAC,IAAI,CAA0B,CAAA;AAEjD;;;;;;;;;GASG;AACH,eAAO,MAAM,gBAAgB,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,CAAC,SAAS,KAAK,MAAM,CAAC,MAAM,CAAC,IAAI,CAClE,CAAA;AAE5B;;;;;;GAMG;AACH,eAAO,MAAM,KAAK,EAAE,CAAC,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,IAAI,CAAmB,CAAA;AAEtH;;;;;;;;GAQG;AACH,eAAO,MAAM,MAAM,EAAE;IACnB;;;;;;;;OAQG;IACH,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAA;IACnG;;;;;;;;OAQG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAA;CACrE,CAAA;AAE5B;;;;;;GAMG;AACH,eAAO,MAAM,IAAI,EAAE,CACjB,IAAI,EAAE,KAAK,EACX,QAAQ,EAAE,iBAAiB,CAAC,iBAAiB,KAC1C,MAAM,CAAC,MAAM,CAAC,cAAc,CAAkB,CAAA;AAEnD;;;;;;;;GAQG;AACH,eAAO,MAAM,GAAG,EAAE;IAChB;;;;;;;;OAQG;IACH,CAAC,KAAK,EAAE,cAAc,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAA;IAC5G;;;;;;;;OAQG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAA;CACjF,CAAA;AAEzB;;;;;;;GAOG;AACH,eAAO,MAAM,IAAI,EAAE,CACjB,iBAAiB,CAAC,EAAE,iBAAiB,CAAC,iBAAiB,KACpD,MAAM,CAAC,MAAM,CAAC,cAAc,CAA0B,CAAA"}