declare const image: {
    readonly "image/aces": {
        readonly source: "iana";
        readonly extensions: readonly ["exr"];
    };
    readonly "image/avci": {
        readonly source: "iana";
        readonly extensions: readonly ["avci"];
    };
    readonly "image/avcs": {
        readonly source: "iana";
        readonly extensions: readonly ["avcs"];
    };
    readonly "image/avif": {
        readonly source: "iana";
        readonly extensions: readonly ["avif"];
    };
    readonly "image/bmp": {
        readonly source: "iana";
        readonly extensions: readonly ["bmp"];
    };
    readonly "image/cgm": {
        readonly source: "iana";
        readonly extensions: readonly ["cgm"];
    };
    readonly "image/dicom-rle": {
        readonly source: "iana";
        readonly extensions: readonly ["drle"];
    };
    readonly "image/emf": {
        readonly source: "iana";
        readonly extensions: readonly ["emf"];
    };
    readonly "image/fits": {
        readonly source: "iana";
        readonly extensions: readonly ["fits"];
    };
    readonly "image/g3fax": {
        readonly source: "iana";
        readonly extensions: readonly ["g3"];
    };
    readonly "image/gif": {
        readonly source: "iana";
        readonly extensions: readonly ["gif"];
    };
    readonly "image/heic": {
        readonly source: "iana";
        readonly extensions: readonly ["heic"];
    };
    readonly "image/heic-sequence": {
        readonly source: "iana";
        readonly extensions: readonly ["heics"];
    };
    readonly "image/heif": {
        readonly source: "iana";
        readonly extensions: readonly ["heif"];
    };
    readonly "image/heif-sequence": {
        readonly source: "iana";
        readonly extensions: readonly ["heifs"];
    };
    readonly "image/hej2k": {
        readonly source: "iana";
        readonly extensions: readonly ["hej2"];
    };
    readonly "image/hsj2": {
        readonly source: "iana";
        readonly extensions: readonly ["hsj2"];
    };
    readonly "image/ief": {
        readonly source: "iana";
        readonly extensions: readonly ["ief"];
    };
    readonly "image/jls": {
        readonly source: "iana";
        readonly extensions: readonly ["jls"];
    };
    readonly "image/jp2": {
        readonly source: "iana";
        readonly extensions: readonly ["jp2", "jpg2"];
    };
    readonly "image/jpeg": {
        readonly source: "iana";
        readonly extensions: readonly ["jpeg", "jpg", "jpe", "jfif", "pjpeg", "pjp"];
    };
    readonly "image/jph": {
        readonly source: "iana";
        readonly extensions: readonly ["jph"];
    };
    readonly "image/jphc": {
        readonly source: "iana";
        readonly extensions: readonly ["jhc"];
    };
    readonly "image/jpm": {
        readonly source: "iana";
        readonly extensions: readonly ["jpm"];
    };
    readonly "image/jpx": {
        readonly source: "iana";
        readonly extensions: readonly ["jpx", "jpf"];
    };
    readonly "image/jxr": {
        readonly source: "iana";
        readonly extensions: readonly ["jxr"];
    };
    readonly "image/jxra": {
        readonly source: "iana";
        readonly extensions: readonly ["jxra"];
    };
    readonly "image/jxrs": {
        readonly source: "iana";
        readonly extensions: readonly ["jxrs"];
    };
    readonly "image/jxs": {
        readonly source: "iana";
        readonly extensions: readonly ["jxs"];
    };
    readonly "image/jxsc": {
        readonly source: "iana";
        readonly extensions: readonly ["jxsc"];
    };
    readonly "image/jxsi": {
        readonly source: "iana";
        readonly extensions: readonly ["jxsi"];
    };
    readonly "image/jxss": {
        readonly source: "iana";
        readonly extensions: readonly ["jxss"];
    };
    readonly "image/ktx": {
        readonly source: "iana";
        readonly extensions: readonly ["ktx"];
    };
    readonly "image/ktx2": {
        readonly source: "iana";
        readonly extensions: readonly ["ktx2"];
    };
    readonly "image/png": {
        readonly source: "iana";
        readonly extensions: readonly ["png"];
    };
    readonly "image/prs.btif": {
        readonly source: "iana";
        readonly extensions: readonly ["btif"];
    };
    readonly "image/prs.pti": {
        readonly source: "iana";
        readonly extensions: readonly ["pti"];
    };
    readonly "image/sgi": {
        readonly source: "apache";
        readonly extensions: readonly ["sgi"];
    };
    readonly "image/svg+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["svg", "svgz"];
    };
    readonly "image/t38": {
        readonly source: "iana";
        readonly extensions: readonly ["t38"];
    };
    readonly "image/tiff": {
        readonly source: "iana";
        readonly extensions: readonly ["tif", "tiff"];
    };
    readonly "image/tiff-fx": {
        readonly source: "iana";
        readonly extensions: readonly ["tfx"];
    };
    readonly "image/vnd.adobe.photoshop": {
        readonly source: "iana";
        readonly extensions: readonly ["psd"];
    };
    readonly "image/vnd.airzip.accelerator.azv": {
        readonly source: "iana";
        readonly extensions: readonly ["azv"];
    };
    readonly "image/vnd.dece.graphic": {
        readonly source: "iana";
        readonly extensions: readonly ["uvi", "uvvi", "uvg", "uvvg"];
    };
    readonly "image/vnd.djvu": {
        readonly source: "iana";
        readonly extensions: readonly ["djvu", "djv"];
    };
    readonly "image/vnd.dvb.subtitle": {
        readonly source: "iana";
        readonly extensions: readonly ["sub"];
    };
    readonly "image/vnd.dwg": {
        readonly source: "iana";
        readonly extensions: readonly ["dwg"];
    };
    readonly "image/vnd.dxf": {
        readonly source: "iana";
        readonly extensions: readonly ["dxf"];
    };
    readonly "image/vnd.fastbidsheet": {
        readonly source: "iana";
        readonly extensions: readonly ["fbs"];
    };
    readonly "image/vnd.fpx": {
        readonly source: "iana";
        readonly extensions: readonly ["fpx"];
    };
    readonly "image/vnd.fst": {
        readonly source: "iana";
        readonly extensions: readonly ["fst"];
    };
    readonly "image/vnd.fujixerox.edmics-mmr": {
        readonly source: "iana";
        readonly extensions: readonly ["mmr"];
    };
    readonly "image/vnd.fujixerox.edmics-rlc": {
        readonly source: "iana";
        readonly extensions: readonly ["rlc"];
    };
    readonly "image/vnd.microsoft.icon": {
        readonly source: "iana";
        readonly extensions: readonly ["ico"];
    };
    readonly "image/vnd.ms-modi": {
        readonly source: "iana";
        readonly extensions: readonly ["mdi"];
    };
    readonly "image/vnd.ms-photo": {
        readonly source: "apache";
        readonly extensions: readonly ["wdp"];
    };
    readonly "image/vnd.net-fpx": {
        readonly source: "iana";
        readonly extensions: readonly ["npx"];
    };
    readonly "image/vnd.pco.b16": {
        readonly source: "iana";
        readonly extensions: readonly ["b16"];
    };
    readonly "image/vnd.tencent.tap": {
        readonly source: "iana";
        readonly extensions: readonly ["tap"];
    };
    readonly "image/vnd.valve.source.texture": {
        readonly source: "iana";
        readonly extensions: readonly ["vtf"];
    };
    readonly "image/vnd.wap.wbmp": {
        readonly source: "iana";
        readonly extensions: readonly ["wbmp"];
    };
    readonly "image/vnd.xiff": {
        readonly source: "iana";
        readonly extensions: readonly ["xif"];
    };
    readonly "image/vnd.zbrush.pcx": {
        readonly source: "iana";
        readonly extensions: readonly ["pcx"];
    };
    readonly "image/webp": {
        readonly source: "apache";
        readonly extensions: readonly ["webp"];
    };
    readonly "image/wmf": {
        readonly source: "iana";
        readonly extensions: readonly ["wmf"];
    };
    readonly "image/x-3ds": {
        readonly source: "apache";
        readonly extensions: readonly ["3ds"];
    };
    readonly "image/x-cmu-raster": {
        readonly source: "apache";
        readonly extensions: readonly ["ras"];
    };
    readonly "image/x-cmx": {
        readonly source: "apache";
        readonly extensions: readonly ["cmx"];
    };
    readonly "image/x-freehand": {
        readonly source: "apache";
        readonly extensions: readonly ["fh", "fhc", "fh4", "fh5", "fh7"];
    };
    readonly "image/x-icon": {
        readonly source: "apache";
        readonly extensions: readonly ["ico"];
    };
    readonly "image/x-jng": {
        readonly source: "nginx";
        readonly extensions: readonly ["jng"];
    };
    readonly "image/x-mrsid-image": {
        readonly source: "apache";
        readonly extensions: readonly ["sid"];
    };
    readonly "image/x-ms-bmp": {
        readonly source: "nginx";
        readonly extensions: readonly ["bmp"];
    };
    readonly "image/x-pcx": {
        readonly source: "apache";
        readonly extensions: readonly ["pcx"];
    };
    readonly "image/x-pict": {
        readonly source: "apache";
        readonly extensions: readonly ["pic", "pct"];
    };
    readonly "image/x-portable-anymap": {
        readonly source: "apache";
        readonly extensions: readonly ["pnm"];
    };
    readonly "image/x-portable-bitmap": {
        readonly source: "apache";
        readonly extensions: readonly ["pbm"];
    };
    readonly "image/x-portable-graymap": {
        readonly source: "apache";
        readonly extensions: readonly ["pgm"];
    };
    readonly "image/x-portable-pixmap": {
        readonly source: "apache";
        readonly extensions: readonly ["ppm"];
    };
    readonly "image/x-rgb": {
        readonly source: "apache";
        readonly extensions: readonly ["rgb"];
    };
    readonly "image/x-tga": {
        readonly source: "apache";
        readonly extensions: readonly ["tga"];
    };
    readonly "image/x-xbitmap": {
        readonly source: "apache";
        readonly extensions: readonly ["xbm"];
    };
    readonly "image/x-xpixmap": {
        readonly source: "apache";
        readonly extensions: readonly ["xpm"];
    };
    readonly "image/x-xwindowdump": {
        readonly source: "apache";
        readonly extensions: readonly ["xwd"];
    };
};

export { image };
