"use strict";exports.id=2141,exports.ids=[2141],exports.modules={37826:(e,t,s)=>{s.d(t,{Cf:()=>p,Es:()=>m,L3:()=>h,c7:()=>u,lG:()=>l,rr:()=>g,zM:()=>d});var a=s(60687),o=s(43210),r=s(26134),n=s(11860),i=s(96241);let l=r.bL,d=r.l9,c=r.ZL;r.bm;let f=o.forwardRef(({className:e,...t},s)=>(0,a.jsx)(r.hJ,{ref:s,className:(0,i.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));f.displayName=r.hJ.displayName;let p=o.forwardRef(({className:e,children:t,...s},o)=>(0,a.jsxs)(c,{children:[(0,a.jsx)(f,{}),(0,a.jsxs)(r.UC,{ref:o,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...s,children:[t,(0,a.jsxs)(r.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(n.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));p.displayName=r.UC.displayName;let u=({className:e,...t})=>(0,a.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});u.displayName="DialogHeader";let m=({className:e,...t})=>(0,a.jsx)("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});m.displayName="DialogFooter";let h=o.forwardRef(({className:e,...t},s)=>(0,a.jsx)(r.hE,{ref:s,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));h.displayName=r.hE.displayName;let g=o.forwardRef(({className:e,...t},s)=>(0,a.jsx)(r.VY,{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));g.displayName=r.VY.displayName},59556:(e,t,s)=>{s.d(t,{A:()=>r});var a=s(51060);class o{constructor(){this.isOfflineMode=!1,this.baseURL="http://localhost:5000/api/v1",this.baseURL||console.warn("NEXT_PUBLIC_BACKEND_API_URL is not defined. API requests may fail."),this.client=a.A.create({baseURL:this.baseURL,headers:{"Content-Type":"application/json"},timeout:1e4}),this.client.interceptors.request.use(e=>e,e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,e=>(e.response?(console.error("API Error Response:",e.response.status,e.response.data),e.response.data&&0!==Object.keys(e.response.data).length||(e.response.data={error:`Request failed with status code ${e.response.status}`}),404===e.response.status&&(console.log("Resource not found:",e.config?.url),e.response.data={error:"The requested resource was not found. Please refresh and try again."}),e.responseData=e.response.data):e.request?(console.error("API No Response:",e.request),e.message&&(e.message.includes("Network Error")||e.message.includes("timeout"))?(console.error("Network Error detected. Server might be down or unreachable."),this.setOfflineMode(!0),e.isOffline=!0,e.responseData={error:"Network Error: Unable to connect to the server. Please check your connection or try again later.",isOffline:!0},console.warn("API Client: Server connection failed. If you're running in development mode, make sure your backend server is running."),window.dispatchEvent(new CustomEvent("api:offline",{detail:{isOffline:!0}}))):e.responseData={error:"No response received from server. Please check your connection."}):(console.error("API Request Error:",e.message),e.responseData={error:e.message||"An unexpected error occurred"}),Promise.reject(e)))}async get(e,t){try{if(this.isOfflineMode&&!e.includes("/health"))throw console.log(`API Client: In offline mode, skipping GET request to ${e}`),Error("Network Error: Application is in offline mode");let s=await this.client.get(e,t);return this.isOfflineMode&&this.setOfflineMode(!1),s.data}catch(e){throw e}}async post(e,t,s){return(await this.client.post(e,t,s)).data}async put(e,t,s){return(await this.client.put(e,t,s)).data}async delete(e,t){try{console.log(`Making DELETE request to: ${e}`);let s=await this.client.delete(e,t);if(204===s.status)return console.log(`DELETE request to ${e} successful with 204 status`),null;return s.data}catch(t){throw console.error(`DELETE request to ${e} failed:`,t),t}}async patch(e,t,s){return(await this.client.patch(e,t,s)).data}async upload(e,t,s){let a={...s,headers:{...s?.headers,"Content-Type":"multipart/form-data"}};return(await this.client.post(e,t,a)).data}getBaseURL(){return this.baseURL}isInOfflineMode(){return this.isOfflineMode}setOfflineMode(e){this.isOfflineMode!==e&&(console.log(`API Client: Setting offline mode to ${e}`),this.isOfflineMode=e,window.dispatchEvent(new CustomEvent("api:offline",{detail:{isOffline:e}})))}async tryReconnect(){try{return console.log("API Client: Attempting to reconnect to the server..."),await this.client.get("/health",{timeout:5e3}),console.log("API Client: Reconnection successful"),this.setOfflineMode(!1),!0}catch(e){return console.log("API Client: Reconnection failed, still in offline mode"),!1}}}let r=new o},63143:(e,t,s)=>{s.d(t,{A:()=>a});let a=(0,s(62688).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},63974:(e,t,s)=>{s.d(t,{bq:()=>p,eb:()=>g,gC:()=>h,l6:()=>c,yv:()=>f});var a=s(60687),o=s(43210),r=s(22670),n=s(78272),i=s(3589),l=s(13964),d=s(96241);let c=r.bL;r.YJ;let f=r.WT,p=o.forwardRef(({className:e,children:t,...s},o)=>(0,a.jsxs)(r.l9,{ref:o,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...s,children:[t,(0,a.jsx)(r.In,{asChild:!0,children:(0,a.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]}));p.displayName=r.l9.displayName;let u=o.forwardRef(({className:e,...t},s)=>(0,a.jsx)(r.PP,{ref:s,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(i.A,{className:"h-4 w-4"})}));u.displayName=r.PP.displayName;let m=o.forwardRef(({className:e,...t},s)=>(0,a.jsx)(r.wn,{ref:s,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(n.A,{className:"h-4 w-4"})}));m.displayName=r.wn.displayName;let h=o.forwardRef(({className:e,children:t,position:s="popper",...o},n)=>(0,a.jsx)(r.ZL,{children:(0,a.jsxs)(r.UC,{ref:n,className:(0,d.cn)("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...o,children:[(0,a.jsx)(u,{}),(0,a.jsx)(r.LM,{className:(0,d.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,a.jsx)(m,{})]})}));h.displayName=r.UC.displayName,o.forwardRef(({className:e,...t},s)=>(0,a.jsx)(r.JU,{ref:s,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=r.JU.displayName;let g=o.forwardRef(({className:e,children:t,...s},o)=>(0,a.jsxs)(r.q7,{ref:o,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(l.A,{className:"h-4 w-4"})})}),(0,a.jsx)(r.p4,{children:t})]}));g.displayName=r.q7.displayName,o.forwardRef(({className:e,...t},s)=>(0,a.jsx)(r.wv,{ref:s,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=r.wv.displayName},68988:(e,t,s)=>{s.d(t,{p:()=>n});var a=s(60687),o=s(43210),r=s(96241);let n=o.forwardRef(({className:e,type:t,...s},o)=>(0,a.jsx)("input",{type:t,className:(0,r.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:o,...s}));n.displayName="Input"},78122:(e,t,s)=>{s.d(t,{A:()=>a});let a=(0,s(62688).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},88233:(e,t,s)=>{s.d(t,{A:()=>a});let a=(0,s(62688).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},99270:(e,t,s)=>{s.d(t,{A:()=>a});let a=(0,s(62688).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])}};