/**
 * Marker used to select the region based on the incoming request
 */ const UTRegion = Symbol("uploadthing-region-symbol");
/**
 * Marker used to append a `customId` to the incoming file data in `.middleware()`
 * @example
 * ```ts
 * .middleware((opts) => {
 *   return {
 *     [UTFiles]: opts.files.map((file) => ({
 *       ...file,
 *       customId: generateId(),
 *     }))
 *   };
 * })
 * ```
 */ const UTFiles = Symbol("uploadthing-custom-id-symbol");

exports.UTFiles = UTFiles;
exports.UTRegion = UTRegion;
