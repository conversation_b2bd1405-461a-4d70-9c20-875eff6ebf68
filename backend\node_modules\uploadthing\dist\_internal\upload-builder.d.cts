import { Json, UploadThingError, RouteOptions, FileRouterInputConfig } from '@uploadthing/shared';
import { UploadBuilder, UnsetMarker } from './types.cjs';

type CreateBuilderOptions<TErrorShape extends Json> = {
    errorFormatter: (err: UploadThingError) => TErrorShape;
};
/**
 * Create a builder for your backend adapter.
 * Refer to the existing adapters for examples on how to use this function.
 * @public
 *
 * @param opts - Options for the builder
 * @returns A file route builder for making UploadThing file routes
 */
declare function createBuilder<TAdapterFnArgs extends Record<string, unknown>, TErrorShape extends Json = {
    message: string;
}>(opts?: CreateBuilderOptions<TErrorShape>): <TRouteOptions extends RouteOptions>(input: FileRouterInputConfig, config?: TRouteOptions) => UploadBuilder<{
    _routeOptions: TRouteOptions;
    _input: {
        in: UnsetMarker;
        out: UnsetMarker;
    };
    _metadata: UnsetMarker;
    _adapterFnArgs: TAdapterFnArgs;
    _errorShape: TErrorShape;
    _errorFn: UnsetMarker;
    _output: UnsetMarker;
}>;

export { createBuilder };
export type { CreateBuilderOptions };
