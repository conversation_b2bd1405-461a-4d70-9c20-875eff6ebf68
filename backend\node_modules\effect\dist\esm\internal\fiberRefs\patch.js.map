{"version": 3, "file": "patch.js", "names": ["Arr", "equals", "dual", "fiberRefs_", "OP_EMPTY", "OP_ADD", "OP_REMOVE", "OP_UPDATE", "OP_AND_THEN", "empty", "_tag", "diff", "oldValue", "newValue", "missingLocals", "Map", "locals", "patch", "fiberRef", "pairs", "entries", "headNonEmpty", "old", "get", "undefined", "combine", "value", "delete", "self", "that", "first", "second", "fiberId", "fiberRefs", "patches", "of", "isNonEmptyReadonlyArray", "head", "tail", "tailNonEmpty", "updateAs", "delete_", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prepend"], "sources": ["../../../../src/internal/fiberRefs/patch.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,GAAG,MAAM,gBAAgB;AACrC,SAASC,MAAM,QAAQ,gBAAgB;AAIvC,SAASC,IAAI,QAAQ,mBAAmB;AACxC,OAAO,KAAKC,UAAU,MAAM,iBAAiB;AAE7C;AACA,OAAO,MAAMC,QAAQ,GAAG,OAAgB;AAKxC;AACA,OAAO,MAAMC,MAAM,GAAG,KAAc;AAKpC;AACA,OAAO,MAAMC,SAAS,GAAG,QAAiB;AAK1C;AACA,OAAO,MAAMC,SAAS,GAAG,QAAiB;AAK1C;AACA,OAAO,MAAMC,WAAW,GAAG,SAAkB;AAK7C;AACA,OAAO,MAAMC,KAAK,GAAmC;EACnDC,IAAI,EAAEN;CAC2B;AAEnC;AACA,OAAO,MAAMO,IAAI,GAAGA,CAClBC,QAA6B,EAC7BC,QAA6B,KACI;EACjC,MAAMC,aAAa,GAAG,IAAIC,GAAG,CAACH,QAAQ,CAACI,MAAM,CAAC;EAC9C,IAAIC,KAAK,GAAGR,KAAK;EACjB,KAAK,MAAM,CAACS,QAAQ,EAAEC,KAAK,CAAC,IAAIN,QAAQ,CAACG,MAAM,CAACI,OAAO,EAAE,EAAE;IACzD,MAAMP,QAAQ,GAAGb,GAAG,CAACqB,YAAY,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC;IAC3C,MAAMG,GAAG,GAAGR,aAAa,CAACS,GAAG,CAACL,QAAQ,CAAC;IACvC,IAAII,GAAG,KAAKE,SAAS,EAAE;MACrB,MAAMZ,QAAQ,GAAGZ,GAAG,CAACqB,YAAY,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC;MACzC,IAAI,CAACrB,MAAM,CAACW,QAAQ,EAAEC,QAAQ,CAAC,EAAE;QAC/BI,KAAK,GAAGQ,OAAO,CAAC;UACdf,IAAI,EAAEH,SAAS;UACfW,QAAQ;UACRD,KAAK,EAAEC,QAAQ,CAACP,IAAI,CAACC,QAAQ,EAAEC,QAAQ;SACxC,CAAC,CAACI,KAAK,CAAC;MACX;IACF,CAAC,MAAM;MACLA,KAAK,GAAGQ,OAAO,CAAC;QACdf,IAAI,EAAEL,MAAM;QACZa,QAAQ;QACRQ,KAAK,EAAEb;OACR,CAAC,CAACI,KAAK,CAAC;IACX;IACAH,aAAa,CAACa,MAAM,CAACT,QAAQ,CAAC;EAChC;EACA,KAAK,MAAM,CAACA,QAAQ,CAAC,IAAIJ,aAAa,CAACM,OAAO,EAAE,EAAE;IAChDH,KAAK,GAAGQ,OAAO,CAAC;MACdf,IAAI,EAAEJ,SAAS;MACfY;KACD,CAAC,CAACD,KAAK,CAAC;EACX;EACA,OAAOA,KAAK;AACd,CAAC;AAED;AACA,OAAO,MAAMQ,OAAO,gBAAGvB,IAAI,CAGzB,CAAC,EAAE,CAAC0B,IAAI,EAAEC,IAAI,MAAM;EACpBnB,IAAI,EAAEF,WAAW;EACjBsB,KAAK,EAAEF,IAAI;EACXG,MAAM,EAAEF;CACT,CAAC,CAAC;AAEH;AACA,OAAO,MAAMZ,KAAK,gBAAGf,IAAI,CAUvB,CAAC,EAAE,CAAC0B,IAAI,EAAEI,OAAO,EAAEpB,QAAQ,KAAI;EAC/B,IAAIqB,SAAS,GAAwBrB,QAAQ;EAC7C,IAAIsB,OAAO,GAAiDlC,GAAG,CAACmC,EAAE,CAACP,IAAI,CAAC;EACxE,OAAO5B,GAAG,CAACoC,uBAAuB,CAACF,OAAO,CAAC,EAAE;IAC3C,MAAMG,IAAI,GAAGrC,GAAG,CAACqB,YAAY,CAACa,OAAO,CAAC;IACtC,MAAMI,IAAI,GAAGtC,GAAG,CAACuC,YAAY,CAACL,OAAO,CAAC;IACtC,QAAQG,IAAI,CAAC3B,IAAI;MACf,KAAKN,QAAQ;QAAE;UACb8B,OAAO,GAAGI,IAAI;UACd;QACF;MACA,KAAKjC,MAAM;QAAE;UACX4B,SAAS,GAAG9B,UAAU,CAACqC,QAAQ,CAACP,SAAS,EAAE;YACzCD,OAAO;YACPd,QAAQ,EAAEmB,IAAI,CAACnB,QAAQ;YACvBQ,KAAK,EAAEW,IAAI,CAACX;WACb,CAAC;UACFQ,OAAO,GAAGI,IAAI;UACd;QACF;MACA,KAAKhC,SAAS;QAAE;UACd2B,SAAS,GAAG9B,UAAU,CAACsC,OAAO,CAACR,SAAS,EAAEI,IAAI,CAACnB,QAAQ,CAAC;UACxDgB,OAAO,GAAGI,IAAI;UACd;QACF;MACA,KAAK/B,SAAS;QAAE;UACd,MAAMmB,KAAK,GAAGvB,UAAU,CAACuC,YAAY,CAACT,SAAS,EAAEI,IAAI,CAACnB,QAAQ,CAAC;UAC/De,SAAS,GAAG9B,UAAU,CAACqC,QAAQ,CAACP,SAAS,EAAE;YACzCD,OAAO;YACPd,QAAQ,EAAEmB,IAAI,CAACnB,QAAQ;YACvBQ,KAAK,EAAEW,IAAI,CAACnB,QAAQ,CAACD,KAAK,CAACoB,IAAI,CAACpB,KAAK,CAAC,CAACS,KAAK;WAC7C,CAAC;UACFQ,OAAO,GAAGI,IAAI;UACd;QACF;MACA,KAAK9B,WAAW;QAAE;UAChB0B,OAAO,GAAGlC,GAAG,CAAC2C,OAAO,CAACN,IAAI,CAACP,KAAK,CAAC,CAAC9B,GAAG,CAAC2C,OAAO,CAACN,IAAI,CAACN,MAAM,CAAC,CAACO,IAAI,CAAC,CAAC;UACjE;QACF;IACF;EACF;EACA,OAAOL,SAAS;AAClB,CAAC,CAAC", "ignoreList": []}