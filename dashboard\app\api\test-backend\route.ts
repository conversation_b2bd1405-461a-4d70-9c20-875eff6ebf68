import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Test if backend is running
    const response = await fetch('http://localhost:5000/api/v1/properties/stats');
    
    if (response.ok) {
      const data = await response.json();
      return NextResponse.json({ 
        success: true, 
        message: 'Backend is running',
        backendData: data 
      });
    } else {
      return NextResponse.json({ 
        success: false, 
        message: 'Backend not responding',
        status: response.status 
      });
    }
  } catch (error: any) {
    return NextResponse.json({ 
      success: false, 
      message: 'Backend connection failed',
      error: error.message 
    });
  }
}
