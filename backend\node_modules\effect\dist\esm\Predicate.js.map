{"version": 3, "file": "Predicate.js", "names": ["dual", "isFunction", "isFunction_", "mapInput", "self", "f", "b", "isTupleOf", "n", "length", "isTupleOfAtLeast", "<PERSON><PERSON><PERSON><PERSON>", "input", "isSet", "Set", "isMap", "Map", "isString", "isNumber", "isBoolean", "isBigInt", "isSymbol", "isPropertyKey", "u", "isUndefined", "undefined", "isNotUndefined", "isNull", "isNotNull", "isNever", "_", "isUnknown", "isRecordOrArray", "isObject", "hasProperty", "property", "isTagged", "tag", "isNullable", "isNotNullable", "isError", "Error", "isUint8Array", "Uint8Array", "isDate", "Date", "isIterable", "Symbol", "iterator", "isRecord", "Array", "isArray", "isReadonlyRecord", "isPromise", "then", "catch", "isPromiseLike", "isRegExp", "RegExp", "compose", "ab", "bc", "a", "product", "that", "all", "collection", "as", "collectionIndex", "p", "productMany", "rest", "head", "tail", "tuple", "elements", "struct", "fields", "keys", "Object", "key", "not", "or", "and", "xor", "eqv", "implies", "antecedent", "consequent", "nor", "nand", "every", "some"], "sources": ["../../src/Predicate.ts"], "sourcesContent": [null], "mappings": "AAAA;;;AAGA,SAASA,IAAI,EAAEC,UAAU,IAAIC,WAAW,QAAQ,eAAe;AAmE/D;;;;;;;;;;;;;;;;;;;AAmBA,OAAO,MAAMC,QAAQ,gBAyCjBH,IAAI,CAAC,CAAC,EAAE,CAAOI,IAAkB,EAAEC,CAAc,KAAoBC,CAAC,IAAKF,IAAI,CAACC,CAAC,CAACC,CAAC,CAAC,CAAC,CAAC;AAE1F;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,OAAO,MAAMC,SAAS,gBAmDlBP,IAAI,CAAC,CAAC,EAAE,CAAsBI,IAAsB,EAAEI,CAAI,KAA4BJ,IAAI,CAACK,MAAM,KAAKD,CAAC,CAAC;AAE5G;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,OAAO,MAAME,gBAAgB,gBAmDzBV,IAAI,CAAC,CAAC,EAAE,CAAsBI,IAAsB,EAAEI,CAAI,KAAmCJ,IAAI,CAACK,MAAM,IAAID,CAAC,CAAC;AAElH;;;;;;;;;;;;;;;;AAgBA,OAAO,MAAMG,QAAQ,GAAIC,KAAc,IAAK,CAAC,CAACA,KAAK;AAEnD;;;;;;;;;;;;;;;;;;AAkBA,OAAO,MAAMC,KAAK,GAAID,KAAc,IAA4BA,KAAK,YAAYE,GAAG;AAEpF;;;;;;;;;;;;;;;;;AAiBA,OAAO,MAAMC,KAAK,GAAIH,KAAc,IAAqCA,KAAK,YAAYI,GAAG;AAE7F;;;;;;;;;;;;;;;;AAgBA,OAAO,MAAMC,QAAQ,GAAIL,KAAc,IAAsB,OAAOA,KAAK,KAAK,QAAQ;AAEtF;;;;;;;;;;;;;;;;AAgBA,OAAO,MAAMM,QAAQ,GAAIN,KAAc,IAAsB,OAAOA,KAAK,KAAK,QAAQ;AAEtF;;;;;;;;;;;;;;;;AAgBA,OAAO,MAAMO,SAAS,GAAIP,KAAc,IAAuB,OAAOA,KAAK,KAAK,SAAS;AAEzF;;;;;;;;;;;;;;;;AAgBA,OAAO,MAAMQ,QAAQ,GAAIR,KAAc,IAAsB,OAAOA,KAAK,KAAK,QAAQ;AAEtF;;;;;;;;;;;;;;;;AAgBA,OAAO,MAAMS,QAAQ,GAAIT,KAAc,IAAsB,OAAOA,KAAK,KAAK,QAAQ;AAEtF;AACA;AACA,OAAO,MAAMU,aAAa,GAAIC,CAAU,IAAuBN,QAAQ,CAACM,CAAC,CAAC,IAAIL,QAAQ,CAACK,CAAC,CAAC,IAAIF,QAAQ,CAACE,CAAC,CAAC;AAExG;;;;;;;;;;;;;;;;AAgBA,OAAO,MAAMtB,UAAU,GAA0CC,WAAW;AAE5E;;;;;;;;;;;;;;;;;AAiBA,OAAO,MAAMsB,WAAW,GAAIZ,KAAc,IAAyBA,KAAK,KAAKa,SAAS;AAEtF;;;;;;;;;;;;;;;;;AAiBA,OAAO,MAAMC,cAAc,GAAOd,KAAQ,IAAqCA,KAAK,KAAKa,SAAS;AAElG;;;;;;;;;;;;;;;;;AAiBA,OAAO,MAAME,MAAM,GAAIf,KAAc,IAAoBA,KAAK,KAAK,IAAI;AAEvE;;;;;;;;;;;;;;;;;AAiBA,OAAO,MAAMgB,SAAS,GAAOhB,KAAQ,IAAgCA,KAAK,KAAK,IAAI;AAEnF;;;;;;;;;;;;;;;;;AAiBA,OAAO,MAAMiB,OAAO,GAAwCC,CAAU,IAAiB,KAAK;AAE5F;;;;;;;;;;;;;;;;;;AAkBA,OAAO,MAAMC,SAAS,GAA0CD,CAAC,IAAmB,IAAI;AAExF;AACA,OAAO,MAAME,eAAe,GAAIpB,KAAc,IAC5C,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI;AAE7C;;;;;;;;;;;;;;;;;;AAkBA,OAAO,MAAMqB,QAAQ,GAAIrB,KAAc,IAAsBoB,eAAe,CAACpB,KAAK,CAAC,IAAIX,UAAU,CAACW,KAAK,CAAC;AAExG;;;;;;AAMA,OAAO,MAAMsB,WAAW,gBAepBlC,IAAI,CACN,CAAC,EACD,CAAwBI,IAAa,EAAE+B,QAAW,KAChDF,QAAQ,CAAC7B,IAAI,CAAC,IAAK+B,QAAQ,IAAI/B,IAAK,CACvC;AAED;;;;;;;;;;;;;;;;;;;AAmBA,OAAO,MAAMgC,QAAQ,gBAyCjBpC,IAAI,CACN,CAAC,EACD,CAAmBI,IAAa,EAAEiC,GAAM,KAA0BH,WAAW,CAAC9B,IAAI,EAAE,MAAM,CAAC,IAAIA,IAAI,CAAC,MAAM,CAAC,KAAKiC,GAAG,CACpH;AAED;;;;;;;;;;;;;;;;;;AAkBA,OAAO,MAAMC,UAAU,GAAO1B,KAAQ,IAA4CA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKa,SAAS;AAEvH;;;;;;;;;;;;;;;;;;AAkBA,OAAO,MAAMc,aAAa,GAAO3B,KAAQ,IAA8BA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKa,SAAS;AAE5G;;;;;;;;;;;;;;;;;AAiBA,OAAO,MAAMe,OAAO,GAAI5B,KAAc,IAAqBA,KAAK,YAAY6B,KAAK;AAEjF;;;;;;;;;;;;;;;;;AAiBA,OAAO,MAAMC,YAAY,GAAI9B,KAAc,IAA0BA,KAAK,YAAY+B,UAAU;AAEhG;;;;;;;;;;;;;;;;;AAiBA,OAAO,MAAMC,MAAM,GAAIhC,KAAc,IAAoBA,KAAK,YAAYiC,IAAI;AAE9E;;;;;;;;;;;;;;;;;;AAkBA,OAAO,MAAMC,UAAU,GAAIlC,KAAc,IAAiCsB,WAAW,CAACtB,KAAK,EAAEmC,MAAM,CAACC,QAAQ,CAAC;AAE7G;;;;;;;;;;;;;;;;;;;;;AAqBA,OAAO,MAAMC,QAAQ,GAAIrC,KAAc,IACrCoB,eAAe,CAACpB,KAAK,CAAC,IAAI,CAACsC,KAAK,CAACC,OAAO,CAACvC,KAAK,CAAC;AAEjD;;;;;;;;;;;;;;;;;;;;AAoBA,OAAO,MAAMwC,gBAAgB,GAE8BH,QAAQ;AAEnE;;;;;;;;;;;;;;;AAeA,OAAO,MAAMI,SAAS,GACpBzC,KAAc,IAEdsB,WAAW,CAACtB,KAAK,EAAE,MAAM,CAAC,IAAI,OAAO,IAAIA,KAAK,IAAIX,UAAU,CAACW,KAAK,CAAC0C,IAAI,CAAC,IAAIrD,UAAU,CAACW,KAAK,CAAC2C,KAAK,CAAC;AAErG;;;;AAIA,OAAO,MAAMC,aAAa,GACxB5C,KAAc,IACoBsB,WAAW,CAACtB,KAAK,EAAE,MAAM,CAAC,IAAIX,UAAU,CAACW,KAAK,CAAC0C,IAAI,CAAC;AAExF;;;;;;;;;;;;;;;AAeA,OAAO,MAAMG,QAAQ,GAAI7C,KAAc,IAAsBA,KAAK,YAAY8C,MAAM;AAEpF;;;AAGA,OAAO,MAAMC,OAAO,gBAiBhB3D,IAAI,CACN,CAAC,EACD,CAA8B4D,EAAoB,EAAEC,EAAoB,KAAwBC,CAAC,IAC/FF,EAAE,CAACE,CAAC,CAAC,IAAID,EAAE,CAACC,CAAC,CAAC,CACjB;AAED;;;;AAIA,OAAO,MAAMC,OAAO,GAClBA,CAAO3D,IAAkB,EAAE4D,IAAkB,KAC7C,CAAC,CAACF,CAAC,EAAExD,CAAC,CAAC,KAAKF,IAAI,CAAC0D,CAAC,CAAC,IAAIE,IAAI,CAAC1D,CAAC,CAAC;AAEhC;;;;AAIA,OAAO,MAAM2D,GAAG,GACdC,UAAkC,IACH;EAC/B,OAAQC,EAAE,IAAI;IACZ,IAAIC,eAAe,GAAG,CAAC;IACvB,KAAK,MAAMC,CAAC,IAAIH,UAAU,EAAE;MAC1B,IAAIE,eAAe,IAAID,EAAE,CAAC1D,MAAM,EAAE;QAChC;MACF;MACA,IAAI4D,CAAC,CAACF,EAAE,CAACC,eAAe,CAAC,CAAC,KAAK,KAAK,EAAE;QACpC,OAAO,KAAK;MACd;MACAA,eAAe,EAAE;IACnB;IACA,OAAO,IAAI;EACb,CAAC;AACH,CAAC;AAED;;;;AAIA,OAAO,MAAME,WAAW,GAAGA,CACzBlE,IAAkB,EAClB8D,UAAkC,KAC2C;EAC7E,MAAMK,IAAI,GAAGN,GAAG,CAACC,UAAU,CAAC;EAC5B,OAAO,CAAC,CAACM,IAAI,EAAE,GAAGC,IAAI,CAAC,KAAKrE,IAAI,CAACoE,IAAI,CAAC,KAAK,KAAK,GAAG,KAAK,GAAGD,IAAI,CAACE,IAAI,CAAC;AACvE,CAAC;AAED;;;;;;;;;;;AAWA,OAAO,MAAMC,KAAK,GAiBdA,CAAC,GAAGC,QAAsC,KAAKV,GAAG,CAACU,QAAQ,CAAQ;AAEvE;;;;;;;;;AASA,OAAO,MAAMC,MAAM,GAgB4BC,MAAS,IAAI;EAC1D,MAAMC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACD,MAAM,CAAC;EAChC,OAAQf,CAA0B,IAAI;IACpC,KAAK,MAAMkB,GAAG,IAAIF,IAAI,EAAE;MACtB,IAAI,CAACD,MAAM,CAACG,GAAG,CAAC,CAAClB,CAAC,CAACkB,GAAG,CAAU,CAAC,EAAE;QACjC,OAAO,KAAK;MACd;IACF;IACA,OAAO,IAAI;EACb,CAAC;AACH,CAAS;AAET;;;;;;;;;;;;;;;;;;AAkBA,OAAO,MAAMC,GAAG,GAAO7E,IAAkB,IAAoB0D,CAAC,IAAK,CAAC1D,IAAI,CAAC0D,CAAC,CAAC;AAE3E;;;;;;;;;;;;;;;;;;AAkBA,OAAO,MAAMoB,EAAE,gBA6EXlF,IAAI,CAAC,CAAC,EAAE,CAAII,IAAkB,EAAE4D,IAAkB,KAAoBF,CAAC,IAAK1D,IAAI,CAAC0D,CAAC,CAAC,IAAIE,IAAI,CAACF,CAAC,CAAC,CAAC;AAEnG;;;;;;;;;;;;;;;;;;;;;AAqBA,OAAO,MAAMqB,GAAG,gBAyFZnF,IAAI,CAAC,CAAC,EAAE,CAAII,IAAkB,EAAE4D,IAAkB,KAAoBF,CAAC,IAAK1D,IAAI,CAAC0D,CAAC,CAAC,IAAIE,IAAI,CAACF,CAAC,CAAC,CAAC;AAEnG;;;;AAIA,OAAO,MAAMsB,GAAG,gBAWZpF,IAAI,CAAC,CAAC,EAAE,CAAII,IAAkB,EAAE4D,IAAkB,KAAoBF,CAAC,IAAK1D,IAAI,CAAC0D,CAAC,CAAC,KAAKE,IAAI,CAACF,CAAC,CAAC,CAAC;AAEpG;;;;AAIA,OAAO,MAAMuB,GAAG,gBAWZrF,IAAI,CAAC,CAAC,EAAE,CAAII,IAAkB,EAAE4D,IAAkB,KAAoBF,CAAC,IAAK1D,IAAI,CAAC0D,CAAC,CAAC,KAAKE,IAAI,CAACF,CAAC,CAAC,CAAC;AAEpG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+CA,OAAO,MAAMwB,OAAO,gBAiGhBtF,IAAI,CACN,CAAC,EACD,CAAIuF,UAAwB,EAAEC,UAAwB,KAAoB1B,CAAC,IAAKyB,UAAU,CAACzB,CAAC,CAAC,GAAG0B,UAAU,CAAC1B,CAAC,CAAC,GAAG,IAAI,CACrH;AAED;;;;AAIA,OAAO,MAAM2B,GAAG,gBAWZzF,IAAI,CACN,CAAC,EACD,CAAII,IAAkB,EAAE4D,IAAkB,KAAoBF,CAAC,IAAK,EAAE1D,IAAI,CAAC0D,CAAC,CAAC,IAAIE,IAAI,CAACF,CAAC,CAAC,CAAC,CAC1F;AAED;;;;AAIA,OAAO,MAAM4B,IAAI,gBAWb1F,IAAI,CACN,CAAC,EACD,CAAII,IAAkB,EAAE4D,IAAkB,KAAoBF,CAAC,IAAK,EAAE1D,IAAI,CAAC0D,CAAC,CAAC,IAAIE,IAAI,CAACF,CAAC,CAAC,CAAC,CAC1F;AAED;;;;AAIA,OAAO,MAAM6B,KAAK,GAAOzB,UAAkC,IAAoBJ,CAAI,IAAI;EACrF,KAAK,MAAMO,CAAC,IAAIH,UAAU,EAAE;IAC1B,IAAI,CAACG,CAAC,CAACP,CAAC,CAAC,EAAE;MACT,OAAO,KAAK;IACd;EACF;EACA,OAAO,IAAI;AACb,CAAC;AAED;;;;AAIA,OAAO,MAAM8B,IAAI,GAAO1B,UAAkC,IAAoBJ,CAAC,IAAI;EACjF,KAAK,MAAMO,CAAC,IAAIH,UAAU,EAAE;IAC1B,IAAIG,CAAC,CAACP,CAAC,CAAC,EAAE;MACR,OAAO,IAAI;IACb;EACF;EACA,OAAO,KAAK;AACd,CAAC", "ignoreList": []}