{"version": 3, "file": "readonlyArrayPatch.js", "names": ["Arr", "Equal", "Dual", "Data", "ReadonlyArrayPatchTypeId", "Symbol", "for", "variance", "a", "PatchProto", "Structural", "prototype", "_Value", "_Patch", "EmptyProto", "Object", "assign", "create", "_tag", "_empty", "empty", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "makeAndThen", "first", "second", "o", "AppendProto", "makeAppend", "values", "SliceProto", "makeSlice", "from", "until", "UpdateProto", "makeUpdate", "index", "patch", "diff", "options", "i", "oldValue", "length", "newValue", "oldElement", "newElement", "valuePatch", "differ", "equals", "combine", "drop", "dual", "self", "that", "readon<PERSON><PERSON><PERSON><PERSON>", "slice", "patches", "of", "isNonEmptyArray", "head", "headNonEmpty", "tail", "tailNonEmpty", "unshift", "value", "push"], "sources": ["../../../../src/internal/differ/readonlyArrayPatch.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,GAAG,MAAM,gBAAgB;AAErC,OAAO,KAAKC,KAAK,MAAM,gBAAgB;AACvC,OAAO,KAAKC,IAAI,MAAM,mBAAmB;AACzC,OAAO,KAAKC,IAAI,MAAM,YAAY;AAElC;AACA,OAAO,MAAMC,wBAAwB,gBAAuCC,MAAM,CAACC,GAAG,CACpF,iCAAiC,CACI;AAEvC,SAASC,QAAQA,CAAOC,CAAI;EAC1B,OAAOA,CAAiB;AAC1B;AAEA,MAAMC,UAAU,GAAG;EACjB,GAAGN,IAAI,CAACO,UAAU,CAACC,SAAS;EAC5B,CAACP,wBAAwB,GAAG;IAC1BQ,MAAM,EAAEL,QAAQ;IAChBM,MAAM,EAAEN;;CAEX;AAMD,MAAMO,UAAU,gBAAGC,MAAM,CAACC,MAAM,eAACD,MAAM,CAACE,MAAM,CAACR,UAAU,CAAC,EAAE;EAC1DS,IAAI,EAAE;CACP,CAAC;AAEF,MAAMC,MAAM,gBAAGJ,MAAM,CAACE,MAAM,CAACH,UAAU,CAAC;AAExC;;;AAGA,OAAO,MAAMM,KAAK,GAAGA,CAAA,KAAqED,MAAM;AAQhG,MAAME,YAAY,gBAAGN,MAAM,CAACC,MAAM,eAACD,MAAM,CAACE,MAAM,CAACR,UAAU,CAAC,EAAE;EAC5DS,IAAI,EAAE;CACP,CAAC;AAEF,MAAMI,WAAW,GAAGA,CAClBC,KAAsD,EACtDC,MAAuD,KACJ;EACnD,MAAMC,CAAC,GAAGV,MAAM,CAACE,MAAM,CAACI,YAAY,CAAC;EACrCI,CAAC,CAACF,KAAK,GAAGA,KAAK;EACfE,CAAC,CAACD,MAAM,GAAGA,MAAM;EACjB,OAAOC,CAAC;AACV,CAAC;AAOD,MAAMC,WAAW,gBAAGX,MAAM,CAACC,MAAM,eAACD,MAAM,CAACE,MAAM,CAACR,UAAU,CAAC,EAAE;EAC3DS,IAAI,EAAE;CACP,CAAC;AAEF,MAAMS,UAAU,GAAkBC,MAA4B,IAAqD;EACjH,MAAMH,CAAC,GAAGV,MAAM,CAACE,MAAM,CAACS,WAAW,CAAC;EACpCD,CAAC,CAACG,MAAM,GAAGA,MAAM;EACjB,OAAOH,CAAC;AACV,CAAC;AAQD,MAAMI,UAAU,gBAAGd,MAAM,CAACC,MAAM,eAACD,MAAM,CAACE,MAAM,CAACR,UAAU,CAAC,EAAE;EAC1DS,IAAI,EAAE;CACP,CAAC;AAEF,MAAMY,SAAS,GAAGA,CAAeC,IAAY,EAAEC,KAAa,KAAqD;EAC/G,MAAMP,CAAC,GAAGV,MAAM,CAACE,MAAM,CAACY,UAAU,CAAC;EACnCJ,CAAC,CAACM,IAAI,GAAGA,IAAI;EACbN,CAAC,CAACO,KAAK,GAAGA,KAAK;EACf,OAAOP,CAAC;AACV,CAAC;AAQD,MAAMQ,WAAW,gBAAGlB,MAAM,CAACC,MAAM,eAACD,MAAM,CAACE,MAAM,CAACR,UAAU,CAAC,EAAE;EAC3DS,IAAI,EAAE;CACP,CAAC;AAEF,MAAMgB,UAAU,GAAGA,CAAeC,KAAa,EAAEC,KAAY,KAAqD;EAChH,MAAMX,CAAC,GAAGV,MAAM,CAACE,MAAM,CAACgB,WAAW,CAAC;EACpCR,CAAC,CAACU,KAAK,GAAGA,KAAK;EACfV,CAAC,CAACW,KAAK,GAAGA,KAAK;EACf,OAAOX,CAAC;AACV,CAAC;AASD;AACA,OAAO,MAAMY,IAAI,GACfC,OAIC,IACkD;EACnD,IAAIC,CAAC,GAAG,CAAC;EACT,IAAIH,KAAK,GAAGhB,KAAK,EAAgB;EACjC,OAAOmB,CAAC,GAAGD,OAAO,CAACE,QAAQ,CAACC,MAAM,IAAIF,CAAC,GAAGD,OAAO,CAACI,QAAQ,CAACD,MAAM,EAAE;IACjE,MAAME,UAAU,GAAGL,OAAO,CAACE,QAAQ,CAACD,CAAC,CAAE;IACvC,MAAMK,UAAU,GAAGN,OAAO,CAACI,QAAQ,CAACH,CAAC,CAAE;IACvC,MAAMM,UAAU,GAAGP,OAAO,CAACQ,MAAM,CAACT,IAAI,CAACM,UAAU,EAAEC,UAAU,CAAC;IAC9D,IAAI,CAAC3C,KAAK,CAAC8C,MAAM,CAACF,UAAU,EAAEP,OAAO,CAACQ,MAAM,CAAC1B,KAAK,CAAC,EAAE;MACnDgB,KAAK,GAAGY,OAAO,CAACZ,KAAK,EAAEF,UAAU,CAACK,CAAC,EAAEM,UAAU,CAAC,CAAC;IACnD;IACAN,CAAC,GAAGA,CAAC,GAAG,CAAC;EACX;EACA,IAAIA,CAAC,GAAGD,OAAO,CAACE,QAAQ,CAACC,MAAM,EAAE;IAC/BL,KAAK,GAAGY,OAAO,CAACZ,KAAK,EAAEN,SAAS,CAAC,CAAC,EAAES,CAAC,CAAC,CAAC;EACzC;EACA,IAAIA,CAAC,GAAGD,OAAO,CAACI,QAAQ,CAACD,MAAM,EAAE;IAC/BL,KAAK,GAAGY,OAAO,CAACZ,KAAK,EAAET,UAAU,CAAC3B,GAAG,CAACiD,IAAI,CAACV,CAAC,CAAC,CAACD,OAAO,CAACI,QAAQ,CAAC,CAAC,CAAC;EACnE;EACA,OAAON,KAAK;AACd,CAAC;AAED;AACA,OAAO,MAAMY,OAAO,gBAAG9C,IAAI,CAACgD,IAAI,CAU9B,CAAC,EAAE,CAACC,IAAI,EAAEC,IAAI,KAAK9B,WAAW,CAAC6B,IAAI,EAAEC,IAAI,CAAC,CAAC;AAE7C;AACA,OAAO,MAAMhB,KAAK,gBAAGlC,IAAI,CAACgD,IAAI,CAU5B,CAAC,EAAE,CACHC,IAAqD,EACrDX,QAA8B,EAC9BM,MAAmC,KACjC;EACF,IAAKK,IAAoB,CAACjC,IAAI,KAAK,OAAO,EAAE;IAC1C,OAAOsB,QAAQ;EACjB;EACA,IAAIa,aAAa,GAAGb,QAAQ,CAACc,KAAK,EAAE;EACpC,IAAIC,OAAO,GAA2DvD,GAAG,CAACwD,EAAE,CAACL,IAAI,CAAC;EAClF,OAAOnD,GAAG,CAACyD,eAAe,CAACF,OAAO,CAAC,EAAE;IACnC,MAAMG,IAAI,GAAgB1D,GAAG,CAAC2D,YAAY,CAACJ,OAAO,CAAgB;IAClE,MAAMK,IAAI,GAAG5D,GAAG,CAAC6D,YAAY,CAACN,OAAO,CAAC;IACtC,QAAQG,IAAI,CAACxC,IAAI;MACf,KAAK,OAAO;QAAE;UACZqC,OAAO,GAAGK,IAAI;UACd;QACF;MACA,KAAK,SAAS;QAAE;UACdA,IAAI,CAACE,OAAO,CAACJ,IAAI,CAACnC,KAAK,EAAEmC,IAAI,CAAClC,MAAM,CAAC;UACrC+B,OAAO,GAAGK,IAAI;UACd;QACF;MACA,KAAK,QAAQ;QAAE;UACb,KAAK,MAAMG,KAAK,IAAIL,IAAI,CAAC9B,MAAM,EAAE;YAC/ByB,aAAa,CAACW,IAAI,CAACD,KAAK,CAAC;UAC3B;UACAR,OAAO,GAAGK,IAAI;UACd;QACF;MACA,KAAK,OAAO;QAAE;UACZP,aAAa,GAAGA,aAAa,CAACC,KAAK,CAACI,IAAI,CAAC3B,IAAI,EAAE2B,IAAI,CAAC1B,KAAK,CAAC;UAC1DuB,OAAO,GAAGK,IAAI;UACd;QACF;MACA,KAAK,QAAQ;QAAE;UACbP,aAAa,CAACK,IAAI,CAACvB,KAAK,CAAC,GAAGW,MAAM,CAACV,KAAK,CAACsB,IAAI,CAACtB,KAAK,EAAEiB,aAAa,CAACK,IAAI,CAACvB,KAAK,CAAE,CAAC;UAChFoB,OAAO,GAAGK,IAAI;UACd;QACF;IACF;EACF;EACA,OAAOP,aAAa;AACtB,CAAC,CAAC", "ignoreList": []}