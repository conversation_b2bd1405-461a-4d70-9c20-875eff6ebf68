import * as internal from "./internal/executionStrategy.js";
/**
 * Execute effects sequentially.
 *
 * @since 2.0.0
 * @category constructors
 */
export const sequential = internal.sequential;
/**
 * Execute effects in parallel.
 *
 * @since 2.0.0
 * @category constructors
 */
export const parallel = internal.parallel;
/**
 * Execute effects in parallel, up to the specified number of concurrent fibers.
 *
 * @since 2.0.0
 * @category constructors
 */
export const parallelN = internal.parallelN;
/**
 * Returns `true` if the specified `ExecutionStrategy` is an instance of
 * `Sequential`, `false` otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
export const isSequential = internal.isSequential;
/**
 * Returns `true` if the specified `ExecutionStrategy` is an instance of
 * `Sequential`, `false` otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
export const isParallel = internal.isParallel;
/**
 * Returns `true` if the specified `ExecutionStrategy` is an instance of
 * `Sequential`, `false` otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
export const isParallelN = internal.isParallelN;
/**
 * Folds over the specified `ExecutionStrategy` using the provided case
 * functions.
 *
 * @since 2.0.0
 * @category folding
 */
export const match = internal.match;
//# sourceMappingURL=ExecutionStrategy.js.map