import * as Data from 'effect/Data';
import * as Effect from 'effect/Effect';
import * as S from 'effect/Schema';
import { ExpandedRouteConfig, UploadThingError, FileRouterInputKey, FileSize, InvalidRouteConfigError, UnknownFileTypeError, InvalidFileTypeError, InvalidFileSizeError } from '@uploadthing/shared';
import { FileRouter } from '../../types/index.js';
import { UploadActionPayload } from './shared-schemas.cjs';

declare class FileSizeMismatch extends Data.Error<{
    reason: string;
}> {
    readonly _tag = "FileSizeMismatch";
    readonly name = "FileSizeMismatchError";
    constructor(type: FileRouterInputKey, max: FileSize, actual: number);
}
declare class FileCountMismatch extends Data.Error<{
    reason: string;
}> {
    readonly _tag = "FileCountMismatch";
    readonly name = "FileCountMismatchError";
    constructor(type: FileRouterInputKey, boundtype: "minimum" | "maximum", bound: number, actual: number);
}
declare const assertFilesMeetConfig: (files: S.Schema.Type<typeof UploadActionPayload>["files"], routeConfig: ExpandedRouteConfig) => Effect.Effect<null, UploadThingError | FileSizeMismatch | FileCountMismatch | InvalidRouteConfigError | UnknownFileTypeError | InvalidFileTypeError | InvalidFileSizeError>;
declare const extractRouterConfig: <TRouter extends FileRouter>(router: TRouter) => Effect.Effect<{
    slug: keyof TRouter;
    config: ExpandedRouteConfig;
}[], InvalidRouteConfigError, never>;

export { assertFilesMeetConfig, extractRouterConfig };
