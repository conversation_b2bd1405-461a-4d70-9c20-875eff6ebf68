var Effect = require('effect/Effect');
var shared = require('@uploadthing/shared');
var handler_cjs = require('../dist/_internal/handler.cjs');
var routeConfig_cjs = require('../dist/_internal/route-config.cjs');
var uploadBuilder_cjs = require('../dist/_internal/upload-builder.cjs');
var types_cjs = require('../dist/_internal/types.cjs');
var platform = require('@effect/platform');
var Arr = require('effect/Array');
var Cause = require('effect/Cause');
var Redacted = require('effect/Redacted');
var S = require('effect/Schema');
var config_cjs = require('../dist/_internal/config.cjs');
var logger_cjs = require('../dist/_internal/logger.cjs');
var runtime_cjs = require('../dist/_internal/runtime.cjs');
var mimeTypes = require('@uploadthing/mime-types');
var Predicate = require('effect/Predicate');
var uploadServer_cjs = require('../dist/_internal/upload-server.cjs');

function _interopNamespace(e) {
  if (e && e.__esModule) return e;
  var n = Object.create(null);
  if (e) {
    Object.keys(e).forEach(function (k) {
      if (k !== 'default') {
        var d = Object.getOwnPropertyDescriptor(e, k);
        Object.defineProperty(n, k, d.get ? d : {
          enumerable: true,
          get: function () { return e[k]; }
        });
      }
    });
  }
  n.default = e;
  return n;
}

var Effect__namespace = /*#__PURE__*/_interopNamespace(Effect);
var Arr__namespace = /*#__PURE__*/_interopNamespace(Arr);
var Cause__namespace = /*#__PURE__*/_interopNamespace(Cause);
var Redacted__namespace = /*#__PURE__*/_interopNamespace(Redacted);
var S__namespace = /*#__PURE__*/_interopNamespace(S);
var Predicate__namespace = /*#__PURE__*/_interopNamespace(Predicate);

/**
 * Extension of the Blob class that simplifies setting the `name` and `customId` properties,
 * similar to the built-in File class from Node > 20.
 */ class UTFile extends Blob {
    constructor(parts, name, options){
        const optionsWithDefaults = {
            ...options,
            type: options?.type ?? (mimeTypes.lookup(name) || "application/octet-stream"),
            lastModified: options?.lastModified ?? Date.now()
        };
        super(parts, optionsWithDefaults);
        this.name = name;
        this.customId = optionsWithDefaults.customId;
        this.lastModified = optionsWithDefaults.lastModified;
    }
}

function guardServerOnly() {
    if (typeof window !== "undefined") {
        throw new shared.UploadThingError({
            code: "INTERNAL_SERVER_ERROR",
            message: "The `utapi` can only be used on the server."
        });
    }
}
const downloadFile = (_url)=>Effect__namespace.gen(function*() {
        let url = Predicate__namespace.isRecord(_url) ? _url.url : _url;
        if (typeof url === "string") {
            // since dataurls will result in name being too long, tell the user
            // to use uploadFiles instead.
            if (url.startsWith("data:")) {
                return yield* Effect__namespace.fail({
                    code: "BAD_REQUEST",
                    message: "Please use uploadFiles() for data URLs. uploadFilesFromUrl() is intended for use with remote URLs only.",
                    data: undefined
                });
            }
        }
        url = new URL(url);
        const { name = url.pathname.split("/").pop() ?? "unknown-filename", customId = undefined } = Predicate__namespace.isRecord(_url) ? _url : {};
        const httpClient = (yield* platform.HttpClient.HttpClient).pipe(platform.HttpClient.filterStatusOk);
        const arrayBuffer = yield* platform.HttpClientRequest.get(url).pipe(platform.HttpClientRequest.modify({
            headers: {}
        }), httpClient.execute, Effect__namespace.flatMap((_)=>_.arrayBuffer), Effect__namespace.mapError((cause)=>{
            return {
                code: "BAD_REQUEST",
                message: `Failed to download requested file: ${cause.message}`,
                data: cause.toJSON()
            };
        }), Effect__namespace.scoped);
        return new UTFile([
            arrayBuffer
        ], name, {
            customId,
            lastModified: Date.now()
        });
    }).pipe(Effect__namespace.withLogSpan("downloadFile"));
const generatePresignedUrl = (file, cd, acl)=>Effect__namespace.gen(function*() {
        const { apiKey, appId } = yield* config_cjs.UTToken;
        const baseUrl = yield* config_cjs.IngestUrl(undefined);
        const key = yield* shared.generateKey(file, appId);
        const url = yield* shared.generateSignedURL(`${baseUrl}/${key}`, apiKey, {
            // ttlInSeconds: routeOptions.presignedURLTTL,
            data: {
                "x-ut-identifier": appId,
                "x-ut-file-name": file.name,
                "x-ut-file-size": file.size,
                "x-ut-file-type": file.type,
                "x-ut-custom-id": file.customId,
                "x-ut-content-disposition": cd,
                "x-ut-acl": acl
            }
        });
        return {
            url,
            key
        };
    }).pipe(Effect__namespace.withLogSpan("generatePresignedUrl"));
const uploadFile = (file, opts)=>Effect__namespace.gen(function*() {
        const presigned = yield* generatePresignedUrl(file, opts.contentDisposition ?? "inline", opts.acl).pipe(Effect__namespace.catchTag("UploadThingError", (e)=>Effect__namespace.fail(shared.UploadThingError.toObject(e))), Effect__namespace.catchTag("ConfigError", ()=>Effect__namespace.fail({
                code: "INVALID_SERVER_CONFIG",
                message: "Failed to generate presigned URL"
            })));
        const response = yield* uploadServer_cjs.uploadWithoutProgress(file, presigned).pipe(Effect__namespace.catchTag("UploadThingError", (e)=>Effect__namespace.fail(shared.UploadThingError.toObject(e))), Effect__namespace.catchTag("ResponseError", (e)=>Effect__namespace.fail({
                code: "UPLOAD_FAILED",
                message: "Failed to upload file",
                data: e.toJSON()
            })));
        return {
            key: presigned.key,
            url: response.url,
            appUrl: response.appUrl,
            ufsUrl: response.ufsUrl,
            lastModified: file.lastModified ?? Date.now(),
            name: file.name,
            size: file.size,
            type: file.type,
            customId: file.customId ?? null,
            fileHash: response.fileHash
        };
    }).pipe(Effect__namespace.withLogSpan("uploadFile"));

class UTApi {
    constructor(options){
        this.requestUploadThing = (pathname, body, responseSchema)=>Effect__namespace.gen(this, function*() {
                const { apiKey } = yield* config_cjs.UTToken;
                const baseUrl = yield* config_cjs.ApiUrl;
                const httpClient = (yield* platform.HttpClient.HttpClient).pipe(platform.HttpClient.filterStatusOk);
                return yield* platform.HttpClientRequest.post(pathname).pipe(platform.HttpClientRequest.prependUrl(baseUrl), platform.HttpClientRequest.bodyUnsafeJson(body), platform.HttpClientRequest.setHeaders({
                    "x-uploadthing-version": config_cjs.UPLOADTHING_VERSION,
                    "x-uploadthing-be-adapter": "server-sdk",
                    "x-uploadthing-api-key": Redacted__namespace.value(apiKey)
                }), httpClient.execute, Effect__namespace.tapBoth({
                    onSuccess: logger_cjs.logHttpClientResponse("UploadThing API Response"),
                    onFailure: logger_cjs.logHttpClientError("Failed to request UploadThing API")
                }), Effect__namespace.flatMap(platform.HttpClientResponse.schemaBodyJson(responseSchema)), Effect__namespace.scoped);
            }).pipe(Effect__namespace.catchTag("ConfigError", (e)=>new shared.UploadThingError({
                    code: "INVALID_SERVER_CONFIG",
                    message: "There was an error with the server configuration. More info can be found on this error's `cause` property",
                    cause: e
                })), Effect__namespace.withLogSpan("utapi.#requestUploadThing"));
        this.executeAsync = async (program, signal)=>{
            const exit = await program.pipe(Effect__namespace.withLogSpan("utapi.#executeAsync"), (e)=>this.runtime.runPromiseExit(e, signal ? {
                    signal
                } : undefined));
            if (exit._tag === "Failure") {
                throw Cause__namespace.squash(exit.cause);
            }
            return exit.value;
        };
        /**
   * Request to delete files from UploadThing storage.
   * @param {string | string[]} fileKeys
   *
   * @example
   * await deleteFiles("2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg");
   *
   * @example
   * await deleteFiles(["2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg","1649353b-04ea-48a2-9db7-31de7f562c8d_image2.jpg"])
   *
   * @example
   * await deleteFiles("myCustomIdentifier", { keyType: "customId" })
   */ this.deleteFiles = async (keys, opts)=>{
            guardServerOnly();
            const { keyType = this.defaultKeyType } = opts ?? {};
            class DeleteFileResponse extends S__namespace.Class("DeleteFileResponse")({
                success: S__namespace.Boolean,
                deletedCount: S__namespace.Number
            }) {
            }
            return await this.executeAsync(this.requestUploadThing("/v6/deleteFiles", keyType === "fileKey" ? {
                fileKeys: Arr__namespace.ensure(keys)
            } : {
                customIds: Arr__namespace.ensure(keys)
            }, DeleteFileResponse).pipe(Effect__namespace.withLogSpan("deleteFiles")));
        };
        /**
   * Request file URLs from UploadThing storage.
   * @param {string | string[]} fileKeys
   *
   * @example
   * const data = await getFileUrls("2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg");
   * console.log(data); // [{key: "2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg", url: "https://uploadthing.com/f/2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg"}]
   *
   * @example
   * const data = await getFileUrls(["2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg","1649353b-04ea-48a2-9db7-31de7f562c8d_image2.jpg"])
   * console.log(data) // [{key: "2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg", url: "https://uploadthing.com/f/2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg" },{key: "1649353b-04ea-48a2-9db7-31de7f562c8d_image2.jpg", url: "https://uploadthing.com/f/1649353b-04ea-48a2-9db7-31de7f562c8d_image2.jpg"}]
   *
   * @deprecated - See https://docs.uploadthing.com/working-with-files#accessing-files for info how to access files
   */ this.getFileUrls = async (keys, opts)=>{
            guardServerOnly();
            const { keyType = this.defaultKeyType } = opts ?? {};
            class GetFileUrlResponse extends S__namespace.Class("GetFileUrlResponse")({
                data: S__namespace.Array(S__namespace.Struct({
                    key: S__namespace.String,
                    url: S__namespace.String
                }))
            }) {
            }
            return await this.executeAsync(this.requestUploadThing("/v6/getFileUrl", keyType === "fileKey" ? {
                fileKeys: Arr__namespace.ensure(keys)
            } : {
                customIds: Arr__namespace.ensure(keys)
            }, GetFileUrlResponse).pipe(Effect__namespace.withLogSpan("getFileUrls")));
        };
        /**
   * Request file list from UploadThing storage.
   * @param {object} opts
   * @param {number} opts.limit The maximum number of files to return
   * @param {number} opts.offset The number of files to skip
   *
   * @example
   * const data = await listFiles({ limit: 1 });
   * console.log(data); // { key: "2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg", id: "2e0fdb64-9957-4262-8e45-f372ba903ac8" }
   */ this.listFiles = async (opts)=>{
            guardServerOnly();
            class ListFileResponse extends S__namespace.Class("ListFileResponse")({
                hasMore: S__namespace.Boolean,
                files: S__namespace.Array(S__namespace.Struct({
                    id: S__namespace.String,
                    customId: S__namespace.NullOr(S__namespace.String),
                    key: S__namespace.String,
                    name: S__namespace.String,
                    size: S__namespace.Number,
                    status: S__namespace.Literal("Deletion Pending", "Failed", "Uploaded", "Uploading"),
                    uploadedAt: S__namespace.Number
                }))
            }) {
            }
            return await this.executeAsync(this.requestUploadThing("/v6/listFiles", {
                ...opts
            }, ListFileResponse).pipe(Effect__namespace.withLogSpan("listFiles")));
        };
        this.renameFiles = async (updates)=>{
            guardServerOnly();
            class RenameFileResponse extends S__namespace.Class("RenameFileResponse")({
                success: S__namespace.Boolean
            }) {
            }
            return await this.executeAsync(this.requestUploadThing("/v6/renameFiles", {
                updates: Arr__namespace.ensure(updates)
            }, RenameFileResponse).pipe(Effect__namespace.withLogSpan("renameFiles")));
        };
        this.getUsageInfo = async ()=>{
            guardServerOnly();
            class GetUsageInfoResponse extends S__namespace.Class("GetUsageInfoResponse")({
                totalBytes: S__namespace.Number,
                appTotalBytes: S__namespace.Number,
                filesUploaded: S__namespace.Number,
                limitBytes: S__namespace.Number
            }) {
            }
            return await this.executeAsync(this.requestUploadThing("/v6/getUsageInfo", {}, GetUsageInfoResponse).pipe(Effect__namespace.withLogSpan("getUsageInfo")));
        };
        /**
   * Generate a presigned url for a private file
   * Unlike {@link getSignedURL}, this method does not make a fetch request to the UploadThing API
   * and is the recommended way to generate a presigned url for a private file.
   **/ this.generateSignedURL = async (key, opts)=>{
            guardServerOnly();
            const expiresIn = shared.parseTimeToSeconds(opts?.expiresIn ?? "5 minutes");
            if (opts?.expiresIn && isNaN(expiresIn)) {
                throw new shared.UploadThingError({
                    code: "BAD_REQUEST",
                    message: "expiresIn must be a valid time string, for example '1d', '2 days', or a number of seconds."
                });
            }
            if (expiresIn > 86400 * 7) {
                throw new shared.UploadThingError({
                    code: "BAD_REQUEST",
                    message: "expiresIn must be less than 7 days (604800 seconds)."
                });
            }
            const program = Effect__namespace.gen(function*() {
                const { apiKey, appId } = yield* config_cjs.UTToken;
                const ufsHost = yield* config_cjs.UfsHost;
                const proto = ufsHost.includes("local") ? "http" : "https";
                const ufsUrl = yield* shared.generateSignedURL(`${proto}://${appId}.${ufsHost}/f/${key}`, apiKey, {
                    ttlInSeconds: expiresIn
                });
                return {
                    ufsUrl
                };
            });
            return await this.executeAsync(program.pipe(Effect__namespace.catchTag("ConfigError", (e)=>new shared.UploadThingError({
                    code: "INVALID_SERVER_CONFIG",
                    message: "There was an error with the server configuration. More info can be found on this error's `cause` property",
                    cause: e
                })), Effect__namespace.withLogSpan("generateSignedURL")));
        };
        /**
   * Request a presigned url for a private file(s)
   * @remarks This method is no longer recommended as it makes a fetch
   * request to the UploadThing API which incurs redundant latency. It
   * will be deprecated in UploadThing v8 and removed in UploadThing v9.
   *
   * @see {@link generateSignedURL} for a more efficient way to generate a presigned url
   **/ this.getSignedURL = async (key, opts)=>{
            guardServerOnly();
            const expiresIn = opts?.expiresIn ? shared.parseTimeToSeconds(opts.expiresIn) : undefined;
            const { keyType = this.defaultKeyType } = opts ?? {};
            if (opts?.expiresIn && isNaN(expiresIn)) {
                throw new shared.UploadThingError({
                    code: "BAD_REQUEST",
                    message: "expiresIn must be a valid time string, for example '1d', '2 days', or a number of seconds."
                });
            }
            if (expiresIn && expiresIn > 86400 * 7) {
                throw new shared.UploadThingError({
                    code: "BAD_REQUEST",
                    message: "expiresIn must be less than 7 days (604800 seconds)."
                });
            }
            class GetSignedUrlResponse extends S__namespace.Class("GetSignedUrlResponse")({
                url: S__namespace.String,
                ufsUrl: S__namespace.String
            }) {
            }
            return await this.executeAsync(this.requestUploadThing("/v6/requestFileAccess", keyType === "fileKey" ? {
                fileKey: key,
                expiresIn
            } : {
                customId: key,
                expiresIn
            }, GetSignedUrlResponse).pipe(Effect__namespace.withLogSpan("getSignedURL")));
        };
        /**
   * Update the ACL of a file or set of files.
   *
   * @example
   * // Make a single file public
   * await utapi.updateACL("2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg", "public-read");
   *
   * // Make multiple files private
   * await utapi.updateACL(
   *   [
   *     "2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg",
   *     "1649353b-04ea-48a2-9db7-31de7f562c8d_image2.jpg",
   *   ],
   *   "private",
   * );
   */ this.updateACL = async (keys, acl, opts)=>{
            guardServerOnly();
            const { keyType = this.defaultKeyType } = opts ?? {};
            const updates = Arr__namespace.ensure(keys).map((key)=>{
                return keyType === "fileKey" ? {
                    fileKey: key,
                    acl
                } : {
                    customId: key,
                    acl
                };
            });
            const responseSchema = S__namespace.Struct({
                success: S__namespace.Boolean
            });
            return await this.executeAsync(this.requestUploadThing("/v6/updateACL", {
                updates
            }, responseSchema).pipe(Effect__namespace.withLogSpan("updateACL")));
        };
        // Assert some stuff
        guardServerOnly();
        this.opts = options ?? {};
        this.fetch = this.opts.fetch ?? globalThis.fetch;
        this.defaultKeyType = this.opts.defaultKeyType ?? "fileKey";
        this.runtime = runtime_cjs.makeRuntime(this.fetch, this.opts);
    }
    uploadFiles(files, opts) {
        guardServerOnly();
        const concurrency = opts?.concurrency ?? 1;
        if (concurrency < 1 || concurrency > 25) {
            throw new shared.UploadThingError({
                code: "BAD_REQUEST",
                message: "concurrency must be a positive integer between 1 and 25"
            });
        }
        const program = Effect__namespace.forEach(Arr__namespace.ensure(files), (file)=>uploadFile(file, opts ?? {}).pipe(Effect__namespace.match({
                onSuccess: (data)=>({
                        data,
                        error: null
                    }),
                onFailure: (error)=>({
                        data: null,
                        error
                    })
            })), {
            concurrency
        }).pipe(Effect__namespace.map((ups)=>Array.isArray(files) ? ups : ups[0]), Effect__namespace.tap((res)=>Effect__namespace.logDebug("Finished uploading").pipe(Effect__namespace.annotateLogs("uploadResult", res))), Effect__namespace.withLogSpan("uploadFiles"));
        return this.executeAsync(program, opts?.signal);
    }
    uploadFilesFromUrl(urls, opts) {
        guardServerOnly();
        const concurrency = opts?.concurrency ?? 1;
        if (concurrency < 1 || concurrency > 25) {
            throw new shared.UploadThingError({
                code: "BAD_REQUEST",
                message: "concurrency must be a positive integer between 1 and 25"
            });
        }
        const program = Effect__namespace.forEach(Arr__namespace.ensure(urls), (url)=>downloadFile(url).pipe(Effect__namespace.flatMap((file)=>uploadFile(file, opts ?? {})), Effect__namespace.match({
                onSuccess: (data)=>({
                        data,
                        error: null
                    }),
                onFailure: (error)=>({
                        data: null,
                        error
                    })
            })), {
            concurrency
        }).pipe(Effect__namespace.map((ups)=>Array.isArray(urls) ? ups : ups[0]), Effect__namespace.tap((res)=>Effect__namespace.logDebug("Finished uploading").pipe(Effect__namespace.annotateLogs("uploadResult", res))), Effect__namespace.withLogSpan("uploadFiles")).pipe(Effect__namespace.withLogSpan("uploadFilesFromUrl"));
        return this.executeAsync(program, opts?.signal);
    }
}

const createUploadthing = (opts)=>uploadBuilder_cjs.createBuilder(opts);
const createRouteHandler = (opts)=>{
    return handler_cjs.makeAdapterHandler((ev)=>Effect__namespace.succeed({
            req: "request" in ev ? ev.request : ev
        }), (ev)=>Effect__namespace.succeed("request" in ev ? ev.request : ev), opts, "server");
};
const extractRouterConfig = (router)=>Effect__namespace.runSync(routeConfig_cjs.extractRouterConfig(router));

Object.defineProperty(exports, "UploadThingError", {
  enumerable: true,
  get: function () { return shared.UploadThingError; }
});
Object.defineProperty(exports, "makeAdapterHandler", {
  enumerable: true,
  get: function () { return handler_cjs.makeAdapterHandler; }
});
Object.defineProperty(exports, "createBuilder", {
  enumerable: true,
  get: function () { return uploadBuilder_cjs.createBuilder; }
});
Object.defineProperty(exports, "UTFiles", {
  enumerable: true,
  get: function () { return types_cjs.UTFiles; }
});
Object.defineProperty(exports, "experimental_UTRegion", {
  enumerable: true,
  get: function () { return types_cjs.UTRegion; }
});
exports.UTApi = UTApi;
exports.UTFile = UTFile;
exports.createRouteHandler = createRouteHandler;
exports.createUploadthing = createUploadthing;
exports.extractRouterConfig = extractRouterConfig;
