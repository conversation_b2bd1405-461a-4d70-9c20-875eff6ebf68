(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1650],{4229:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},12486:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},14503:(e,t,r)=>{"use strict";r.d(t,{dj:()=>f,oR:()=>u});var a=r(12115);let s=0,n=new Map,i=e=>{if(n.has(e))return;let t=setTimeout(()=>{n.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,t)},o=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?i(r):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],d={toasts:[]};function c(e){d=o(d,e),l.forEach(e=>{e(d)})}function u(e){let{...t}=e,r=(s=(s+1)%Number.MAX_SAFE_INTEGER).toString(),a=()=>c({type:"DISMISS_TOAST",toastId:r});return c({type:"ADD_TOAST",toast:{...t,id:r,open:!0,onOpenChange:e=>{e||a()}}}),{id:r,dismiss:a,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:r}})}}function f(){let[e,t]=a.useState(d);return a.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},17580:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},28905:(e,t,r)=>{"use strict";r.d(t,{C:()=>i});var a=r(12115),s=r(6101),n=r(52712),i=e=>{let{present:t,children:r}=e,i=function(e){var t,r;let[s,i]=a.useState(),l=a.useRef({}),d=a.useRef(e),c=a.useRef("none"),[u,f]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},a.useReducer((e,t)=>{let a=r[e][t];return null!=a?a:e},t));return a.useEffect(()=>{let e=o(l.current);c.current="mounted"===u?e:"none"},[u]),(0,n.N)(()=>{let t=l.current,r=d.current;if(r!==e){let a=c.current,s=o(t);e?f("MOUNT"):"none"===s||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):r&&a!==s?f("ANIMATION_OUT"):f("UNMOUNT"),d.current=e}},[e,f]),(0,n.N)(()=>{if(s){var e;let t;let r=null!==(e=s.ownerDocument.defaultView)&&void 0!==e?e:window,a=e=>{let a=o(l.current).includes(e.animationName);if(e.target===s&&a&&(f("ANIMATION_END"),!d.current)){let e=s.style.animationFillMode;s.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===s.style.animationFillMode&&(s.style.animationFillMode=e)})}},n=e=>{e.target===s&&(c.current=o(l.current))};return s.addEventListener("animationstart",n),s.addEventListener("animationcancel",a),s.addEventListener("animationend",a),()=>{r.clearTimeout(t),s.removeEventListener("animationstart",n),s.removeEventListener("animationcancel",a),s.removeEventListener("animationend",a)}}f("ANIMATION_END")},[s,f]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:a.useCallback(e=>{e&&(l.current=getComputedStyle(e)),i(e)},[])}}(t),l="function"==typeof r?r({present:i.isPresent}):a.Children.only(r),d=(0,s.s)(i.ref,function(e){var t,r;let a=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,s=a&&"isReactWarning"in a&&a.isReactWarning;return s?e.ref:(s=(a=null===(r=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===r?void 0:r.get)&&"isReactWarning"in a&&a.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof r||i.isPresent?a.cloneElement(l,{ref:d}):null};function o(e){return(null==e?void 0:e.animationName)||"none"}i.displayName="Presence"},31886:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var a=r(23464);class s{async get(e,t){try{if(this.isOfflineMode&&!e.includes("/health"))throw console.log("API Client: In offline mode, skipping GET request to ".concat(e)),Error("Network Error: Application is in offline mode");let r=await this.client.get(e,t);return this.isOfflineMode&&this.setOfflineMode(!1),r.data}catch(e){throw e}}async post(e,t,r){return(await this.client.post(e,t,r)).data}async put(e,t,r){return(await this.client.put(e,t,r)).data}async delete(e,t){try{console.log("Making DELETE request to: ".concat(e));let r=await this.client.delete(e,t);if(204===r.status)return console.log("DELETE request to ".concat(e," successful with 204 status")),null;return r.data}catch(t){throw console.error("DELETE request to ".concat(e," failed:"),t),t}}async patch(e,t,r){return(await this.client.patch(e,t,r)).data}async upload(e,t,r){let a={...r,headers:{...null==r?void 0:r.headers,"Content-Type":"multipart/form-data"}};return(await this.client.post(e,t,a)).data}getBaseURL(){return this.baseURL}isInOfflineMode(){return this.isOfflineMode}setOfflineMode(e){this.isOfflineMode!==e&&(console.log("API Client: Setting offline mode to ".concat(e)),this.isOfflineMode=e,window.dispatchEvent(new CustomEvent("api:offline",{detail:{isOffline:e}})))}async tryReconnect(){try{return console.log("API Client: Attempting to reconnect to the server..."),await this.client.get("/health",{timeout:5e3}),console.log("API Client: Reconnection successful"),this.setOfflineMode(!1),!0}catch(e){return console.log("API Client: Reconnection failed, still in offline mode"),!1}}constructor(){this.isOfflineMode=!1,this.baseURL="http://localhost:5000/api/v1",this.baseURL||console.warn("NEXT_PUBLIC_BACKEND_API_URL is not defined. API requests may fail."),this.client=a.A.create({baseURL:this.baseURL,headers:{"Content-Type":"application/json"},timeout:1e4}),this.client.interceptors.request.use(e=>e,e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,e=>{if(e.response){if(console.error("API Error Response:",e.response.status,e.response.data),e.response.data&&0!==Object.keys(e.response.data).length||(e.response.data={error:"Request failed with status code ".concat(e.response.status)}),404===e.response.status){var t;console.log("Resource not found:",null===(t=e.config)||void 0===t?void 0:t.url),e.response.data={error:"The requested resource was not found. Please refresh and try again."}}e.responseData=e.response.data}else e.request?(console.error("API No Response:",e.request),e.message&&(e.message.includes("Network Error")||e.message.includes("timeout"))?(console.error("Network Error detected. Server might be down or unreachable."),this.setOfflineMode(!0),e.isOffline=!0,e.responseData={error:"Network Error: Unable to connect to the server. Please check your connection or try again later.",isOffline:!0},console.warn("API Client: Server connection failed. If you're running in development mode, make sure your backend server is running."),window.dispatchEvent(new CustomEvent("api:offline",{detail:{isOffline:!0}}))):e.responseData={error:"No response received from server. Please check your connection."}):(console.error("API Request Error:",e.message),e.responseData={error:e.message||"An unexpected error occurred"});return Promise.reject(e)})}}let n=new s},35169:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},35695:(e,t,r)=>{"use strict";var a=r(18999);r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},40968:(e,t,r)=>{"use strict";r.d(t,{b:()=>o});var a=r(12115),s=r(63655),n=r(95155),i=a.forwardRef((e,t)=>(0,n.jsx)(s.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null===(r=e.onMouseDown)||void 0===r||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var o=i},47354:(e,t,r)=>{Promise.resolve().then(r.bind(r,94556))},53999:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var a=r(52596),s=r(39688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}},76981:(e,t,r)=>{"use strict";r.d(t,{C1:()=>C,bL:()=>A});var a=r(12115),s=r(6101),n=r(46081),i=r(85185),o=r(5845),l=r(45503),d=r(11275),c=r(28905),u=r(63655),f=r(95155),m="Checkbox",[p,g]=(0,n.A)(m),[h,v]=p(m),x=a.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:n,checked:l,defaultChecked:d,required:c,disabled:m,value:p="on",onCheckedChange:g,form:v,...x}=e,[y,b]=a.useState(null),A=(0,s.s)(t,e=>b(e)),C=a.useRef(!1),E=!y||v||!!y.closest("form"),[k=!1,T]=(0,o.i)({prop:l,defaultProp:d,onChange:g}),R=a.useRef(k);return a.useEffect(()=>{let e=null==y?void 0:y.form;if(e){let t=()=>T(R.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[y,T]),(0,f.jsxs)(h,{scope:r,state:k,disabled:m,children:[(0,f.jsx)(u.sG.button,{type:"button",role:"checkbox","aria-checked":w(k)?"mixed":k,"aria-required":c,"data-state":j(k),"data-disabled":m?"":void 0,disabled:m,value:p,...x,ref:A,onKeyDown:(0,i.m)(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,i.m)(e.onClick,e=>{T(e=>!!w(e)||!e),E&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),E&&(0,f.jsx)(N,{control:y,bubbles:!C.current,name:n,value:p,checked:k,required:c,disabled:m,form:v,style:{transform:"translateX(-100%)"},defaultChecked:!w(d)&&d})]})});x.displayName=m;var y="CheckboxIndicator",b=a.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:a,...s}=e,n=v(y,r);return(0,f.jsx)(c.C,{present:a||w(n.state)||!0===n.state,children:(0,f.jsx)(u.sG.span,{"data-state":j(n.state),"data-disabled":n.disabled?"":void 0,...s,ref:t,style:{pointerEvents:"none",...e.style}})})});b.displayName=y;var N=e=>{let{control:t,checked:r,bubbles:s=!0,defaultChecked:n,...i}=e,o=a.useRef(null),c=(0,l.Z)(r),u=(0,d.X)(t);a.useEffect(()=>{let e=o.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(c!==r&&t){let a=new Event("click",{bubbles:s});e.indeterminate=w(r),t.call(e,!w(r)&&r),e.dispatchEvent(a)}},[c,r,s]);let m=a.useRef(!w(r)&&r);return(0,f.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:null!=n?n:m.current,...i,tabIndex:-1,ref:o,style:{...e.style,...u,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function w(e){return"indeterminate"===e}function j(e){return w(e)?"indeterminate":e?"checked":"unchecked"}var A=x,C=b},81497:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},82714:(e,t,r)=>{"use strict";r.d(t,{J:()=>d});var a=r(95155),s=r(12115),n=r(40968),i=r(74466),o=r(53999);let l=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.b,{ref:t,className:(0,o.cn)(l(),r),...s})});d.displayName=n.b.displayName},88145:(e,t,r)=>{"use strict";r.d(t,{E:()=>o});var a=r(95155);r(12115);var s=r(74466),n=r(53999);let i=(0,s.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:r,...s}=e;return(0,a.jsx)("div",{className:(0,n.cn)(i({variant:r}),t),...s})}},88482:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>i,aR:()=>o,wL:()=>u});var a=r(95155),s=r(12115),n=r(53999);let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...s})});i.displayName="Card";let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",r),...s})});o.displayName="CardHeader";let l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",r),...s})});l.displayName="CardTitle";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",r),...s})});d.displayName="CardDescription";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",r),...s})});c.displayName="CardContent";let u=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",r),...s})});u.displayName="CardFooter"},89852:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var a=r(95155),s=r(12115),n=r(53999);let i=s.forwardRef((e,t)=>{let{className:r,type:s,...i}=e;return(0,a.jsx)("input",{type:s,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,...i})});i.displayName="Input"},94556:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>A});var a=r(95155),s=r(12115),n=r(35695),i=r(97168),o=r(88482),l=r(89852),d=r(82714),c=r(99474),u=r(95784),f=r(95139),m=r(88145),p=r(35169),g=r(81497),h=r(17580),v=r(4229),x=r(12486),y=r(14503),b=r(31886),N=r(6874),w=r.n(N);function j(){let e=(0,n.useRouter)(),{toast:t}=(0,y.dj)(),[r,N]=(0,s.useState)(!1),[j,A]=(0,s.useState)([]),[C,E]=(0,s.useState)([]),[k,T]=(0,s.useState)(!0),[R,I]=(0,s.useState)(!0),[O,S]=(0,s.useState)({name:"",message:"",templateId:"",selectedClientTypes:[],sendImmediately:!0});(0,s.useEffect)(()=>{(async()=>{try{let e=await b.A.get("/marketing/templates");console.log("Templates response:",e);let t=(Array.isArray(e)?e:[]).sort((e,t)=>"ar"===e.language&&"ar"!==t.language?-1:"ar"!==e.language&&"ar"===t.language?1:"en"===e.language&&"en"!==t.language&&"ar"!==t.language?-1:"en"!==e.language&&"en"===t.language&&"ar"!==e.language?1:e.name.localeCompare(t.name));A(t),T(!1);let r=await b.A.get("/marketing/client-types");console.log("Client types response:",r);let a=Array.isArray(r)?r:[];E(a),I(!1)}catch(e){console.error("Error fetching data:",e),t({title:"Error",description:"Failed to load templates and client types",variant:"destructive"}),T(!1),I(!1)}})()},[t]);let M=(e,t)=>{S(r=>({...r,[e]:t}))},P=e=>{S(t=>({...t,selectedClientTypes:t.selectedClientTypes.includes(e)?t.selectedClientTypes.filter(t=>t!==e):[...t.selectedClientTypes,e]}))},D=async function(){let r=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{if(N(!0),!O.name.trim()){t({title:"Validation Error",description:"Campaign name is required",variant:"destructive"});return}if(!O.message.trim()){t({title:"Validation Error",description:"Campaign message is required",variant:"destructive"});return}if(0===O.selectedClientTypes.length){t({title:"Validation Error",description:"Please select at least one client type",variant:"destructive"});return}let a={name:O.name.trim(),message:O.message.trim(),type:"WhatsApp",status:r?"Draft":O.sendImmediately?"Active":"Scheduled",clientTypes:O.selectedClientTypes.map(e=>{let t=C.find(t=>t.id===e);return t?t.name:e}),templateId:O.templateId||void 0};console.log("Creating campaign:",a),await b.A.post("/marketing/campaigns",a),t({title:"Success",description:"Campaign ".concat(r?"saved as draft":"created"," successfully")}),e.push("/dashboard/campaigns")}catch(e){var a,s;console.error("Error creating campaign:",e),t({title:"Error",description:(null===(s=e.response)||void 0===s?void 0:null===(a=s.data)||void 0===a?void 0:a.error)||"Failed to create campaign",variant:"destructive"})}finally{N(!1)}},L=O.templateId&&"none"!==O.templateId?j.find(e=>e.id===O.templateId):null;return(0,a.jsxs)("div",{className:"max-w-2xl mx-auto space-y-6",children:[(0,a.jsx)(w(),{href:"/dashboard/campaigns",children:(0,a.jsxs)(i.$,{variant:"outline",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Back to Campaigns"]})}),(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{children:(0,a.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(g.A,{className:"h-5 w-5"}),"Campaign Details"]})}),(0,a.jsxs)(o.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"name",children:"Campaign Name *"}),(0,a.jsx)(l.p,{id:"name",placeholder:"Enter campaign name",value:O.name,onChange:e=>M("name",e.target.value)})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(d.J,{htmlFor:"template",children:["Template (Optional)",j.length>0&&(0,a.jsxs)("span",{className:"ml-2 text-xs text-muted-foreground",children:["(",j.filter(e=>"ar"===e.language).length," Arabic, ",j.filter(e=>"en"===e.language).length," English)"]})]}),(0,a.jsxs)(u.l6,{value:O.templateId||"none",onValueChange:e=>{if("none"===e||"no-templates"===e)S(e=>({...e,templateId:""}));else{let t=j.find(t=>t.id===e);S(r=>({...r,templateId:e,message:t?t.content:r.message}))}},children:[(0,a.jsx)(u.bq,{children:(0,a.jsx)(u.yv,{placeholder:k?"Loading templates...":"Select a template"})}),(0,a.jsxs)(u.gC,{children:[(0,a.jsx)(u.eb,{value:"none",children:"No template"}),j.length>0?j.map(e=>(0,a.jsxs)(u.eb,{value:e.id,children:["ar"===e.language?"\uD83C\uDDF8\uD83C\uDDE6":"\uD83C\uDDFA\uD83C\uDDF8"," ",e.name," (",e.language.toUpperCase(),") - ",e.category]},e.id)):!k&&(0,a.jsx)(u.eb,{value:"no-templates",disabled:!0,children:"No templates available"})]})]}),L&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m.E,{variant:"outline",children:L.category}),(0,a.jsx)(m.E,{variant:"secondary",children:L.language})]}),(0,a.jsxs)("div",{className:"text-sm text-muted-foreground p-2 bg-muted rounded",children:[(0,a.jsx)("strong",{children:"Preview:"})," ",L.content.substring(0,100),L.content.length>100&&"..."]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"message",children:"Message *"}),(0,a.jsx)(c.T,{id:"message",placeholder:"Enter your campaign message",value:O.message,onChange:e=>M("message",e.target.value),rows:4}),(0,a.jsxs)("div",{className:"text-sm text-muted-foreground",children:[O.message.length," characters"]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{children:"Target Audience *"}),(0,a.jsx)("div",{className:"space-y-2",children:R?(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Loading client types..."}):0===C.length?(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"No client types available"}):C.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(f.S,{id:e.id,checked:O.selectedClientTypes.includes(e.id),onCheckedChange:()=>P(e.id)}),(0,a.jsxs)(d.J,{htmlFor:e.id,className:"flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"h-4 w-4"}),e.name]})]},e.id))}),O.selectedClientTypes.length>0&&(0,a.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Selected: ",O.selectedClientTypes.length," client type(s)"]})]}),(0,a.jsxs)("div",{className:"flex gap-2 pt-4",children:[(0,a.jsxs)(i.$,{onClick:()=>D(!0),variant:"outline",disabled:r,children:[(0,a.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"Save as Draft"]}),(0,a.jsxs)(i.$,{onClick:()=>D(!1),disabled:r,children:[(0,a.jsx)(x.A,{className:"h-4 w-4 mr-2"}),r?"Creating...":"Create & Send"]})]})]})]})]})}function A(){return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold",children:"Create Campaign"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Create a new WhatsApp campaign"})]}),(0,a.jsx)(j,{})]})}},95139:(e,t,r)=>{"use strict";r.d(t,{S:()=>l});var a=r(95155),s=r(12115),n=r(76981),i=r(5196),o=r(53999);let l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.bL,{ref:t,className:(0,o.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",r),...s,children:(0,a.jsx)(n.C1,{className:(0,o.cn)("flex items-center justify-center text-current"),children:(0,a.jsx)(i.A,{className:"h-4 w-4"})})})});l.displayName=n.bL.displayName},95784:(e,t,r)=>{"use strict";r.d(t,{bq:()=>f,eb:()=>h,gC:()=>g,l6:()=>c,yv:()=>u});var a=r(95155),s=r(12115),n=r(31992),i=r(66474),o=r(47863),l=r(5196),d=r(53999);let c=n.bL;n.YJ;let u=n.WT,f=s.forwardRef((e,t)=>{let{className:r,children:s,...o}=e;return(0,a.jsxs)(n.l9,{ref:t,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",r),...o,children:[s,(0,a.jsx)(n.In,{asChild:!0,children:(0,a.jsx)(i.A,{className:"h-4 w-4 opacity-50"})})]})});f.displayName=n.l9.displayName;let m=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.PP,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",r),...s,children:(0,a.jsx)(o.A,{className:"h-4 w-4"})})});m.displayName=n.PP.displayName;let p=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.wn,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",r),...s,children:(0,a.jsx)(i.A,{className:"h-4 w-4"})})});p.displayName=n.wn.displayName;let g=s.forwardRef((e,t)=>{let{className:r,children:s,position:i="popper",...o}=e;return(0,a.jsx)(n.ZL,{children:(0,a.jsxs)(n.UC,{ref:t,className:(0,d.cn)("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",r),position:i,...o,children:[(0,a.jsx)(m,{}),(0,a.jsx)(n.LM,{className:(0,d.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,a.jsx)(p,{})]})})});g.displayName=n.UC.displayName,s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.JU,{ref:t,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",r),...s})}).displayName=n.JU.displayName;let h=s.forwardRef((e,t)=>{let{className:r,children:s,...i}=e;return(0,a.jsxs)(n.q7,{ref:t,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),...i,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(n.VF,{children:(0,a.jsx)(l.A,{className:"h-4 w-4"})})}),(0,a.jsx)(n.p4,{children:s})]})});h.displayName=n.q7.displayName,s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.wv,{ref:t,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",r),...s})}).displayName=n.wv.displayName},97168:(e,t,r)=>{"use strict";r.d(t,{$:()=>d,r:()=>l});var a=r(95155),s=r(12115),n=r(99708),i=r(74466),o=r(53999);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=s.forwardRef((e,t)=>{let{className:r,variant:s,size:i,asChild:d=!1,...c}=e,u=d?n.DX:"button";return(0,a.jsx)(u,{className:(0,o.cn)(l({variant:s,size:i,className:r})),ref:t,...c})});d.displayName="Button"},99474:(e,t,r)=>{"use strict";r.d(t,{T:()=>i});var a=r(95155),s=r(12115),n=r(53999);let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,...s})});i.displayName="Textarea"}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,6071,9509,9855,3464,6874,8441,1684,7358],()=>t(47354)),_N_E=e.O()}]);