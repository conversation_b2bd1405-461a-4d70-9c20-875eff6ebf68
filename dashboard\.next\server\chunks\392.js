"use strict";exports.id=392,exports.ids=[392],exports.modules={10392:(e,a,r)=>{r.d(a,{V:()=>F});var s=r(60687),t=r(43210),l=r(13964),d=r(96474),i=r(11860),o=r(47033),n=r(14952),c=r(8819),m=r(24934),x=r(68988),g=r(39390),u=r(15616),h=r(63974),p=r(42902),b=r(55192),f=r(59821),y=r(62369),j=r(96241);let v=t.forwardRef(({className:e,orientation:a="horizontal",decorative:r=!0,...t},l)=>(0,s.jsx)(y.b,{ref:l,decorative:r,orientation:a,className:(0,j.cn)("shrink-0 bg-border","horizontal"===a?"h-[1px] w-full":"h-full w-[1px]",e),...t}));v.displayName=y.b.displayName;var N=r(10452),k=r(16023),w=r(41862),A=r(35308);(0,A.Jt)();let C=(0,A.Wi)();var I=r(52581);function P({images:e,onImagesChange:a,onAutoSave:r,onUploadStatusChange:d,propertyId:o,maxImages:n=10,disabled:c=!1}){let{language:x,isArabic:g}=(0,N.Y)(),[u,h]=(0,t.useState)(!1),[p,b]=(0,t.useState)("idle"),f={ar:{uploadImages:"رفع الصور",dragDrop:"اسحب الصور هنا أو انقر للاختيار",uploading:"جاري الرفع...",autoSaving:"حفظ تلقائي...",saved:"تم الحفظ",remove:"حذف",mainImage:"الصورة الرئيسية",images:"صور",of:"من"},en:{uploadImages:"Upload Images",dragDrop:"Drag images here or click to select",uploading:"Uploading...",autoSaving:"Auto-saving...",saved:"Saved",remove:"Remove",mainImage:"Main Image",images:"images",of:"of"}}[x],y=(0,t.useCallback)(s=>{if(s&&s.length>0){let t=[...e,...s.map(e=>e.url)].slice(0,n);a(t),h(!1),d?.(!1),I.oR.success(`${s.length} ${f.images} ${f.saved}`),r&&t.length>0&&setTimeout(()=>{r(t)},500)}},[e,a,n,d,r,f.images,f.saved]),j=(0,t.useCallback)(e=>{console.error("Upload error:",e),h(!1),d?.(!1),I.oR.error(`Upload failed: ${e.message}`)},[d]),v=(0,t.useCallback)(()=>{h(!0),d?.(!0)},[d]),A=(0,t.useCallback)(r=>{a(e.filter((e,a)=>a!==r))},[e,a]),P=(0,t.useCallback)(r=>{if(0===r)return;let s=[...e],[t]=s.splice(r,1);s.unshift(t),a(s)},[e,a]),F=e.length<n;return(0,s.jsxs)("div",{className:`space-y-4 ${g?"rtl":"ltr"}`,dir:g?"rtl":"ltr",children:[F&&!c&&(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)("div",{className:"border-2 border-dashed border-slate-600 rounded-lg p-6 text-center hover:border-emerald-500 bg-slate-800/50",children:[(0,s.jsx)(k.A,{className:"mx-auto h-8 w-8 text-slate-400 mb-3"}),(0,s.jsx)("p",{className:"text-sm text-slate-400 mb-4",children:f.dragDrop}),(0,s.jsxs)("p",{className:"text-xs text-slate-500",children:[e.length," ",f.of," ",n," ",f.images]})]}),(0,s.jsx)(C,{endpoint:"propertyImageUploader",onClientUploadComplete:y,onUploadError:j,onUploadBegin:v,className:"absolute inset-0 opacity-0 cursor-pointer"}),u&&(0,s.jsx)("div",{className:"absolute inset-0 bg-white/90 dark:bg-slate-900/90 rounded-lg flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(w.A,{className:"h-6 w-6 animate-spin mx-auto mb-2 text-emerald-500"}),(0,s.jsx)("p",{className:"text-sm text-slate-600 dark:text-slate-400",children:f.uploading})]})})]}),"idle"!==p&&(0,s.jsxs)("div",{className:`flex items-center gap-2 text-sm ${"saving"===p?"text-blue-600 dark:text-blue-400":"text-green-600 dark:text-green-400"} ${g?"flex-row-reverse":""}`,children:["saving"===p?(0,s.jsx)(w.A,{className:"h-4 w-4 animate-spin"}):(0,s.jsx)(l.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"saving"===p?f.autoSaving:f.saved})]}),e.length>0&&(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("div",{className:"relative",children:(0,s.jsxs)("div",{className:"aspect-video bg-slate-100 dark:bg-slate-800 rounded-lg overflow-hidden",children:[(0,s.jsx)("img",{src:e[0],alt:"Main property image",className:"w-full h-full object-cover"}),(0,s.jsx)("div",{className:`absolute top-2 ${g?"right-2":"left-2"} bg-emerald-600 text-white text-xs px-2 py-1 rounded`,children:f.mainImage}),(0,s.jsx)(m.$,{type:"button",variant:"destructive",size:"sm",className:`absolute top-2 ${g?"left-2":"right-2"} h-6 w-6 p-0 rounded`,onClick:()=>A(0),children:(0,s.jsx)(i.A,{className:"h-3 w-3"})})]})}),e.length>1&&(0,s.jsx)("div",{className:"grid grid-cols-4 gap-2",children:e.slice(1).map((e,a)=>(0,s.jsx)("div",{className:"relative group",children:(0,s.jsxs)("div",{className:"aspect-square bg-slate-100 dark:bg-slate-800 rounded overflow-hidden cursor-pointer hover:opacity-75 transition-opacity",onClick:()=>P(a+1),children:[(0,s.jsx)("img",{src:e,alt:`Property image ${a+2}`,className:"w-full h-full object-cover"}),(0,s.jsx)(m.$,{type:"button",variant:"destructive",size:"sm",className:"absolute -top-1 -right-1 h-5 w-5 p-0 rounded-full opacity-0 group-hover:opacity-100 transition-opacity",onClick:e=>{e.stopPropagation(),A(a+1)},children:(0,s.jsx)(i.A,{className:"h-2 w-2"})})]})},a+1))})]}),e.length>=n&&(0,s.jsx)("div",{className:"text-center p-3 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg",children:(0,s.jsx)("p",{className:"text-sm text-amber-800 dark:text-amber-200",children:"ar"===x?`الحد الأقصى ${n} صور`:`Maximum ${n} images`})})]})}function F({onSave:e,loading:a,initialData:r,isEdit:y=!1,propertyId:j}){let{language:k}=(0,N.Y)(),[w,A]=(0,t.useState)(1),[C,I]=(0,t.useState)(!1),[F,E]=(0,t.useState)(r||{title:"",titleAr:"",description:"",descriptionAr:"",price:"",currency:"SAR",type:"APARTMENT",status:"AVAILABLE",bedrooms:"",bathrooms:"",area:"",location:"",locationAr:"",address:"",addressAr:"",city:"",cityAr:"",country:"Saudi Arabia",countryAr:"المملكة العربية السعودية",images:[],features:[],featuresAr:[],amenities:[],amenitiesAr:[],yearBuilt:"",parking:"",furnished:!1,petFriendly:!1,utilities:"",utilitiesAr:"",contactInfo:"",isFeatured:!1,isActive:!0}),[S,R]=(0,t.useState)(""),[D,T]=(0,t.useState)(""),[J,U]=(0,t.useState)(""),[B,$]=(0,t.useState)(""),L={ar:{step:"الخطوة",of:"من",next:"التالي",previous:"السابق",save:"حفظ العقار",required:"مطلوب",optional:"اختياري",basicInfo:"المعلومات الأساسية",propertyDetails:"تفاصيل العقار",locationInfo:"معلومات الموقع",additionalInfo:"معلومات إضافية",title:"عنوان العقار",description:"وصف العقار",price:"السعر",currency:"العملة",propertyType:"نوع العقار",status:"حالة العقار",bedrooms:"غرف النوم",bathrooms:"دورات المياه",area:"المساحة (متر مربع)",yearBuilt:"سنة البناء",parking:"مواقف السيارات",location:"الموقع",address:"العنوان",city:"المدينة",country:"الدولة",images:"صور العقار",features:"مميزات العقار",amenities:"المرافق والخدمات",utilities:"الخدمات المشمولة",contactInfo:"معلومات التواصل",furnished:"مفروش",petFriendly:"يسمح بالحيوانات الأليفة",featured:"عقار مميز",active:"نشط",addFeature:"إضافة ميزة",addAmenity:"إضافة مرفق",uploadImages:"رفع صور العقار",dragDropImages:"اسحب وأفلت الصور هنا، أو انقر للاختيار",titlePlaceholder:"أدخل عنوان العقار...",descriptionPlaceholder:"اكتب وصفاً مفصلاً للعقار...",locationPlaceholder:"أدخل موقع العقار...",addressPlaceholder:"أدخل العنوان الكامل...",cityPlaceholder:"أدخل اسم المدينة...",featurePlaceholder:"أضف ميزة جديدة...",amenityPlaceholder:"أضف مرفق جديد...",utilitiesPlaceholder:"اذكر الخدمات المشمولة...",contactPlaceholder:"أدخل معلومات التواصل...",stepDescription1:"أدخل المعلومات الأساسية للعقار",stepDescription2:"حدد تفاصيل ومواصفات العقار",stepDescription3:"أضف معلومات الموقع والعنوان",stepDescription4:"أضف الصور والمعلومات الإضافية",completed:"مكتمل",current:"الحالي",pending:"في الانتظار",imageGallery:"معرض الصور",mainImage:"الصورة الرئيسية",additionalImages:"الصور الإضافية",imageTips:"نصائح للصور",noFeatures:"لا توجد مميزات مضافة",noAmenities:"لا توجد مرافق مضافة",noImages:"لم يتم رفع صور بعد",setAsMain:"تعيين كصورة رئيسية",removeImage:"حذف الصورة",saving:"جاري الحفظ...",success:"تم بنجاح",error:"حدث خطأ"},en:{step:"Step",of:"of",next:"Next",previous:"Previous",save:"Save Property",required:"Required",optional:"Optional",basicInfo:"Basic Information",propertyDetails:"Property Details",locationInfo:"Location Information",additionalInfo:"Additional Information",title:"Property Title",description:"Property Description",price:"Price",currency:"Currency",propertyType:"Property Type",status:"Property Status",bedrooms:"Bedrooms",bathrooms:"Bathrooms",area:"Area (sqm)",yearBuilt:"Year Built",parking:"Parking Spaces",location:"Location",address:"Address",city:"City",country:"Country",images:"Property Images",features:"Property Features",amenities:"Amenities & Services",utilities:"Included Utilities",contactInfo:"Contact Information",furnished:"Furnished",petFriendly:"Pet Friendly",featured:"Featured Property",active:"Active",addFeature:"Add Feature",addAmenity:"Add Amenity",uploadImages:"Upload Property Images",dragDropImages:"Drag and drop images here, or click to select",titlePlaceholder:"Enter property title...",descriptionPlaceholder:"Write a detailed property description...",locationPlaceholder:"Enter property location...",addressPlaceholder:"Enter full address...",cityPlaceholder:"Enter city name...",featurePlaceholder:"Add new feature...",amenityPlaceholder:"Add new amenity...",utilitiesPlaceholder:"List included utilities...",contactPlaceholder:"Enter contact information...",stepDescription1:"Enter basic property information",stepDescription2:"Specify property details and specifications",stepDescription3:"Add location and address information",stepDescription4:"Add images and additional information",completed:"Completed",current:"Current",pending:"Pending",imageGallery:"Image Gallery",mainImage:"Main Image",additionalImages:"Additional Images",imageTips:"Image Tips",noFeatures:"No features added",noAmenities:"No amenities added",noImages:"No images uploaded yet",setAsMain:"Set as Main Image",removeImage:"Remove Image",saving:"Saving...",success:"Success",error:"Error"}}[k],O=[L.basicInfo,L.propertyDetails,L.locationInfo,L.additionalInfo],M=async a=>{if(a.preventDefault(),!C)try{await e(F),localStorage.removeItem("property-draft"),setTimeout(()=>{window.location.href="/dashboard/properties"},1500)}catch(e){console.error("Save failed:",e)}},q=async e=>{if(y&&j)try{let a=await fetch(`/api/v1/properties/${j}/images`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({images:e})});if(!a.ok){let e=await a.json();throw Error(e.message||"Failed to auto-save images")}let r=await a.json();console.log("Images auto-saved successfully:",r)}catch(e){throw console.error("Auto-save error:",e),e}},V=()=>{S.trim()&&(E(e=>({...e,features:[...e.features,S.trim()]})),R(""))},z=()=>{J.trim()&&(E(e=>({...e,amenities:[...e.amenities,J.trim()]})),U(""))},W=e=>{E(a=>({...a,features:a.features.filter((a,r)=>r!==e)}))},H=e=>{E(a=>({...a,amenities:a.amenities.filter((a,r)=>r!==e)}))},Z=e=>{switch(e){case 1:return F.title&&F.description&&F.price&&F.type;case 2:case 4:return!0;case 3:return F.location&&F.address&&F.city;default:return!1}};return(0,s.jsxs)("div",{className:`${"ar"===k?"rtl":"ltr"}`,dir:"ar"===k?"rtl":"ltr",children:[(0,s.jsxs)("div",{className:"mb-16",children:[(0,s.jsxs)("div",{className:"relative mb-8",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-emerald-100/50 to-teal-100/50 dark:from-emerald-900/20 dark:to-teal-900/20 rounded-2xl"}),(0,s.jsx)("div",{className:"relative p-6",children:(0,s.jsxs)("div",{className:`flex items-center justify-between ${"ar"===k?"flex-row-reverse":""}`,children:[(0,s.jsxs)("div",{className:`flex items-center gap-4 ${"ar"===k?"flex-row-reverse":""}`,children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"w-14 h-14 bg-gradient-to-br from-emerald-500 via-teal-500 to-cyan-600 rounded-2xl flex items-center justify-center shadow-xl shadow-emerald-200 dark:shadow-emerald-900/50",children:(0,s.jsx)("span",{className:"text-white font-black text-lg",children:w})}),(0,s.jsx)("div",{className:`absolute -top-1 ${"ar"===k?"-right-1":"-left-1"} w-6 h-6 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center animate-pulse`,children:(0,s.jsx)("span",{className:"text-white text-xs font-bold",children:"✦"})})]}),(0,s.jsxs)("div",{className:`space-y-1 ${"ar"===k?"text-right":"text-left"}`,children:[(0,s.jsxs)("div",{className:`flex items-center gap-2 ${"ar"===k?"flex-row-reverse":""}`,children:[(0,s.jsxs)("span",{className:"text-xl font-black text-slate-800 dark:text-white",children:[L.step," ",w," ",L.of," ",4]}),(0,s.jsx)("div",{className:"px-3 py-1 bg-emerald-100 dark:bg-emerald-900/30 rounded-full",children:(0,s.jsx)("span",{className:"text-sm font-bold text-emerald-700 dark:text-emerald-300",children:L.current})})]}),(0,s.jsx)("div",{className:"text-lg font-bold text-slate-700 dark:text-slate-300",children:O[w-1]}),(0,s.jsxs)("div",{className:"text-sm text-slate-600 dark:text-slate-400",children:[1===w&&L.stepDescription1,2===w&&L.stepDescription2,3===w&&L.stepDescription3,4===w&&L.stepDescription4]})]})]}),(0,s.jsxs)("div",{className:`${"ar"===k?"text-right":"text-left"}`,children:[(0,s.jsxs)("div",{className:"text-4xl font-black bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent",children:[Math.round(w/4*100),"%"]}),(0,s.jsx)("div",{className:"text-sm font-bold text-emerald-600 dark:text-emerald-400",children:L.completed})]})]})})]}),(0,s.jsx)("div",{className:"relative mb-8",children:(0,s.jsx)("div",{className:"w-full bg-slate-200 dark:bg-slate-700 rounded-full h-4 shadow-inner",children:(0,s.jsxs)("div",{className:"bg-gradient-to-r from-emerald-500 via-teal-500 to-cyan-500 h-4 rounded-full transition-all duration-1000 ease-out shadow-lg relative overflow-hidden",style:{width:`${w/4*100}%`},children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-pulse"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-emerald-400/50 to-teal-400/50 animate-pulse delay-300"})]})})}),(0,s.jsx)("div",{className:"grid grid-cols-4 gap-4",children:O.map((e,a)=>(0,s.jsxs)("div",{className:"flex flex-col items-center",children:[(0,s.jsx)("div",{className:`w-16 h-16 rounded-2xl flex items-center justify-center text-lg font-black transition-all duration-500 shadow-xl ${a+1<=w?"bg-gradient-to-br from-emerald-500 via-teal-500 to-cyan-600 text-white shadow-emerald-200 dark:shadow-emerald-900/50 scale-110":a+1===w+1?"bg-gradient-to-br from-slate-300 to-slate-400 text-slate-700 shadow-slate-200 dark:shadow-slate-800 scale-105":"bg-slate-200 dark:bg-slate-700 text-slate-500 dark:text-slate-400"}`,children:a+1<w?(0,s.jsx)(l.A,{className:"h-7 w-7"}):a+1===w?(0,s.jsx)("div",{className:"w-3 h-3 bg-white rounded-full animate-pulse"}):(0,s.jsx)("span",{className:"font-black",children:a+1})}),(0,s.jsxs)("div",{className:"mt-4 text-center",children:[(0,s.jsx)("div",{className:`text-sm font-bold leading-tight ${a+1<=w?"text-emerald-700 dark:text-emerald-300":a+1===w+1?"text-slate-600 dark:text-slate-400":"text-slate-500 dark:text-slate-500"}`,children:e}),(0,s.jsx)("div",{className:`text-xs mt-1 ${a+1<=w?"text-emerald-600 dark:text-emerald-400":"text-slate-400 dark:text-slate-500"}`,children:a+1<=w?L.completed:L.pending})]})]},a))})]}),(0,s.jsxs)("form",{onSubmit:M,className:"space-y-8",children:[1===w&&(0,s.jsxs)(b.Zp,{className:"border border-slate-700 bg-slate-800",children:[(0,s.jsx)(b.aR,{className:"pb-6 border-b border-slate-700",children:(0,s.jsxs)(b.ZB,{className:"text-xl font-bold text-white flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-emerald-600 text-white rounded-lg flex items-center justify-center text-lg font-bold",children:"1"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-xl",children:L.basicInfo}),(0,s.jsx)("div",{className:"text-sm font-normal text-slate-400 mt-1",children:"ar"===k?"المعلومات الأساسية":"Basic Information"})]})]})}),(0,s.jsxs)(b.Wu,{className:"space-y-8 p-8",children:[(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)(g.J,{htmlFor:"title",className:"text-sm font-medium text-white",children:[L.title," ",(0,s.jsx)("span",{className:"text-red-400",children:"*"})]}),(0,s.jsx)(x.p,{id:"title",value:F.title,onChange:e=>E(a=>({...a,title:e.target.value})),placeholder:L.titlePlaceholder,required:!0,dir:"ar"===k?"rtl":"ltr",className:"h-12 border border-slate-600 focus:border-emerald-500 bg-slate-700 text-white placeholder:text-slate-400 rounded-lg"})]}),"en"===k&&(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)(g.J,{htmlFor:"titleEn",className:"text-base font-semibold text-slate-700 dark:text-slate-300 flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-6 h-6 bg-slate-100 dark:bg-slate-700 rounded-lg flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-slate-600 dark:text-slate-400 text-sm",children:"EN"})}),L.titleEn,(0,s.jsxs)("span",{className:"text-slate-400 text-sm",children:["(",L.optional,")"]})]}),(0,s.jsx)(x.p,{id:"titleEn",value:F.titleAr,onChange:e=>E(a=>({...a,titleAr:e.target.value})),placeholder:L.titleEnPlaceholder,dir:"ltr",className:"h-12 border-2 border-slate-200 dark:border-slate-600 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-300 rounded-xl bg-white/30 dark:bg-slate-800/30"})]})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)(g.J,{htmlFor:"description",className:"text-base font-bold text-slate-800 dark:text-slate-200 flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-6 h-6 bg-emerald-100 dark:bg-emerald-900/30 rounded-lg flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-emerald-600 dark:text-emerald-400 text-sm font-bold",children:"2"})}),L.description,(0,s.jsx)("span",{className:"text-red-500 text-lg",children:"*"})]}),(0,s.jsx)(u.T,{id:"description",value:F.description,onChange:e=>E(a=>({...a,description:e.target.value})),placeholder:L.descriptionPlaceholder,required:!0,rows:6,dir:"rtl",className:"border-2 border-slate-200 dark:border-slate-600 focus:border-emerald-500 dark:focus:border-emerald-400 transition-all duration-300 resize-none rounded-xl text-lg bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm shadow-sm hover:shadow-md focus:shadow-lg"})]})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)(g.J,{htmlFor:"price",className:"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2",children:[L.price,(0,s.jsx)("span",{className:"text-red-500 text-lg",children:"*"})]}),(0,s.jsx)(x.p,{id:"price",type:"number",value:F.price,onChange:e=>E(a=>({...a,price:e.target.value})),placeholder:"0",required:!0,className:"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-200 rounded-lg"})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(g.J,{htmlFor:"currency",className:"text-sm font-semibold text-gray-700 dark:text-gray-300",children:L.currency}),(0,s.jsxs)(h.l6,{value:F.currency,onValueChange:e=>E(a=>({...a,currency:e})),children:[(0,s.jsx)(h.bq,{className:"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 rounded-lg",children:(0,s.jsx)(h.yv,{})}),(0,s.jsxs)(h.gC,{children:[(0,s.jsx)(h.eb,{value:"SAR",children:"SAR - ريال سعودي"}),(0,s.jsx)(h.eb,{value:"AED",children:"AED - درهم إماراتي"}),(0,s.jsx)(h.eb,{value:"USD",children:"USD - دولار أمريكي"}),(0,s.jsx)(h.eb,{value:"EUR",children:"EUR - يورو"}),(0,s.jsx)(h.eb,{value:"GBP",children:"GBP - جنيه إسترليني"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)(g.J,{htmlFor:"type",className:"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2",children:[L.propertyType,(0,s.jsx)("span",{className:"text-red-500 text-lg",children:"*"})]}),(0,s.jsxs)(h.l6,{value:F.type,onValueChange:e=>E(a=>({...a,type:e})),children:[(0,s.jsx)(h.bq,{className:"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 rounded-lg",children:(0,s.jsx)(h.yv,{})}),(0,s.jsx)(h.gC,{children:Object.entries({ar:{APARTMENT:"شقة سكنية",VILLA:"فيلا",TOWNHOUSE:"تاون هاوس",PENTHOUSE:"بنتهاوس",STUDIO:"استوديو",OFFICE:"مكتب تجاري",SHOP:"محل تجاري",WAREHOUSE:"مستودع",LAND:"قطعة أرض",BUILDING:"مبنى كامل"},en:{APARTMENT:"Apartment",VILLA:"Villa",TOWNHOUSE:"Townhouse",PENTHOUSE:"Penthouse",STUDIO:"Studio",OFFICE:"Office",SHOP:"Shop",WAREHOUSE:"Warehouse",LAND:"Land",BUILDING:"Building"}}[k]).map(([e,a])=>(0,s.jsx)(h.eb,{value:e,children:a},e))})]})]})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(g.J,{htmlFor:"status",className:"text-sm font-semibold text-gray-700 dark:text-gray-300",children:L.status}),(0,s.jsxs)(h.l6,{value:F.status,onValueChange:e=>E(a=>({...a,status:e})),children:[(0,s.jsx)(h.bq,{className:"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 rounded-lg",children:(0,s.jsx)(h.yv,{})}),(0,s.jsx)(h.gC,{children:Object.entries({ar:{AVAILABLE:"متاح للبيع",SOLD:"تم البيع",RENTED:"مؤجر",RESERVED:"محجوز",OFF_MARKET:"غير متاح"},en:{AVAILABLE:"Available",SOLD:"Sold",RENTED:"Rented",RESERVED:"Reserved",OFF_MARKET:"Off Market"}}[k]).map(([e,a])=>(0,s.jsx)(h.eb,{value:e,children:a},e))})]})]})})]})]}),2===w&&(0,s.jsxs)(b.Zp,{className:"shadow-xl border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm",children:[(0,s.jsx)(b.aR,{className:"pb-6 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-t-lg",children:(0,s.jsxs)(b.ZB,{className:"text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg",children:"2"}),L.propertyDetails]})}),(0,s.jsxs)(b.Wu,{className:"space-y-8 p-8",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6",children:[(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(g.J,{htmlFor:"bedrooms",className:"text-sm font-semibold text-gray-700 dark:text-gray-300",children:L.bedrooms}),(0,s.jsx)(x.p,{id:"bedrooms",type:"number",value:F.bedrooms,onChange:e=>E(a=>({...a,bedrooms:e.target.value})),placeholder:"0",min:"0",className:"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg"})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(g.J,{htmlFor:"bathrooms",className:"text-sm font-semibold text-gray-700 dark:text-gray-300",children:L.bathrooms}),(0,s.jsx)(x.p,{id:"bathrooms",type:"number",value:F.bathrooms,onChange:e=>E(a=>({...a,bathrooms:e.target.value})),placeholder:"0",min:"0",className:"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg"})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(g.J,{htmlFor:"area",className:"text-sm font-semibold text-gray-700 dark:text-gray-300",children:L.area}),(0,s.jsx)(x.p,{id:"area",type:"number",value:F.area,onChange:e=>E(a=>({...a,area:e.target.value})),placeholder:"0",min:"0",className:"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg"})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(g.J,{htmlFor:"yearBuilt",className:"text-sm font-semibold text-gray-700 dark:text-gray-300",children:L.yearBuilt}),(0,s.jsx)(x.p,{id:"yearBuilt",type:"number",value:F.yearBuilt,onChange:e=>E(a=>({...a,yearBuilt:e.target.value})),placeholder:"2024",min:"1900",max:"2030",className:"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg"})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(g.J,{htmlFor:"parking",className:"text-sm font-semibold text-gray-700 dark:text-gray-300",children:L.parking}),(0,s.jsx)(x.p,{id:"parking",type:"number",value:F.parking,onChange:e=>E(a=>({...a,parking:e.target.value})),placeholder:"0",min:"0",className:"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg"})]})]}),(0,s.jsx)(v,{className:"my-8"}),(0,s.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(g.J,{className:"text-sm font-semibold text-gray-700 dark:text-gray-300",children:L.features}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(x.p,{value:S,onChange:e=>R(e.target.value),placeholder:L.featurePlaceholder,onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),V()),className:"h-10 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg"}),(0,s.jsx)(m.$,{type:"button",onClick:V,size:"sm",className:"bg-green-600 hover:bg-green-700 px-4",children:(0,s.jsx)(d.A,{className:"h-4 w-4"})})]}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-2 min-h-[40px] p-3 border-2 border-dashed border-gray-200 dark:border-gray-600 rounded-lg",children:[F.features.map((e,a)=>(0,s.jsxs)(f.E,{variant:"secondary",className:"flex items-center gap-1 px-3 py-1 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",children:[e,(0,s.jsx)(i.A,{className:"h-3 w-3 cursor-pointer hover:text-red-600",onClick:()=>W(a)})]},a)),0===F.features.length&&(0,s.jsx)("span",{className:"text-gray-400 text-sm",children:L.noFeatures})]})]})}),(0,s.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(g.J,{className:"text-sm font-semibold text-gray-700 dark:text-gray-300",children:L.amenities}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(x.p,{value:J,onChange:e=>U(e.target.value),placeholder:L.amenityPlaceholder,onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),z()),className:"h-10 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg"}),(0,s.jsx)(m.$,{type:"button",onClick:z,size:"sm",className:"bg-green-600 hover:bg-green-700 px-4",children:(0,s.jsx)(d.A,{className:"h-4 w-4"})})]}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-2 min-h-[40px] p-3 border-2 border-dashed border-gray-200 dark:border-gray-600 rounded-lg",children:[F.amenities.map((e,a)=>(0,s.jsxs)(f.E,{variant:"secondary",className:"flex items-center gap-1 px-3 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",children:[e,(0,s.jsx)(i.A,{className:"h-3 w-3 cursor-pointer hover:text-red-600",onClick:()=>H(a)})]},a)),0===F.amenities.length&&(0,s.jsx)("span",{className:"text-gray-400 text-sm",children:L.noAmenities})]})]})})]})]}),3===w&&(0,s.jsxs)(b.Zp,{className:"shadow-xl border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm",children:[(0,s.jsx)(b.aR,{className:"pb-6 bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-t-lg",children:(0,s.jsxs)(b.ZB,{className:"text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg",children:"3"}),L.locationInfo]})}),(0,s.jsxs)(b.Wu,{className:"space-y-8 p-8",children:[(0,s.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)(g.J,{htmlFor:"location",className:"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2",children:[L.location,(0,s.jsx)("span",{className:"text-red-500 text-lg",children:"*"})]}),(0,s.jsx)(x.p,{id:"location",value:F.location,onChange:e=>E(a=>({...a,location:e.target.value})),placeholder:L.locationPlaceholder,required:!0,className:"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg"})]})}),(0,s.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)(g.J,{htmlFor:"address",className:"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2",children:[L.address,(0,s.jsx)("span",{className:"text-red-500 text-lg",children:"*"})]}),(0,s.jsx)(x.p,{id:"address",value:F.address,onChange:e=>E(a=>({...a,address:e.target.value})),placeholder:L.addressPlaceholder,required:!0,className:"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg"})]})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)(g.J,{htmlFor:"city",className:"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2",children:[L.city,(0,s.jsx)("span",{className:"text-red-500 text-lg",children:"*"})]}),(0,s.jsxs)(h.l6,{value:F.city,onValueChange:e=>E(a=>({...a,city:e})),children:[(0,s.jsx)(h.bq,{className:"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg",children:(0,s.jsx)(h.yv,{placeholder:"ar"===k?"اختر المدينة السعودية":"Select Saudi City"})}),(0,s.jsxs)(h.gC,{children:[(0,s.jsx)(h.eb,{value:"Riyadh",children:"Riyadh - الرياض"}),(0,s.jsx)(h.eb,{value:"Jeddah",children:"Jeddah - جدة"}),(0,s.jsx)(h.eb,{value:"Mecca",children:"Mecca - مكة المكرمة"}),(0,s.jsx)(h.eb,{value:"Medina",children:"Medina - المدينة المنورة"}),(0,s.jsx)(h.eb,{value:"Dammam",children:"Dammam - الدمام"}),(0,s.jsx)(h.eb,{value:"Khobar",children:"Khobar - الخبر"}),(0,s.jsx)(h.eb,{value:"Dhahran",children:"Dhahran - الظهران"}),(0,s.jsx)(h.eb,{value:"Taif",children:"Taif - الطائف"}),(0,s.jsx)(h.eb,{value:"Buraidah",children:"Buraidah - بريدة"}),(0,s.jsx)(h.eb,{value:"Tabuk",children:"Tabuk - تبوك"}),(0,s.jsx)(h.eb,{value:"Hail",children:"Hail - حائل"}),(0,s.jsx)(h.eb,{value:"Abha",children:"Abha - أبها"}),(0,s.jsx)(h.eb,{value:"Yanbu",children:"Yanbu - ينبع"}),(0,s.jsx)(h.eb,{value:"Jubail",children:"Jubail - الجبيل"}),(0,s.jsx)(h.eb,{value:"Najran",children:"Najran - نجران"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(g.J,{htmlFor:"country",className:"text-sm font-semibold text-gray-700 dark:text-gray-300",children:L.country}),(0,s.jsxs)(h.l6,{value:F.country,onValueChange:e=>E(a=>({...a,country:e})),children:[(0,s.jsx)(h.bq,{className:"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 rounded-lg",children:(0,s.jsx)(h.yv,{})}),(0,s.jsxs)(h.gC,{children:[(0,s.jsx)(h.eb,{value:"Saudi Arabia",children:"Saudi Arabia - المملكة العربية السعودية"}),(0,s.jsx)(h.eb,{value:"UAE",children:"UAE - الإمارات العربية المتحدة"}),(0,s.jsx)(h.eb,{value:"Qatar",children:"Qatar - قطر"}),(0,s.jsx)(h.eb,{value:"Kuwait",children:"Kuwait - الكويت"}),(0,s.jsx)(h.eb,{value:"Bahrain",children:"Bahrain - البحرين"}),(0,s.jsx)(h.eb,{value:"Oman",children:"Oman - عُمان"}),(0,s.jsx)(h.eb,{value:"Jordan",children:"Jordan - الأردن"}),(0,s.jsx)(h.eb,{value:"Egypt",children:"Egypt - مصر"})]})]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)(g.J,{htmlFor:"cityAr",className:"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2",children:[L.cityAr,(0,s.jsxs)("span",{className:"text-gray-400 text-sm",children:["(",L.optional,")"]})]}),(0,s.jsx)(x.p,{id:"cityAr",value:F.cityAr,onChange:e=>E(a=>({...a,cityAr:e.target.value})),placeholder:L.cityArPlaceholder,dir:"rtl",className:"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg"})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(g.J,{htmlFor:"countryAr",className:"text-sm font-semibold text-gray-700 dark:text-gray-300",children:L.countryAr}),(0,s.jsx)(x.p,{id:"countryAr",value:F.countryAr,onChange:e=>E(a=>({...a,countryAr:e.target.value})),placeholder:"أدخل اسم البلد بالعربية",dir:"rtl",className:"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg"})]})]})]})]})]}),4===w&&(0,s.jsxs)(b.Zp,{className:"shadow-xl border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm",children:[(0,s.jsx)(b.aR,{className:"pb-6 bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-900/20 dark:to-amber-900/20 rounded-t-lg",children:(0,s.jsxs)(b.ZB,{className:"text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg",children:"4"}),L.additionalInfo]})}),(0,s.jsxs)(b.Wu,{className:"space-y-8 p-8",children:[(0,s.jsx)(P,{images:F.images,onImagesChange:e=>E(a=>({...a,images:e})),onAutoSave:y?q:void 0,onUploadStatusChange:I,propertyId:j,maxImages:10,disabled:a}),(0,s.jsx)(v,{className:"my-8"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(g.J,{htmlFor:"utilities",className:"text-sm font-semibold text-gray-700 dark:text-gray-300",children:L.utilities}),(0,s.jsx)(u.T,{id:"utilities",value:F.utilities,onChange:e=>E(a=>({...a,utilities:e.target.value})),placeholder:L.utilitiesPlaceholder,rows:3,className:"border-2 border-gray-200 dark:border-gray-600 focus:border-orange-500 dark:focus:border-orange-400 transition-all duration-200 resize-none rounded-lg"})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(g.J,{htmlFor:"utilitiesAr",className:"text-sm font-semibold text-gray-700 dark:text-gray-300",children:L.utilitiesAr}),(0,s.jsx)(u.T,{id:"utilitiesAr",value:F.utilitiesAr,onChange:e=>E(a=>({...a,utilitiesAr:e.target.value})),placeholder:L.utilitiesArPlaceholder,dir:"rtl",rows:3,className:"border-2 border-gray-200 dark:border-gray-600 focus:border-orange-500 dark:focus:border-orange-400 transition-all duration-200 resize-none rounded-lg"})]})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(g.J,{htmlFor:"contactInfo",className:"text-sm font-semibold text-gray-700 dark:text-gray-300",children:L.contactInfo}),(0,s.jsx)(u.T,{id:"contactInfo",value:F.contactInfo,onChange:e=>E(a=>({...a,contactInfo:e.target.value})),placeholder:L.contactPlaceholder,rows:3,className:"border-2 border-gray-200 dark:border-gray-600 focus:border-orange-500 dark:focus:border-orange-400 transition-all duration-200 resize-none rounded-lg"})]}),(0,s.jsx)(v,{className:"my-8"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-8",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3 p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-orange-400 transition-colors",children:[(0,s.jsx)(p.d,{id:"furnished",checked:F.furnished,onCheckedChange:e=>E(a=>({...a,furnished:e})),className:"data-[state=checked]:bg-orange-600"}),(0,s.jsx)(g.J,{htmlFor:"furnished",className:"text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer",children:L.furnished})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3 p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-orange-400 transition-colors",children:[(0,s.jsx)(p.d,{id:"petFriendly",checked:F.petFriendly,onCheckedChange:e=>E(a=>({...a,petFriendly:e})),className:"data-[state=checked]:bg-orange-600"}),(0,s.jsx)(g.J,{htmlFor:"petFriendly",className:"text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer",children:L.petFriendly})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3 p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-orange-400 transition-colors",children:[(0,s.jsx)(p.d,{id:"isFeatured",checked:F.isFeatured,onCheckedChange:e=>E(a=>({...a,isFeatured:e})),className:"data-[state=checked]:bg-orange-600"}),(0,s.jsx)(g.J,{htmlFor:"isFeatured",className:"text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer",children:L.featured})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3 p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-orange-400 transition-colors",children:[(0,s.jsx)(p.d,{id:"isActive",checked:F.isActive,onCheckedChange:e=>E(a=>({...a,isActive:e})),className:"data-[state=checked]:bg-orange-600"}),(0,s.jsx)(g.J,{htmlFor:"isActive",className:"text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer",children:L.active})]})]})]})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center pt-8",children:[(0,s.jsxs)(m.$,{type:"button",variant:"outline",onClick:()=>{w>1&&A(w-1)},disabled:1===w,className:"flex items-center gap-2 px-6 py-3 h-12 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors",children:[(0,s.jsx)(o.A,{className:"h-4 w-4"}),L.previous]}),(0,s.jsx)("div",{className:"flex gap-4",children:w<4?(0,s.jsxs)(m.$,{type:"button",onClick:()=>{w<4&&A(w+1)},disabled:!Z(w),className:"flex items-center gap-2 px-8 py-3 h-12 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl",children:[L.next,(0,s.jsx)(n.A,{className:"h-4 w-4"})]}):(0,s.jsxs)(m.$,{type:"submit",disabled:a||C||!Z(w),className:"flex items-center gap-2 px-8 py-3 h-12 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl",children:[a||C?(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}):(0,s.jsx)(c.A,{className:"h-4 w-4"}),C?"ar"===k?"جاري رفع الصور...":"Uploading images...":L.save]})})]})]})]})}},10452:(e,a,r)=>{r.d(a,{Y:()=>t});var s=r(43210);function t(){let[e,a]=(0,s.useState)("ar");return{language:e,setLanguage:e=>{a(e)},isRTL:"ar"===e,isArabic:"ar"===e,isEnglish:"en"===e}}r(39266)},15616:(e,a,r)=>{r.d(a,{T:()=>d});var s=r(60687),t=r(43210),l=r(96241);let d=t.forwardRef(({className:e,...a},r)=>(0,s.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:r,...a}));d.displayName="Textarea"},39266:(e,a,r)=>{function s(){}function t(e,...a){}r.d(a,{Ei:()=>t,ss:()=>s})},39390:(e,a,r)=>{r.d(a,{J:()=>n});var s=r(60687),t=r(43210),l=r(78148),d=r(24224),i=r(96241);let o=(0,d.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),n=t.forwardRef(({className:e,...a},r)=>(0,s.jsx)(l.b,{ref:r,className:(0,i.cn)(o(),e),...a}));n.displayName=l.b.displayName},42902:(e,a,r)=>{r.d(a,{d:()=>i});var s=r(60687),t=r(43210),l=r(90270),d=r(96241);let i=t.forwardRef(({className:e,...a},r)=>(0,s.jsx)(l.bL,{className:(0,d.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...a,ref:r,children:(0,s.jsx)(l.zi,{className:(0,d.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));i.displayName=l.bL.displayName},55192:(e,a,r)=>{r.d(a,{BT:()=>n,Wu:()=>c,ZB:()=>o,Zp:()=>d,aR:()=>i,wL:()=>m});var s=r(60687),t=r(43210),l=r(96241);let d=t.forwardRef(({className:e,...a},r)=>(0,s.jsx)("div",{ref:r,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...a}));d.displayName="Card";let i=t.forwardRef(({className:e,...a},r)=>(0,s.jsx)("div",{ref:r,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",e),...a}));i.displayName="CardHeader";let o=t.forwardRef(({className:e,...a},r)=>(0,s.jsx)("div",{ref:r,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",e),...a}));o.displayName="CardTitle";let n=t.forwardRef(({className:e,...a},r)=>(0,s.jsx)("div",{ref:r,className:(0,l.cn)("text-sm text-muted-foreground",e),...a}));n.displayName="CardDescription";let c=t.forwardRef(({className:e,...a},r)=>(0,s.jsx)("div",{ref:r,className:(0,l.cn)("p-6 pt-0",e),...a}));c.displayName="CardContent";let m=t.forwardRef(({className:e,...a},r)=>(0,s.jsx)("div",{ref:r,className:(0,l.cn)("flex items-center p-6 pt-0",e),...a}));m.displayName="CardFooter"},63974:(e,a,r)=>{r.d(a,{bq:()=>x,eb:()=>p,gC:()=>h,l6:()=>c,yv:()=>m});var s=r(60687),t=r(43210),l=r(22670),d=r(78272),i=r(3589),o=r(13964),n=r(96241);let c=l.bL;l.YJ;let m=l.WT,x=t.forwardRef(({className:e,children:a,...r},t)=>(0,s.jsxs)(l.l9,{ref:t,className:(0,n.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...r,children:[a,(0,s.jsx)(l.In,{asChild:!0,children:(0,s.jsx)(d.A,{className:"h-4 w-4 opacity-50"})})]}));x.displayName=l.l9.displayName;let g=t.forwardRef(({className:e,...a},r)=>(0,s.jsx)(l.PP,{ref:r,className:(0,n.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:(0,s.jsx)(i.A,{className:"h-4 w-4"})}));g.displayName=l.PP.displayName;let u=t.forwardRef(({className:e,...a},r)=>(0,s.jsx)(l.wn,{ref:r,className:(0,n.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:(0,s.jsx)(d.A,{className:"h-4 w-4"})}));u.displayName=l.wn.displayName;let h=t.forwardRef(({className:e,children:a,position:r="popper",...t},d)=>(0,s.jsx)(l.ZL,{children:(0,s.jsxs)(l.UC,{ref:d,className:(0,n.cn)("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...t,children:[(0,s.jsx)(g,{}),(0,s.jsx)(l.LM,{className:(0,n.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),(0,s.jsx)(u,{})]})}));h.displayName=l.UC.displayName,t.forwardRef(({className:e,...a},r)=>(0,s.jsx)(l.JU,{ref:r,className:(0,n.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...a})).displayName=l.JU.displayName;let p=t.forwardRef(({className:e,children:a,...r},t)=>(0,s.jsxs)(l.q7,{ref:t,className:(0,n.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...r,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(l.VF,{children:(0,s.jsx)(o.A,{className:"h-4 w-4"})})}),(0,s.jsx)(l.p4,{children:a})]}));p.displayName=l.q7.displayName,t.forwardRef(({className:e,...a},r)=>(0,s.jsx)(l.wv,{ref:r,className:(0,n.cn)("-mx-1 my-1 h-px bg-muted",e),...a})).displayName=l.wv.displayName},68988:(e,a,r)=>{r.d(a,{p:()=>d});var s=r(60687),t=r(43210),l=r(96241);let d=t.forwardRef(({className:e,type:a,...r},t)=>(0,s.jsx)("input",{type:a,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:t,...r}));d.displayName="Input"}};