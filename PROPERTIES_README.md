# 🏠 Properties Management System

A complete **bilingual Properties management system** with **UploadThing integration** built following **SOLID principles**.

## ✨ Features

- 🌐 **Bilingual Support** (Arabic/English)
- 📸 **Image Upload** with UploadThing
- 🔍 **Advanced Search & Filtering**
- 📊 **Statistics Dashboard**
- 🎨 **Clean UI/UX Design**
- 🌙 **Dark Mode Support**
- 📱 **Responsive Design**
- 🔒 **Type Safety** with TypeScript
- 🏗️ **SOLID Architecture**

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- PostgreSQL database
- UploadThing account (optional for file uploads)

### Installation

1. **Install all dependencies:**
```bash
npm run install:all
```

2. **Set up environment variables:**

**Backend (.env):**
```env
DATABASE_URL="postgresql://username:password@localhost:5432/boot_db"
PORT=5000
```

**Frontend (.env.local):**
```env
UPLOADTHING_SECRET=your_uploadthing_secret
UPLOADTHING_APP_ID=your_uploadthing_app_id
```

3. **Run database migrations:**
```bash
cd backend
npx prisma db push
npx prisma generate
```

4. **Start both servers:**
```bash
npm run dev
```

This will start:
- Backend API on `http://localhost:5000`
- Frontend on `http://localhost:3000`

## 📁 Project Structure

```
├── backend/
│   ├── src/
│   │   ├── services/PropertyService.ts
│   │   ├── routes/properties.ts
│   │   ├── routes/uploadthing.ts
│   │   └── app.ts
│   └── prisma/schema.prisma
├── dashboard/
│   ├── app/dashboard/properties/
│   ├── components/properties/
│   ├── lib/uploadthing.ts
│   └── app/api/uploadthing/
└── package.json
```

## 🔧 API Endpoints

### Properties
- `GET /api/v1/properties` - List properties with filters
- `GET /api/v1/properties/:id` - Get single property
- `POST /api/v1/properties` - Create property
- `PUT /api/v1/properties/:id` - Update property
- `DELETE /api/v1/properties/:id` - Delete property
- `GET /api/v1/properties/stats` - Get statistics
- `GET /api/v1/properties/featured` - Get featured properties

### File Upload
- `POST /api/uploadthing` - Upload property images

## 🎯 Usage

### Creating a Property

1. Navigate to `/dashboard/properties`
2. Click "Add Property" button
3. Fill in the bilingual form:
   - Basic info (title, description in EN/AR)
   - Price and property type
   - Location details (EN/AR)
   - Upload images via drag & drop
   - Add features and amenities (EN/AR)
   - Set property status and options

### Managing Properties

- **View**: Browse properties in grid or list view
- **Search**: Use the search bar to find properties
- **Filter**: Filter by type, status, city, etc.
- **Edit**: Click edit button on any property
- **Delete**: Remove properties with confirmation
- **Statistics**: View analytics on the dashboard

### Bilingual Content

The system automatically displays content based on the selected language:
- **English**: Default content fields
- **Arabic**: RTL support with Arabic content fields
- **Dynamic**: Language switching without page reload

## 🔧 Configuration

### UploadThing Setup

1. Create account at [uploadthing.com](https://uploadthing.com)
2. Get your API keys
3. Add to environment variables
4. Configure file types and limits in `uploadRouter`

### Database Schema

The Property model includes:
- Bilingual text fields (title, description, location, etc.)
- Property details (bedrooms, bathrooms, area, etc.)
- Image URLs array
- Features and amenities arrays (EN/AR)
- Status and type enums
- Relations to users and appointments

### Customization

- **Property Types**: Edit `PropertyType` enum in schema
- **Statuses**: Modify `PropertyStatus` enum
- **UI Theme**: Customize in Tailwind config
- **Languages**: Add more languages in i18n config

## 🐛 Troubleshooting

### Backend Not Starting
- Check PostgreSQL connection
- Verify environment variables
- Run `npx prisma generate`

### Frontend Errors
- Ensure backend is running on port 5000
- Check Next.js proxy configuration
- Verify UploadThing credentials

### File Upload Issues
- Check UploadThing API keys
- Verify file size limits
- Ensure proper CORS configuration

## 📝 Development Notes

### SOLID Principles Applied
- **S**: PropertyService handles single responsibility
- **O**: Extensible property types and statuses
- **L**: Interface-based service layer
- **I**: Separated concerns for API, service, and data
- **D**: Dependency injection with Prisma client

### Code Quality
- TypeScript for type safety
- ESLint and Prettier for code formatting
- Error handling and logging
- Input validation and sanitization
- Responsive design patterns

## 🎉 Success!

Your Properties system is now ready! The implementation includes:

✅ Complete CRUD operations  
✅ Bilingual UI (Arabic/English)  
✅ File upload with UploadThing  
✅ Advanced filtering and search  
✅ Statistics dashboard  
✅ Clean, responsive design  
✅ SOLID architecture  
✅ Type-safe implementation  

Navigate to `/dashboard/properties` to start managing properties! 🏠
