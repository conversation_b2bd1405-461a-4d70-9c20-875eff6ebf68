/**
 * @since 2.0.0
 */
import * as internal from "./internal/configProvider/pathPatch.js";
/**
 * @since 2.0.0
 * @category constructors
 */
export const empty = internal.empty;
/**
 * @since 2.0.0
 * @category constructors
 */
export const andThen = internal.andThen;
/**
 * @since 2.0.0
 * @category constructors
 */
export const mapName = internal.mapName;
/**
 * @since 2.0.0
 * @category constructors
 */
export const nested = internal.nested;
/**
 * @since 2.0.0
 * @category constructors
 */
export const unnested = internal.unnested;
//# sourceMappingURL=ConfigProviderPathPatch.js.map