'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Plus, Search, Filter, Grid, List, Eye, Edit, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useLanguage } from '@/hooks/useLanguage';
import PropertyFormDialog from '@/components/properties/property-form-dialog';

interface Property {
  id: string;
  title: string;
  titleAr?: string;
  description: string;
  descriptionAr?: string;
  price: number;
  currency: string;
  type: string;
  status: string;
  bedrooms?: number;
  bathrooms?: number;
  area?: number;
  location: string;
  locationAr?: string;
  address: string;
  addressAr?: string;
  city: string;
  cityAr?: string;
  images: string[];
  features: string[];
  featuresAr: string[];
  amenities: string[];
  amenitiesAr: string[];
  isFeatured: boolean;
  isActive: boolean;
  viewCount: number;
  createdAt: string;
  agent?: {
    id: string;
    name: string;
    email: string;
  };
}

interface PropertyStats {
  total: number;
  available: number;
  sold: number;
  rented: number;
  featured: number;
  byType: Record<string, number>;
}

export default function PropertiesPage() {
  const router = useRouter();
  const { language, t } = useLanguage();
  const [properties, setProperties] = useState<Property[]>([]);
  const [stats, setStats] = useState<PropertyStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('ALL');
  const [filterStatus, setFilterStatus] = useState('ALL');
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [selectedProperty, setSelectedProperty] = useState<Property | null>(null);
  const [showEditDialog, setShowEditDialog] = useState(false);

  // Fetch properties and stats
  useEffect(() => {
    fetchProperties();
    fetchStats();
  }, [searchTerm, filterType, filterStatus]);

  const fetchProperties = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (searchTerm) params.append('search', searchTerm);
      if (filterType && filterType !== 'ALL') params.append('type', filterType);
      if (filterStatus && filterStatus !== 'ALL') params.append('status', filterStatus);
      params.append('isActive', 'true');

      const response = await fetch(`/api/v1/properties?${params}`);

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setProperties(data.data.properties);
        }
      } else {
        // Fallback to mock data if backend is not available
        console.log('Backend not available, using mock data');
        setProperties([
          {
            id: '1',
            title: 'Luxury Villa in Dubai Marina',
            titleAr: 'فيلا فاخرة في دبي مارينا',
            description: 'Beautiful 4-bedroom villa with sea view',
            descriptionAr: 'فيلا جميلة من 4 غرف نوم مع إطلالة على البحر',
            price: 2500000,
            currency: 'AED',
            type: 'VILLA',
            status: 'AVAILABLE',
            bedrooms: 4,
            bathrooms: 3,
            area: 350,
            location: 'Dubai Marina',
            locationAr: 'دبي مارينا',
            address: '123 Marina Walk',
            addressAr: '123 ممشى المارينا',
            city: 'Dubai',
            cityAr: 'دبي',
            images: ['/placeholder.jpg'],
            features: ['Swimming Pool', 'Gym', 'Parking'],
            featuresAr: ['مسبح', 'صالة رياضية', 'موقف سيارات'],
            amenities: ['24/7 Security', 'Concierge'],
            amenitiesAr: ['أمن 24/7', 'خدمة الكونسيرج'],
            isFeatured: true,
            isActive: true,
            viewCount: 125,
            createdAt: new Date().toISOString(),
          },
          {
            id: '2',
            title: 'Modern Apartment in Downtown',
            titleAr: 'شقة حديثة في وسط المدينة',
            description: 'Spacious 2-bedroom apartment',
            descriptionAr: 'شقة واسعة من غرفتي نوم',
            price: 1200000,
            currency: 'AED',
            type: 'APARTMENT',
            status: 'AVAILABLE',
            bedrooms: 2,
            bathrooms: 2,
            area: 120,
            location: 'Downtown Dubai',
            locationAr: 'وسط مدينة دبي',
            address: '456 Sheikh Zayed Road',
            addressAr: '456 شارع الشيخ زايد',
            city: 'Dubai',
            cityAr: 'دبي',
            images: ['/placeholder.jpg'],
            features: ['Balcony', 'Built-in Wardrobes'],
            featuresAr: ['شرفة', 'خزائن مدمجة'],
            amenities: ['Gym', 'Pool'],
            amenitiesAr: ['صالة رياضية', 'مسبح'],
            isFeatured: false,
            isActive: true,
            viewCount: 89,
            createdAt: new Date().toISOString(),
          }
        ]);
      }
    } catch (error) {
      console.error('Error fetching properties:', error);
      // Fallback to empty array on error
      setProperties([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/v1/properties/stats');

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setStats(data.data);
        }
      } else {
        // Fallback to mock stats if backend is not available
        console.log('Backend not available, using mock stats');
        setStats({
          total: 2,
          available: 2,
          sold: 0,
          rented: 0,
          featured: 1,
          byType: {
            APARTMENT: 1,
            VILLA: 1,
            TOWNHOUSE: 0,
            PENTHOUSE: 0,
            STUDIO: 0,
            OFFICE: 0,
            SHOP: 0,
            WAREHOUSE: 0,
            LAND: 0,
            BUILDING: 0,
          }
        });
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
      // Fallback to default stats on error
      setStats({
        total: 0,
        available: 0,
        sold: 0,
        rented: 0,
        featured: 0,
        byType: {
          APARTMENT: 0,
          VILLA: 0,
          TOWNHOUSE: 0,
          PENTHOUSE: 0,
          STUDIO: 0,
          OFFICE: 0,
          SHOP: 0,
          WAREHOUSE: 0,
          LAND: 0,
          BUILDING: 0,
        }
      });
    }
  };

  const handleDeleteProperty = async (id: string) => {
    if (!confirm(language === 'ar' ? 'هل أنت متأكد من حذف هذا العقار؟' : 'Are you sure you want to delete this property?')) {
      return;
    }

    try {
      const response = await fetch(`/api/v1/properties/${id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        fetchProperties();
        fetchStats();
      }
    } catch (error) {
      console.error('Error deleting property:', error);
    }
  };

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat(language === 'ar' ? 'ar-AE' : 'en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
    }).format(price);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'AVAILABLE': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'SOLD': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'RENTED': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'RESERVED': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getPropertyTitle = (property: Property) => {
    return language === 'ar' && property.titleAr ? property.titleAr : property.title;
  };

  const getPropertyLocation = (property: Property) => {
    return language === 'ar' && property.locationAr ? property.locationAr : property.location;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            {language === 'ar' ? 'العقارات' : 'Properties'}
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            {language === 'ar' ? 'إدارة العقارات والممتلكات' : 'Manage properties and real estate'}
          </p>
        </div>
        <Button
          onClick={() => router.push('/dashboard/properties/create')}
          className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2 shadow-lg hover:shadow-xl transition-all duration-200"
        >
          <Plus className="h-4 w-4" />
          {language === 'ar' ? 'إضافة عقار' : 'Add Property'}
        </Button>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {language === 'ar' ? 'إجمالي العقارات' : 'Total Properties'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {language === 'ar' ? 'متاح' : 'Available'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{stats.available}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {language === 'ar' ? 'مباع' : 'Sold'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{stats.sold}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {language === 'ar' ? 'مؤجر' : 'Rented'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{stats.rented}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {language === 'ar' ? 'مميز' : 'Featured'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">{stats.featured}</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder={language === 'ar' ? 'البحث في العقارات...' : 'Search properties...'}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={filterType} onValueChange={setFilterType}>
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue placeholder={language === 'ar' ? 'نوع العقار' : 'Property Type'} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="ALL">{language === 'ar' ? 'جميع الأنواع' : 'All Types'}</SelectItem>
            <SelectItem value="APARTMENT">{language === 'ar' ? 'شقة' : 'Apartment'}</SelectItem>
            <SelectItem value="VILLA">{language === 'ar' ? 'فيلا' : 'Villa'}</SelectItem>
            <SelectItem value="TOWNHOUSE">{language === 'ar' ? 'تاون هاوس' : 'Townhouse'}</SelectItem>
            <SelectItem value="OFFICE">{language === 'ar' ? 'مكتب' : 'Office'}</SelectItem>
          </SelectContent>
        </Select>
        <Select value={filterStatus} onValueChange={setFilterStatus}>
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue placeholder={language === 'ar' ? 'الحالة' : 'Status'} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="ALL">{language === 'ar' ? 'جميع الحالات' : 'All Statuses'}</SelectItem>
            <SelectItem value="AVAILABLE">{language === 'ar' ? 'متاح' : 'Available'}</SelectItem>
            <SelectItem value="SOLD">{language === 'ar' ? 'مباع' : 'Sold'}</SelectItem>
            <SelectItem value="RENTED">{language === 'ar' ? 'مؤجر' : 'Rented'}</SelectItem>
          </SelectContent>
        </Select>
        <div className="flex gap-2">
          <Button
            variant={viewMode === 'grid' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('grid')}
          >
            <Grid className="h-4 w-4" />
          </Button>
          <Button
            variant={viewMode === 'list' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('list')}
          >
            <List className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Properties Grid/List */}
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 dark:border-white"></div>
        </div>
      ) : (
        <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
          {properties.map((property) => (
            <Card key={property.id} className="overflow-hidden hover:shadow-lg transition-shadow">
              {viewMode === 'grid' ? (
                <>
                  {property.images.length > 0 && (
                    <div className="relative h-48 bg-gray-200 dark:bg-gray-700">
                      <img
                        src={property.images[0]}
                        alt={getPropertyTitle(property)}
                        className="w-full h-full object-cover"
                      />
                      {property.isFeatured && (
                        <Badge className="absolute top-2 left-2 bg-purple-600">
                          {language === 'ar' ? 'مميز' : 'Featured'}
                        </Badge>
                      )}
                    </div>
                  )}
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="font-semibold text-lg truncate">{getPropertyTitle(property)}</h3>
                      <Badge className={getStatusColor(property.status)}>
                        {property.status}
                      </Badge>
                    </div>
                    <p className="text-gray-600 dark:text-gray-400 text-sm mb-2">
                      {getPropertyLocation(property)}
                    </p>
                    <div className="flex justify-between items-center mb-3">
                      <span className="text-xl font-bold text-blue-600">
                        {formatPrice(property.price, property.currency)}
                      </span>
                      <div className="flex gap-2 text-sm text-gray-500">
                        {property.bedrooms && <span>{property.bedrooms} {language === 'ar' ? 'غرف' : 'bed'}</span>}
                        {property.bathrooms && <span>{property.bathrooms} {language === 'ar' ? 'حمام' : 'bath'}</span>}
                        {property.area && <span>{property.area}m²</span>}
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <div className="flex gap-1">
                        <Button size="sm" variant="outline" onClick={() => setSelectedProperty(property)}>
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => router.push(`/dashboard/properties/edit/${property.id}`)}
                          className="hover:bg-blue-50 hover:border-blue-300 transition-colors"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline" onClick={() => handleDeleteProperty(property.id)}>
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                      <span className="text-xs text-gray-500">
                        {language === 'ar' ? 'المشاهدات:' : 'Views:'} {property.viewCount}
                      </span>
                    </div>
                  </CardContent>
                </>
              ) : (
                <CardContent className="p-4">
                  <div className="flex gap-4">
                    {property.images.length > 0 && (
                      <div className="relative w-24 h-24 bg-gray-200 dark:bg-gray-700 rounded">
                        <img
                          src={property.images[0]}
                          alt={getPropertyTitle(property)}
                          className="w-full h-full object-cover rounded"
                        />
                      </div>
                    )}
                    <div className="flex-1">
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="font-semibold text-lg">{getPropertyTitle(property)}</h3>
                        <div className="flex gap-2">
                          <Badge className={getStatusColor(property.status)}>
                            {property.status}
                          </Badge>
                          {property.isFeatured && (
                            <Badge className="bg-purple-600">
                              {language === 'ar' ? 'مميز' : 'Featured'}
                            </Badge>
                          )}
                        </div>
                      </div>
                      <p className="text-gray-600 dark:text-gray-400 text-sm mb-2">
                        {getPropertyLocation(property)}
                      </p>
                      <div className="flex justify-between items-center">
                        <span className="text-xl font-bold text-blue-600">
                          {formatPrice(property.price, property.currency)}
                        </span>
                        <div className="flex gap-4 text-sm text-gray-500">
                          {property.bedrooms && <span>{property.bedrooms} {language === 'ar' ? 'غرف' : 'bed'}</span>}
                          {property.bathrooms && <span>{property.bathrooms} {language === 'ar' ? 'حمام' : 'bath'}</span>}
                          {property.area && <span>{property.area}m²</span>}
                          <span>{language === 'ar' ? 'المشاهدات:' : 'Views:'} {property.viewCount}</span>
                        </div>
                        <div className="flex gap-1">
                          <Button size="sm" variant="outline" onClick={() => setSelectedProperty(property)}>
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => router.push(`/dashboard/properties/edit/${property.id}`)}
                            className="hover:bg-blue-50 hover:border-blue-300 transition-colors"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button size="sm" variant="outline" onClick={() => handleDeleteProperty(property.id)}>
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              )}
            </Card>
          ))}
        </div>
      )}

      {/* Create Property Dialog */}
      <PropertyFormDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        onSuccess={() => {
          fetchProperties();
          fetchStats();
        }}
      />

      {/* Edit Property Dialog */}
      <PropertyFormDialog
        open={showEditDialog}
        onOpenChange={setShowEditDialog}
        property={selectedProperty}
        onSuccess={() => {
          fetchProperties();
          fetchStats();
        }}
      />
    </div>
  );
}
