(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1614],{7363:(r,e,t)=>{"use strict";t.r(e),t.d(e,{default:()=>h});var a=t(95155),i=t(12115),s=t(35695),o=t(51154),n=t(57340),d=t(13052),l=t(35169),c=t(97168),p=t(88482),u=t(86132),y=t(56671),m=t(17681);function h(){var r,e,t,h,g,x;let b=(0,s.useRouter)(),A=(0,s.useParams)(),{language:f}=(0,u.Y)(),[v,k]=(0,i.useState)(!1),[j,N]=(0,i.useState)(!0),[w,E]=(0,i.useState)(null),P={ar:{editProperty:"تعديل العقار",backToProperties:"العودة إلى العقارات",subtitle:"تحديث معلومات وتفاصيل العقار",properties:"العقارات",home:"الرئيسية",loading:"جاري تحميل العقار...",notFound:"العقار غير موجود",loadingError:"فشل في تحميل العقار"},en:{editProperty:"Edit Property",backToProperties:"Back to Properties",subtitle:"Update property information and details",properties:"Properties",home:"Home",loading:"Loading property...",notFound:"Property not found",loadingError:"Failed to load property"}},F=P[f]||P.ar;(0,i.useEffect)(()=>{(async()=>{if(A.id)try{N(!0);let r=await fetch("/api/v1/properties/".concat(A.id));if(r.ok){let e=await r.json();e.success?E(e.data):(y.oR.error(F.notFound),b.push("/dashboard/properties"))}else console.log("Backend not available, using mock data"),E({id:A.id,title:"Luxury Villa in Dubai Marina",titleAr:"فيلا فاخرة في دبي مارينا",description:"Beautiful 4-bedroom villa with sea view",descriptionAr:"فيلا جميلة من 4 غرف نوم مع إطلالة على البحر",price:25e5,currency:"AED",type:"VILLA",status:"AVAILABLE",bedrooms:4,bathrooms:3,area:350,location:"Dubai Marina",locationAr:"دبي مارينا",address:"123 Marina Walk",addressAr:"123 ممشى المارينا",city:"Dubai",cityAr:"دبي",country:"UAE",countryAr:"الإمارات العربية المتحدة",images:["/placeholder.jpg"],features:["Swimming Pool","Gym","Parking"],featuresAr:["مسبح","صالة رياضية","موقف سيارات"],amenities:["24/7 Security","Concierge"],amenitiesAr:["أمن 24/7","خدمة الكونسيرج"],yearBuilt:2020,parking:2,furnished:!0,petFriendly:!1,utilities:"Electricity, Water, Internet",utilitiesAr:"كهرباء، ماء، إنترنت",contactInfo:"Contact: +971 50 123 4567",isFeatured:!0,isActive:!0})}catch(r){console.error("Error fetching property:",r),y.oR.error(F.loadingError),b.push("/dashboard/properties")}finally{N(!1)}})()},[A.id,b,F.loadingError,F.notFound]);let S=async r=>{k(!0);try{let e={...r,price:parseFloat(r.price),bedrooms:r.bedrooms?parseInt(r.bedrooms):void 0,bathrooms:r.bathrooms?parseInt(r.bathrooms):void 0,area:r.area?parseFloat(r.area):void 0,yearBuilt:r.yearBuilt?parseInt(r.yearBuilt):void 0,parking:r.parking?parseInt(r.parking):void 0},t=await fetch("/api/v1/properties/".concat(A.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(t.ok)y.oR.success("ar"===f?"تم تحديث العقار بنجاح":"Property updated successfully"),b.push("/dashboard/properties");else{let r=await t.json();y.oR.error(r.message||("ar"===f?"فشل في تحديث العقار":"Failed to update property"))}}catch(r){console.error("Error updating property:",r),y.oR.error("ar"===f?"فشل في تحديث العقار":"Failed to update property")}finally{k(!1)}};if(j)return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 ".concat("ar"===f?"rtl":"ltr"),children:(0,a.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,a.jsx)(p.Zp,{className:"shadow-xl border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm",children:(0,a.jsx)(p.Wu,{className:"flex items-center justify-center py-16",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(o.A,{className:"h-8 w-8 animate-spin mx-auto mb-4 text-blue-600"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:F.loading})]})})})})});if(!w)return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 ".concat("ar"===f?"rtl":"ltr"),children:(0,a.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,a.jsx)(p.Zp,{className:"shadow-xl border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm",children:(0,a.jsx)(p.Wu,{className:"flex items-center justify-center py-16",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:F.notFound}),(0,a.jsx)(c.$,{onClick:()=>b.push("/dashboard/properties"),children:F.backToProperties})]})})})})});let I={title:w.title||"",titleAr:w.titleAr||"",description:w.description||"",descriptionAr:w.descriptionAr||"",price:(null===(r=w.price)||void 0===r?void 0:r.toString())||"",currency:w.currency||"AED",type:w.type||"APARTMENT",status:w.status||"AVAILABLE",bedrooms:(null===(e=w.bedrooms)||void 0===e?void 0:e.toString())||"",bathrooms:(null===(t=w.bathrooms)||void 0===t?void 0:t.toString())||"",area:(null===(h=w.area)||void 0===h?void 0:h.toString())||"",location:w.location||"",locationAr:w.locationAr||"",address:w.address||"",addressAr:w.addressAr||"",city:w.city||"",cityAr:w.cityAr||"",country:w.country||"UAE",countryAr:w.countryAr||"الإمارات العربية المتحدة",images:w.images||[],features:w.features||[],featuresAr:w.featuresAr||[],amenities:w.amenities||[],amenitiesAr:w.amenitiesAr||[],yearBuilt:(null===(g=w.yearBuilt)||void 0===g?void 0:g.toString())||"",parking:(null===(x=w.parking)||void 0===x?void 0:x.toString())||"",furnished:w.furnished||!1,petFriendly:w.petFriendly||!1,utilities:w.utilities||"",utilitiesAr:w.utilitiesAr||"",contactInfo:w.contactInfo||"",isFeatured:w.isFeatured||!1,isActive:void 0===w.isActive||w.isActive};return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 ".concat("ar"===f?"rtl":"ltr"),children:(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsxs)("nav",{className:"flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400 mb-8",dir:"ar"===f?"rtl":"ltr",children:[(0,a.jsx)(n.A,{className:"h-4 w-4"}),(0,a.jsx)(d.A,{className:"h-4 w-4"}),(0,a.jsx)("button",{onClick:()=>b.push("/dashboard/properties"),className:"hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:F.properties}),(0,a.jsx)(d.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-gray-900 dark:text-gray-100",children:F.editProperty})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-2",children:F.editProperty}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 max-w-2xl",children:F.subtitle})]}),(0,a.jsxs)(c.$,{variant:"outline",onClick:()=>b.push("/dashboard/properties"),className:"flex items-center gap-2 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors",children:[(0,a.jsx)(l.A,{className:"h-4 w-4"}),F.backToProperties]})]}),(0,a.jsx)(m.V,{onSave:S,loading:v,initialData:I,isEdit:!0,propertyId:A.id})]})})}},64155:(r,e,t)=>{Promise.resolve().then(t.bind(t,7363))}},r=>{var e=e=>r(r.s=e);r.O(0,[4277,6071,9509,9855,1721,2842,7681,8441,1684,7358],()=>e(64155)),_N_E=r.O()}]);