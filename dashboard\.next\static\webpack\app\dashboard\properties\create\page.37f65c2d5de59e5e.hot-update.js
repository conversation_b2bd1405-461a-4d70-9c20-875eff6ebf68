"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/properties/create/page",{

/***/ "(app-pages-browser)/./hooks/useTheme.tsx":
/*!****************************!*\
  !*** ./hooks/useTheme.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_cookieCleanup__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/cookieCleanup */ \"(app-pages-browser)/./lib/cookieCleanup.ts\");\n/* __next_internal_client_entry_do_not_use__ useTheme auto */ var _s = $RefreshSig$();\n\n\n/**\n * Dark mode theme hook for Properties system\n * Supports light and dark themes with persistence\n */ function useTheme() {\n    _s();\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('dark'); // Default to dark mode\n    // Initialize theme\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useTheme.useEffect\": ()=>{\n            // Check for saved theme preference, default to dark mode\n            const savedTheme = localStorage.getItem('properties-theme');\n            if (savedTheme === 'light' || savedTheme === 'dark') {\n                setTheme(savedTheme);\n            } else {\n                // Default to dark mode if no preference saved\n                setTheme('dark');\n                localStorage.setItem('properties-theme', 'dark');\n            }\n            // Set initial dark class\n            document.documentElement.classList.add('dark');\n            (0,_lib_cookieCleanup__WEBPACK_IMPORTED_MODULE_1__.safeLog)('🎨 Theme system initialized (default: dark)');\n        }\n    }[\"useTheme.useEffect\"], []);\n    // Update document theme when theme changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useTheme.useEffect\": ()=>{\n            // Save theme preference\n            localStorage.setItem('properties-theme', theme);\n            // Update document class\n            if (theme === 'dark') {\n                document.documentElement.classList.add('dark');\n            } else {\n                document.documentElement.classList.remove('dark');\n            }\n            (0,_lib_cookieCleanup__WEBPACK_IMPORTED_MODULE_1__.safeLog)(\"\\uD83C\\uDF19 Theme switched to: \".concat(theme));\n        }\n    }[\"useTheme.useEffect\"], [\n        theme\n    ]);\n    const toggleTheme = ()=>{\n        setTheme((prev)=>prev === 'light' ? 'dark' : 'light');\n    };\n    const setLightTheme = ()=>setTheme('light');\n    const setDarkTheme = ()=>setTheme('dark');\n    return {\n        theme,\n        setTheme,\n        toggleTheme,\n        setLightTheme,\n        setDarkTheme,\n        isDark: theme === 'dark',\n        isLight: theme === 'light'\n    };\n}\n_s(useTheme, \"FaR+67HYMnxyxOLL3EkvHKRjhfs=\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/useTheme.tsx\n"));

/***/ })

});