import { defaultErrorFormatter } from './error-formatter.js';

function internalCreateBuilder(initDef = {}) {
    const _def = {
        $types: {},
        // Default router config
        routerConfig: {
            image: {
                maxFileSize: "4MB"
            }
        },
        routeOptions: {
            awaitServerData: true
        },
        inputParser: {
            parseAsync: ()=>Promise.resolve(undefined),
            _input: undefined,
            _output: undefined
        },
        middleware: ()=>({}),
        onUploadError: ()=>{
        // noop
        },
        onUploadComplete: ()=>undefined,
        errorFormatter: initDef.errorFormatter ?? defaultErrorFormatter,
        // Overload with properties passed in
        ...initDef
    };
    return {
        input (userParser) {
            return internalCreateBuilder({
                ..._def,
                inputParser: userParser
            });
        },
        middleware (userMiddleware) {
            return internalCreateBuilder({
                ..._def,
                middleware: userMiddleware
            });
        },
        onUploadComplete (userUploadComplete) {
            return {
                ..._def,
                onUploadComplete: userUploadComplete
            };
        },
        onUploadError (userOnUploadError) {
            return internalCreateBuilder({
                ..._def,
                onUploadError: userOnUploadError
            });
        }
    };
}
/**
 * Create a builder for your backend adapter.
 * Refer to the existing adapters for examples on how to use this function.
 * @public
 *
 * @param opts - Options for the builder
 * @returns A file route builder for making UploadThing file routes
 */ function createBuilder(opts) {
    return (input, config)=>{
        return internalCreateBuilder({
            routerConfig: input,
            routeOptions: config ?? {},
            ...opts
        });
    };
}

export { createBuilder };
