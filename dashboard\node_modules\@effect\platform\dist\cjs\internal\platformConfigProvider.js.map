{"version": 3, "file": "platformConfigProvider.js", "names": ["FileSystem", "_interopRequireWildcard", "require", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Context", "DefaultServices", "Effect", "FiberRef", "Layer", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "fromDotEnv", "path", "config", "gen", "fs", "content", "readFileString", "obj", "parseDotEnv", "fromMap", "Map", "entries", "assign", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exports", "layerDotEnv", "pipe", "map", "setConfigProvider", "unwrapEffect", "layerDotEnvAdd", "dotEnvConfigProvider", "orElseSucceed", "logDebug", "empty", "currentConfigProvider", "currentServices", "services", "config<PERSON><PERSON><PERSON>", "orElse", "DOT_ENV_LINE", "lines", "replace", "match", "exec", "key", "value", "trim", "maybeQuote", "expand", "parsed", "newParsed", "config<PERSON><PERSON>", "interpolate", "envValue", "lastUnescapedDollarSignIndex", "searchLast", "rightMostGroup", "slice", "matchGroup", "_", "group", "variableName", "defaultValue", "str", "rgx", "matches", "Array", "from", "matchAll", "length", "index"], "sources": ["../../../src/internal/platformConfigProvider.ts"], "sourcesContent": [null], "mappings": ";;;;;;AACA,IAAAA,UAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,cAAA,GAAAF,uBAAA,CAAAC,OAAA;AACA,IAAAE,OAAA,GAAAH,uBAAA,CAAAC,OAAA;AACA,IAAAG,eAAA,GAAAJ,uBAAA,CAAAC,OAAA;AACA,IAAAI,MAAA,GAAAL,uBAAA,CAAAC,OAAA;AACA,IAAAK,QAAA,GAAAN,uBAAA,CAAAC,OAAA;AACA,IAAAM,KAAA,GAAAP,uBAAA,CAAAC,OAAA;AAAqC,SAAAO,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAT,wBAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAErC;;;;;;;AAQA;AACO,MAAMW,UAAU,GAAGA,CACxBC,IAAY,EACZC,MAA6D,KAE7DzB,MAAM,CAAC0B,GAAG,CAAC,aAAS;EAClB,MAAMC,EAAE,GAAG,OAAOjC,UAAU,CAACA,UAAU;EACvC,MAAMkC,OAAO,GAAG,OAAOD,EAAE,CAACE,cAAc,CAACL,IAAI,CAAC;EAC9C,MAAMM,GAAG,GAAGC,WAAW,CAACH,OAAO,CAAC;EAChC,OAAO/B,cAAc,CAACmC,OAAO,CAC3B,IAAIC,GAAG,CAAClB,MAAM,CAACmB,OAAO,CAACJ,GAAG,CAAC,CAAC,EAC5Bf,MAAM,CAACoB,MAAM,CAAC,EAAE,EAAE;IAAEC,SAAS,EAAE,GAAG;IAAEC,QAAQ,EAAE;EAAG,CAAE,EAAEZ,MAAM,CAAC,CAC7D;AACH,CAAC,CAAC;AAEJ;AAAAa,OAAA,CAAAf,UAAA,GAAAA,UAAA;AACO,MAAMgB,WAAW,GAAIf,IAAY,IACtCD,UAAU,CAACC,IAAI,CAAC,CAACgB,IAAI,CACnBxC,MAAM,CAACyC,GAAG,CAACvC,KAAK,CAACwC,iBAAiB,CAAC,EACnCxC,KAAK,CAACyC,YAAY,CACnB;AAEH;AAAAL,OAAA,CAAAC,WAAA,GAAAA,WAAA;AACO,MAAMK,cAAc,GAAIpB,IAAY,IACzCxB,MAAM,CAAC0B,GAAG,CAAC,aAAS;EAClB,MAAMmB,oBAAoB,GAAG,OAAO7C,MAAM,CAAC8C,aAAa,CAACvB,UAAU,CAACC,IAAI,CAAC,EAAE,MAAM,IAAI,CAAC;EAEtF,IAAIqB,oBAAoB,KAAK,IAAI,EAAE;IACjC,OAAO7C,MAAM,CAAC+C,QAAQ,CAAC,SAASvB,IAAI,8CAA8C,CAAC;IACnF,OAAOtB,KAAK,CAAC8C,KAAK;EACpB;EAEA,MAAMC,qBAAqB,GAAG,OAAOhD,QAAQ,CAACU,GAAG,CAACZ,eAAe,CAACmD,eAAe,CAAC,CAACV,IAAI,CACrFxC,MAAM,CAACyC,GAAG,CAAEU,QAAQ,IAAKrD,OAAO,CAACa,GAAG,CAACwC,QAAQ,EAAEtD,cAAc,CAACA,cAAc,CAAC,CAAC,CAC/E;EACD,MAAMuD,cAAc,GAAGvD,cAAc,CAACwD,MAAM,CAACJ,qBAAqB,EAAE,MAAMJ,oBAAoB,CAAC;EAC/F,OAAO3C,KAAK,CAACwC,iBAAiB,CAACU,cAAc,CAAC;AAChD,CAAC,CAAC,CAACZ,IAAI,CAACtC,KAAK,CAACyC,YAAY,CAAC;AAE7B;AAAAL,OAAA,CAAAM,cAAA,GAAAA,cAAA;AACA,MAAMU,YAAY,GAChB,8IAA8I;AAEhJ;AACA,MAAMvB,WAAW,GAAIwB,KAAa,IAA4B;EAC5D,MAAMzB,GAAG,GAA2B,EAAE;EAEtC;EACAyB,KAAK,GAAGA,KAAK,CAACC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC;EAEtC,IAAIC,KAA6B;EACjC,OAAO,CAACA,KAAK,GAAGH,YAAY,CAACI,IAAI,CAACH,KAAK,CAAC,KAAK,IAAI,EAAE;IACjD,MAAMI,GAAG,GAAGF,KAAK,CAAC,CAAC,CAAC;IAEpB;IACA,IAAIG,KAAK,GAAGH,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE;IAE1B;IACAG,KAAK,GAAGA,KAAK,CAACC,IAAI,EAAE;IAEpB;IACA,MAAMC,UAAU,GAAGF,KAAK,CAAC,CAAC,CAAC;IAE3B;IACAA,KAAK,GAAGA,KAAK,CAACJ,OAAO,CAAC,wBAAwB,EAAE,IAAI,CAAC;IAErD;IACA,IAAIM,UAAU,KAAK,IAAI,EAAE;MACvBF,KAAK,GAAGA,KAAK,CAACJ,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC;MACnCI,KAAK,GAAGA,KAAK,CAACJ,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC;IACrC;IAEA;IACA1B,GAAG,CAAC6B,GAAG,CAAC,GAAGC,KAAK;EAClB;EAEA,OAAOG,MAAM,CAACjC,GAAG,CAAC;AACpB,CAAC;AAED;AACA,MAAMiC,MAAM,GAAIC,MAA8B,IAAI;EAChD,MAAMC,SAAS,GAA2B,EAAE;EAE5C,KAAK,MAAMC,SAAS,IAAIF,MAAM,EAAE;IAC9B;IACAC,SAAS,CAACC,SAAS,CAAC,GAAGC,WAAW,CAACH,MAAM,CAACE,SAAS,CAAC,EAAEF,MAAM,CAAC,CAACR,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;EACrF;EAEA,OAAOS,SAAS;AAClB,CAAC;AAED;AACA,MAAME,WAAW,GAAGA,CAACC,QAAgB,EAAEJ,MAA8B,KAAI;EACvE;EACA;EACA,MAAMK,4BAA4B,GAAGC,UAAU,CAACF,QAAQ,EAAE,gBAAgB,CAAC;EAE3E;EACA;EACA,IAAIC,4BAA4B,KAAK,CAAC,CAAC,EAAE,OAAOD,QAAQ;EAExD;EACA,MAAMG,cAAc,GAAGH,QAAQ,CAACI,KAAK,CAACH,4BAA4B,CAAC;EAEnE;;;;;;;;;;;EAWA,MAAMI,UAAU,GAAG,4CAA4C;EAC/D,MAAMhB,KAAK,GAAGc,cAAc,CAACd,KAAK,CAACgB,UAAU,CAAC;EAE9C,IAAIhB,KAAK,KAAK,IAAI,EAAE;IAClB,MAAM,CAACiB,CAAC,EAAEC,KAAK,EAAEC,YAAY,EAAEC,YAAY,CAAC,GAAGpB,KAAK;IAEpD,OAAOU,WAAW,CAChBC,QAAQ,CAACZ,OAAO,CAACmB,KAAK,EAAEE,YAAY,IAAIb,MAAM,CAACY,YAAY,CAAC,IAAI,EAAE,CAAC,EACnEZ,MAAM,CACP;EACH;EAEA,OAAOI,QAAQ;AACjB,CAAC;AAED;AACA,MAAME,UAAU,GAAGA,CAACQ,GAAW,EAAEC,GAAW,KAAI;EAC9C,MAAMC,OAAO,GAAGC,KAAK,CAACC,IAAI,CAACJ,GAAG,CAACK,QAAQ,CAACJ,GAAG,CAAC,CAAC;EAC7C,OAAOC,OAAO,CAACI,MAAM,GAAG,CAAC,GAAGJ,OAAO,CAACR,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACa,KAAK,GAAG,CAAC,CAAC;AAC7D,CAAC", "ignoreList": []}