{"version": 3, "file": "ScopedRef.d.ts", "sourceRoot": "", "sources": ["../../src/ScopedRef.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAC1C,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,eAAe,CAAA;AAE5C,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAA;AAC7C,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AAExC,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AACxC,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AAExC;;;GAGG;AACH,eAAO,MAAM,eAAe,EAAE,OAAO,MAAiC,CAAA;AAEtE;;;GAGG;AACH,MAAM,MAAM,eAAe,GAAG,OAAO,eAAe,CAAA;AAEpD;;;;;;;;;GASG;AACH,MAAM,WAAW,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAE,SAAQ,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ;IAI5F,QAAQ,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,EAAE,OAAO,CAAA;IACrC,QAAQ,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,EAAE,cAAc,CAAC,IAAI,CAAC,CAAA;IACnD,QAAQ,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,EAAE,oBAAoB,CAAA;CACrD;AAED;;;GAGG;AACH,MAAM,WAAW,cAAc,CAAC,CAAC,SAAS;IAAE,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,EAAE,GAAG,CAAA;CAAE,CAAE,SAAQ,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC;IACnG,SAAS,CAAC,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC,CAAA;CAC/D;AAED;;;GAGG;AACH,MAAM,WAAW,oBAAqB,SAAQ,MAAM,CAAC,iBAAiB;IACpE,MAAM,CAAC,EAAE,IAAI,CAAA;CACd;AAED;;GAEG;AACH,MAAM,CAAC,OAAO,WAAW,SAAS,CAAC;IACjC;;;OAGG;IACH,UAAiB,QAAQ,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;QAChC,QAAQ,CAAC,CAAC,eAAe,CAAC,EAAE;YAC1B,QAAQ,CAAC,EAAE,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;SAChC,CAAA;KACF;CACF;AAED;;;;;;GAMG;AACH,eAAO,MAAM,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAChC,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAC5B,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,KAAK,GAAG,CAAC,CAAwB,CAAA;AAE3E;;;;;GAKG;AACH,eAAO,MAAM,GAAG,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,CAAgB,CAAA;AAE5E;;;;;;GAMG;AACH,eAAO,MAAM,IAAI,EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,CAAiB,CAAA;AAE/G;;;;;;;;;;GAUG;AACH,eAAO,MAAM,GAAG,EAAE;IAChB;;;;;;;;;;OAUG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAA;IACnH;;;;;;;;;;OAUG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAA;CACjG,CAAA"}