"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6399],{16559:(e,a,s)=>{s.d(a,{$v:()=>f,EO:()=>u,Lt:()=>d,Rx:()=>h,Zr:()=>j,ck:()=>x,r7:()=>g,tv:()=>c,wd:()=>p});var t=s(95155),l=s(12115),n=s(17649),r=s(53999),i=s(97168);let d=n.bL,c=n.l9,o=n.ZL,m=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)(n.hJ,{className:(0,r.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...l,ref:a})});m.displayName=n.hJ.displayName;let u=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsxs)(o,{children:[(0,t.jsx)(m,{}),(0,t.jsx)(n.UC,{ref:a,className:(0,r.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",s),...l})]})});u.displayName=n.UC.displayName;let p=e=>{let{className:a,...s}=e;return(0,t.jsx)("div",{className:(0,r.cn)("flex flex-col space-y-2 text-center sm:text-left",a),...s})};p.displayName="AlertDialogHeader";let x=e=>{let{className:a,...s}=e;return(0,t.jsx)("div",{className:(0,r.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",a),...s})};x.displayName="AlertDialogFooter";let g=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)(n.hE,{ref:a,className:(0,r.cn)("text-lg font-semibold",s),...l})});g.displayName=n.hE.displayName;let f=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)(n.VY,{ref:a,className:(0,r.cn)("text-sm text-muted-foreground",s),...l})});f.displayName=n.VY.displayName;let h=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)(n.rc,{ref:a,className:(0,r.cn)((0,i.r)(),s),...l})});h.displayName=n.rc.displayName;let j=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)(n.ZD,{ref:a,className:(0,r.cn)((0,i.r)({variant:"outline"}),"mt-2 sm:mt-0",s),...l})});j.displayName=n.ZD.displayName},20433:(e,a,s)=>{s.d(a,{T:()=>g});var t=s(95155),l=s(12115),n=s(97168),r=s(95139),i=s(82714),d=s(55747),c=s(95784),o=s(51154),m=s(12486),u=s(14503),p=s(31886),x=s(99840);function g(e){let{open:a,onOpenChange:s,campaign:g,onSuccess:f}=e,[h,j]=(0,l.useState)([]),[y,v]=(0,l.useState)([]),[N,b]=(0,l.useState)([]),[w,C]=(0,l.useState)("none"),[S,D]=(0,l.useState)("send"),[k,A]=(0,l.useState)(!1),[E,F]=(0,l.useState)(!0),{toast:R}=(0,u.dj)();(0,l.useEffect)(()=>{let e=async()=>{F(!0);try{let[e,a]=await Promise.all([p.A.get("/marketing/client-types"),p.A.get("/marketing/templates")]);console.log("Send dialog - Client types:",e),console.log("Send dialog - All templates:",a);let s=Array.isArray(e)?e:[],t=(Array.isArray(a)?a:[]).sort((e,a)=>"ar"===e.language&&"ar"!==a.language?-1:"ar"!==e.language&&"ar"===a.language?1:"en"===e.language&&"en"!==a.language&&"ar"!==a.language?-1:"en"!==e.language&&"en"===a.language&&"ar"!==e.language?1:e.language===a.language?e.name.localeCompare(a.name):e.language.localeCompare(a.language));console.log("Sorted templates:",t.map(e=>"".concat(e.name," (").concat(e.language,")"))),j(s),v(t)}catch(e){console.error("Error fetching templates and client types:",e),R({title:"Error",description:"Failed to load templates and client types",variant:"destructive"}),j([]),v([])}finally{F(!1)}};a&&e()},[a,R]);let J=e=>{b(a=>a.includes(e)?a.filter(a=>a!==e):[...a,e])},L=async()=>{if(0===N.length){R({title:"Error",description:"Please select at least one client type",variant:"destructive"});return}if(0===N.filter(e=>h.some(a=>a.id===e)).length){R({title:"Error",description:"Selected client types are not valid",variant:"destructive"});return}A(!0);try{if(!g)return;let e="activate"===S?"/marketing/campaigns/".concat(g.id,"/activate-and-send"):"/marketing/campaigns/".concat(g.id,"/send-to-type"),a=e=>{let a=h.find(a=>a.id===e);return a?a.name:e},t="activate"===S?{clientTypes:N.map(a),..."none"!==w&&{templateName:w}}:{clientType:a(N[0]),..."none"!==w&&{templateName:w}};console.log("Sending campaign with payload:",t);let l=await p.A.post(e,t);R({title:"Success",description:"Campaign ".concat("activate"===S?"activated and ":"","sent to ").concat(l.totalClients," clients (").concat(l.sentCount," successful, ").concat(l.failedCount," failed)")}),s(!1),f&&f()}catch(s){var e,a,t;console.error("Error sending campaign:",s),R({title:"Error",description:(null===(a=s.response)||void 0===a?void 0:null===(e=a.data)||void 0===e?void 0:e.error)||(null===(t=s.responseData)||void 0===t?void 0:t.error)||s.message||"Failed to send campaign",variant:"destructive"})}finally{A(!1)}};return(0,t.jsx)(x.lG,{open:a,onOpenChange:s,children:(0,t.jsxs)(x.Cf,{className:"sm:max-w-[500px]",children:[(0,t.jsxs)(x.c7,{children:[(0,t.jsx)(x.L3,{children:"Send Campaign"}),(0,t.jsxs)(x.rr,{children:['Send "',(null==g?void 0:g.name)||"Campaign",'" to specific client types']})]}),E?(0,t.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,t.jsx)(o.A,{className:"h-8 w-8 animate-spin text-primary"})}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"space-y-4 py-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(i.J,{children:"Send Mode"}),(0,t.jsxs)(d.z,{value:S,onValueChange:e=>D(e),className:"flex flex-col space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(d.C,{value:"send",id:"send"}),(0,t.jsx)(i.J,{htmlFor:"send",children:"Send to client type (no campaign changes)"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(d.C,{value:"activate",id:"activate"}),(0,t.jsx)(i.J,{htmlFor:"activate",children:"Activate campaign and update audience"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(i.J,{children:"Client Types"}),(0,t.jsx)("div",{className:"border rounded-md p-3 space-y-2",children:h.length>0?h.map(e=>(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(r.S,{id:"type-".concat(e.id),checked:N.includes(e.id),onCheckedChange:()=>J(e.id),disabled:"send"===S&&N.length>0&&!N.includes(e.id)}),(0,t.jsxs)(i.J,{htmlFor:"type-".concat(e.id),className:"flex-1",children:[e.name," (",e.count," clients)"]})]},e.id)):(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"No client types available"})}),"send"===S&&(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:'In "Send" mode, you can only select one client type at a time'})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(i.J,{htmlFor:"template",children:["Template Override (Optional)",y.length>0&&(0,t.jsxs)("span",{className:"ml-2 text-xs text-muted-foreground",children:["(",y.filter(e=>"ar"===e.language).length," Arabic, ",y.filter(e=>"en"===e.language).length," English, ",y.filter(e=>"ar"!==e.language&&"en"!==e.language).length," Others)"]})]}),(0,t.jsxs)(c.l6,{value:w,onValueChange:C,defaultValue:"none",children:[(0,t.jsx)(c.bq,{id:"template",children:(0,t.jsx)(c.yv,{placeholder:"Use campaign message"})}),(0,t.jsxs)(c.gC,{className:"max-h-[300px]",children:[(0,t.jsx)(c.eb,{value:"none",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{children:"\uD83D\uDCDD"}),(0,t.jsx)("span",{children:"Use campaign message"})]})}),y.length>0?y.map(e=>(0,t.jsx)(c.eb,{value:e.name,children:(0,t.jsxs)("div",{className:"flex items-center gap-2 w-full",children:[(0,t.jsx)("span",{children:(e=>{switch(e){case"ar":return"\uD83C\uDDF8\uD83C\uDDE6";case"en":return"\uD83C\uDDFA\uD83C\uDDF8";default:return"\uD83C\uDF10"}})(e.language)}),(0,t.jsx)("span",{className:"font-medium",children:e.name}),(0,t.jsx)("span",{className:"text-xs bg-blue-100 text-blue-800 px-1.5 py-0.5 rounded",children:(e=>{switch(e){case"ar":return"AR";case"en":return"EN";default:return e.toUpperCase()}})(e.language)}),(0,t.jsx)("span",{className:"text-xs text-muted-foreground",children:e.category})]})},e.id)):(0,t.jsx)(c.eb,{value:"no-templates",disabled:!0,children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{children:"❌"}),(0,t.jsx)("span",{children:"No templates available"})]})})]})]}),(0,t.jsxs)("div",{className:"text-xs text-muted-foreground space-y-1",children:[(0,t.jsx)("p",{children:"• Select a template to override the campaign message"}),(0,t.jsx)("p",{children:"• Arabic templates (\uD83C\uDDF8\uD83C\uDDE6) are shown first, followed by English (\uD83C\uDDFA\uD83C\uDDF8)"}),(0,t.jsx)("p",{children:"• All languages are available for selection"})]}),w&&"none"!==w&&(0,t.jsxs)("div",{className:"mt-3 p-3 bg-muted rounded-lg",children:[(0,t.jsx)("div",{className:"text-sm font-medium mb-2",children:"Template Preview:"}),(()=>{let e=y.find(e=>e.name===w);return e?(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{children:"ar"===e.language?"\uD83C\uDDF8\uD83C\uDDE6":"en"===e.language?"\uD83C\uDDFA\uD83C\uDDF8":"\uD83C\uDF10"}),(0,t.jsx)("span",{className:"font-medium",children:e.name}),(0,t.jsx)("span",{className:"text-xs bg-blue-100 text-blue-800 px-1.5 py-0.5 rounded",children:e.language.toUpperCase()}),(0,t.jsx)("span",{className:"text-xs bg-gray-100 text-gray-800 px-1.5 py-0.5 rounded",children:e.category})]}),(0,t.jsx)("div",{className:"text-sm text-muted-foreground p-2 bg-background rounded border",children:e.content}),e.variables&&e.variables.length>0&&(0,t.jsxs)("div",{className:"text-xs text-muted-foreground",children:[(0,t.jsx)("strong",{children:"Variables:"})," ",e.variables.join(", ")]})]}):null})()]})]})]}),(0,t.jsxs)(x.Es,{children:[(0,t.jsx)(n.$,{variant:"outline",onClick:()=>s(!1),children:"Cancel"}),(0,t.jsx)(n.$,{onClick:L,disabled:k||0===N.length,children:k?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(o.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Sending..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"Send Campaign"]})})]})]})]})})}},55747:(e,a,s)=>{s.d(a,{C:()=>c,z:()=>d});var t=s(95155),l=s(12115),n=s(54059),r=s(9428),i=s(53999);let d=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)(n.bL,{className:(0,i.cn)("grid gap-2",s),...l,ref:a})});d.displayName=n.bL.displayName;let c=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)(n.q7,{ref:a,className:(0,i.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),...l,children:(0,t.jsx)(n.C1,{className:"flex items-center justify-center",children:(0,t.jsx)(r.A,{className:"h-2.5 w-2.5 fill-current text-current"})})})});c.displayName=n.q7.displayName},82714:(e,a,s)=>{s.d(a,{J:()=>c});var t=s(95155),l=s(12115),n=s(40968),r=s(74466),i=s(53999);let d=(0,r.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)(n.b,{ref:a,className:(0,i.cn)(d(),s),...l})});c.displayName=n.b.displayName},95139:(e,a,s)=>{s.d(a,{S:()=>d});var t=s(95155),l=s(12115),n=s(76981),r=s(5196),i=s(53999);let d=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)(n.bL,{ref:a,className:(0,i.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",s),...l,children:(0,t.jsx)(n.C1,{className:(0,i.cn)("flex items-center justify-center text-current"),children:(0,t.jsx)(r.A,{className:"h-4 w-4"})})})});d.displayName=n.bL.displayName}}]);