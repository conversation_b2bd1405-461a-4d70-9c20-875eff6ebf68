{"version": 3, "file": "patch.js", "names": ["Arr", "_interopRequireWildcard", "require", "_Equal", "_Function", "fiberRefs_", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "OP_EMPTY", "exports", "OP_ADD", "OP_REMOVE", "OP_UPDATE", "OP_AND_THEN", "empty", "_tag", "diff", "oldValue", "newValue", "missingLocals", "Map", "locals", "patch", "fiberRef", "pairs", "entries", "headNonEmpty", "old", "undefined", "equals", "combine", "value", "delete", "dual", "self", "that", "first", "second", "fiberId", "fiberRefs", "patches", "of", "isNonEmptyReadonlyArray", "head", "tail", "tailNonEmpty", "updateAs", "delete_", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prepend"], "sources": ["../../../../src/internal/fiberRefs/patch.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA,IAAAA,GAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AAIA,IAAAE,SAAA,GAAAF,OAAA;AACA,IAAAG,UAAA,GAAAJ,uBAAA,CAAAC,OAAA;AAA6C,SAAAI,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAN,wBAAAM,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAE7C;AACO,MAAMW,QAAQ,GAAAC,OAAA,CAAAD,QAAA,GAAG,OAAgB;AAKxC;AACO,MAAME,MAAM,GAAAD,OAAA,CAAAC,MAAA,GAAG,KAAc;AAKpC;AACO,MAAMC,SAAS,GAAAF,OAAA,CAAAE,SAAA,GAAG,QAAiB;AAK1C;AACO,MAAMC,SAAS,GAAAH,OAAA,CAAAG,SAAA,GAAG,QAAiB;AAK1C;AACO,MAAMC,WAAW,GAAAJ,OAAA,CAAAI,WAAA,GAAG,SAAkB;AAK7C;AACO,MAAMC,KAAK,GAAAL,OAAA,CAAAK,KAAA,GAAmC;EACnDC,IAAI,EAAEP;CAC2B;AAEnC;AACO,MAAMQ,IAAI,GAAGA,CAClBC,QAA6B,EAC7BC,QAA6B,KACI;EACjC,MAAMC,aAAa,GAAG,IAAIC,GAAG,CAACH,QAAQ,CAACI,MAAM,CAAC;EAC9C,IAAIC,KAAK,GAAGR,KAAK;EACjB,KAAK,MAAM,CAACS,QAAQ,EAAEC,KAAK,CAAC,IAAIN,QAAQ,CAACG,MAAM,CAACI,OAAO,EAAE,EAAE;IACzD,MAAMP,QAAQ,GAAGpC,GAAG,CAAC4C,YAAY,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC;IAC3C,MAAMG,GAAG,GAAGR,aAAa,CAACvB,GAAG,CAAC2B,QAAQ,CAAC;IACvC,IAAII,GAAG,KAAKC,SAAS,EAAE;MACrB,MAAMX,QAAQ,GAAGnC,GAAG,CAAC4C,YAAY,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC;MACzC,IAAI,CAAC,IAAAE,aAAM,EAACZ,QAAQ,EAAEC,QAAQ,CAAC,EAAE;QAC/BI,KAAK,GAAGQ,OAAO,CAAC;UACdf,IAAI,EAAEH,SAAS;UACfW,QAAQ;UACRD,KAAK,EAAEC,QAAQ,CAACP,IAAI,CAACC,QAAQ,EAAEC,QAAQ;SACxC,CAAC,CAACI,KAAK,CAAC;MACX;IACF,CAAC,MAAM;MACLA,KAAK,GAAGQ,OAAO,CAAC;QACdf,IAAI,EAAEL,MAAM;QACZa,QAAQ;QACRQ,KAAK,EAAEb;OACR,CAAC,CAACI,KAAK,CAAC;IACX;IACAH,aAAa,CAACa,MAAM,CAACT,QAAQ,CAAC;EAChC;EACA,KAAK,MAAM,CAACA,QAAQ,CAAC,IAAIJ,aAAa,CAACM,OAAO,EAAE,EAAE;IAChDH,KAAK,GAAGQ,OAAO,CAAC;MACdf,IAAI,EAAEJ,SAAS;MACfY;KACD,CAAC,CAACD,KAAK,CAAC;EACX;EACA,OAAOA,KAAK;AACd,CAAC;AAED;AAAAb,OAAA,CAAAO,IAAA,GAAAA,IAAA;AACO,MAAMc,OAAO,GAAArB,OAAA,CAAAqB,OAAA,gBAAG,IAAAG,cAAI,EAGzB,CAAC,EAAE,CAACC,IAAI,EAAEC,IAAI,MAAM;EACpBpB,IAAI,EAAEF,WAAW;EACjBuB,KAAK,EAAEF,IAAI;EACXG,MAAM,EAAEF;CACT,CAAC,CAAC;AAEH;AACO,MAAMb,KAAK,GAAAb,OAAA,CAAAa,KAAA,gBAAG,IAAAW,cAAI,EAUvB,CAAC,EAAE,CAACC,IAAI,EAAEI,OAAO,EAAErB,QAAQ,KAAI;EAC/B,IAAIsB,SAAS,GAAwBtB,QAAQ;EAC7C,IAAIuB,OAAO,GAAiD1D,GAAG,CAAC2D,EAAE,CAACP,IAAI,CAAC;EACxE,OAAOpD,GAAG,CAAC4D,uBAAuB,CAACF,OAAO,CAAC,EAAE;IAC3C,MAAMG,IAAI,GAAG7D,GAAG,CAAC4C,YAAY,CAACc,OAAO,CAAC;IACtC,MAAMI,IAAI,GAAG9D,GAAG,CAAC+D,YAAY,CAACL,OAAO,CAAC;IACtC,QAAQG,IAAI,CAAC5B,IAAI;MACf,KAAKP,QAAQ;QAAE;UACbgC,OAAO,GAAGI,IAAI;UACd;QACF;MACA,KAAKlC,MAAM;QAAE;UACX6B,SAAS,GAAGpD,UAAU,CAAC2D,QAAQ,CAACP,SAAS,EAAE;YACzCD,OAAO;YACPf,QAAQ,EAAEoB,IAAI,CAACpB,QAAQ;YACvBQ,KAAK,EAAEY,IAAI,CAACZ;WACb,CAAC;UACFS,OAAO,GAAGI,IAAI;UACd;QACF;MACA,KAAKjC,SAAS;QAAE;UACd4B,SAAS,GAAGpD,UAAU,CAAC4D,OAAO,CAACR,SAAS,EAAEI,IAAI,CAACpB,QAAQ,CAAC;UACxDiB,OAAO,GAAGI,IAAI;UACd;QACF;MACA,KAAKhC,SAAS;QAAE;UACd,MAAMmB,KAAK,GAAG5C,UAAU,CAAC6D,YAAY,CAACT,SAAS,EAAEI,IAAI,CAACpB,QAAQ,CAAC;UAC/DgB,SAAS,GAAGpD,UAAU,CAAC2D,QAAQ,CAACP,SAAS,EAAE;YACzCD,OAAO;YACPf,QAAQ,EAAEoB,IAAI,CAACpB,QAAQ;YACvBQ,KAAK,EAAEY,IAAI,CAACpB,QAAQ,CAACD,KAAK,CAACqB,IAAI,CAACrB,KAAK,CAAC,CAACS,KAAK;WAC7C,CAAC;UACFS,OAAO,GAAGI,IAAI;UACd;QACF;MACA,KAAK/B,WAAW;QAAE;UAChB2B,OAAO,GAAG1D,GAAG,CAACmE,OAAO,CAACN,IAAI,CAACP,KAAK,CAAC,CAACtD,GAAG,CAACmE,OAAO,CAACN,IAAI,CAACN,MAAM,CAAC,CAACO,IAAI,CAAC,CAAC;UACjE;QACF;IACF;EACF;EACA,OAAOL,SAAS;AAClB,CAAC,CAAC", "ignoreList": []}