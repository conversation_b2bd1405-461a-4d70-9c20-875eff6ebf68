"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.matchSimple = exports.match = void 0;
var core = _interopRequireWildcard(require("./core.js"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
/** @internal */
const match = (concurrency, sequential, unbounded, bounded) => {
  switch (concurrency) {
    case undefined:
      return sequential();
    case "unbounded":
      return unbounded();
    case "inherit":
      return core.fiberRefGetWith(core.currentConcurrency, concurrency => concurrency === "unbounded" ? unbounded() : concurrency > 1 ? bounded(concurrency) : sequential());
    default:
      return concurrency > 1 ? bounded(concurrency) : sequential();
  }
};
/** @internal */
exports.match = match;
const matchSimple = (concurrency, sequential, concurrent) => {
  switch (concurrency) {
    case undefined:
      return sequential();
    case "unbounded":
      return concurrent();
    case "inherit":
      return core.fiberRefGetWith(core.currentConcurrency, concurrency => concurrency === "unbounded" || concurrency > 1 ? concurrent() : sequential());
    default:
      return concurrency > 1 ? concurrent() : sequential();
  }
};
exports.matchSimple = matchSimple;
//# sourceMappingURL=concurrency.js.map