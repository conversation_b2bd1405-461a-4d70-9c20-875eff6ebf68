'use client';

import { useState, useEffect } from 'react';
import { Plus, X, Upload, Check, ChevronRight, ChevronLeft, Save } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useSimpleLanguage } from '@/hooks/useSimpleLanguage';
import { SimpleImageUpload } from '@/components/SimpleImageUpload';

interface PropertyFormStepsProps {
  onSave: (formData: any) => Promise<void>;
  loading: boolean;
  initialData?: any;
  isEdit?: boolean;
  propertyId?: string;
}

export function PropertyFormSteps({ onSave, loading, initialData, isEdit = false, propertyId }: PropertyFormStepsProps) {
  const { language } = useSimpleLanguage();
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 4;
  const [isUploading, setIsUploading] = useState(false);

  const defaultFormData = {
    title: '',
    titleAr: '',
    description: '',
    descriptionAr: '',
    price: '',
    currency: 'SAR',
    type: 'APARTMENT',
    status: 'AVAILABLE',
    bedrooms: '',
    bathrooms: '',
    area: '',
    location: '',
    locationAr: '',
    address: '',
    addressAr: '',
    city: '',
    cityAr: '',
    country: 'Saudi Arabia',
    countryAr: 'المملكة العربية السعودية',
    images: [] as string[],
    features: [] as string[],
    featuresAr: [] as string[],
    amenities: [] as string[],
    amenitiesAr: [] as string[],
    yearBuilt: '',
    parking: '',
    furnished: false,
    petFriendly: false,
    utilities: '',
    utilitiesAr: '',
    contactInfo: '',
    isFeatured: false,
    isActive: true,
  };

  const [formData, setFormData] = useState(initialData || defaultFormData);

  const [newFeature, setNewFeature] = useState('');
  const [newFeatureAr, setNewFeatureAr] = useState('');
  const [newAmenity, setNewAmenity] = useState('');
  const [newAmenityAr, setNewAmenityAr] = useState('');

  // Auto-save functionality (only for create mode)
  useEffect(() => {
    if (!isEdit && !initialData) {
      const savedData = localStorage.getItem('property-draft');
      if (savedData) {
        try {
          const parsed = JSON.parse(savedData);
          setFormData(parsed);
        } catch (error) {
          console.error('Error loading draft:', error);
        }
      }
    }
  }, [isEdit, initialData]);

  useEffect(() => {
    if (!isEdit) {
      const timer = setTimeout(() => {
        localStorage.setItem('property-draft', JSON.stringify(formData));
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [formData, isEdit]);

  // Initialize form data when initialData changes (for edit mode)
  useEffect(() => {
    if (initialData && isEdit) {
      setFormData(initialData);
    }
  }, [initialData, isEdit]);

  // Comprehensive bilingual translations
  const translations = {
    ar: {
      step: 'الخطوة',
      of: 'من',
      next: 'التالي',
      previous: 'السابق',
      save: 'حفظ العقار',
      required: 'مطلوب',
      optional: 'اختياري',
      basicInfo: 'المعلومات الأساسية',
      propertyDetails: 'تفاصيل العقار',
      locationInfo: 'معلومات الموقع',
      additionalInfo: 'معلومات إضافية',
    title: 'عنوان العقار',
    description: 'وصف العقار',
    price: 'السعر',
    currency: 'العملة',
    propertyType: 'نوع العقار',
    status: 'حالة العقار',
    bedrooms: 'غرف النوم',
    bathrooms: 'دورات المياه',
    area: 'المساحة (متر مربع)',
    yearBuilt: 'سنة البناء',
    parking: 'مواقف السيارات',
    location: 'الموقع',
    address: 'العنوان',
    city: 'المدينة',
    country: 'الدولة',
    images: 'صور العقار',
    features: 'مميزات العقار',
    amenities: 'المرافق والخدمات',
    utilities: 'الخدمات المشمولة',
    contactInfo: 'معلومات التواصل',
    furnished: 'مفروش',
    petFriendly: 'يسمح بالحيوانات الأليفة',
    featured: 'عقار مميز',
    active: 'نشط',
    addFeature: 'إضافة ميزة',
    addAmenity: 'إضافة مرفق',
    uploadImages: 'رفع صور العقار',
    dragDropImages: 'اسحب وأفلت الصور هنا، أو انقر للاختيار',
    titlePlaceholder: 'أدخل عنوان العقار...',
    descriptionPlaceholder: 'اكتب وصفاً مفصلاً للعقار...',
    locationPlaceholder: 'أدخل موقع العقار...',
    addressPlaceholder: 'أدخل العنوان الكامل...',
    cityPlaceholder: 'أدخل اسم المدينة...',
    featurePlaceholder: 'أضف ميزة جديدة...',
    amenityPlaceholder: 'أضف مرفق جديد...',
    utilitiesPlaceholder: 'اذكر الخدمات المشمولة...',
    contactPlaceholder: 'أدخل معلومات التواصل...',
    stepDescription1: 'أدخل المعلومات الأساسية للعقار',
    stepDescription2: 'حدد تفاصيل ومواصفات العقار',
    stepDescription3: 'أضف معلومات الموقع والعنوان',
    stepDescription4: 'أضف الصور والمعلومات الإضافية',
    completed: 'مكتمل',
    current: 'الحالي',
    pending: 'في الانتظار',
    imageGallery: 'معرض الصور',
    mainImage: 'الصورة الرئيسية',
    additionalImages: 'الصور الإضافية',
    imageTips: 'نصائح للصور',
    noFeatures: 'لا توجد مميزات مضافة',
    noAmenities: 'لا توجد مرافق مضافة',
    noImages: 'لم يتم رفع صور بعد',
    setAsMain: 'تعيين كصورة رئيسية',
    removeImage: 'حذف الصورة',
    saving: 'جاري الحفظ...',
    success: 'تم بنجاح',
    error: 'حدث خطأ',
    },
    en: {
      step: 'Step',
      of: 'of',
      next: 'Next',
      previous: 'Previous',
      save: 'Save Property',
      required: 'Required',
      optional: 'Optional',
      basicInfo: 'Basic Information',
      propertyDetails: 'Property Details',
      locationInfo: 'Location Information',
      additionalInfo: 'Additional Information',
      title: 'Property Title',
      description: 'Property Description',
      price: 'Price',
      currency: 'Currency',
      propertyType: 'Property Type',
      status: 'Property Status',
      bedrooms: 'Bedrooms',
      bathrooms: 'Bathrooms',
      area: 'Area (sqm)',
      yearBuilt: 'Year Built',
      parking: 'Parking Spaces',
      location: 'Location',
      address: 'Address',
      city: 'City',
      country: 'Country',
      images: 'Property Images',
      features: 'Property Features',
      amenities: 'Amenities & Services',
      utilities: 'Included Utilities',
      contactInfo: 'Contact Information',
      furnished: 'Furnished',
      petFriendly: 'Pet Friendly',
      featured: 'Featured Property',
      active: 'Active',
      addFeature: 'Add Feature',
      addAmenity: 'Add Amenity',
      uploadImages: 'Upload Property Images',
      dragDropImages: 'Drag and drop images here, or click to select',
      titlePlaceholder: 'Enter property title...',
      descriptionPlaceholder: 'Write a detailed property description...',
      locationPlaceholder: 'Enter property location...',
      addressPlaceholder: 'Enter full address...',
      cityPlaceholder: 'Enter city name...',
      featurePlaceholder: 'Add new feature...',
      amenityPlaceholder: 'Add new amenity...',
      utilitiesPlaceholder: 'List included utilities...',
      contactPlaceholder: 'Enter contact information...',
      stepDescription1: 'Enter basic property information',
      stepDescription2: 'Specify property details and specifications',
      stepDescription3: 'Add location and address information',
      stepDescription4: 'Add images and additional information',
      completed: 'Completed',
      current: 'Current',
      pending: 'Pending',
      imageGallery: 'Image Gallery',
      mainImage: 'Main Image',
      additionalImages: 'Additional Images',
      imageTips: 'Image Tips',
      noFeatures: 'No features added',
      noAmenities: 'No amenities added',
      noImages: 'No images uploaded yet',
      setAsMain: 'Set as Main Image',
      removeImage: 'Remove Image',
      saving: 'Saving...',
      success: 'Success',
      error: 'Error',
    }
  };

  const t = translations[language];

  // Bilingual property types
  const propertyTypes = {
    ar: {
      APARTMENT: 'شقة سكنية',
      VILLA: 'فيلا',
      TOWNHOUSE: 'تاون هاوس',
      PENTHOUSE: 'بنتهاوس',
      STUDIO: 'استوديو',
      OFFICE: 'مكتب تجاري',
      SHOP: 'محل تجاري',
      WAREHOUSE: 'مستودع',
      LAND: 'قطعة أرض',
      BUILDING: 'مبنى كامل',
    },
    en: {
      APARTMENT: 'Apartment',
      VILLA: 'Villa',
      TOWNHOUSE: 'Townhouse',
      PENTHOUSE: 'Penthouse',
      STUDIO: 'Studio',
      OFFICE: 'Office',
      SHOP: 'Shop',
      WAREHOUSE: 'Warehouse',
      LAND: 'Land',
      BUILDING: 'Building',
    }
  };

  // Bilingual property statuses
  const propertyStatuses = {
    ar: {
      AVAILABLE: 'متاح للبيع',
      SOLD: 'تم البيع',
      RENTED: 'مؤجر',
      RESERVED: 'محجوز',
      OFF_MARKET: 'غير متاح',
    },
    en: {
      AVAILABLE: 'Available',
      SOLD: 'Sold',
      RENTED: 'Rented',
      RESERVED: 'Reserved',
      OFF_MARKET: 'Off Market',
    }
  };

  const stepTitles = [
    t.basicInfo,
    t.propertyDetails,
    t.locationInfo,
    t.additionalInfo,
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Wait for any ongoing uploads to complete
    if (isUploading) {
      return;
    }

    try {
      await onSave(formData);
      // Clear draft after successful save
      localStorage.removeItem('property-draft');

      // Auto-redirect after successful save
      setTimeout(() => {
        window.location.href = '/dashboard/properties';
      }, 1500);
    } catch (error) {
      console.error('Save failed:', error);
    }
  };

  // Auto-save function for images
  const handleImageAutoSave = async (images: string[]) => {
    if (!isEdit || !propertyId) return;

    try {
      const response = await fetch(`/api/v1/properties/${propertyId}/images`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ images }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to auto-save images');
      }

      const result = await response.json();
      console.log('Images auto-saved successfully:', result);
    } catch (error) {
      console.error('Auto-save error:', error);
      throw error;
    }
  };

  const addFeature = () => {
    if (newFeature.trim()) {
      setFormData(prev => ({
        ...prev,
        features: [...prev.features, newFeature.trim()],
      }));
      setNewFeature('');
    }
  };

  const addFeatureAr = () => {
    if (newFeatureAr.trim()) {
      setFormData(prev => ({
        ...prev,
        featuresAr: [...prev.featuresAr, newFeatureAr.trim()],
      }));
      setNewFeatureAr('');
    }
  };

  const addAmenity = () => {
    if (newAmenity.trim()) {
      setFormData(prev => ({
        ...prev,
        amenities: [...prev.amenities, newAmenity.trim()],
      }));
      setNewAmenity('');
    }
  };

  const addAmenityAr = () => {
    if (newAmenityAr.trim()) {
      setFormData(prev => ({
        ...prev,
        amenitiesAr: [...prev.amenitiesAr, newAmenityAr.trim()],
      }));
      setNewAmenityAr('');
    }
  };

  const removeFeature = (index: number) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.filter((_, i) => i !== index),
    }));
  };

  const removeFeatureAr = (index: number) => {
    setFormData(prev => ({
      ...prev,
      featuresAr: prev.featuresAr.filter((_, i) => i !== index),
    }));
  };

  const removeAmenity = (index: number) => {
    setFormData(prev => ({
      ...prev,
      amenities: prev.amenities.filter((_, i) => i !== index),
    }));
  };

  const removeAmenityAr = (index: number) => {
    setFormData(prev => ({
      ...prev,
      amenitiesAr: prev.amenitiesAr.filter((_, i) => i !== index),
    }));
  };

  const removeImage = (index: number) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index),
    }));
  };

  const moveImage = (fromIndex: number, toIndex: number) => {
    setFormData(prev => {
      const newImages = [...prev.images];
      const [movedImage] = newImages.splice(fromIndex, 1);
      newImages.splice(toIndex, 0, movedImage);
      return {
        ...prev,
        images: newImages,
      };
    });
  };

  const setMainImage = (index: number) => {
    if (index === 0) return; // Already main image
    moveImage(index, 0);
  };

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const isStepValid = (step: number) => {
    switch (step) {
      case 1:
        return formData.title && formData.description && formData.price && formData.type;
      case 2:
        return true; // Property details are optional
      case 3:
        return formData.location && formData.address && formData.city;
      case 4:
        return true; // Additional info is optional
      default:
        return false;
    }
  };

  return (
    <div className={`${language === 'ar' ? 'rtl' : 'ltr'}`} dir={language === 'ar' ? 'rtl' : 'ltr'}>
      {/* Enhanced Bilingual Progress Indicator */}
      <div className="mb-16">
        {/* Progress Header */}
        <div className="relative mb-8">
          <div className="absolute inset-0 bg-gradient-to-r from-emerald-100/50 to-teal-100/50 dark:from-emerald-900/20 dark:to-teal-900/20 rounded-2xl"></div>
          <div className="relative p-6">
            <div className={`flex items-center justify-between ${language === 'ar' ? 'flex-row-reverse' : ''}`}>
              <div className={`flex items-center gap-4 ${language === 'ar' ? 'flex-row-reverse' : ''}`}>
                <div className="relative">
                  <div className="w-14 h-14 bg-gradient-to-br from-emerald-500 via-teal-500 to-cyan-600 rounded-2xl flex items-center justify-center shadow-xl shadow-emerald-200 dark:shadow-emerald-900/50">
                    <span className="text-white font-black text-lg">{currentStep}</span>
                  </div>
                  <div className={`absolute -top-1 ${language === 'ar' ? '-right-1' : '-left-1'} w-6 h-6 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center animate-pulse`}>
                    <span className="text-white text-xs font-bold">✦</span>
                  </div>
                </div>
                <div className={`space-y-1 ${language === 'ar' ? 'text-right' : 'text-left'}`}>
                  <div className={`flex items-center gap-2 ${language === 'ar' ? 'flex-row-reverse' : ''}`}>
                    <span className="text-xl font-black text-slate-800 dark:text-white">
                      {t.step} {currentStep} {t.of} {totalSteps}
                    </span>
                    <div className="px-3 py-1 bg-emerald-100 dark:bg-emerald-900/30 rounded-full">
                      <span className="text-sm font-bold text-emerald-700 dark:text-emerald-300">{t.current}</span>
                    </div>
                  </div>
                  <div className="text-lg font-bold text-slate-700 dark:text-slate-300">
                    {stepTitles[currentStep - 1]}
                  </div>
                  <div className="text-sm text-slate-600 dark:text-slate-400">
                    {currentStep === 1 && t.stepDescription1}
                    {currentStep === 2 && t.stepDescription2}
                    {currentStep === 3 && t.stepDescription3}
                    {currentStep === 4 && t.stepDescription4}
                  </div>
                </div>
              </div>

              <div className={`${language === 'ar' ? 'text-right' : 'text-left'}`}>
                <div className="text-4xl font-black bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                  {Math.round((currentStep / totalSteps) * 100)}%
                </div>
                <div className="text-sm font-bold text-emerald-600 dark:text-emerald-400">
                  {t.completed}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Beautiful Progress Bar */}
        <div className="relative mb-8">
          <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-4 shadow-inner">
            <div
              className="bg-gradient-to-r from-emerald-500 via-teal-500 to-cyan-500 h-4 rounded-full transition-all duration-1000 ease-out shadow-lg relative overflow-hidden"
              style={{ width: `${(currentStep / totalSteps) * 100}%` }}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-pulse"></div>
              <div className="absolute inset-0 bg-gradient-to-r from-emerald-400/50 to-teal-400/50 animate-pulse delay-300"></div>
            </div>
          </div>
        </div>

        {/* Beautiful Step Indicators */}
        <div className="grid grid-cols-4 gap-4">
          {stepTitles.map((title, index) => (
            <div key={index} className="flex flex-col items-center">
              <div
                className={`w-16 h-16 rounded-2xl flex items-center justify-center text-lg font-black transition-all duration-500 shadow-xl ${
                  index + 1 <= currentStep
                    ? 'bg-gradient-to-br from-emerald-500 via-teal-500 to-cyan-600 text-white shadow-emerald-200 dark:shadow-emerald-900/50 scale-110'
                    : index + 1 === currentStep + 1
                    ? 'bg-gradient-to-br from-slate-300 to-slate-400 text-slate-700 shadow-slate-200 dark:shadow-slate-800 scale-105'
                    : 'bg-slate-200 dark:bg-slate-700 text-slate-500 dark:text-slate-400'
                }`}
              >
                {index + 1 < currentStep ? (
                  <Check className="h-7 w-7" />
                ) : index + 1 === currentStep ? (
                  <div className="w-3 h-3 bg-white rounded-full animate-pulse"></div>
                ) : (
                  <span className="font-black">{index + 1}</span>
                )}
              </div>
              <div className="mt-4 text-center">
                <div className={`text-sm font-bold leading-tight ${
                  index + 1 <= currentStep
                    ? 'text-emerald-700 dark:text-emerald-300'
                    : index + 1 === currentStep + 1
                    ? 'text-slate-600 dark:text-slate-400'
                    : 'text-slate-500 dark:text-slate-500'
                }`}>
                  {title}
                </div>
                <div className={`text-xs mt-1 ${
                  index + 1 <= currentStep
                    ? 'text-emerald-600 dark:text-emerald-400'
                    : 'text-slate-400 dark:text-slate-500'
                }`}>
                  {index + 1 <= currentStep ? t.completed : t.pending}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Step 1: Basic Information - Simple Dark Theme */}
        {currentStep === 1 && (
          <Card className="border border-slate-700 bg-slate-800">
            <CardHeader className="pb-6 border-b border-slate-700">
              <CardTitle className="text-xl font-bold text-white flex items-center gap-3">
                <div className="w-10 h-10 bg-emerald-600 text-white rounded-lg flex items-center justify-center text-lg font-bold">
                  1
                </div>
                <div>
                  <div className="text-xl">{t.basicInfo}</div>
                  <div className="text-sm font-normal text-slate-400 mt-1">
                    {language === 'ar' ? 'المعلومات الأساسية' : 'Basic Information'}
                  </div>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-8 p-8">
              {/* Title Fields - Arabic First */}
              <div className="space-y-6">
                <div className="space-y-4">
                  <Label htmlFor="title" className="text-sm font-medium text-white">
                    {t.title} <span className="text-red-400">*</span>
                  </Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    placeholder={t.titlePlaceholder}
                    required
                    dir={language === 'ar' ? 'rtl' : 'ltr'}
                    className="h-12 border border-slate-600 focus:border-emerald-500 bg-slate-700 text-white placeholder:text-slate-400 rounded-lg"
                  />
                </div>
                {language === 'en' && (
                  <div className="space-y-4">
                    <Label htmlFor="titleEn" className="text-base font-semibold text-slate-700 dark:text-slate-300 flex items-center gap-3">
                      <div className="w-6 h-6 bg-slate-100 dark:bg-slate-700 rounded-lg flex items-center justify-center">
                        <span className="text-slate-600 dark:text-slate-400 text-sm">EN</span>
                      </div>
                      {t.titleEn}
                      <span className="text-slate-400 text-sm">({t.optional})</span>
                    </Label>
                    <Input
                      id="titleEn"
                      value={formData.titleAr}
                      onChange={(e) => setFormData(prev => ({ ...prev, titleAr: e.target.value }))}
                      placeholder={t.titleEnPlaceholder}
                      dir="ltr"
                      className="h-12 border-2 border-slate-200 dark:border-slate-600 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-300 rounded-xl bg-white/30 dark:bg-slate-800/30"
                    />
                  </div>
                )}
              </div>

              {/* Description Fields */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className="space-y-3">
                  <Label htmlFor="description" className="text-base font-bold text-slate-800 dark:text-slate-200 flex items-center gap-3">
                    <div className="w-6 h-6 bg-emerald-100 dark:bg-emerald-900/30 rounded-lg flex items-center justify-center">
                      <span className="text-emerald-600 dark:text-emerald-400 text-sm font-bold">2</span>
                    </div>
                    {t.description}
                    <span className="text-red-500 text-lg">*</span>
                  </Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder={t.descriptionPlaceholder}
                    required
                    rows={6}
                    dir="rtl"
                    className="border-2 border-slate-200 dark:border-slate-600 focus:border-emerald-500 dark:focus:border-emerald-400 transition-all duration-300 resize-none rounded-xl text-lg bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm shadow-sm hover:shadow-md focus:shadow-lg"
                  />
                </div>
              </div>

              {/* Price and Type */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-3">
                  <Label htmlFor="price" className="text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2">
                    {t.price}
                    <span className="text-red-500 text-lg">*</span>
                  </Label>
                  <Input
                    id="price"
                    type="number"
                    value={formData.price}
                    onChange={(e) => setFormData(prev => ({ ...prev, price: e.target.value }))}
                    placeholder="0"
                    required
                    className="h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-200 rounded-lg"
                  />
                </div>
                <div className="space-y-3">
                  <Label htmlFor="currency" className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                    {t.currency}
                  </Label>
                  <Select value={formData.currency} onValueChange={(value) => setFormData(prev => ({ ...prev, currency: value }))}>
                    <SelectTrigger className="h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 rounded-lg">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="SAR">SAR - ريال سعودي</SelectItem>
                      <SelectItem value="AED">AED - درهم إماراتي</SelectItem>
                      <SelectItem value="USD">USD - دولار أمريكي</SelectItem>
                      <SelectItem value="EUR">EUR - يورو</SelectItem>
                      <SelectItem value="GBP">GBP - جنيه إسترليني</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-3">
                  <Label htmlFor="type" className="text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2">
                    {t.propertyType}
                    <span className="text-red-500 text-lg">*</span>
                  </Label>
                  <Select value={formData.type} onValueChange={(value) => setFormData(prev => ({ ...prev, type: value }))}>
                    <SelectTrigger className="h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 rounded-lg">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(propertyTypes[language]).map(([key, value]) => (
                        <SelectItem key={key} value={key}>{value}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Status */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <Label htmlFor="status" className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                    {t.status}
                  </Label>
                  <Select value={formData.status} onValueChange={(value) => setFormData(prev => ({ ...prev, status: value }))}>
                    <SelectTrigger className="h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 rounded-lg">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(propertyStatuses[language]).map(([key, value]) => (
                        <SelectItem key={key} value={key}>{value}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Step 2: Property Details */}
        {currentStep === 2 && (
          <Card className="shadow-xl border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm">
            <CardHeader className="pb-6 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-t-lg">
              <CardTitle className="text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg">
                  2
                </div>
                {t.propertyDetails}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-8 p-8">
              {/* Property Specifications */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
                <div className="space-y-3">
                  <Label htmlFor="bedrooms" className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                    {t.bedrooms}
                  </Label>
                  <Input
                    id="bedrooms"
                    type="number"
                    value={formData.bedrooms}
                    onChange={(e) => setFormData(prev => ({ ...prev, bedrooms: e.target.value }))}
                    placeholder="0"
                    min="0"
                    className="h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg"
                  />
                </div>
                <div className="space-y-3">
                  <Label htmlFor="bathrooms" className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                    {t.bathrooms}
                  </Label>
                  <Input
                    id="bathrooms"
                    type="number"
                    value={formData.bathrooms}
                    onChange={(e) => setFormData(prev => ({ ...prev, bathrooms: e.target.value }))}
                    placeholder="0"
                    min="0"
                    className="h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg"
                  />
                </div>
                <div className="space-y-3">
                  <Label htmlFor="area" className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                    {t.area}
                  </Label>
                  <Input
                    id="area"
                    type="number"
                    value={formData.area}
                    onChange={(e) => setFormData(prev => ({ ...prev, area: e.target.value }))}
                    placeholder="0"
                    min="0"
                    className="h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg"
                  />
                </div>
                <div className="space-y-3">
                  <Label htmlFor="yearBuilt" className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                    {t.yearBuilt}
                  </Label>
                  <Input
                    id="yearBuilt"
                    type="number"
                    value={formData.yearBuilt}
                    onChange={(e) => setFormData(prev => ({ ...prev, yearBuilt: e.target.value }))}
                    placeholder="2024"
                    min="1900"
                    max="2030"
                    className="h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg"
                  />
                </div>
                <div className="space-y-3">
                  <Label htmlFor="parking" className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                    {t.parking}
                  </Label>
                  <Input
                    id="parking"
                    type="number"
                    value={formData.parking}
                    onChange={(e) => setFormData(prev => ({ ...prev, parking: e.target.value }))}
                    placeholder="0"
                    min="0"
                    className="h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg"
                  />
                </div>
              </div>

              <Separator className="my-8" />

              {/* Property Features */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className="space-y-4">
                  <Label className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                    {t.features}
                  </Label>
                  <div className="flex gap-2">
                    <Input
                      value={newFeature}
                      onChange={(e) => setNewFeature(e.target.value)}
                      placeholder={t.featurePlaceholder}
                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addFeature())}
                      className="h-10 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg"
                    />
                    <Button type="button" onClick={addFeature} size="sm" className="bg-green-600 hover:bg-green-700 px-4">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2 min-h-[40px] p-3 border-2 border-dashed border-gray-200 dark:border-gray-600 rounded-lg">
                    {formData.features.map((feature, index) => (
                      <Badge key={index} variant="secondary" className="flex items-center gap-1 px-3 py-1 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                        {feature}
                        <X
                          className="h-3 w-3 cursor-pointer hover:text-red-600"
                          onClick={() => removeFeature(index)}
                        />
                      </Badge>
                    ))}
                    {formData.features.length === 0 && (
                      <span className="text-gray-400 text-sm">{t.noFeatures}</span>
                    )}
                  </div>
                </div>
              </div>

              {/* Property Amenities */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className="space-y-4">
                  <Label className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                    {t.amenities}
                  </Label>
                  <div className="flex gap-2">
                    <Input
                      value={newAmenity}
                      onChange={(e) => setNewAmenity(e.target.value)}
                      placeholder={t.amenityPlaceholder}
                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addAmenity())}
                      className="h-10 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg"
                    />
                    <Button type="button" onClick={addAmenity} size="sm" className="bg-green-600 hover:bg-green-700 px-4">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2 min-h-[40px] p-3 border-2 border-dashed border-gray-200 dark:border-gray-600 rounded-lg">
                    {formData.amenities.map((amenity, index) => (
                      <Badge key={index} variant="secondary" className="flex items-center gap-1 px-3 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                        {amenity}
                        <X
                          className="h-3 w-3 cursor-pointer hover:text-red-600"
                          onClick={() => removeAmenity(index)}
                        />
                      </Badge>
                    ))}
                    {formData.amenities.length === 0 && (
                      <span className="text-gray-400 text-sm">{t.noAmenities}</span>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Step 3: Location Information */}
        {currentStep === 3 && (
          <Card className="shadow-xl border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm">
            <CardHeader className="pb-6 bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-t-lg">
              <CardTitle className="text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg">
                  3
                </div>
                {t.locationInfo}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-8 p-8">
              {/* Location Fields */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className="space-y-3">
                  <Label htmlFor="location" className="text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2">
                    {t.location}
                    <span className="text-red-500 text-lg">*</span>
                  </Label>
                  <Input
                    id="location"
                    value={formData.location}
                    onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                    placeholder={t.locationPlaceholder}
                    required
                    className="h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg"
                  />
                </div>

              </div>

              {/* Address Fields */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className="space-y-3">
                  <Label htmlFor="address" className="text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2">
                    {t.address}
                    <span className="text-red-500 text-lg">*</span>
                  </Label>
                  <Input
                    id="address"
                    value={formData.address}
                    onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                    placeholder={t.addressPlaceholder}
                    required
                    className="h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg"
                  />
                </div>

              </div>

              {/* City and Country Fields */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <Label htmlFor="city" className="text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2">
                      {t.city}
                      <span className="text-red-500 text-lg">*</span>
                    </Label>
                    <Select value={formData.city} onValueChange={(value) => setFormData(prev => ({ ...prev, city: value }))}>
                      <SelectTrigger className="h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg">
                        <SelectValue placeholder={language === 'ar' ? 'اختر المدينة السعودية' : 'Select Saudi City'} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Riyadh">Riyadh - الرياض</SelectItem>
                        <SelectItem value="Jeddah">Jeddah - جدة</SelectItem>
                        <SelectItem value="Mecca">Mecca - مكة المكرمة</SelectItem>
                        <SelectItem value="Medina">Medina - المدينة المنورة</SelectItem>
                        <SelectItem value="Dammam">Dammam - الدمام</SelectItem>
                        <SelectItem value="Khobar">Khobar - الخبر</SelectItem>
                        <SelectItem value="Dhahran">Dhahran - الظهران</SelectItem>
                        <SelectItem value="Taif">Taif - الطائف</SelectItem>
                        <SelectItem value="Buraidah">Buraidah - بريدة</SelectItem>
                        <SelectItem value="Tabuk">Tabuk - تبوك</SelectItem>
                        <SelectItem value="Hail">Hail - حائل</SelectItem>
                        <SelectItem value="Abha">Abha - أبها</SelectItem>
                        <SelectItem value="Yanbu">Yanbu - ينبع</SelectItem>
                        <SelectItem value="Jubail">Jubail - الجبيل</SelectItem>
                        <SelectItem value="Najran">Najran - نجران</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-3">
                    <Label htmlFor="country" className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                      {t.country}
                    </Label>
                    <Select value={formData.country} onValueChange={(value) => setFormData(prev => ({ ...prev, country: value }))}>
                      <SelectTrigger className="h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 rounded-lg">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Saudi Arabia">Saudi Arabia - المملكة العربية السعودية</SelectItem>
                        <SelectItem value="UAE">UAE - الإمارات العربية المتحدة</SelectItem>
                        <SelectItem value="Qatar">Qatar - قطر</SelectItem>
                        <SelectItem value="Kuwait">Kuwait - الكويت</SelectItem>
                        <SelectItem value="Bahrain">Bahrain - البحرين</SelectItem>
                        <SelectItem value="Oman">Oman - عُمان</SelectItem>
                        <SelectItem value="Jordan">Jordan - الأردن</SelectItem>
                        <SelectItem value="Egypt">Egypt - مصر</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <Label htmlFor="cityAr" className="text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2">
                      {t.cityAr}
                      <span className="text-gray-400 text-sm">({t.optional})</span>
                    </Label>
                    <Input
                      id="cityAr"
                      value={formData.cityAr}
                      onChange={(e) => setFormData(prev => ({ ...prev, cityAr: e.target.value }))}
                      placeholder={t.cityArPlaceholder}
                      dir="rtl"
                      className="h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg"
                    />
                  </div>
                  <div className="space-y-3">
                    <Label htmlFor="countryAr" className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                      {t.countryAr}
                    </Label>
                    <Input
                      id="countryAr"
                      value={formData.countryAr}
                      onChange={(e) => setFormData(prev => ({ ...prev, countryAr: e.target.value }))}
                      placeholder="أدخل اسم البلد بالعربية"
                      dir="rtl"
                      className="h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Step 4: Additional Information */}
        {currentStep === 4 && (
          <Card className="shadow-xl border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm">
            <CardHeader className="pb-6 bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-900/20 dark:to-amber-900/20 rounded-t-lg">
              <CardTitle className="text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg">
                  4
                </div>
                {t.additionalInfo}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-8 p-8">
              {/* Simple Images Upload with Auto-Complete */}
              <SimpleImageUpload
                images={formData.images}
                onImagesChange={(images) => setFormData(prev => ({ ...prev, images }))}
                onAutoSave={isEdit ? handleImageAutoSave : undefined}
                onUploadStatusChange={setIsUploading}
                propertyId={propertyId}
                maxImages={10}
                disabled={loading}
              />



              <Separator className="my-8" />

              {/* Utilities and Contact */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className="space-y-3">
                  <Label htmlFor="utilities" className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                    {t.utilities}
                  </Label>
                  <Textarea
                    id="utilities"
                    value={formData.utilities}
                    onChange={(e) => setFormData(prev => ({ ...prev, utilities: e.target.value }))}
                    placeholder={t.utilitiesPlaceholder}
                    rows={3}
                    className="border-2 border-gray-200 dark:border-gray-600 focus:border-orange-500 dark:focus:border-orange-400 transition-all duration-200 resize-none rounded-lg"
                  />
                </div>
                <div className="space-y-3">
                  <Label htmlFor="utilitiesAr" className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                    {t.utilitiesAr}
                  </Label>
                  <Textarea
                    id="utilitiesAr"
                    value={formData.utilitiesAr}
                    onChange={(e) => setFormData(prev => ({ ...prev, utilitiesAr: e.target.value }))}
                    placeholder={t.utilitiesArPlaceholder}
                    dir="rtl"
                    rows={3}
                    className="border-2 border-gray-200 dark:border-gray-600 focus:border-orange-500 dark:focus:border-orange-400 transition-all duration-200 resize-none rounded-lg"
                  />
                </div>
              </div>

              <div className="space-y-3">
                <Label htmlFor="contactInfo" className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                  {t.contactInfo}
                </Label>
                <Textarea
                  id="contactInfo"
                  value={formData.contactInfo}
                  onChange={(e) => setFormData(prev => ({ ...prev, contactInfo: e.target.value }))}
                  placeholder={t.contactPlaceholder}
                  rows={3}
                  className="border-2 border-gray-200 dark:border-gray-600 focus:border-orange-500 dark:focus:border-orange-400 transition-all duration-200 resize-none rounded-lg"
                />
              </div>

              <Separator className="my-8" />

              {/* Property Options */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
                <div className="flex items-center space-x-3 p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-orange-400 transition-colors">
                  <Switch
                    id="furnished"
                    checked={formData.furnished}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, furnished: checked }))}
                    className="data-[state=checked]:bg-orange-600"
                  />
                  <Label htmlFor="furnished" className="text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer">
                    {t.furnished}
                  </Label>
                </div>
                <div className="flex items-center space-x-3 p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-orange-400 transition-colors">
                  <Switch
                    id="petFriendly"
                    checked={formData.petFriendly}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, petFriendly: checked }))}
                    className="data-[state=checked]:bg-orange-600"
                  />
                  <Label htmlFor="petFriendly" className="text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer">
                    {t.petFriendly}
                  </Label>
                </div>
                <div className="flex items-center space-x-3 p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-orange-400 transition-colors">
                  <Switch
                    id="isFeatured"
                    checked={formData.isFeatured}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isFeatured: checked }))}
                    className="data-[state=checked]:bg-orange-600"
                  />
                  <Label htmlFor="isFeatured" className="text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer">
                    {t.featured}
                  </Label>
                </div>
                <div className="flex items-center space-x-3 p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-orange-400 transition-colors">
                  <Switch
                    id="isActive"
                    checked={formData.isActive}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: checked }))}
                    className="data-[state=checked]:bg-orange-600"
                  />
                  <Label htmlFor="isActive" className="text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer">
                    {t.active}
                  </Label>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Navigation Buttons */}
        <div className="flex justify-between items-center pt-8">
          <Button
            type="button"
            variant="outline"
            onClick={prevStep}
            disabled={currentStep === 1}
            className="flex items-center gap-2 px-6 py-3 h-12 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <ChevronLeft className="h-4 w-4" />
            {t.previous}
          </Button>

          <div className="flex gap-4">
            {currentStep < totalSteps ? (
              <Button
                type="button"
                onClick={nextStep}
                disabled={!isStepValid(currentStep)}
                className="flex items-center gap-2 px-8 py-3 h-12 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                {t.next}
                <ChevronRight className="h-4 w-4" />
              </Button>
            ) : (
              <Button
                type="submit"
                disabled={loading || isUploading || !isStepValid(currentStep)}
                className="flex items-center gap-2 px-8 py-3 h-12 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                {loading || isUploading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                ) : (
                  <Save className="h-4 w-4" />
                )}
                {isUploading ? (language === 'ar' ? 'جاري رفع الصور...' : 'Uploading images...') : t.save}
              </Button>
            )}
          </div>
        </div>
      </form>
    </div>
  );
}