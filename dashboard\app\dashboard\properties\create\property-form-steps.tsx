'use client';

import { useState, useEffect } from 'react';
import { Plus, X, Upload, Check, ChevronRight, ChevronLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useLanguage } from '@/hooks/useLanguage';
import { UploadDropzone } from '@/lib/uploadthing';

interface PropertyFormStepsProps {
  onSave: (formData: any) => Promise<void>;
  loading: boolean;
  initialData?: any;
  isEdit?: boolean;
}

export function PropertyFormSteps({ onSave, loading, initialData, isEdit = false }: PropertyFormStepsProps) {
  const { language } = useLanguage();
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 4;

  const defaultFormData = {
    title: '',
    titleAr: '',
    description: '',
    descriptionAr: '',
    price: '',
    currency: 'AED',
    type: 'APARTMENT',
    status: 'AVAILABLE',
    bedrooms: '',
    bathrooms: '',
    area: '',
    location: '',
    locationAr: '',
    address: '',
    addressAr: '',
    city: '',
    cityAr: '',
    country: 'UAE',
    countryAr: 'الإمارات العربية المتحدة',
    images: [] as string[],
    features: [] as string[],
    featuresAr: [] as string[],
    amenities: [] as string[],
    amenitiesAr: [] as string[],
    yearBuilt: '',
    parking: '',
    furnished: false,
    petFriendly: false,
    utilities: '',
    utilitiesAr: '',
    contactInfo: '',
    isFeatured: false,
    isActive: true,
  };

  const [formData, setFormData] = useState(initialData || defaultFormData);

  const [newFeature, setNewFeature] = useState('');
  const [newFeatureAr, setNewFeatureAr] = useState('');
  const [newAmenity, setNewAmenity] = useState('');
  const [newAmenityAr, setNewAmenityAr] = useState('');

  // Auto-save functionality (only for create mode)
  useEffect(() => {
    if (!isEdit && !initialData) {
      const savedData = localStorage.getItem('property-draft');
      if (savedData) {
        try {
          const parsed = JSON.parse(savedData);
          setFormData(parsed);
        } catch (error) {
          console.error('Error loading draft:', error);
        }
      }
    }
  }, [isEdit, initialData]);

  useEffect(() => {
    if (!isEdit) {
      const timer = setTimeout(() => {
        localStorage.setItem('property-draft', JSON.stringify(formData));
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [formData, isEdit]);

  // Initialize form data when initialData changes (for edit mode)
  useEffect(() => {
    if (initialData && isEdit) {
      setFormData(initialData);
    }
  }, [initialData, isEdit]);

  const translations = {
    en: {
      step: 'Step',
      of: 'of',
      next: 'Next',
      previous: 'Previous',
      save: 'Save Property',
      required: 'Required',
      optional: 'Optional',
      basicInfo: 'Basic Information',
      propertyDetails: 'Property Details',
      locationInfo: 'Location Information',
      additionalInfo: 'Additional Information',
      title: 'Property Title',
      titleAr: 'Property Title (Arabic)',
      description: 'Description',
      descriptionAr: 'Description (Arabic)',
      price: 'Price',
      currency: 'Currency',
      propertyType: 'Property Type',
      status: 'Status',
      bedrooms: 'Bedrooms',
      bathrooms: 'Bathrooms',
      area: 'Area (m²)',
      yearBuilt: 'Year Built',
      parking: 'Parking Spaces',
      location: 'Location',
      locationAr: 'Location (Arabic)',
      address: 'Address',
      addressAr: 'Address (Arabic)',
      city: 'City',
      cityAr: 'City (Arabic)',
      country: 'Country',
      countryAr: 'Country (Arabic)',
      images: 'Property Images',
      features: 'Features',
      featuresAr: 'Features (Arabic)',
      amenities: 'Amenities',
      amenitiesAr: 'Amenities (Arabic)',
      utilities: 'Utilities Included',
      utilitiesAr: 'Utilities Included (Arabic)',
      contactInfo: 'Contact Information',
      furnished: 'Furnished',
      petFriendly: 'Pet Friendly',
      featured: 'Featured Property',
      active: 'Active Listing',
      addFeature: 'Add Feature',
      addAmenity: 'Add Amenity',
      uploadImages: 'Upload Property Images',
      dragDropImages: 'Drag and drop images here, or click to select',
      titlePlaceholder: 'Enter property title',
      titleArPlaceholder: 'أدخل عنوان العقار بالعربية',
      descriptionPlaceholder: 'Enter property description',
      descriptionArPlaceholder: 'أدخل وصف العقار بالعربية',
      locationPlaceholder: 'Enter location',
      locationArPlaceholder: 'أدخل الموقع بالعربية',
      addressPlaceholder: 'Enter full address',
      addressArPlaceholder: 'أدخل العنوان الكامل بالعربية',
      cityPlaceholder: 'Enter city name',
      cityArPlaceholder: 'أدخل اسم المدينة بالعربية',
      featurePlaceholder: 'Add a feature',
      featureArPlaceholder: 'أضف ميزة',
      amenityPlaceholder: 'Add an amenity',
      amenityArPlaceholder: 'أضف مرفق',
      utilitiesPlaceholder: 'List included utilities',
      utilitiesArPlaceholder: 'اذكر المرافق المشمولة',
      contactPlaceholder: 'Enter contact information',
    },
    ar: {
      step: 'الخطوة',
      of: 'من',
      next: 'التالي',
      previous: 'السابق',
      save: 'حفظ العقار',
      required: 'مطلوب',
      optional: 'اختياري',
      basicInfo: 'المعلومات الأساسية',
      propertyDetails: 'تفاصيل العقار',
      locationInfo: 'معلومات الموقع',
      additionalInfo: 'معلومات إضافية',
      title: 'عنوان العقار',
      titleAr: 'عنوان العقار (عربي)',
      description: 'الوصف',
      descriptionAr: 'الوصف (عربي)',
      price: 'السعر',
      currency: 'العملة',
      propertyType: 'نوع العقار',
      status: 'الحالة',
      bedrooms: 'غرف النوم',
      bathrooms: 'الحمامات',
      area: 'المساحة (م²)',
      yearBuilt: 'سنة البناء',
      parking: 'مواقف السيارات',
      location: 'الموقع',
      locationAr: 'الموقع (عربي)',
      address: 'العنوان',
      addressAr: 'العنوان (عربي)',
      city: 'المدينة',
      cityAr: 'المدينة (عربي)',
      country: 'البلد',
      countryAr: 'البلد (عربي)',
      images: 'صور العقار',
      features: 'المميزات',
      featuresAr: 'المميزات (عربي)',
      amenities: 'المرافق',
      amenitiesAr: 'المرافق (عربي)',
      utilities: 'المرافق المشمولة',
      utilitiesAr: 'المرافق المشمولة (عربي)',
      contactInfo: 'معلومات الاتصال',
      furnished: 'مفروش',
      petFriendly: 'يسمح بالحيوانات الأليفة',
      featured: 'عقار مميز',
      active: 'إعلان نشط',
      addFeature: 'إضافة ميزة',
      addAmenity: 'إضافة مرفق',
      uploadImages: 'رفع صور العقار',
      dragDropImages: 'اسحب وأفلت الصور هنا، أو انقر للاختيار',
      titlePlaceholder: 'أدخل عنوان العقار',
      titleArPlaceholder: 'أدخل عنوان العقار بالعربية',
      descriptionPlaceholder: 'أدخل وصف العقار',
      descriptionArPlaceholder: 'أدخل وصف العقار بالعربية',
      locationPlaceholder: 'أدخل الموقع',
      locationArPlaceholder: 'أدخل الموقع بالعربية',
      addressPlaceholder: 'أدخل العنوان الكامل',
      addressArPlaceholder: 'أدخل العنوان الكامل بالعربية',
      cityPlaceholder: 'أدخل اسم المدينة',
      cityArPlaceholder: 'أدخل اسم المدينة بالعربية',
      featurePlaceholder: 'أضف ميزة',
      featureArPlaceholder: 'أضف ميزة',
      amenityPlaceholder: 'أضف مرفق',
      amenityArPlaceholder: 'أضف مرفق',
      utilitiesPlaceholder: 'اذكر المرافق المشمولة',
      utilitiesArPlaceholder: 'اذكر المرافق المشمولة',
      contactPlaceholder: 'أدخل معلومات الاتصال',
    }
  };

  const t = translations[language as keyof typeof translations];

  const propertyTypes = {
    en: {
      APARTMENT: 'Apartment',
      VILLA: 'Villa',
      TOWNHOUSE: 'Townhouse',
      PENTHOUSE: 'Penthouse',
      STUDIO: 'Studio',
      OFFICE: 'Office',
      SHOP: 'Shop',
      WAREHOUSE: 'Warehouse',
      LAND: 'Land',
      BUILDING: 'Building',
    },
    ar: {
      APARTMENT: 'شقة',
      VILLA: 'فيلا',
      TOWNHOUSE: 'تاون هاوس',
      PENTHOUSE: 'بنتهاوس',
      STUDIO: 'استوديو',
      OFFICE: 'مكتب',
      SHOP: 'محل تجاري',
      WAREHOUSE: 'مستودع',
      LAND: 'أرض',
      BUILDING: 'مبنى',
    }
  };

  const propertyStatuses = {
    en: {
      AVAILABLE: 'Available',
      SOLD: 'Sold',
      RENTED: 'Rented',
      RESERVED: 'Reserved',
      OFF_MARKET: 'Off Market',
    },
    ar: {
      AVAILABLE: 'متاح',
      SOLD: 'مباع',
      RENTED: 'مؤجر',
      RESERVED: 'محجوز',
      OFF_MARKET: 'خارج السوق',
    }
  };

  const stepTitles = [
    t.basicInfo,
    t.propertyDetails,
    t.locationInfo,
    t.additionalInfo,
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await onSave(formData);
    // Clear draft after successful save
    localStorage.removeItem('property-draft');
  };

  const addFeature = () => {
    if (newFeature.trim()) {
      setFormData(prev => ({
        ...prev,
        features: [...prev.features, newFeature.trim()],
      }));
      setNewFeature('');
    }
  };

  const addFeatureAr = () => {
    if (newFeatureAr.trim()) {
      setFormData(prev => ({
        ...prev,
        featuresAr: [...prev.featuresAr, newFeatureAr.trim()],
      }));
      setNewFeatureAr('');
    }
  };

  const addAmenity = () => {
    if (newAmenity.trim()) {
      setFormData(prev => ({
        ...prev,
        amenities: [...prev.amenities, newAmenity.trim()],
      }));
      setNewAmenity('');
    }
  };

  const addAmenityAr = () => {
    if (newAmenityAr.trim()) {
      setFormData(prev => ({
        ...prev,
        amenitiesAr: [...prev.amenitiesAr, newAmenityAr.trim()],
      }));
      setNewAmenityAr('');
    }
  };

  const removeFeature = (index: number) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.filter((_, i) => i !== index),
    }));
  };

  const removeFeatureAr = (index: number) => {
    setFormData(prev => ({
      ...prev,
      featuresAr: prev.featuresAr.filter((_, i) => i !== index),
    }));
  };

  const removeAmenity = (index: number) => {
    setFormData(prev => ({
      ...prev,
      amenities: prev.amenities.filter((_, i) => i !== index),
    }));
  };

  const removeAmenityAr = (index: number) => {
    setFormData(prev => ({
      ...prev,
      amenitiesAr: prev.amenitiesAr.filter((_, i) => i !== index),
    }));
  };

  const removeImage = (index: number) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index),
    }));
  };

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const isStepValid = (step: number) => {
    switch (step) {
      case 1:
        return formData.title && formData.description && formData.price && formData.type;
      case 2:
        return true; // Property details are optional
      case 3:
        return formData.location && formData.address && formData.city;
      case 4:
        return true; // Additional info is optional
      default:
        return false;
    }
  };

  return (
    <div className={`${language === 'ar' ? 'rtl' : 'ltr'}`}>
      {/* Progress Indicator */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            {t.step} {currentStep} {t.of} {totalSteps}
          </span>
          <span className="text-sm text-gray-500 dark:text-gray-400">
            {Math.round((currentStep / totalSteps) * 100)}%
          </span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div
            className="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full transition-all duration-500 ease-out"
            style={{ width: `${(currentStep / totalSteps) * 100}%` }}
          />
        </div>

        {/* Step indicators */}
        <div className="flex justify-between mt-4">
          {stepTitles.map((title, index) => (
            <div key={index} className="flex flex-col items-center">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold transition-all duration-300 ${
                  index + 1 <= currentStep
                    ? 'bg-blue-600 text-white shadow-lg'
                    : 'bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400'
                }`}
              >
                {index + 1 < currentStep ? (
                  <Check className="h-4 w-4" />
                ) : (
                  index + 1
                )}
              </div>
              <span className="text-xs text-gray-600 dark:text-gray-400 mt-2 text-center max-w-20">
                {title}
              </span>
            </div>
          ))}
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Step 1: Basic Information */}
        {currentStep === 1 && (
          <Card className="shadow-xl border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm">
            <CardHeader className="pb-6 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-t-lg">
              <CardTitle className="text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg">
                  1
                </div>
                {t.basicInfo}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-8 p-8">
              {/* Title Fields */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className="space-y-3">
                  <Label htmlFor="title" className="text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2">
                    {t.title}
                    <span className="text-red-500 text-lg">*</span>
                  </Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    placeholder={t.titlePlaceholder}
                    required
                    className="h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-200 rounded-lg"
                  />
                </div>
                <div className="space-y-3">
                  <Label htmlFor="titleAr" className="text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2">
                    {t.titleAr}
                    <span className="text-gray-400 text-sm">({t.optional})</span>
                  </Label>
                  <Input
                    id="titleAr"
                    value={formData.titleAr}
                    onChange={(e) => setFormData(prev => ({ ...prev, titleAr: e.target.value }))}
                    placeholder={t.titleArPlaceholder}
                    dir="rtl"
                    className="h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-200 rounded-lg"
                  />
                </div>
              </div>

              {/* Description Fields */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className="space-y-3">
                  <Label htmlFor="description" className="text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2">
                    {t.description}
                    <span className="text-red-500 text-lg">*</span>
                  </Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder={t.descriptionPlaceholder}
                    required
                    rows={5}
                    className="border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-200 resize-none rounded-lg"
                  />
                </div>
                <div className="space-y-3">
                  <Label htmlFor="descriptionAr" className="text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2">
                    {t.descriptionAr}
                    <span className="text-gray-400 text-sm">({t.optional})</span>
                  </Label>
                  <Textarea
                    id="descriptionAr"
                    value={formData.descriptionAr}
                    onChange={(e) => setFormData(prev => ({ ...prev, descriptionAr: e.target.value }))}
                    placeholder={t.descriptionArPlaceholder}
                    dir="rtl"
                    rows={5}
                    className="border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-200 resize-none rounded-lg"
                  />
                </div>
              </div>

              {/* Price and Type */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-3">
                  <Label htmlFor="price" className="text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2">
                    {t.price}
                    <span className="text-red-500 text-lg">*</span>
                  </Label>
                  <Input
                    id="price"
                    type="number"
                    value={formData.price}
                    onChange={(e) => setFormData(prev => ({ ...prev, price: e.target.value }))}
                    placeholder="0"
                    required
                    className="h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-200 rounded-lg"
                  />
                </div>
                <div className="space-y-3">
                  <Label htmlFor="currency" className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                    {t.currency}
                  </Label>
                  <Select value={formData.currency} onValueChange={(value) => setFormData(prev => ({ ...prev, currency: value }))}>
                    <SelectTrigger className="h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 rounded-lg">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="AED">AED - درهم إماراتي</SelectItem>
                      <SelectItem value="USD">USD - دولار أمريكي</SelectItem>
                      <SelectItem value="EUR">EUR - يورو</SelectItem>
                      <SelectItem value="GBP">GBP - جنيه إسترليني</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-3">
                  <Label htmlFor="type" className="text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2">
                    {t.propertyType}
                    <span className="text-red-500 text-lg">*</span>
                  </Label>
                  <Select value={formData.type} onValueChange={(value) => setFormData(prev => ({ ...prev, type: value }))}>
                    <SelectTrigger className="h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 rounded-lg">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(propertyTypes[language as keyof typeof propertyTypes]).map(([key, value]) => (
                        <SelectItem key={key} value={key}>{value}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Status */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <Label htmlFor="status" className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                    {t.status}
                  </Label>
                  <Select value={formData.status} onValueChange={(value) => setFormData(prev => ({ ...prev, status: value }))}>
                    <SelectTrigger className="h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 rounded-lg">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(propertyStatuses[language as keyof typeof propertyStatuses]).map(([key, value]) => (
                        <SelectItem key={key} value={key}>{value}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Step 2: Property Details */}
        {currentStep === 2 && (
          <Card className="shadow-xl border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm">
            <CardHeader className="pb-6 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-t-lg">
              <CardTitle className="text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg">
                  2
                </div>
                {t.propertyDetails}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-8 p-8">
              {/* Property Specifications */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
                <div className="space-y-3">
                  <Label htmlFor="bedrooms" className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                    {t.bedrooms}
                  </Label>
                  <Input
                    id="bedrooms"
                    type="number"
                    value={formData.bedrooms}
                    onChange={(e) => setFormData(prev => ({ ...prev, bedrooms: e.target.value }))}
                    placeholder="0"
                    min="0"
                    className="h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg"
                  />
                </div>
                <div className="space-y-3">
                  <Label htmlFor="bathrooms" className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                    {t.bathrooms}
                  </Label>
                  <Input
                    id="bathrooms"
                    type="number"
                    value={formData.bathrooms}
                    onChange={(e) => setFormData(prev => ({ ...prev, bathrooms: e.target.value }))}
                    placeholder="0"
                    min="0"
                    className="h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg"
                  />
                </div>
                <div className="space-y-3">
                  <Label htmlFor="area" className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                    {t.area}
                  </Label>
                  <Input
                    id="area"
                    type="number"
                    value={formData.area}
                    onChange={(e) => setFormData(prev => ({ ...prev, area: e.target.value }))}
                    placeholder="0"
                    min="0"
                    className="h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg"
                  />
                </div>
                <div className="space-y-3">
                  <Label htmlFor="yearBuilt" className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                    {t.yearBuilt}
                  </Label>
                  <Input
                    id="yearBuilt"
                    type="number"
                    value={formData.yearBuilt}
                    onChange={(e) => setFormData(prev => ({ ...prev, yearBuilt: e.target.value }))}
                    placeholder="2024"
                    min="1900"
                    max="2030"
                    className="h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg"
                  />
                </div>
                <div className="space-y-3">
                  <Label htmlFor="parking" className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                    {t.parking}
                  </Label>
                  <Input
                    id="parking"
                    type="number"
                    value={formData.parking}
                    onChange={(e) => setFormData(prev => ({ ...prev, parking: e.target.value }))}
                    placeholder="0"
                    min="0"
                    className="h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg"
                  />
                </div>
              </div>

              <Separator className="my-8" />

              {/* Property Features */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className="space-y-4">
                  <Label className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                    {t.features}
                  </Label>
                  <div className="flex gap-2">
                    <Input
                      value={newFeature}
                      onChange={(e) => setNewFeature(e.target.value)}
                      placeholder={t.featurePlaceholder}
                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addFeature())}
                      className="h-10 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg"
                    />
                    <Button type="button" onClick={addFeature} size="sm" className="bg-green-600 hover:bg-green-700 px-4">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2 min-h-[40px] p-3 border-2 border-dashed border-gray-200 dark:border-gray-600 rounded-lg">
                    {formData.features.map((feature, index) => (
                      <Badge key={index} variant="secondary" className="flex items-center gap-1 px-3 py-1 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                        {feature}
                        <X
                          className="h-3 w-3 cursor-pointer hover:text-red-600"
                          onClick={() => removeFeature(index)}
                        />
                      </Badge>
                    ))}
                    {formData.features.length === 0 && (
                      <span className="text-gray-400 text-sm">{language === 'ar' ? 'لا توجد مميزات مضافة' : 'No features added'}</span>
                    )}
                  </div>
                </div>
                <div className="space-y-4">
                  <Label className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                    {t.featuresAr}
                  </Label>
                  <div className="flex gap-2">
                    <Input
                      value={newFeatureAr}
                      onChange={(e) => setNewFeatureAr(e.target.value)}
                      placeholder={t.featureArPlaceholder}
                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addFeatureAr())}
                      dir="rtl"
                      className="h-10 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg"
                    />
                    <Button type="button" onClick={addFeatureAr} size="sm" className="bg-green-600 hover:bg-green-700 px-4">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2 min-h-[40px] p-3 border-2 border-dashed border-gray-200 dark:border-gray-600 rounded-lg" dir="rtl">
                    {formData.featuresAr.map((feature, index) => (
                      <Badge key={index} variant="secondary" className="flex items-center gap-1 px-3 py-1 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                        {feature}
                        <X
                          className="h-3 w-3 cursor-pointer hover:text-red-600"
                          onClick={() => removeFeatureAr(index)}
                        />
                      </Badge>
                    ))}
                    {formData.featuresAr.length === 0 && (
                      <span className="text-gray-400 text-sm">لا توجد مميزات مضافة</span>
                    )}
                  </div>
                </div>
              </div>

              {/* Property Amenities */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className="space-y-4">
                  <Label className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                    {t.amenities}
                  </Label>
                  <div className="flex gap-2">
                    <Input
                      value={newAmenity}
                      onChange={(e) => setNewAmenity(e.target.value)}
                      placeholder={t.amenityPlaceholder}
                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addAmenity())}
                      className="h-10 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg"
                    />
                    <Button type="button" onClick={addAmenity} size="sm" className="bg-green-600 hover:bg-green-700 px-4">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2 min-h-[40px] p-3 border-2 border-dashed border-gray-200 dark:border-gray-600 rounded-lg">
                    {formData.amenities.map((amenity, index) => (
                      <Badge key={index} variant="secondary" className="flex items-center gap-1 px-3 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                        {amenity}
                        <X
                          className="h-3 w-3 cursor-pointer hover:text-red-600"
                          onClick={() => removeAmenity(index)}
                        />
                      </Badge>
                    ))}
                    {formData.amenities.length === 0 && (
                      <span className="text-gray-400 text-sm">{language === 'ar' ? 'لا توجد مرافق مضافة' : 'No amenities added'}</span>
                    )}
                  </div>
                </div>
                <div className="space-y-4">
                  <Label className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                    {t.amenitiesAr}
                  </Label>
                  <div className="flex gap-2">
                    <Input
                      value={newAmenityAr}
                      onChange={(e) => setNewAmenityAr(e.target.value)}
                      placeholder={t.amenityArPlaceholder}
                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addAmenityAr())}
                      dir="rtl"
                      className="h-10 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg"
                    />
                    <Button type="button" onClick={addAmenityAr} size="sm" className="bg-green-600 hover:bg-green-700 px-4">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2 min-h-[40px] p-3 border-2 border-dashed border-gray-200 dark:border-gray-600 rounded-lg" dir="rtl">
                    {formData.amenitiesAr.map((amenity, index) => (
                      <Badge key={index} variant="secondary" className="flex items-center gap-1 px-3 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                        {amenity}
                        <X
                          className="h-3 w-3 cursor-pointer hover:text-red-600"
                          onClick={() => removeAmenityAr(index)}
                        />
                      </Badge>
                    ))}
                    {formData.amenitiesAr.length === 0 && (
                      <span className="text-gray-400 text-sm">لا توجد مرافق مضافة</span>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Step 3: Location Information */}
        {currentStep === 3 && (
          <Card className="shadow-xl border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm">
            <CardHeader className="pb-6 bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-t-lg">
              <CardTitle className="text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg">
                  3
                </div>
                {t.locationInfo}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-8 p-8">
              {/* Location Fields */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className="space-y-3">
                  <Label htmlFor="location" className="text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2">
                    {t.location}
                    <span className="text-red-500 text-lg">*</span>
                  </Label>
                  <Input
                    id="location"
                    value={formData.location}
                    onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                    placeholder={t.locationPlaceholder}
                    required
                    className="h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg"
                  />
                </div>
                <div className="space-y-3">
                  <Label htmlFor="locationAr" className="text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2">
                    {t.locationAr}
                    <span className="text-gray-400 text-sm">({t.optional})</span>
                  </Label>
                  <Input
                    id="locationAr"
                    value={formData.locationAr}
                    onChange={(e) => setFormData(prev => ({ ...prev, locationAr: e.target.value }))}
                    placeholder={t.locationArPlaceholder}
                    dir="rtl"
                    className="h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg"
                  />
                </div>
              </div>

              {/* Address Fields */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className="space-y-3">
                  <Label htmlFor="address" className="text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2">
                    {t.address}
                    <span className="text-red-500 text-lg">*</span>
                  </Label>
                  <Input
                    id="address"
                    value={formData.address}
                    onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                    placeholder={t.addressPlaceholder}
                    required
                    className="h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg"
                  />
                </div>
                <div className="space-y-3">
                  <Label htmlFor="addressAr" className="text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2">
                    {t.addressAr}
                    <span className="text-gray-400 text-sm">({t.optional})</span>
                  </Label>
                  <Input
                    id="addressAr"
                    value={formData.addressAr}
                    onChange={(e) => setFormData(prev => ({ ...prev, addressAr: e.target.value }))}
                    placeholder={t.addressArPlaceholder}
                    dir="rtl"
                    className="h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg"
                  />
                </div>
              </div>

              {/* City and Country Fields */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <Label htmlFor="city" className="text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2">
                      {t.city}
                      <span className="text-red-500 text-lg">*</span>
                    </Label>
                    <Input
                      id="city"
                      value={formData.city}
                      onChange={(e) => setFormData(prev => ({ ...prev, city: e.target.value }))}
                      placeholder={t.cityPlaceholder}
                      required
                      className="h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg"
                    />
                  </div>
                  <div className="space-y-3">
                    <Label htmlFor="country" className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                      {t.country}
                    </Label>
                    <Select value={formData.country} onValueChange={(value) => setFormData(prev => ({ ...prev, country: value }))}>
                      <SelectTrigger className="h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 rounded-lg">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="UAE">UAE - الإمارات العربية المتحدة</SelectItem>
                        <SelectItem value="Saudi Arabia">Saudi Arabia - المملكة العربية السعودية</SelectItem>
                        <SelectItem value="Qatar">Qatar - قطر</SelectItem>
                        <SelectItem value="Kuwait">Kuwait - الكويت</SelectItem>
                        <SelectItem value="Bahrain">Bahrain - البحرين</SelectItem>
                        <SelectItem value="Oman">Oman - عُمان</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <Label htmlFor="cityAr" className="text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2">
                      {t.cityAr}
                      <span className="text-gray-400 text-sm">({t.optional})</span>
                    </Label>
                    <Input
                      id="cityAr"
                      value={formData.cityAr}
                      onChange={(e) => setFormData(prev => ({ ...prev, cityAr: e.target.value }))}
                      placeholder={t.cityArPlaceholder}
                      dir="rtl"
                      className="h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg"
                    />
                  </div>
                  <div className="space-y-3">
                    <Label htmlFor="countryAr" className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                      {t.countryAr}
                    </Label>
                    <Input
                      id="countryAr"
                      value={formData.countryAr}
                      onChange={(e) => setFormData(prev => ({ ...prev, countryAr: e.target.value }))}
                      placeholder="أدخل اسم البلد بالعربية"
                      dir="rtl"
                      className="h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Step 4: Additional Information */}
        {currentStep === 4 && (
          <Card className="shadow-xl border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm">
            <CardHeader className="pb-6 bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-900/20 dark:to-amber-900/20 rounded-t-lg">
              <CardTitle className="text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg">
                  4
                </div>
                {t.additionalInfo}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-8 p-8">
              {/* Images Upload */}
              <div className="space-y-4">
                <Label className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                  {t.images}
                </Label>
                <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 hover:border-orange-400 transition-colors">
                  <UploadDropzone
                    endpoint="propertyImageUploader"
                    onClientUploadComplete={(res) => {
                      if (res) {
                        const newImages = res.map(file => file.url);
                        setFormData(prev => ({
                          ...prev,
                          images: [...prev.images, ...newImages],
                        }));
                      }
                    }}
                    onUploadError={(error: Error) => {
                      console.error('Upload error:', error);
                    }}
                    className="ut-button:bg-orange-600 ut-button:hover:bg-orange-700"
                  />
                </div>
                {formData.images.length > 0 && (
                  <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 mt-6">
                    {formData.images.map((image, index) => (
                      <div key={index} className="relative group">
                        <img
                          src={image}
                          alt={`Property image ${index + 1}`}
                          className="w-full h-24 object-cover rounded-lg shadow-md group-hover:shadow-lg transition-shadow"
                        />
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          className="absolute -top-2 -right-2 h-6 w-6 p-0 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                          onClick={() => removeImage(index)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              <Separator className="my-8" />

              {/* Utilities and Contact */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className="space-y-3">
                  <Label htmlFor="utilities" className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                    {t.utilities}
                  </Label>
                  <Textarea
                    id="utilities"
                    value={formData.utilities}
                    onChange={(e) => setFormData(prev => ({ ...prev, utilities: e.target.value }))}
                    placeholder={t.utilitiesPlaceholder}
                    rows={3}
                    className="border-2 border-gray-200 dark:border-gray-600 focus:border-orange-500 dark:focus:border-orange-400 transition-all duration-200 resize-none rounded-lg"
                  />
                </div>
                <div className="space-y-3">
                  <Label htmlFor="utilitiesAr" className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                    {t.utilitiesAr}
                  </Label>
                  <Textarea
                    id="utilitiesAr"
                    value={formData.utilitiesAr}
                    onChange={(e) => setFormData(prev => ({ ...prev, utilitiesAr: e.target.value }))}
                    placeholder={t.utilitiesArPlaceholder}
                    dir="rtl"
                    rows={3}
                    className="border-2 border-gray-200 dark:border-gray-600 focus:border-orange-500 dark:focus:border-orange-400 transition-all duration-200 resize-none rounded-lg"
                  />
                </div>
              </div>

              <div className="space-y-3">
                <Label htmlFor="contactInfo" className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                  {t.contactInfo}
                </Label>
                <Textarea
                  id="contactInfo"
                  value={formData.contactInfo}
                  onChange={(e) => setFormData(prev => ({ ...prev, contactInfo: e.target.value }))}
                  placeholder={t.contactPlaceholder}
                  rows={3}
                  className="border-2 border-gray-200 dark:border-gray-600 focus:border-orange-500 dark:focus:border-orange-400 transition-all duration-200 resize-none rounded-lg"
                />
              </div>

              <Separator className="my-8" />

              {/* Property Options */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
                <div className="flex items-center space-x-3 p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-orange-400 transition-colors">
                  <Switch
                    id="furnished"
                    checked={formData.furnished}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, furnished: checked }))}
                    className="data-[state=checked]:bg-orange-600"
                  />
                  <Label htmlFor="furnished" className="text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer">
                    {t.furnished}
                  </Label>
                </div>
                <div className="flex items-center space-x-3 p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-orange-400 transition-colors">
                  <Switch
                    id="petFriendly"
                    checked={formData.petFriendly}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, petFriendly: checked }))}
                    className="data-[state=checked]:bg-orange-600"
                  />
                  <Label htmlFor="petFriendly" className="text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer">
                    {t.petFriendly}
                  </Label>
                </div>
                <div className="flex items-center space-x-3 p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-orange-400 transition-colors">
                  <Switch
                    id="isFeatured"
                    checked={formData.isFeatured}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isFeatured: checked }))}
                    className="data-[state=checked]:bg-orange-600"
                  />
                  <Label htmlFor="isFeatured" className="text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer">
                    {t.featured}
                  </Label>
                </div>
                <div className="flex items-center space-x-3 p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-orange-400 transition-colors">
                  <Switch
                    id="isActive"
                    checked={formData.isActive}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: checked }))}
                    className="data-[state=checked]:bg-orange-600"
                  />
                  <Label htmlFor="isActive" className="text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer">
                    {t.active}
                  </Label>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Navigation Buttons */}
        <div className="flex justify-between items-center pt-8">
          <Button
            type="button"
            variant="outline"
            onClick={prevStep}
            disabled={currentStep === 1}
            className="flex items-center gap-2 px-6 py-3 h-12 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <ChevronLeft className="h-4 w-4" />
            {t.previous}
          </Button>

          <div className="flex gap-4">
            {currentStep < totalSteps ? (
              <Button
                type="button"
                onClick={nextStep}
                disabled={!isStepValid(currentStep)}
                className="flex items-center gap-2 px-8 py-3 h-12 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                {t.next}
                <ChevronRight className="h-4 w-4" />
              </Button>
            ) : (
              <Button
                type="submit"
                disabled={loading || !isStepValid(currentStep)}
                className="flex items-center gap-2 px-8 py-3 h-12 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                {loading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                ) : (
                  <Save className="h-4 w-4" />
                )}
                {t.save}
              </Button>
            )}
          </div>
        </div>
      </form>
    </div>
  );
}