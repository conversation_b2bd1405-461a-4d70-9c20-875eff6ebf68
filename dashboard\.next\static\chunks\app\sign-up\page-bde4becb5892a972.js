(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3884],{50980:(e,s,a)=>{Promise.resolve().then(a.bind(a,88438))},88438:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>v});var t=a(95155),r=a(12115),i=a(72280),l=a(35695),n=a(45493),o=a(62177),c=a(90221),d=a(55594),m=a(97168),h=a(89852),p=a(82714),x=a(78519),u=a(56671);let f=d.Ik({firstName:d.Yj().min(1),lastName:d.Yj().optional(),email:d.Yj().email(),password:d.Yj().min(6),confirmPassword:d.Yj().min(6)}).refine(e=>e.password===e.confirmPassword,{message:"Passwords do not match",path:["confirmPassword"]});function j(){let{t:e}=(0,i.B)(),s=(0,l.useRouter)(),a=(0,l.useSearchParams)().get("redirect_url")||"/dashboard/analytics";console.log("Redirect URL:",a);let[d,j]=(0,r.useState)(!1),{register:N,handleSubmit:g,formState:{errors:w}}=(0,o.mN)({resolver:(0,c.u)(f)});async function v(t){j(!0);try{let r=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({firstName:t.firstName,lastName:t.lastName,email:t.email,password:t.password})});if(!r.ok){let s=await r.json();u.oR.error(s.message||e("auth.registrationFailed")),j(!1);return}let i=await r.json();console.log("Registration response:",i);let l=await (0,n.Jv)("credentials",{email:t.email,password:t.password,redirect:!1,callbackUrl:a});if(null==l?void 0:l.error){u.oR.error(e("auth.signInAfterRegistrationFailed")),j(!1);return}u.oR.success(e("auth.registrationSuccessful")),s.push(a),s.refresh()}catch(s){console.error("Sign up error:",s),u.oR.error(e("common.somethingWentWrong")),j(!1)}}return(0,t.jsxs)("div",{className:"grid gap-6",children:[(0,t.jsx)("form",{onSubmit:g(v),children:(0,t.jsxs)("div",{className:"grid gap-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(p.J,{htmlFor:"firstName",children:e("auth.firstName")}),(0,t.jsx)(h.p,{id:"firstName",type:"text",autoCapitalize:"words",autoComplete:"given-name",disabled:d,...N("firstName")}),w.firstName&&(0,t.jsx)("p",{className:"text-sm text-destructive",children:w.firstName.message})]}),(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(p.J,{htmlFor:"lastName",children:e("auth.lastName")}),(0,t.jsx)(h.p,{id:"lastName",type:"text",autoCapitalize:"words",autoComplete:"family-name",disabled:d,...N("lastName")}),w.lastName&&(0,t.jsx)("p",{className:"text-sm text-destructive",children:w.lastName.message})]})]}),(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(p.J,{htmlFor:"email",children:e("auth.email")}),(0,t.jsx)(h.p,{id:"email",type:"email",placeholder:"<EMAIL>",autoCapitalize:"none",autoComplete:"email",autoCorrect:"off",disabled:d,...N("email")}),w.email&&(0,t.jsx)("p",{className:"text-sm text-destructive",children:w.email.message})]}),(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(p.J,{htmlFor:"password",children:e("auth.password")}),(0,t.jsx)(h.p,{id:"password",type:"password",autoCapitalize:"none",autoComplete:"new-password",disabled:d,...N("password")}),w.password&&(0,t.jsx)("p",{className:"text-sm text-destructive",children:w.password.message})]}),(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(p.J,{htmlFor:"confirmPassword",children:e("auth.confirmPassword")}),(0,t.jsx)(h.p,{id:"confirmPassword",type:"password",autoCapitalize:"none",autoComplete:"new-password",disabled:d,...N("confirmPassword")}),w.confirmPassword&&(0,t.jsx)("p",{className:"text-sm text-destructive",children:w.confirmPassword.message})]}),(0,t.jsxs)(m.$,{type:"submit",disabled:d,children:[d&&(0,t.jsx)(x.F.spinner,{className:"mr-2 h-4 w-4 animate-spin"}),e("auth.signUp")]})]})}),(0,t.jsxs)("div",{className:"text-center text-sm",children:[e("auth.alreadyHaveAccount")," ",(0,t.jsx)(m.$,{variant:"link",className:"px-0",asChild:!0,children:(0,t.jsx)("a",{href:"/sign-in".concat(a?"?redirect_url=".concat(encodeURIComponent(a)):""),children:e("auth.signIn")})})]})]})}var N=a(73911),g=a(27971);function w(){let{t:e}=(0,i.B)();return(0,t.jsx)("div",{className:"container flex h-screen w-screen flex-col items-center justify-center",children:(0,t.jsxs)("div",{className:"mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]",children:[(0,t.jsxs)("div",{className:"flex flex-col space-y-2 text-center",children:[(0,t.jsx)(x.F.logo,{className:"mx-auto h-6 w-6"}),(0,t.jsx)("h1",{className:"text-2xl font-semibold tracking-tight",children:e("auth.createAccount")}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:e("auth.enterDetailsToCreateAccount")})]}),(0,t.jsx)(j,{}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)(N.c,{}),(0,t.jsx)(g.U,{})]})]})})}function v(){return(0,t.jsx)(r.Suspense,{fallback:(0,t.jsx)("div",{className:"flex h-screen items-center justify-center",children:"Loading..."}),children:(0,t.jsx)(w,{})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[4277,5493,3692,1721,1342,7270,7535,8441,1684,7358],()=>s(50980)),_N_E=e.O()}]);