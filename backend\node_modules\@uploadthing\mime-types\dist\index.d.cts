/**
 * Vendored version of mime-types that can run on the edge due to not using path.extname
 *
 * Also ported it to TypeScript cause it was easier than playing around with custom d.ts file
 *
 * Removed all the stuff we didn't use
 */
declare const mimes: {
    "chemical/x-cdx": {
        readonly source: "apache";
        readonly extensions: readonly ["cdx"];
    };
    "chemical/x-cif": {
        readonly source: "apache";
        readonly extensions: readonly ["cif"];
    };
    "chemical/x-cmdf": {
        readonly source: "apache";
        readonly extensions: readonly ["cmdf"];
    };
    "chemical/x-cml": {
        readonly source: "apache";
        readonly extensions: readonly ["cml"];
    };
    "chemical/x-csml": {
        readonly source: "apache";
        readonly extensions: readonly ["csml"];
    };
    "chemical/x-xyz": {
        readonly source: "apache";
        readonly extensions: readonly ["xyz"];
    };
    "font/collection": {
        readonly source: "iana";
        readonly extensions: readonly ["ttc"];
    };
    "font/otf": {
        readonly source: "iana";
        readonly extensions: readonly ["otf"];
    };
    "font/ttf": {
        readonly source: "iana";
        readonly extensions: readonly ["ttf"];
    };
    "font/woff": {
        readonly source: "iana";
        readonly extensions: readonly ["woff"];
    };
    "font/woff2": {
        readonly source: "iana";
        readonly extensions: readonly ["woff2"];
    };
    "message/disposition-notification": {
        readonly source: "iana";
        readonly extensions: readonly ["disposition-notification"];
    };
    "message/global": {
        readonly source: "iana";
        readonly extensions: readonly ["u8msg"];
    };
    "message/global-delivery-status": {
        readonly source: "iana";
        readonly extensions: readonly ["u8dsn"];
    };
    "message/global-disposition-notification": {
        readonly source: "iana";
        readonly extensions: readonly ["u8mdn"];
    };
    "message/global-headers": {
        readonly source: "iana";
        readonly extensions: readonly ["u8hdr"];
    };
    "message/rfc822": {
        readonly source: "iana";
        readonly extensions: readonly ["eml", "mime"];
    };
    "message/vnd.wfa.wsc": {
        readonly source: "iana";
        readonly extensions: readonly ["wsc"];
    };
    "model/3mf": {
        readonly source: "iana";
        readonly extensions: readonly ["3mf"];
    };
    "model/gltf+json": {
        readonly source: "iana";
        readonly extensions: readonly ["gltf"];
    };
    "model/gltf-binary": {
        readonly source: "iana";
        readonly extensions: readonly ["glb"];
    };
    "model/iges": {
        readonly source: "iana";
        readonly extensions: readonly ["igs", "iges"];
    };
    "model/mesh": {
        readonly source: "iana";
        readonly extensions: readonly ["msh", "mesh", "silo"];
    };
    "model/mtl": {
        readonly source: "iana";
        readonly extensions: readonly ["mtl"];
    };
    "model/obj": {
        readonly source: "iana";
        readonly extensions: readonly ["obj"];
    };
    "model/step": {
        readonly source: "iana";
        readonly extensions: readonly [".p21", ".stp", ".step", ".stpnc", ".210"];
    };
    "model/step+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["stpx"];
    };
    "model/step+zip": {
        readonly source: "iana";
        readonly extensions: readonly ["stpz"];
    };
    "model/step-xml+zip": {
        readonly source: "iana";
        readonly extensions: readonly ["stpxz"];
    };
    "model/stl": {
        readonly source: "iana";
        readonly extensions: readonly ["stl"];
    };
    "model/vnd.collada+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["dae"];
    };
    "model/vnd.dwf": {
        readonly source: "iana";
        readonly extensions: readonly ["dwf"];
    };
    "model/vnd.gdl": {
        readonly source: "iana";
        readonly extensions: readonly ["gdl"];
    };
    "model/vnd.gtw": {
        readonly source: "iana";
        readonly extensions: readonly ["gtw"];
    };
    "model/vnd.mts": {
        readonly source: "iana";
        readonly extensions: readonly ["mts"];
    };
    "model/vnd.opengex": {
        readonly source: "iana";
        readonly extensions: readonly ["ogex"];
    };
    "model/vnd.parasolid.transmit.binary": {
        readonly source: "iana";
        readonly extensions: readonly ["x_b"];
    };
    "model/vnd.parasolid.transmit.text": {
        readonly source: "iana";
        readonly extensions: readonly ["x_t"];
    };
    "model/vnd.sap.vds": {
        readonly source: "iana";
        readonly extensions: readonly ["vds"];
    };
    "model/vnd.usdz+zip": {
        readonly source: "iana";
        readonly extensions: readonly ["usdz"];
    };
    "model/vnd.valve.source.compiled-map": {
        readonly source: "iana";
        readonly extensions: readonly ["bsp"];
    };
    "model/vnd.vtu": {
        readonly source: "iana";
        readonly extensions: readonly ["vtu"];
    };
    "model/vrml": {
        readonly source: "iana";
        readonly extensions: readonly ["wrl", "vrml"];
    };
    "model/x3d+binary": {
        readonly source: "apache";
        readonly extensions: readonly ["x3db", "x3dbz"];
    };
    "model/x3d+fastinfoset": {
        readonly source: "iana";
        readonly extensions: readonly ["x3db"];
    };
    "model/x3d+vrml": {
        readonly source: "apache";
        readonly extensions: readonly ["x3dv", "x3dvz"];
    };
    "model/x3d+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["x3d", "x3dz"];
    };
    "model/x3d-vrml": {
        readonly source: "iana";
        readonly extensions: readonly ["x3dv"];
    };
    "x-conference/x-cooltalk": {
        readonly source: "apache";
        readonly extensions: readonly ["ice"];
    };
    "video/3gpp": {
        readonly source: "iana";
        readonly extensions: readonly ["3gp", "3gpp"];
    };
    "video/3gpp2": {
        readonly source: "iana";
        readonly extensions: readonly ["3g2"];
    };
    "video/h261": {
        readonly source: "iana";
        readonly extensions: readonly ["h261"];
    };
    "video/h263": {
        readonly source: "iana";
        readonly extensions: readonly ["h263"];
    };
    "video/h264": {
        readonly source: "iana";
        readonly extensions: readonly ["h264"];
    };
    "video/iso.segment": {
        readonly source: "iana";
        readonly extensions: readonly ["m4s"];
    };
    "video/jpeg": {
        readonly source: "iana";
        readonly extensions: readonly ["jpgv"];
    };
    "video/jpm": {
        readonly source: "apache";
        readonly extensions: readonly ["jpm", "jpgm"];
    };
    "video/mj2": {
        readonly source: "iana";
        readonly extensions: readonly ["mj2", "mjp2"];
    };
    "video/mp2t": {
        readonly source: "iana";
        readonly extensions: readonly ["ts"];
    };
    "video/mp4": {
        readonly source: "iana";
        readonly extensions: readonly ["mp4", "mp4v", "mpg4"];
    };
    "video/mpeg": {
        readonly source: "iana";
        readonly extensions: readonly ["mpeg", "mpg", "mpe", "m1v", "m2v"];
    };
    "video/ogg": {
        readonly source: "iana";
        readonly extensions: readonly ["ogv"];
    };
    "video/quicktime": {
        readonly source: "iana";
        readonly extensions: readonly ["qt", "mov"];
    };
    "video/vnd.dece.hd": {
        readonly source: "iana";
        readonly extensions: readonly ["uvh", "uvvh"];
    };
    "video/vnd.dece.mobile": {
        readonly source: "iana";
        readonly extensions: readonly ["uvm", "uvvm"];
    };
    "video/vnd.dece.pd": {
        readonly source: "iana";
        readonly extensions: readonly ["uvp", "uvvp"];
    };
    "video/vnd.dece.sd": {
        readonly source: "iana";
        readonly extensions: readonly ["uvs", "uvvs"];
    };
    "video/vnd.dece.video": {
        readonly source: "iana";
        readonly extensions: readonly ["uvv", "uvvv"];
    };
    "video/vnd.dvb.file": {
        readonly source: "iana";
        readonly extensions: readonly ["dvb"];
    };
    "video/vnd.fvt": {
        readonly source: "iana";
        readonly extensions: readonly ["fvt"];
    };
    "video/vnd.mpegurl": {
        readonly source: "iana";
        readonly extensions: readonly ["mxu", "m4u"];
    };
    "video/vnd.ms-playready.media.pyv": {
        readonly source: "iana";
        readonly extensions: readonly ["pyv"];
    };
    "video/vnd.uvvu.mp4": {
        readonly source: "iana";
        readonly extensions: readonly ["uvu", "uvvu"];
    };
    "video/vnd.vivo": {
        readonly source: "iana";
        readonly extensions: readonly ["viv"];
    };
    "video/webm": {
        readonly source: "apache";
        readonly extensions: readonly ["webm"];
    };
    "video/x-f4v": {
        readonly source: "apache";
        readonly extensions: readonly ["f4v"];
    };
    "video/x-fli": {
        readonly source: "apache";
        readonly extensions: readonly ["fli"];
    };
    "video/x-flv": {
        readonly source: "apache";
        readonly extensions: readonly ["flv"];
    };
    "video/x-m4v": {
        readonly source: "apache";
        readonly extensions: readonly ["m4v"];
    };
    "video/x-matroska": {
        readonly source: "apache";
        readonly extensions: readonly ["mkv", "mk3d", "mks"];
    };
    "video/x-mng": {
        readonly source: "apache";
        readonly extensions: readonly ["mng"];
    };
    "video/x-ms-asf": {
        readonly source: "apache";
        readonly extensions: readonly ["asf", "asx"];
    };
    "video/x-ms-vob": {
        readonly source: "apache";
        readonly extensions: readonly ["vob"];
    };
    "video/x-ms-wm": {
        readonly source: "apache";
        readonly extensions: readonly ["wm"];
    };
    "video/x-ms-wmv": {
        readonly source: "apache";
        readonly extensions: readonly ["wmv"];
    };
    "video/x-ms-wmx": {
        readonly source: "apache";
        readonly extensions: readonly ["wmx"];
    };
    "video/x-ms-wvx": {
        readonly source: "apache";
        readonly extensions: readonly ["wvx"];
    };
    "video/x-msvideo": {
        readonly source: "apache";
        readonly extensions: readonly ["avi"];
    };
    "video/x-sgi-movie": {
        readonly source: "apache";
        readonly extensions: readonly ["movie"];
    };
    "video/x-smv": {
        readonly source: "apache";
        readonly extensions: readonly ["smv"];
    };
    "text/cache-manifest": {
        readonly source: "iana";
        readonly extensions: readonly ["appcache", "manifest"];
    };
    "text/calendar": {
        readonly source: "iana";
        readonly extensions: readonly ["ics", "ifb"];
    };
    "text/css": {
        readonly source: "iana";
        readonly charset: "UTF-8";
        readonly extensions: readonly ["css"];
    };
    "text/csv": {
        readonly source: "iana";
        readonly extensions: readonly ["csv"];
    };
    "text/html": {
        readonly source: "iana";
        readonly extensions: readonly ["html", "htm", "shtml"];
    };
    "text/markdown": {
        readonly source: "iana";
        readonly extensions: readonly ["markdown", "md"];
    };
    "text/mathml": {
        readonly source: "nginx";
        readonly extensions: readonly ["mml"];
    };
    "text/n3": {
        readonly source: "iana";
        readonly charset: "UTF-8";
        readonly extensions: readonly ["n3"];
    };
    "text/plain": {
        readonly source: "iana";
        readonly extensions: readonly ["txt", "text", "conf", "def", "list", "log", "in", "ini"];
    };
    "text/prs.lines.tag": {
        readonly source: "iana";
        readonly extensions: readonly ["dsc"];
    };
    "text/richtext": {
        readonly source: "iana";
        readonly extensions: readonly ["rtx"];
    };
    "text/rtf": {
        readonly source: "iana";
        readonly extensions: readonly ["rtf"];
    };
    "text/sgml": {
        readonly source: "iana";
        readonly extensions: readonly ["sgml", "sgm"];
    };
    "text/shex": {
        readonly source: "iana";
        readonly extensions: readonly ["shex"];
    };
    "text/spdx": {
        readonly source: "iana";
        readonly extensions: readonly ["spdx"];
    };
    "text/tab-separated-values": {
        readonly source: "iana";
        readonly extensions: readonly ["tsv"];
    };
    "text/troff": {
        readonly source: "iana";
        readonly extensions: readonly ["t", "tr", "roff", "man", "me", "ms"];
    };
    "text/turtle": {
        readonly source: "iana";
        readonly charset: "UTF-8";
        readonly extensions: readonly ["ttl"];
    };
    "text/uri-list": {
        readonly source: "iana";
        readonly extensions: readonly ["uri", "uris", "urls"];
    };
    "text/vcard": {
        readonly source: "iana";
        readonly extensions: readonly ["vcard"];
    };
    "text/vnd.curl": {
        readonly source: "iana";
        readonly extensions: readonly ["curl"];
    };
    "text/vnd.curl.dcurl": {
        readonly source: "apache";
        readonly extensions: readonly ["dcurl"];
    };
    "text/vnd.curl.mcurl": {
        readonly source: "apache";
        readonly extensions: readonly ["mcurl"];
    };
    "text/vnd.curl.scurl": {
        readonly source: "apache";
        readonly extensions: readonly ["scurl"];
    };
    "text/vnd.dvb.subtitle": {
        readonly source: "iana";
        readonly extensions: readonly ["sub"];
    };
    "text/vnd.familysearch.gedcom": {
        readonly source: "iana";
        readonly extensions: readonly ["ged"];
    };
    "text/vnd.fly": {
        readonly source: "iana";
        readonly extensions: readonly ["fly"];
    };
    "text/vnd.fmi.flexstor": {
        readonly source: "iana";
        readonly extensions: readonly ["flx"];
    };
    "text/vnd.graphviz": {
        readonly source: "iana";
        readonly extensions: readonly ["gv"];
    };
    "text/vnd.in3d.3dml": {
        readonly source: "iana";
        readonly extensions: readonly ["3dml"];
    };
    "text/vnd.in3d.spot": {
        readonly source: "iana";
        readonly extensions: readonly ["spot"];
    };
    "text/vnd.sun.j2me.app-descriptor": {
        readonly source: "iana";
        readonly charset: "UTF-8";
        readonly extensions: readonly ["jad"];
    };
    "text/vnd.wap.wml": {
        readonly source: "iana";
        readonly extensions: readonly ["wml"];
    };
    "text/vnd.wap.wmlscript": {
        readonly source: "iana";
        readonly extensions: readonly ["wmls"];
    };
    "text/vtt": {
        readonly source: "iana";
        readonly charset: "UTF-8";
        readonly extensions: readonly ["vtt"];
    };
    "text/x-asm": {
        readonly source: "apache";
        readonly extensions: readonly ["s", "asm"];
    };
    "text/x-c": {
        readonly source: "apache";
        readonly extensions: readonly ["c", "cc", "cxx", "cpp", "h", "hh", "dic"];
    };
    "text/x-component": {
        readonly source: "nginx";
        readonly extensions: readonly ["htc"];
    };
    "text/x-fortran": {
        readonly source: "apache";
        readonly extensions: readonly ["f", "for", "f77", "f90"];
    };
    "text/x-java-source": {
        readonly source: "apache";
        readonly extensions: readonly ["java"];
    };
    "text/x-nfo": {
        readonly source: "apache";
        readonly extensions: readonly ["nfo"];
    };
    "text/x-opml": {
        readonly source: "apache";
        readonly extensions: readonly ["opml"];
    };
    "text/x-pascal": {
        readonly source: "apache";
        readonly extensions: readonly ["p", "pas"];
    };
    "text/x-setext": {
        readonly source: "apache";
        readonly extensions: readonly ["etx"];
    };
    "text/x-sfv": {
        readonly source: "apache";
        readonly extensions: readonly ["sfv"];
    };
    "text/x-uuencode": {
        readonly source: "apache";
        readonly extensions: readonly ["uu"];
    };
    "text/x-vcalendar": {
        readonly source: "apache";
        readonly extensions: readonly ["vcs"];
    };
    "text/x-vcard": {
        readonly source: "apache";
        readonly extensions: readonly ["vcf"];
    };
    "text/xml": {
        readonly source: "iana";
        readonly extensions: readonly ["xml"];
    };
    "image/aces": {
        readonly source: "iana";
        readonly extensions: readonly ["exr"];
    };
    "image/avci": {
        readonly source: "iana";
        readonly extensions: readonly ["avci"];
    };
    "image/avcs": {
        readonly source: "iana";
        readonly extensions: readonly ["avcs"];
    };
    "image/avif": {
        readonly source: "iana";
        readonly extensions: readonly ["avif"];
    };
    "image/bmp": {
        readonly source: "iana";
        readonly extensions: readonly ["bmp"];
    };
    "image/cgm": {
        readonly source: "iana";
        readonly extensions: readonly ["cgm"];
    };
    "image/dicom-rle": {
        readonly source: "iana";
        readonly extensions: readonly ["drle"];
    };
    "image/emf": {
        readonly source: "iana";
        readonly extensions: readonly ["emf"];
    };
    "image/fits": {
        readonly source: "iana";
        readonly extensions: readonly ["fits"];
    };
    "image/g3fax": {
        readonly source: "iana";
        readonly extensions: readonly ["g3"];
    };
    "image/gif": {
        readonly source: "iana";
        readonly extensions: readonly ["gif"];
    };
    "image/heic": {
        readonly source: "iana";
        readonly extensions: readonly ["heic"];
    };
    "image/heic-sequence": {
        readonly source: "iana";
        readonly extensions: readonly ["heics"];
    };
    "image/heif": {
        readonly source: "iana";
        readonly extensions: readonly ["heif"];
    };
    "image/heif-sequence": {
        readonly source: "iana";
        readonly extensions: readonly ["heifs"];
    };
    "image/hej2k": {
        readonly source: "iana";
        readonly extensions: readonly ["hej2"];
    };
    "image/hsj2": {
        readonly source: "iana";
        readonly extensions: readonly ["hsj2"];
    };
    "image/ief": {
        readonly source: "iana";
        readonly extensions: readonly ["ief"];
    };
    "image/jls": {
        readonly source: "iana";
        readonly extensions: readonly ["jls"];
    };
    "image/jp2": {
        readonly source: "iana";
        readonly extensions: readonly ["jp2", "jpg2"];
    };
    "image/jpeg": {
        readonly source: "iana";
        readonly extensions: readonly ["jpeg", "jpg", "jpe", "jfif", "pjpeg", "pjp"];
    };
    "image/jph": {
        readonly source: "iana";
        readonly extensions: readonly ["jph"];
    };
    "image/jphc": {
        readonly source: "iana";
        readonly extensions: readonly ["jhc"];
    };
    "image/jpm": {
        readonly source: "iana";
        readonly extensions: readonly ["jpm"];
    };
    "image/jpx": {
        readonly source: "iana";
        readonly extensions: readonly ["jpx", "jpf"];
    };
    "image/jxr": {
        readonly source: "iana";
        readonly extensions: readonly ["jxr"];
    };
    "image/jxra": {
        readonly source: "iana";
        readonly extensions: readonly ["jxra"];
    };
    "image/jxrs": {
        readonly source: "iana";
        readonly extensions: readonly ["jxrs"];
    };
    "image/jxs": {
        readonly source: "iana";
        readonly extensions: readonly ["jxs"];
    };
    "image/jxsc": {
        readonly source: "iana";
        readonly extensions: readonly ["jxsc"];
    };
    "image/jxsi": {
        readonly source: "iana";
        readonly extensions: readonly ["jxsi"];
    };
    "image/jxss": {
        readonly source: "iana";
        readonly extensions: readonly ["jxss"];
    };
    "image/ktx": {
        readonly source: "iana";
        readonly extensions: readonly ["ktx"];
    };
    "image/ktx2": {
        readonly source: "iana";
        readonly extensions: readonly ["ktx2"];
    };
    "image/png": {
        readonly source: "iana";
        readonly extensions: readonly ["png"];
    };
    "image/prs.btif": {
        readonly source: "iana";
        readonly extensions: readonly ["btif"];
    };
    "image/prs.pti": {
        readonly source: "iana";
        readonly extensions: readonly ["pti"];
    };
    "image/sgi": {
        readonly source: "apache";
        readonly extensions: readonly ["sgi"];
    };
    "image/svg+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["svg", "svgz"];
    };
    "image/t38": {
        readonly source: "iana";
        readonly extensions: readonly ["t38"];
    };
    "image/tiff": {
        readonly source: "iana";
        readonly extensions: readonly ["tif", "tiff"];
    };
    "image/tiff-fx": {
        readonly source: "iana";
        readonly extensions: readonly ["tfx"];
    };
    "image/vnd.adobe.photoshop": {
        readonly source: "iana";
        readonly extensions: readonly ["psd"];
    };
    "image/vnd.airzip.accelerator.azv": {
        readonly source: "iana";
        readonly extensions: readonly ["azv"];
    };
    "image/vnd.dece.graphic": {
        readonly source: "iana";
        readonly extensions: readonly ["uvi", "uvvi", "uvg", "uvvg"];
    };
    "image/vnd.djvu": {
        readonly source: "iana";
        readonly extensions: readonly ["djvu", "djv"];
    };
    "image/vnd.dvb.subtitle": {
        readonly source: "iana";
        readonly extensions: readonly ["sub"];
    };
    "image/vnd.dwg": {
        readonly source: "iana";
        readonly extensions: readonly ["dwg"];
    };
    "image/vnd.dxf": {
        readonly source: "iana";
        readonly extensions: readonly ["dxf"];
    };
    "image/vnd.fastbidsheet": {
        readonly source: "iana";
        readonly extensions: readonly ["fbs"];
    };
    "image/vnd.fpx": {
        readonly source: "iana";
        readonly extensions: readonly ["fpx"];
    };
    "image/vnd.fst": {
        readonly source: "iana";
        readonly extensions: readonly ["fst"];
    };
    "image/vnd.fujixerox.edmics-mmr": {
        readonly source: "iana";
        readonly extensions: readonly ["mmr"];
    };
    "image/vnd.fujixerox.edmics-rlc": {
        readonly source: "iana";
        readonly extensions: readonly ["rlc"];
    };
    "image/vnd.microsoft.icon": {
        readonly source: "iana";
        readonly extensions: readonly ["ico"];
    };
    "image/vnd.ms-modi": {
        readonly source: "iana";
        readonly extensions: readonly ["mdi"];
    };
    "image/vnd.ms-photo": {
        readonly source: "apache";
        readonly extensions: readonly ["wdp"];
    };
    "image/vnd.net-fpx": {
        readonly source: "iana";
        readonly extensions: readonly ["npx"];
    };
    "image/vnd.pco.b16": {
        readonly source: "iana";
        readonly extensions: readonly ["b16"];
    };
    "image/vnd.tencent.tap": {
        readonly source: "iana";
        readonly extensions: readonly ["tap"];
    };
    "image/vnd.valve.source.texture": {
        readonly source: "iana";
        readonly extensions: readonly ["vtf"];
    };
    "image/vnd.wap.wbmp": {
        readonly source: "iana";
        readonly extensions: readonly ["wbmp"];
    };
    "image/vnd.xiff": {
        readonly source: "iana";
        readonly extensions: readonly ["xif"];
    };
    "image/vnd.zbrush.pcx": {
        readonly source: "iana";
        readonly extensions: readonly ["pcx"];
    };
    "image/webp": {
        readonly source: "apache";
        readonly extensions: readonly ["webp"];
    };
    "image/wmf": {
        readonly source: "iana";
        readonly extensions: readonly ["wmf"];
    };
    "image/x-3ds": {
        readonly source: "apache";
        readonly extensions: readonly ["3ds"];
    };
    "image/x-cmu-raster": {
        readonly source: "apache";
        readonly extensions: readonly ["ras"];
    };
    "image/x-cmx": {
        readonly source: "apache";
        readonly extensions: readonly ["cmx"];
    };
    "image/x-freehand": {
        readonly source: "apache";
        readonly extensions: readonly ["fh", "fhc", "fh4", "fh5", "fh7"];
    };
    "image/x-icon": {
        readonly source: "apache";
        readonly extensions: readonly ["ico"];
    };
    "image/x-jng": {
        readonly source: "nginx";
        readonly extensions: readonly ["jng"];
    };
    "image/x-mrsid-image": {
        readonly source: "apache";
        readonly extensions: readonly ["sid"];
    };
    "image/x-ms-bmp": {
        readonly source: "nginx";
        readonly extensions: readonly ["bmp"];
    };
    "image/x-pcx": {
        readonly source: "apache";
        readonly extensions: readonly ["pcx"];
    };
    "image/x-pict": {
        readonly source: "apache";
        readonly extensions: readonly ["pic", "pct"];
    };
    "image/x-portable-anymap": {
        readonly source: "apache";
        readonly extensions: readonly ["pnm"];
    };
    "image/x-portable-bitmap": {
        readonly source: "apache";
        readonly extensions: readonly ["pbm"];
    };
    "image/x-portable-graymap": {
        readonly source: "apache";
        readonly extensions: readonly ["pgm"];
    };
    "image/x-portable-pixmap": {
        readonly source: "apache";
        readonly extensions: readonly ["ppm"];
    };
    "image/x-rgb": {
        readonly source: "apache";
        readonly extensions: readonly ["rgb"];
    };
    "image/x-tga": {
        readonly source: "apache";
        readonly extensions: readonly ["tga"];
    };
    "image/x-xbitmap": {
        readonly source: "apache";
        readonly extensions: readonly ["xbm"];
    };
    "image/x-xpixmap": {
        readonly source: "apache";
        readonly extensions: readonly ["xpm"];
    };
    "image/x-xwindowdump": {
        readonly source: "apache";
        readonly extensions: readonly ["xwd"];
    };
    "audio/3gpp": {
        readonly source: "iana";
        readonly extensions: readonly ["3gpp"];
    };
    "audio/adpcm": {
        readonly source: "apache";
        readonly extensions: readonly ["adp"];
    };
    "audio/amr": {
        readonly source: "iana";
        readonly extensions: readonly ["amr"];
    };
    "audio/basic": {
        readonly source: "iana";
        readonly extensions: readonly ["au", "snd"];
    };
    "audio/midi": {
        readonly source: "apache";
        readonly extensions: readonly ["mid", "midi", "kar", "rmi"];
    };
    "audio/mobile-xmf": {
        readonly source: "iana";
        readonly extensions: readonly ["mxmf"];
    };
    "audio/mp4": {
        readonly source: "iana";
        readonly extensions: readonly ["m4a", "mp4a"];
    };
    "audio/mpeg": {
        readonly source: "iana";
        readonly extensions: readonly ["mpga", "mp2", "mp2a", "mp3", "m2a", "m3a"];
    };
    "audio/ogg": {
        readonly source: "iana";
        readonly extensions: readonly ["oga", "ogg", "spx", "opus"];
    };
    "audio/s3m": {
        readonly source: "apache";
        readonly extensions: readonly ["s3m"];
    };
    "audio/silk": {
        readonly source: "apache";
        readonly extensions: readonly ["sil"];
    };
    "audio/vnd.dece.audio": {
        readonly source: "iana";
        readonly extensions: readonly ["uva", "uvva"];
    };
    "audio/vnd.digital-winds": {
        readonly source: "iana";
        readonly extensions: readonly ["eol"];
    };
    "audio/vnd.dra": {
        readonly source: "iana";
        readonly extensions: readonly ["dra"];
    };
    "audio/vnd.dts": {
        readonly source: "iana";
        readonly extensions: readonly ["dts"];
    };
    "audio/vnd.dts.hd": {
        readonly source: "iana";
        readonly extensions: readonly ["dtshd"];
    };
    "audio/vnd.lucent.voice": {
        readonly source: "iana";
        readonly extensions: readonly ["lvp"];
    };
    "audio/vnd.ms-playready.media.pya": {
        readonly source: "iana";
        readonly extensions: readonly ["pya"];
    };
    "audio/vnd.nuera.ecelp4800": {
        readonly source: "iana";
        readonly extensions: readonly ["ecelp4800"];
    };
    "audio/vnd.nuera.ecelp7470": {
        readonly source: "iana";
        readonly extensions: readonly ["ecelp7470"];
    };
    "audio/vnd.nuera.ecelp9600": {
        readonly source: "iana";
        readonly extensions: readonly ["ecelp9600"];
    };
    "audio/vnd.rip": {
        readonly source: "iana";
        readonly extensions: readonly ["rip"];
    };
    "audio/webm": {
        readonly source: "apache";
        readonly extensions: readonly ["weba"];
    };
    "audio/x-aac": {
        readonly source: "apache";
        readonly extensions: readonly ["aac"];
    };
    "audio/x-aiff": {
        readonly source: "apache";
        readonly extensions: readonly ["aif", "aiff", "aifc"];
    };
    "audio/x-caf": {
        readonly source: "apache";
        readonly extensions: readonly ["caf"];
    };
    "audio/x-flac": {
        readonly source: "apache";
        readonly extensions: readonly ["flac"];
    };
    "audio/x-m4a": {
        readonly source: "nginx";
        readonly extensions: readonly ["m4a"];
    };
    "audio/x-matroska": {
        readonly source: "apache";
        readonly extensions: readonly ["mka"];
    };
    "audio/x-mpegurl": {
        readonly source: "apache";
        readonly extensions: readonly ["m3u"];
    };
    "audio/x-ms-wax": {
        readonly source: "apache";
        readonly extensions: readonly ["wax"];
    };
    "audio/x-ms-wma": {
        readonly source: "apache";
        readonly extensions: readonly ["wma"];
    };
    "audio/x-pn-realaudio": {
        readonly source: "apache";
        readonly extensions: readonly ["ram", "ra"];
    };
    "audio/x-pn-realaudio-plugin": {
        readonly source: "apache";
        readonly extensions: readonly ["rmp"];
    };
    "audio/x-realaudio": {
        readonly source: "nginx";
        readonly extensions: readonly ["ra"];
    };
    "audio/x-wav": {
        readonly source: "apache";
        readonly extensions: readonly ["wav"];
    };
    "audio/x-gsm": {
        readonly source: "apache";
        readonly extensions: readonly ["gsm"];
    };
    "audio/xm": {
        readonly source: "apache";
        readonly extensions: readonly ["xm"];
    };
    "application/andrew-inset": {
        readonly source: "iana";
        readonly extensions: readonly ["ez"];
    };
    "application/applixware": {
        readonly source: "apache";
        readonly extensions: readonly ["aw"];
    };
    "application/atom+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["atom"];
    };
    "application/atomcat+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["atomcat"];
    };
    "application/atomdeleted+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["atomdeleted"];
    };
    "application/atomsvc+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["atomsvc"];
    };
    "application/atsc-dwd+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["dwd"];
    };
    "application/atsc-held+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["held"];
    };
    "application/atsc-rsat+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["rsat"];
    };
    "application/calendar+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["xcs"];
    };
    "application/ccxml+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["ccxml"];
    };
    "application/cdfx+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["cdfx"];
    };
    "application/cdmi-capability": {
        readonly source: "iana";
        readonly extensions: readonly ["cdmia"];
    };
    "application/cdmi-container": {
        readonly source: "iana";
        readonly extensions: readonly ["cdmic"];
    };
    "application/cdmi-domain": {
        readonly source: "iana";
        readonly extensions: readonly ["cdmid"];
    };
    "application/cdmi-object": {
        readonly source: "iana";
        readonly extensions: readonly ["cdmio"];
    };
    "application/cdmi-queue": {
        readonly source: "iana";
        readonly extensions: readonly ["cdmiq"];
    };
    "application/cpl+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["cpl"];
    };
    "application/cu-seeme": {
        readonly source: "apache";
        readonly extensions: readonly ["cu"];
    };
    "application/dash+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["mpd"];
    };
    "application/dash-patch+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["mpp"];
    };
    "application/davmount+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["davmount"];
    };
    "application/dicom": {
        readonly source: "iana";
        readonly extensions: readonly ["dcm"];
    };
    "application/docbook+xml": {
        readonly source: "apache";
        readonly extensions: readonly ["dbk"];
    };
    "application/dssc+der": {
        readonly source: "iana";
        readonly extensions: readonly ["dssc"];
    };
    "application/dssc+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["xdssc"];
    };
    "application/ecmascript": {
        readonly source: "iana";
        readonly extensions: readonly ["es", "ecma"];
    };
    "application/emma+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["emma"];
    };
    "application/emotionml+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["emotionml"];
    };
    "application/epub+zip": {
        readonly source: "iana";
        readonly extensions: readonly ["epub"];
    };
    "application/exi": {
        readonly source: "iana";
        readonly extensions: readonly ["exi"];
    };
    "application/express": {
        readonly source: "iana";
        readonly extensions: readonly ["exp"];
    };
    "application/fdt+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["fdt"];
    };
    "application/font-tdpfr": {
        readonly source: "iana";
        readonly extensions: readonly ["pfr"];
    };
    "application/geo+json": {
        readonly source: "iana";
        readonly extensions: readonly ["geojson"];
    };
    "application/gml+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["gml"];
    };
    "application/gpx+xml": {
        readonly source: "apache";
        readonly extensions: readonly ["gpx"];
    };
    "application/gxf": {
        readonly source: "apache";
        readonly extensions: readonly ["gxf"];
    };
    "application/gzip": {
        readonly source: "iana";
        readonly extensions: readonly ["gz"];
    };
    "application/hyperstudio": {
        readonly source: "iana";
        readonly extensions: readonly ["stk"];
    };
    "application/inkml+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["ink", "inkml"];
    };
    "application/ipfix": {
        readonly source: "iana";
        readonly extensions: readonly ["ipfix"];
    };
    "application/its+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["its"];
    };
    "application/java-archive": {
        readonly source: "apache";
        readonly extensions: readonly ["jar", "war", "ear"];
    };
    "application/java-serialized-object": {
        readonly source: "apache";
        readonly extensions: readonly ["ser"];
    };
    "application/java-vm": {
        readonly source: "apache";
        readonly extensions: readonly ["class"];
    };
    "application/javascript": {
        readonly source: "iana";
        readonly charset: "UTF-8";
        readonly extensions: readonly ["js", "mjs"];
    };
    "application/json": {
        readonly source: "iana";
        readonly charset: "UTF-8";
        readonly extensions: readonly ["json", "map"];
    };
    "application/jsonml+json": {
        readonly source: "apache";
        readonly extensions: readonly ["jsonml"];
    };
    "application/ld+json": {
        readonly source: "iana";
        readonly extensions: readonly ["jsonld"];
    };
    "application/lgr+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["lgr"];
    };
    "application/lost+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["lostxml"];
    };
    "application/mac-binhex40": {
        readonly source: "iana";
        readonly extensions: readonly ["hqx"];
    };
    "application/mac-compactpro": {
        readonly source: "apache";
        readonly extensions: readonly ["cpt"];
    };
    "application/mads+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["mads"];
    };
    "application/manifest+json": {
        readonly source: "iana";
        readonly charset: "UTF-8";
        readonly extensions: readonly ["webmanifest"];
    };
    "application/marc": {
        readonly source: "iana";
        readonly extensions: readonly ["mrc"];
    };
    "application/marcxml+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["mrcx"];
    };
    "application/mathematica": {
        readonly source: "iana";
        readonly extensions: readonly ["ma", "nb", "mb"];
    };
    "application/mathml+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["mathml"];
    };
    "application/mbox": {
        readonly source: "iana";
        readonly extensions: readonly ["mbox"];
    };
    "application/media-policy-dataset+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["mpf"];
    };
    "application/mediaservercontrol+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["mscml"];
    };
    "application/metalink+xml": {
        readonly source: "apache";
        readonly extensions: readonly ["metalink"];
    };
    "application/metalink4+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["meta4"];
    };
    "application/mets+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["mets"];
    };
    "application/mmt-aei+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["maei"];
    };
    "application/mmt-usd+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["musd"];
    };
    "application/mods+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["mods"];
    };
    "application/mp21": {
        readonly source: "iana";
        readonly extensions: readonly ["m21", "mp21"];
    };
    "application/mp4": {
        readonly source: "iana";
        readonly extensions: readonly ["mp4s", "m4p"];
    };
    "application/msword": {
        readonly source: "iana";
        readonly extensions: readonly ["doc", "dot"];
    };
    "application/mxf": {
        readonly source: "iana";
        readonly extensions: readonly ["mxf"];
    };
    "application/n-quads": {
        readonly source: "iana";
        readonly extensions: readonly ["nq"];
    };
    "application/n-triples": {
        readonly source: "iana";
        readonly extensions: readonly ["nt"];
    };
    "application/node": {
        readonly source: "iana";
        readonly extensions: readonly ["cjs"];
    };
    "application/octet-stream": {
        readonly source: "iana";
        readonly extensions: readonly ["bin", "dms", "lrf", "mar", "so", "dist", "distz", "pkg", "bpk", "dump", "elc", "deploy", "exe", "dll", "deb", "dmg", "iso", "img", "msi", "msp", "msm", "buffer"];
    };
    "application/oda": {
        readonly source: "iana";
        readonly extensions: readonly ["oda"];
    };
    "application/oebps-package+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["opf"];
    };
    "application/ogg": {
        readonly source: "iana";
        readonly extensions: readonly ["ogx"];
    };
    "application/omdoc+xml": {
        readonly source: "apache";
        readonly extensions: readonly ["omdoc"];
    };
    "application/onenote": {
        readonly source: "apache";
        readonly extensions: readonly ["onetoc", "onetoc2", "onetmp", "onepkg"];
    };
    "application/oxps": {
        readonly source: "iana";
        readonly extensions: readonly ["oxps"];
    };
    "application/p2p-overlay+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["relo"];
    };
    "application/patch-ops-error+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["xer"];
    };
    "application/pdf": {
        readonly source: "iana";
        readonly extensions: readonly ["pdf"];
    };
    "application/pgp-encrypted": {
        readonly source: "iana";
        readonly extensions: readonly ["pgp"];
    };
    "application/pgp-keys": {
        readonly source: "iana";
        readonly extensions: readonly ["asc"];
    };
    "application/pgp-signature": {
        readonly source: "iana";
        readonly extensions: readonly ["asc", "sig"];
    };
    "application/pics-rules": {
        readonly source: "apache";
        readonly extensions: readonly ["prf"];
    };
    "application/pkcs10": {
        readonly source: "iana";
        readonly extensions: readonly ["p10"];
    };
    "application/pkcs7-mime": {
        readonly source: "iana";
        readonly extensions: readonly ["p7m", "p7c"];
    };
    "application/pkcs7-signature": {
        readonly source: "iana";
        readonly extensions: readonly ["p7s"];
    };
    "application/pkcs8": {
        readonly source: "iana";
        readonly extensions: readonly ["p8"];
    };
    "application/pkix-attr-cert": {
        readonly source: "iana";
        readonly extensions: readonly ["ac"];
    };
    "application/pkix-cert": {
        readonly source: "iana";
        readonly extensions: readonly ["cer"];
    };
    "application/pkix-crl": {
        readonly source: "iana";
        readonly extensions: readonly ["crl"];
    };
    "application/pkix-pkipath": {
        readonly source: "iana";
        readonly extensions: readonly ["pkipath"];
    };
    "application/pkixcmp": {
        readonly source: "iana";
        readonly extensions: readonly ["pki"];
    };
    "application/pls+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["pls"];
    };
    "application/postscript": {
        readonly source: "iana";
        readonly extensions: readonly ["ai", "eps", "ps"];
    };
    "application/provenance+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["provx"];
    };
    "application/prs.cww": {
        readonly source: "iana";
        readonly extensions: readonly ["cww"];
    };
    "application/pskc+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["pskcxml"];
    };
    "application/rdf+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["rdf", "owl"];
    };
    "application/reginfo+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["rif"];
    };
    "application/relax-ng-compact-syntax": {
        readonly source: "iana";
        readonly extensions: readonly ["rnc"];
    };
    "application/resource-lists+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["rl"];
    };
    "application/resource-lists-diff+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["rld"];
    };
    "application/rls-services+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["rs"];
    };
    "application/route-apd+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["rapd"];
    };
    "application/route-s-tsid+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["sls"];
    };
    "application/route-usd+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["rusd"];
    };
    "application/rpki-ghostbusters": {
        readonly source: "iana";
        readonly extensions: readonly ["gbr"];
    };
    "application/rpki-manifest": {
        readonly source: "iana";
        readonly extensions: readonly ["mft"];
    };
    "application/rpki-roa": {
        readonly source: "iana";
        readonly extensions: readonly ["roa"];
    };
    "application/rsd+xml": {
        readonly source: "apache";
        readonly extensions: readonly ["rsd"];
    };
    "application/rss+xml": {
        readonly source: "apache";
        readonly extensions: readonly ["rss"];
    };
    "application/rtf": {
        readonly source: "iana";
        readonly extensions: readonly ["rtf"];
    };
    "application/sbml+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["sbml"];
    };
    "application/scvp-cv-request": {
        readonly source: "iana";
        readonly extensions: readonly ["scq"];
    };
    "application/scvp-cv-response": {
        readonly source: "iana";
        readonly extensions: readonly ["scs"];
    };
    "application/scvp-vp-request": {
        readonly source: "iana";
        readonly extensions: readonly ["spq"];
    };
    "application/scvp-vp-response": {
        readonly source: "iana";
        readonly extensions: readonly ["spp"];
    };
    "application/sdp": {
        readonly source: "iana";
        readonly extensions: readonly ["sdp"];
    };
    "application/senml+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["senmlx"];
    };
    "application/sensml+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["sensmlx"];
    };
    "application/set-payment-initiation": {
        readonly source: "iana";
        readonly extensions: readonly ["setpay"];
    };
    "application/set-registration-initiation": {
        readonly source: "iana";
        readonly extensions: readonly ["setreg"];
    };
    "application/shf+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["shf"];
    };
    "application/sieve": {
        readonly source: "iana";
        readonly extensions: readonly ["siv", "sieve"];
    };
    "application/smil+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["smi", "smil"];
    };
    "application/sparql-query": {
        readonly source: "iana";
        readonly extensions: readonly ["rq"];
    };
    "application/sparql-results+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["srx"];
    };
    "application/srgs": {
        readonly source: "iana";
        readonly extensions: readonly ["gram"];
    };
    "application/srgs+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["grxml"];
    };
    "application/sru+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["sru"];
    };
    "application/ssdl+xml": {
        readonly source: "apache";
        readonly extensions: readonly ["ssdl"];
    };
    "application/ssml+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["ssml"];
    };
    "application/swid+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["swidtag"];
    };
    "application/tei+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["tei", "teicorpus"];
    };
    "application/thraud+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["tfi"];
    };
    "application/timestamped-data": {
        readonly source: "iana";
        readonly extensions: readonly ["tsd"];
    };
    "application/trig": {
        readonly source: "iana";
        readonly extensions: readonly ["trig"];
    };
    "application/ttml+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["ttml"];
    };
    "application/urc-ressheet+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["rsheet"];
    };
    "application/urc-targetdesc+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["td"];
    };
    "application/vnd.1000minds.decision-model+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["1km"];
    };
    "application/vnd.3gpp.pic-bw-large": {
        readonly source: "iana";
        readonly extensions: readonly ["plb"];
    };
    "application/vnd.3gpp.pic-bw-small": {
        readonly source: "iana";
        readonly extensions: readonly ["psb"];
    };
    "application/vnd.3gpp.pic-bw-var": {
        readonly source: "iana";
        readonly extensions: readonly ["pvb"];
    };
    "application/vnd.3gpp2.tcap": {
        readonly source: "iana";
        readonly extensions: readonly ["tcap"];
    };
    "application/vnd.3m.post-it-notes": {
        readonly source: "iana";
        readonly extensions: readonly ["pwn"];
    };
    "application/vnd.accpac.simply.aso": {
        readonly source: "iana";
        readonly extensions: readonly ["aso"];
    };
    "application/vnd.accpac.simply.imp": {
        readonly source: "iana";
        readonly extensions: readonly ["imp"];
    };
    "application/vnd.acucobol": {
        readonly source: "iana";
        readonly extensions: readonly ["acu"];
    };
    "application/vnd.acucorp": {
        readonly source: "iana";
        readonly extensions: readonly ["atc", "acutc"];
    };
    "application/vnd.adobe.air-application-installer-package+zip": {
        readonly source: "apache";
        readonly extensions: readonly ["air"];
    };
    "application/vnd.adobe.formscentral.fcdt": {
        readonly source: "iana";
        readonly extensions: readonly ["fcdt"];
    };
    "application/vnd.adobe.fxp": {
        readonly source: "iana";
        readonly extensions: readonly ["fxp", "fxpl"];
    };
    "application/vnd.adobe.xdp+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["xdp"];
    };
    "application/vnd.adobe.xfdf": {
        readonly source: "iana";
        readonly extensions: readonly ["xfdf"];
    };
    "application/vnd.age": {
        readonly source: "iana";
        readonly extensions: readonly ["age"];
    };
    "application/vnd.ahead.space": {
        readonly source: "iana";
        readonly extensions: readonly ["ahead"];
    };
    "application/vnd.airzip.filesecure.azf": {
        readonly source: "iana";
        readonly extensions: readonly ["azf"];
    };
    "application/vnd.airzip.filesecure.azs": {
        readonly source: "iana";
        readonly extensions: readonly ["azs"];
    };
    "application/vnd.amazon.ebook": {
        readonly source: "apache";
        readonly extensions: readonly ["azw"];
    };
    "application/vnd.americandynamics.acc": {
        readonly source: "iana";
        readonly extensions: readonly ["acc"];
    };
    "application/vnd.amiga.ami": {
        readonly source: "iana";
        readonly extensions: readonly ["ami"];
    };
    "application/vnd.android.package-archive": {
        readonly source: "apache";
        readonly extensions: readonly ["apk"];
    };
    "application/vnd.anser-web-certificate-issue-initiation": {
        readonly source: "iana";
        readonly extensions: readonly ["cii"];
    };
    "application/vnd.anser-web-funds-transfer-initiation": {
        readonly source: "apache";
        readonly extensions: readonly ["fti"];
    };
    "application/vnd.antix.game-component": {
        readonly source: "iana";
        readonly extensions: readonly ["atx"];
    };
    "application/vnd.apple.installer+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["mpkg"];
    };
    "application/vnd.apple.keynote": {
        readonly source: "iana";
        readonly extensions: readonly ["key"];
    };
    "application/vnd.apple.mpegurl": {
        readonly source: "iana";
        readonly extensions: readonly ["m3u8"];
    };
    "application/vnd.apple.numbers": {
        readonly source: "iana";
        readonly extensions: readonly ["numbers"];
    };
    "application/vnd.apple.pages": {
        readonly source: "iana";
        readonly extensions: readonly ["pages"];
    };
    "application/vnd.aristanetworks.swi": {
        readonly source: "iana";
        readonly extensions: readonly ["swi"];
    };
    "application/vnd.astraea-software.iota": {
        readonly source: "iana";
        readonly extensions: readonly ["iota"];
    };
    "application/vnd.audiograph": {
        readonly source: "iana";
        readonly extensions: readonly ["aep"];
    };
    "application/vnd.balsamiq.bmml+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["bmml"];
    };
    "application/vnd.blueice.multipass": {
        readonly source: "iana";
        readonly extensions: readonly ["mpm"];
    };
    "application/vnd.bmi": {
        readonly source: "iana";
        readonly extensions: readonly ["bmi"];
    };
    "application/vnd.businessobjects": {
        readonly source: "iana";
        readonly extensions: readonly ["rep"];
    };
    "application/vnd.chemdraw+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["cdxml"];
    };
    "application/vnd.chipnuts.karaoke-mmd": {
        readonly source: "iana";
        readonly extensions: readonly ["mmd"];
    };
    "application/vnd.cinderella": {
        readonly source: "iana";
        readonly extensions: readonly ["cdy"];
    };
    "application/vnd.citationstyles.style+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["csl"];
    };
    "application/vnd.claymore": {
        readonly source: "iana";
        readonly extensions: readonly ["cla"];
    };
    "application/vnd.cloanto.rp9": {
        readonly source: "iana";
        readonly extensions: readonly ["rp9"];
    };
    "application/vnd.clonk.c4group": {
        readonly source: "iana";
        readonly extensions: readonly ["c4g", "c4d", "c4f", "c4p", "c4u"];
    };
    "application/vnd.cluetrust.cartomobile-config": {
        readonly source: "iana";
        readonly extensions: readonly ["c11amc"];
    };
    "application/vnd.cluetrust.cartomobile-config-pkg": {
        readonly source: "iana";
        readonly extensions: readonly ["c11amz"];
    };
    "application/vnd.commonspace": {
        readonly source: "iana";
        readonly extensions: readonly ["csp"];
    };
    "application/vnd.contact.cmsg": {
        readonly source: "iana";
        readonly extensions: readonly ["cdbcmsg"];
    };
    "application/vnd.cosmocaller": {
        readonly source: "iana";
        readonly extensions: readonly ["cmc"];
    };
    "application/vnd.crick.clicker": {
        readonly source: "iana";
        readonly extensions: readonly ["clkx"];
    };
    "application/vnd.crick.clicker.keyboard": {
        readonly source: "iana";
        readonly extensions: readonly ["clkk"];
    };
    "application/vnd.crick.clicker.palette": {
        readonly source: "iana";
        readonly extensions: readonly ["clkp"];
    };
    "application/vnd.crick.clicker.template": {
        readonly source: "iana";
        readonly extensions: readonly ["clkt"];
    };
    "application/vnd.crick.clicker.wordbank": {
        readonly source: "iana";
        readonly extensions: readonly ["clkw"];
    };
    "application/vnd.criticaltools.wbs+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["wbs"];
    };
    "application/vnd.ctc-posml": {
        readonly source: "iana";
        readonly extensions: readonly ["pml"];
    };
    "application/vnd.cups-ppd": {
        readonly source: "iana";
        readonly extensions: readonly ["ppd"];
    };
    "application/vnd.curl.car": {
        readonly source: "apache";
        readonly extensions: readonly ["car"];
    };
    "application/vnd.curl.pcurl": {
        readonly source: "apache";
        readonly extensions: readonly ["pcurl"];
    };
    "application/vnd.dart": {
        readonly source: "iana";
        readonly extensions: readonly ["dart"];
    };
    "application/vnd.data-vision.rdz": {
        readonly source: "iana";
        readonly extensions: readonly ["rdz"];
    };
    "application/vnd.dbf": {
        readonly source: "iana";
        readonly extensions: readonly ["dbf"];
    };
    "application/vnd.dece.data": {
        readonly source: "iana";
        readonly extensions: readonly ["uvf", "uvvf", "uvd", "uvvd"];
    };
    "application/vnd.dece.ttml+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["uvt", "uvvt"];
    };
    "application/vnd.dece.unspecified": {
        readonly source: "iana";
        readonly extensions: readonly ["uvx", "uvvx"];
    };
    "application/vnd.dece.zip": {
        readonly source: "iana";
        readonly extensions: readonly ["uvz", "uvvz"];
    };
    "application/vnd.denovo.fcselayout-link": {
        readonly source: "iana";
        readonly extensions: readonly ["fe_launch"];
    };
    "application/vnd.dna": {
        readonly source: "iana";
        readonly extensions: readonly ["dna"];
    };
    "application/vnd.dolby.mlp": {
        readonly source: "apache";
        readonly extensions: readonly ["mlp"];
    };
    "application/vnd.dpgraph": {
        readonly source: "iana";
        readonly extensions: readonly ["dpg"];
    };
    "application/vnd.dreamfactory": {
        readonly source: "iana";
        readonly extensions: readonly ["dfac"];
    };
    "application/vnd.ds-keypoint": {
        readonly source: "apache";
        readonly extensions: readonly ["kpxx"];
    };
    "application/vnd.dvb.ait": {
        readonly source: "iana";
        readonly extensions: readonly ["ait"];
    };
    "application/vnd.dvb.service": {
        readonly source: "iana";
        readonly extensions: readonly ["svc"];
    };
    "application/vnd.dynageo": {
        readonly source: "iana";
        readonly extensions: readonly ["geo"];
    };
    "application/vnd.ecowin.chart": {
        readonly source: "iana";
        readonly extensions: readonly ["mag"];
    };
    "application/vnd.enliven": {
        readonly source: "iana";
        readonly extensions: readonly ["nml"];
    };
    "application/vnd.epson.esf": {
        readonly source: "iana";
        readonly extensions: readonly ["esf"];
    };
    "application/vnd.epson.msf": {
        readonly source: "iana";
        readonly extensions: readonly ["msf"];
    };
    "application/vnd.epson.quickanime": {
        readonly source: "iana";
        readonly extensions: readonly ["qam"];
    };
    "application/vnd.epson.salt": {
        readonly source: "iana";
        readonly extensions: readonly ["slt"];
    };
    "application/vnd.epson.ssf": {
        readonly source: "iana";
        readonly extensions: readonly ["ssf"];
    };
    "application/vnd.eszigno3+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["es3", "et3"];
    };
    "application/vnd.ezpix-album": {
        readonly source: "iana";
        readonly extensions: readonly ["ez2"];
    };
    "application/vnd.ezpix-package": {
        readonly source: "iana";
        readonly extensions: readonly ["ez3"];
    };
    "application/vnd.fdf": {
        readonly source: "iana";
        readonly extensions: readonly ["fdf"];
    };
    "application/vnd.fdsn.mseed": {
        readonly source: "iana";
        readonly extensions: readonly ["mseed"];
    };
    "application/vnd.fdsn.seed": {
        readonly source: "iana";
        readonly extensions: readonly ["seed", "dataless"];
    };
    "application/vnd.flographit": {
        readonly source: "iana";
        readonly extensions: readonly ["gph"];
    };
    "application/vnd.fluxtime.clip": {
        readonly source: "iana";
        readonly extensions: readonly ["ftc"];
    };
    "application/vnd.framemaker": {
        readonly source: "iana";
        readonly extensions: readonly ["fm", "frame", "maker", "book"];
    };
    "application/vnd.frogans.fnc": {
        readonly source: "iana";
        readonly extensions: readonly ["fnc"];
    };
    "application/vnd.frogans.ltf": {
        readonly source: "iana";
        readonly extensions: readonly ["ltf"];
    };
    "application/vnd.fsc.weblaunch": {
        readonly source: "iana";
        readonly extensions: readonly ["fsc"];
    };
    "application/vnd.fujitsu.oasys": {
        readonly source: "iana";
        readonly extensions: readonly ["oas"];
    };
    "application/vnd.fujitsu.oasys2": {
        readonly source: "iana";
        readonly extensions: readonly ["oa2"];
    };
    "application/vnd.fujitsu.oasys3": {
        readonly source: "iana";
        readonly extensions: readonly ["oa3"];
    };
    "application/vnd.fujitsu.oasysgp": {
        readonly source: "iana";
        readonly extensions: readonly ["fg5"];
    };
    "application/vnd.fujitsu.oasysprs": {
        readonly source: "iana";
        readonly extensions: readonly ["bh2"];
    };
    "application/vnd.fujixerox.ddd": {
        readonly source: "iana";
        readonly extensions: readonly ["ddd"];
    };
    "application/vnd.fujixerox.docuworks": {
        readonly source: "iana";
        readonly extensions: readonly ["xdw"];
    };
    "application/vnd.fujixerox.docuworks.binder": {
        readonly source: "iana";
        readonly extensions: readonly ["xbd"];
    };
    "application/vnd.fuzzysheet": {
        readonly source: "iana";
        readonly extensions: readonly ["fzs"];
    };
    "application/vnd.genomatix.tuxedo": {
        readonly source: "iana";
        readonly extensions: readonly ["txd"];
    };
    "application/vnd.geogebra.file": {
        readonly source: "iana";
        readonly extensions: readonly ["ggb"];
    };
    "application/vnd.geogebra.tool": {
        readonly source: "iana";
        readonly extensions: readonly ["ggt"];
    };
    "application/vnd.geometry-explorer": {
        readonly source: "iana";
        readonly extensions: readonly ["gex", "gre"];
    };
    "application/vnd.geonext": {
        readonly source: "iana";
        readonly extensions: readonly ["gxt"];
    };
    "application/vnd.geoplan": {
        readonly source: "iana";
        readonly extensions: readonly ["g2w"];
    };
    "application/vnd.geospace": {
        readonly source: "iana";
        readonly extensions: readonly ["g3w"];
    };
    "application/vnd.gmx": {
        readonly source: "iana";
        readonly extensions: readonly ["gmx"];
    };
    "application/vnd.google-earth.kml+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["kml"];
    };
    "application/vnd.google-earth.kmz": {
        readonly source: "iana";
        readonly extensions: readonly ["kmz"];
    };
    "application/vnd.grafeq": {
        readonly source: "iana";
        readonly extensions: readonly ["gqf", "gqs"];
    };
    "application/vnd.groove-account": {
        readonly source: "iana";
        readonly extensions: readonly ["gac"];
    };
    "application/vnd.groove-help": {
        readonly source: "iana";
        readonly extensions: readonly ["ghf"];
    };
    "application/vnd.groove-identity-message": {
        readonly source: "iana";
        readonly extensions: readonly ["gim"];
    };
    "application/vnd.groove-injector": {
        readonly source: "iana";
        readonly extensions: readonly ["grv"];
    };
    "application/vnd.groove-tool-message": {
        readonly source: "iana";
        readonly extensions: readonly ["gtm"];
    };
    "application/vnd.groove-tool-template": {
        readonly source: "iana";
        readonly extensions: readonly ["tpl"];
    };
    "application/vnd.groove-vcard": {
        readonly source: "iana";
        readonly extensions: readonly ["vcg"];
    };
    "application/vnd.hal+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["hal"];
    };
    "application/vnd.handheld-entertainment+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["zmm"];
    };
    "application/vnd.hbci": {
        readonly source: "iana";
        readonly extensions: readonly ["hbci"];
    };
    "application/vnd.hhe.lesson-player": {
        readonly source: "iana";
        readonly extensions: readonly ["les"];
    };
    "application/vnd.hp-hpgl": {
        readonly source: "iana";
        readonly extensions: readonly ["hpgl"];
    };
    "application/vnd.hp-hpid": {
        readonly source: "iana";
        readonly extensions: readonly ["hpid"];
    };
    "application/vnd.hp-hps": {
        readonly source: "iana";
        readonly extensions: readonly ["hps"];
    };
    "application/vnd.hp-jlyt": {
        readonly source: "iana";
        readonly extensions: readonly ["jlt"];
    };
    "application/vnd.hp-pcl": {
        readonly source: "iana";
        readonly extensions: readonly ["pcl"];
    };
    "application/vnd.hp-pclxl": {
        readonly source: "iana";
        readonly extensions: readonly ["pclxl"];
    };
    "application/vnd.hydrostatix.sof-data": {
        readonly source: "iana";
        readonly extensions: readonly ["sfd-hdstx"];
    };
    "application/vnd.ibm.minipay": {
        readonly source: "iana";
        readonly extensions: readonly ["mpy"];
    };
    "application/vnd.ibm.modcap": {
        readonly source: "iana";
        readonly extensions: readonly ["afp", "listafp", "list3820"];
    };
    "application/vnd.ibm.rights-management": {
        readonly source: "iana";
        readonly extensions: readonly ["irm"];
    };
    "application/vnd.ibm.secure-container": {
        readonly source: "iana";
        readonly extensions: readonly ["sc"];
    };
    "application/vnd.iccprofile": {
        readonly source: "iana";
        readonly extensions: readonly ["icc", "icm"];
    };
    "application/vnd.igloader": {
        readonly source: "iana";
        readonly extensions: readonly ["igl"];
    };
    "application/vnd.immervision-ivp": {
        readonly source: "iana";
        readonly extensions: readonly ["ivp"];
    };
    "application/vnd.immervision-ivu": {
        readonly source: "iana";
        readonly extensions: readonly ["ivu"];
    };
    "application/vnd.insors.igm": {
        readonly source: "iana";
        readonly extensions: readonly ["igm"];
    };
    "application/vnd.intercon.formnet": {
        readonly source: "iana";
        readonly extensions: readonly ["xpw", "xpx"];
    };
    "application/vnd.intergeo": {
        readonly source: "iana";
        readonly extensions: readonly ["i2g"];
    };
    "application/vnd.intu.qbo": {
        readonly source: "iana";
        readonly extensions: readonly ["qbo"];
    };
    "application/vnd.intu.qfx": {
        readonly source: "iana";
        readonly extensions: readonly ["qfx"];
    };
    "application/vnd.ipunplugged.rcprofile": {
        readonly source: "iana";
        readonly extensions: readonly ["rcprofile"];
    };
    "application/vnd.irepository.package+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["irp"];
    };
    "application/vnd.is-xpr": {
        readonly source: "iana";
        readonly extensions: readonly ["xpr"];
    };
    "application/vnd.isac.fcs": {
        readonly source: "iana";
        readonly extensions: readonly ["fcs"];
    };
    "application/vnd.jam": {
        readonly source: "iana";
        readonly extensions: readonly ["jam"];
    };
    "application/vnd.jcp.javame.midlet-rms": {
        readonly source: "iana";
        readonly extensions: readonly ["rms"];
    };
    "application/vnd.jisp": {
        readonly source: "iana";
        readonly extensions: readonly ["jisp"];
    };
    "application/vnd.joost.joda-archive": {
        readonly source: "iana";
        readonly extensions: readonly ["joda"];
    };
    "application/vnd.kahootz": {
        readonly source: "iana";
        readonly extensions: readonly ["ktz", "ktr"];
    };
    "application/vnd.kde.karbon": {
        readonly source: "iana";
        readonly extensions: readonly ["karbon"];
    };
    "application/vnd.kde.kchart": {
        readonly source: "iana";
        readonly extensions: readonly ["chrt"];
    };
    "application/vnd.kde.kformula": {
        readonly source: "iana";
        readonly extensions: readonly ["kfo"];
    };
    "application/vnd.kde.kivio": {
        readonly source: "iana";
        readonly extensions: readonly ["flw"];
    };
    "application/vnd.kde.kontour": {
        readonly source: "iana";
        readonly extensions: readonly ["kon"];
    };
    "application/vnd.kde.kpresenter": {
        readonly source: "iana";
        readonly extensions: readonly ["kpr", "kpt"];
    };
    "application/vnd.kde.kspread": {
        readonly source: "iana";
        readonly extensions: readonly ["ksp"];
    };
    "application/vnd.kde.kword": {
        readonly source: "iana";
        readonly extensions: readonly ["kwd", "kwt"];
    };
    "application/vnd.kenameaapp": {
        readonly source: "iana";
        readonly extensions: readonly ["htke"];
    };
    "application/vnd.kidspiration": {
        readonly source: "iana";
        readonly extensions: readonly ["kia"];
    };
    "application/vnd.kinar": {
        readonly source: "iana";
        readonly extensions: readonly ["kne", "knp"];
    };
    "application/vnd.koan": {
        readonly source: "iana";
        readonly extensions: readonly ["skp", "skd", "skt", "skm"];
    };
    "application/vnd.kodak-descriptor": {
        readonly source: "iana";
        readonly extensions: readonly ["sse"];
    };
    "application/vnd.las.las+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["lasxml"];
    };
    "application/vnd.llamagraphics.life-balance.desktop": {
        readonly source: "iana";
        readonly extensions: readonly ["lbd"];
    };
    "application/vnd.llamagraphics.life-balance.exchange+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["lbe"];
    };
    "application/vnd.lotus-1-2-3": {
        readonly source: "iana";
        readonly extensions: readonly ["123"];
    };
    "application/vnd.lotus-approach": {
        readonly source: "iana";
        readonly extensions: readonly ["apr"];
    };
    "application/vnd.lotus-freelance": {
        readonly source: "iana";
        readonly extensions: readonly ["pre"];
    };
    "application/vnd.lotus-notes": {
        readonly source: "iana";
        readonly extensions: readonly ["nsf"];
    };
    "application/vnd.lotus-organizer": {
        readonly source: "iana";
        readonly extensions: readonly ["org"];
    };
    "application/vnd.lotus-screencam": {
        readonly source: "iana";
        readonly extensions: readonly ["scm"];
    };
    "application/vnd.lotus-wordpro": {
        readonly source: "iana";
        readonly extensions: readonly ["lwp"];
    };
    "application/vnd.macports.portpkg": {
        readonly source: "iana";
        readonly extensions: readonly ["portpkg"];
    };
    "application/vnd.mapbox-vector-tile": {
        readonly source: "iana";
        readonly extensions: readonly ["mvt"];
    };
    "application/vnd.mcd": {
        readonly source: "iana";
        readonly extensions: readonly ["mcd"];
    };
    "application/vnd.medcalcdata": {
        readonly source: "iana";
        readonly extensions: readonly ["mc1"];
    };
    "application/vnd.mediastation.cdkey": {
        readonly source: "iana";
        readonly extensions: readonly ["cdkey"];
    };
    "application/vnd.mfer": {
        readonly source: "iana";
        readonly extensions: readonly ["mwf"];
    };
    "application/vnd.mfmp": {
        readonly source: "iana";
        readonly extensions: readonly ["mfm"];
    };
    "application/vnd.micrografx.flo": {
        readonly source: "iana";
        readonly extensions: readonly ["flo"];
    };
    "application/vnd.micrografx.igx": {
        readonly source: "iana";
        readonly extensions: readonly ["igx"];
    };
    "application/vnd.mif": {
        readonly source: "iana";
        readonly extensions: readonly ["mif"];
    };
    "application/vnd.mobius.daf": {
        readonly source: "iana";
        readonly extensions: readonly ["daf"];
    };
    "application/vnd.mobius.dis": {
        readonly source: "iana";
        readonly extensions: readonly ["dis"];
    };
    "application/vnd.mobius.mbk": {
        readonly source: "iana";
        readonly extensions: readonly ["mbk"];
    };
    "application/vnd.mobius.mqy": {
        readonly source: "iana";
        readonly extensions: readonly ["mqy"];
    };
    "application/vnd.mobius.msl": {
        readonly source: "iana";
        readonly extensions: readonly ["msl"];
    };
    "application/vnd.mobius.plc": {
        readonly source: "iana";
        readonly extensions: readonly ["plc"];
    };
    "application/vnd.mobius.txf": {
        readonly source: "iana";
        readonly extensions: readonly ["txf"];
    };
    "application/vnd.mophun.application": {
        readonly source: "iana";
        readonly extensions: readonly ["mpn"];
    };
    "application/vnd.mophun.certificate": {
        readonly source: "iana";
        readonly extensions: readonly ["mpc"];
    };
    "application/vnd.mozilla.xul+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["xul"];
    };
    "application/vnd.ms-artgalry": {
        readonly source: "iana";
        readonly extensions: readonly ["cil"];
    };
    "application/vnd.ms-cab-compressed": {
        readonly source: "iana";
        readonly extensions: readonly ["cab"];
    };
    "application/vnd.ms-excel": {
        readonly source: "iana";
        readonly extensions: readonly ["xls", "xlm", "xla", "xlc", "xlt", "xlw"];
    };
    "application/vnd.ms-excel.addin.macroenabled.12": {
        readonly source: "iana";
        readonly extensions: readonly ["xlam"];
    };
    "application/vnd.ms-excel.sheet.binary.macroenabled.12": {
        readonly source: "iana";
        readonly extensions: readonly ["xlsb"];
    };
    "application/vnd.ms-excel.sheet.macroenabled.12": {
        readonly source: "iana";
        readonly extensions: readonly ["xlsm"];
    };
    "application/vnd.ms-excel.template.macroenabled.12": {
        readonly source: "iana";
        readonly extensions: readonly ["xltm"];
    };
    "application/vnd.ms-fontobject": {
        readonly source: "iana";
        readonly extensions: readonly ["eot"];
    };
    "application/vnd.ms-htmlhelp": {
        readonly source: "iana";
        readonly extensions: readonly ["chm"];
    };
    "application/vnd.ms-ims": {
        readonly source: "iana";
        readonly extensions: readonly ["ims"];
    };
    "application/vnd.ms-lrm": {
        readonly source: "iana";
        readonly extensions: readonly ["lrm"];
    };
    "application/vnd.ms-officetheme": {
        readonly source: "iana";
        readonly extensions: readonly ["thmx"];
    };
    "application/vnd.ms-pki.seccat": {
        readonly source: "apache";
        readonly extensions: readonly ["cat"];
    };
    "application/vnd.ms-pki.stl": {
        readonly source: "apache";
        readonly extensions: readonly ["stl"];
    };
    "application/vnd.ms-powerpoint": {
        readonly source: "iana";
        readonly extensions: readonly ["ppt", "pps", "pot"];
    };
    "application/vnd.ms-powerpoint.addin.macroenabled.12": {
        readonly source: "iana";
        readonly extensions: readonly ["ppam"];
    };
    "application/vnd.ms-powerpoint.presentation.macroenabled.12": {
        readonly source: "iana";
        readonly extensions: readonly ["pptm"];
    };
    "application/vnd.ms-powerpoint.slide.macroenabled.12": {
        readonly source: "iana";
        readonly extensions: readonly ["sldm"];
    };
    "application/vnd.ms-powerpoint.slideshow.macroenabled.12": {
        readonly source: "iana";
        readonly extensions: readonly ["ppsm"];
    };
    "application/vnd.ms-powerpoint.template.macroenabled.12": {
        readonly source: "iana";
        readonly extensions: readonly ["potm"];
    };
    "application/vnd.ms-project": {
        readonly source: "iana";
        readonly extensions: readonly ["mpp", "mpt"];
    };
    "application/vnd.ms-word.document.macroenabled.12": {
        readonly source: "iana";
        readonly extensions: readonly ["docm"];
    };
    "application/vnd.ms-word.template.macroenabled.12": {
        readonly source: "iana";
        readonly extensions: readonly ["dotm"];
    };
    "application/vnd.ms-works": {
        readonly source: "iana";
        readonly extensions: readonly ["wps", "wks", "wcm", "wdb"];
    };
    "application/vnd.ms-wpl": {
        readonly source: "iana";
        readonly extensions: readonly ["wpl"];
    };
    "application/vnd.ms-xpsdocument": {
        readonly source: "iana";
        readonly extensions: readonly ["xps"];
    };
    "application/vnd.mseq": {
        readonly source: "iana";
        readonly extensions: readonly ["mseq"];
    };
    "application/vnd.musician": {
        readonly source: "iana";
        readonly extensions: readonly ["mus"];
    };
    "application/vnd.muvee.style": {
        readonly source: "iana";
        readonly extensions: readonly ["msty"];
    };
    "application/vnd.mynfc": {
        readonly source: "iana";
        readonly extensions: readonly ["taglet"];
    };
    "application/vnd.neurolanguage.nlu": {
        readonly source: "iana";
        readonly extensions: readonly ["nlu"];
    };
    "application/vnd.nitf": {
        readonly source: "iana";
        readonly extensions: readonly ["ntf", "nitf"];
    };
    "application/vnd.noblenet-directory": {
        readonly source: "iana";
        readonly extensions: readonly ["nnd"];
    };
    "application/vnd.noblenet-sealer": {
        readonly source: "iana";
        readonly extensions: readonly ["nns"];
    };
    "application/vnd.noblenet-web": {
        readonly source: "iana";
        readonly extensions: readonly ["nnw"];
    };
    "application/vnd.nokia.n-gage.ac+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["ac"];
    };
    "application/vnd.nokia.n-gage.data": {
        readonly source: "iana";
        readonly extensions: readonly ["ngdat"];
    };
    "application/vnd.nokia.n-gage.symbian.install": {
        readonly source: "iana";
        readonly extensions: readonly ["n-gage"];
    };
    "application/vnd.nokia.radio-preset": {
        readonly source: "iana";
        readonly extensions: readonly ["rpst"];
    };
    "application/vnd.nokia.radio-presets": {
        readonly source: "iana";
        readonly extensions: readonly ["rpss"];
    };
    "application/vnd.novadigm.edm": {
        readonly source: "iana";
        readonly extensions: readonly ["edm"];
    };
    "application/vnd.novadigm.edx": {
        readonly source: "iana";
        readonly extensions: readonly ["edx"];
    };
    "application/vnd.novadigm.ext": {
        readonly source: "iana";
        readonly extensions: readonly ["ext"];
    };
    "application/vnd.oasis.opendocument.chart": {
        readonly source: "iana";
        readonly extensions: readonly ["odc"];
    };
    "application/vnd.oasis.opendocument.chart-template": {
        readonly source: "iana";
        readonly extensions: readonly ["otc"];
    };
    "application/vnd.oasis.opendocument.database": {
        readonly source: "iana";
        readonly extensions: readonly ["odb"];
    };
    "application/vnd.oasis.opendocument.formula": {
        readonly source: "iana";
        readonly extensions: readonly ["odf"];
    };
    "application/vnd.oasis.opendocument.formula-template": {
        readonly source: "iana";
        readonly extensions: readonly ["odft"];
    };
    "application/vnd.oasis.opendocument.graphics": {
        readonly source: "iana";
        readonly extensions: readonly ["odg"];
    };
    "application/vnd.oasis.opendocument.graphics-template": {
        readonly source: "iana";
        readonly extensions: readonly ["otg"];
    };
    "application/vnd.oasis.opendocument.image": {
        readonly source: "iana";
        readonly extensions: readonly ["odi"];
    };
    "application/vnd.oasis.opendocument.image-template": {
        readonly source: "iana";
        readonly extensions: readonly ["oti"];
    };
    "application/vnd.oasis.opendocument.presentation": {
        readonly source: "iana";
        readonly extensions: readonly ["odp"];
    };
    "application/vnd.oasis.opendocument.presentation-template": {
        readonly source: "iana";
        readonly extensions: readonly ["otp"];
    };
    "application/vnd.oasis.opendocument.spreadsheet": {
        readonly source: "iana";
        readonly extensions: readonly ["ods"];
    };
    "application/vnd.oasis.opendocument.spreadsheet-template": {
        readonly source: "iana";
        readonly extensions: readonly ["ots"];
    };
    "application/vnd.oasis.opendocument.text": {
        readonly source: "iana";
        readonly extensions: readonly ["odt"];
    };
    "application/vnd.oasis.opendocument.text-master": {
        readonly source: "iana";
        readonly extensions: readonly ["odm"];
    };
    "application/vnd.oasis.opendocument.text-template": {
        readonly source: "iana";
        readonly extensions: readonly ["ott"];
    };
    "application/vnd.oasis.opendocument.text-web": {
        readonly source: "iana";
        readonly extensions: readonly ["oth"];
    };
    "application/vnd.olpc-sugar": {
        readonly source: "iana";
        readonly extensions: readonly ["xo"];
    };
    "application/vnd.oma.dd2+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["dd2"];
    };
    "application/vnd.openblox.game+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["obgx"];
    };
    "application/vnd.openofficeorg.extension": {
        readonly source: "apache";
        readonly extensions: readonly ["oxt"];
    };
    "application/vnd.openstreetmap.data+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["osm"];
    };
    "application/vnd.openxmlformats-officedocument.presentationml.presentation": {
        readonly source: "iana";
        readonly extensions: readonly ["pptx"];
    };
    "application/vnd.openxmlformats-officedocument.presentationml.slide": {
        readonly source: "iana";
        readonly extensions: readonly ["sldx"];
    };
    "application/vnd.openxmlformats-officedocument.presentationml.slideshow": {
        readonly source: "iana";
        readonly extensions: readonly ["ppsx"];
    };
    "application/vnd.openxmlformats-officedocument.presentationml.template": {
        readonly source: "iana";
        readonly extensions: readonly ["potx"];
    };
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": {
        readonly source: "iana";
        readonly extensions: readonly ["xlsx"];
    };
    "application/vnd.openxmlformats-officedocument.spreadsheetml.template": {
        readonly source: "iana";
        readonly extensions: readonly ["xltx"];
    };
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document": {
        readonly source: "iana";
        readonly extensions: readonly ["docx"];
    };
    "application/vnd.openxmlformats-officedocument.wordprocessingml.template": {
        readonly source: "iana";
        readonly extensions: readonly ["dotx"];
    };
    "application/vnd.osgeo.mapguide.package": {
        readonly source: "iana";
        readonly extensions: readonly ["mgp"];
    };
    "application/vnd.osgi.dp": {
        readonly source: "iana";
        readonly extensions: readonly ["dp"];
    };
    "application/vnd.osgi.subsystem": {
        readonly source: "iana";
        readonly extensions: readonly ["esa"];
    };
    "application/vnd.palm": {
        readonly source: "iana";
        readonly extensions: readonly ["pdb", "pqa", "oprc"];
    };
    "application/vnd.pawaafile": {
        readonly source: "iana";
        readonly extensions: readonly ["paw"];
    };
    "application/vnd.pg.format": {
        readonly source: "iana";
        readonly extensions: readonly ["str"];
    };
    "application/vnd.pg.osasli": {
        readonly source: "iana";
        readonly extensions: readonly ["ei6"];
    };
    "application/vnd.picsel": {
        readonly source: "iana";
        readonly extensions: readonly ["efif"];
    };
    "application/vnd.pmi.widget": {
        readonly source: "iana";
        readonly extensions: readonly ["wg"];
    };
    "application/vnd.pocketlearn": {
        readonly source: "iana";
        readonly extensions: readonly ["plf"];
    };
    "application/vnd.powerbuilder6": {
        readonly source: "iana";
        readonly extensions: readonly ["pbd"];
    };
    "application/vnd.previewsystems.box": {
        readonly source: "iana";
        readonly extensions: readonly ["box"];
    };
    "application/vnd.proteus.magazine": {
        readonly source: "iana";
        readonly extensions: readonly ["mgz"];
    };
    "application/vnd.publishare-delta-tree": {
        readonly source: "iana";
        readonly extensions: readonly ["qps"];
    };
    "application/vnd.pvi.ptid1": {
        readonly source: "iana";
        readonly extensions: readonly ["ptid"];
    };
    "application/vnd.quark.quarkxpress": {
        readonly source: "iana";
        readonly extensions: readonly ["qxd", "qxt", "qwd", "qwt", "qxl", "qxb"];
    };
    "application/vnd.rar": {
        readonly source: "iana";
        readonly extensions: readonly ["rar"];
    };
    "application/vnd.realvnc.bed": {
        readonly source: "iana";
        readonly extensions: readonly ["bed"];
    };
    "application/vnd.recordare.musicxml": {
        readonly source: "iana";
        readonly extensions: readonly ["mxl"];
    };
    "application/vnd.recordare.musicxml+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["musicxml"];
    };
    "application/vnd.rig.cryptonote": {
        readonly source: "iana";
        readonly extensions: readonly ["cryptonote"];
    };
    "application/vnd.rim.cod": {
        readonly source: "apache";
        readonly extensions: readonly ["cod"];
    };
    "application/vnd.rn-realmedia": {
        readonly source: "apache";
        readonly extensions: readonly ["rm"];
    };
    "application/vnd.rn-realmedia-vbr": {
        readonly source: "apache";
        readonly extensions: readonly ["rmvb"];
    };
    "application/vnd.route66.link66+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["link66"];
    };
    "application/vnd.sailingtracker.track": {
        readonly source: "iana";
        readonly extensions: readonly ["st"];
    };
    "application/vnd.seemail": {
        readonly source: "iana";
        readonly extensions: readonly ["see"];
    };
    "application/vnd.sema": {
        readonly source: "iana";
        readonly extensions: readonly ["sema"];
    };
    "application/vnd.semd": {
        readonly source: "iana";
        readonly extensions: readonly ["semd"];
    };
    "application/vnd.semf": {
        readonly source: "iana";
        readonly extensions: readonly ["semf"];
    };
    "application/vnd.shana.informed.formdata": {
        readonly source: "iana";
        readonly extensions: readonly ["ifm"];
    };
    "application/vnd.shana.informed.formtemplate": {
        readonly source: "iana";
        readonly extensions: readonly ["itp"];
    };
    "application/vnd.shana.informed.interchange": {
        readonly source: "iana";
        readonly extensions: readonly ["iif"];
    };
    "application/vnd.shana.informed.package": {
        readonly source: "iana";
        readonly extensions: readonly ["ipk"];
    };
    "application/vnd.simtech-mindmapper": {
        readonly source: "iana";
        readonly extensions: readonly ["twd", "twds"];
    };
    "application/vnd.smaf": {
        readonly source: "iana";
        readonly extensions: readonly ["mmf"];
    };
    "application/vnd.smart.teacher": {
        readonly source: "iana";
        readonly extensions: readonly ["teacher"];
    };
    "application/vnd.software602.filler.form+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["fo"];
    };
    "application/vnd.solent.sdkm+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["sdkm", "sdkd"];
    };
    "application/vnd.spotfire.dxp": {
        readonly source: "iana";
        readonly extensions: readonly ["dxp"];
    };
    "application/vnd.spotfire.sfs": {
        readonly source: "iana";
        readonly extensions: readonly ["sfs"];
    };
    "application/vnd.stardivision.calc": {
        readonly source: "apache";
        readonly extensions: readonly ["sdc"];
    };
    "application/vnd.stardivision.draw": {
        readonly source: "apache";
        readonly extensions: readonly ["sda"];
    };
    "application/vnd.stardivision.impress": {
        readonly source: "apache";
        readonly extensions: readonly ["sdd"];
    };
    "application/vnd.stardivision.math": {
        readonly source: "apache";
        readonly extensions: readonly ["smf"];
    };
    "application/vnd.stardivision.writer": {
        readonly source: "apache";
        readonly extensions: readonly ["sdw", "vor"];
    };
    "application/vnd.stardivision.writer-global": {
        readonly source: "apache";
        readonly extensions: readonly ["sgl"];
    };
    "application/vnd.stepmania.package": {
        readonly source: "iana";
        readonly extensions: readonly ["smzip"];
    };
    "application/vnd.stepmania.stepchart": {
        readonly source: "iana";
        readonly extensions: readonly ["sm"];
    };
    "application/vnd.sun.wadl+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["wadl"];
    };
    "application/vnd.sun.xml.calc": {
        readonly source: "apache";
        readonly extensions: readonly ["sxc"];
    };
    "application/vnd.sun.xml.calc.template": {
        readonly source: "apache";
        readonly extensions: readonly ["stc"];
    };
    "application/vnd.sun.xml.draw": {
        readonly source: "apache";
        readonly extensions: readonly ["sxd"];
    };
    "application/vnd.sun.xml.draw.template": {
        readonly source: "apache";
        readonly extensions: readonly ["std"];
    };
    "application/vnd.sun.xml.impress": {
        readonly source: "apache";
        readonly extensions: readonly ["sxi"];
    };
    "application/vnd.sun.xml.impress.template": {
        readonly source: "apache";
        readonly extensions: readonly ["sti"];
    };
    "application/vnd.sun.xml.math": {
        readonly source: "apache";
        readonly extensions: readonly ["sxm"];
    };
    "application/vnd.sun.xml.writer": {
        readonly source: "apache";
        readonly extensions: readonly ["sxw"];
    };
    "application/vnd.sun.xml.writer.global": {
        readonly source: "apache";
        readonly extensions: readonly ["sxg"];
    };
    "application/vnd.sun.xml.writer.template": {
        readonly source: "apache";
        readonly extensions: readonly ["stw"];
    };
    "application/vnd.sus-calendar": {
        readonly source: "iana";
        readonly extensions: readonly ["sus", "susp"];
    };
    "application/vnd.svd": {
        readonly source: "iana";
        readonly extensions: readonly ["svd"];
    };
    "application/vnd.symbian.install": {
        readonly source: "apache";
        readonly extensions: readonly ["sis", "sisx"];
    };
    "application/vnd.syncml+xml": {
        readonly source: "iana";
        readonly charset: "UTF-8";
        readonly extensions: readonly ["xsm"];
    };
    "application/vnd.syncml.dm+wbxml": {
        readonly source: "iana";
        readonly charset: "UTF-8";
        readonly extensions: readonly ["bdm"];
    };
    "application/vnd.syncml.dm+xml": {
        readonly source: "iana";
        readonly charset: "UTF-8";
        readonly extensions: readonly ["xdm"];
    };
    "application/vnd.syncml.dmddf+xml": {
        readonly source: "iana";
        readonly charset: "UTF-8";
        readonly extensions: readonly ["ddf"];
    };
    "application/vnd.tao.intent-module-archive": {
        readonly source: "iana";
        readonly extensions: readonly ["tao"];
    };
    "application/vnd.tcpdump.pcap": {
        readonly source: "iana";
        readonly extensions: readonly ["pcap", "cap", "dmp"];
    };
    "application/vnd.tmobile-livetv": {
        readonly source: "iana";
        readonly extensions: readonly ["tmo"];
    };
    "application/vnd.trid.tpt": {
        readonly source: "iana";
        readonly extensions: readonly ["tpt"];
    };
    "application/vnd.triscape.mxs": {
        readonly source: "iana";
        readonly extensions: readonly ["mxs"];
    };
    "application/vnd.trueapp": {
        readonly source: "iana";
        readonly extensions: readonly ["tra"];
    };
    "application/vnd.ufdl": {
        readonly source: "iana";
        readonly extensions: readonly ["ufd", "ufdl"];
    };
    "application/vnd.uiq.theme": {
        readonly source: "iana";
        readonly extensions: readonly ["utz"];
    };
    "application/vnd.umajin": {
        readonly source: "iana";
        readonly extensions: readonly ["umj"];
    };
    "application/vnd.unity": {
        readonly source: "iana";
        readonly extensions: readonly ["unityweb"];
    };
    "application/vnd.uoml+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["uoml"];
    };
    "application/vnd.vcx": {
        readonly source: "iana";
        readonly extensions: readonly ["vcx"];
    };
    "application/vnd.visio": {
        readonly source: "iana";
        readonly extensions: readonly ["vsd", "vst", "vss", "vsw"];
    };
    "application/vnd.visionary": {
        readonly source: "iana";
        readonly extensions: readonly ["vis"];
    };
    "application/vnd.vsf": {
        readonly source: "iana";
        readonly extensions: readonly ["vsf"];
    };
    "application/vnd.wap.wbxml": {
        readonly source: "iana";
        readonly charset: "UTF-8";
        readonly extensions: readonly ["wbxml"];
    };
    "application/vnd.wap.wmlc": {
        readonly source: "iana";
        readonly extensions: readonly ["wmlc"];
    };
    "application/vnd.wap.wmlscriptc": {
        readonly source: "iana";
        readonly extensions: readonly ["wmlsc"];
    };
    "application/vnd.webturbo": {
        readonly source: "iana";
        readonly extensions: readonly ["wtb"];
    };
    "application/vnd.wolfram.player": {
        readonly source: "iana";
        readonly extensions: readonly ["nbp"];
    };
    "application/vnd.wordperfect": {
        readonly source: "iana";
        readonly extensions: readonly ["wpd"];
    };
    "application/vnd.wqd": {
        readonly source: "iana";
        readonly extensions: readonly ["wqd"];
    };
    "application/vnd.wt.stf": {
        readonly source: "iana";
        readonly extensions: readonly ["stf"];
    };
    "application/vnd.xara": {
        readonly source: "iana";
        readonly extensions: readonly ["xar"];
    };
    "application/vnd.xfdl": {
        readonly source: "iana";
        readonly extensions: readonly ["xfdl"];
    };
    "application/vnd.yamaha.hv-dic": {
        readonly source: "iana";
        readonly extensions: readonly ["hvd"];
    };
    "application/vnd.yamaha.hv-script": {
        readonly source: "iana";
        readonly extensions: readonly ["hvs"];
    };
    "application/vnd.yamaha.hv-voice": {
        readonly source: "iana";
        readonly extensions: readonly ["hvp"];
    };
    "application/vnd.yamaha.openscoreformat": {
        readonly source: "iana";
        readonly extensions: readonly ["osf"];
    };
    "application/vnd.yamaha.openscoreformat.osfpvg+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["osfpvg"];
    };
    "application/vnd.yamaha.smaf-audio": {
        readonly source: "iana";
        readonly extensions: readonly ["saf"];
    };
    "application/vnd.yamaha.smaf-phrase": {
        readonly source: "iana";
        readonly extensions: readonly ["spf"];
    };
    "application/vnd.yellowriver-custom-menu": {
        readonly source: "iana";
        readonly extensions: readonly ["cmp"];
    };
    "application/vnd.zul": {
        readonly source: "iana";
        readonly extensions: readonly ["zir", "zirz"];
    };
    "application/vnd.zzazz.deck+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["zaz"];
    };
    "application/voicexml+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["vxml"];
    };
    "application/wasm": {
        readonly source: "iana";
        readonly extensions: readonly ["wasm"];
    };
    "application/watcherinfo+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["wif"];
    };
    "application/widget": {
        readonly source: "iana";
        readonly extensions: readonly ["wgt"];
    };
    "application/winhlp": {
        readonly source: "apache";
        readonly extensions: readonly ["hlp"];
    };
    "application/wsdl+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["wsdl"];
    };
    "application/wspolicy+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["wspolicy"];
    };
    "application/x-7z-compressed": {
        readonly source: "apache";
        readonly extensions: readonly ["7z"];
    };
    "application/x-abiword": {
        readonly source: "apache";
        readonly extensions: readonly ["abw"];
    };
    "application/x-ace-compressed": {
        readonly source: "apache";
        readonly extensions: readonly ["ace"];
    };
    "application/x-apple-diskimage": {
        readonly source: "apache";
        readonly extensions: readonly ["dmg"];
    };
    "application/x-authorware-bin": {
        readonly source: "apache";
        readonly extensions: readonly ["aab", "x32", "u32", "vox"];
    };
    "application/x-authorware-map": {
        readonly source: "apache";
        readonly extensions: readonly ["aam"];
    };
    "application/x-authorware-seg": {
        readonly source: "apache";
        readonly extensions: readonly ["aas"];
    };
    "application/x-bcpio": {
        readonly source: "apache";
        readonly extensions: readonly ["bcpio"];
    };
    "application/x-bittorrent": {
        readonly source: "apache";
        readonly extensions: readonly ["torrent"];
    };
    "application/x-blorb": {
        readonly source: "apache";
        readonly extensions: readonly ["blb", "blorb"];
    };
    "application/x-bzip": {
        readonly source: "apache";
        readonly extensions: readonly ["bz"];
    };
    "application/x-bzip2": {
        readonly source: "apache";
        readonly extensions: readonly ["bz2", "boz"];
    };
    "application/x-cbr": {
        readonly source: "apache";
        readonly extensions: readonly ["cbr", "cba", "cbt", "cbz", "cb7"];
    };
    "application/x-cdlink": {
        readonly source: "apache";
        readonly extensions: readonly ["vcd"];
    };
    "application/x-cfs-compressed": {
        readonly source: "apache";
        readonly extensions: readonly ["cfs"];
    };
    "application/x-chat": {
        readonly source: "apache";
        readonly extensions: readonly ["chat"];
    };
    "application/x-chess-pgn": {
        readonly source: "apache";
        readonly extensions: readonly ["pgn"];
    };
    "application/x-cocoa": {
        readonly source: "nginx";
        readonly extensions: readonly ["cco"];
    };
    "application/x-conference": {
        readonly source: "apache";
        readonly extensions: readonly ["nsc"];
    };
    "application/x-cpio": {
        readonly source: "apache";
        readonly extensions: readonly ["cpio"];
    };
    "application/x-csh": {
        readonly source: "apache";
        readonly extensions: readonly ["csh"];
    };
    "application/x-debian-package": {
        readonly source: "apache";
        readonly extensions: readonly ["deb", "udeb"];
    };
    "application/x-dgc-compressed": {
        readonly source: "apache";
        readonly extensions: readonly ["dgc"];
    };
    "application/x-director": {
        readonly source: "apache";
        readonly extensions: readonly ["dir", "dcr", "dxr", "cst", "cct", "cxt", "w3d", "fgd", "swa"];
    };
    "application/x-doom": {
        readonly source: "apache";
        readonly extensions: readonly ["wad"];
    };
    "application/x-dtbncx+xml": {
        readonly source: "apache";
        readonly extensions: readonly ["ncx"];
    };
    "application/x-dtbook+xml": {
        readonly source: "apache";
        readonly extensions: readonly ["dtb"];
    };
    "application/x-dtbresource+xml": {
        readonly source: "apache";
        readonly extensions: readonly ["res"];
    };
    "application/x-dvi": {
        readonly source: "apache";
        readonly extensions: readonly ["dvi"];
    };
    "application/x-envoy": {
        readonly source: "apache";
        readonly extensions: readonly ["evy"];
    };
    "application/x-eva": {
        readonly source: "apache";
        readonly extensions: readonly ["eva"];
    };
    "application/x-font-bdf": {
        readonly source: "apache";
        readonly extensions: readonly ["bdf"];
    };
    "application/x-font-ghostscript": {
        readonly source: "apache";
        readonly extensions: readonly ["gsf"];
    };
    "application/x-font-linux-psf": {
        readonly source: "apache";
        readonly extensions: readonly ["psf"];
    };
    "application/x-font-pcf": {
        readonly source: "apache";
        readonly extensions: readonly ["pcf"];
    };
    "application/x-font-snf": {
        readonly source: "apache";
        readonly extensions: readonly ["snf"];
    };
    "application/x-font-type1": {
        readonly source: "apache";
        readonly extensions: readonly ["pfa", "pfb", "pfm", "afm"];
    };
    "application/x-freearc": {
        readonly source: "apache";
        readonly extensions: readonly ["arc"];
    };
    "application/x-futuresplash": {
        readonly source: "apache";
        readonly extensions: readonly ["spl"];
    };
    "application/x-gca-compressed": {
        readonly source: "apache";
        readonly extensions: readonly ["gca"];
    };
    "application/x-glulx": {
        readonly source: "apache";
        readonly extensions: readonly ["ulx"];
    };
    "application/x-gnumeric": {
        readonly source: "apache";
        readonly extensions: readonly ["gnumeric"];
    };
    "application/x-gramps-xml": {
        readonly source: "apache";
        readonly extensions: readonly ["gramps"];
    };
    "application/x-gtar": {
        readonly source: "apache";
        readonly extensions: readonly ["gtar"];
    };
    "application/x-hdf": {
        readonly source: "apache";
        readonly extensions: readonly ["hdf"];
    };
    "application/x-install-instructions": {
        readonly source: "apache";
        readonly extensions: readonly ["install"];
    };
    "application/x-iso9660-image": {
        readonly source: "apache";
        readonly extensions: readonly ["iso"];
    };
    "application/x-java-archive-diff": {
        readonly source: "nginx";
        readonly extensions: readonly ["jardiff"];
    };
    "application/x-java-jnlp-file": {
        readonly source: "apache";
        readonly extensions: readonly ["jnlp"];
    };
    "application/x-latex": {
        readonly source: "apache";
        readonly extensions: readonly ["latex"];
    };
    "application/x-lzh-compressed": {
        readonly source: "apache";
        readonly extensions: readonly ["lzh", "lha"];
    };
    "application/x-makeself": {
        readonly source: "nginx";
        readonly extensions: readonly ["run"];
    };
    "application/x-mie": {
        readonly source: "apache";
        readonly extensions: readonly ["mie"];
    };
    "application/x-mobipocket-ebook": {
        readonly source: "apache";
        readonly extensions: readonly ["prc", "mobi"];
    };
    "application/x-ms-application": {
        readonly source: "apache";
        readonly extensions: readonly ["application"];
    };
    "application/x-ms-shortcut": {
        readonly source: "apache";
        readonly extensions: readonly ["lnk"];
    };
    "application/x-ms-wmd": {
        readonly source: "apache";
        readonly extensions: readonly ["wmd"];
    };
    "application/x-ms-wmz": {
        readonly source: "apache";
        readonly extensions: readonly ["wmz"];
    };
    "application/x-ms-xbap": {
        readonly source: "apache";
        readonly extensions: readonly ["xbap"];
    };
    "application/x-msaccess": {
        readonly source: "apache";
        readonly extensions: readonly ["mdb"];
    };
    "application/x-msbinder": {
        readonly source: "apache";
        readonly extensions: readonly ["obd"];
    };
    "application/x-mscardfile": {
        readonly source: "apache";
        readonly extensions: readonly ["crd"];
    };
    "application/x-msclip": {
        readonly source: "apache";
        readonly extensions: readonly ["clp"];
    };
    "application/x-msdownload": {
        readonly source: "apache";
        readonly extensions: readonly ["exe", "dll", "com", "bat", "msi"];
    };
    "application/x-msmediaview": {
        readonly source: "apache";
        readonly extensions: readonly ["mvb", "m13", "m14"];
    };
    "application/x-msmetafile": {
        readonly source: "apache";
        readonly extensions: readonly ["wmf", "wmz", "emf", "emz"];
    };
    "application/x-msmoney": {
        readonly source: "apache";
        readonly extensions: readonly ["mny"];
    };
    "application/x-mspublisher": {
        readonly source: "apache";
        readonly extensions: readonly ["pub"];
    };
    "application/x-msschedule": {
        readonly source: "apache";
        readonly extensions: readonly ["scd"];
    };
    "application/x-msterminal": {
        readonly source: "apache";
        readonly extensions: readonly ["trm"];
    };
    "application/x-mswrite": {
        readonly source: "apache";
        readonly extensions: readonly ["wri"];
    };
    "application/x-netcdf": {
        readonly source: "apache";
        readonly extensions: readonly ["nc", "cdf"];
    };
    "application/x-nzb": {
        readonly source: "apache";
        readonly extensions: readonly ["nzb"];
    };
    "application/x-perl": {
        readonly source: "nginx";
        readonly extensions: readonly ["pl", "pm"];
    };
    "application/x-pilot": {
        readonly source: "nginx";
        readonly extensions: readonly ["prc", "pdb"];
    };
    "application/x-pkcs12": {
        readonly source: "apache";
        readonly extensions: readonly ["p12", "pfx"];
    };
    "application/x-pkcs7-certificates": {
        readonly source: "apache";
        readonly extensions: readonly ["p7b", "spc"];
    };
    "application/x-pkcs7-certreqresp": {
        readonly source: "apache";
        readonly extensions: readonly ["p7r"];
    };
    "application/x-rar-compressed": {
        readonly source: "apache";
        readonly extensions: readonly ["rar"];
    };
    "application/x-redhat-package-manager": {
        readonly source: "nginx";
        readonly extensions: readonly ["rpm"];
    };
    "application/x-research-info-systems": {
        readonly source: "apache";
        readonly extensions: readonly ["ris"];
    };
    "application/x-sea": {
        readonly source: "nginx";
        readonly extensions: readonly ["sea"];
    };
    "application/x-sh": {
        readonly source: "apache";
        readonly extensions: readonly ["sh"];
    };
    "application/x-shar": {
        readonly source: "apache";
        readonly extensions: readonly ["shar"];
    };
    "application/x-shockwave-flash": {
        readonly source: "apache";
        readonly extensions: readonly ["swf"];
    };
    "application/x-silverlight-app": {
        readonly source: "apache";
        readonly extensions: readonly ["xap"];
    };
    "application/x-sql": {
        readonly source: "apache";
        readonly extensions: readonly ["sql"];
    };
    "application/x-stuffit": {
        readonly source: "apache";
        readonly extensions: readonly ["sit"];
    };
    "application/x-stuffitx": {
        readonly source: "apache";
        readonly extensions: readonly ["sitx"];
    };
    "application/x-subrip": {
        readonly source: "apache";
        readonly extensions: readonly ["srt"];
    };
    "application/x-sv4cpio": {
        readonly source: "apache";
        readonly extensions: readonly ["sv4cpio"];
    };
    "application/x-sv4crc": {
        readonly source: "apache";
        readonly extensions: readonly ["sv4crc"];
    };
    "application/x-t3vm-image": {
        readonly source: "apache";
        readonly extensions: readonly ["t3"];
    };
    "application/x-tads": {
        readonly source: "apache";
        readonly extensions: readonly ["gam"];
    };
    "application/x-tar": {
        readonly source: "apache";
        readonly extensions: readonly ["tar"];
    };
    "application/x-tcl": {
        readonly source: "apache";
        readonly extensions: readonly ["tcl", "tk"];
    };
    "application/x-tex": {
        readonly source: "apache";
        readonly extensions: readonly ["tex"];
    };
    "application/x-tex-tfm": {
        readonly source: "apache";
        readonly extensions: readonly ["tfm"];
    };
    "application/x-texinfo": {
        readonly source: "apache";
        readonly extensions: readonly ["texinfo", "texi"];
    };
    "application/x-tgif": {
        readonly source: "apache";
        readonly extensions: readonly ["obj"];
    };
    "application/x-ustar": {
        readonly source: "apache";
        readonly extensions: readonly ["ustar"];
    };
    "application/x-wais-source": {
        readonly source: "apache";
        readonly extensions: readonly ["src"];
    };
    "application/x-x509-ca-cert": {
        readonly source: "iana";
        readonly extensions: readonly ["der", "crt", "pem"];
    };
    "application/x-xfig": {
        readonly source: "apache";
        readonly extensions: readonly ["fig"];
    };
    "application/x-xliff+xml": {
        readonly source: "apache";
        readonly extensions: readonly ["xlf"];
    };
    "application/x-xpinstall": {
        readonly source: "apache";
        readonly extensions: readonly ["xpi"];
    };
    "application/x-xz": {
        readonly source: "apache";
        readonly extensions: readonly ["xz"];
    };
    "application/x-zmachine": {
        readonly source: "apache";
        readonly extensions: readonly ["z1", "z2", "z3", "z4", "z5", "z6", "z7", "z8"];
    };
    "application/xaml+xml": {
        readonly source: "apache";
        readonly extensions: readonly ["xaml"];
    };
    "application/xcap-att+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["xav"];
    };
    "application/xcap-caps+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["xca"];
    };
    "application/xcap-diff+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["xdf"];
    };
    "application/xcap-el+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["xel"];
    };
    "application/xcap-ns+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["xns"];
    };
    "application/xenc+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["xenc"];
    };
    "application/xhtml+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["xhtml", "xht"];
    };
    "application/xliff+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["xlf"];
    };
    "application/xml": {
        readonly source: "iana";
        readonly extensions: readonly ["xml", "xsl", "xsd", "rng"];
    };
    "application/xml-dtd": {
        readonly source: "iana";
        readonly extensions: readonly ["dtd"];
    };
    "application/xop+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["xop"];
    };
    "application/xproc+xml": {
        readonly source: "apache";
        readonly extensions: readonly ["xpl"];
    };
    "application/xslt+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["xsl", "xslt"];
    };
    "application/xspf+xml": {
        readonly source: "apache";
        readonly extensions: readonly ["xspf"];
    };
    "application/xv+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["mxml", "xhvml", "xvml", "xvm"];
    };
    "application/yaml": {
        readonly source: "iana";
        readonly extensions: readonly ["yaml", "yml"];
    };
    "application/yang": {
        readonly source: "iana";
        readonly extensions: readonly ["yang"];
    };
    "application/yin+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["yin"];
    };
    "application/zip": {
        readonly source: "iana";
        readonly extensions: readonly ["zip"];
    };
};
type MimeType = keyof typeof mimes;
type FileExtension = (typeof mimes)[MimeType]["extensions"][number];
declare const mimeTypes: Record<MimeType, {
    source: string;
    extensions: FileExtension[];
}>;
declare function getTypes(): Record<FileExtension, MimeType>;
declare function getExtensions(): Record<MimeType, FileExtension[]>;
/**
 * Lookup the MIME type for a file path/extension.
 */
declare function lookup(path: string): false | MimeType;

export { getExtensions, getTypes, lookup, mimeTypes };
export type { FileExtension, MimeType };
