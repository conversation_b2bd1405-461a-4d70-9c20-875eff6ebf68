import * as internal from "./internal/etag.js";
/**
 * @since 1.0.0
 * @category convertions
 */
export const toString = internal.toString;
/**
 * @since 1.0.0
 * @category type ids
 */
export const GeneratorTypeId = internal.GeneratorTypeId;
/**
 * @since 1.0.0
 * @category tags
 */
export const Generator = internal.tag;
/**
 * @since 1.0.0
 * @category layers
 */
export const layer = internal.layer;
/**
 * @since 1.0.0
 * @category layers
 */
export const layerWeak = internal.layerWeak;
//# sourceMappingURL=Etag.js.map