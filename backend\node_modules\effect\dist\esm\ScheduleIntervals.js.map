{"version": 3, "file": "ScheduleIntervals.js", "names": ["internal", "IntervalsTypeId", "make", "empty", "fromIterable", "union", "intersect", "start", "end", "lessThan", "isNonEmpty", "max"], "sources": ["../../src/ScheduleIntervals.ts"], "sourcesContent": [null], "mappings": "AAIA,OAAO,KAAKA,QAAQ,MAAM,kCAAkC;AAG5D;;;;AAIA,OAAO,MAAMC,eAAe,GAAkBD,QAAQ,CAACC,eAAe;AAmBtE;;;;;;AAMA,OAAO,MAAMC,IAAI,GAA6DF,QAAQ,CAACE,IAAI;AAE3F;;;;;;AAMA,OAAO,MAAMC,KAAK,GAAcH,QAAQ,CAACG,KAAK;AAE9C;;;;;;AAMA,OAAO,MAAMC,YAAY,GAA0DJ,QAAQ,CAACI,YAAY;AAExG;;;;;;AAMA,OAAO,MAAMC,KAAK,GAedL,QAAQ,CAACK,KAAK;AAElB;;;;;;AAMA,OAAO,MAAMC,SAAS,GAelBN,QAAQ,CAACM,SAAS;AAEtB;;;;;;AAMA,OAAO,MAAMC,KAAK,GAAgCP,QAAQ,CAACO,KAAK;AAEhE;;;;;;AAMA,OAAO,MAAMC,GAAG,GAAgCR,QAAQ,CAACQ,GAAG;AAE5D;;;;;;;AAOA,OAAO,MAAMC,QAAQ,GAiBjBT,QAAQ,CAACS,QAAQ;AAErB;;;;;;AAMA,OAAO,MAAMC,UAAU,GAAiCV,QAAQ,CAACU,UAAU;AAE3E;;;;;;AAMA,OAAO,MAAMC,GAAG,GAeZX,QAAQ,CAACW,GAAG", "ignoreList": []}