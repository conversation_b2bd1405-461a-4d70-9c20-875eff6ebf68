import { MimeType } from '@uploadthing/mime-types';
import * as Micro from 'effect/Micro';
import * as effect_Types from 'effect/Types';
import * as Context from 'effect/Context';
import { CSSProperties, ReactNode } from 'react';
import { JSX } from 'solid-js/jsx-runtime';
import { StyleValue, RenderFunction } from 'vue';
import * as Redacted from 'effect/Redacted';

declare const ALLOWED_FILE_TYPES: readonly ["image", "video", "audio", "pdf", "text", "blob"];
type AllowedFileType = (typeof ALLOWED_FILE_TYPES)[number];

type JsonValue = string | number | boolean | null | undefined;
type JsonObject = {
    [key: string]: JsonValue | JsonObject | JsonArray;
};
type JsonArray = (JsonValue | JsonObject)[];
type Json = JsonValue | JsonObject | JsonArray;
type Overwrite<T, U> = Omit<T, keyof U> & U;
type WithRequired<T, K extends keyof T> = T & Required<Pick<T, K>>;
type ErrorMessage<TError extends string> = TError;
type Simplify<TType> = {
    [TKey in keyof TType]: TType[TKey];
} & {};
type MaybePromise<TType> = TType | Promise<TType>;
type Either<TData, TError> = {
    data: TData;
    error: null;
} | {
    data: null;
    error: TError;
};
type ExtendObjectIf<Predicate, ToAdd> = undefined extends Predicate ? {} : ToAdd;
type DeepPartial<T> = T extends object ? {
    [P in keyof T]?: DeepPartial<T[P]>;
} : T;
interface FileProperties {
    name: string;
    size: number;
    type: string;
    lastModified?: number | undefined;
}
type ExtractHashPartsFn = (file: FileProperties) => (string | number | undefined | null | boolean)[];
/**
 * A subset of the standard RequestInit properties needed by UploadThing internally.
 * @see RequestInit from lib.dom.d.ts
 */
interface RequestInitEsque {
    /**
     * Sets the request's body.
     */
    body?: FormData | ReadableStream | string | null;
    /**
     * Sets the request's associated headers.
     */
    headers?: [string, string][] | Record<string, string>;
    /**
     * The request's HTTP-style method.
     */
    method?: string;
}
/**
 * A subset of the standard Response properties needed by UploadThing internally.
 * @see Response from lib.dom.d.ts
 */
interface ResponseEsque {
    status: number;
    statusText: string;
    ok: boolean;
    /**
     * @remarks
     * The built-in Response::json() method returns Promise<any>, but
     * that's not as type-safe as unknown. We use unknown because we're
     * more type-safe. You do want more type safety, right? 😉
     */
    json: <T = unknown>() => Promise<T>;
    text: () => Promise<string>;
    blob: () => Promise<Blob>;
    body: ReadableStream | null;
    headers: Headers;
    clone: () => ResponseEsque;
}
type MaybeUrl = string | URL;
/**
 * A subset of the standard fetch function type needed by UploadThing internally.
 * @see fetch from lib.dom.d.ts
 */
type FetchEsque = (input: RequestInfo | MaybeUrl, init?: RequestInit | RequestInitEsque) => Promise<ResponseEsque>;
type PowOf2 = 1 | 2 | 4 | 8 | 16 | 32 | 64 | 128 | 256 | 512 | 1024;
type SizeUnit = "B" | "KB" | "MB" | "GB";
type FileSize = `${PowOf2}${SizeUnit}`;
type TimeShort = "s" | "m" | "h" | "d";
type TimeLong = "second" | "minute" | "hour" | "day";
type SuggestedNumbers = 2 | 3 | 4 | 5 | 6 | 7 | 10 | 15 | 30 | 60;
type AutoCompleteableNumber = SuggestedNumbers | (number & {});
type Time = number | `1${TimeShort}` | `${AutoCompleteableNumber}${TimeShort}` | `1 ${TimeLong}` | `${AutoCompleteableNumber} ${TimeLong}s`;
declare const ValidContentDispositions: readonly ["inline", "attachment"];
type ContentDisposition = (typeof ValidContentDispositions)[number];
declare const ValidACLs: readonly ["public-read", "private"];
type ACL = (typeof ValidACLs)[number];
type ImageProperties = {
    /** Specify the width of the image. */
    width?: number;
    /** Specify the height of the image. */
    height?: number;
    /**
     * Specify the aspect ratio of the image.
     * @remarks If both width and height are specified, this will be ignored.
     */
    aspectRatio?: number;
};
type AdditionalProperties<T> = Record<string, unknown> & T;
type RouteConfig<TAdditionalProperties extends Record<string, unknown>> = {
    /**
     * Human-readable file size limit
     * @example "1MB"
     * @default https://docs.uploadthing.com/api-reference/server#defaults
     */
    maxFileSize: FileSize;
    /**
     * Maximum number of files allowed to be uploaded of this type
     * @example 10
     * @default https://docs.uploadthing.com/api-reference/server#defaults
     */
    maxFileCount: number;
    /**
     * Minimum number of files allowed to be uploaded of this type
     * @remarks Must be <= maxFileCount
     * @example 2
     * @default 1
     */
    minFileCount: number;
    /**
     * Specify the [content disposition](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Disposition) of the uploaded file
     * @example "attachment"
     * @default "inline"
     */
    contentDisposition: ContentDisposition;
    /**
     * Specify the [access control list](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Access-Control-Allow-Origin) of the uploaded file
     * @remarks This must be enabled for your app. See https://docs.uploadthing.com/regions-and-acl#access-controls.
     * @example "private"
     * @default "public-read"
     */
    acl?: ACL;
    /**
     * Additional properties to be passed to the client-side `useRouteConfig` hook
     * @remarks These properties are not validated on the server on upload
     */
    additionalProperties?: AdditionalProperties<TAdditionalProperties>;
};
/**
 * Shared config options for an entire route not bound to any specific file type
 * @example
 * ```ts
 * f(
 *   { image: {} },
 *   { awaitServerData: true },
 * )
 * ```
 */
type RouteOptions = {
    /**
     * Set this to `false` to run the client-side `onClientUploadComplete`
     * immediately after file has been uploaded without waiting for the
     * server to return the `onUploadComplete` data.
     * @default true
     */
    awaitServerData?: boolean;
    /**
     * TTL for the presigned URLs generated for the upload
     * @default `1h`
     */
    presignedURLTTL?: Time;
    /**
     * Function that pulls out the properties of the uploaded file
     * that you want to be included as part of the presigned URL generation.
     * By default, we include all properties as well as a timestamp to make
     * each URL unique. You can for example override this to always return
     * the same hash for the same file, no matter when it was uploaded.
     * @default (file) => [file.name, file.size, file.type, file.lastModified,  Date.now()]
     */
    getFileHashParts?: ExtractHashPartsFn;
};
type FileRouterInputKey = AllowedFileType | MimeType;
type ExpandedRouteConfig = {
    [key in FileRouterInputKey]?: key extends `image${string}` ? RouteConfig<ImageProperties> : RouteConfig<Record<string, unknown>>;
};
type EndpointMetadata = {
    slug: string;
    config: ExpandedRouteConfig;
}[];
type FileRouterInputConfig = FileRouterInputKey[] | DeepPartial<ExpandedRouteConfig>;

declare const InvalidRouteConfigError_base: new <A extends Record<string, any> = {}>(args: effect_Types.Equals<A, {}> extends true ? void : { readonly [P in keyof A as P extends "_tag" ? never : P]: A[P]; }) => Micro.YieldableError & {
    readonly _tag: "InvalidRouteConfig";
} & Readonly<A>;
declare class InvalidRouteConfigError extends /** #__PURE__ */ InvalidRouteConfigError_base<{
    reason: string;
}> {
    constructor(type: string, field?: string);
}
declare const UnknownFileTypeError_base: new <A extends Record<string, any> = {}>(args: effect_Types.Equals<A, {}> extends true ? void : { readonly [P in keyof A as P extends "_tag" ? never : P]: A[P]; }) => Micro.YieldableError & {
    readonly _tag: "UnknownFileType";
} & Readonly<A>;
declare class UnknownFileTypeError extends /** #__PURE__ */ UnknownFileTypeError_base<{
    reason: string;
}> {
    constructor(fileName: string);
}
declare const InvalidFileTypeError_base: new <A extends Record<string, any> = {}>(args: effect_Types.Equals<A, {}> extends true ? void : { readonly [P in keyof A as P extends "_tag" ? never : P]: A[P]; }) => Micro.YieldableError & {
    readonly _tag: "InvalidFileType";
} & Readonly<A>;
declare class InvalidFileTypeError extends /** #__PURE__ */ InvalidFileTypeError_base<{
    reason: string;
}> {
    constructor(fileType: string, fileName: string);
}
declare const InvalidFileSizeError_base: new <A extends Record<string, any> = {}>(args: effect_Types.Equals<A, {}> extends true ? void : { readonly [P in keyof A as P extends "_tag" ? never : P]: A[P]; }) => Micro.YieldableError & {
    readonly _tag: "InvalidFileSize";
} & Readonly<A>;
declare class InvalidFileSizeError extends /** #__PURE__ */ InvalidFileSizeError_base<{
    reason: string;
}> {
    constructor(fileSize: string);
}
declare const InvalidURLError_base: new <A extends Record<string, any> = {}>(args: effect_Types.Equals<A, {}> extends true ? void : { readonly [P in keyof A as P extends "_tag" ? never : P]: A[P]; }) => Micro.YieldableError & {
    readonly _tag: "InvalidURL";
} & Readonly<A>;
declare class InvalidURLError extends /** #__PURE__ */ InvalidURLError_base<{
    reason: string;
}> {
    constructor(attemptedUrl: string);
}
declare const RetryError_base: new <A extends Record<string, any> = {}>(args: effect_Types.Equals<A, {}> extends true ? void : { readonly [P in keyof A as P extends "_tag" ? never : P]: A[P]; }) => Micro.YieldableError & {
    readonly _tag: "RetryError";
} & Readonly<A>;
declare class RetryError extends /** #__PURE__ */ RetryError_base {
}
declare const FetchError_base: new <A extends Record<string, any> = {}>(args: effect_Types.Equals<A, {}> extends true ? void : { readonly [P in keyof A as P extends "_tag" ? never : P]: A[P]; }) => Micro.YieldableError & {
    readonly _tag: "FetchError";
} & Readonly<A>;
declare class FetchError extends /** #__PURE__ */ FetchError_base<{
    readonly input: {
        url: string;
        method: string | undefined;
        body: unknown;
        headers: Record<string, string>;
    };
    readonly error: unknown;
}> {
}
declare const InvalidJsonError_base: new <A extends Record<string, any> = {}>(args: effect_Types.Equals<A, {}> extends true ? void : { readonly [P in keyof A as P extends "_tag" ? never : P]: A[P]; }) => Micro.YieldableError & {
    readonly _tag: "InvalidJson";
} & Readonly<A>;
declare class InvalidJsonError extends /** #__PURE__ */ InvalidJsonError_base<{
    readonly input: unknown;
    readonly error: unknown;
}> {
}
declare const BadRequestError_base: new <A extends Record<string, any> = {}>(args: effect_Types.Equals<A, {}> extends true ? void : { readonly [P in keyof A as P extends "_tag" ? never : P]: A[P]; }) => Micro.YieldableError & {
    readonly _tag: "BadRequestError";
} & Readonly<A>;
declare class BadRequestError<T = unknown> extends /** #__PURE__ */ BadRequestError_base<{
    readonly message: string;
    readonly status: number;
    readonly json: T;
}> {
    getMessage(): string;
}
declare const UploadPausedError_base: new <A extends Record<string, any> = {}>(args: effect_Types.Equals<A, {}> extends true ? void : { readonly [P in keyof A as P extends "_tag" ? never : P]: A[P]; }) => Micro.YieldableError & {
    readonly _tag: "UploadAborted";
} & Readonly<A>;
declare class UploadPausedError extends /** #__PURE__ */ UploadPausedError_base {
}
declare const UploadAbortedError_base: new <A extends Record<string, any> = {}>(args: effect_Types.Equals<A, {}> extends true ? void : { readonly [P in keyof A as P extends "_tag" ? never : P]: A[P]; }) => Micro.YieldableError & {
    readonly _tag: "UploadAborted";
} & Readonly<A>;
declare class UploadAbortedError extends /** #__PURE__ */ UploadAbortedError_base {
}

declare function isRouteArray(routeConfig: FileRouterInputConfig): routeConfig is FileRouterInputKey[];
declare function getDefaultSizeForType(fileType: FileRouterInputKey): FileSize;
declare function getDefaultRouteConfigValues(type: FileRouterInputKey): RouteConfig<Record<string, never>>;
/**
 * This function takes in the user's input and "upscales" it to a full config
 * Additionally, it replaces numbers with "safe" equivalents
 *
 * Example:
 * ```ts
 * ["image"] => { image: { maxFileSize: "4MB", limit: 1 } }
 * ```
 */
declare const fillInputRouteConfig: (routeConfig: FileRouterInputConfig) => Micro.Micro<ExpandedRouteConfig, InvalidRouteConfigError>;
/**
 * Match the file's type for a given allow list e.g. `image/png => image`
 * Prefers the file's type, then falls back to a extension-based lookup
 */
declare const matchFileType: (file: FileProperties, allowedTypes: FileRouterInputKey[]) => Micro.Micro<FileRouterInputKey, UnknownFileTypeError | InvalidFileTypeError>;
declare const FILESIZE_UNITS: readonly ["B", "KB", "MB", "GB", "TB"];
type FileSizeUnit = (typeof FILESIZE_UNITS)[number];
declare const fileSizeToBytes: (fileSize: FileSize) => Micro.Micro<number, InvalidFileSizeError>;
declare const bytesToFileSize: (bytes: number) => string;
declare function safeParseJSON<T>(input: ResponseEsque): Promise<T | Error>;
/** typesafe Object.keys */
declare function objectKeys<T extends Record<string, unknown>>(obj: T): (keyof T)[];
declare function filterDefinedObjectValues<T>(obj: Record<string, T | null | undefined>): Record<string, T>;
declare function semverLite(required: string, toCheck: string): boolean;
declare function warnIfInvalidPeerDependency(pkg: string, required: string, toCheck: string): void;
declare const getRequestUrl: (req: Request) => Micro.Micro<URL, InvalidURLError, never>;
declare const getFullApiUrl: (maybeUrl?: string) => Micro.Micro<URL, InvalidURLError>;
declare const resolveMaybeUrlArg: (maybeUrl: string | URL | undefined) => URL;
declare function parseTimeToSeconds(time: Time): number;
/**
 * Replacer for JSON.stringify that will replace numbers that cannot be
 * serialized to JSON with "reasonable equivalents".
 *
 * Infinity and -Infinity are replaced by MAX_SAFE_INTEGER and MIN_SAFE_INTEGER
 * NaN is replaced by 0
 *
 */
declare const safeNumberReplacer: (_: string, value: unknown) => unknown;
declare function noop(): void;
declare function createIdentityProxy<TObj extends Record<string, unknown>>(): TObj;
declare function unwrap<T extends Json | PropertyKey, Param extends unknown[]>(x: T | ((...args: Param) => T), ...args: Param): T;

declare const ERROR_CODES: {
    readonly BAD_REQUEST: 400;
    readonly NOT_FOUND: 404;
    readonly FORBIDDEN: 403;
    readonly INTERNAL_SERVER_ERROR: 500;
    readonly INTERNAL_CLIENT_ERROR: 500;
    readonly TOO_LARGE: 413;
    readonly TOO_SMALL: 400;
    readonly TOO_MANY_FILES: 400;
    readonly KEY_TOO_LONG: 400;
    readonly URL_GENERATION_FAILED: 500;
    readonly UPLOAD_FAILED: 500;
    readonly MISSING_ENV: 500;
    readonly INVALID_SERVER_CONFIG: 500;
    readonly FILE_LIMIT_EXCEEDED: 500;
};
type ErrorCode = keyof typeof ERROR_CODES;
type UploadThingErrorOptions<T> = {
    code: keyof typeof ERROR_CODES;
    message?: string | undefined;
    cause?: unknown;
    data?: T;
};
interface SerializedUploadThingError {
    code: ErrorCode;
    message: string;
    data?: Json;
}
declare class UploadThingError<TShape extends Json = {
    message: string;
}> extends Micro.Error<{
    message: string;
}> {
    readonly _tag = "UploadThingError";
    readonly name = "UploadThingError";
    readonly cause?: unknown;
    readonly code: ErrorCode;
    readonly data: TShape | undefined;
    constructor(initOpts: UploadThingErrorOptions<TShape> | string);
    static toObject(error: UploadThingError): SerializedUploadThingError;
    static serialize(error: UploadThingError): string;
}
declare function getErrorTypeFromStatusCode(statusCode: number): ErrorCode;
declare function getStatusCodeFromError(error: UploadThingError<any>): 400 | 404 | 403 | 500 | 413;
declare const INTERNAL_DO_NOT_USE__fatalClientError: (e: Error) => UploadThingError<{
    message: string;
}>;

declare const FetchContext_base: Context.TagClass<FetchContext, "uploadthing/Fetch", FetchEsque>;
declare class FetchContext extends /** #__PURE__ */ FetchContext_base {
}
interface ResponseWithURL extends ResponseEsque {
    requestUrl: string;
}
declare const fetchEff: (input: string | URL, init?: RequestInit) => Micro.Micro<ResponseWithURL, FetchError, FetchContext>;
declare const parseResponseJson: (res: ResponseWithURL) => Micro.Micro<unknown, InvalidJsonError | BadRequestError>;

type ProgressGranularity = "all" | "fine" | "coarse";
declare const roundProgress: (progress: number, granularity: ProgressGranularity) => number;
declare const generateMimeTypes: (typesOrRouteConfig: string[] | ExpandedRouteConfig) => string[];
declare const generateClientDropzoneAccept: (fileTypes: string[]) => {
    [k: string]: never[];
};
declare function getFilesFromClipboardEvent(event: ClipboardEvent): File[] | undefined;
/**
 * Shared helpers for our premade components that's reusable by multiple frameworks
 */
declare const generatePermittedFileTypes: (config?: ExpandedRouteConfig) => {
    fileTypes: FileRouterInputKey[];
    multiple: boolean;
};
declare const capitalizeStart: (str: string) => string;
declare const INTERNAL_doFormatting: (config?: ExpandedRouteConfig) => string;
declare const allowedContentTextLabelGenerator: (config?: ExpandedRouteConfig) => string;
type AnyRuntime = "react" | "solid" | "svelte" | "vue";
type MinCallbackArg = {
    __runtime: AnyRuntime;
};
type inferRuntime<T extends MinCallbackArg> = T["__runtime"] extends "react" ? "react" : T["__runtime"] extends "solid" ? "solid" : T["__runtime"] extends "svelte" ? "svelte" : T["__runtime"] extends "vue" ? "vue" : never;
type ElementEsque<TRuntime extends AnyRuntime> = TRuntime extends "react" ? ReactNode : TRuntime extends "solid" ? JSX.Element : ReturnType<RenderFunction>;
type CSSPropertiesEsque<TRuntime extends AnyRuntime> = TRuntime extends "react" ? CSSProperties : TRuntime extends "solid" ? JSX.CSSProperties : TRuntime extends "svelte" ? string : TRuntime extends "vue" ? StyleValue : never;
type StyleField<CallbackArg extends MinCallbackArg, TRuntime extends AnyRuntime = inferRuntime<CallbackArg>> = string | CSSPropertiesEsque<TRuntime> | ((arg: Omit<CallbackArg, "__runtime">) => string | CSSPropertiesEsque<TRuntime>);
type ContentField<CallbackArg extends MinCallbackArg, TRuntime extends AnyRuntime = inferRuntime<CallbackArg>> = ElementEsque<TRuntime> | ((arg: Omit<CallbackArg, "__runtime">) => ElementEsque<TRuntime>);
declare const styleFieldToClassName: <T extends MinCallbackArg>(styleField: StyleField<T> | undefined, args: T) => string;
declare const styleFieldToCssObject: <T extends MinCallbackArg>(styleField: StyleField<T> | undefined, args: T) => (CSSPropertiesEsque<inferRuntime<T>> & null) | {};
declare const contentFieldToContent: <T extends MinCallbackArg>(contentField: ContentField<T> | undefined, arg: T) => ElementEsque<inferRuntime<T>> | null | undefined;
type ClassListMerger = (...classes: (string | null | undefined | false)[]) => string;
declare const defaultClassListMerger: ClassListMerger;

declare const signPayload: (payload: string, secret: Redacted.Redacted<string>) => Micro.Micro<string, UploadThingError<{
    message: string;
}>, never>;
declare const verifySignature: (payload: string, signature: string | null, secret: Redacted.Redacted<string>) => Micro.Micro<boolean, never, never>;
declare const generateKey: (file: FileProperties, appId: string, getHashParts?: ExtractHashPartsFn) => Micro.Micro<string, never, never>;
declare const verifyKey: (key: string, appId: string) => Micro.Micro<boolean, never, never>;
declare const generateSignedURL: (url: string | URL, secretKey: Redacted.Redacted<string>, opts: {
    ttlInSeconds?: Time | undefined;
    data?: Record<string, string | number | boolean | null | undefined>;
}) => Micro.Micro<string, UploadThingError<{
    message: string;
}>, never>;

type AcceptProp = Record<string, string[]>;
type DropzoneOptions = {
    multiple?: boolean;
    accept?: AcceptProp | undefined;
    minSize?: number;
    maxSize?: number;
    maxFiles?: number;
    disabled?: boolean | undefined;
    onDrop: <T extends File>(acceptedFiles: T[]) => void;
};
type DropzoneState = {
    isFocused: boolean;
    isDragActive: boolean;
    isDragAccept: boolean;
    isDragReject: boolean;
    isFileDialogActive: boolean;
    acceptedFiles: File[];
};
declare const isPropagationStopped: (event: Event & {
    isPropagationStopped?: () => boolean;
}) => boolean;
declare function isFileAccepted(file: File, accept: string | string[]): boolean;
declare function isEnterOrSpace(event: {
    key?: string;
    keyCode?: number;
}): boolean;
declare function isValidSize(file: File, minSize: number, maxSize: number): boolean;
declare function isValidQuantity(files: File[], multiple: boolean, maxFiles: number): boolean;
declare function allFilesAccepted({ files, accept, minSize, maxSize, multiple, maxFiles, }: {
    files: File[];
    accept: string | string[];
    minSize: number;
    maxSize: number;
    multiple: boolean;
    maxFiles: number;
}): boolean;
declare function isEventWithFiles(event: Partial<Event>): boolean;
declare function isIeOrEdge(ua?: string): boolean;
/**
 * Convert the `{accept}` dropzone prop to an array of MIME types/extensions.
 */
declare function acceptPropAsAcceptAttr(accept?: AcceptProp): string | undefined;
/**
 * ================================================
 *                    Reducer
 * ================================================
 */
type Payload<T extends keyof DropzoneState> = Pick<DropzoneState, T>;
type Focus = {
    type: "focus";
};
type Blur = {
    type: "blur";
};
type OpenDialog = {
    type: "openDialog";
};
type CloseDialog = {
    type: "closeDialog";
};
type SetDraggedFiles = {
    type: "setDraggedFiles";
    payload: Payload<"isDragActive" | "isDragAccept" | "isDragReject">;
};
type SetFiles = {
    type: "setFiles";
    payload: Payload<"acceptedFiles">;
};
type Reset = {
    type: "reset";
};
type DropzoneActions = Focus | Blur | OpenDialog | CloseDialog | SetDraggedFiles | SetFiles | Reset;
declare const initialState: {
    isFocused: boolean;
    isFileDialogActive: boolean;
    isDragActive: boolean;
    isDragAccept: boolean;
    isDragReject: boolean;
    acceptedFiles: File[];
};
declare function reducer(state: DropzoneState, action: DropzoneActions): DropzoneState;

export { ALLOWED_FILE_TYPES, BadRequestError, FILESIZE_UNITS, FetchContext, FetchError, INTERNAL_DO_NOT_USE__fatalClientError, INTERNAL_doFormatting, InvalidFileSizeError, InvalidFileTypeError, InvalidJsonError, InvalidRouteConfigError, InvalidURLError, RetryError, UnknownFileTypeError, UploadAbortedError, UploadPausedError, UploadThingError, ValidACLs, ValidContentDispositions, acceptPropAsAcceptAttr, allFilesAccepted, allowedContentTextLabelGenerator, bytesToFileSize, capitalizeStart, contentFieldToContent, createIdentityProxy, defaultClassListMerger, fetchEff, fileSizeToBytes, fillInputRouteConfig, filterDefinedObjectValues, generateClientDropzoneAccept, generateKey, generateMimeTypes, generatePermittedFileTypes, generateSignedURL, getDefaultRouteConfigValues, getDefaultSizeForType, getErrorTypeFromStatusCode, getFilesFromClipboardEvent, getFullApiUrl, getRequestUrl, getStatusCodeFromError, initialState, isEnterOrSpace, isEventWithFiles, isFileAccepted, isIeOrEdge, isPropagationStopped, isRouteArray, isValidQuantity, isValidSize, matchFileType, noop, objectKeys, parseResponseJson, parseTimeToSeconds, reducer, resolveMaybeUrlArg, roundProgress, safeNumberReplacer, safeParseJSON, semverLite, signPayload, styleFieldToClassName, styleFieldToCssObject, unwrap, verifyKey, verifySignature, warnIfInvalidPeerDependency };
export type { ACL, AcceptProp, AllowedFileType, ClassListMerger, ContentDisposition, ContentField, DeepPartial, DropzoneOptions, DropzoneState, Either, EndpointMetadata, ErrorMessage, ExpandedRouteConfig, ExtendObjectIf, ExtractHashPartsFn, FetchEsque, FileProperties, FileRouterInputConfig, FileRouterInputKey, FileSize, FileSizeUnit, Json, JsonArray, JsonObject, JsonValue, MaybePromise, MaybeUrl, Overwrite, ProgressGranularity, RequestInitEsque, ResponseEsque, RouteConfig, RouteOptions, SerializedUploadThingError, Simplify, SizeUnit, StyleField, Time, TimeLong, TimeShort, WithRequired };
