import * as internal from "./internal/console.js";
import * as defaultConsole from "./internal/defaultServices/console.js";
/**
 * @since 2.0.0
 * @category type ids
 */
export const TypeId = defaultConsole.TypeId;
/**
 * @since 2.0.0
 * @category context
 */
export const Console = defaultConsole.consoleTag;
/**
 * @since 2.0.0
 * @category default services
 */
export const withConsole = internal.withConsole;
/**
 * @since 2.0.0
 * @category default services
 */
export const setConsole = internal.setConsole;
/**
 * @since 2.0.0
 * @category accessor
 */
export const consoleWith = internal.consoleWith;
/**
 * @since 2.0.0
 * @category accessor
 */
export const assert = internal.assert;
/**
 * @since 2.0.0
 * @category accessor
 */
export const clear = internal.clear;
/**
 * @since 2.0.0
 * @category accessor
 */
export const count = internal.count;
/**
 * @since 2.0.0
 * @category accessor
 */
export const countReset = internal.countReset;
/**
 * @since 2.0.0
 * @category accessor
 */
export const debug = internal.debug;
/**
 * @since 2.0.0
 * @category accessor
 */
export const dir = internal.dir;
/**
 * @since 2.0.0
 * @category accessor
 */
export const dirxml = internal.dirxml;
/**
 * @since 2.0.0
 * @category accessor
 */
export const error = internal.error;
/**
 * @since 2.0.0
 * @category accessor
 */
export const group = internal.group;
/**
 * @since 2.0.0
 * @category accessor
 */
export const info = internal.info;
/**
 * @since 2.0.0
 * @category accessor
 */
export const log = internal.log;
/**
 * @since 2.0.0
 * @category accessor
 */
export const table = internal.table;
/**
 * @since 2.0.0
 * @category accessor
 */
export const time = internal.time;
/**
 * @since 2.0.0
 * @category accessor
 */
export const timeLog = internal.timeLog;
/**
 * @since 2.0.0
 * @category accessor
 */
export const trace = internal.trace;
/**
 * @since 2.0.0
 * @category accessor
 */
export const warn = internal.warn;
/**
 * @since 2.0.0
 * @category accessor
 */
export const withGroup = internal.withGroup;
/**
 * @since 2.0.0
 * @category accessor
 */
export const withTime = internal.withTime;
//# sourceMappingURL=Console.js.map