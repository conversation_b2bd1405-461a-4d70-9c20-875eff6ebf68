(()=>{var e={};e.id=9735,e.ids=[9735],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12484:(e,t,r)=>{"use strict";r.d(t,{ES:()=>s,eo:()=>o});let o=["en","ar"],s={en:{translation:{"sidebar.analytics":"Analytics","sidebar.user":"Dashboard","sidebar.clients":"Clients","sidebar.messaging":"Messaging","sidebar.marketing":"Marketing","sidebar.campaigns":"Campaigns","sidebar.templates":"Templates","sidebar.appointments":"Appointments","sidebar.ai-chatbot":"AI Chatbot","sidebar.users":"Users","sidebar.properties":"Properties","sidebar.settings":"Settings","sidebar.profile":"Profile","role.admin":"Administrator","role.agent":"Agent","role.client":"Client","users.invite":"Invite User","users.role.change":"Change Role","users.edit":"Edit User","users.delete":"Delete User","users.status.active":"Active","users.status.inactive":"Inactive","users.status.suspended":"Suspended","form.email":"Email","form.firstName":"First Name","form.lastName":"Last Name","form.role":"Role","form.cancel":"Cancel","form.save":"Save Changes","form.send":"Send Invitation","toast.success":"Success","toast.error":"Error","toast.role.updated":"Role updated successfully","toast.invitation.sent":"Invitation sent successfully","property.create.title":"Create New Property","property.create.subtitle":"Add a new property listing to your real estate portfolio","property.form.propertyTitle":"Property Title","property.form.price":"Price (USD)","property.form.location":"Location","property.form.bedrooms":"Bedrooms","property.form.bathrooms":"Bathrooms","property.form.area":"Area (sq ft)","property.form.status":"Status","property.form.type":"Property Type","property.form.description":"Description","property.form.images":"Property Images","property.form.uploadImages":"Upload Images","property.form.propertyInfo":"Property Information","property.form.requiredFields":"All fields marked with * are required. Adding high-quality images will increase the visibility of your property listing.","property.form.cancel":"Cancel","property.form.create":"Create Property","property.form.creating":"Creating Property...","property.form.selectStatus":"Select status","property.form.selectType":"Select type","property.form.enterTitle":"Enter property title","property.form.enterPrice":"Enter price","property.form.enterLocation":"Enter property location","property.form.enterBedrooms":"Number of bedrooms","property.form.enterBathrooms":"Number of bathrooms","property.form.enterArea":"Property area","property.form.enterDescription":"Enter property description","property.status.active":"Active","property.status.pending":"Pending","property.status.sold":"Sold","property.type.villa":"Villa","property.type.apartment":"Apartment","property.type.townhouse":"Townhouse","property.type.penthouse":"Penthouse","property.type.duplex":"Duplex","property.toast.success.title":"Property created successfully!","property.toast.success.description":"Your property has been added to the listings.","property.toast.error.title":"Failed to create property","property.toast.error.description":"There was an error creating your property. Please try again.","auth.signIn":"Sign In","auth.signUp":"Sign Up","auth.createAccount":"Create Account","auth.emailAddress":"Email Address","auth.password":"Password","auth.newPassword":"New Password","auth.confirmPassword":"Confirm Password","auth.continue":"Continue","auth.continueWithGoogle":"Continue with Google","auth.orContinueWith":"or continue with","auth.noAccount":"Don't have an account?","auth.alreadyHaveAccount":"Already have an account?","auth.forgotPassword":"Forgot password?","auth.resetPassword":"Reset Password","auth.resetPasswordInstructions":"Enter your email address and we'll send you a link to reset your password.","auth.goBack":"Go back","auth.checkEmail":"Check your email","auth.codeSentTo":"We sent a code to","auth.verificationCode":"Verification Code","auth.resendCode":"Resend Code","auth.enterPassword":"Enter your password","auth.completeYourProfile":"Complete your profile","auth.firstName":"First Name","auth.lastName":"Last Name","auth.redirectMessage":"You need to sign in to access this page. You'll be redirected after authentication.","auth.createAccountRedirect":"You need to create an account to access this page. You'll be redirected after registration.","auth.accessDenied":"Access Denied","auth.insufficientPermissions":"You don't have permission to access this page. Your current role is: {{role}}.","auth.notAuthenticated":"You need to be authenticated to access this page.","auth.contactAdminForAccess":"Please contact an administrator if you need access to this resource.","auth.welcomeBack":"Welcome Back","auth.signInToContinue":"Sign in to continue to your account","auth.enterDetailsToCreateAccount":"Enter your details to create an account","auth.email":"Email","auth.invalidCredentials":"Invalid email or password","auth.registrationFailed":"Registration failed","auth.signInAfterRegistrationFailed":"Registration successful but sign-in failed","auth.registrationSuccessful":"Registration successful","auth.loginSuccessful":"Login successful","auth.signingOut":"Signing Out","auth.redirectingToSignIn":"Redirecting to sign-in page...","common.somethingWentWrong":"Something went wrong. Please try again.","common.loading":"Loading...","common.goBack":"Go Back","common.goToHomePage":"Go to Home Page"}},ar:{translation:{"sidebar.analytics":"التحليلات","sidebar.user":"لوحة التحكم","sidebar.clients":"العملاء","sidebar.messaging":"المراسلة","sidebar.marketing":"التسويق","sidebar.campaigns":"الحملات","sidebar.templates":"القوالب","sidebar.appointments":"المواعيد","sidebar.ai-chatbot":"روبوت المحادثة","sidebar.users":"المستخدمين","sidebar.properties":"العقارات","sidebar.settings":"الإعدادات","sidebar.profile":"الملف الشخصي","role.admin":"مدير","role.agent":"وكيل","role.client":"عميل","users.invite":"دعوة مستخدم","users.role.change":"تغيير الدور","users.edit":"تعديل المستخدم","users.delete":"حذف المستخدم","users.status.active":"نشط","users.status.inactive":"غير نشط","users.status.suspended":"معلق","form.email":"البريد الإلكتروني","form.firstName":"الاسم الأول","form.lastName":"اسم العائلة","form.role":"الدور","form.cancel":"إلغاء","form.save":"حفظ التغييرات","form.send":"إرسال الدعوة","toast.success":"نجاح","toast.error":"خطأ","toast.role.updated":"تم تحديث الدور بنجاح","toast.invitation.sent":"تم إرسال الدعوة بنجاح","property.create.title":"إنشاء عقار جديد","property.create.subtitle":"أضف قائمة عقارية جديدة إلى محفظتك العقارية","property.form.propertyTitle":"عنوان العقار","property.form.price":"السعر (دولار أمريكي)","property.form.location":"الموقع","property.form.bedrooms":"غرف النوم","property.form.bathrooms":"الحمامات","property.form.area":"المساحة (قدم مربع)","property.form.status":"الحالة","property.form.type":"نوع العقار","property.form.description":"الوصف","property.form.images":"صور العقار","property.form.uploadImages":"تحميل الصور","property.form.propertyInfo":"معلومات العقار","property.form.requiredFields":"جميع الحقول المميزة بعلامة * مطلوبة. إضافة صور عالية الجودة ستزيد من ظهور قائمة العقار الخاص بك.","property.form.cancel":"إلغاء","property.form.create":"إنشاء العقار","property.form.creating":"جاري إنشاء العقار...","property.form.selectStatus":"اختر الحالة","property.form.selectType":"اختر النوع","property.form.enterTitle":"أدخل عنوان العقار","property.form.enterPrice":"أدخل السعر","property.form.enterLocation":"أدخل موقع العقار","property.form.enterBedrooms":"عدد غرف النوم","property.form.enterBathrooms":"عدد الحمامات","property.form.enterArea":"مساحة العقار","property.form.enterDescription":"أدخل وصف العقار","property.status.active":"نشط","property.status.pending":"قيد الانتظار","property.status.sold":"مباع","property.type.villa":"فيلا","property.type.apartment":"شقة","property.type.townhouse":"تاون هاوس","property.type.penthouse":"بنتهاوس","property.type.duplex":"دوبلكس","property.toast.success.title":"تم إنشاء العقار بنجاح!","property.toast.success.description":"تمت إضافة العقار الخاص بك إلى القوائم.","property.toast.error.title":"فشل في إنشاء العقار","property.toast.error.description":"حدث خطأ أثناء إنشاء العقار الخاص بك. يرجى المحاولة مرة أخرى.","auth.signIn":"تسجيل الدخول","auth.signUp":"إنشاء حساب","auth.createAccount":"إنشاء حساب جديد","auth.emailAddress":"البريد الإلكتروني","auth.password":"كلمة المرور","auth.newPassword":"كلمة المرور الجديدة","auth.confirmPassword":"تأكيد كلمة المرور","auth.continue":"متابعة","auth.continueWithGoogle":"متابعة باستخدام جوجل","auth.orContinueWith":"أو متابعة باستخدام","auth.noAccount":"ليس لديك حساب؟","auth.alreadyHaveAccount":"لديك حساب بالفعل؟","auth.forgotPassword":"نسيت كلمة المرور؟","auth.resetPassword":"إعادة تعيين كلمة المرور","auth.resetPasswordInstructions":"أدخل بريدك الإلكتروني وسنرسل لك رابطًا لإعادة تعيين كلمة المرور.","auth.goBack":"العودة","auth.checkEmail":"تحقق من بريدك الإلكتروني","auth.codeSentTo":"لقد أرسلنا رمزًا إلى","auth.verificationCode":"رمز التحقق","auth.resendCode":"إعادة إرسال الرمز","auth.enterPassword":"أدخل كلمة المرور","auth.completeYourProfile":"أكمل ملفك الشخصي","auth.firstName":"الاسم الأول","auth.lastName":"اسم العائلة","auth.redirectMessage":"تحتاج إلى تسجيل الدخول للوصول إلى هذه الصفحة. ستتم إعادة توجيهك بعد المصادقة.","auth.createAccountRedirect":"تحتاج إلى إنشاء حساب للوصول إلى هذه الصفحة. ستتم إعادة توجيهك بعد التسجيل.","auth.accessDenied":"تم رفض الوصول","auth.insufficientPermissions":"ليس لديك إذن للوصول إلى هذه الصفحة. دورك الحالي هو: {{role}}.","auth.notAuthenticated":"تحتاج إلى المصادقة للوصول إلى هذه الصفحة.","auth.contactAdminForAccess":"يرجى الاتصال بمسؤول إذا كنت بحاجة إلى الوصول إلى هذا المورد.","auth.welcomeBack":"مرحبًا بعودتك","auth.signInToContinue":"قم بتسجيل الدخول للمتابعة إلى حسابك","auth.enterDetailsToCreateAccount":"أدخل بياناتك لإنشاء حساب","auth.email":"البريد الإلكتروني","auth.invalidCredentials":"بريد إلكتروني أو كلمة مرور غير صالحة","auth.registrationFailed":"فشل التسجيل","auth.signInAfterRegistrationFailed":"تم التسجيل بنجاح ولكن فشل تسجيل الدخول","auth.registrationSuccessful":"تم التسجيل بنجاح","auth.loginSuccessful":"تم تسجيل الدخول بنجاح","auth.signingOut":"تسجيل الخروج","auth.redirectingToSignIn":"جاري إعادة التوجيه إلى صفحة تسجيل الدخول...","common.somethingWentWrong":"حدث خطأ ما. يرجى المحاولة مرة أخرى.","common.loading":"جار التحميل...","common.goBack":"العودة","common.goToHomePage":"الذهاب إلى الصفحة الرئيسية"}}}},14218:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>l,tree:()=>p});var o=r(65239),s=r(48088),a=r(88170),n=r.n(a),i=r(30893),c={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);r.d(t,c);let p={children:["",{children:["access-denied",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,33896)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\access-denied\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,60566)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\access-denied\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},l=new o.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/access-denied/page",pathname:"/access-denied",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24934:(e,t,r)=>{"use strict";r.d(t,{$:()=>p,r:()=>c});var o=r(60687),s=r(43210),a=r(8730),n=r(24224),i=r(96241);let c=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),p=s.forwardRef(({className:e,variant:t,size:r,asChild:s=!1,...n},p)=>{let d=s?a.DX:"button";return(0,o.jsx)(d,{className:(0,i.cn)(c({variant:t,size:r,className:e})),ref:p,...n})});p.displayName="Button"},28559:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});let o=(0,r(62688).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32192:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});let o=(0,r(62688).A)("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},33873:e=>{"use strict";e.exports=require("path")},33896:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});let o=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\access-denied\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\access-denied\\page.tsx","default")},41705:(e,t,r)=>{Promise.resolve().then(r.bind(r,33896))},59042:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var o=r(60687),s=r(43210),a=r(68082),n=r(24934),i=r(16189),c=r(72840),p=r(28559),d=r(32192),u=r(99208);function l(){let{t:e}=(0,a.B)(),t=(0,i.useRouter)(),{data:r,status:l}=(0,u.wV)(),[m,h]=(0,s.useState)("loading"===l);return m?(0,o.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-screen bg-background",children:[(0,o.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-4"}),(0,o.jsx)("h1",{className:"text-2xl font-bold mb-2",children:e("common.loading")})]}):(0,o.jsx)("div",{className:"flex flex-col items-center justify-center min-h-screen bg-background p-4",children:(0,o.jsxs)("div",{className:"w-full max-w-md p-6 bg-card rounded-lg shadow-xl text-center",children:[(0,o.jsx)("div",{className:"flex justify-center mb-6",children:(0,o.jsx)(c.A,{className:"h-16 w-16 text-destructive"})}),(0,o.jsx)("h1",{className:"text-2xl font-bold mb-4",children:e("auth.accessDenied")}),(0,o.jsx)("p",{className:"text-muted-foreground mb-6",children:"authenticated"===l?e("auth.insufficientPermissions",{role:r?.user?.role||"user"}):e("auth.notAuthenticated")}),(0,o.jsx)("p",{className:"text-sm text-muted-foreground mb-8",children:e("auth.contactAdminForAccess")}),(0,o.jsxs)("div",{className:"flex flex-col space-y-3",children:[(0,o.jsxs)(n.$,{onClick:()=>{t.back()},variant:"outline",className:"w-full",children:[(0,o.jsx)(p.A,{className:"mr-2 h-4 w-4"}),e("common.goBack")]}),(0,o.jsxs)(n.$,{onClick:()=>{t.push("/")},variant:"default",className:"w-full",children:[(0,o.jsx)(d.A,{className:"mr-2 h-4 w-4"}),e("common.goToHomePage")]})]})]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68082:(e,t,r)=>{"use strict";r.d(t,{B:()=>n});var o=r(46755),s=r(16457),a=r(12484);function n(){return(0,s.Bd)()}o.Ay.use(s.r9).init({lng:"en",fallbackLng:"en",resources:a.ES,interpolation:{escapeValue:!1}})},72840:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});let o=(0,r(62688).A)("ShieldAlert",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"M12 8v4",key:"1got3b"}],["path",{d:"M12 16h.01",key:"1drbdi"}]])},83561:(e,t,r)=>{Promise.resolve().then(r.bind(r,59042))}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[7719,9656,3903,8722],()=>r(14218));module.exports=o})();