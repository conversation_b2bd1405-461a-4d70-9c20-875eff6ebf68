'use client';

/**
 * Cookie cleanup utility for Arabic Properties system
 * Removes unnecessary cookies and fixes session issues
 */

export function cleanupCookies() {
  if (typeof window === 'undefined') return;

  // List of cookies to remove
  const cookiesToRemove = [
    '__next_hmr_refresh_hash__',
    '__clerk_db_jwt_NFfxy5s4',
    '__refresh_NFfxy5s4',
    '__session_NFfxy5s4',
    '__client_uat_NFfxy5s4',
    '__clerk_db_jwt_TYLMw0H7',
    '__refresh_TYLMw0H7',
    '__session_TYLMw0H7',
    '__client_uat_TYLMw0H7',
    '__clerk_db_jwt',
    '__clerk_db_jwt_kCaGdcWF',
    '__client_uat_kCaGdcWF',
    '__client_uat',
    'NEXT_LOCALE',
    'authjs.csrf-token',
    'authjs.callback-url',
  ];

  // Remove each cookie
  cookiesToRemove.forEach(cookieName => {
    // Remove from current domain
    document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
    
    // Remove from all possible domains
    const domains = [
      window.location.hostname,
      `.${window.location.hostname}`,
      'localhost',
      '.localhost'
    ];

    domains.forEach(domain => {
      document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=${domain};`;
    });
  });

  // Set Arabic language preference
  document.cookie = 'language=ar; path=/; max-age=31536000'; // 1 year
  
  console.log('🧹 Cookies cleaned up for Arabic Properties system');
}

/**
 * Initialize Arabic-only environment
 */
export function initializeArabicEnvironment() {
  if (typeof window === 'undefined') return;

  // Clean up cookies first
  cleanupCookies();

  // Set document language and direction
  document.documentElement.lang = 'ar';
  document.documentElement.dir = 'rtl';
  document.documentElement.className = 'rtl arabic-interface';

  // Set Arabic fonts
  document.body.style.fontFamily = "'Cairo', 'Noto Sans Arabic', 'Tajawal', 'Amiri', sans-serif";

  // Add Arabic CSS class to body
  document.body.classList.add('lang-ar', 'rtl-container');

  console.log('🌟 Arabic environment initialized');
}

/**
 * Check if running in development mode
 */
export function isDevelopment() {
  return process.env.NODE_ENV === 'development';
}

/**
 * Safe console log for production
 */
export function safeLog(message: string, ...args: any[]) {
  if (isDevelopment()) {
    console.log(message, ...args);
  }
}
