import { Readable } from 'node:stream';
import * as Effect from 'effect/Effect';
import { Router } from 'express';
import { makeAdapterHandler } from '../dist/_internal/handler.js';
import { getPostBody, toWebRequest } from '../dist/_internal/to-web-request.js';
import { createBuilder } from '../dist/_internal/upload-builder.js';
export { UTFiles, UTRegion as experimental_UTRegion } from '../dist/_internal/types.js';

const createUploadthing = (opts)=>createBuilder(opts);
const createRouteHandler = (opts)=>{
    const handler = makeAdapterHandler((req, res)=>Effect.succeed({
            req,
            res
        }), (req)=>Effect.flatMap(getPostBody({
            req
        }), (body)=>toWebRequest(req, body)).pipe(Effect.orDie), opts, "express");
    return Router().all("/", async (req, res)=>{
        const response = await handler(req, res);
        res.writeHead(response.status, Object.fromEntries(response.headers));
        if (response.body) {
            // Slight type mismatch in `node:stream.ReadableStream` and Fetch's `ReadableStream`.
            Readable.fromWeb(response.body).pipe(res);
        } else {
            res.end();
        }
    });
};

export { createRouteHandler, createUploadthing };
