'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { ArrowLeft, Home, ChevronRight, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useSimpleLanguage } from '@/hooks/useSimpleLanguage';
import { toast } from 'sonner';
import { PropertyFormSteps } from '../../create/property-form-steps';

interface Property {
  id: string;
  title: string;
  titleAr?: string;
  description: string;
  descriptionAr?: string;
  price: number;
  currency: string;
  type: string;
  status: string;
  bedrooms?: number;
  bathrooms?: number;
  area?: number;
  location: string;
  locationAr?: string;
  address: string;
  addressAr?: string;
  city: string;
  cityAr?: string;
  country: string;
  countryAr?: string;
  images: string[];
  features: string[];
  featuresAr: string[];
  amenities: string[];
  amenitiesAr: string[];
  yearBuilt?: number;
  parking?: number;
  furnished: boolean;
  petFriendly: boolean;
  utilities?: string;
  utilitiesAr?: string;
  contactInfo?: string;
  isFeatured: boolean;
  isActive: boolean;
}

export default function EditPropertyPage() {
  const router = useRouter();
  const params = useParams();
  const { language } = useSimpleLanguage();
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [property, setProperty] = useState<Property | null>(null);

  const translations = {
    ar: {
      editProperty: 'تعديل العقار',
      backToProperties: 'العودة إلى العقارات',
      subtitle: 'تحديث معلومات وتفاصيل العقار',
      properties: 'العقارات',
      home: 'الرئيسية',
      loading: 'جاري تحميل العقار...',
      notFound: 'العقار غير موجود',
      loadingError: 'فشل في تحميل العقار',
    },
    en: {
      editProperty: 'Edit Property',
      backToProperties: 'Back to Properties',
      subtitle: 'Update property information and details',
      properties: 'Properties',
      home: 'Home',
      loading: 'Loading property...',
      notFound: 'Property not found',
      loadingError: 'Failed to load property',
    }
  };

  const t = translations[language as keyof typeof translations] || translations.ar;

  useEffect(() => {
    const fetchProperty = async () => {
      if (!params.id) return;

      try {
        setInitialLoading(true);
        const response = await fetch(`/api/v1/properties/${params.id}`);

        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            setProperty(data.data);
          } else {
            toast.error(t.notFound);
            router.push('/dashboard/properties');
          }
        } else {
          // Fallback to mock data for testing
          console.log('Backend not available, using mock data');
          setProperty({
            id: params.id as string,
            title: 'Luxury Villa in Dubai Marina',
            titleAr: 'فيلا فاخرة في دبي مارينا',
            description: 'Beautiful 4-bedroom villa with sea view',
            descriptionAr: 'فيلا جميلة من 4 غرف نوم مع إطلالة على البحر',
            price: 2500000,
            currency: 'AED',
            type: 'VILLA',
            status: 'AVAILABLE',
            bedrooms: 4,
            bathrooms: 3,
            area: 350,
            location: 'Dubai Marina',
            locationAr: 'دبي مارينا',
            address: '123 Marina Walk',
            addressAr: '123 ممشى المارينا',
            city: 'Dubai',
            cityAr: 'دبي',
            country: 'UAE',
            countryAr: 'الإمارات العربية المتحدة',
            images: ['/placeholder.jpg'],
            features: ['Swimming Pool', 'Gym', 'Parking'],
            featuresAr: ['مسبح', 'صالة رياضية', 'موقف سيارات'],
            amenities: ['24/7 Security', 'Concierge'],
            amenitiesAr: ['أمن 24/7', 'خدمة الكونسيرج'],
            yearBuilt: 2020,
            parking: 2,
            furnished: true,
            petFriendly: false,
            utilities: 'Electricity, Water, Internet',
            utilitiesAr: 'كهرباء، ماء، إنترنت',
            contactInfo: 'Contact: +971 50 123 4567',
            isFeatured: true,
            isActive: true,
          });
        }
      } catch (error) {
        console.error('Error fetching property:', error);
        toast.error(t.loadingError);
        router.push('/dashboard/properties');
      } finally {
        setInitialLoading(false);
      }
    };

    fetchProperty();
  }, [params.id, router, t.loadingError, t.notFound]);

  const handleSave = async (formData: any) => {
    setLoading(true);
    try {
      const payload = {
        ...formData,
        price: parseFloat(formData.price),
        bedrooms: formData.bedrooms ? parseInt(formData.bedrooms) : undefined,
        bathrooms: formData.bathrooms ? parseInt(formData.bathrooms) : undefined,
        area: formData.area ? parseFloat(formData.area) : undefined,
        yearBuilt: formData.yearBuilt ? parseInt(formData.yearBuilt) : undefined,
        parking: formData.parking ? parseInt(formData.parking) : undefined,
      };

      const response = await fetch(`/api/v1/properties/${params.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (response.ok) {
        toast.success(language === 'ar' ? 'تم تحديث العقار بنجاح' : 'Property updated successfully');
        router.push('/dashboard/properties');
      } else {
        const error = await response.json();
        toast.error(error.message || (language === 'ar' ? 'فشل في تحديث العقار' : 'Failed to update property'));
      }
    } catch (error) {
      console.error('Error updating property:', error);
      toast.error(language === 'ar' ? 'فشل في تحديث العقار' : 'Failed to update property');
    } finally {
      setLoading(false);
    }
  };

  if (initialLoading) {
    return (
      <div className={`min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 ${language === 'ar' ? 'rtl' : 'ltr'}`}>
        <div className="container mx-auto px-4 py-8">
          <Card className="shadow-xl border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm">
            <CardContent className="flex items-center justify-center py-16">
              <div className="text-center">
                <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
                <p className="text-gray-600 dark:text-gray-400">{t.loading}</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (!property) {
    return (
      <div className={`min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 ${language === 'ar' ? 'rtl' : 'ltr'}`}>
        <div className="container mx-auto px-4 py-8">
          <Card className="shadow-xl border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm">
            <CardContent className="flex items-center justify-center py-16">
              <div className="text-center">
                <p className="text-gray-600 dark:text-gray-400 mb-4">{t.notFound}</p>
                <Button onClick={() => router.push('/dashboard/properties')}>
                  {t.backToProperties}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // Convert property data to form format
  const initialFormData = {
    title: property.title || '',
    titleAr: property.titleAr || '',
    description: property.description || '',
    descriptionAr: property.descriptionAr || '',
    price: property.price?.toString() || '',
    currency: property.currency || 'AED',
    type: property.type || 'APARTMENT',
    status: property.status || 'AVAILABLE',
    bedrooms: property.bedrooms?.toString() || '',
    bathrooms: property.bathrooms?.toString() || '',
    area: property.area?.toString() || '',
    location: property.location || '',
    locationAr: property.locationAr || '',
    address: property.address || '',
    addressAr: property.addressAr || '',
    city: property.city || '',
    cityAr: property.cityAr || '',
    country: property.country || 'UAE',
    countryAr: property.countryAr || 'الإمارات العربية المتحدة',
    images: property.images || [],
    features: property.features || [],
    featuresAr: property.featuresAr || [],
    amenities: property.amenities || [],
    amenitiesAr: property.amenitiesAr || [],
    yearBuilt: property.yearBuilt?.toString() || '',
    parking: property.parking?.toString() || '',
    furnished: property.furnished || false,
    petFriendly: property.petFriendly || false,
    utilities: property.utilities || '',
    utilitiesAr: property.utilitiesAr || '',
    contactInfo: property.contactInfo || '',
    isFeatured: property.isFeatured || false,
    isActive: property.isActive !== undefined ? property.isActive : true,
  };

  return (
    <div className={`min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 ${language === 'ar' ? 'rtl' : 'ltr'}`}>
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400 mb-8" dir={language === 'ar' ? 'rtl' : 'ltr'}>
          <Home className="h-4 w-4" />
          <ChevronRight className="h-4 w-4" />
          <button
            onClick={() => router.push('/dashboard/properties')}
            className="hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
          >
            {t.properties}
          </button>
          <ChevronRight className="h-4 w-4" />
          <span className="text-gray-900 dark:text-gray-100">{t.editProperty}</span>
        </nav>

        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              {t.editProperty}
            </h1>
            <p className="text-gray-600 dark:text-gray-400 max-w-2xl">
              {t.subtitle}
            </p>
          </div>
          <Button
            variant="outline"
            onClick={() => router.push('/dashboard/properties')}
            className="flex items-center gap-2 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
            {t.backToProperties}
          </Button>
        </div>

        {/* Property Form */}
        <PropertyFormSteps
          onSave={handleSave}
          loading={loading}
          initialData={initialFormData}
          isEdit={true}
          propertyId={params.id as string}
        />
      </div>
    </div>
  );
}
