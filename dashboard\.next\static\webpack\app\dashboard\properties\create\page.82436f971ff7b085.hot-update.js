"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/properties/create/page",{

/***/ "(app-pages-browser)/./hooks/useSimpleLanguage.tsx":
/*!*************************************!*\
  !*** ./hooks/useSimpleLanguage.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSimpleLanguage: () => (/* binding */ useSimpleLanguage)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_cookieCleanup__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/cookieCleanup */ \"(app-pages-browser)/./lib/cookieCleanup.ts\");\n/* __next_internal_client_entry_do_not_use__ useSimpleLanguage auto */ var _s = $RefreshSig$();\n\n\n/**\n * Bilingual language hook for Properties system\n * Supports Arabic (default) and English with proper RTL/LTR switching\n */ function useSimpleLanguage() {\n    _s();\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('ar');\n    // Initialize bilingual interface with cleanup\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSimpleLanguage.useEffect\": ()=>{\n            // Clean up cookies first\n            (0,_lib_cookieCleanup__WEBPACK_IMPORTED_MODULE_1__.cleanupCookies)();\n            // Load saved language preference (Arabic as default)\n            const savedLanguage = localStorage.getItem('properties-language');\n            if (savedLanguage === 'en' || savedLanguage === 'ar') {\n                setLanguage(savedLanguage);\n            }\n            (0,_lib_cookieCleanup__WEBPACK_IMPORTED_MODULE_1__.safeLog)('🏠 Bilingual Properties system initialized');\n        }\n    }[\"useSimpleLanguage.useEffect\"], []);\n    // Update document direction and language when language changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSimpleLanguage.useEffect\": ()=>{\n            // Save language preference\n            localStorage.setItem('properties-language', language);\n            // Update document properties\n            document.documentElement.lang = language;\n            document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr';\n            // Update CSS classes\n            document.documentElement.className = language === 'ar' ? 'rtl arabic-interface' : 'ltr english-interface';\n            // Set appropriate fonts\n            if (language === 'ar') {\n                document.body.style.fontFamily = \"'Cairo', 'Noto Sans Arabic', 'Tajawal', 'Amiri', sans-serif\";\n            } else {\n                document.body.style.fontFamily = \"'Inter', 'Segoe UI', 'Roboto', sans-serif\";\n            }\n            (0,_lib_cookieCleanup__WEBPACK_IMPORTED_MODULE_1__.safeLog)(\"\\uD83C\\uDF10 Language switched to: \".concat(language));\n        }\n    }[\"useSimpleLanguage.useEffect\"], [\n        language\n    ]);\n    const changeLanguage = (newLanguage)=>{\n        setLanguage(newLanguage);\n    };\n    return {\n        language,\n        setLanguage: changeLanguage,\n        isRTL: language === 'ar',\n        isArabic: language === 'ar',\n        isEnglish: language === 'en'\n    };\n}\n_s(useSimpleLanguage, \"iBs+XNlLZDTp3CbhC97v1VRCllI=\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/useSimpleLanguage.tsx\n"));

/***/ })

});