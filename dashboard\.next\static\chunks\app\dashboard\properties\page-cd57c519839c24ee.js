(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3711],{13717:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(19946).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},32002:(e,a,t)=>{Promise.resolve().then(t.bind(t,63098))},35695:(e,a,t)=>{"use strict";var r=t(18999);t.o(r,"useParams")&&t.d(a,{useParams:function(){return r.useParams}}),t.o(r,"usePathname")&&t.d(a,{usePathname:function(){return r.usePathname}}),t.o(r,"useRouter")&&t.d(a,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(a,{useSearchParams:function(){return r.useSearchParams}})},45300:(e,a,t)=>{"use strict";function r(){["__next_hmr_refresh_hash__","__clerk_db_jwt_NFfxy5s4","__refresh_NFfxy5s4","__session_NFfxy5s4","__client_uat_NFfxy5s4","__clerk_db_jwt_TYLMw0H7","__refresh_TYLMw0H7","__session_TYLMw0H7","__client_uat_TYLMw0H7","__clerk_db_jwt","__clerk_db_jwt_kCaGdcWF","__client_uat_kCaGdcWF","__client_uat","NEXT_LOCALE","authjs.csrf-token","authjs.callback-url"].forEach(e=>{document.cookie="".concat(e,"=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;"),[window.location.hostname,".".concat(window.location.hostname),"localhost",".localhost"].forEach(a=>{document.cookie="".concat(e,"=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=").concat(a,";")})}),document.cookie="language=ar; path=/; max-age=31536000",console.log("\uD83E\uDDF9 Cookies cleaned up for Arabic Properties system")}function s(e){for(var a=arguments.length,t=Array(a>1?a-1:0),r=1;r<a;r++)t[r-1]=arguments[r]}t.d(a,{Ei:()=>s,ss:()=>r})},47924:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(19946).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},53999:(e,a,t)=>{"use strict";t.d(a,{cn:()=>l});var r=t(52596),s=t(39688);function l(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return(0,s.QP)((0,r.$)(a))}},62525:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(19946).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},63098:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>j});var r=t(95155),s=t(12115),l=t(35695),i=t(84616),n=t(47924),d=t(19946);let o=(0,d.A)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]]),c=(0,d.A)("List",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]]);var u=t(92657),m=t(13717),x=t(62525),h=t(97168),f=t(89852),p=t(88482),g=t(88145),b=t(95784),y=t(86132);function j(){let e=(0,l.useRouter)(),{language:a}=(0,y.Y)(),[t,d]=(0,s.useState)([]),[j,v]=(0,s.useState)(null),[N,w]=(0,s.useState)(!0),[k,A]=(0,s.useState)("grid"),[_,E]=(0,s.useState)(""),[S,C]=(0,s.useState)("الكل"),[L,R]=(0,s.useState)("الكل");(0,s.useEffect)(()=>{P(),T()},[_,S,L]);let P=async()=>{try{w(!0);let e=new URLSearchParams;_&&e.append("search",_),S&&"الكل"!==S&&"ALL"!==S&&e.append("type",S),L&&"الكل"!==L&&"ALL"!==L&&e.append("status",L),e.append("isActive","true");let a=await fetch("/api/v1/properties?".concat(e));if(a.ok){let e=await a.json();e.success&&d(e.data.properties)}else console.log("Backend not available, using mock data"),d([{id:"1",title:"Luxury Villa in Dubai Marina",titleAr:"فيلا فاخرة في دبي مارينا",description:"Beautiful 4-bedroom villa with sea view",descriptionAr:"فيلا جميلة من 4 غرف نوم مع إطلالة على البحر",price:25e5,currency:"AED",type:"VILLA",status:"AVAILABLE",bedrooms:4,bathrooms:3,area:350,location:"Dubai Marina",locationAr:"دبي مارينا",address:"123 Marina Walk",addressAr:"123 ممشى المارينا",city:"Dubai",cityAr:"دبي",images:["/placeholder.jpg"],features:["Swimming Pool","Gym","Parking"],featuresAr:["مسبح","صالة رياضية","موقف سيارات"],amenities:["24/7 Security","Concierge"],amenitiesAr:["أمن 24/7","خدمة الكونسيرج"],isFeatured:!0,isActive:!0,viewCount:125,createdAt:new Date().toISOString()},{id:"2",title:"Modern Apartment in Downtown",titleAr:"شقة حديثة في وسط المدينة",description:"Spacious 2-bedroom apartment",descriptionAr:"شقة واسعة من غرفتي نوم",price:12e5,currency:"AED",type:"APARTMENT",status:"AVAILABLE",bedrooms:2,bathrooms:2,area:120,location:"Downtown Dubai",locationAr:"وسط مدينة دبي",address:"456 Sheikh Zayed Road",addressAr:"456 شارع الشيخ زايد",city:"Dubai",cityAr:"دبي",images:["/placeholder.jpg"],features:["Balcony","Built-in Wardrobes"],featuresAr:["شرفة","خزائن مدمجة"],amenities:["Gym","Pool"],amenitiesAr:["صالة رياضية","مسبح"],isFeatured:!1,isActive:!0,viewCount:89,createdAt:new Date().toISOString()}])}catch(e){console.error("Error fetching properties:",e),d([])}finally{w(!1)}},T=async()=>{try{let e=await fetch("/api/v1/properties/stats");if(e.ok){let a=await e.json();a.success&&v(a.data)}else console.log("Backend not available, using mock stats"),v({total:2,available:2,sold:0,rented:0,featured:1,byType:{APARTMENT:1,VILLA:1,TOWNHOUSE:0,PENTHOUSE:0,STUDIO:0,OFFICE:0,SHOP:0,WAREHOUSE:0,LAND:0,BUILDING:0}})}catch(e){console.error("Error fetching stats:",e),v({total:0,available:0,sold:0,rented:0,featured:0,byType:{APARTMENT:0,VILLA:0,TOWNHOUSE:0,PENTHOUSE:0,STUDIO:0,OFFICE:0,SHOP:0,WAREHOUSE:0,LAND:0,BUILDING:0}})}},M=async e=>{if(confirm("ar"===a?"هل أنت متأكد من حذف هذا العقار؟":"Are you sure you want to delete this property?"))try{(await fetch("/api/v1/properties/".concat(e),{method:"DELETE"})).ok&&(P(),T())}catch(e){console.error("Error deleting property:",e)}},F=(e,t)=>new Intl.NumberFormat("ar"===a?"ar-AE":"en-US",{style:"currency",currency:t,minimumFractionDigits:0}).format(e),D=e=>{switch(e){case"AVAILABLE":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";case"SOLD":return"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";case"RENTED":return"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";case"RESERVED":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"}},I=e=>"ar"===a&&e.titleAr?e.titleAr:e.title,O=e=>"ar"===a&&e.locationAr?e.locationAr:e.location;return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"ar"===a?"العقارات":"Properties"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"ar"===a?"إدارة العقارات والممتلكات":"Manage properties and real estate"})]}),(0,r.jsxs)(h.$,{onClick:()=>e.push("/dashboard/properties/create"),className:"bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2 shadow-lg hover:shadow-xl transition-all duration-200",children:[(0,r.jsx)(i.A,{className:"h-4 w-4"}),"ar"===a?"إضافة عقار":"Add Property"]})]}),j&&(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4",children:[(0,r.jsxs)(p.Zp,{children:[(0,r.jsx)(p.aR,{className:"pb-2",children:(0,r.jsx)(p.ZB,{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"ar"===a?"إجمالي العقارات":"Total Properties"})}),(0,r.jsx)(p.Wu,{children:(0,r.jsx)("div",{className:"text-2xl font-bold",children:j.total})})]}),(0,r.jsxs)(p.Zp,{children:[(0,r.jsx)(p.aR,{className:"pb-2",children:(0,r.jsx)(p.ZB,{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"ar"===a?"متاح":"Available"})}),(0,r.jsx)(p.Wu,{children:(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600",children:j.available})})]}),(0,r.jsxs)(p.Zp,{children:[(0,r.jsx)(p.aR,{className:"pb-2",children:(0,r.jsx)(p.ZB,{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"ar"===a?"مباع":"Sold"})}),(0,r.jsx)(p.Wu,{children:(0,r.jsx)("div",{className:"text-2xl font-bold text-red-600",children:j.sold})})]}),(0,r.jsxs)(p.Zp,{children:[(0,r.jsx)(p.aR,{className:"pb-2",children:(0,r.jsx)(p.ZB,{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"ar"===a?"مؤجر":"Rented"})}),(0,r.jsx)(p.Wu,{children:(0,r.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:j.rented})})]}),(0,r.jsxs)(p.Zp,{children:[(0,r.jsx)(p.aR,{className:"pb-2",children:(0,r.jsx)(p.ZB,{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"ar"===a?"مميز":"Featured"})}),(0,r.jsx)(p.Wu,{children:(0,r.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:j.featured})})]})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,r.jsxs)("div",{className:"relative flex-1",children:[(0,r.jsx)(n.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,r.jsx)(f.p,{placeholder:"ar"===a?"البحث في العقارات...":"Search properties...",value:_,onChange:e=>E(e.target.value),className:"pl-10"})]}),(0,r.jsxs)(b.l6,{value:S,onValueChange:C,children:[(0,r.jsx)(b.bq,{className:"w-full sm:w-48",children:(0,r.jsx)(b.yv,{placeholder:"ar"===a?"نوع العقار":"Property Type"})}),(0,r.jsxs)(b.gC,{children:[(0,r.jsx)(b.eb,{value:"الكل",children:"جميع الأنواع"}),(0,r.jsx)(b.eb,{value:"APARTMENT",children:"شقة"}),(0,r.jsx)(b.eb,{value:"VILLA",children:"فيلا"}),(0,r.jsx)(b.eb,{value:"TOWNHOUSE",children:"تاون هاوس"}),(0,r.jsx)(b.eb,{value:"OFFICE",children:"مكتب"})]})]}),(0,r.jsxs)(b.l6,{value:L,onValueChange:R,children:[(0,r.jsx)(b.bq,{className:"w-full sm:w-48",children:(0,r.jsx)(b.yv,{placeholder:"ar"===a?"الحالة":"Status"})}),(0,r.jsxs)(b.gC,{children:[(0,r.jsx)(b.eb,{value:"الكل",children:"جميع الحالات"}),(0,r.jsx)(b.eb,{value:"AVAILABLE",children:"متاح"}),(0,r.jsx)(b.eb,{value:"SOLD",children:"مباع"}),(0,r.jsx)(b.eb,{value:"RENTED",children:"مؤجر"})]})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(h.$,{variant:"grid"===k?"default":"outline",size:"sm",onClick:()=>A("grid"),children:(0,r.jsx)(o,{className:"h-4 w-4"})}),(0,r.jsx)(h.$,{variant:"list"===k?"default":"outline",size:"sm",onClick:()=>A("list"),children:(0,r.jsx)(c,{className:"h-4 w-4"})})]})]}),N?(0,r.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 dark:border-white"})}):(0,r.jsx)("div",{className:"grid"===k?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6":"space-y-4",children:t.map(t=>(0,r.jsx)(p.Zp,{className:"overflow-hidden hover:shadow-lg transition-shadow",children:"grid"===k?(0,r.jsxs)(r.Fragment,{children:[t.images.length>0&&(0,r.jsxs)("div",{className:"relative h-48 bg-gray-200 dark:bg-gray-700",children:[(0,r.jsx)("img",{src:t.images[0],alt:I(t),className:"w-full h-full object-cover"}),t.isFeatured&&(0,r.jsx)(g.E,{className:"absolute top-2 left-2 bg-purple-600",children:"ar"===a?"مميز":"Featured"})]}),(0,r.jsxs)(p.Wu,{className:"p-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,r.jsx)("h3",{className:"font-semibold text-lg truncate",children:I(t)}),(0,r.jsx)(g.E,{className:D(t.status),children:t.status})]}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-sm mb-2",children:O(t)}),(0,r.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,r.jsx)("span",{className:"text-xl font-bold text-blue-600",children:F(t.price,t.currency)}),(0,r.jsxs)("div",{className:"flex gap-2 text-sm text-gray-500",children:[t.bedrooms&&(0,r.jsxs)("span",{children:[t.bedrooms," ","ar"===a?"غرف":"bed"]}),t.bathrooms&&(0,r.jsxs)("span",{children:[t.bathrooms," ","ar"===a?"حمام":"bath"]}),t.area&&(0,r.jsxs)("span",{children:[t.area,"m\xb2"]})]})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{className:"flex gap-1",children:[(0,r.jsx)(h.$,{size:"sm",variant:"outline",onClick:()=>setSelectedProperty(t),children:(0,r.jsx)(u.A,{className:"h-4 w-4"})}),(0,r.jsx)(h.$,{size:"sm",variant:"outline",onClick:()=>e.push("/dashboard/properties/edit/".concat(t.id)),className:"hover:bg-blue-50 hover:border-blue-300 transition-colors",children:(0,r.jsx)(m.A,{className:"h-4 w-4"})}),(0,r.jsx)(h.$,{size:"sm",variant:"outline",onClick:()=>M(t.id),children:(0,r.jsx)(x.A,{className:"h-4 w-4"})})]}),(0,r.jsxs)("span",{className:"text-xs text-gray-500",children:["ar"===a?"المشاهدات:":"Views:"," ",t.viewCount]})]})]})]}):(0,r.jsx)(p.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex gap-4",children:[t.images.length>0&&(0,r.jsx)("div",{className:"relative w-24 h-24 bg-gray-200 dark:bg-gray-700 rounded",children:(0,r.jsx)("img",{src:t.images[0],alt:I(t),className:"w-full h-full object-cover rounded"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,r.jsx)("h3",{className:"font-semibold text-lg",children:I(t)}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(g.E,{className:D(t.status),children:t.status}),t.isFeatured&&(0,r.jsx)(g.E,{className:"bg-purple-600",children:"ar"===a?"مميز":"Featured"})]})]}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-sm mb-2",children:O(t)}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-xl font-bold text-blue-600",children:F(t.price,t.currency)}),(0,r.jsxs)("div",{className:"flex gap-4 text-sm text-gray-500",children:[t.bedrooms&&(0,r.jsxs)("span",{children:[t.bedrooms," ","ar"===a?"غرف":"bed"]}),t.bathrooms&&(0,r.jsxs)("span",{children:[t.bathrooms," ","ar"===a?"حمام":"bath"]}),t.area&&(0,r.jsxs)("span",{children:[t.area,"m\xb2"]}),(0,r.jsxs)("span",{children:["ar"===a?"المشاهدات:":"Views:"," ",t.viewCount]})]}),(0,r.jsxs)("div",{className:"flex gap-1",children:[(0,r.jsx)(h.$,{size:"sm",variant:"outline",onClick:()=>setSelectedProperty(t),children:(0,r.jsx)(u.A,{className:"h-4 w-4"})}),(0,r.jsx)(h.$,{size:"sm",variant:"outline",onClick:()=>e.push("/dashboard/properties/edit/".concat(t.id)),className:"hover:bg-blue-50 hover:border-blue-300 transition-colors",children:(0,r.jsx)(m.A,{className:"h-4 w-4"})}),(0,r.jsx)(h.$,{size:"sm",variant:"outline",onClick:()=>M(t.id),children:(0,r.jsx)(x.A,{className:"h-4 w-4"})})]})]})]})]})})},t.id))})]})}},84616:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(19946).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},86132:(e,a,t)=>{"use strict";t.d(a,{Y:()=>l});var r=t(12115),s=t(45300);function l(){let[e,a]=(0,r.useState)("ar");return(0,r.useEffect)(()=>{(0,s.ss)();let e=localStorage.getItem("properties-language");("en"===e||"ar"===e)&&a(e),(0,s.Ei)("\uD83C\uDFE0 Bilingual Properties system initialized")},[]),(0,r.useEffect)(()=>{localStorage.setItem("properties-language",e),document.documentElement.lang=e,document.documentElement.dir="ar"===e?"rtl":"ltr",document.documentElement.className="ar"===e?"rtl arabic-interface":"ltr english-interface","ar"===e?document.body.style.fontFamily="'Cairo', 'Noto Sans Arabic', 'Tajawal', 'Amiri', sans-serif":document.body.style.fontFamily="'Inter', 'Segoe UI', 'Roboto', sans-serif",(0,s.Ei)("\uD83C\uDF10 Language switched to: ".concat(e))},[e]),{language:e,setLanguage:e=>{a(e)},isRTL:"ar"===e,isArabic:"ar"===e,isEnglish:"en"===e}}},88145:(e,a,t)=>{"use strict";t.d(a,{E:()=>n});var r=t(95155);t(12115);var s=t(74466),l=t(53999);let i=(0,s.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function n(e){let{className:a,variant:t,...s}=e;return(0,r.jsx)("div",{className:(0,l.cn)(i({variant:t}),a),...s})}},88482:(e,a,t)=>{"use strict";t.d(a,{BT:()=>o,Wu:()=>c,ZB:()=>d,Zp:()=>i,aR:()=>n,wL:()=>u});var r=t(95155),s=t(12115),l=t(53999);let i=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...s})});i.displayName="Card";let n=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",t),...s})});n.displayName="CardHeader";let d=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",t),...s})});d.displayName="CardTitle";let o=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,className:(0,l.cn)("text-sm text-muted-foreground",t),...s})});o.displayName="CardDescription";let c=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,className:(0,l.cn)("p-6 pt-0",t),...s})});c.displayName="CardContent";let u=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,className:(0,l.cn)("flex items-center p-6 pt-0",t),...s})});u.displayName="CardFooter"},89852:(e,a,t)=>{"use strict";t.d(a,{p:()=>i});var r=t(95155),s=t(12115),l=t(53999);let i=s.forwardRef((e,a)=>{let{className:t,type:s,...i}=e;return(0,r.jsx)("input",{type:s,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:a,...i})});i.displayName="Input"},92657:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(19946).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},95784:(e,a,t)=>{"use strict";t.d(a,{bq:()=>m,eb:()=>p,gC:()=>f,l6:()=>c,yv:()=>u});var r=t(95155),s=t(12115),l=t(31992),i=t(66474),n=t(47863),d=t(5196),o=t(53999);let c=l.bL;l.YJ;let u=l.WT,m=s.forwardRef((e,a)=>{let{className:t,children:s,...n}=e;return(0,r.jsxs)(l.l9,{ref:a,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),...n,children:[s,(0,r.jsx)(l.In,{asChild:!0,children:(0,r.jsx)(i.A,{className:"h-4 w-4 opacity-50"})})]})});m.displayName=l.l9.displayName;let x=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(l.PP,{ref:a,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,r.jsx)(n.A,{className:"h-4 w-4"})})});x.displayName=l.PP.displayName;let h=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(l.wn,{ref:a,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,r.jsx)(i.A,{className:"h-4 w-4"})})});h.displayName=l.wn.displayName;let f=s.forwardRef((e,a)=>{let{className:t,children:s,position:i="popper",...n}=e;return(0,r.jsx)(l.ZL,{children:(0,r.jsxs)(l.UC,{ref:a,className:(0,o.cn)("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:i,...n,children:[(0,r.jsx)(x,{}),(0,r.jsx)(l.LM,{className:(0,o.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,r.jsx)(h,{})]})})});f.displayName=l.UC.displayName,s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(l.JU,{ref:a,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",t),...s})}).displayName=l.JU.displayName;let p=s.forwardRef((e,a)=>{let{className:t,children:s,...i}=e;return(0,r.jsxs)(l.q7,{ref:a,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...i,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(l.VF,{children:(0,r.jsx)(d.A,{className:"h-4 w-4"})})}),(0,r.jsx)(l.p4,{children:s})]})});p.displayName=l.q7.displayName,s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(l.wv,{ref:a,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",t),...s})}).displayName=l.wv.displayName},97168:(e,a,t)=>{"use strict";t.d(a,{$:()=>o,r:()=>d});var r=t(95155),s=t(12115),l=t(99708),i=t(74466),n=t(53999);let d=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=s.forwardRef((e,a)=>{let{className:t,variant:s,size:i,asChild:o=!1,...c}=e,u=o?l.DX:"button";return(0,r.jsx)(u,{className:(0,n.cn)(d({variant:s,size:i,className:t})),ref:a,...c})});o.displayName="Button"}},e=>{var a=a=>e(e.s=a);e.O(0,[4277,6071,9509,9855,8441,1684,7358],()=>a(32002)),_N_E=e.O()}]);