{"version": 3, "file": "managedRuntime.js", "names": ["Effectable", "pipeArguments", "hasProperty", "<PERSON><PERSON>", "core", "fiberRuntime", "internalLayer", "circular", "internalRuntime", "isManagedRuntime", "u", "TypeId", "provide", "managed", "effect", "flatMap", "runtimeEffect", "rt", "withFiberRuntime", "fiber", "setFiberRefs", "fiberRefs", "currentRuntimeFlags", "runtimeFlags", "provideContext", "context", "ManagedRuntimeProto", "CommitPrototype", "pipe", "arguments", "commit", "make", "layer", "memoMap", "unsafeMakeMemoMap", "scope", "unsafeRunSyncEffect", "scopeMake", "buildFiber", "unsafeForkEffect", "tap", "extend", "toRuntimeWithMemoMap", "self", "cachedRuntime", "scheduler", "currentScheduler", "flatten", "await", "Object", "assign", "create", "undefined", "runtime", "unsafeRunPromiseEffect", "Promise", "resolve", "dispose", "disposeEffect", "suspend", "die", "close", "exitVoid", "runFork", "options", "unsafeFork", "runSyncExit", "unsafeRunSyncExitEffect", "unsafeRunSyncExit", "runSync", "unsafeRunSync", "runPromiseExit", "unsafeRunPromiseExitEffect", "unsafeRunPromiseExit", "<PERSON><PERSON><PERSON><PERSON>", "unsafeRunCallback", "defaultRuntime", "runPromise", "unsafeRunPromise"], "sources": ["../../../src/internal/managedRuntime.ts"], "sourcesContent": [null], "mappings": "AACA,OAAO,KAAKA,UAAU,MAAM,kBAAkB;AAK9C,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,SAASC,WAAW,QAAQ,iBAAiB;AAE7C,OAAO,KAAKC,KAAK,MAAM,aAAa;AAEpC,OAAO,KAAKC,IAAI,MAAM,WAAW;AACjC,OAAO,KAAKC,YAAY,MAAM,mBAAmB;AACjD,OAAO,KAAKC,aAAa,MAAM,YAAY;AAC3C,OAAO,KAAKC,QAAQ,MAAM,8BAA8B;AACxD,OAAO,KAAKC,eAAe,MAAM,cAAc;AAO/C;AACA,OAAO,MAAMC,gBAAgB,GAAIC,CAAU,IAA8CR,WAAW,CAACQ,CAAC,EAAEH,QAAQ,CAACI,MAAM,CAAC;AAExH,SAASC,OAAOA,CACdC,OAAkC,EAClCC,MAA8B;EAE9B,OAAOV,IAAI,CAACW,OAAO,CACjBF,OAAO,CAACG,aAAa,EACpBC,EAAE,IACDb,IAAI,CAACc,gBAAgB,CAAEC,KAAK,IAAI;IAC9BA,KAAK,CAACC,YAAY,CAACH,EAAE,CAACI,SAAS,CAAC;IAChCF,KAAK,CAACG,mBAAmB,GAAGL,EAAE,CAACM,YAAY;IAC3C,OAAOnB,IAAI,CAACoB,cAAc,CAACV,MAAM,EAAEG,EAAE,CAACQ,OAAO,CAAC;EAChD,CAAC,CAAC,CACL;AACH;AAEA,MAAMC,mBAAmB,GAAG;EAC1B,GAAG1B,UAAU,CAAC2B,eAAe;EAC7B,CAACpB,QAAQ,CAACI,MAAM,GAAGJ,QAAQ,CAACI,MAAM;EAClCiB,IAAIA,CAAA;IACF,OAAO3B,aAAa,CAAC,IAAI,EAAE4B,SAAS,CAAC;EACvC,CAAC;EACDC,MAAMA,CAAA;IACJ,OAAO,IAAI,CAACd,aAAa;EAC3B;CACD;AAED;AACA,OAAO,MAAMe,IAAI,GAAGA,CAClBC,KAAgC,EAChCC,OAAuB,KACI;EAC3BA,OAAO,GAAGA,OAAO,IAAI3B,aAAa,CAAC4B,iBAAiB,EAAE;EACtD,MAAMC,KAAK,GAAG3B,eAAe,CAAC4B,mBAAmB,CAAC/B,YAAY,CAACgC,SAAS,EAAE,CAAC;EAC3E,IAAIC,UAAkE;EACtE,MAAMtB,aAAa,GAAGZ,IAAI,CAACc,gBAAgB,CAA0BC,KAAK,IAAI;IAC5E,IAAI,CAACmB,UAAU,EAAE;MACfA,UAAU,GAAG9B,eAAe,CAAC+B,gBAAgB,CAC3CnC,IAAI,CAACoC,GAAG,CACNrC,KAAK,CAACsC,MAAM,CACVnC,aAAa,CAACoC,oBAAoB,CAACV,KAAK,EAAEC,OAAO,CAAC,EAClDE,KAAK,CACN,EACAlB,EAAE,IAAI;QACL0B,IAAI,CAACC,aAAa,GAAG3B,EAAE;MACzB,CAAC,CACF,EACD;QAAEkB,KAAK;QAAEU,SAAS,EAAE1B,KAAK,CAAC2B;MAAgB,CAAE,CAC7C;IACH;IACA,OAAO1C,IAAI,CAAC2C,OAAO,CAACT,UAAU,CAACU,KAAK,CAAC;EACvC,CAAC,CAAC;EACF,MAAML,IAAI,GAA8BM,MAAM,CAACC,MAAM,CAACD,MAAM,CAACE,MAAM,CAACzB,mBAAmB,CAAC,EAAE;IACxFO,OAAO;IACPE,KAAK;IACLnB,aAAa;IACb4B,aAAa,EAAEQ,SAAS;IACxBC,OAAOA,CAAA;MACL,OAAOV,IAAI,CAACC,aAAa,KAAKQ,SAAS,GACrC5C,eAAe,CAAC8C,sBAAsB,CAACX,IAAI,CAAC3B,aAAa,CAAC,GAC1DuC,OAAO,CAACC,OAAO,CAACb,IAAI,CAACC,aAAa,CAAC;IACvC,CAAC;IACDa,OAAOA,CAAA;MACL,OAAOjD,eAAe,CAAC8C,sBAAsB,CAACX,IAAI,CAACe,aAAa,CAAC;IACnE,CAAC;IACDA,aAAa,EAAEtD,IAAI,CAACuD,OAAO,CAAC,MAAK;MAC/B;MAAEhB,IAA2C,CAAC3B,aAAa,GAAGZ,IAAI,CAACwD,GAAG,CAAC,yBAAyB,CAAC;MACjGjB,IAAI,CAACC,aAAa,GAAGQ,SAAS;MAC9B,OAAOjD,KAAK,CAAC0D,KAAK,CAAClB,IAAI,CAACR,KAAK,EAAE/B,IAAI,CAAC0D,QAAQ,CAAC;IAC/C,CAAC,CAAC;IACFC,OAAOA,CAAOjD,MAA8B,EAAEkD,OAAgC;MAC5E,OAAOrB,IAAI,CAACC,aAAa,KAAKQ,SAAS,GACrC5C,eAAe,CAAC+B,gBAAgB,CAAC3B,OAAO,CAAC+B,IAAI,EAAE7B,MAAM,CAAC,EAAEkD,OAAO,CAAC,GAChExD,eAAe,CAACyD,UAAU,CAACtB,IAAI,CAACC,aAAa,CAAC,CAAC9B,MAAM,EAAEkD,OAAO,CAAC;IACnE,CAAC;IACDE,WAAWA,CAAOpD,MAA8B;MAC9C,OAAO6B,IAAI,CAACC,aAAa,KAAKQ,SAAS,GACrC5C,eAAe,CAAC2D,uBAAuB,CAACvD,OAAO,CAAC+B,IAAI,EAAE7B,MAAM,CAAC,CAAC,GAC9DN,eAAe,CAAC4D,iBAAiB,CAACzB,IAAI,CAACC,aAAa,CAAC,CAAC9B,MAAM,CAAC;IACjE,CAAC;IACDuD,OAAOA,CAAOvD,MAA8B;MAC1C,OAAO6B,IAAI,CAACC,aAAa,KAAKQ,SAAS,GACrC5C,eAAe,CAAC4B,mBAAmB,CAACxB,OAAO,CAAC+B,IAAI,EAAE7B,MAAM,CAAC,CAAC,GAC1DN,eAAe,CAAC8D,aAAa,CAAC3B,IAAI,CAACC,aAAa,CAAC,CAAC9B,MAAM,CAAC;IAC7D,CAAC;IACDyD,cAAcA,CAAOzD,MAA8B,EAAEkD,OAEpD;MACC,OAAOrB,IAAI,CAACC,aAAa,KAAKQ,SAAS,GACrC5C,eAAe,CAACgE,0BAA0B,CAAC5D,OAAO,CAAC+B,IAAI,EAAE7B,MAAM,CAAC,EAAEkD,OAAO,CAAC,GAC1ExD,eAAe,CAACiE,oBAAoB,CAAC9B,IAAI,CAACC,aAAa,CAAC,CAAC9B,MAAM,EAAEkD,OAAO,CAAC;IAC7E,CAAC;IACDU,WAAWA,CACT5D,MAA8B,EAC9BkD,OAA2D;MAE3D,OAAOrB,IAAI,CAACC,aAAa,KAAKQ,SAAS,GACrC5C,eAAe,CAACmE,iBAAiB,CAACnE,eAAe,CAACoE,cAAc,CAAC,CAAChE,OAAO,CAAC+B,IAAI,EAAE7B,MAAM,CAAC,EAAEkD,OAAO,CAAC,GACjGxD,eAAe,CAACmE,iBAAiB,CAAChC,IAAI,CAACC,aAAa,CAAC,CAAC9B,MAAM,EAAEkD,OAAO,CAAC;IAC1E,CAAC;IACDa,UAAUA,CAAO/D,MAA8B,EAAEkD,OAEhD;MACC,OAAOrB,IAAI,CAACC,aAAa,KAAKQ,SAAS,GACrC5C,eAAe,CAAC8C,sBAAsB,CAAC1C,OAAO,CAAC+B,IAAI,EAAE7B,MAAM,CAAC,EAAEkD,OAAO,CAAC,GACtExD,eAAe,CAACsE,gBAAgB,CAACnC,IAAI,CAACC,aAAa,CAAC,CAAC9B,MAAM,EAAEkD,OAAO,CAAC;IACzE;GACD,CAAC;EACF,OAAOrB,IAAI;AACb,CAAC", "ignoreList": []}