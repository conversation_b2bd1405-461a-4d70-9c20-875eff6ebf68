const video = {
    "video/3gpp": {
        source: "iana",
        extensions: [
            "3gp",
            "3gpp"
        ]
    },
    "video/3gpp2": {
        source: "iana",
        extensions: [
            "3g2"
        ]
    },
    "video/h261": {
        source: "iana",
        extensions: [
            "h261"
        ]
    },
    "video/h263": {
        source: "iana",
        extensions: [
            "h263"
        ]
    },
    "video/h264": {
        source: "iana",
        extensions: [
            "h264"
        ]
    },
    "video/iso.segment": {
        source: "iana",
        extensions: [
            "m4s"
        ]
    },
    "video/jpeg": {
        source: "iana",
        extensions: [
            "jpgv"
        ]
    },
    "video/jpm": {
        source: "apache",
        extensions: [
            "jpm",
            "jpgm"
        ]
    },
    "video/mj2": {
        source: "iana",
        extensions: [
            "mj2",
            "mjp2"
        ]
    },
    "video/mp2t": {
        source: "iana",
        extensions: [
            "ts"
        ]
    },
    "video/mp4": {
        source: "iana",
        extensions: [
            "mp4",
            "mp4v",
            "mpg4"
        ]
    },
    "video/mpeg": {
        source: "iana",
        extensions: [
            "mpeg",
            "mpg",
            "mpe",
            "m1v",
            "m2v"
        ]
    },
    "video/ogg": {
        source: "iana",
        extensions: [
            "ogv"
        ]
    },
    "video/quicktime": {
        source: "iana",
        extensions: [
            "qt",
            "mov"
        ]
    },
    "video/vnd.dece.hd": {
        source: "iana",
        extensions: [
            "uvh",
            "uvvh"
        ]
    },
    "video/vnd.dece.mobile": {
        source: "iana",
        extensions: [
            "uvm",
            "uvvm"
        ]
    },
    "video/vnd.dece.pd": {
        source: "iana",
        extensions: [
            "uvp",
            "uvvp"
        ]
    },
    "video/vnd.dece.sd": {
        source: "iana",
        extensions: [
            "uvs",
            "uvvs"
        ]
    },
    "video/vnd.dece.video": {
        source: "iana",
        extensions: [
            "uvv",
            "uvvv"
        ]
    },
    "video/vnd.dvb.file": {
        source: "iana",
        extensions: [
            "dvb"
        ]
    },
    "video/vnd.fvt": {
        source: "iana",
        extensions: [
            "fvt"
        ]
    },
    "video/vnd.mpegurl": {
        source: "iana",
        extensions: [
            "mxu",
            "m4u"
        ]
    },
    "video/vnd.ms-playready.media.pyv": {
        source: "iana",
        extensions: [
            "pyv"
        ]
    },
    "video/vnd.uvvu.mp4": {
        source: "iana",
        extensions: [
            "uvu",
            "uvvu"
        ]
    },
    "video/vnd.vivo": {
        source: "iana",
        extensions: [
            "viv"
        ]
    },
    "video/webm": {
        source: "apache",
        extensions: [
            "webm"
        ]
    },
    "video/x-f4v": {
        source: "apache",
        extensions: [
            "f4v"
        ]
    },
    "video/x-fli": {
        source: "apache",
        extensions: [
            "fli"
        ]
    },
    "video/x-flv": {
        source: "apache",
        extensions: [
            "flv"
        ]
    },
    "video/x-m4v": {
        source: "apache",
        extensions: [
            "m4v"
        ]
    },
    "video/x-matroska": {
        source: "apache",
        extensions: [
            "mkv",
            "mk3d",
            "mks"
        ]
    },
    "video/x-mng": {
        source: "apache",
        extensions: [
            "mng"
        ]
    },
    "video/x-ms-asf": {
        source: "apache",
        extensions: [
            "asf",
            "asx"
        ]
    },
    "video/x-ms-vob": {
        source: "apache",
        extensions: [
            "vob"
        ]
    },
    "video/x-ms-wm": {
        source: "apache",
        extensions: [
            "wm"
        ]
    },
    "video/x-ms-wmv": {
        source: "apache",
        extensions: [
            "wmv"
        ]
    },
    "video/x-ms-wmx": {
        source: "apache",
        extensions: [
            "wmx"
        ]
    },
    "video/x-ms-wvx": {
        source: "apache",
        extensions: [
            "wvx"
        ]
    },
    "video/x-msvideo": {
        source: "apache",
        extensions: [
            "avi"
        ]
    },
    "video/x-sgi-movie": {
        source: "apache",
        extensions: [
            "movie"
        ]
    },
    "video/x-smv": {
        source: "apache",
        extensions: [
            "smv"
        ]
    }
};

exports.video = video;
