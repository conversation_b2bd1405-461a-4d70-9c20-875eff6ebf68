import * as _effect_platform_HttpClientError from '@effect/platform/HttpClientError';
import { HttpClient } from '@effect/platform';
import * as Effect from 'effect/Effect';
import { UploadThingError } from '@uploadthing/shared';
import { Blob as Blob$1 } from 'buffer';

type BlobEsque = Blob$1 | Blob;
type FileEsque = BlobEsque & {
    name: string;
    lastModified?: number;
    customId?: string | null | undefined;
};

declare const uploadWithoutProgress: (file: FileEsque, presigned: {
    key: string;
    url: string;
}) => Effect.Effect<{
    url: string;
    appUrl: string;
    ufsUrl: string;
    fileHash: string;
    serverData: unknown;
}, _effect_platform_HttpClientError.ResponseError | UploadThingError<{
    message: string;
}>, HttpClient.HttpClient>;

export { uploadWithoutProgress };
