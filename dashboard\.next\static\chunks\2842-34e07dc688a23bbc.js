"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2842],{4229:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},4884:(e,t,n)=>{n.d(t,{bL:()=>_,zi:()=>j});var r=n(12115),a=n(85185),i=n(6101),s=n(46081),o=n(5845),l=n(45503),u=n(11275),c=n(63655),d=n(95155),p="Switch",[f,h]=(0,s.A)(p),[g,v]=f(p),m=r.forwardRef((e,t)=>{let{__scopeSwitch:n,name:s,checked:l,defaultChecked:u,required:p,disabled:f,value:h="on",onCheckedChange:v,form:m,...x}=e,[b,_]=r.useState(null),j=(0,i.s)(t,e=>_(e)),k=r.useRef(!1),E=!b||m||!!b.closest("form"),[S=!1,O]=(0,o.i)({prop:l,defaultProp:u,onChange:v});return(0,d.jsxs)(g,{scope:n,checked:S,disabled:f,children:[(0,d.jsx)(c.sG.button,{type:"button",role:"switch","aria-checked":S,"aria-required":p,"data-state":w(S),"data-disabled":f?"":void 0,disabled:f,value:h,...x,ref:j,onClick:(0,a.m)(e.onClick,e=>{O(e=>!e),E&&(k.current=e.isPropagationStopped(),k.current||e.stopPropagation())})}),E&&(0,d.jsx)(y,{control:b,bubbles:!k.current,name:s,value:h,checked:S,required:p,disabled:f,form:m,style:{transform:"translateX(-100%)"}})]})});m.displayName=p;var x="SwitchThumb",b=r.forwardRef((e,t)=>{let{__scopeSwitch:n,...r}=e,a=v(x,n);return(0,d.jsx)(c.sG.span,{"data-state":w(a.checked),"data-disabled":a.disabled?"":void 0,...r,ref:t})});b.displayName=x;var y=e=>{let{control:t,checked:n,bubbles:a=!0,...i}=e,s=r.useRef(null),o=(0,l.Z)(n),c=(0,u.X)(t);return r.useEffect(()=>{let e=s.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(o!==n&&t){let r=new Event("click",{bubbles:a});t.call(e,n),e.dispatchEvent(r)}},[o,n,a]),(0,d.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:n,...i,tabIndex:-1,ref:s,style:{...e.style,...c,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function w(e){return e?"checked":"unchecked"}var _=m,j=b},29869:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},35169:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},35695:(e,t,n)=>{var r=n(18999);n.o(r,"useParams")&&n.d(t,{useParams:function(){return r.useParams}}),n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}}),n.o(r,"useSearchParams")&&n.d(t,{useSearchParams:function(){return r.useSearchParams}})},58471:(e,t,n)=>{let r,a;n.d(t,{Jt:()=>rb,Wi:()=>ry});var i,s=n(95155);let o=function(e,t){if("function"==typeof e)return function(){return e(arguments)?t.apply(this,arguments):e=>t(e,...arguments)};switch(e){case 0:case 1:throw RangeError("Invalid arity ".concat(e));case 2:return function(e,n){return arguments.length>=2?t(e,n):function(n){return t(n,e)}};case 3:return function(e,n,r){return arguments.length>=3?t(e,n,r):function(r){return t(r,e,n)}};case 4:return function(e,n,r,a){return arguments.length>=4?t(e,n,r,a):function(a){return t(a,e,n,r)}};case 5:return function(e,n,r,a,i){return arguments.length>=5?t(e,n,r,a,i):function(i){return t(i,e,n,r,a)}};default:return function(){if(arguments.length>=e)return t.apply(this,arguments);let n=arguments;return function(e){return t(e,...n)}}}},l=e=>e,u=(a=void 0,()=>a),c="effect/GlobalValue/globalStoreId/".concat("3.14.21"),d=(e,t)=>{if(!r){var n,a;null!==(a=(n=globalThis)[c])&&void 0!==a||(n[c]=new Map),r=globalThis[c]}return r.has(e)||r.set(e,t()),r.get(e)},p=e=>"string"==typeof e,f=e=>"number"==typeof e,h=e=>"function"==typeof e,g=e=>"object"==typeof e&&null!==e,v=e=>g(e)||h(e),m=o(2,(e,t)=>v(e)&&t in e),x=o(2,(e,t)=>m(e,"_tag")&&e._tag===t),b=e=>g(e)&&!Array.isArray(e);function y(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}let w=e=>"BUG: ".concat(e," - please report an issue at https://github.com/Effect-TS/effect/issues");Symbol.iterator;class _{next(e){return this.called?{value:e,done:!0}:(this.called=!0,{value:this.self,done:!1})}return(e){return{value:e,done:!0}}throw(e){throw e}[Symbol.iterator](){return new _(this.self)}constructor(e){this.called=!1,this.self=e}}let j=Symbol.for("effect/Utils/YieldWrap");var k=new WeakMap;class E{[j](){var e;return(e=y(this,k,"get")).get?e.get.call(this):e.value}constructor(e){!function(e,t,n){(function(e,t){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object")})(e,t),t.set(e,n)}(this,k,{writable:!0,value:void 0}),function(e,t,n){var r=y(e,t,"set");!function(e,t,n){if(t.set)t.set.call(e,n);else{if(!t.writable)throw TypeError("attempted to set read only private field");t.value=n}}(e,r,n)}(this,k,e)}}let S=d("effect/Utils/isStructuralRegion",()=>({enabled:!1,tester:void 0})),O={effect_internal_function:e=>e()};(null===(i=O.effect_internal_function(()=>Error().stack))||void 0===i?void 0:i.includes("effect_internal_function"))===!0&&O.effect_internal_function;let C=d(Symbol.for("effect/Hash/randomHashCache"),()=>new WeakMap),R=Symbol.for("effect/Hash"),A=e=>{if(!0===S.enabled)return 0;switch(typeof e){case"number":return M(e);case"bigint":return P(e.toString(10));case"boolean":case"symbol":return P(String(e));case"string":return P(e);case"undefined":return P("undefined");case"function":case"object":if(null===e)return P("null");if(e instanceof Date)return A(e.toISOString());if(e instanceof URL)return A(e.href);else if(I(e))return e[R]();else return T(e);default:throw Error("BUG: unhandled typeof ".concat(typeof e," - please report an issue at https://github.com/Effect-TS/effect/issues"))}},T=e=>(C.has(e)||C.set(e,M(Math.floor(Math.random()*Number.MAX_SAFE_INTEGER))),C.get(e)),N=e=>t=>53*t^e,U=e=>0xbfffffff&e|e>>>1&0x40000000,I=e=>m(e,R),M=e=>{if(e!=e||e===1/0)return 0;let t=0|e;for(t!==e&&(t^=0xffffffff*e);e>0xffffffff;)t^=e/=0xffffffff;return U(t)},P=e=>{let t=5381,n=e.length;for(;n;)t=33*t^e.charCodeAt(--n);return U(t)},L=(e,t)=>{let n=12289;for(let r=0;r<t.length;r++)n^=function(e,t,n,r,a,i,s,o,l){switch(arguments.length){case 1:return e;case 2:return t(e);case 3:return n(t(e));case 4:return r(n(t(e)));case 5:return a(r(n(t(e))));case 6:return i(a(r(n(t(e)))));case 7:return s(i(a(r(n(t(e))))));case 8:return o(s(i(a(r(n(t(e)))))));case 9:return l(o(s(i(a(r(n(t(e))))))));default:{let e=arguments[0];for(let t=1;t<arguments.length;t++)e=arguments[t](e);return e}}}(P(t[r]),N(A(e[t[r]])));return U(n)},D=e=>L(e,Object.keys(e)),F=function(){if(1==arguments.length){let e=arguments[0];return function(t){return Object.defineProperty(e,R,{value:()=>t,enumerable:!1}),t}}let e=arguments[0],t=arguments[1];return Object.defineProperty(e,R,{value:()=>t,enumerable:!1}),t},z=Symbol.for("effect/Equal");function $(){return 1==arguments.length?e=>B(e,arguments[0]):B(arguments[0],arguments[1])}function B(e,t){if(e===t)return!0;let n=typeof e;if(n!==typeof t)return!1;if("object"===n||"function"===n){if(null!==e&&null!==t){if(H(e)&&H(t))return!!(A(e)===A(t)&&e[z](t))||!!S.enabled&&!!S.tester&&S.tester(e,t);if(e instanceof Date&&t instanceof Date)return e.toISOString()===t.toISOString();if(e instanceof URL&&t instanceof URL)return e.href===t.href}if(S.enabled){if(Array.isArray(e)&&Array.isArray(t))return e.length===t.length&&e.every((e,n)=>B(e,t[n]));if(Object.getPrototypeOf(e)===Object.prototype&&Object.getPrototypeOf(e)===Object.prototype){let n=Object.keys(e),r=Object.keys(t);if(n.length===r.length){for(let r of n)if(!(r in t&&B(e[r],t[r])))return!!S.tester&&S.tester(e,t);return!0}}return!!S.tester&&S.tester(e,t)}}return!!S.enabled&&!!S.tester&&S.tester(e,t)}let H=e=>m(e,z),J=Symbol.for("nodejs.util.inspect.custom"),V=e=>{try{if(m(e,"toJSON")&&h(e.toJSON)&&0===e.toJSON.length)return e.toJSON();if(Array.isArray(e))return e.map(V)}catch(e){return{}}return K(e)},G=e=>JSON.stringify(e,null,2),q=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;if("string"==typeof e)return e;try{return"object"==typeof e?W(e,t):String(e)}catch(t){return String(e)}},W=(e,t)=>{let n=[],r=JSON.stringify(e,(e,t)=>"object"==typeof t&&null!==t?n.includes(t)?void 0:n.push(t)&&(void 0!==Z.fiberRefs&&X(t)?t[Y](Z.fiberRefs):t):t,t);return n=void 0,r},Y=Symbol.for("effect/Inspectable/Redactable"),X=e=>"object"==typeof e&&null!==e&&Y in e,Z=d("effect/Inspectable/redactableState",()=>({fiberRefs:void 0})),K=e=>X(e)&&void 0!==Z.fiberRefs?e[Y](Z.fiberRefs):e,Q=(e,t)=>{switch(t.length){case 0:return e;case 1:return t[0](e);case 2:return t[1](t[0](e));case 3:return t[2](t[1](t[0](e)));case 4:return t[3](t[2](t[1](t[0](e))));case 5:return t[4](t[3](t[2](t[1](t[0](e)))));case 6:return t[5](t[4](t[3](t[2](t[1](t[0](e))))));case 7:return t[6](t[5](t[4](t[3](t[2](t[1](t[0](e)))))));case 8:return t[7](t[6](t[5](t[4](t[3](t[2](t[1](t[0](e))))))));case 9:return t[8](t[7](t[6](t[5](t[4](t[3](t[2](t[1](t[0](e)))))))));default:{let n=e;for(let e=0,r=t.length;e<r;e++)n=t[e](n);return n}}},ee=Symbol.for("effect/Effect"),et=Symbol.for("effect/Stream"),en=Symbol.for("effect/Sink"),er=Symbol.for("effect/Channel"),ea={_R:e=>e,_E:e=>e,_A:e=>e,_V:"3.14.21"},ei={[ee]:ea,[et]:ea,[en]:{_A:e=>e,_In:e=>e,_L:e=>e,_E:e=>e,_R:e=>e},[er]:{_Env:e=>e,_InErr:e=>e,_InElem:e=>e,_InDone:e=>e,_OutErr:e=>e,_OutElem:e=>e,_OutDone:e=>e},[z](e){return this===e},[R](){return F(this,T(this))},[Symbol.iterator](){return new _(new E(this))},pipe(){return Q(this,arguments)}},es={[R](){return F(this,D(this))},[z](e){let t=Object.keys(this),n=Object.keys(e);if(t.length!==n.length)return!1;for(let n of t)if(!(n in e&&$(this[n],e[n])))return!1;return!0}},eo=Symbol.for("effect/Option"),el={...ei,[eo]:{_A:e=>e},[J](){return this.toJSON()},toString(){return G(this.toJSON())}},eu=Object.assign(Object.create(el),{_tag:"Some",_op:"Some",[z](e){return ep(e)&&eh(e)&&$(this.value,e.value)},[R](){return F(this,N(A(this._tag))(A(this.value)))},toJSON(){return{_id:"Option",_tag:this._tag,value:V(this.value)}}}),ec=A("None"),ed=Object.assign(Object.create(el),{_tag:"None",_op:"None",[z]:e=>ep(e)&&ef(e),[R]:()=>ec,toJSON(){return{_id:"Option",_tag:this._tag}}}),ep=e=>m(e,eo),ef=e=>"None"===e._tag,eh=e=>"Some"===e._tag,eg=Object.create(ed);Symbol.iterator,()=>ev;let ev={next:()=>({done:!0,value:void 0})};Object.fromEntries,(e,t)=>{let n=[];for(let r of em(e))n.push(t(r,e[r]));return n};let em=e=>Object.keys(e),ex=e=>Array.isArray(e)?e:Array.from(e),eb=e=>Array.isArray(e)?e:[e],ey=Symbol.for("effect/Context/Tag"),ew=Symbol.for("effect/Context/Reference"),e_=Symbol.for("effect/STM"),ej={...ei,_op:"Tag",[e_]:ea,[ey]:{_Service:e=>e,_Identifier:e=>e},toString(){return G(this.toJSON())},toJSON(){return{_id:"Tag",key:this.key,stack:this.stack}},[J](){return this.toJSON()},of:e=>e,context(e){return eA(this,e)}},ek={...ej,[ew]:ew},eE=Symbol.for("effect/Context"),eS={[eE]:{_Services:e=>e},[z](e){if(eR(e)&&this.unsafeMap.size===e.unsafeMap.size){for(let t of this.unsafeMap.keys())if(!e.unsafeMap.has(t)||!$(this.unsafeMap.get(t),e.unsafeMap.get(t)))return!1;return!0}return!1},[R](){return F(this,M(this.unsafeMap.size))},pipe(){return Q(this,arguments)},toString(){return G(this.toJSON())},toJSON(){return{_id:"Context",services:Array.from(this.unsafeMap).map(V)}},[J](){return this.toJSON()}},eO=e=>{let t=Object.create(eS);return t.unsafeMap=e,t},eC=e=>{let t=Error("Service not found".concat(e.key?": ".concat(String(e.key)):""));if(e.stack){let n=e.stack.split("\n");if(n.length>2){let e=n[2].match(/at (.*)/);e&&(t.message=t.message+" (defined at ".concat(e[1],")"))}}if(t.stack){let e=t.stack.split("\n");e.splice(1,3),t.stack=e.join("\n")}return t},eR=e=>m(e,eE),eA=(e,t)=>eO(new Map([[e.key,t]])),eT=o(3,(e,t,n)=>{let r=new Map(e.unsafeMap);return r.set(t.key,n),eO(r)}),eN=d("effect/Context/defaultValueCache",()=>new Map),eU=e=>{if(eN.has(e.key))return eN.get(e.key);let t=e.defaultValue();return eN.set(e.key,t),t},eI=(e,t)=>e.unsafeMap.has(t.key)?e.unsafeMap.get(t.key):eU(t),eM=o(2,(e,t)=>{if(!e.unsafeMap.has(t.key)){if(ew in t)return eU(t);throw eC(t)}return e.unsafeMap.get(t.key)}),eP=e=>()=>{let t=Error.stackTraceLimit;Error.stackTraceLimit=2;let n=Error();function r(){}return Error.stackTraceLimit=t,Object.setPrototypeOf(r,ej),r.key=e,Object.defineProperty(r,"stack",{get:()=>n.stack}),r},eL=()=>(e,t)=>{let n=Error.stackTraceLimit;Error.stackTraceLimit=2;let r=Error();function a(){}return Error.stackTraceLimit=n,Object.setPrototypeOf(a,ek),a.key=e,a.defaultValue=t.defaultValue,Object.defineProperty(a,"stack",{get:()=>r.stack}),a},eD=Symbol.for("effect/Micro"),eF=Symbol.for("effect/Micro/MicroExit"),ez=e=>"object"==typeof e&&null!==e&&eD in e,e$=Symbol.for("effect/Micro/MicroCause"),eB={_E:l};class eH extends globalThis.Error{pipe(){return Q(this,arguments)}toString(){return this.stack}[J](){return this.stack}constructor(e,t,n){let r,a,i;let s="MicroCause.".concat(e);if(t instanceof globalThis.Error){r="(".concat(s,") ").concat(t.name);let e=(a=t.message).split("\n").length;i=t.stack?"(".concat(s,") ").concat(t.stack.split("\n").slice(0,e+3).join("\n")):"".concat(r,": ").concat(a)}else r=s,a=q(t,0),i="".concat(r,": ").concat(a);n.length>0&&(i+="\n    ".concat(n.join("\n    "))),super(a),this._tag=e,this.traces=n,this[e$]=eB,this.name=r,this.stack=i}}class eJ extends eH{constructor(e,t=[]){super("Fail",e,t),this.error=e}}let eV=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return new eJ(e,t)};class eG extends eH{constructor(e,t=[]){super("Die",e,t),this.defect=e}}let eq=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return new eG(e,t)};class eW extends eH{constructor(e=[]){super("Interrupt","interrupted",e)}}let eY=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return new eW(e)},eX=e=>"Fail"===e._tag,eZ=e=>"Interrupt"===e._tag,eK=e=>"Fail"===e._tag?e.error:"Die"===e._tag?e.defect:e,eQ=o(2,(e,t)=>{let n=[...e.traces,t];switch(e._tag){case"Die":return eq(e.defect,n);case"Interrupt":return eY(n);case"Fail":return eV(e.error,n)}}),e0=Symbol.for("effect/Micro/MicroFiber"),e1={_A:l,_E:l};class e2{getRef(e){return eI(this.context,e)}addObserver(e){return this._exit?(e(this._exit),u):(this._observers.push(e),()=>{let t=this._observers.indexOf(e);t>=0&&this._observers.splice(t,1)})}unsafeInterrupt(){!this._exit&&(this._interrupted=!0,this.interruptible&&this.evaluate(tN))}unsafePoll(){return this._exit}evaluate(e){if(this._exit)return;if(void 0!==this._yielded){let e=this._yielded;this._yielded=void 0,e()}let t=this.runLoop(e);if(t===tt)return;let n=e4.interruptChildren&&e4.interruptChildren(this);if(void 0!==n)return this.evaluate(tO(n,()=>t));this._exit=t;for(let e=0;e<this._observers.length;e++)this._observers[e](t);this._observers.length=0}runLoop(e){let t=!1,n=e;this.currentOpCount=0;try{for(;;){if(this.currentOpCount++,!t&&this.getRef(tH).shouldYield(this)){t=!0;let e=n;n=tO(tp,()=>e)}if((n=n[e7](this))===tt){let e=this._yielded;if(eF in e)return this._yielded=void 0,e;return tt}}}catch(e){if(!m(n,e7))return tU("MicroFiber.runLoop: Not a valid effect: ".concat(String(n)));return tU(e)}}getCont(e){for(;;){let t=this._stack.pop();if(!t)return;let n=t[te]&&t[te](this);if(n)return{[e]:n};if(t[e])return t}}yieldWith(e){return this._yielded=e,tt}children(){var e;return null!==(e=this._children)&&void 0!==e?e:this._children=new Set}constructor(e,t=!0){this._stack=[],this._observers=[],this.currentOpCount=0,this._interrupted=!1,this._yielded=void 0,this.context=e,this.interruptible=t,this[e0]=e1}}let e4=d("effect/Micro/fiberMiddleware",()=>({interruptChildren:void 0})),e3=e=>td(()=>{for(let t of e)t.unsafeInterrupt();let t=e[Symbol.iterator](),n=td(()=>{let e=t.next();for(;!e.done;){if(e.value.unsafePoll()){e=t.next();continue}let r=e.value;return tw(e=>{r.addObserver(t=>{e(n)})})}return tM});return n}),e5=Symbol.for("effect/Micro/identifier"),e6=Symbol.for("effect/Micro/args"),e7=Symbol.for("effect/Micro/evaluate"),e9=Symbol.for("effect/Micro/successCont"),e8=Symbol.for("effect/Micro/failureCont"),te=Symbol.for("effect/Micro/ensureCont"),tt=Symbol.for("effect/Micro/Yield"),tn={...ei,_op:"Micro",[eD]:{_A:l,_E:l,_R:l},pipe(){return Q(this,arguments)},[Symbol.iterator](){return new _(new E(this))},toJSON(){return{_id:"Micro",op:this[e5],...e6 in this?{args:this[e6]}:void 0}},toString(){return G(this)},[J](){return G(this)}};function tr(e){return tU("Micro.evaluate: Not implemented")}let ta=e=>{var t;return{...tn,[e5]:e.op,[e7]:null!==(t=e.eval)&&void 0!==t?t:tr,[e9]:e.contA,[e8]:e.contE,[te]:e.ensure}},ti=e=>{let t=ta(e);return function(){let n=Object.create(t);return n[e6]=!1===e.single?arguments:arguments[0],n}},ts=e=>{let t={...ta(e),[eF]:eF,_tag:e.op,get[e.prop](){return this[e6]},toJSON(){return{_id:"MicroExit",_tag:e.op,[e.prop]:this[e6]}},[z](t){return tA(t)&&t._tag===e.op&&$(this[e6],t[e6])},[R](){return F(this,N(P(e.op))(A(this[e6])))}};return function(e){let n=Object.create(t);return n[e6]=e,n[e9]=void 0,n[e8]=void 0,n[te]=void 0,n}},to=ts({op:"Success",prop:"value",eval(e){let t=e.getCont(e9);return t?t[e9](this[e6],e):e.yieldWith(this)}}),tl=ts({op:"Failure",prop:"cause",eval(e){let t=e.getCont(e8);for(;eZ(this[e6])&&t&&e.interruptible;)t=e.getCont(e8);return t?t[e8](this[e6],e):e.yieldWith(this)}}),tu=e=>tl(eV(e)),tc=ti({op:"Sync",eval(e){let t=this[e6](),n=e.getCont(e9);return n?n[e9](t,e):e.yieldWith(tT(t))}}),td=ti({op:"Suspend",eval(e){return this[e6]()}}),tp=ti({op:"Yield",eval(e){var t;let n=!1;return e.getRef(tH).scheduleTask(()=>{n||e.evaluate(tM)},null!==(t=this[e6])&&void 0!==t?t:0),e.yieldWith(()=>{n=!0})}})(0),tf=e=>tU(e),th=to(void 0),tg=e=>td(()=>{try{return to(e.try())}catch(t){return tu(e.catch(t))}}),tv=e=>tb(function(t,n){e(n).then(e=>t(to(e)),e=>t(tf(e)))},0!==e.length),tm=e=>tb(function(t,n){try{e.try(n).then(e=>t(to(e)),n=>t(tu(e.catch(n))))}catch(n){t(tu(e.catch(n)))}},0!==e.try.length),tx=ti({op:"WithMicroFiber",eval(e){return this[e6](e)}}),tb=ti({op:"Async",single:!1,eval(e){let t=this[e6][0],n=!1,r=!1,a=this[e6][1]?new AbortController:void 0,i=t(t=>{n||(n=!0,r?e.evaluate(t):r=t)},null==a?void 0:a.signal);return!1!==r?r:(r=!0,e._yielded=()=>{n=!0},void 0===a&&void 0===i||e._stack.push(ty(()=>(n=!0,null==a||a.abort(),null!=i?i:tM))),tt)}}),ty=ti({op:"AsyncFinalizer",ensure(e){e.interruptible&&(e.interruptible=!1,e._stack.push(t3(!0)))},contE(e,t){return eZ(e)?tO(this[e6](),()=>tl(e)):tl(e)}}),tw=e=>tb(e,e.length>=2),t_=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return td(()=>tj(1===t.length?t[0]():t[1].call(t[0])))},tj=ti({op:"Iterator",contA(e,t){let n=this[e6].next(e);return n.done?to(n.value):(t._stack.push(this),function(e){if("object"==typeof e&&null!==e&&j in e)return e[j]();throw Error(w("yieldWrapGet"))}(n.value))},eval(e){return this[e9](void 0,e)}}),tk=o(2,(e,t)=>tR(e,e=>t)),tE=o(2,(e,t)=>tO(e,e=>{let n=ez(t)?t:"function"==typeof t?t(e):t;return ez(n)?n:to(n)})),tS=o(2,(e,t)=>tO(e,e=>{let n=ez(t)?t:"function"==typeof t?t(e):t;return ez(n)?tk(n,e):to(e)})),tO=o(2,(e,t)=>{let n=Object.create(tC);return n[e6]=e,n[e9]=t,n}),tC=ta({op:"OnSuccess",eval(e){return e._stack.push(this),this[e6]}}),tR=o(2,(e,t)=>tO(e,e=>to(t(e)))),tA=e=>m(e,eF),tT=to,tN=tl(eY()),tU=e=>tl(eq(e)),tI=e=>"Failure"===e._tag,tM=tT(void 0),tP="setImmediate"in globalThis?globalThis.setImmediate:e=>setTimeout(e,0);class tL{scheduleTask(e,t){this.tasks.push(e),this.running||(this.running=!0,tP(this.afterScheduled))}runTasks(){let e=this.tasks;this.tasks=[];for(let t=0,n=e.length;t<n;t++)e[t]()}shouldYield(e){return e.currentOpCount>=e.getRef(t$)}flush(){for(;this.tasks.length>0;)this.runTasks()}constructor(){this.tasks=[],this.running=!1,this.afterScheduled=()=>{this.running=!1,this.runTasks()}}}let tD=e=>tx(t=>to(eM(t.context,e))),tF=o(2,(e,t)=>tx(n=>{let r=n.context;return n.context=t(r),t1(e,()=>(n.context=r,th))})),tz=o(3,(e,t,n)=>tF(e,eT(t,n)));class t$ extends eL()("effect/Micro/currentMaxOpsBeforeYield",{defaultValue:()=>2048}){}class tB extends eL()("effect/Micro/currentConcurrency",{defaultValue:()=>"unbounded"}){}class tH extends eL()("effect/Micro/currentScheduler",{defaultValue:()=>new tL}){}let tJ=o(e=>ez(e[0]),(e,t,n)=>tO(e,e=>t(e)?to(e):tu(n(e)))),tV=o(2,(e,t)=>{let n=Object.create(tG);return n[e6]=e,n[e8]=t,n}),tG=ta({op:"OnFailure",eval(e){return e._stack.push(this),this[e6]}}),tq=o(3,(e,t,n)=>tV(e,e=>t(e)?n(e):tl(e))),tW=o(3,(e,t,n)=>tq(e,t,e=>tE(n(e),tl(e)))),tY=o(2,(e,t)=>tW(e,eX,e=>t(e.error))),tX=o(3,(e,t,n)=>tq(e,e=>eX(e)&&t(e.error),e=>n(e.error))),tZ=o(3,(e,t,n)=>tX(e,x(t),n)),tK=function(){let e=globalThis.Error.stackTraceLimit;globalThis.Error.stackTraceLimit=2;let t=new globalThis.Error;globalThis.Error.stackTraceLimit=e;let n=e=>n=>t4(n,n=>tl(function(e,n){var r;let a=t.stack;if(!a)return n;let i=null===(r=a.split("\n")[2])||void 0===r?void 0:r.trim().replace(/^at /,"");if(!i)return n;let s=i.match(/\((.*)\)$/);return eQ(n,"at ".concat(e," (").concat(s?s[1]:i,")"))}(e,n)));return 2==arguments.length?n(arguments[1])(arguments[0]):n(arguments[0])},tQ=o(2,(e,t)=>{let n=Object.create(t0);return n[e6]=e,n[e9]=t.onSuccess,n[e8]=t.onFailure,n}),t0=ta({op:"OnSuccessAndFailure",eval(e){return e._stack.push(this),this[e6]}}),t1=o(2,(e,t)=>t6(n=>tQ(n(e),{onFailure:e=>tO(t(tl(e)),()=>tl(e)),onSuccess:e=>tO(t(tT(e)),()=>to(e))}))),t2=o(3,(e,t,n)=>t1(e,e=>t(e)?n(e):tM)),t4=o(2,(e,t)=>t2(e,tI,e=>t(e.cause))),t3=ti({op:"SetInterruptible",ensure(e){if(e.interruptible=this[e6],e._interrupted&&e.interruptible)return()=>tN}}),t5=e=>tx(t=>t.interruptible?e:(t.interruptible=!0,t._stack.push(t3(!1)),t._interrupted)?tN:e),t6=e=>tx(t=>t.interruptible?(t.interruptible=!1,t._stack.push(t3(!0)),e(t5)):e(l)),t7=ti({op:"While",contA(e,t){return(this[e6].step(e),this[e6].while())?(t._stack.push(this),this[e6].body()):tM},eval(e){return this[e6].while()?(e._stack.push(this),this[e6].body()):tM}}),t9=(e,t,n)=>tx(r=>{var a;let i=(null==n?void 0:n.concurrency)==="inherit"?r.getRef(tB):null!==(a=null==n?void 0:n.concurrency)&&void 0!==a?a:1,s="unbounded"===i?Number.POSITIVE_INFINITY:Math.max(1,i),o=ex(e),l=o.length;if(0===l)return(null==n?void 0:n.discard)?th:to([]);let u=(null==n?void 0:n.discard)?void 0:Array(l),c=0;return 1===s?tk(t7({while:()=>c<o.length,body:()=>t(o[c],c),step:u?e=>u[c++]=e:e=>c++}),u):tw(e=>{let n;let a=new Set,i=0,d=0,p=!1,f=!1;return!function h(){for(p=!0;i<s&&c<l;){let g=c,v=o[g];c++,i++;try{let o=t8(r,t(v,g),!0,!0);a.add(o),o.addObserver(t=>{a.delete(o),!f&&("Failure"===t._tag?void 0===n&&(n=t,l=c,a.forEach(e=>e.unsafeInterrupt())):void 0!==u&&(u[g]=t.value),d++,i--,d===l?e(null!=n?n:to(u)):!p&&i<s&&h())})}catch(e){n=tU(e),l=c,a.forEach(e=>e.unsafeInterrupt())}}p=!1}(),td(()=>(f=!0,c=l,e3(a)))})}),t8=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],a=new e2(e.context,e.interruptible);return r||(e.children().add(a),a.addObserver(()=>e.children().delete(a))),n?a.evaluate(t):e.getRef(tH).scheduleTask(()=>a.evaluate(t),0),a},ne=(e,t)=>{var n;let r=new e2(tH.context(null!==(n=null==t?void 0:t.scheduler)&&void 0!==n?n:new tL));if(r.evaluate(e),null==t?void 0:t.signal){if(t.signal.aborted)r.unsafeInterrupt();else{let e=()=>r.unsafeInterrupt();t.signal.addEventListener("abort",e,{once:!0}),r.addObserver(()=>t.signal.removeEventListener("abort",e))}}return r},nt=(e,t)=>new Promise((n,r)=>{ne(e,t).addObserver(n)}),nn=(e,t)=>nt(e,t).then(e=>{if("Failure"===e._tag)throw e.cause;return e.value}),nr=e=>{var t;let n=new tL,r=ne(e,{scheduler:n});return n.flush(),null!==(t=r._exit)&&void 0!==t?t:tU(r)},na=e=>{let t=nr(e);if("Failure"===t._tag)throw t.cause;return t.value},ni=function(){class e extends globalThis.Error{}return Object.assign(e.prototype,tn,es,{[e5]:"Failure",[e7](){return tu(this)},toString(){return this.message?"".concat(this.name,": ").concat(this.message):this.name},toJSON(){return{...this}},[J](){let e=this.stack;return e?"".concat(this.toString(),"\n").concat(e.split("\n").slice(1).join("\n")):this.toString()}}),e}(),ns=class extends ni{constructor(e){super(),e&&Object.assign(this,e)}},no=e=>{class t extends ns{constructor(...t){super(...t),this._tag=e}}return t.prototype.name=e,t},nl={"audio/3gpp":{source:"iana",extensions:["3gpp"]},"audio/adpcm":{source:"apache",extensions:["adp"]},"audio/amr":{source:"iana",extensions:["amr"]},"audio/basic":{source:"iana",extensions:["au","snd"]},"audio/midi":{source:"apache",extensions:["mid","midi","kar","rmi"]},"audio/mobile-xmf":{source:"iana",extensions:["mxmf"]},"audio/mp4":{source:"iana",extensions:["m4a","mp4a"]},"audio/mpeg":{source:"iana",extensions:["mpga","mp2","mp2a","mp3","m2a","m3a"]},"audio/ogg":{source:"iana",extensions:["oga","ogg","spx","opus"]},"audio/s3m":{source:"apache",extensions:["s3m"]},"audio/silk":{source:"apache",extensions:["sil"]},"audio/vnd.dece.audio":{source:"iana",extensions:["uva","uvva"]},"audio/vnd.digital-winds":{source:"iana",extensions:["eol"]},"audio/vnd.dra":{source:"iana",extensions:["dra"]},"audio/vnd.dts":{source:"iana",extensions:["dts"]},"audio/vnd.dts.hd":{source:"iana",extensions:["dtshd"]},"audio/vnd.lucent.voice":{source:"iana",extensions:["lvp"]},"audio/vnd.ms-playready.media.pya":{source:"iana",extensions:["pya"]},"audio/vnd.nuera.ecelp4800":{source:"iana",extensions:["ecelp4800"]},"audio/vnd.nuera.ecelp7470":{source:"iana",extensions:["ecelp7470"]},"audio/vnd.nuera.ecelp9600":{source:"iana",extensions:["ecelp9600"]},"audio/vnd.rip":{source:"iana",extensions:["rip"]},"audio/webm":{source:"apache",extensions:["weba"]},"audio/x-aac":{source:"apache",extensions:["aac"]},"audio/x-aiff":{source:"apache",extensions:["aif","aiff","aifc"]},"audio/x-caf":{source:"apache",extensions:["caf"]},"audio/x-flac":{source:"apache",extensions:["flac"]},"audio/x-m4a":{source:"nginx",extensions:["m4a"]},"audio/x-matroska":{source:"apache",extensions:["mka"]},"audio/x-mpegurl":{source:"apache",extensions:["m3u"]},"audio/x-ms-wax":{source:"apache",extensions:["wax"]},"audio/x-ms-wma":{source:"apache",extensions:["wma"]},"audio/x-pn-realaudio":{source:"apache",extensions:["ram","ra"]},"audio/x-pn-realaudio-plugin":{source:"apache",extensions:["rmp"]},"audio/x-realaudio":{source:"nginx",extensions:["ra"]},"audio/x-wav":{source:"apache",extensions:["wav"]},"audio/x-gsm":{source:"apache",extensions:["gsm"]},"audio/xm":{source:"apache",extensions:["xm"]}},nu={"image/aces":{source:"iana",extensions:["exr"]},"image/avci":{source:"iana",extensions:["avci"]},"image/avcs":{source:"iana",extensions:["avcs"]},"image/avif":{source:"iana",extensions:["avif"]},"image/bmp":{source:"iana",extensions:["bmp"]},"image/cgm":{source:"iana",extensions:["cgm"]},"image/dicom-rle":{source:"iana",extensions:["drle"]},"image/emf":{source:"iana",extensions:["emf"]},"image/fits":{source:"iana",extensions:["fits"]},"image/g3fax":{source:"iana",extensions:["g3"]},"image/gif":{source:"iana",extensions:["gif"]},"image/heic":{source:"iana",extensions:["heic"]},"image/heic-sequence":{source:"iana",extensions:["heics"]},"image/heif":{source:"iana",extensions:["heif"]},"image/heif-sequence":{source:"iana",extensions:["heifs"]},"image/hej2k":{source:"iana",extensions:["hej2"]},"image/hsj2":{source:"iana",extensions:["hsj2"]},"image/ief":{source:"iana",extensions:["ief"]},"image/jls":{source:"iana",extensions:["jls"]},"image/jp2":{source:"iana",extensions:["jp2","jpg2"]},"image/jpeg":{source:"iana",extensions:["jpeg","jpg","jpe","jfif","pjpeg","pjp"]},"image/jph":{source:"iana",extensions:["jph"]},"image/jphc":{source:"iana",extensions:["jhc"]},"image/jpm":{source:"iana",extensions:["jpm"]},"image/jpx":{source:"iana",extensions:["jpx","jpf"]},"image/jxr":{source:"iana",extensions:["jxr"]},"image/jxra":{source:"iana",extensions:["jxra"]},"image/jxrs":{source:"iana",extensions:["jxrs"]},"image/jxs":{source:"iana",extensions:["jxs"]},"image/jxsc":{source:"iana",extensions:["jxsc"]},"image/jxsi":{source:"iana",extensions:["jxsi"]},"image/jxss":{source:"iana",extensions:["jxss"]},"image/ktx":{source:"iana",extensions:["ktx"]},"image/ktx2":{source:"iana",extensions:["ktx2"]},"image/png":{source:"iana",extensions:["png"]},"image/prs.btif":{source:"iana",extensions:["btif"]},"image/prs.pti":{source:"iana",extensions:["pti"]},"image/sgi":{source:"apache",extensions:["sgi"]},"image/svg+xml":{source:"iana",extensions:["svg","svgz"]},"image/t38":{source:"iana",extensions:["t38"]},"image/tiff":{source:"iana",extensions:["tif","tiff"]},"image/tiff-fx":{source:"iana",extensions:["tfx"]},"image/vnd.adobe.photoshop":{source:"iana",extensions:["psd"]},"image/vnd.airzip.accelerator.azv":{source:"iana",extensions:["azv"]},"image/vnd.dece.graphic":{source:"iana",extensions:["uvi","uvvi","uvg","uvvg"]},"image/vnd.djvu":{source:"iana",extensions:["djvu","djv"]},"image/vnd.dvb.subtitle":{source:"iana",extensions:["sub"]},"image/vnd.dwg":{source:"iana",extensions:["dwg"]},"image/vnd.dxf":{source:"iana",extensions:["dxf"]},"image/vnd.fastbidsheet":{source:"iana",extensions:["fbs"]},"image/vnd.fpx":{source:"iana",extensions:["fpx"]},"image/vnd.fst":{source:"iana",extensions:["fst"]},"image/vnd.fujixerox.edmics-mmr":{source:"iana",extensions:["mmr"]},"image/vnd.fujixerox.edmics-rlc":{source:"iana",extensions:["rlc"]},"image/vnd.microsoft.icon":{source:"iana",extensions:["ico"]},"image/vnd.ms-modi":{source:"iana",extensions:["mdi"]},"image/vnd.ms-photo":{source:"apache",extensions:["wdp"]},"image/vnd.net-fpx":{source:"iana",extensions:["npx"]},"image/vnd.pco.b16":{source:"iana",extensions:["b16"]},"image/vnd.tencent.tap":{source:"iana",extensions:["tap"]},"image/vnd.valve.source.texture":{source:"iana",extensions:["vtf"]},"image/vnd.wap.wbmp":{source:"iana",extensions:["wbmp"]},"image/vnd.xiff":{source:"iana",extensions:["xif"]},"image/vnd.zbrush.pcx":{source:"iana",extensions:["pcx"]},"image/webp":{source:"apache",extensions:["webp"]},"image/wmf":{source:"iana",extensions:["wmf"]},"image/x-3ds":{source:"apache",extensions:["3ds"]},"image/x-cmu-raster":{source:"apache",extensions:["ras"]},"image/x-cmx":{source:"apache",extensions:["cmx"]},"image/x-freehand":{source:"apache",extensions:["fh","fhc","fh4","fh5","fh7"]},"image/x-icon":{source:"apache",extensions:["ico"]},"image/x-jng":{source:"nginx",extensions:["jng"]},"image/x-mrsid-image":{source:"apache",extensions:["sid"]},"image/x-ms-bmp":{source:"nginx",extensions:["bmp"]},"image/x-pcx":{source:"apache",extensions:["pcx"]},"image/x-pict":{source:"apache",extensions:["pic","pct"]},"image/x-portable-anymap":{source:"apache",extensions:["pnm"]},"image/x-portable-bitmap":{source:"apache",extensions:["pbm"]},"image/x-portable-graymap":{source:"apache",extensions:["pgm"]},"image/x-portable-pixmap":{source:"apache",extensions:["ppm"]},"image/x-rgb":{source:"apache",extensions:["rgb"]},"image/x-tga":{source:"apache",extensions:["tga"]},"image/x-xbitmap":{source:"apache",extensions:["xbm"]},"image/x-xpixmap":{source:"apache",extensions:["xpm"]},"image/x-xwindowdump":{source:"apache",extensions:["xwd"]}},nc={"text/cache-manifest":{source:"iana",extensions:["appcache","manifest"]},"text/calendar":{source:"iana",extensions:["ics","ifb"]},"text/css":{source:"iana",charset:"UTF-8",extensions:["css"]},"text/csv":{source:"iana",extensions:["csv"]},"text/html":{source:"iana",extensions:["html","htm","shtml"]},"text/markdown":{source:"iana",extensions:["markdown","md"]},"text/mathml":{source:"nginx",extensions:["mml"]},"text/n3":{source:"iana",charset:"UTF-8",extensions:["n3"]},"text/plain":{source:"iana",extensions:["txt","text","conf","def","list","log","in","ini"]},"text/prs.lines.tag":{source:"iana",extensions:["dsc"]},"text/richtext":{source:"iana",extensions:["rtx"]},"text/rtf":{source:"iana",extensions:["rtf"]},"text/sgml":{source:"iana",extensions:["sgml","sgm"]},"text/shex":{source:"iana",extensions:["shex"]},"text/spdx":{source:"iana",extensions:["spdx"]},"text/tab-separated-values":{source:"iana",extensions:["tsv"]},"text/troff":{source:"iana",extensions:["t","tr","roff","man","me","ms"]},"text/turtle":{source:"iana",charset:"UTF-8",extensions:["ttl"]},"text/uri-list":{source:"iana",extensions:["uri","uris","urls"]},"text/vcard":{source:"iana",extensions:["vcard"]},"text/vnd.curl":{source:"iana",extensions:["curl"]},"text/vnd.curl.dcurl":{source:"apache",extensions:["dcurl"]},"text/vnd.curl.mcurl":{source:"apache",extensions:["mcurl"]},"text/vnd.curl.scurl":{source:"apache",extensions:["scurl"]},"text/vnd.dvb.subtitle":{source:"iana",extensions:["sub"]},"text/vnd.familysearch.gedcom":{source:"iana",extensions:["ged"]},"text/vnd.fly":{source:"iana",extensions:["fly"]},"text/vnd.fmi.flexstor":{source:"iana",extensions:["flx"]},"text/vnd.graphviz":{source:"iana",extensions:["gv"]},"text/vnd.in3d.3dml":{source:"iana",extensions:["3dml"]},"text/vnd.in3d.spot":{source:"iana",extensions:["spot"]},"text/vnd.sun.j2me.app-descriptor":{source:"iana",charset:"UTF-8",extensions:["jad"]},"text/vnd.wap.wml":{source:"iana",extensions:["wml"]},"text/vnd.wap.wmlscript":{source:"iana",extensions:["wmls"]},"text/vtt":{source:"iana",charset:"UTF-8",extensions:["vtt"]},"text/x-asm":{source:"apache",extensions:["s","asm"]},"text/x-c":{source:"apache",extensions:["c","cc","cxx","cpp","h","hh","dic"]},"text/x-component":{source:"nginx",extensions:["htc"]},"text/x-fortran":{source:"apache",extensions:["f","for","f77","f90"]},"text/x-java-source":{source:"apache",extensions:["java"]},"text/x-nfo":{source:"apache",extensions:["nfo"]},"text/x-opml":{source:"apache",extensions:["opml"]},"text/x-pascal":{source:"apache",extensions:["p","pas"]},"text/x-setext":{source:"apache",extensions:["etx"]},"text/x-sfv":{source:"apache",extensions:["sfv"]},"text/x-uuencode":{source:"apache",extensions:["uu"]},"text/x-vcalendar":{source:"apache",extensions:["vcs"]},"text/x-vcard":{source:"apache",extensions:["vcf"]},"text/xml":{source:"iana",extensions:["xml"]}},nd={"video/3gpp":{source:"iana",extensions:["3gp","3gpp"]},"video/3gpp2":{source:"iana",extensions:["3g2"]},"video/h261":{source:"iana",extensions:["h261"]},"video/h263":{source:"iana",extensions:["h263"]},"video/h264":{source:"iana",extensions:["h264"]},"video/iso.segment":{source:"iana",extensions:["m4s"]},"video/jpeg":{source:"iana",extensions:["jpgv"]},"video/jpm":{source:"apache",extensions:["jpm","jpgm"]},"video/mj2":{source:"iana",extensions:["mj2","mjp2"]},"video/mp2t":{source:"iana",extensions:["ts"]},"video/mp4":{source:"iana",extensions:["mp4","mp4v","mpg4"]},"video/mpeg":{source:"iana",extensions:["mpeg","mpg","mpe","m1v","m2v"]},"video/ogg":{source:"iana",extensions:["ogv"]},"video/quicktime":{source:"iana",extensions:["qt","mov"]},"video/vnd.dece.hd":{source:"iana",extensions:["uvh","uvvh"]},"video/vnd.dece.mobile":{source:"iana",extensions:["uvm","uvvm"]},"video/vnd.dece.pd":{source:"iana",extensions:["uvp","uvvp"]},"video/vnd.dece.sd":{source:"iana",extensions:["uvs","uvvs"]},"video/vnd.dece.video":{source:"iana",extensions:["uvv","uvvv"]},"video/vnd.dvb.file":{source:"iana",extensions:["dvb"]},"video/vnd.fvt":{source:"iana",extensions:["fvt"]},"video/vnd.mpegurl":{source:"iana",extensions:["mxu","m4u"]},"video/vnd.ms-playready.media.pyv":{source:"iana",extensions:["pyv"]},"video/vnd.uvvu.mp4":{source:"iana",extensions:["uvu","uvvu"]},"video/vnd.vivo":{source:"iana",extensions:["viv"]},"video/webm":{source:"apache",extensions:["webm"]},"video/x-f4v":{source:"apache",extensions:["f4v"]},"video/x-fli":{source:"apache",extensions:["fli"]},"video/x-flv":{source:"apache",extensions:["flv"]},"video/x-m4v":{source:"apache",extensions:["m4v"]},"video/x-matroska":{source:"apache",extensions:["mkv","mk3d","mks"]},"video/x-mng":{source:"apache",extensions:["mng"]},"video/x-ms-asf":{source:"apache",extensions:["asf","asx"]},"video/x-ms-vob":{source:"apache",extensions:["vob"]},"video/x-ms-wm":{source:"apache",extensions:["wm"]},"video/x-ms-wmv":{source:"apache",extensions:["wmv"]},"video/x-ms-wmx":{source:"apache",extensions:["wmx"]},"video/x-ms-wvx":{source:"apache",extensions:["wvx"]},"video/x-msvideo":{source:"apache",extensions:["avi"]},"video/x-sgi-movie":{source:"apache",extensions:["movie"]},"video/x-smv":{source:"apache",extensions:["smv"]}};var np=n(49509);class nf extends no("InvalidURL"){constructor(e){super({reason:`Failed to parse '${e}' as a URL.`})}}class nh extends no("FetchError"){}class ng extends no("InvalidJson"){}class nv extends no("BadRequestError"){getMessage(){return b(this.json)&&"string"==typeof this.json.message?this.json.message:this.message}}class nm extends no("UploadAborted"){}class nx extends no("UploadAborted"){}async function nb(e){let t=await e.text();try{return JSON.parse(t)}catch(e){return console.error(`Error parsing JSON, got '${t}'`,e),Error(`Error parsing JSON, got '${t}'`)}}function ny(e){return Object.keys(e)}function nw(e,t,n){!function(e,t){let n=/(\d+)\.?(\d+)?\.?(\d+)?/,r=n.exec(e);if(!r?.[0])throw Error(`Invalid semver requirement: ${e}`);let a=n.exec(t);if(!a?.[0])throw Error(`Invalid semver to check: ${t}`);let[i,s,o,l]=r,[u,c,d,p]=a;return e.startsWith("^")?s===c&&(!o||!d||!(o>d)):e.startsWith("~")?s===c&&o===d:s===c&&o===d&&l===p}(t,n)&&console.warn(`!!!WARNING::: ${e} requires "uploadthing@${t}", but version "${n}" is installed`)}let n_=e=>t_(function*(){let t="undefined"!=typeof window?window.location.origin:np.env.VERCEL_URL?`https://${np.env.VERCEL_URL}`:"http://localhost:3000",n=yield*tg({try:()=>new URL(e??"/api/uploadthing",t),catch:()=>new nf(e??"/api/uploadthing")});return"/"===n.pathname&&(n.pathname="/api/uploadthing"),n}),nj=e=>e instanceof URL?e:na(n_(e));function nk(){}let nE={BAD_REQUEST:400,NOT_FOUND:404,FORBIDDEN:403,INTERNAL_SERVER_ERROR:500,INTERNAL_CLIENT_ERROR:500,TOO_LARGE:413,TOO_SMALL:400,TOO_MANY_FILES:400,KEY_TOO_LONG:400,URL_GENERATION_FAILED:500,UPLOAD_FAILED:500,MISSING_ENV:500,INVALID_SERVER_CONFIG:500,FILE_LIMIT_EXCEEDED:500};class nS extends ns{constructor(e){let t="string"==typeof e?{code:"INTERNAL_SERVER_ERROR",message:e}:e;super({message:t.message??function(e,t){return"string"==typeof e?e:e instanceof Error||e&&"object"==typeof e&&"message"in e&&"string"==typeof e.message?e.message:t??"An unknown error occurred"}(t.cause,t.code)}),this._tag="UploadThingError",this.name="UploadThingError",this.code=t.code,this.data=t.data,t.cause instanceof Error?this.cause=t.cause:b(t.cause)&&f(t.cause.status)&&p(t.cause.statusText)?this.cause=Error(`Response ${t.cause.status} ${t.cause.statusText}`):p(t.cause)?this.cause=Error(t.cause):this.cause=t.cause}static toObject(e){return{code:e.code,message:e.message,data:e.data}}static serialize(e){return JSON.stringify(nS.toObject(e))}}let nO=e=>new nS({code:"INTERNAL_CLIENT_ERROR",message:"Something went wrong. Please report this to UploadThing.",cause:e});class nC extends eP("uploadthing/Fetch")(){}let nR=(e,t)=>tO(tD(nC),n=>{let r=new Headers(t?.headers??[]),a={url:e.toString(),method:t?.method,body:t?.body,headers:Object.fromEntries(r)};return tm({try:a=>n(e,{...t,headers:r,signal:a}),catch:e=>new nh({error:e instanceof Error?{...e,name:e.name,message:e.message,stack:e.stack}:e,input:a})}).pipe(tY(e=>tc(()=>console.error(e.input))),tR(e=>Object.assign(e,{requestUrl:a.url})),tK("fetch"))}),nA=e=>tm({try:async()=>({json:await e.json(),ok:e.ok,status:e.status}),catch:t=>new ng({error:t,input:e.requestUrl})}).pipe(tJ(({ok:e})=>e,({json:t,status:n})=>new nv({status:n,message:`Request to ${e.requestUrl} failed with status ${n}`,json:t})),tR(({json:e})=>e),tK("parseJson")),nT=(e,t)=>"all"===t?e:"fine"===t?Math.round(e):10*Math.floor(e/10),nN=e=>{let t=Array.isArray(e)?e:ny(e);return t.includes("blob")?[]:t.map(e=>"pdf"===e?"application/pdf":e.includes("/")?e:"audio"===e?["audio/*",...ny(nl)].join(", "):"image"===e?["image/*",...ny(nu)].join(", "):"text"===e?["text/*",...ny(nc)].join(", "):"video"===e?["video/*",...ny(nd)].join(", "):`${e}/*`)},nU=e=>Object.fromEntries(nN(e).map(e=>[e,[]]));function nI(e){let t=e.clipboardData?.items;if(t)return Array.from(t).reduce((e,t)=>{let n=t.getAsFile();return n?[...e,n]:e},[])}let nM=e=>({fileTypes:e?ny(e):[],multiple:(e?Object.values(e).map(e=>e.maxFileCount):[]).some(e=>e&&e>1)}),nP=e=>e.charAt(0).toUpperCase()+e.slice(1),nL=e=>{if(!e)return"";let t=ny(e),n=t.map(e=>"blob"===e?"file":e);if(n.length>1){let e=n.pop();return`${n.join("s, ")} and ${e}s`}let r=t[0],a=n[0];if(!r||!a)return"";let{maxFileSize:i,maxFileCount:s,minFileCount:o}=e[r];return s&&s>1?o>1?`${o} - ${s} ${a}s up to ${i}`:`${a}s up to ${i}, max ${s}`:`${a} (${i})`},nD=e=>nP(nL(e)),nF=(e,t)=>{if("string"==typeof e)return e;if("function"==typeof e){let n=e(t);if("string"==typeof n)return n}return""},nz=(e,t)=>{if("object"==typeof e)return e;if("function"==typeof e){let n=e(t);if("object"==typeof n)return n}return{}},n$=(e,t)=>e?"function"!=typeof e?e:"function"==typeof e?e(t):void 0:null,nB=(...e)=>e.filter(Boolean).join(" ");function nH(e,t){return"application/x-moz-file"===e.type||function(e,t){if(t){let n=Array.isArray(t)?t:t.split(","),r=e.name,a=e.type.toLowerCase(),i=a.replace(/\/.*$/,"");return n.some(e=>{let t=e.trim().toLowerCase();return t.startsWith(".")?r.toLowerCase().endsWith(t):t.endsWith("/*")?i===t.replace(/\/.*$/,""):a===t})}return!0}(e,t)}new TextEncoder;let nJ=e=>null!=e;function nV(e,t,n){return!nJ(e.size)||(nJ(t)&&nJ(n)?e.size>=t&&e.size<=n:!(nJ(t)&&e.size<t||nJ(n)&&e.size>n))}function nG(e,t,n){return(!!t||!(e.length>1))&&(!t||!(n>=1)||!(e.length>n))}function nq(e){return"dataTransfer"in e&&null!==e.dataTransfer?Array.prototype.some.call(e.dataTransfer?.types,e=>"Files"===e||"application/x-moz-file"===e):!!e.target&&"files"in e.target&&!!e.target.files}let nW={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,acceptedFiles:[]};function nY(e,t){switch(t.type){case"focus":return{...e,isFocused:!0};case"blur":return{...e,isFocused:!1};case"openDialog":return{...nW,isFileDialogActive:!0};case"closeDialog":return{...e,isFileDialogActive:!1};case"setDraggedFiles":case"setFiles":return{...e,...t.payload};case"reset":return nW;default:return e}}let nX=()=>{let e,t;let n=new AbortController;return{promise:new Promise((n,r)=>{e=n,t=r}),ac:n,resolve:e,reject:t}},nZ=e=>{console.warn(`⚠️ [uploadthing][deprecated] ${e}`)},nK=e=>{let t=new URL(e.url),n=new URLSearchParams(t.search);return n.set("actionType",e.actionType),n.set("slug",e.slug),t.search=n.toString(),t},nQ=e=>(t,n)=>t_(function*(){let r=nK({url:e.url,slug:e.endpoint,actionType:t}),a=new Headers((yield*tv(async()=>"function"==typeof e.headers?await e.headers():e.headers)));return e.package&&a.set("x-uploadthing-package",e.package),a.set("x-uploadthing-version","7.7.2"),a.set("Content-Type","application/json"),yield*nR(r,{method:"POST",body:JSON.stringify(n),headers:a}).pipe(tE(nA),tR(l),tZ("FetchError",e=>tu(new nS({code:"INTERNAL_CLIENT_ERROR",message:`Failed to report event "${t}" to UploadThing server`,cause:e}))),tZ("BadRequestError",e=>tu(new nS({code:function(e){for(let[t,n]of Object.entries(nE))if(n===e)return t;return"INTERNAL_SERVER_ERROR"}(e.status),message:e.getMessage(),cause:e.json}))),tZ("InvalidJson",e=>tu(new nS({code:"INTERNAL_CLIENT_ERROR",message:"Failed to parse response from UploadThing server",cause:e}))))}),n0=(e,t,n,r)=>tw(a=>{let i=new XMLHttpRequest;i.open("PUT",n.url,!0),i.setRequestHeader("Range",`bytes=${t}-`),i.setRequestHeader("x-uploadthing-version","7.7.2"),i.responseType="json";let s=0;i.upload.addEventListener("progress",({loaded:e})=>{let t=e-s;r?.({loaded:e,delta:t}),s=e}),i.addEventListener("load",()=>{i.status>=200&&i.status<300&&b(i.response)?m(i.response,"error")?a(new nS({code:"UPLOAD_FAILED",message:String(i.response.error),data:i.response})):a(to(i.response)):a(new nS({code:"UPLOAD_FAILED",message:`XHR failed ${i.status} ${i.statusText}`,data:i.response}))}),i.addEventListener("error",()=>{a(new nS({code:"UPLOAD_FAILED"}))});let o=new FormData;return"uri"in e?o.append("file",{uri:e.uri,type:e.type,name:e.name,...t>0&&{range:t}}):o.append("file",t>0?e.slice(t):e),i.send(o),tc(()=>i.abort())}),n1=(e,t,n)=>nR(t.url,{method:"HEAD"}).pipe(tR(({headers:e})=>parseInt(e.get("x-ut-range-start")??"0",10)),tS(e=>n.onUploadProgress?.({delta:e,loaded:e})),tO(r=>n0(e,r,t,e=>n.onUploadProgress?.({delta:e.delta,loaded:e.loaded+r}))),tR(l),tR(n=>({name:e.name,size:e.size,key:t.key,lastModified:e.lastModified,serverData:n.serverData,get url(){return nZ("`file.url` is deprecated and will be removed in uploadthing v9. Use `file.ufsUrl` instead."),n.url},get appUrl(){return nZ("`file.appUrl` is deprecated and will be removed in uploadthing v9. Use `file.ufsUrl` instead."),n.appUrl},ufsUrl:n.ufsUrl,customId:t.customId,type:e.type,fileHash:n.fileHash}))),n2=(e,t)=>{let n=nQ({endpoint:String(e),package:t.package,url:t.url,headers:t.headers}),r=t.files.reduce((e,t)=>e+t.size,0),a=0;return tO(n("upload",{input:"input"in t?t.input:null,files:t.files.map(e=>({name:e.name,size:e.size,type:e.type,lastModified:e.lastModified}))}),e=>t9(e,(e,n)=>tO(tc(()=>t.onUploadBegin?.({file:t.files[n].name})),()=>n1(t.files[n],e,{onUploadProgress:e=>{a+=e.delta,t.onUploadProgress?.({file:t.files[n],progress:e.loaded/t.files[n].size*100,loaded:e.loaded,delta:e.delta,totalLoaded:a,totalProgress:a/r})}})),{concurrency:6}))},n4="7.7.2",n3=e=>{let t=new Proxy(nk,{get:(e,t)=>t});return{uploadFiles:(n,r)=>{let a="function"==typeof n?n(t):n,i=e?.fetch??window.fetch;return n2(a,{...r,skipPolling:{},url:nj(e?.url),package:e?.package??"uploadthing/client",input:r.input}).pipe(tz(nC,i),e=>nt(e,r.signal&&{signal:r.signal})).then(e=>{if("Success"===e._tag)return e.value;if("Interrupt"===e.cause._tag)throw new nx;throw eK(e.cause)})},createUpload:async(n,r)=>{let a=new Map,i=nQ({endpoint:String("function"==typeof n?n(t):n),package:e?.package??"uploadthing/client",url:nj(e?.url),headers:r.headers}),s=e?.fetch??window.fetch,o=await nn(i("upload",{input:"input"in r?r.input:null,files:r.files.map(e=>({name:e.name,size:e.size,type:e.type,lastModified:e.lastModified}))}).pipe(tz(nC,s))),l=r.files.reduce((e,t)=>e+t.size,0),u=0,c=(e,t)=>n1(e,t,{onUploadProgress:t=>{u+=t.delta,r.onUploadProgress?.({...t,file:e,progress:Math.round(t.loaded/e.size*100),totalLoaded:u,totalProgress:Math.round(u/l*100)})}}).pipe(tz(nC,s));for(let[e,t]of o.entries()){let n=r.files[e];if(!n)continue;let i=nX();a.set(n,{deferred:i,presigned:t}),nt(c(n,t),{signal:i.ac.signal}).then(e=>{if("Success"===e._tag)return i.resolve(e.value);if("Interrupt"===e.cause._tag)throw new nm;throw eK(e.cause)}).catch(e=>{e instanceof nm||i.reject(e)})}return{pauseUpload:e=>{for(let t of eb(e??r.files)){let e=a.get(t);if(!e)return;if(e.deferred.ac.signal.aborted)throw new nx;e.deferred.ac.abort()}},resumeUpload:e=>{for(let t of eb(e??r.files)){let e=a.get(t);if(!e)throw"No upload found";e.deferred.ac=new AbortController,nt(c(t,e.presigned),{signal:e.deferred.ac.signal}).then(t=>{if("Success"===t._tag)return e.deferred.resolve(t.value);if("Interrupt"===t.cause._tag)throw new nm;throw eK(t.cause)}).catch(t=>{t instanceof nm||e.deferred.reject(t)})}},done:async e=>{let t=[];for(let n of eb(e??r.files)){let e=a.get(n);if(!e)throw"No upload found";t.push(e.deferred.promise)}let n=await Promise.all(t);return e?n[0]:n}}},routeRegistry:t}};var n5=n(12115),n6={uploadthing:"^7.2.0"};let n7="undefined"!=typeof window?n5.useInsertionEffect:()=>void 0;function n9(e){var t;let n=n5.useRef(n8);n7(()=>{n.current=e},[e]);let r=n5.useRef(null);return null!==(t=r.current)&&void 0!==t||(r.current=function(){return n.current.apply(this,arguments)}),r.current}function n8(){throw Error("INVALID_USEEVENT_INVOCATION: the callback from useEvent cannot be invoked before the component has mounted.")}let re=(e,t,n)=>{var r,a;let i=globalThis.__UPLOADTHING,{data:s}=function(e,t,n){let r=(0,n5.useRef)({}),a=(0,n5.useRef)(!1),i={error:void 0,data:void 0},[s,o]=(0,n5.useReducer)((e,t)=>{switch(t.type){case"loading":return{...i};case"fetched":return{...i,data:t.payload};case"error":return{...i,error:t.payload};default:return e}},i);return(0,n5.useEffect)(()=>{if(t)return a.current=!1,(async()=>{if(o({type:"loading"}),r.current[t]){o({type:"fetched",payload:r.current[t]});return}try{let n=await e(t,void 0);if(!n.ok)throw Error(n.statusText);let i=await nb(n);if(i instanceof Error)throw i;if(r.current[t]=i,a.current)return;o({type:"fetched",payload:i})}catch(e){if(a.current)return;o({type:"error",payload:e})}})(),()=>{a.current=!0}},[t]),s}(e,i?void 0:t.href);return null===(a=null!=i?i:s)||void 0===a?void 0:null===(r=a.find(e=>e.slug===n))||void 0===r?void 0:r.config},rt=function(e,t,n,r){var a;let i=null!==(a=null==r?void 0:r.uploadProgressGranularity)&&void 0!==a?a:"coarse",{uploadFiles:s,routeRegistry:o}=n3({fetch:n,url:e,package:"@uploadthing/react"}),[l,u]=(0,n5.useState)(!1),c=(0,n5.useRef)(0),d=(0,n5.useRef)(new Map);return{startUpload:n9(async function(){for(var e,n,a,o,l,p=arguments.length,f=Array(p),h=0;h<p;h++)f[h]=arguments[h];let g=null!==(a=await (null==r?void 0:null===(e=r.onBeforeUploadBegin)||void 0===e?void 0:e.call(r,f[0])))&&void 0!==a?a:f[0],v=f[1];u(!0),g.forEach(e=>d.current.set(e,0)),null==r||null===(n=r.onUploadProgress)||void 0===n||n.call(r,0);try{let e=await s(t,{signal:null==r?void 0:r.signal,headers:null==r?void 0:r.headers,files:g,onUploadProgress:e=>{if(!(null==r?void 0:r.onUploadProgress))return;d.current.set(e.file,e.progress);let t=0;d.current.forEach(e=>{t+=e});let n=nT(Math.min(100,t/d.current.size),i);n!==c.current&&(r.onUploadProgress(n),c.current=n)},onUploadBegin(e){let{file:t}=e;(null==r?void 0:r.onUploadBegin)&&r.onUploadBegin(t)},input:v});return await (null==r?void 0:null===(o=r.onClientUploadComplete)||void 0===o?void 0:o.call(r,e)),e}catch(t){let e;if(t instanceof nx)throw t;t instanceof nS?e=t:console.error("Something went wrong. Please contact UploadThing and provide the following cause:",(e=nO(t)).cause instanceof Error?e.cause.toString():e.cause),await (null==r?void 0:null===(l=r.onUploadError)||void 0===l?void 0:l.call(r,e))}finally{u(!1),d.current=new Map,c.current=0}}),isUploading:l,routeConfig:re(n,e,function(e,...t){return"function"==typeof e?e(...t):e}(t,o))}},rn=e=>{let t=n9(e);(0,n5.useEffect)(()=>{let e=new AbortController;return window.addEventListener("paste",t,{signal:e.signal}),()=>{e.abort()}},[t])};function rr(){return(0,s.jsx)("svg",{className:"z-10 block h-5 w-5 animate-spin align-middle text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 576 512",children:(0,s.jsx)("path",{fill:"currentColor",d:"M256 32C256 14.33 270.3 0 288 0C429.4 0 544 114.6 544 256C544 302.6 531.5 346.4 509.7 384C500.9 399.3 481.3 404.6 465.1 395.7C450.7 386.9 445.5 367.3 454.3 351.1C470.6 323.8 480 291 480 255.1C480 149.1 394 63.1 288 63.1C270.3 63.1 256 49.67 256 31.1V32z"})})}function ra(e){let{className:t,cn:n,...r}=e;return(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",strokeLinecap:"round",strokeLinejoin:"round",className:n("fill-none stroke-current stroke-2",t),...r,children:[(0,s.jsx)("circle",{cx:"12",cy:"12",r:"10"}),(0,s.jsx)("path",{d:"m4.9 4.9 14.2 14.2"})]})}function ri(e){var t,n,r,a,i,o,l,u,c,d,p,f,h,g,v,m;let{mode:x="auto",appendOnPaste:b=!1,cn:y=nB}=null!==(i=e.config)&&void 0!==i?i:{},w=(0,n5.useRef)(new AbortController),_=(0,n5.useRef)(null),[j,k]=(0,n5.useState)(null!==(o=e.__internal_upload_progress)&&void 0!==o?o:0),[E,S]=(0,n5.useState)([]),{startUpload:O,isUploading:C,routeConfig:R}=rt(nj(e.url),e.endpoint,null!==(l=e.fetch)&&void 0!==l?l:globalThis.fetch,{signal:w.current.signal,headers:e.headers,onClientUploadComplete:t=>{var n;_.current&&(_.current.value=""),S([]),null===(n=e.onClientUploadComplete)||void 0===n||n.call(e,t),k(0)},uploadProgressGranularity:e.uploadProgressGranularity,onUploadProgress:t=>{var n;k(t),null===(n=e.onUploadProgress)||void 0===n||n.call(e,t)},onUploadError:e.onUploadError,onUploadBegin:e.onUploadBegin,onBeforeUploadBegin:e.onBeforeUploadBegin}),{fileTypes:A,multiple:T}=nM(R),N=!!(null!==(u=e.__internal_button_disabled)&&void 0!==u?u:e.disabled),U=(()=>{let t="ready"===e.__internal_state||A.length>0;return e.__internal_state?e.__internal_state:N?"disabled":t?C?"uploading":"ready":"readying"})(),I=(0,n5.useCallback)(t=>{O(t,"input"in e?e.input:void 0).catch(t=>{if(t instanceof nx){var n;null===(n=e.onUploadAborted)||void 0===n||n.call(e)}else throw t})},[e,O]),M=(0,n5.useMemo)(()=>({type:"file",ref:_,multiple:T,accept:nN(A).join(", "),onChange:t=>{var n;if(!t.target.files)return;let r=Array.from(t.target.files);if(null===(n=e.onChange)||void 0===n||n.call(e,r),"manual"===x){S(r);return}I(r)},disabled:N,tabIndex:N?-1:0}),[e,N,A,x,T,I]);rn(t=>{if(!b||document.activeElement!==_.current)return;let n=nI(t);if(!n)return;let r=n;S(t=>{var a;return r=[...t,...n],null===(a=e.onChange)||void 0===a||a.call(e,r),r}),"auto"===x&&I(E)});let P=(0,n5.useMemo)(()=>({ready:"readying"!==U,isUploading:"uploading"===U,uploadProgress:j,fileTypes:A,files:E}),[A,E,U,j]);return(0,s.jsxs)("div",{className:y("flex flex-col items-center justify-center gap-1",e.className,nF(null===(t=e.appearance)||void 0===t?void 0:t.container,P)),style:{"--progress-width":"".concat(j,"%"),...nz(null===(n=e.appearance)||void 0===n?void 0:n.container,P)},"data-state":U,children:[(0,s.jsxs)("label",{className:y("group relative flex h-10 w-36 cursor-pointer items-center justify-center overflow-hidden rounded-md text-white after:transition-[width] after:duration-500 focus-within:ring-2 focus-within:ring-blue-600 focus-within:ring-offset-2","disabled:pointer-events-none","data-[state=disabled]:cursor-not-allowed data-[state=readying]:cursor-not-allowed","data-[state=disabled]:bg-blue-400 data-[state=ready]:bg-blue-600 data-[state=readying]:bg-blue-400 data-[state=uploading]:bg-blue-400","after:absolute after:left-0 after:h-full after:w-[var(--progress-width)] after:content-[''] data-[state=uploading]:after:bg-blue-600",nF(null===(r=e.appearance)||void 0===r?void 0:r.button,P)),style:nz(null===(a=e.appearance)||void 0===a?void 0:a.button,P),"data-state":U,"data-ut-element":"button",onClick:e=>{if("uploading"===U){e.preventDefault(),e.stopPropagation(),w.current.abort(),w.current=new AbortController;return}"manual"===x&&E.length>0&&(e.preventDefault(),e.stopPropagation(),I(E))},children:[(0,s.jsx)("input",{...M,className:"sr-only"}),(()=>{var t;let n=n$(null===(t=e.content)||void 0===t?void 0:t.button,P);if(n)return n;switch(U){case"readying":return"Loading...";case"uploading":if(j>=100)return(0,s.jsx)(rr,{});return(0,s.jsxs)("span",{className:"z-50",children:[(0,s.jsxs)("span",{className:"block group-hover:hidden",children:[Math.round(j),"%"]}),(0,s.jsx)(ra,{cn:y,className:"hidden size-4 group-hover:block"})]});default:if("manual"===x&&E.length>0)return"Upload ".concat(E.length," file").concat(1===E.length?"":"s");return"Choose File".concat(M.multiple?"(s)":"")}})()]}),"manual"===x&&E.length>0?(0,s.jsx)("button",{onClick:()=>{var t;S([]),_.current&&(_.current.value=""),null===(t=e.onChange)||void 0===t||t.call(e,[])},className:y("h-[1.25rem] cursor-pointer rounded border-none bg-transparent text-gray-500 transition-colors hover:bg-slate-200 hover:text-gray-600",nF(null===(c=e.appearance)||void 0===c?void 0:c.clearBtn,P)),style:nz(null===(d=e.appearance)||void 0===d?void 0:d.clearBtn,P),"data-state":U,"data-ut-element":"clear-btn",children:null!==(f=n$(null===(p=e.content)||void 0===p?void 0:p.clearBtn,P))&&void 0!==f?f:"Clear"}):(0,s.jsx)("div",{className:y("h-[1.25rem] text-xs leading-5 text-gray-600",nF(null===(h=e.appearance)||void 0===h?void 0:h.allowedContent,P)),style:nz(null===(g=e.appearance)||void 0===g?void 0:g.allowedContent,P),"data-state":U,"data-ut-element":"allowed-content",children:null!==(m=n$(null===(v=e.content)||void 0===v?void 0:v.allowedContent,P))&&void 0!==m?m:nD(R)})]})}var rs=n(39249),ro=new Map([["aac","audio/aac"],["abw","application/x-abiword"],["arc","application/x-freearc"],["avif","image/avif"],["avi","video/x-msvideo"],["azw","application/vnd.amazon.ebook"],["bin","application/octet-stream"],["bmp","image/bmp"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["cda","application/x-cdf"],["csh","application/x-csh"],["css","text/css"],["csv","text/csv"],["doc","application/msword"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["eot","application/vnd.ms-fontobject"],["epub","application/epub+zip"],["gz","application/gzip"],["gif","image/gif"],["heic","image/heic"],["heif","image/heif"],["htm","text/html"],["html","text/html"],["ico","image/vnd.microsoft.icon"],["ics","text/calendar"],["jar","application/java-archive"],["jpeg","image/jpeg"],["jpg","image/jpeg"],["js","text/javascript"],["json","application/json"],["jsonld","application/ld+json"],["mid","audio/midi"],["midi","audio/midi"],["mjs","text/javascript"],["mp3","audio/mpeg"],["mp4","video/mp4"],["mpeg","video/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["opus","audio/opus"],["otf","font/otf"],["png","image/png"],["pdf","application/pdf"],["php","application/x-httpd-php"],["ppt","application/vnd.ms-powerpoint"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["rar","application/vnd.rar"],["rtf","application/rtf"],["sh","application/x-sh"],["svg","image/svg+xml"],["swf","application/x-shockwave-flash"],["tar","application/x-tar"],["tif","image/tiff"],["tiff","image/tiff"],["ts","video/mp2t"],["ttf","font/ttf"],["txt","text/plain"],["vsd","application/vnd.visio"],["wav","audio/wav"],["weba","audio/webm"],["webm","video/webm"],["webp","image/webp"],["woff","font/woff"],["woff2","font/woff2"],["xhtml","application/xhtml+xml"],["xls","application/vnd.ms-excel"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xml","application/xml"],["xul","application/vnd.mozilla.xul+xml"],["zip","application/zip"],["7z","application/x-7z-compressed"],["mkv","video/x-matroska"],["mov","video/quicktime"],["msg","application/vnd.ms-outlook"]]);function rl(e,t){var n=function(e){var t=e.name;if(t&&-1!==t.lastIndexOf(".")&&!e.type){var n=t.split(".").pop().toLowerCase(),r=ro.get(n);r&&Object.defineProperty(e,"type",{value:r,writable:!1,configurable:!1,enumerable:!0})}return e}(e);if("string"!=typeof n.path){var r=e.webkitRelativePath;Object.defineProperty(n,"path",{value:"string"==typeof t?t:"string"==typeof r&&r.length>0?r:e.name,writable:!1,configurable:!1,enumerable:!0})}return n}var ru=[".DS_Store","Thumbs.db"];function rc(e){return(0,rs.sH)(this,void 0,void 0,function(){return(0,rs.YH)(this,function(t){var n;if(rd(e)&&rd(e.dataTransfer))return[2,function(e,t){return(0,rs.sH)(this,void 0,void 0,function(){var n;return(0,rs.YH)(this,function(r){switch(r.label){case 0:if(!e.items)return[3,2];if(n=rf(e.items).filter(function(e){return"file"===e.kind}),"drop"!==t)return[2,n];return[4,Promise.all(n.map(rh))];case 1:return[2,rp(function e(t){return t.reduce(function(t,n){return(0,rs.fX)((0,rs.fX)([],(0,rs.zs)(t),!1),(0,rs.zs)(Array.isArray(n)?e(n):[n]),!1)},[])}(r.sent()))];case 2:return[2,rp(rf(e.files).map(function(e){return rl(e)}))]}})})}(e.dataTransfer,e.type)];if(rd(n=e)&&rd(n.target))return[2,rf(e.target.files).map(function(e){return rl(e)})];return Array.isArray(e)&&e.every(function(e){return"getFile"in e&&"function"==typeof e.getFile})?[2,function(e){return(0,rs.sH)(this,void 0,void 0,function(){return(0,rs.YH)(this,function(t){switch(t.label){case 0:return[4,Promise.all(e.map(function(e){return e.getFile()}))];case 1:return[2,t.sent().map(function(e){return rl(e)})]}})})}(e)]:[2,[]]})})}function rd(e){return"object"==typeof e&&null!==e}function rp(e){return e.filter(function(e){return -1===ru.indexOf(e.name)})}function rf(e){if(null===e)return[];for(var t=[],n=0;n<e.length;n++){var r=e[n];t.push(r)}return t}function rh(e){if("function"!=typeof e.webkitGetAsEntry)return rg(e);var t=e.webkitGetAsEntry();return t&&t.isDirectory?rm(t):rg(e)}function rg(e){var t=e.getAsFile();return t?Promise.resolve(rl(t)):Promise.reject("".concat(e," is not a File"))}function rv(e){return(0,rs.sH)(this,void 0,void 0,function(){return(0,rs.YH)(this,function(t){return[2,e.isDirectory?rm(e):function(e){return(0,rs.sH)(this,void 0,void 0,function(){return(0,rs.YH)(this,function(t){return[2,new Promise(function(t,n){e.file(function(n){t(rl(n,e.fullPath))},function(e){n(e)})})]})})}(e)]})})}function rm(e){var t=e.createReader();return new Promise(function(e,n){var r=[];!function a(){var i=this;t.readEntries(function(t){return(0,rs.sH)(i,void 0,void 0,function(){var i;return(0,rs.YH)(this,function(s){switch(s.label){case 0:if(t.length)return[3,5];s.label=1;case 1:return s.trys.push([1,3,,4]),[4,Promise.all(r)];case 2:return e(s.sent()),[3,4];case 3:return n(s.sent()),[3,4];case 4:return[3,6];case 5:i=Promise.all(t.map(rv)),r.push(i),a(),s.label=6;case 6:return[2]}})})},function(e){n(e)})}()})}function rx(e){var t,n,r,a,i,o,l,u,c,d,p,f,h,g,v,m,x,b,y,w;let{mode:_="manual",appendOnPaste:j=!1,cn:k=nB}=null!==(g=e.config)&&void 0!==g?g:{},E=(0,n5.useRef)(new AbortController),[S,O]=(0,n5.useState)([]),[C,R]=(0,n5.useState)(null!==(v=e.__internal_upload_progress)&&void 0!==v?v:0),{startUpload:A,isUploading:T,routeConfig:N}=rt(nj(e.url),e.endpoint,null!==(m=e.fetch)&&void 0!==m?m:globalThis.fetch,{signal:E.current.signal,headers:e.headers,onClientUploadComplete:t=>{var n;O([]),null===(n=e.onClientUploadComplete)||void 0===n||n.call(e,t),R(0)},uploadProgressGranularity:e.uploadProgressGranularity,onUploadProgress:t=>{var n;R(t),null===(n=e.onUploadProgress)||void 0===n||n.call(e,t)},onUploadError:e.onUploadError,onUploadBegin:e.onUploadBegin,onBeforeUploadBegin:e.onBeforeUploadBegin}),{fileTypes:U,multiple:I}=nM(N),M=!!(null!==(x=e.__internal_dropzone_disabled)&&void 0!==x?x:e.disabled),P=(()=>{var t;let n=null!==(t=e.__internal_ready)&&void 0!==t?t:"ready"===e.__internal_state||U.length>0;return e.__internal_state?e.__internal_state:M?"disabled":n?T?"uploading":"ready":"readying"})(),L=(0,n5.useCallback)(t=>{A(t,"input"in e?e.input:void 0).catch(t=>{if(t instanceof nx){var n;null===(n=e.onUploadAborted)||void 0===n||n.call(e)}else throw t})},[e,A]),{getRootProps:D,getInputProps:F,isDragActive:z,rootRef:$}=function(e){let{accept:t,disabled:n=!1,maxSize:r=Number.POSITIVE_INFINITY,minSize:a=0,multiple:i=!0,maxFiles:s=0,onDrop:o}=e,l=(0,n5.useMemo)(()=>(function(e){if(nJ(e))return Object.entries(e).reduce((e,[t,n])=>[...e,t,...n],[]).filter(e=>"audio/*"===e||"video/*"===e||"image/*"===e||"text/*"===e||/\w+\/[-+.\w]+/g.test(e)||/^.*\.[\w]+$/.test(e)).join(",")})(t),[t]),u=(0,n5.useRef)(null),c=(0,n5.useRef)(null),d=(0,n5.useRef)([]),[p,f]=(0,n5.useReducer)(nY,nW);(0,n5.useEffect)(()=>{let e=new AbortController;return window.addEventListener("focus",()=>{p.isFileDialogActive&&setTimeout(()=>{if(c.current){let{files:e}=c.current;(null==e?void 0:e.length)||f({type:"closeDialog"})}},300)},{signal:e.signal}),()=>{e.abort()}},[p.isFileDialogActive]),(0,n5.useEffect)(()=>{let e=new AbortController;return document.addEventListener("dragover",e=>e.preventDefault(),{capture:!1,signal:e.signal}),document.addEventListener("drop",e=>{var t;null!==(t=u.current)&&void 0!==t&&t.contains(e.target)||(e.preventDefault(),d.current=[])},{capture:!1,signal:e.signal}),()=>{e.abort()}},[]);let h=(0,n5.useCallback)(e=>{e.preventDefault(),e.persist(),d.current=[...d.current,e.target],nq(e)&&Promise.resolve(rc(e)).then(t=>{if(e.isPropagationStopped())return;let n=t.length,o=n>0&&function({files:e,accept:t,minSize:n,maxSize:r,multiple:a,maxFiles:i}){return!!nG(e,a,i)&&e.every(e=>nH(e,t)&&nV(e,n,r))}({files:t,accept:l,minSize:a,maxSize:r,multiple:i,maxFiles:s});f({type:"setDraggedFiles",payload:{isDragAccept:o,isDragReject:n>0&&!o,isDragActive:!0}})}).catch(nk)},[l,s,r,a,i]),g=(0,n5.useCallback)(e=>{if(e.preventDefault(),e.persist(),nq(e))try{e.dataTransfer.dropEffect="copy"}catch(e){}return!1},[]),v=(0,n5.useCallback)(e=>{e.preventDefault(),e.persist();let t=d.current.filter(e=>{var t;return null===(t=u.current)||void 0===t?void 0:t.contains(e)}),n=t.indexOf(e.target);-1!==n&&t.splice(n,1),d.current=t,t.length>0||f({type:"setDraggedFiles",payload:{isDragActive:!1,isDragAccept:!1,isDragReject:!1}})},[]),m=(0,n5.useCallback)(e=>{let t=[];e.forEach(e=>{let n=nH(e,l),i=nV(e,a,r);n&&i&&t.push(e)}),nG(t,i,s)||t.splice(0),f({type:"setFiles",payload:{acceptedFiles:t}}),o(t)},[l,s,r,a,i,o]),x=(0,n5.useCallback)(e=>{e.preventDefault(),e.persist(),d.current=[],nq(e)&&Promise.resolve(rc(e)).then(t=>{e.isPropagationStopped()||m(t)}).catch(nk),f({type:"reset"})},[m]),b=(0,n5.useCallback)(()=>{c.current&&(f({type:"openDialog"}),c.current.value="",c.current.click())},[]),y=(0,n5.useCallback)(e=>{var t;(null===(t=u.current)||void 0===t?void 0:t.isEqualNode(e.target))&&("key"in e&&(" "===e.key||"Enter"===e.key)||"keyCode"in e&&(32===e.keyCode||13===e.keyCode))&&(e.preventDefault(),b())},[b]),w=(0,n5.useCallback)(e=>{e.stopPropagation(),p.isFileDialogActive&&e.preventDefault()},[p.isFileDialogActive]),_=(0,n5.useCallback)(()=>f({type:"focus"}),[]),j=(0,n5.useCallback)(()=>f({type:"blur"}),[]),k=(0,n5.useCallback)(()=>{!function(e=window.navigator.userAgent){return e.includes("MSIE ")||e.includes("Trident/")||e.includes("Edge/")}()?b():setTimeout(b,0)},[b]),E=(0,n5.useMemo)(()=>()=>({ref:u,role:"presentation",...n?{}:{tabIndex:0,onKeyDown:y,onFocus:_,onBlur:j,onClick:k,onDragEnter:h,onDragOver:g,onDragLeave:v,onDrop:x}}),[n,j,k,h,v,g,x,_,y]),S=(0,n5.useMemo)(()=>()=>({ref:c,type:"file",style:{display:"none"},accept:l,multiple:i,tabIndex:-1,...n?{}:{onChange:x,onClick:w}}),[l,i,x,w,n]);return{...p,getRootProps:E,getInputProps:S,rootRef:u}}({onDrop:(0,n5.useCallback)(t=>{var n,r;null===(n=e.onDrop)||void 0===n||n.call(e,t),null===(r=e.onChange)||void 0===r||r.call(e,t),O(t),"auto"===_&&L(t)},[e,_,L]),multiple:I,accept:nU(U),disabled:M});rn(t=>{var n;if(!j||document.activeElement!==$.current)return;let r=nI(t);if(!(null==r?void 0:r.length))return;let a=r;O(t=>{var n;return a=[...t,...r],null===(n=e.onChange)||void 0===n||n.call(e,a),a}),null===(n=e.onChange)||void 0===n||n.call(e,a),"auto"===_&&L(a)});let B=(0,n5.useMemo)(()=>({ready:"readying"!==P,isUploading:"uploading"===P,uploadProgress:C,fileTypes:U,files:S,isDragActive:z}),[U,S,P,C,z]);return(0,s.jsxs)("div",{className:k("mt-2 flex flex-col items-center justify-center rounded-lg border border-dashed border-gray-900/25 px-6 py-10 text-center",z&&"bg-blue-600/10",e.className,nF(null===(t=e.appearance)||void 0===t?void 0:t.container,B)),...D(),style:nz(null===(n=e.appearance)||void 0===n?void 0:n.container,B),"data-state":P,children:[null!==(b=n$(null===(r=e.content)||void 0===r?void 0:r.uploadIcon,B))&&void 0!==b?b:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",className:k("mx-auto block h-12 w-12 align-middle text-gray-400",nF(null===(a=e.appearance)||void 0===a?void 0:a.uploadIcon,B)),style:nz(null===(i=e.appearance)||void 0===i?void 0:i.uploadIcon,B),"data-ut-element":"upload-icon","data-state":P,children:(0,s.jsx)("path",{fill:"currentColor",fillRule:"evenodd",d:"M5.5 17a4.5 4.5 0 0 1-1.44-8.765a4.5 4.5 0 0 1 8.302-3.046a3.5 3.5 0 0 1 4.504 4.272A4 4 0 0 1 15 17H5.5Zm3.75-2.75a.75.75 0 0 0 1.5 0V9.66l1.95 2.1a.75.75 0 1 0 1.1-1.02l-3.25-3.5a.75.75 0 0 0-1.1 0l-3.25 3.5a.75.75 0 1 0 1.1 1.02l1.95-2.1v4.59Z",clipRule:"evenodd"})}),(0,s.jsxs)("label",{className:k("relative mt-4 flex w-64 cursor-pointer items-center justify-center text-sm font-semibold leading-6 text-gray-600 focus-within:outline-none focus-within:ring-2 focus-within:ring-blue-600 focus-within:ring-offset-2 hover:text-blue-500","ready"===P?"text-blue-600":"text-gray-500",nF(null===(o=e.appearance)||void 0===o?void 0:o.label,B)),style:nz(null===(l=e.appearance)||void 0===l?void 0:l.label,B),"data-ut-element":"label","data-state":P,children:[(0,s.jsx)("input",{className:"sr-only",...F()}),null!==(y=n$(null===(u=e.content)||void 0===u?void 0:u.label,B))&&void 0!==y?y:"ready"===P?"Choose ".concat(I?"file(s)":"a file"," or drag and drop"):"Loading..."]}),(0,s.jsx)("div",{className:k("m-0 h-[1.25rem] text-xs leading-5 text-gray-600",nF(null===(c=e.appearance)||void 0===c?void 0:c.allowedContent,B)),style:nz(null===(d=e.appearance)||void 0===d?void 0:d.allowedContent,B),"data-ut-element":"allowed-content","data-state":P,children:null!==(w=n$(null===(p=e.content)||void 0===p?void 0:p.allowedContent,B))&&void 0!==w?w:nD(N)}),(0,s.jsx)("button",{className:k("group relative mt-4 flex h-10 w-36 items-center justify-center overflow-hidden rounded-md border-none text-base text-white","after:absolute after:left-0 after:h-full after:w-[var(--progress-width)] after:bg-blue-600 after:transition-[width] after:duration-500 after:content-['']","focus-within:ring-2 focus-within:ring-blue-600 focus-within:ring-offset-2","disabled:pointer-events-none","data-[state=disabled]:cursor-not-allowed data-[state=readying]:cursor-not-allowed","data-[state=disabled]:bg-blue-400 data-[state=ready]:bg-blue-600 data-[state=readying]:bg-blue-400 data-[state=uploading]:bg-blue-400",nF(null===(f=e.appearance)||void 0===f?void 0:f.button,B)),style:{"--progress-width":"".concat(C,"%"),...nz(null===(h=e.appearance)||void 0===h?void 0:h.button,B)},onClick:e=>{if("uploading"===P){e.preventDefault(),e.stopPropagation(),E.current.abort(),E.current=new AbortController;return}"manual"===_&&S.length>0&&(e.preventDefault(),e.stopPropagation(),L(S))},"data-ut-element":"button","data-state":P,type:"button",disabled:0===S.length||"disabled"===P,children:(()=>{var t;let n=n$(null===(t=e.content)||void 0===t?void 0:t.button,B);if(n)return n;switch(P){case"readying":return"Loading...";case"uploading":if(C>=100)return(0,s.jsx)(rr,{});return(0,s.jsxs)("span",{className:"z-50",children:[(0,s.jsxs)("span",{className:"block group-hover:hidden",children:[Math.round(C),"%"]}),(0,s.jsx)(ra,{cn:k,className:"hidden size-4 group-hover:block"})]});default:if("manual"===_&&S.length>0)return"Upload ".concat(S.length," file").concat(1===S.length?"":"s");return"Choose File".concat(I?"(s)":"")}})()})]})}let rb=e=>{nw("@uploadthing/react",n6.uploadthing,n4);let t=nj(e?.url),n=e?.fetch??globalThis.fetch;return e=>(0,s.jsx)(ri,{...e,url:t,fetch:n})},ry=e=>{nw("@uploadthing/react",n6.uploadthing,n4);let t=nj(e?.url),n=e?.fetch??globalThis.fetch;return e=>(0,s.jsx)(rx,{...e,url:t,fetch:n})}},87489:(e,t,n)=>{n.d(t,{b:()=>u});var r=n(12115),a=n(63655),i=n(95155),s="horizontal",o=["horizontal","vertical"],l=r.forwardRef((e,t)=>{var n;let{decorative:r,orientation:l=s,...u}=e,c=(n=l,o.includes(n))?l:s;return(0,i.jsx)(a.sG.div,{"data-orientation":c,...r?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...u,ref:t})});l.displayName="Separator";var u=l}}]);