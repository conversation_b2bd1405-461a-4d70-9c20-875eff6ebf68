{"version": 3, "file": "RuntimeFlagsPatch.d.ts", "sourceRoot": "", "sources": ["../../src/RuntimeFlagsPatch.ts"], "names": [], "mappings": "AAKA,OAAO,KAAK,KAAK,YAAY,MAAM,mBAAmB,CAAA;AAEtD;;;GAGG;AACH,MAAM,MAAM,iBAAiB,GAAG,MAAM,GAAG;IACvC,QAAQ,CAAC,iBAAiB,EAAE,OAAO,MAAM,CAAA;CAC1C,CAAA;AAED;;;;;GAKG;AACH,eAAO,MAAM,KAAK,EAAE,iBAAkC,CAAA;AAEtD;;;GAGG;AACH,eAAO,MAAM,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,KAAK,iBAAiC,CAAA;AAEzF;;;;;GAKG;AACH,eAAO,MAAM,MAAM,EAAE,CAAC,IAAI,EAAE,YAAY,CAAC,WAAW,KAAK,iBAAmC,CAAA;AAE5F;;;;;GAKG;AACH,eAAO,MAAM,OAAO,EAAE,CAAC,IAAI,EAAE,YAAY,CAAC,WAAW,KAAK,iBAAoC,CAAA;AAE9F;;;;;GAKG;AACH,eAAO,MAAM,OAAO,EAAE,CAAC,KAAK,EAAE,iBAAiB,KAAK,OAA0B,CAAA;AAE9E;;;;;;GAMG;AACH,eAAO,MAAM,QAAQ,EAAE;IACrB;;;;;;OAMG;IACH,CAAC,IAAI,EAAE,iBAAiB,GAAG,CAAC,IAAI,EAAE,iBAAiB,KAAK,OAAO,CAAA;IAC/D;;;;;;OAMG;IACH,CAAC,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,iBAAiB,GAAG,OAAO,CAAA;CACxC,CAAA;AAErB;;;;;;GAMG;AACH,eAAO,MAAM,SAAS,EAAE;IACtB;;;;;;OAMG;IACH,CAAC,IAAI,EAAE,YAAY,CAAC,WAAW,GAAG,CAAC,IAAI,EAAE,iBAAiB,KAAK,OAAO,CAAA;IACtE;;;;;;OAMG;IACH,CAAC,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,YAAY,CAAC,WAAW,GAAG,OAAO,CAAA;CAC9C,CAAA;AAEtB;;;;;;GAMG;AACH,eAAO,MAAM,UAAU,EAAE;IACvB;;;;;;OAMG;IACH,CAAC,IAAI,EAAE,YAAY,CAAC,WAAW,GAAG,CAAC,IAAI,EAAE,iBAAiB,KAAK,OAAO,CAAA;IACtE;;;;;;OAMG;IACH,CAAC,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,YAAY,CAAC,WAAW,GAAG,OAAO,CAAA;CAC7C,CAAA;AAEvB;;;;;;GAMG;AACH,eAAO,MAAM,QAAQ,EAAE;IACrB;;;;;;OAMG;IACH,CAAC,IAAI,EAAE,iBAAiB,GAAG,CAAC,IAAI,EAAE,iBAAiB,KAAK,OAAO,CAAA;IAC/D;;;;;;OAMG;IACH,CAAC,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,iBAAiB,GAAG,OAAO,CAAA;CACxC,CAAA;AAErB;;;;;;GAMG;AACH,eAAO,MAAM,OAAO,EAAE;IACpB;;;;;;OAMG;IACH,CAAC,IAAI,EAAE,iBAAiB,GAAG,CAAC,IAAI,EAAE,iBAAiB,KAAK,iBAAiB,CAAA;IACzE;;;;;;OAMG;IACH,CAAC,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,iBAAiB,GAAG,iBAAiB,CAAA;CACnD,CAAA;AAEpB;;;;;;GAMG;AACH,eAAO,MAAM,IAAI,EAAE;IACjB;;;;;;OAMG;IACH,CAAC,IAAI,EAAE,iBAAiB,GAAG,CAAC,IAAI,EAAE,iBAAiB,KAAK,iBAAiB,CAAA;IACzE;;;;;;OAMG;IACH,CAAC,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,iBAAiB,GAAG,iBAAiB,CAAA;CACtD,CAAA;AAEjB;;;;;;GAMG;AACH,eAAO,MAAM,MAAM,EAAE;IACnB;;;;;;OAMG;IACH,CAAC,IAAI,EAAE,iBAAiB,GAAG,CAAC,IAAI,EAAE,iBAAiB,KAAK,iBAAiB,CAAA;IACzE;;;;;;OAMG;IACH,CAAC,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,iBAAiB,GAAG,iBAAiB,CAAA;CACpD,CAAA;AAEnB;;;;;;GAMG;AACH,eAAO,MAAM,OAAO,EAAE;IACpB;;;;;;OAMG;IACH,CAAC,IAAI,EAAE,YAAY,CAAC,WAAW,GAAG,CAAC,IAAI,EAAE,iBAAiB,KAAK,iBAAiB,CAAA;IAChF;;;;;;OAMG;IACH,CAAC,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,YAAY,CAAC,WAAW,GAAG,iBAAiB,CAAA;CAC1D,CAAA;AAEpB;;;;;;GAMG;AACH,eAAO,MAAM,OAAO,EAAE,CAAC,KAAK,EAAE,iBAAiB,KAAK,iBAAoC,CAAA;AAExF;;;;;;GAMG;AACH,eAAO,MAAM,UAAU,EAAE,CAAC,IAAI,EAAE,iBAAiB,KAAK,WAAW,CAAC,YAAY,CAAC,WAAW,CAA2B,CAAA;AAErH;;;;;;GAMG;AACH,eAAO,MAAM,WAAW,EAAE,CAAC,IAAI,EAAE,iBAAiB,KAAK,WAAW,CAAC,YAAY,CAAC,WAAW,CAA4B,CAAA;AAEvH;;;;;GAKG;AACH,eAAO,MAAM,MAAM,EAAE,CAAC,IAAI,EAAE,iBAAiB,KAAK,MAAiC,CAAA"}