{"version": 3, "file": "option.js", "names": ["Equal", "_interopRequireWildcard", "require", "Hash", "_Inspectable", "_Predicate", "_effectable", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "TypeId", "Symbol", "for", "CommonProto", "EffectPrototype", "_A", "_", "NodeInspectSymbol", "toJSON", "toString", "format", "SomeProto", "assign", "create", "_tag", "_op", "symbol", "that", "isOption", "isSome", "equals", "value", "cached", "combine", "hash", "_id", "NoneHash", "NoneProto", "isNone", "input", "hasProperty", "exports", "fa", "none", "some"], "sources": ["../../../src/internal/option.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAIA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,IAAA,GAAAF,uBAAA,CAAAC,OAAA;AACA,IAAAE,YAAA,GAAAF,OAAA;AAEA,IAAAG,UAAA,GAAAH,OAAA;AACA,IAAAI,WAAA,GAAAJ,OAAA;AAAiD,SAAAK,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAP,wBAAAO,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AATjD;;;;AAWA,MAAMW,MAAM,gBAAkBC,MAAM,CAACC,GAAG,CAAC,eAAe,CAAkB;AAE1E,MAAMC,WAAW,GAAG;EAClB,GAAGC,2BAAe;EAClB,CAACJ,MAAM,GAAG;IACRK,EAAE,EAAGC,CAAQ,IAAKA;GACnB;EACD,CAACC,8BAAiB,IAAC;IACjB,OAAO,IAAI,CAACC,MAAM,EAAE;EACtB,CAAC;EACDC,QAAQA,CAAA;IACN,OAAO,IAAAC,mBAAM,EAAC,IAAI,CAACF,MAAM,EAAE,CAAC;EAC9B;CACD;AAED,MAAMG,SAAS,gBAAGnB,MAAM,CAACoB,MAAM,eAACpB,MAAM,CAACqB,MAAM,CAACV,WAAW,CAAC,EAAE;EAC1DW,IAAI,EAAE,MAAM;EACZC,GAAG,EAAE,MAAM;EACX,CAAC1C,KAAK,CAAC2C,MAAM,EAA2BC,IAAa;IACnD,OAAOC,QAAQ,CAACD,IAAI,CAAC,IAAIE,MAAM,CAACF,IAAI,CAAC,IAAI5C,KAAK,CAAC+C,MAAM,CAAC,IAAI,CAACC,KAAK,EAAEJ,IAAI,CAACI,KAAK,CAAC;EAC/E,CAAC;EACD,CAAC7C,IAAI,CAACwC,MAAM,IAAC;IACX,OAAOxC,IAAI,CAAC8C,MAAM,CAAC,IAAI,EAAE9C,IAAI,CAAC+C,OAAO,CAAC/C,IAAI,CAACgD,IAAI,CAAC,IAAI,CAACV,IAAI,CAAC,CAAC,CAACtC,IAAI,CAACgD,IAAI,CAAC,IAAI,CAACH,KAAK,CAAC,CAAC,CAAC;EACrF,CAAC;EACDb,MAAMA,CAAA;IACJ,OAAO;MACLiB,GAAG,EAAE,QAAQ;MACbX,IAAI,EAAE,IAAI,CAACA,IAAI;MACfO,KAAK,EAAE,IAAAb,mBAAM,EAAC,IAAI,CAACa,KAAK;KACzB;EACH;CACD,CAAC;AAEF,MAAMK,QAAQ,gBAAGlD,IAAI,CAACgD,IAAI,CAAC,MAAM,CAAC;AAClC,MAAMG,SAAS,gBAAGnC,MAAM,CAACoB,MAAM,eAACpB,MAAM,CAACqB,MAAM,CAACV,WAAW,CAAC,EAAE;EAC1DW,IAAI,EAAE,MAAM;EACZC,GAAG,EAAE,MAAM;EACX,CAAC1C,KAAK,CAAC2C,MAAM,EAA2BC,IAAa;IACnD,OAAOC,QAAQ,CAACD,IAAI,CAAC,IAAIW,MAAM,CAACX,IAAI,CAAC;EACvC,CAAC;EACD,CAACzC,IAAI,CAACwC,MAAM,IAAC;IACX,OAAOU,QAAQ;EACjB,CAAC;EACDlB,MAAMA,CAAA;IACJ,OAAO;MACLiB,GAAG,EAAE,QAAQ;MACbX,IAAI,EAAE,IAAI,CAACA;KACZ;EACH;CACD,CAAC;AAEF;AACO,MAAMI,QAAQ,GAAIW,KAAc,IAAsC,IAAAC,sBAAW,EAACD,KAAK,EAAE7B,MAAM,CAAC;AAEvG;AAAA+B,OAAA,CAAAb,QAAA,GAAAA,QAAA;AACO,MAAMU,MAAM,GAAOI,EAAoB,IAA2BA,EAAE,CAAClB,IAAI,KAAK,MAAM;AAE3F;AAAAiB,OAAA,CAAAH,MAAA,GAAAA,MAAA;AACO,MAAMT,MAAM,GAAOa,EAAoB,IAA2BA,EAAE,CAAClB,IAAI,KAAK,MAAM;AAE3F;AAAAiB,OAAA,CAAAZ,MAAA,GAAAA,MAAA;AACO,MAAMc,IAAI,GAAAF,OAAA,CAAAE,IAAA,gBAAyBzC,MAAM,CAACqB,MAAM,CAACc,SAAS,CAAC;AAElE;AACO,MAAMO,IAAI,GAAOb,KAAQ,IAAsB;EACpD,MAAM9B,CAAC,GAAGC,MAAM,CAACqB,MAAM,CAACF,SAAS,CAAC;EAClCpB,CAAC,CAAC8B,KAAK,GAAGA,KAAK;EACf,OAAO9B,CAAC;AACV,CAAC;AAAAwC,OAAA,CAAAG,IAAA,GAAAA,IAAA", "ignoreList": []}