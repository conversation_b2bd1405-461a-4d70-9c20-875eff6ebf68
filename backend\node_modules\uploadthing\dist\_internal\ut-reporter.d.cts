import * as Micro from 'effect/Micro';
import { UploadThingError, FetchContext, MaybePromise } from '@uploadthing/shared';
import { UTEvents } from './types.cjs';

type UTReporter = <TEvent extends keyof UTEvents>(type: TEvent, payload: UTEvents[TEvent]["in"]) => Micro.Micro<UTEvents[TEvent]["out"], UploadThingError, FetchContext>;
/**
 * Creates a "client" for reporting events to the UploadThing server via the user's API endpoint.
 * Events are handled in "./handler.ts starting at L112"
 */
declare const createUTReporter: (cfg: {
    url: URL;
    endpoint: string;
    package?: string | undefined;
    headers: HeadersInit | (() => MaybePromise<HeadersInit>) | undefined;
}) => UTReporter;

export { createUTReporter };
export type { UTReporter };
