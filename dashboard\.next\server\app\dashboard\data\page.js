(()=>{var e={};e.id=5538,e.ids=[5538],e.modules={1708:e=>{"use strict";e.exports=require("node:process")},2547:(e,t,s)=>{"use strict";s.d(t,{CampaignsDataTable:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call CampaignsDataTable() from the server but CampaignsDataTable is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\components\\data\\campaigns-data-table.tsx","CampaignsDataTable")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5486:e=>{"use strict";e.exports=require("bcrypt")},7066:e=>{"use strict";e.exports=require("node:tty")},7331:(e,t,s)=>{"use strict";s.d(t,{QAPairsDataTable:()=>p});var r=s(60687),a=s(43210),n=s(96752),i=s(55629),o=s(24934),l=s(41862),c=s(93661),d=s(63143),u=s(96362),h=s(59821);function p(){let[e,t]=(0,a.useState)([]),[s,p]=(0,a.useState)(!0),[m,x]=(0,a.useState)(null);return s?(0,r.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,r.jsx)(l.A,{className:"h-8 w-8 animate-spin text-primary"})}):m?(0,r.jsx)("div",{className:"text-center py-8 text-red-500",children:(0,r.jsx)("p",{children:m})}):(0,r.jsx)("div",{className:"rounded-md border",children:(0,r.jsxs)(n.XI,{children:[(0,r.jsx)(n.A0,{children:(0,r.jsxs)(n.Hj,{children:[(0,r.jsx)(n.nd,{children:"Question"}),(0,r.jsx)(n.nd,{children:"Answer"}),(0,r.jsx)(n.nd,{children:"Category"}),(0,r.jsx)(n.nd,{className:"w-[80px]"})]})}),(0,r.jsx)(n.BF,{children:0===e.length?(0,r.jsx)(n.Hj,{children:(0,r.jsx)(n.nA,{colSpan:4,className:"text-center py-8 text-muted-foreground",children:"No QA pairs found"})}):e.map(e=>(0,r.jsxs)(n.Hj,{children:[(0,r.jsx)(n.nA,{className:"font-medium max-w-[300px] truncate",children:e.question}),(0,r.jsx)(n.nA,{className:"max-w-[400px] truncate",children:e.answer}),(0,r.jsx)(n.nA,{children:(0,r.jsx)(h.E,{variant:"outline",children:e.category})}),(0,r.jsx)(n.nA,{children:(0,r.jsxs)(i.rI,{children:[(0,r.jsx)(i.ty,{asChild:!0,children:(0,r.jsxs)(o.$,{variant:"ghost",className:"h-8 w-8 p-0",children:[(0,r.jsx)("span",{className:"sr-only",children:"Open menu"}),(0,r.jsx)(c.A,{className:"h-4 w-4"})]})}),(0,r.jsxs)(i.SQ,{align:"end",children:[(0,r.jsxs)(i._2,{children:[(0,r.jsx)(d.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Edit"})]}),(0,r.jsxs)(i._2,{className:"text-red-600",children:[(0,r.jsx)(u.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Delete"})]})]})]})})]},e.id))})]})})}s(59556)},10656:(e,t,s)=>{"use strict";s.d(t,{MessagesDataTable:()=>c});var r=s(60687),a=s(43210),n=s(96752),i=s(59821),o=s(41862);s(59556);var l=s(78201);function c(){let[e,t]=(0,a.useState)([]),[s,c]=(0,a.useState)(!0),[d,u]=(0,a.useState)(null);return s?(0,r.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,r.jsx)(o.A,{className:"h-8 w-8 animate-spin text-primary"})}):d?(0,r.jsx)("div",{className:"text-center py-8 text-red-500",children:(0,r.jsx)("p",{children:d})}):(0,r.jsx)("div",{className:"rounded-md border",children:(0,r.jsxs)(n.XI,{children:[(0,r.jsx)(n.A0,{children:(0,r.jsxs)(n.Hj,{children:[(0,r.jsx)(n.nd,{children:"Message"}),(0,r.jsx)(n.nd,{children:"Client"}),(0,r.jsx)(n.nd,{children:"Type"}),(0,r.jsx)(n.nd,{children:"Timestamp"})]})}),(0,r.jsx)(n.BF,{children:0===e.length?(0,r.jsx)(n.Hj,{children:(0,r.jsx)(n.nA,{colSpan:4,className:"text-center py-8 text-muted-foreground",children:"No messages found"})}):e.map(e=>(0,r.jsxs)(n.Hj,{children:[(0,r.jsx)(n.nA,{className:"max-w-[400px] truncate",children:e.text}),(0,r.jsx)(n.nA,{children:e.client?.name||"Unknown"}),(0,r.jsx)(n.nA,{children:(0,r.jsx)(i.E,{variant:e.isBot?"secondary":"default",children:e.isBot?"Bot":"User"})}),(0,r.jsx)(n.nA,{children:(0,l.m)(new Date(e.createdAt),{addSuffix:!0})})]},e.id))})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},12948:(e,t,s)=>{"use strict";s.d(t,{ClientsDataTable:()=>N});var r=s(60687),a=s(43210),n=s(96752),i=s(55629),o=s(24934),l=s(41862),c=s(93661),d=s(13861),u=s(58887),h=s(59821),p=s(47033),m=s(14952),x=s(96241);let f=({className:e,...t})=>(0,r.jsx)("nav",{role:"navigation","aria-label":"pagination",className:(0,x.cn)("mx-auto flex w-full justify-center",e),...t});f.displayName="Pagination";let j=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("ul",{ref:s,className:(0,x.cn)("flex flex-row items-center gap-1",e),...t}));j.displayName="PaginationContent";let b=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("li",{ref:s,className:(0,x.cn)("",e),...t}));b.displayName="PaginationItem";let g=({className:e,isActive:t,size:s="icon",...a})=>(0,r.jsx)("a",{"aria-current":t?"page":void 0,className:(0,x.cn)((0,o.r)({variant:t?"outline":"ghost",size:s}),e),...a});g.displayName="PaginationLink";let v=({className:e,...t})=>(0,r.jsxs)(g,{"aria-label":"Go to previous page",size:"default",className:(0,x.cn)("gap-1 pl-2.5",e),...t,children:[(0,r.jsx)(p.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Previous"})]});v.displayName="PaginationPrevious";let y=({className:e,...t})=>(0,r.jsxs)(g,{"aria-label":"Go to next page",size:"default",className:(0,x.cn)("gap-1 pr-2.5",e),...t,children:[(0,r.jsx)("span",{children:"Next"}),(0,r.jsx)(m.A,{className:"h-4 w-4"})]});y.displayName="PaginationNext",s(91983);var w=s(78201);function N(){let[e,t]=(0,a.useState)([]),[s,p]=(0,a.useState)(!0),[m,x]=(0,a.useState)(null),[N,A]=(0,a.useState)(1),[C,T]=(0,a.useState)(1),[D,P]=(0,a.useState)(!1),[k,R]=(0,a.useState)(!1),M=e=>{A(e)};return s?(0,r.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,r.jsx)(l.A,{className:"h-8 w-8 animate-spin text-primary"})}):m?(0,r.jsx)("div",{className:"text-center py-8 text-red-500",children:(0,r.jsx)("p",{children:m})}):(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"rounded-md border",children:(0,r.jsxs)(n.XI,{children:[(0,r.jsx)(n.A0,{children:(0,r.jsxs)(n.Hj,{children:[(0,r.jsx)(n.nd,{children:"Name"}),(0,r.jsx)(n.nd,{children:"Phone"}),(0,r.jsx)(n.nd,{children:"Type"}),(0,r.jsx)(n.nd,{children:"Last Active"}),(0,r.jsx)(n.nd,{children:"Last Message"}),(0,r.jsx)(n.nd,{children:"Created At"}),(0,r.jsx)(n.nd,{className:"w-[80px]"})]})}),(0,r.jsx)(n.BF,{children:0===e.length?(0,r.jsx)(n.Hj,{children:(0,r.jsx)(n.nA,{colSpan:7,className:"text-center py-8 text-muted-foreground",children:"No clients found"})}):e.map(e=>(0,r.jsxs)(n.Hj,{children:[(0,r.jsx)(n.nA,{className:"font-medium",children:e.name}),(0,r.jsx)(n.nA,{children:e.phone}),(0,r.jsx)(n.nA,{children:(0,r.jsx)(h.E,{variant:"outline",children:e.type})}),(0,r.jsx)(n.nA,{children:(0,w.m)(new Date(e.lastActive),{addSuffix:!0})}),(0,r.jsx)(n.nA,{className:"max-w-[200px] truncate",children:e.lastMessage}),(0,r.jsx)(n.nA,{children:new Date(e.createdAt).toLocaleDateString()}),(0,r.jsx)(n.nA,{children:(0,r.jsxs)(i.rI,{children:[(0,r.jsx)(i.ty,{asChild:!0,children:(0,r.jsxs)(o.$,{variant:"ghost",className:"h-8 w-8 p-0",children:[(0,r.jsx)("span",{className:"sr-only",children:"Open menu"}),(0,r.jsx)(c.A,{className:"h-4 w-4"})]})}),(0,r.jsxs)(i.SQ,{align:"end",children:[(0,r.jsxs)(i._2,{children:[(0,r.jsx)(d.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"View Details"})]}),(0,r.jsxs)(i._2,{children:[(0,r.jsx)(u.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"View Messages"})]})]})]})})]},e.id))})]})}),C>1&&(0,r.jsx)(f,{children:(0,r.jsxs)(j,{children:[k&&(0,r.jsx)(b,{children:(0,r.jsx)(v,{href:"#",onClick:e=>{e.preventDefault(),M(N-1)}})}),Array.from({length:C},(e,t)=>t+1).map(e=>(0,r.jsx)(b,{children:(0,r.jsx)(g,{href:"#",isActive:e===N,onClick:t=>{t.preventDefault(),M(e)},children:e})},e)),D&&(0,r.jsx)(b,{children:(0,r.jsx)(y,{href:"#",onClick:e=>{e.preventDefault(),M(N+1)}})})]})})]})}},13040:(e,t,s)=>{"use strict";s.d(t,{QAPairsDataTable:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call QAPairsDataTable() from the server but QAPairsDataTable is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\components\\data\\qa-pairs-data-table.tsx","QAPairsDataTable")},13861:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26269:(e,t,s)=>{"use strict";s.d(t,{Tabs:()=>k,TabsContent:()=>q,TabsList:()=>R,TabsTrigger:()=>M});var r=s(60687),a=s(43210),n=s(70569),i=s(11273),o=s(72942),l=s(46059),c=s(14163),d=s(43),u=s(65551),h=s(96963),p="Tabs",[m,x]=(0,i.A)(p,[o.RG]),f=(0,o.RG)(),[j,b]=m(p),g=a.forwardRef((e,t)=>{let{__scopeTabs:s,value:a,onValueChange:n,defaultValue:i,orientation:o="horizontal",dir:l,activationMode:p="automatic",...m}=e,x=(0,d.jH)(l),[f,b]=(0,u.i)({prop:a,onChange:n,defaultProp:i});return(0,r.jsx)(j,{scope:s,baseId:(0,h.B)(),value:f,onValueChange:b,orientation:o,dir:x,activationMode:p,children:(0,r.jsx)(c.sG.div,{dir:x,"data-orientation":o,...m,ref:t})})});g.displayName=p;var v="TabsList",y=a.forwardRef((e,t)=>{let{__scopeTabs:s,loop:a=!0,...n}=e,i=b(v,s),l=f(s);return(0,r.jsx)(o.bL,{asChild:!0,...l,orientation:i.orientation,dir:i.dir,loop:a,children:(0,r.jsx)(c.sG.div,{role:"tablist","aria-orientation":i.orientation,...n,ref:t})})});y.displayName=v;var w="TabsTrigger",N=a.forwardRef((e,t)=>{let{__scopeTabs:s,value:a,disabled:i=!1,...l}=e,d=b(w,s),u=f(s),h=T(d.baseId,a),p=D(d.baseId,a),m=a===d.value;return(0,r.jsx)(o.q7,{asChild:!0,...u,focusable:!i,active:m,children:(0,r.jsx)(c.sG.button,{type:"button",role:"tab","aria-selected":m,"aria-controls":p,"data-state":m?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:h,...l,ref:t,onMouseDown:(0,n.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(a)}),onKeyDown:(0,n.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(a)}),onFocus:(0,n.m)(e.onFocus,()=>{let e="manual"!==d.activationMode;m||i||!e||d.onValueChange(a)})})})});N.displayName=w;var A="TabsContent",C=a.forwardRef((e,t)=>{let{__scopeTabs:s,value:n,forceMount:i,children:o,...d}=e,u=b(A,s),h=T(u.baseId,n),p=D(u.baseId,n),m=n===u.value,x=a.useRef(m);return a.useEffect(()=>{let e=requestAnimationFrame(()=>x.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,r.jsx)(l.C,{present:i||m,children:({present:s})=>(0,r.jsx)(c.sG.div,{"data-state":m?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":h,hidden:!s,id:p,tabIndex:0,...d,ref:t,style:{...e.style,animationDuration:x.current?"0s":void 0},children:s&&o})})});function T(e,t){return`${e}-trigger-${t}`}function D(e,t){return`${e}-content-${t}`}C.displayName=A;var P=s(96241);let k=g,R=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(y,{ref:s,className:(0,P.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));R.displayName=y.displayName;let M=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(N,{ref:s,className:(0,P.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));M.displayName=N.displayName;let q=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(C,{ref:s,className:(0,P.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));q.displayName=C.displayName},26761:(e,t,s)=>{Promise.resolve().then(s.bind(s,2547)),Promise.resolve().then(s.bind(s,80536)),Promise.resolve().then(s.bind(s,83302)),Promise.resolve().then(s.bind(s,13040)),Promise.resolve().then(s.bind(s,55916))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},38390:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>c});var r=s(65239),a=s(48088),n=s(88170),i=s.n(n),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let c={children:["",{children:["dashboard",{children:["data",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,91697)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\data\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,83249)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,60566)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\data\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/data/page",pathname:"/dashboard/data",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},47033:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},48161:e=>{"use strict";e.exports=require("node:os")},51358:(e,t,s)=>{"use strict";s.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>l,Zp:()=>i,aR:()=>o});var r=s(37413),a=s(61120),n=s(66819);let i=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));i.displayName="Card";let o=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...t}));o.displayName="CardHeader";let l=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle";let c=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",e),...t}));c.displayName="CardDescription";let d=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,n.cn)("p-6 pt-0",e),...t}));d.displayName="CardContent",a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},51455:e=>{"use strict";e.exports=require("node:fs/promises")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},55916:(e,t,s)=>{"use strict";s.d(t,{Tabs:()=>a,TabsContent:()=>o,TabsList:()=>n,TabsTrigger:()=>i});var r=s(12907);let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call Tabs() from the server but Tabs is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\components\\ui\\tabs.tsx","Tabs"),n=(0,r.registerClientReference)(function(){throw Error("Attempted to call TabsList() from the server but TabsList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\components\\ui\\tabs.tsx","TabsList"),i=(0,r.registerClientReference)(function(){throw Error("Attempted to call TabsTrigger() from the server but TabsTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\components\\ui\\tabs.tsx","TabsTrigger"),o=(0,r.registerClientReference)(function(){throw Error("Attempted to call TabsContent() from the server but TabsContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\components\\ui\\tabs.tsx","TabsContent")},57975:e=>{"use strict";e.exports=require("node:util")},59556:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var r=s(51060);class a{constructor(){this.isOfflineMode=!1,this.baseURL="http://localhost:5000/api/v1",this.baseURL||console.warn("NEXT_PUBLIC_BACKEND_API_URL is not defined. API requests may fail."),this.client=r.A.create({baseURL:this.baseURL,headers:{"Content-Type":"application/json"},timeout:1e4}),this.client.interceptors.request.use(e=>e,e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,e=>(e.response?(console.error("API Error Response:",e.response.status,e.response.data),e.response.data&&0!==Object.keys(e.response.data).length||(e.response.data={error:`Request failed with status code ${e.response.status}`}),404===e.response.status&&(console.log("Resource not found:",e.config?.url),e.response.data={error:"The requested resource was not found. Please refresh and try again."}),e.responseData=e.response.data):e.request?(console.error("API No Response:",e.request),e.message&&(e.message.includes("Network Error")||e.message.includes("timeout"))?(console.error("Network Error detected. Server might be down or unreachable."),this.setOfflineMode(!0),e.isOffline=!0,e.responseData={error:"Network Error: Unable to connect to the server. Please check your connection or try again later.",isOffline:!0},console.warn("API Client: Server connection failed. If you're running in development mode, make sure your backend server is running."),window.dispatchEvent(new CustomEvent("api:offline",{detail:{isOffline:!0}}))):e.responseData={error:"No response received from server. Please check your connection."}):(console.error("API Request Error:",e.message),e.responseData={error:e.message||"An unexpected error occurred"}),Promise.reject(e)))}async get(e,t){try{if(this.isOfflineMode&&!e.includes("/health"))throw console.log(`API Client: In offline mode, skipping GET request to ${e}`),Error("Network Error: Application is in offline mode");let s=await this.client.get(e,t);return this.isOfflineMode&&this.setOfflineMode(!1),s.data}catch(e){throw e}}async post(e,t,s){return(await this.client.post(e,t,s)).data}async put(e,t,s){return(await this.client.put(e,t,s)).data}async delete(e,t){try{console.log(`Making DELETE request to: ${e}`);let s=await this.client.delete(e,t);if(204===s.status)return console.log(`DELETE request to ${e} successful with 204 status`),null;return s.data}catch(t){throw console.error(`DELETE request to ${e} failed:`,t),t}}async patch(e,t,s){return(await this.client.patch(e,t,s)).data}async upload(e,t,s){let r={...s,headers:{...s?.headers,"Content-Type":"multipart/form-data"}};return(await this.client.post(e,t,r)).data}getBaseURL(){return this.baseURL}isInOfflineMode(){return this.isOfflineMode}setOfflineMode(e){this.isOfflineMode!==e&&(console.log(`API Client: Setting offline mode to ${e}`),this.isOfflineMode=e,window.dispatchEvent(new CustomEvent("api:offline",{detail:{isOffline:e}})))}async tryReconnect(){try{return console.log("API Client: Attempting to reconnect to the server..."),await this.client.get("/health",{timeout:5e3}),console.log("API Client: Reconnection successful"),this.setOfflineMode(!1),!0}catch(e){return console.log("API Client: Reconnection failed, still in offline mode"),!1}}}let n=new a},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66819:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n});var r=s(75986),a=s(8974);function n(...e){return(0,a.QP)((0,r.$)(e))}},69441:(e,t,s)=>{"use strict";s.d(t,{CampaignsDataTable:()=>h});var r=s(60687),a=s(43210),n=s(96752),i=s(55629),o=s(24934),l=s(41862),c=s(93661),d=s(13861),u=s(59821);function h(){let[e,t]=(0,a.useState)([]),[s,h]=(0,a.useState)(!0),[p,m]=(0,a.useState)(null);return s?(0,r.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,r.jsx)(l.A,{className:"h-8 w-8 animate-spin text-primary"})}):p?(0,r.jsx)("div",{className:"text-center py-8 text-red-500",children:(0,r.jsx)("p",{children:p})}):(0,r.jsx)("div",{className:"rounded-md border",children:(0,r.jsxs)(n.XI,{children:[(0,r.jsx)(n.A0,{children:(0,r.jsxs)(n.Hj,{children:[(0,r.jsx)(n.nd,{children:"Name"}),(0,r.jsx)(n.nd,{children:"Type"}),(0,r.jsx)(n.nd,{children:"Status"}),(0,r.jsx)(n.nd,{children:"Created At"}),(0,r.jsx)(n.nd,{children:"Scheduled At"}),(0,r.jsx)(n.nd,{className:"w-[80px]"})]})}),(0,r.jsx)(n.BF,{children:0===e.length?(0,r.jsx)(n.Hj,{children:(0,r.jsx)(n.nA,{colSpan:6,className:"text-center py-8 text-muted-foreground",children:"No campaigns found"})}):e.map(e=>(0,r.jsxs)(n.Hj,{children:[(0,r.jsx)(n.nA,{className:"font-medium",children:e.name}),(0,r.jsx)(n.nA,{children:e.type}),(0,r.jsx)(n.nA,{children:(0,r.jsx)(u.E,{variant:"active"===e.status.toLowerCase()?"default":"completed"===e.status.toLowerCase()?"secondary":"outline",children:e.status})}),(0,r.jsx)(n.nA,{children:new Date(e.createdAt).toLocaleDateString()}),(0,r.jsx)(n.nA,{children:e.scheduledAt?new Date(e.scheduledAt).toLocaleDateString():"Not scheduled"}),(0,r.jsx)(n.nA,{children:(0,r.jsxs)(i.rI,{children:[(0,r.jsx)(i.ty,{asChild:!0,children:(0,r.jsxs)(o.$,{variant:"ghost",className:"h-8 w-8 p-0",children:[(0,r.jsx)("span",{className:"sr-only",children:"Open menu"}),(0,r.jsx)(c.A,{className:"h-4 w-4"})]})}),(0,r.jsx)(i.SQ,{align:"end",children:(0,r.jsxs)(i._2,{children:[(0,r.jsx)(d.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"View Details"})]})})]})})]},e.id))})]})})}s(94792)},73024:e=>{"use strict";e.exports=require("node:fs")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},78201:(e,t,s)=>{"use strict";s.d(t,{m:()=>u});var r=s(87981),a=s(64722),n=s(78872),i=s(31504),o=s(29789),l=s(23711);function c(e,t){let s=+(0,l.a)(e)-+(0,l.a)(t);return s<0?-1:s>0?1:s}var d=s(58505);function u(e,t){return function(e,t,s){let r;let u=(0,n.q)(),h=s?.locale??u.locale??a.c,p=c(e,t);if(isNaN(p))throw RangeError("Invalid time value");let m=Object.assign({},s,{addSuffix:s?.addSuffix,comparison:p}),[x,f]=(0,o.x)(s?.in,...p>0?[t,e]:[e,t]),j=function(e,t,s){var r;return(r=void 0,e=>{let t=(r?Math[r]:Math.trunc)(e);return 0===t?0:t})((+(0,l.a)(e)-+(0,l.a)(t))/1e3)}(f,x),b=Math.round((j-((0,i.G)(f)-(0,i.G)(x))/1e3)/60);if(b<2){if(s?.includeSeconds){if(j<5)return h.formatDistance("lessThanXSeconds",5,m);if(j<10)return h.formatDistance("lessThanXSeconds",10,m);if(j<20)return h.formatDistance("lessThanXSeconds",20,m);else if(j<40)return h.formatDistance("halfAMinute",0,m);else if(j<60)return h.formatDistance("lessThanXMinutes",1,m);else return h.formatDistance("xMinutes",1,m)}return 0===b?h.formatDistance("lessThanXMinutes",1,m):h.formatDistance("xMinutes",b,m)}if(b<45)return h.formatDistance("xMinutes",b,m);if(b<90)return h.formatDistance("aboutXHours",1,m);if(b<d.F6){let e=Math.round(b/60);return h.formatDistance("aboutXHours",e,m)}if(b<2520)return h.formatDistance("xDays",1,m);else if(b<d.Nw){let e=Math.round(b/d.F6);return h.formatDistance("xDays",e,m)}else if(b<2*d.Nw)return r=Math.round(b/d.Nw),h.formatDistance("aboutXMonths",r,m);if((r=function(e,t,s){let[r,a,n]=(0,o.x)(void 0,e,e,t),i=c(a,n),d=Math.abs(function(e,t,s){let[r,a]=(0,o.x)(void 0,e,t);return 12*(r.getFullYear()-a.getFullYear())+(r.getMonth()-a.getMonth())}(a,n));if(d<1)return 0;1===a.getMonth()&&a.getDate()>27&&a.setDate(30),a.setMonth(a.getMonth()-i*d);let u=c(a,n)===-i;(function(e,t){let s=(0,l.a)(e,void 0);return+function(e,t){let s=(0,l.a)(e,t?.in);return s.setHours(23,59,59,999),s}(s,void 0)==+function(e,t){let s=(0,l.a)(e,t?.in),r=s.getMonth();return s.setFullYear(s.getFullYear(),r+1,0),s.setHours(23,59,59,999),s}(s,t)})(r)&&1===d&&1===c(r,n)&&(u=!1);let h=i*(d-+u);return 0===h?0:h}(f,x))<12){let e=Math.round(b/d.Nw);return h.formatDistance("xMonths",e,m)}{let e=r%12,t=Math.trunc(r/12);return e<3?h.formatDistance("aboutXYears",t,m):e<9?h.formatDistance("overXYears",t,m):h.formatDistance("almostXYears",t+1,m)}}(e,(0,r.w)(e,Date.now()),t)}},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},80536:(e,t,s)=>{"use strict";s.d(t,{ClientsDataTable:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call ClientsDataTable() from the server but ClientsDataTable is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\components\\data\\clients-data-table.tsx","ClientsDataTable")},81630:e=>{"use strict";e.exports=require("http")},83302:(e,t,s)=>{"use strict";s.d(t,{MessagesDataTable:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call MessagesDataTable() from the server but MessagesDataTable is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\components\\data\\messages-data-table.tsx","MessagesDataTable")},83997:e=>{"use strict";e.exports=require("tty")},91697:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var r=s(37413),a=s(55916),n=s(51358),i=s(80536),o=s(83302),l=s(2547),c=s(13040);function d(){return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Database Dashboard"}),(0,r.jsx)("p",{className:"text-muted-foreground mt-2",children:"View and manage all data from the backend database"})]}),(0,r.jsxs)(a.Tabs,{defaultValue:"clients",className:"space-y-4",children:[(0,r.jsxs)(a.TabsList,{children:[(0,r.jsx)(a.TabsTrigger,{value:"clients",children:"Clients"}),(0,r.jsx)(a.TabsTrigger,{value:"messages",children:"Messages"}),(0,r.jsx)(a.TabsTrigger,{value:"campaigns",children:"Campaigns"}),(0,r.jsx)(a.TabsTrigger,{value:"qa-pairs",children:"QA Pairs"})]}),(0,r.jsx)(a.TabsContent,{value:"clients",className:"space-y-4",children:(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{children:(0,r.jsx)(n.ZB,{children:"Clients"})}),(0,r.jsx)(n.Wu,{children:(0,r.jsx)(i.ClientsDataTable,{})})]})}),(0,r.jsx)(a.TabsContent,{value:"messages",className:"space-y-4",children:(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{children:(0,r.jsx)(n.ZB,{children:"Messages"})}),(0,r.jsx)(n.Wu,{children:(0,r.jsx)(o.MessagesDataTable,{})})]})}),(0,r.jsx)(a.TabsContent,{value:"campaigns",className:"space-y-4",children:(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{children:(0,r.jsx)(n.ZB,{children:"Campaigns"})}),(0,r.jsx)(n.Wu,{children:(0,r.jsx)(l.CampaignsDataTable,{})})]})}),(0,r.jsx)(a.TabsContent,{value:"qa-pairs",className:"space-y-4",children:(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{children:(0,r.jsx)(n.ZB,{children:"QA Pairs"})}),(0,r.jsx)(n.Wu,{children:(0,r.jsx)(c.QAPairsDataTable,{})})]})})]})]})}},91913:(e,t,s)=>{Promise.resolve().then(s.bind(s,69441)),Promise.resolve().then(s.bind(s,12948)),Promise.resolve().then(s.bind(s,10656)),Promise.resolve().then(s.bind(s,7331)),Promise.resolve().then(s.bind(s,26269))},91983:(e,t,s)=>{"use strict";s.d(t,{Lf:()=>a,UU:()=>n,zO:()=>i});var r=s(59556);let a=async(e=1,t=50)=>{try{return await r.A.get(`/clients?page=${e}&limit=${t}`)}catch(e){throw console.error("Error fetching clients:",e),e}},n=async e=>{try{return await r.A.post("/clients",e)}catch(e){throw console.error("Error creating client:",e),e}},i=async(e,t)=>{try{return await r.A.put(`/clients/${e}`,t)}catch(t){throw console.error(`Error updating client with ID ${e}:`,t),t}}},94735:e=>{"use strict";e.exports=require("events")},94792:(e,t,s)=>{"use strict";s.d(t,{ME:()=>a,SX:()=>i,jz:()=>n});var r=s(59556);let a=async()=>{try{return await r.A.get("/marketing/campaigns")}catch(e){throw console.error("Error fetching campaigns:",e),e}},n=async e=>{try{return await r.A.get(`/marketing/campaigns/${e}`)}catch(t){throw console.error(`Error fetching campaign with ID ${e}:`,t),t}},i=async(e,t)=>{try{return await r.A.put(`/marketing/campaigns/${e}`,t)}catch(t){throw console.error(`Error updating campaign with ID ${e}:`,t),t}}},96330:e=>{"use strict";e.exports=require("@prisma/client")},96752:(e,t,s)=>{"use strict";s.d(t,{A0:()=>o,BF:()=>l,Hj:()=>c,XI:()=>i,nA:()=>u,nd:()=>d});var r=s(60687),a=s(43210),n=s(96241);let i=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{className:"relative w-full overflow-auto",children:(0,r.jsx)("table",{ref:s,className:(0,n.cn)("w-full caption-bottom text-sm",e),...t})}));i.displayName="Table";let o=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("thead",{ref:s,className:(0,n.cn)("[&_tr]:border-b",e),...t}));o.displayName="TableHeader";let l=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("tbody",{ref:s,className:(0,n.cn)("[&_tr:last-child]:border-0",e),...t}));l.displayName="TableBody",a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("tfoot",{ref:s,className:(0,n.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...t})).displayName="TableFooter";let c=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("tr",{ref:s,className:(0,n.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...t}));c.displayName="TableRow";let d=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("th",{ref:s,className:(0,n.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...t}));d.displayName="TableHead";let u=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("td",{ref:s,className:(0,n.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...t}));u.displayName="TableCell",a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("caption",{ref:s,className:(0,n.cn)("mt-4 text-sm text-muted-foreground",e),...t})).displayName="TableCaption"}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[7719,9656,2190,3903,5153,3555,1060,4017,9474,8722,9464,381],()=>s(38390));module.exports=r})();