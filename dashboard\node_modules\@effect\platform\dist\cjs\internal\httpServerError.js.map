{"version": 3, "file": "httpServerError.js", "names": ["Cause", "_interopRequireWildcard", "require", "Effect", "FiberId", "_GlobalValue", "Option", "Predicate", "Respondable", "internalServerResponse", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "TypeId", "exports", "Symbol", "for", "isServerError", "hasProperty", "clientAbortFiberId", "globalValue", "runtime", "causeResponse", "cause", "effect", "stripped", "reduce", "succeed", "internalServerError", "empty", "acc", "_tag", "some", "toResponseOrElse", "error", "toResponseOrElseDefect", "defect", "none", "response", "fiberId", "clientAbortError", "serverAbortError", "map", "isEmptyType", "die", "sequential", "causeResponseStripped", "stripSomeDefects", "isServerResponse", "status", "exitResponse", "exit", "value"], "sources": ["../../../src/internal/httpServerError.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAF,uBAAA,CAAAC,OAAA;AAEA,IAAAE,OAAA,GAAAH,uBAAA,CAAAC,OAAA;AACA,IAAAG,YAAA,GAAAH,OAAA;AACA,IAAAI,MAAA,GAAAL,uBAAA,CAAAC,OAAA;AACA,IAAAK,SAAA,GAAAN,uBAAA,CAAAC,OAAA;AAEA,IAAAM,WAAA,GAAAP,uBAAA,CAAAC,OAAA;AAEA,IAAAO,sBAAA,GAAAR,uBAAA,CAAAC,OAAA;AAAiE,SAAAQ,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAV,wBAAAU,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAEjE;AACO,MAAMW,MAAM,GAAAC,OAAA,CAAAD,MAAA,gBAAiBE,MAAM,CAACC,GAAG,CAC5C,kCAAkC,CACnB;AAEjB;AACO,MAAMC,aAAa,GAAIT,CAAU,IAAiClB,SAAS,CAAC4B,WAAW,CAACV,CAAC,EAAEK,MAAM,CAAC;AAEzG;AAAAC,OAAA,CAAAG,aAAA,GAAAA,aAAA;AACO,MAAME,kBAAkB,GAAAL,OAAA,CAAAK,kBAAA,gBAAG,IAAAC,wBAAW,EAC3C,qDAAqD,EACrD,MAAMjC,OAAO,CAACkC,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAC/B;AAED;AACO,MAAMC,aAAa,GACxBC,KAAqB,IAC2C;EAChE,MAAM,CAACC,MAAM,EAAEC,QAAQ,CAAC,GAAG1C,KAAK,CAAC2C,MAAM,CACrCH,KAAK,EACL,CAACrC,MAAM,CAACyC,OAAO,CAACC,mBAAmB,CAAC,EAAE7C,KAAK,CAAC8C,KAAuB,CAAU,EAC7E,CAACC,GAAG,EAAEP,KAAK,KAAI;IACb,QAAQA,KAAK,CAACQ,IAAI;MAChB,KAAK,OAAO;QAAE;UACZ,OAAO1C,MAAM,CAAC2C,IAAI,CAACF,GAAG,CAAC;QACzB;MACA,KAAK,MAAM;QAAE;UACX,OAAOzC,MAAM,CAAC2C,IAAI,CAAC,CAACzC,WAAW,CAAC0C,gBAAgB,CAACV,KAAK,CAACW,KAAK,EAAEN,mBAAmB,CAAC,EAAEL,KAAK,CAAU,CAAC;QACtG;MACA,KAAK,KAAK;QAAE;UACV,OAAOlC,MAAM,CAAC2C,IAAI,CAAC,CAACzC,WAAW,CAAC4C,sBAAsB,CAACZ,KAAK,CAACa,MAAM,EAAER,mBAAmB,CAAC,EAAEL,KAAK,CAAU,CAAC;QAC7G;MACA,KAAK,WAAW;QAAE;UAChB,IAAIO,GAAG,CAAC,CAAC,CAAC,CAACC,IAAI,KAAK,OAAO,EAAE;YAC3B,OAAO1C,MAAM,CAACgD,IAAI,EAAE;UACtB;UACA,MAAMC,QAAQ,GAAGf,KAAK,CAACgB,OAAO,KAAKpB,kBAAkB,GAAGqB,gBAAgB,GAAGC,gBAAgB;UAC3F,OAAOpD,MAAM,CAAC2C,IAAI,CAAC,CAAC9C,MAAM,CAACyC,OAAO,CAACW,QAAQ,CAAC,EAAEf,KAAK,CAAU,CAAC;QAChE;MACA;QAAS;UACP,OAAOlC,MAAM,CAACgD,IAAI,EAAE;QACtB;IACF;EACF,CAAC,CACF;EACD,OAAOnD,MAAM,CAACwD,GAAG,CAAClB,MAAM,EAAGc,QAAQ,IAAI;IACrC,IAAIvD,KAAK,CAAC4D,WAAW,CAAClB,QAAQ,CAAC,EAAE;MAC/B,OAAO,CAACa,QAAQ,EAAEvD,KAAK,CAAC6D,GAAG,CAACN,QAAQ,CAAC,CAAU;IACjD;IACA,OAAO,CAACA,QAAQ,EAAEvD,KAAK,CAAC8D,UAAU,CAACpB,QAAQ,EAAE1C,KAAK,CAAC6D,GAAG,CAACN,QAAQ,CAAC,CAAC,CAAU;EAC7E,CAAC,CAAC;AACJ,CAAC;AAED;AAAAxB,OAAA,CAAAQ,aAAA,GAAAA,aAAA;AACO,MAAMwB,qBAAqB,GAChCvB,KAAqB,IAC4D;EACjF,IAAIe,QAAwC;EAC5C,MAAMb,QAAQ,GAAG1C,KAAK,CAACgE,gBAAgB,CAACxB,KAAK,EAAGa,MAAM,IAAI;IACxD,IAAI5C,sBAAsB,CAACwD,gBAAgB,CAACZ,MAAM,CAAC,EAAE;MACnDE,QAAQ,GAAGF,MAAM;MACjB,OAAO/C,MAAM,CAAC2C,IAAI,CAACjD,KAAK,CAAC8C,KAAK,CAAC;IACjC;IACA,OAAOxC,MAAM,CAACgD,IAAI,EAAE;EACtB,CAAC,CAAC;EACF,OAAO,CAACC,QAAQ,IAAIV,mBAAmB,EAAEH,QAAQ,CAAC;AACpD,CAAC;AAAAX,OAAA,CAAAgC,qBAAA,GAAAA,qBAAA;AAED,MAAMlB,mBAAmB,gBAAGpC,sBAAsB,CAACqC,KAAK,CAAC;EAAEoB,MAAM,EAAE;AAAG,CAAE,CAAC;AACzE,MAAMT,gBAAgB,gBAAGhD,sBAAsB,CAACqC,KAAK,CAAC;EAAEoB,MAAM,EAAE;AAAG,CAAE,CAAC;AACtE,MAAMR,gBAAgB,gBAAGjD,sBAAsB,CAACqC,KAAK,CAAC;EAAEoB,MAAM,EAAE;AAAG,CAAE,CAAC;AAEtE;AACO,MAAMC,YAAY,GAAOC,IAAsC,IAAwB;EAC5F,IAAIA,IAAI,CAACpB,IAAI,KAAK,SAAS,EAAE;IAC3B,OAAOoB,IAAI,CAACC,KAAK;EACnB;EACA,OAAON,qBAAqB,CAACK,IAAI,CAAC5B,KAAK,CAAC,CAAC,CAAC,CAAC;AAC7C,CAAC;AAAAT,OAAA,CAAAoC,YAAA,GAAAA,YAAA", "ignoreList": []}