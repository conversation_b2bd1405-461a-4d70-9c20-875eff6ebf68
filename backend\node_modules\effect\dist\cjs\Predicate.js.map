{"version": 3, "file": "Predicate.js", "names": ["_Function", "require", "mapInput", "exports", "dual", "self", "f", "b", "isTupleOf", "n", "length", "isTupleOfAtLeast", "<PERSON><PERSON><PERSON><PERSON>", "input", "isSet", "Set", "isMap", "Map", "isString", "isNumber", "isBoolean", "isBigInt", "isSymbol", "isPropertyKey", "u", "isFunction", "isFunction_", "isUndefined", "undefined", "isNotUndefined", "isNull", "isNotNull", "isNever", "_", "isUnknown", "isRecordOrArray", "isObject", "hasProperty", "property", "isTagged", "tag", "isNullable", "isNotNullable", "isError", "Error", "isUint8Array", "Uint8Array", "isDate", "Date", "isIterable", "Symbol", "iterator", "isRecord", "Array", "isArray", "isReadonlyRecord", "isPromise", "then", "catch", "isPromiseLike", "isRegExp", "RegExp", "compose", "ab", "bc", "a", "product", "that", "all", "collection", "as", "collectionIndex", "p", "productMany", "rest", "head", "tail", "tuple", "elements", "struct", "fields", "keys", "Object", "key", "not", "or", "and", "xor", "eqv", "implies", "antecedent", "consequent", "nor", "nand", "every", "some"], "sources": ["../../src/Predicate.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAGA,IAAAA,SAAA,GAAAC,OAAA;AAHA;;;;AAsEA;;;;;;;;;;;;;;;;;;;AAmBO,MAAMC,QAAQ,GAAAC,OAAA,CAAAD,QAAA,gBAyCjB,IAAAE,cAAI,EAAC,CAAC,EAAE,CAAOC,IAAkB,EAAEC,CAAc,KAAoBC,CAAC,IAAKF,IAAI,CAACC,CAAC,CAACC,CAAC,CAAC,CAAC,CAAC;AAE1F;;;;;;;;;;;;;;;;;;;;;;;;AAwBO,MAAMC,SAAS,GAAAL,OAAA,CAAAK,SAAA,gBAmDlB,IAAAJ,cAAI,EAAC,CAAC,EAAE,CAAsBC,IAAsB,EAAEI,CAAI,KAA4BJ,IAAI,CAACK,MAAM,KAAKD,CAAC,CAAC;AAE5G;;;;;;;;;;;;;;;;;;;;;;;;AAwBO,MAAME,gBAAgB,GAAAR,OAAA,CAAAQ,gBAAA,gBAmDzB,IAAAP,cAAI,EAAC,CAAC,EAAE,CAAsBC,IAAsB,EAAEI,CAAI,KAAmCJ,IAAI,CAACK,MAAM,IAAID,CAAC,CAAC;AAElH;;;;;;;;;;;;;;;;AAgBO,MAAMG,QAAQ,GAAIC,KAAc,IAAK,CAAC,CAACA,KAAK;AAEnD;;;;;;;;;;;;;;;;;;AAAAV,OAAA,CAAAS,QAAA,GAAAA,QAAA;AAkBO,MAAME,KAAK,GAAID,KAAc,IAA4BA,KAAK,YAAYE,GAAG;AAEpF;;;;;;;;;;;;;;;;;AAAAZ,OAAA,CAAAW,KAAA,GAAAA,KAAA;AAiBO,MAAME,KAAK,GAAIH,KAAc,IAAqCA,KAAK,YAAYI,GAAG;AAE7F;;;;;;;;;;;;;;;;AAAAd,OAAA,CAAAa,KAAA,GAAAA,KAAA;AAgBO,MAAME,QAAQ,GAAIL,KAAc,IAAsB,OAAOA,KAAK,KAAK,QAAQ;AAEtF;;;;;;;;;;;;;;;;AAAAV,OAAA,CAAAe,QAAA,GAAAA,QAAA;AAgBO,MAAMC,QAAQ,GAAIN,KAAc,IAAsB,OAAOA,KAAK,KAAK,QAAQ;AAEtF;;;;;;;;;;;;;;;;AAAAV,OAAA,CAAAgB,QAAA,GAAAA,QAAA;AAgBO,MAAMC,SAAS,GAAIP,KAAc,IAAuB,OAAOA,KAAK,KAAK,SAAS;AAEzF;;;;;;;;;;;;;;;;AAAAV,OAAA,CAAAiB,SAAA,GAAAA,SAAA;AAgBO,MAAMC,QAAQ,GAAIR,KAAc,IAAsB,OAAOA,KAAK,KAAK,QAAQ;AAEtF;;;;;;;;;;;;;;;;AAAAV,OAAA,CAAAkB,QAAA,GAAAA,QAAA;AAgBO,MAAMC,QAAQ,GAAIT,KAAc,IAAsB,OAAOA,KAAK,KAAK,QAAQ;AAEtF;AACA;AAAAV,OAAA,CAAAmB,QAAA,GAAAA,QAAA;AACO,MAAMC,aAAa,GAAIC,CAAU,IAAuBN,QAAQ,CAACM,CAAC,CAAC,IAAIL,QAAQ,CAACK,CAAC,CAAC,IAAIF,QAAQ,CAACE,CAAC,CAAC;AAExG;;;;;;;;;;;;;;;;AAAArB,OAAA,CAAAoB,aAAA,GAAAA,aAAA;AAgBO,MAAME,UAAU,GAAAtB,OAAA,CAAAsB,UAAA,GAA0CC,oBAAW;AAE5E;;;;;;;;;;;;;;;;;AAiBO,MAAMC,WAAW,GAAId,KAAc,IAAyBA,KAAK,KAAKe,SAAS;AAEtF;;;;;;;;;;;;;;;;;AAAAzB,OAAA,CAAAwB,WAAA,GAAAA,WAAA;AAiBO,MAAME,cAAc,GAAOhB,KAAQ,IAAqCA,KAAK,KAAKe,SAAS;AAElG;;;;;;;;;;;;;;;;;AAAAzB,OAAA,CAAA0B,cAAA,GAAAA,cAAA;AAiBO,MAAMC,MAAM,GAAIjB,KAAc,IAAoBA,KAAK,KAAK,IAAI;AAEvE;;;;;;;;;;;;;;;;;AAAAV,OAAA,CAAA2B,MAAA,GAAAA,MAAA;AAiBO,MAAMC,SAAS,GAAOlB,KAAQ,IAAgCA,KAAK,KAAK,IAAI;AAEnF;;;;;;;;;;;;;;;;;AAAAV,OAAA,CAAA4B,SAAA,GAAAA,SAAA;AAiBO,MAAMC,OAAO,GAAwCC,CAAU,IAAiB,KAAK;AAE5F;;;;;;;;;;;;;;;;;;AAAA9B,OAAA,CAAA6B,OAAA,GAAAA,OAAA;AAkBO,MAAME,SAAS,GAA0CD,CAAC,IAAmB,IAAI;AAExF;AAAA9B,OAAA,CAAA+B,SAAA,GAAAA,SAAA;AACO,MAAMC,eAAe,GAAItB,KAAc,IAC5C,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI;AAE7C;;;;;;;;;;;;;;;;;;AAAAV,OAAA,CAAAgC,eAAA,GAAAA,eAAA;AAkBO,MAAMC,QAAQ,GAAIvB,KAAc,IAAsBsB,eAAe,CAACtB,KAAK,CAAC,IAAIY,UAAU,CAACZ,KAAK,CAAC;AAExG;;;;;;AAAAV,OAAA,CAAAiC,QAAA,GAAAA,QAAA;AAMO,MAAMC,WAAW,GAAAlC,OAAA,CAAAkC,WAAA,gBAepB,IAAAjC,cAAI,EACN,CAAC,EACD,CAAwBC,IAAa,EAAEiC,QAAW,KAChDF,QAAQ,CAAC/B,IAAI,CAAC,IAAKiC,QAAQ,IAAIjC,IAAK,CACvC;AAED;;;;;;;;;;;;;;;;;;;AAmBO,MAAMkC,QAAQ,GAAApC,OAAA,CAAAoC,QAAA,gBAyCjB,IAAAnC,cAAI,EACN,CAAC,EACD,CAAmBC,IAAa,EAAEmC,GAAM,KAA0BH,WAAW,CAAChC,IAAI,EAAE,MAAM,CAAC,IAAIA,IAAI,CAAC,MAAM,CAAC,KAAKmC,GAAG,CACpH;AAED;;;;;;;;;;;;;;;;;;AAkBO,MAAMC,UAAU,GAAO5B,KAAQ,IAA4CA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKe,SAAS;AAEvH;;;;;;;;;;;;;;;;;;AAAAzB,OAAA,CAAAsC,UAAA,GAAAA,UAAA;AAkBO,MAAMC,aAAa,GAAO7B,KAAQ,IAA8BA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKe,SAAS;AAE5G;;;;;;;;;;;;;;;;;AAAAzB,OAAA,CAAAuC,aAAA,GAAAA,aAAA;AAiBO,MAAMC,OAAO,GAAI9B,KAAc,IAAqBA,KAAK,YAAY+B,KAAK;AAEjF;;;;;;;;;;;;;;;;;AAAAzC,OAAA,CAAAwC,OAAA,GAAAA,OAAA;AAiBO,MAAME,YAAY,GAAIhC,KAAc,IAA0BA,KAAK,YAAYiC,UAAU;AAEhG;;;;;;;;;;;;;;;;;AAAA3C,OAAA,CAAA0C,YAAA,GAAAA,YAAA;AAiBO,MAAME,MAAM,GAAIlC,KAAc,IAAoBA,KAAK,YAAYmC,IAAI;AAE9E;;;;;;;;;;;;;;;;;;AAAA7C,OAAA,CAAA4C,MAAA,GAAAA,MAAA;AAkBO,MAAME,UAAU,GAAIpC,KAAc,IAAiCwB,WAAW,CAACxB,KAAK,EAAEqC,MAAM,CAACC,QAAQ,CAAC;AAE7G;;;;;;;;;;;;;;;;;;;;;AAAAhD,OAAA,CAAA8C,UAAA,GAAAA,UAAA;AAqBO,MAAMG,QAAQ,GAAIvC,KAAc,IACrCsB,eAAe,CAACtB,KAAK,CAAC,IAAI,CAACwC,KAAK,CAACC,OAAO,CAACzC,KAAK,CAAC;AAEjD;;;;;;;;;;;;;;;;;;;;AAAAV,OAAA,CAAAiD,QAAA,GAAAA,QAAA;AAoBO,MAAMG,gBAAgB,GAAApD,OAAA,CAAAoD,gBAAA,GAE8BH,QAAQ;AAEnE;;;;;;;;;;;;;;;AAeO,MAAMI,SAAS,GACpB3C,KAAc,IAEdwB,WAAW,CAACxB,KAAK,EAAE,MAAM,CAAC,IAAI,OAAO,IAAIA,KAAK,IAAIY,UAAU,CAACZ,KAAK,CAAC4C,IAAI,CAAC,IAAIhC,UAAU,CAACZ,KAAK,CAAC6C,KAAK,CAAC;AAErG;;;;AAAAvD,OAAA,CAAAqD,SAAA,GAAAA,SAAA;AAIO,MAAMG,aAAa,GACxB9C,KAAc,IACoBwB,WAAW,CAACxB,KAAK,EAAE,MAAM,CAAC,IAAIY,UAAU,CAACZ,KAAK,CAAC4C,IAAI,CAAC;AAExF;;;;;;;;;;;;;;;AAAAtD,OAAA,CAAAwD,aAAA,GAAAA,aAAA;AAeO,MAAMC,QAAQ,GAAI/C,KAAc,IAAsBA,KAAK,YAAYgD,MAAM;AAEpF;;;AAAA1D,OAAA,CAAAyD,QAAA,GAAAA,QAAA;AAGO,MAAME,OAAO,GAAA3D,OAAA,CAAA2D,OAAA,gBAiBhB,IAAA1D,cAAI,EACN,CAAC,EACD,CAA8B2D,EAAoB,EAAEC,EAAoB,KAAwBC,CAAC,IAC/FF,EAAE,CAACE,CAAC,CAAC,IAAID,EAAE,CAACC,CAAC,CAAC,CACjB;AAED;;;;AAIO,MAAMC,OAAO,GAClBA,CAAO7D,IAAkB,EAAE8D,IAAkB,KAC7C,CAAC,CAACF,CAAC,EAAE1D,CAAC,CAAC,KAAKF,IAAI,CAAC4D,CAAC,CAAC,IAAIE,IAAI,CAAC5D,CAAC,CAAC;AAEhC;;;;AAAAJ,OAAA,CAAA+D,OAAA,GAAAA,OAAA;AAIO,MAAME,GAAG,GACdC,UAAkC,IACH;EAC/B,OAAQC,EAAE,IAAI;IACZ,IAAIC,eAAe,GAAG,CAAC;IACvB,KAAK,MAAMC,CAAC,IAAIH,UAAU,EAAE;MAC1B,IAAIE,eAAe,IAAID,EAAE,CAAC5D,MAAM,EAAE;QAChC;MACF;MACA,IAAI8D,CAAC,CAACF,EAAE,CAACC,eAAe,CAAC,CAAC,KAAK,KAAK,EAAE;QACpC,OAAO,KAAK;MACd;MACAA,eAAe,EAAE;IACnB;IACA,OAAO,IAAI;EACb,CAAC;AACH,CAAC;AAED;;;;AAAApE,OAAA,CAAAiE,GAAA,GAAAA,GAAA;AAIO,MAAMK,WAAW,GAAGA,CACzBpE,IAAkB,EAClBgE,UAAkC,KAC2C;EAC7E,MAAMK,IAAI,GAAGN,GAAG,CAACC,UAAU,CAAC;EAC5B,OAAO,CAAC,CAACM,IAAI,EAAE,GAAGC,IAAI,CAAC,KAAKvE,IAAI,CAACsE,IAAI,CAAC,KAAK,KAAK,GAAG,KAAK,GAAGD,IAAI,CAACE,IAAI,CAAC;AACvE,CAAC;AAED;;;;;;;;;;;AAAAzE,OAAA,CAAAsE,WAAA,GAAAA,WAAA;AAWO,MAAMI,KAAK,GAiBdA,CAAC,GAAGC,QAAsC,KAAKV,GAAG,CAACU,QAAQ,CAAQ;AAEvE;;;;;;;;;AAAA3E,OAAA,CAAA0E,KAAA,GAAAA,KAAA;AASO,MAAME,MAAM,GAgB4BC,MAAS,IAAI;EAC1D,MAAMC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACD,MAAM,CAAC;EAChC,OAAQf,CAA0B,IAAI;IACpC,KAAK,MAAMkB,GAAG,IAAIF,IAAI,EAAE;MACtB,IAAI,CAACD,MAAM,CAACG,GAAG,CAAC,CAAClB,CAAC,CAACkB,GAAG,CAAU,CAAC,EAAE;QACjC,OAAO,KAAK;MACd;IACF;IACA,OAAO,IAAI;EACb,CAAC;AACH,CAAS;AAET;;;;;;;;;;;;;;;;;;AAAAhF,OAAA,CAAA4E,MAAA,GAAAA,MAAA;AAkBO,MAAMK,GAAG,GAAO/E,IAAkB,IAAoB4D,CAAC,IAAK,CAAC5D,IAAI,CAAC4D,CAAC,CAAC;AAE3E;;;;;;;;;;;;;;;;;;AAAA9D,OAAA,CAAAiF,GAAA,GAAAA,GAAA;AAkBO,MAAMC,EAAE,GAAAlF,OAAA,CAAAkF,EAAA,gBA6EX,IAAAjF,cAAI,EAAC,CAAC,EAAE,CAAIC,IAAkB,EAAE8D,IAAkB,KAAoBF,CAAC,IAAK5D,IAAI,CAAC4D,CAAC,CAAC,IAAIE,IAAI,CAACF,CAAC,CAAC,CAAC;AAEnG;;;;;;;;;;;;;;;;;;;;;AAqBO,MAAMqB,GAAG,GAAAnF,OAAA,CAAAmF,GAAA,gBAyFZ,IAAAlF,cAAI,EAAC,CAAC,EAAE,CAAIC,IAAkB,EAAE8D,IAAkB,KAAoBF,CAAC,IAAK5D,IAAI,CAAC4D,CAAC,CAAC,IAAIE,IAAI,CAACF,CAAC,CAAC,CAAC;AAEnG;;;;AAIO,MAAMsB,GAAG,GAAApF,OAAA,CAAAoF,GAAA,gBAWZ,IAAAnF,cAAI,EAAC,CAAC,EAAE,CAAIC,IAAkB,EAAE8D,IAAkB,KAAoBF,CAAC,IAAK5D,IAAI,CAAC4D,CAAC,CAAC,KAAKE,IAAI,CAACF,CAAC,CAAC,CAAC;AAEpG;;;;AAIO,MAAMuB,GAAG,GAAArF,OAAA,CAAAqF,GAAA,gBAWZ,IAAApF,cAAI,EAAC,CAAC,EAAE,CAAIC,IAAkB,EAAE8D,IAAkB,KAAoBF,CAAC,IAAK5D,IAAI,CAAC4D,CAAC,CAAC,KAAKE,IAAI,CAACF,CAAC,CAAC,CAAC;AAEpG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+CO,MAAMwB,OAAO,GAAAtF,OAAA,CAAAsF,OAAA,gBAiGhB,IAAArF,cAAI,EACN,CAAC,EACD,CAAIsF,UAAwB,EAAEC,UAAwB,KAAoB1B,CAAC,IAAKyB,UAAU,CAACzB,CAAC,CAAC,GAAG0B,UAAU,CAAC1B,CAAC,CAAC,GAAG,IAAI,CACrH;AAED;;;;AAIO,MAAM2B,GAAG,GAAAzF,OAAA,CAAAyF,GAAA,gBAWZ,IAAAxF,cAAI,EACN,CAAC,EACD,CAAIC,IAAkB,EAAE8D,IAAkB,KAAoBF,CAAC,IAAK,EAAE5D,IAAI,CAAC4D,CAAC,CAAC,IAAIE,IAAI,CAACF,CAAC,CAAC,CAAC,CAC1F;AAED;;;;AAIO,MAAM4B,IAAI,GAAA1F,OAAA,CAAA0F,IAAA,gBAWb,IAAAzF,cAAI,EACN,CAAC,EACD,CAAIC,IAAkB,EAAE8D,IAAkB,KAAoBF,CAAC,IAAK,EAAE5D,IAAI,CAAC4D,CAAC,CAAC,IAAIE,IAAI,CAACF,CAAC,CAAC,CAAC,CAC1F;AAED;;;;AAIO,MAAM6B,KAAK,GAAOzB,UAAkC,IAAoBJ,CAAI,IAAI;EACrF,KAAK,MAAMO,CAAC,IAAIH,UAAU,EAAE;IAC1B,IAAI,CAACG,CAAC,CAACP,CAAC,CAAC,EAAE;MACT,OAAO,KAAK;IACd;EACF;EACA,OAAO,IAAI;AACb,CAAC;AAED;;;;AAAA9D,OAAA,CAAA2F,KAAA,GAAAA,KAAA;AAIO,MAAMC,IAAI,GAAO1B,UAAkC,IAAoBJ,CAAC,IAAI;EACjF,KAAK,MAAMO,CAAC,IAAIH,UAAU,EAAE;IAC1B,IAAIG,CAAC,CAACP,CAAC,CAAC,EAAE;MACR,OAAO,IAAI;IACb;EACF;EACA,OAAO,KAAK;AACd,CAAC;AAAA9D,OAAA,CAAA4F,IAAA,GAAAA,IAAA", "ignoreList": []}