{"version": 3, "file": "Scheduler.js", "names": ["dual", "globalValue", "core", "PriorityBuckets", "buckets", "scheduleTask", "task", "priority", "length", "bucket", "undefined", "index", "push", "splice", "MixedScheduler", "maxNextTickBeforeTimer", "running", "tasks", "constructor", "starveInternal", "depth", "_", "to<PERSON>un", "i", "starve", "setTimeout", "Promise", "resolve", "then", "<PERSON><PERSON><PERSON>", "fiber", "currentOpCount", "getFiberRef", "currentMaxOpsBeforeYield", "currentSchedulingPriority", "defaultScheduler", "Symbol", "for", "SyncScheduler", "deferred", "flush", "ControlledScheduler", "step", "makeMatrix", "record", "sort", "p0", "p1", "scheduler", "defaultShouldYield", "make", "makeBatched", "callback", "tasksToRun", "timer", "ms", "timerBatched", "currentScheduler", "fiberRefUnsafeMake", "withScheduler", "self", "fiberRefLocally"], "sources": ["../../src/Scheduler.ts"], "sourcesContent": [null], "mappings": "AAAA;;;AAOA,SAASA,IAAI,QAAQ,eAAe;AACpC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,KAAKC,IAAI,MAAM,oBAAoB;AAiB1C;;;;AAIA,OAAM,MAAOC,eAAe;EAC1B;;;EAGOC,OAAO,GAA8B,EAAE;EAC9C;;;EAGAC,YAAYA,CAACC,IAAO,EAAEC,QAAgB;IACpC,MAAMC,MAAM,GAAG,IAAI,CAACJ,OAAO,CAACI,MAAM;IAClC,IAAIC,MAAM,GAAmCC,SAAS;IACtD,IAAIC,KAAK,GAAG,CAAC;IACb,OAAOA,KAAK,GAAGH,MAAM,EAAEG,KAAK,EAAE,EAAE;MAC9B,IAAI,IAAI,CAACP,OAAO,CAACO,KAAK,CAAC,CAAC,CAAC,CAAC,IAAIJ,QAAQ,EAAE;QACtCE,MAAM,GAAG,IAAI,CAACL,OAAO,CAACO,KAAK,CAAC;MAC9B,CAAC,MAAM;QACL;MACF;IACF;IACA,IAAIF,MAAM,IAAIA,MAAM,CAAC,CAAC,CAAC,KAAKF,QAAQ,EAAE;MACpCE,MAAM,CAAC,CAAC,CAAC,CAACG,IAAI,CAACN,IAAI,CAAC;IACtB,CAAC,MAAM,IAAIK,KAAK,KAAKH,MAAM,EAAE;MAC3B,IAAI,CAACJ,OAAO,CAACQ,IAAI,CAAC,CAACL,QAAQ,EAAE,CAACD,IAAI,CAAC,CAAC,CAAC;IACvC,CAAC,MAAM;MACL,IAAI,CAACF,OAAO,CAACS,MAAM,CAACF,KAAK,EAAE,CAAC,EAAE,CAACJ,QAAQ,EAAE,CAACD,IAAI,CAAC,CAAC,CAAC;IACnD;EACF;;AAGF;;;;AAIA,OAAM,MAAOQ,cAAc;EAcdC,sBAAA;EAbX;;;EAGAC,OAAO,GAAG,KAAK;EACf;;;EAGAC,KAAK,gBAAG,IAAId,eAAe,EAAE;EAE7Be;EACE;;;EAGSH,sBAA8B;IAA9B,KAAAA,sBAAsB,GAAtBA,sBAAsB;EAC9B;EAEH;;;EAGQI,cAAcA,CAACC,KAAa;IAClC,MAAMH,KAAK,GAAG,IAAI,CAACA,KAAK,CAACb,OAAO;IAChC,IAAI,CAACa,KAAK,CAACb,OAAO,GAAG,EAAE;IACvB,KAAK,MAAM,CAACiB,CAAC,EAAEC,KAAK,CAAC,IAAIL,KAAK,EAAE;MAC9B,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACd,MAAM,EAAEe,CAAC,EAAE,EAAE;QACrCD,KAAK,CAACC,CAAC,CAAC,EAAE;MACZ;IACF;IACA,IAAI,IAAI,CAACN,KAAK,CAACb,OAAO,CAACI,MAAM,KAAK,CAAC,EAAE;MACnC,IAAI,CAACQ,OAAO,GAAG,KAAK;IACtB,CAAC,MAAM;MACL,IAAI,CAACQ,MAAM,CAACJ,KAAK,CAAC;IACpB;EACF;EAEA;;;EAGQI,MAAMA,CAACJ,KAAK,GAAG,CAAC;IACtB,IAAIA,KAAK,IAAI,IAAI,CAACL,sBAAsB,EAAE;MACxCU,UAAU,CAAC,MAAM,IAAI,CAACN,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7C,CAAC,MAAM;MACLO,OAAO,CAACC,OAAO,CAAC,KAAK,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,IAAI,CAACT,cAAc,CAACC,KAAK,GAAG,CAAC,CAAC,CAAC;IACpE;EACF;EAEA;;;EAGAS,WAAWA,CAACC,KAAqC;IAC/C,OAAOA,KAAK,CAACC,cAAc,GAAGD,KAAK,CAACE,WAAW,CAAC9B,IAAI,CAAC+B,wBAAwB,CAAC,GAC1EH,KAAK,CAACE,WAAW,CAAC9B,IAAI,CAACgC,yBAAyB,CAAC,GACjD,KAAK;EACX;EAEA;;;EAGA7B,YAAYA,CAACC,IAAU,EAAEC,QAAgB;IACvC,IAAI,CAACU,KAAK,CAACZ,YAAY,CAACC,IAAI,EAAEC,QAAQ,CAAC;IACvC,IAAI,CAAC,IAAI,CAACS,OAAO,EAAE;MACjB,IAAI,CAACA,OAAO,GAAG,IAAI;MACnB,IAAI,CAACQ,MAAM,EAAE;IACf;EACF;;AAGF;;;;AAIA,OAAO,MAAMW,gBAAgB,gBAAclC,WAAW,eACpDmC,MAAM,CAACC,GAAG,CAAC,mCAAmC,CAAC,EAC/C,MAAM,IAAIvB,cAAc,CAAC,IAAI,CAAC,CAC/B;AAED;;;;AAIA,OAAM,MAAOwB,aAAa;EACxB;;;EAGArB,KAAK,gBAAG,IAAId,eAAe,EAAE;EAE7B;;;EAGAoC,QAAQ,GAAG,KAAK;EAEhB;;;EAGAlC,YAAYA,CAACC,IAAU,EAAEC,QAAgB;IACvC,IAAI,IAAI,CAACgC,QAAQ,EAAE;MACjBJ,gBAAgB,CAAC9B,YAAY,CAACC,IAAI,EAAEC,QAAQ,CAAC;IAC/C,CAAC,MAAM;MACL,IAAI,CAACU,KAAK,CAACZ,YAAY,CAACC,IAAI,EAAEC,QAAQ,CAAC;IACzC;EACF;EAEA;;;EAGAsB,WAAWA,CAACC,KAAqC;IAC/C,OAAOA,KAAK,CAACC,cAAc,GAAGD,KAAK,CAACE,WAAW,CAAC9B,IAAI,CAAC+B,wBAAwB,CAAC,GAC1EH,KAAK,CAACE,WAAW,CAAC9B,IAAI,CAACgC,yBAAyB,CAAC,GACjD,KAAK;EACX;EAEA;;;EAGAM,KAAKA,CAAA;IACH,OAAO,IAAI,CAACvB,KAAK,CAACb,OAAO,CAACI,MAAM,GAAG,CAAC,EAAE;MACpC,MAAMS,KAAK,GAAG,IAAI,CAACA,KAAK,CAACb,OAAO;MAChC,IAAI,CAACa,KAAK,CAACb,OAAO,GAAG,EAAE;MACvB,KAAK,MAAM,CAACiB,CAAC,EAAEC,KAAK,CAAC,IAAIL,KAAK,EAAE;QAC9B,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACd,MAAM,EAAEe,CAAC,EAAE,EAAE;UACrCD,KAAK,CAACC,CAAC,CAAC,EAAE;QACZ;MACF;IACF;IACA,IAAI,CAACgB,QAAQ,GAAG,IAAI;EACtB;;AAGF;;;;AAIA,OAAM,MAAOE,mBAAmB;EAC9B;;;EAGAxB,KAAK,gBAAG,IAAId,eAAe,EAAE;EAE7B;;;EAGAoC,QAAQ,GAAG,KAAK;EAEhB;;;EAGAlC,YAAYA,CAACC,IAAU,EAAEC,QAAgB;IACvC,IAAI,IAAI,CAACgC,QAAQ,EAAE;MACjBJ,gBAAgB,CAAC9B,YAAY,CAACC,IAAI,EAAEC,QAAQ,CAAC;IAC/C,CAAC,MAAM;MACL,IAAI,CAACU,KAAK,CAACZ,YAAY,CAACC,IAAI,EAAEC,QAAQ,CAAC;IACzC;EACF;EAEA;;;EAGAsB,WAAWA,CAACC,KAAqC;IAC/C,OAAOA,KAAK,CAACC,cAAc,GAAGD,KAAK,CAACE,WAAW,CAAC9B,IAAI,CAAC+B,wBAAwB,CAAC,GAC1EH,KAAK,CAACE,WAAW,CAAC9B,IAAI,CAACgC,yBAAyB,CAAC,GACjD,KAAK;EACX;EAEA;;;EAGAQ,IAAIA,CAAA;IACF,MAAMzB,KAAK,GAAG,IAAI,CAACA,KAAK,CAACb,OAAO;IAChC,IAAI,CAACa,KAAK,CAACb,OAAO,GAAG,EAAE;IACvB,KAAK,MAAM,CAACiB,CAAC,EAAEC,KAAK,CAAC,IAAIL,KAAK,EAAE;MAC9B,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACd,MAAM,EAAEe,CAAC,EAAE,EAAE;QACrCD,KAAK,CAACC,CAAC,CAAC,EAAE;MACZ;IACF;EACF;;AAGF;;;;AAIA,OAAO,MAAMoB,UAAU,GAAGA,CAAC,GAAGC,MAAkC,KAAe;EAC7E,MAAMjC,KAAK,GAAGiC,MAAM,CAACC,IAAI,CAAC,CAAC,CAACC,EAAE,CAAC,EAAE,CAACC,EAAE,CAAC,KAAKD,EAAE,GAAGC,EAAE,GAAG,CAAC,CAAC,GAAGD,EAAE,GAAGC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;EACzE,OAAO;IACLlB,WAAWA,CAACC,KAAK;MACf,KAAK,MAAMkB,SAAS,IAAIJ,MAAM,EAAE;QAC9B,MAAMrC,QAAQ,GAAGyC,SAAS,CAAC,CAAC,CAAC,CAACnB,WAAW,CAACC,KAAK,CAAC;QAChD,IAAIvB,QAAQ,KAAK,KAAK,EAAE;UACtB,OAAOA,QAAQ;QACjB;MACF;MACA,OAAO,KAAK;IACd,CAAC;IACDF,YAAYA,CAACC,IAAI,EAAEC,QAAQ;MACzB,IAAIyC,SAAS,GAA0BtC,SAAS;MAChD,KAAK,MAAMa,CAAC,IAAIZ,KAAK,EAAE;QACrB,IAAIJ,QAAQ,IAAIgB,CAAC,CAAC,CAAC,CAAC,EAAE;UACpByB,SAAS,GAAGzB,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,MAAM;UACL,OAAO,CAACyB,SAAS,IAAIb,gBAAgB,EAAE9B,YAAY,CAACC,IAAI,EAAEC,QAAQ,CAAC;QACrE;MACF;MACA,OAAO,CAACyC,SAAS,IAAIb,gBAAgB,EAAE9B,YAAY,CAACC,IAAI,EAAEC,QAAQ,CAAC;IACrE;GACD;AACH,CAAC;AAED;;;;AAIA,OAAO,MAAM0C,kBAAkB,GAA8BnB,KAAK,IAAI;EACpE,OAAOA,KAAK,CAACC,cAAc,GAAGD,KAAK,CAACE,WAAW,CAAC9B,IAAI,CAAC+B,wBAAwB,CAAC,GAC1EH,KAAK,CAACE,WAAW,CAAC9B,IAAI,CAACgC,yBAAyB,CAAC,GACjD,KAAK;AACX,CAAC;AAED;;;;AAIA,OAAO,MAAMgB,IAAI,GAAGA,CAClB7C,YAAuC,EACvCwB,WAAA,GAAwCoB,kBAAkB,MAC3C;EACf5C,YAAY;EACZwB;CACD,CAAC;AAEF;;;;AAIA,OAAO,MAAMsB,WAAW,GAAGA,CACzBC,QAAwC,EACxCvB,WAAA,GAAwCoB,kBAAkB,KACxD;EACF,IAAIjC,OAAO,GAAG,KAAK;EACnB,MAAMC,KAAK,GAAG,IAAId,eAAe,EAAE;EACnC,MAAMgB,cAAc,GAAGA,CAAA,KAAK;IAC1B,MAAMkC,UAAU,GAAGpC,KAAK,CAACb,OAAO;IAChCa,KAAK,CAACb,OAAO,GAAG,EAAE;IAClB,KAAK,MAAM,CAACiB,CAAC,EAAEC,KAAK,CAAC,IAAI+B,UAAU,EAAE;MACnC,KAAK,IAAI9B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACd,MAAM,EAAEe,CAAC,EAAE,EAAE;QACrCD,KAAK,CAACC,CAAC,CAAC,EAAE;MACZ;IACF;IACA,IAAIN,KAAK,CAACb,OAAO,CAACI,MAAM,KAAK,CAAC,EAAE;MAC9BQ,OAAO,GAAG,KAAK;IACjB,CAAC,MAAM;MACLQ,MAAM,EAAE;IACV;EACF,CAAC;EAED,MAAMA,MAAM,GAAGA,CAAA,KAAM4B,QAAQ,CAACjC,cAAc,CAAC;EAE7C,OAAO+B,IAAI,CAAC,CAAC5C,IAAI,EAAEC,QAAQ,KAAI;IAC7BU,KAAK,CAACZ,YAAY,CAACC,IAAI,EAAEC,QAAQ,CAAC;IAClC,IAAI,CAACS,OAAO,EAAE;MACZA,OAAO,GAAG,IAAI;MACdQ,MAAM,EAAE;IACV;EACF,CAAC,EAAEK,WAAW,CAAC;AACjB,CAAC;AAED;;;;AAIA,OAAO,MAAMyB,KAAK,GAAGA,CAACC,EAAU,EAAE1B,WAAA,GAAwCoB,kBAAkB,KAC1FC,IAAI,CAAE5C,IAAI,IAAKmB,UAAU,CAACnB,IAAI,EAAEiD,EAAE,CAAC,EAAE1B,WAAW,CAAC;AAEnD;;;;AAIA,OAAO,MAAM2B,YAAY,GAAGA,CAACD,EAAU,EAAE1B,WAAA,GAAwCoB,kBAAkB,KACjGE,WAAW,CAAE7C,IAAI,IAAKmB,UAAU,CAACnB,IAAI,EAAEiD,EAAE,CAAC,EAAE1B,WAAW,CAAC;AAE1D;AACA,OAAO,MAAM4B,gBAAgB,gBAAwBxD,WAAW,eAC9DmC,MAAM,CAACC,GAAG,CAAC,kCAAkC,CAAC,EAC9C,MAAMnC,IAAI,CAACwD,kBAAkB,CAACvB,gBAAgB,CAAC,CAChD;AAED;AACA,OAAO,MAAMwB,aAAa,gBAAG3D,IAAI,CAK/B,CAAC,EAAE,CAAC4D,IAAI,EAAEZ,SAAS,KAAK9C,IAAI,CAAC2D,eAAe,CAACD,IAAI,EAAEH,gBAAgB,EAAET,SAAS,CAAC,CAAC", "ignoreList": []}