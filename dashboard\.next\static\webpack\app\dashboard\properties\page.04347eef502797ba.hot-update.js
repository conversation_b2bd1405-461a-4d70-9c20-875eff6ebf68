"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/properties/page",{

/***/ "(app-pages-browser)/./app/dashboard/properties/page.tsx":
/*!*******************************************!*\
  !*** ./app/dashboard/properties/page.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PropertiesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Grid,List,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Grid,List,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Grid,List,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Grid,List,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Grid,List,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Grid,List,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Grid,List,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useSimpleLanguage */ \"(app-pages-browser)/./hooks/useSimpleLanguage.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction PropertiesPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { language } = (0,_hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_8__.useSimpleLanguage)();\n    const [properties, setProperties] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('grid');\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [filterType, setFilterType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('الكل');\n    const [filterStatus, setFilterStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('الكل');\n    // Removed dialog states - now using dedicated pages\n    // Fetch properties and stats\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PropertiesPage.useEffect\": ()=>{\n            fetchProperties();\n            fetchStats();\n        }\n    }[\"PropertiesPage.useEffect\"], [\n        searchTerm,\n        filterType,\n        filterStatus\n    ]);\n    const fetchProperties = async ()=>{\n        try {\n            setLoading(true);\n            const params = new URLSearchParams();\n            if (searchTerm) params.append('search', searchTerm);\n            if (filterType && filterType !== 'الكل' && filterType !== 'ALL') params.append('type', filterType);\n            if (filterStatus && filterStatus !== 'الكل' && filterStatus !== 'ALL') params.append('status', filterStatus);\n            params.append('isActive', 'true');\n            const response = await fetch(\"/api/v1/properties?\".concat(params));\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success) {\n                    setProperties(data.data.properties);\n                }\n            } else {\n                // Fallback to mock data if backend is not available\n                console.log('Backend not available, using mock data');\n                setProperties([\n                    {\n                        id: '1',\n                        title: 'Luxury Villa in Dubai Marina',\n                        titleAr: 'فيلا فاخرة في دبي مارينا',\n                        description: 'Beautiful 4-bedroom villa with sea view',\n                        descriptionAr: 'فيلا جميلة من 4 غرف نوم مع إطلالة على البحر',\n                        price: 2500000,\n                        currency: 'AED',\n                        type: 'VILLA',\n                        status: 'AVAILABLE',\n                        bedrooms: 4,\n                        bathrooms: 3,\n                        area: 350,\n                        location: 'Dubai Marina',\n                        locationAr: 'دبي مارينا',\n                        address: '123 Marina Walk',\n                        addressAr: '123 ممشى المارينا',\n                        city: 'Dubai',\n                        cityAr: 'دبي',\n                        images: [\n                            '/placeholder.jpg'\n                        ],\n                        features: [\n                            'Swimming Pool',\n                            'Gym',\n                            'Parking'\n                        ],\n                        featuresAr: [\n                            'مسبح',\n                            'صالة رياضية',\n                            'موقف سيارات'\n                        ],\n                        amenities: [\n                            '24/7 Security',\n                            'Concierge'\n                        ],\n                        amenitiesAr: [\n                            'أمن 24/7',\n                            'خدمة الكونسيرج'\n                        ],\n                        isFeatured: true,\n                        isActive: true,\n                        viewCount: 125,\n                        createdAt: new Date().toISOString()\n                    },\n                    {\n                        id: '2',\n                        title: 'Modern Apartment in Downtown',\n                        titleAr: 'شقة حديثة في وسط المدينة',\n                        description: 'Spacious 2-bedroom apartment',\n                        descriptionAr: 'شقة واسعة من غرفتي نوم',\n                        price: 1200000,\n                        currency: 'AED',\n                        type: 'APARTMENT',\n                        status: 'AVAILABLE',\n                        bedrooms: 2,\n                        bathrooms: 2,\n                        area: 120,\n                        location: 'Downtown Dubai',\n                        locationAr: 'وسط مدينة دبي',\n                        address: '456 Sheikh Zayed Road',\n                        addressAr: '456 شارع الشيخ زايد',\n                        city: 'Dubai',\n                        cityAr: 'دبي',\n                        images: [\n                            '/placeholder.jpg'\n                        ],\n                        features: [\n                            'Balcony',\n                            'Built-in Wardrobes'\n                        ],\n                        featuresAr: [\n                            'شرفة',\n                            'خزائن مدمجة'\n                        ],\n                        amenities: [\n                            'Gym',\n                            'Pool'\n                        ],\n                        amenitiesAr: [\n                            'صالة رياضية',\n                            'مسبح'\n                        ],\n                        isFeatured: false,\n                        isActive: true,\n                        viewCount: 89,\n                        createdAt: new Date().toISOString()\n                    }\n                ]);\n            }\n        } catch (error) {\n            console.error('Error fetching properties:', error);\n            // Fallback to empty array on error\n            setProperties([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchStats = async ()=>{\n        try {\n            const response = await fetch('/api/v1/properties/stats');\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success) {\n                    setStats(data.data);\n                }\n            } else {\n                // Fallback to mock stats if backend is not available\n                console.log('Backend not available, using mock stats');\n                setStats({\n                    total: 2,\n                    available: 2,\n                    sold: 0,\n                    rented: 0,\n                    featured: 1,\n                    byType: {\n                        APARTMENT: 1,\n                        VILLA: 1,\n                        TOWNHOUSE: 0,\n                        PENTHOUSE: 0,\n                        STUDIO: 0,\n                        OFFICE: 0,\n                        SHOP: 0,\n                        WAREHOUSE: 0,\n                        LAND: 0,\n                        BUILDING: 0\n                    }\n                });\n            }\n        } catch (error) {\n            console.error('Error fetching stats:', error);\n            // Fallback to default stats on error\n            setStats({\n                total: 0,\n                available: 0,\n                sold: 0,\n                rented: 0,\n                featured: 0,\n                byType: {\n                    APARTMENT: 0,\n                    VILLA: 0,\n                    TOWNHOUSE: 0,\n                    PENTHOUSE: 0,\n                    STUDIO: 0,\n                    OFFICE: 0,\n                    SHOP: 0,\n                    WAREHOUSE: 0,\n                    LAND: 0,\n                    BUILDING: 0\n                }\n            });\n        }\n    };\n    const handleDeleteProperty = async (id)=>{\n        if (!confirm(language === 'ar' ? 'هل أنت متأكد من حذف هذا العقار؟' : 'Are you sure you want to delete this property?')) {\n            return;\n        }\n        try {\n            const response = await fetch(\"/api/v1/properties/\".concat(id), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                fetchProperties();\n                fetchStats();\n            }\n        } catch (error) {\n            console.error('Error deleting property:', error);\n        }\n    };\n    const formatPrice = (price, currency)=>{\n        return new Intl.NumberFormat(language === 'ar' ? 'ar-AE' : 'en-US', {\n            style: 'currency',\n            currency: currency,\n            minimumFractionDigits: 0\n        }).format(price);\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'AVAILABLE':\n                return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';\n            case 'SOLD':\n                return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';\n            case 'RENTED':\n                return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';\n            case 'RESERVED':\n                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';\n            default:\n                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';\n        }\n    };\n    const getPropertyTitle = (property)=>{\n        return language === 'ar' && property.titleAr ? property.titleAr : property.title;\n    };\n    const getPropertyLocation = (property)=>{\n        return language === 'ar' && property.locationAr ? property.locationAr : property.location;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 dark:text-white\",\n                                children: language === 'ar' ? 'العقارات' : 'Properties'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 dark:text-gray-400\",\n                                children: language === 'ar' ? 'إدارة العقارات والممتلكات' : 'Manage properties and real estate'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: ()=>router.push('/dashboard/properties/create'),\n                        className: \"bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2 shadow-lg hover:shadow-xl transition-all duration-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 11\n                            }, this),\n                            language === 'ar' ? 'إضافة عقار' : 'Add Property'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                lineNumber: 271,\n                columnNumber: 7\n            }, this),\n            stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                    children: language === 'ar' ? 'إجمالي العقارات' : 'Total Properties'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: stats.total\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                    children: language === 'ar' ? 'متاح' : 'Available'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-green-600\",\n                                    children: stats.available\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                    children: language === 'ar' ? 'مباع' : 'Sold'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-red-600\",\n                                    children: stats.sold\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 312,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                    children: language === 'ar' ? 'مؤجر' : 'Rented'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-blue-600\",\n                                    children: stats.rented\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                    children: language === 'ar' ? 'مميز' : 'Featured'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-purple-600\",\n                                    children: stats.featured\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                lineNumber: 291,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                placeholder: language === 'ar' ? 'البحث في العقارات...' : 'Search properties...',\n                                value: searchTerm,\n                                onChange: (e)=>setSearchTerm(e.target.value),\n                                className: \"pl-10\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                        value: filterType,\n                        onValueChange: setFilterType,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                className: \"w-full sm:w-48\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                    placeholder: language === 'ar' ? 'نوع العقار' : 'Property Type'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                        value: \"ALL\",\n                                        children: language === 'ar' ? 'جميع الأنواع' : 'All Types'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                        value: \"APARTMENT\",\n                                        children: language === 'ar' ? 'شقة' : 'Apartment'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                        value: \"VILLA\",\n                                        children: language === 'ar' ? 'فيلا' : 'Villa'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                        value: \"TOWNHOUSE\",\n                                        children: language === 'ar' ? 'تاون هاوس' : 'Townhouse'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                        value: \"OFFICE\",\n                                        children: language === 'ar' ? 'مكتب' : 'Office'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                        value: filterStatus,\n                        onValueChange: setFilterStatus,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                className: \"w-full sm:w-48\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                    placeholder: language === 'ar' ? 'الحالة' : 'Status'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                        value: \"ALL\",\n                                        children: language === 'ar' ? 'جميع الحالات' : 'All Statuses'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                        value: \"AVAILABLE\",\n                                        children: language === 'ar' ? 'متاح' : 'Available'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                        value: \"SOLD\",\n                                        children: language === 'ar' ? 'مباع' : 'Sold'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                        value: \"RENTED\",\n                                        children: language === 'ar' ? 'مؤجر' : 'Rented'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 368,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: viewMode === 'grid' ? 'default' : 'outline',\n                                size: \"sm\",\n                                onClick: ()=>setViewMode('grid'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 385,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: viewMode === 'list' ? 'default' : 'outline',\n                                size: \"sm\",\n                                onClick: ()=>setViewMode('list'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 379,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                lineNumber: 346,\n                columnNumber: 7\n            }, this),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 dark:border-white\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                    lineNumber: 400,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                lineNumber: 399,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4',\n                children: properties.map((property)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"overflow-hidden hover:shadow-lg transition-shadow\",\n                        children: viewMode === 'grid' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                property.images.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative h-48 bg-gray-200 dark:bg-gray-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: property.images[0],\n                                            alt: getPropertyTitle(property),\n                                            className: \"w-full h-full object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 23\n                                        }, this),\n                                        property.isFeatured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                            className: \"absolute top-2 left-2 bg-purple-600\",\n                                            children: language === 'ar' ? 'مميز' : 'Featured'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 25\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 409,\n                                    columnNumber: 21\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-lg truncate\",\n                                                    children: getPropertyTitle(property)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                    className: getStatusColor(property.status),\n                                                    children: property.status\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-400 text-sm mb-2\",\n                                            children: getPropertyLocation(property)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xl font-bold text-blue-600\",\n                                                    children: formatPrice(property.price, property.currency)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2 text-sm text-gray-500\",\n                                                    children: [\n                                                        property.bedrooms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                property.bedrooms,\n                                                                \" \",\n                                                                language === 'ar' ? 'غرف' : 'bed'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                            lineNumber: 437,\n                                                            columnNumber: 47\n                                                        }, this),\n                                                        property.bathrooms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                property.bathrooms,\n                                                                \" \",\n                                                                language === 'ar' ? 'حمام' : 'bath'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                            lineNumber: 438,\n                                                            columnNumber: 48\n                                                        }, this),\n                                                        property.area && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                property.area,\n                                                                \"m\\xb2\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                            lineNumber: 439,\n                                                            columnNumber: 43\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            size: \"sm\",\n                                                            variant: \"outline\",\n                                                            onClick: ()=>setSelectedProperty(property),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 445,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            size: \"sm\",\n                                                            variant: \"outline\",\n                                                            onClick: ()=>router.push(\"/dashboard/properties/edit/\".concat(property.id)),\n                                                            className: \"hover:bg-blue-50 hover:border-blue-300 transition-colors\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 453,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                            lineNumber: 447,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            size: \"sm\",\n                                                            variant: \"outline\",\n                                                            onClick: ()=>handleDeleteProperty(property.id),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 456,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                            lineNumber: 455,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: [\n                                                        language === 'ar' ? 'المشاهدات:' : 'Views:',\n                                                        \" \",\n                                                        property.viewCount\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 422,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-4\",\n                                children: [\n                                    property.images.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative w-24 h-24 bg-gray-200 dark:bg-gray-700 rounded\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: property.images[0],\n                                            alt: getPropertyTitle(property),\n                                            className: \"w-full h-full object-cover rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                            lineNumber: 470,\n                                            columnNumber: 25\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 23\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-lg\",\n                                                        children: getPropertyTitle(property)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                className: getStatusColor(property.status),\n                                                                children: property.status\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 481,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            property.isFeatured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                className: \"bg-purple-600\",\n                                                                children: language === 'ar' ? 'مميز' : 'Featured'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 485,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 dark:text-gray-400 text-sm mb-2\",\n                                                children: getPropertyLocation(property)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xl font-bold text-blue-600\",\n                                                        children: formatPrice(property.price, property.currency)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                        lineNumber: 495,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-4 text-sm text-gray-500\",\n                                                        children: [\n                                                            property.bedrooms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    property.bedrooms,\n                                                                    \" \",\n                                                                    language === 'ar' ? 'غرف' : 'bed'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 499,\n                                                                columnNumber: 49\n                                                            }, this),\n                                                            property.bathrooms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    property.bathrooms,\n                                                                    \" \",\n                                                                    language === 'ar' ? 'حمام' : 'bath'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 500,\n                                                                columnNumber: 50\n                                                            }, this),\n                                                            property.area && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    property.area,\n                                                                    \"m\\xb2\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 501,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    language === 'ar' ? 'المشاهدات:' : 'Views:',\n                                                                    \" \",\n                                                                    property.viewCount\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 502,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                        lineNumber: 498,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>setSelectedProperty(property),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                    lineNumber: 506,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 505,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>router.push(\"/dashboard/properties/edit/\".concat(property.id)),\n                                                                className: \"hover:bg-blue-50 hover:border-blue-300 transition-colors\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                    lineNumber: 514,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 508,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleDeleteProperty(property.id),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                    lineNumber: 517,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 516,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                        lineNumber: 504,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                lineNumber: 494,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 477,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 467,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                            lineNumber: 466,\n                            columnNumber: 17\n                        }, this)\n                    }, property.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 405,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                lineNumber: 403,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n        lineNumber: 269,\n        columnNumber: 5\n    }, this);\n}\n_s(PropertiesPage, \"fSK+ki2zuOH8XoJ/w9r3SeZEO7o=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_8__.useSimpleLanguage\n    ];\n});\n_c = PropertiesPage;\nvar _c;\n$RefreshReg$(_c, \"PropertiesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/properties/page.tsx\n"));

/***/ })

});