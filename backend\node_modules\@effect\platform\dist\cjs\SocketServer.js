"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.SocketServerError = exports.SocketServer = exports.ErrorTypeId = void 0;
var Context = _interopRequireWildcard(require("effect/Context"));
var Data = _interopRequireWildcard(require("effect/Data"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
/**
 * @since 1.0.0
 */

/**
 * @since 1.0.0
 * @category tags
 */
class SocketServer extends /*#__PURE__*/Context.Tag("@effect/platform/SocketServer")() {}
/**
 * @since 1.0.0
 * @category errors
 */
exports.SocketServer = SocketServer;
const ErrorTypeId = exports.ErrorTypeId = /*#__PURE__*/Symbol.for("@effect/platform/SocketServer/SocketServerError");
/**
 * @since 1.0.0
 * @category errors
 */
class SocketServerError extends /*#__PURE__*/Data.TaggedError("SocketServerError") {
  /**
   * @since 1.0.0
   */
  [ErrorTypeId] = ErrorTypeId;
  /**
   * @since 1.0.0
   */
  get message() {
    return this.reason;
  }
}
exports.SocketServerError = SocketServerError;
//# sourceMappingURL=SocketServer.js.map