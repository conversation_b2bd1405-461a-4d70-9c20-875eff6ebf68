import * as _uploadthing_shared from '@uploadthing/shared';
import { UploadThingError } from '@uploadthing/shared';
import * as effect_Cause from 'effect/Cause';
import * as effect_ConfigError from 'effect/ConfigError';
import * as _effect_platform_HttpServerError from '@effect/platform/HttpServerError';
import { HttpRouter, HttpBody, HttpClient } from '@effect/platform';
import * as Context from 'effect/Context';
import * as Effect from 'effect/Effect';
import { RouteHandlerOptions, FileRouter } from '../../types/index.js';

declare const AdapterArguments_base: Context.TagClass<AdapterArguments, "uploadthing/AdapterArguments", Record<string, unknown>>;
declare class AdapterArguments extends AdapterArguments_base {
}
/**
 * Create a request handler adapter for any framework or server library.
 * Refer to the existing adapters for examples on how to use this function.
 * @public
 *
 * @param makeAdapterArgs - Function that takes the args from your framework and returns an Effect that resolves to the adapter args.
 * These args are passed to the `.middleware`, `.onUploadComplete`, and `.onUploadError` hooks.
 * @param toRequest - Function that takes the args from your framework and returns an Effect that resolves to a web Request object.
 * @param opts - The router config and other options that are normally passed to `createRequestHandler` of official adapters
 * @param beAdapter - [Optional] The adapter name of the adapter, used for telemetry purposes
 * @returns A function that takes the args from your framework and returns a promise that resolves to a Response object.
 */
declare const makeAdapterHandler: <Args extends any[], AdapterArgs extends Record<string, unknown>>(makeAdapterArgs: (...args: Args) => Effect.Effect<AdapterArgs>, toRequest: (...args: Args) => Effect.Effect<Request>, opts: RouteHandlerOptions<FileRouter>, beAdapter?: string) => ((...args: Args) => Promise<Response>);
declare const createRequestHandler: <TRouter extends FileRouter>(opts: RouteHandlerOptions<TRouter>, beAdapter: string) => Effect.Effect<HttpRouter.HttpRouter<HttpBody.HttpBodyError | _effect_platform_HttpServerError.RequestError | effect_ConfigError.ConfigError | effect_Cause.NoSuchElementException, AdapterArguments | HttpClient.HttpClient>, UploadThingError<{
    message: string;
}> | _uploadthing_shared.InvalidRouteConfigError | effect_ConfigError.ConfigError, never>;

export { AdapterArguments, createRequestHandler, makeAdapterHandler };
