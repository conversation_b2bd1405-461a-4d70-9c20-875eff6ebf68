var S = require('effect/Schema');
var shared = require('@uploadthing/shared');

function _interopNamespace(e) {
  if (e && e.__esModule) return e;
  var n = Object.create(null);
  if (e) {
    Object.keys(e).forEach(function (k) {
      if (k !== 'default') {
        var d = Object.getOwnPropertyDescriptor(e, k);
        Object.defineProperty(n, k, d.get ? d : {
          enumerable: true,
          get: function () { return e[k]; }
        });
      }
    });
  }
  n.default = e;
  return n;
}

var S__namespace = /*#__PURE__*/_interopNamespace(S);

const ContentDispositionSchema = S__namespace.Literal(...shared.ValidContentDispositions);
const ACLSchema = S__namespace.Literal(...shared.ValidACLs);
/**
 * Valid options for the `?actionType` query param
 */ const ActionType = S__namespace.Literal("upload");
/**
 * Valid options for the `uploadthing-hook` header
 * for requests coming from UT server
 */ const UploadThingHook = S__namespace.Literal("callback", "error");
/**
 * =============================================================================
 * =========================== Configuration ===================================
 * =============================================================================
 */ const DecodeString = S__namespace.transform(S__namespace.Uint8ArrayFromSelf, S__namespace.String, {
    decode: (data)=>new TextDecoder().decode(data),
    encode: (data)=>new TextEncoder().encode(data)
});
const ParsedToken = S__namespace.Struct({
    apiKey: S__namespace.Redacted(S__namespace.String.pipe(S__namespace.startsWith("sk_"))),
    appId: S__namespace.String,
    regions: S__namespace.NonEmptyArray(S__namespace.String),
    ingestHost: S__namespace.String.pipe(S__namespace.optionalWith({
        default: ()=>"ingest.uploadthing.com"
    }))
});
const UploadThingToken = S__namespace.Uint8ArrayFromBase64.pipe(S__namespace.compose(DecodeString), S__namespace.compose(S__namespace.parseJson(ParsedToken)));
/**
 * =============================================================================
 * ======================== File Type Hierarchy ===============================
 * =============================================================================
 */ /**
 * Properties from the web File object, this is what the client sends when initiating an upload
 */ class FileUploadData extends S__namespace.Class("FileUploadData")({
    name: S__namespace.String,
    size: S__namespace.Number,
    type: S__namespace.String,
    lastModified: S__namespace.Number.pipe(S__namespace.optional)
}) {
}
/**
 * `.middleware()` can add a customId to the incoming file data
 */ class FileUploadDataWithCustomId extends FileUploadData.extend("FileUploadDataWithCustomId")({
    customId: S__namespace.NullOr(S__namespace.String)
}) {
}
/**
 * When files are uploaded, we get back
 * - a key
 * - URLs for the file
 * - the hash (md5-hex) of the uploaded file's contents
 */ class UploadedFileData extends FileUploadDataWithCustomId.extend("UploadedFileData")({
    key: S__namespace.String,
    /**
   * @deprecated
   * This field will be removed in uploadthing v9. Use `ufsUrl` instead.
   */ url: S__namespace.String,
    /**
   * @deprecated
   * This field will be removed in uploadthing v9. Use `ufsUrl` instead.
   */ appUrl: S__namespace.String,
    ufsUrl: S__namespace.String,
    fileHash: S__namespace.String
}) {
}
/**
 * =============================================================================
 * ======================== Server Response Schemas ============================
 * =============================================================================
 */ class NewPresignedUrl extends S__namespace.Class("NewPresignedUrl")({
    url: S__namespace.String,
    key: S__namespace.String,
    customId: S__namespace.NullOr(S__namespace.String),
    name: S__namespace.String
}) {
}
class MetadataFetchStreamPart extends S__namespace.Class("MetadataFetchStreamPart")({
    payload: S__namespace.String,
    signature: S__namespace.String,
    hook: UploadThingHook
}) {
}
class MetadataFetchResponse extends S__namespace.Class("MetadataFetchResponse")({
    ok: S__namespace.Boolean
}) {
}
class CallbackResultResponse extends S__namespace.Class("CallbackResultResponse")({
    ok: S__namespace.Boolean
}) {
}
/**
 * =============================================================================
 * ======================== Client Action Payloads ============================
 * =============================================================================
 */ class UploadActionPayload extends S__namespace.Class("UploadActionPayload")({
    files: S__namespace.Array(FileUploadData),
    input: S__namespace.Unknown
}) {
}

exports.ACLSchema = ACLSchema;
exports.ActionType = ActionType;
exports.CallbackResultResponse = CallbackResultResponse;
exports.ContentDispositionSchema = ContentDispositionSchema;
exports.FileUploadData = FileUploadData;
exports.FileUploadDataWithCustomId = FileUploadDataWithCustomId;
exports.MetadataFetchResponse = MetadataFetchResponse;
exports.MetadataFetchStreamPart = MetadataFetchStreamPart;
exports.NewPresignedUrl = NewPresignedUrl;
exports.ParsedToken = ParsedToken;
exports.UploadActionPayload = UploadActionPayload;
exports.UploadThingHook = UploadThingHook;
exports.UploadThingToken = UploadThingToken;
exports.UploadedFileData = UploadedFileData;
