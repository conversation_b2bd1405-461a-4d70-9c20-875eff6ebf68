{"version": 3, "file": "multipart.js", "names": ["CT", "_interopRequireWildcard", "require", "HP", "Search", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "prototype", "hasOwnProperty", "call", "i", "set", "State", "errInvalidDisposition", "_tag", "errEnd<PERSON>otReached", "errMaxParts", "limit", "errMaxTotalSize", "errMaxPartSize", "errMaxFieldSize", "constCR", "TextEncoder", "encode", "defaultIsFile", "info", "filename", "undefined", "contentType", "parseBoundary", "headers", "parse", "parameters", "boundary", "noopOnChunk", "_chunk", "make", "onFile", "onPart", "onField", "onError", "onDone", "isFile", "maxParts", "Infinity", "maxTotalSize", "maxPartSize", "maxFieldSize", "write", "end", "state", "index", "parts", "onChunk", "headerSkip", "partSize", "totalSize", "fieldChunks", "fieldSize", "<PERSON><PERSON><PERSON>", "body", "<PERSON><PERSON><PERSON><PERSON>", "split", "chunk", "length", "buf", "Uint8Array", "offset", "result", "error", "contentDisposition", "value", "encodedFilename", "decodeURIComponent", "name", "contentTypeParameters", "contentDispositionParameters", "endPosition", "subarray", "push", "utf8Decoder", "TextDecoder", "getDecoder", "charset", "decodeField", "decode"], "sources": ["../../../src/internal/multipart.ts"], "sourcesContent": [null], "mappings": ";;;;;;;;AACA,IAAAA,EAAA,gBAAAC,uBAAA,eAAAC,OAAA;AACA,IAAAC,EAAA,gBAAAF,uBAAA,eAAAC,OAAA;AACA,IAAAE,MAAA,gBAAAH,uBAAA,eAAAC,OAAA;AAAqC,SAAAG,yBAAAC,CAAA;EAAA,yBAAAC,OAAA;EAAA,IAAAC,CAAA,OAAAD,OAAA;IAAAE,CAAA,OAAAF,OAAA;EAAA,QAAAF,wBAAA,YAAAA,CAAAC,CAAA;IAAA,OAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA;EAAA,GAAAF,CAAA;AAAA;AAAA,SAAAL,wBAAAK,CAAA,EAAAE,CAAA;EAAA,KAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA;EAAA,aAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA;IAAAK,OAAA,EAAAL;EAAA;EAAA,IAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA;EAAA,IAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA;EAAA,IAAAQ,CAAA;MAAAC,SAAA;IAAA;IAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA;EAAA,SAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAjB,CAAA,EAAAc,CAAA;IAAA,IAAAI,CAAA,GAAAR,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA;IAAAI,CAAA,KAAAA,CAAA,CAAAX,GAAA,IAAAW,CAAA,CAAAC,GAAA,IAAAR,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAI,CAAA,IAAAV,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA;EAAA;EAAA,OAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAgB,GAAA,CAAAnB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAAA;AAErC,IAAWY,KAGV;AAHD,WAAWA,KAAK;EACdA,KAAA,CAAAA,KAAA,4BAAO;EACPA,KAAA,CAAAA,KAAA,sBAAI;AACN,CAAC,EAHUA,KAAK,KAALA,KAAK;AAKhB,MAAMC,qBAAqB,GAAmB;EAAEC,IAAI,EAAE;AAAoB,CAAE;AAC5E,MAAMC,gBAAgB,GAAmB;EAAED,IAAI,EAAE;AAAe,CAAE;AAClE,MAAME,WAAW,GAAmB;EAAEF,IAAI,EAAE,cAAc;EAAEG,KAAK,EAAE;AAAU,CAAE;AAC/E,MAAMC,eAAe,GAAmB;EACtCJ,IAAI,EAAE,cAAc;EACpBG,KAAK,EAAE;CACR;AACD,MAAME,cAAc,GAAmB;EACrCL,IAAI,EAAE,cAAc;EACpBG,KAAK,EAAE;CACR;AACD,MAAMG,eAAe,GAAmB;EACtCN,IAAI,EAAE,cAAc;EACpBG,KAAK,EAAE;CACR;AAED,MAAMI,OAAO,gBAAG,IAAIC,WAAW,EAAE,CAACC,MAAM,CAAC,MAAM,CAAC;AAE1C,SAAUC,aAAaA,CAACC,IAAc;EAC1C,OACEA,IAAI,CAACC,QAAQ,KAAKC,SAAS,IAC3BF,IAAI,CAACG,WAAW,KAAK,0BAA0B;AAEnD;AAEA,SAASC,aAAaA,CAACC,OAA+B;EACpD,MAAMF,WAAW,GAAG1C,EAAE,CAAC6C,KAAK,CAACD,OAAO,CAAC,cAAc,CAAC,CAAC;EACrD,OAAOF,WAAW,CAACI,UAAU,CAACC,QAAQ;AACxC;AAEA,SAASC,WAAWA,CAACC,MAAyB,GAAG;AAE3C,SAAUC,IAAIA,CAAC;EACnBN,OAAO;EACPO,MAAM,EAAEC,MAAM;EACdC,OAAO;EACPC,OAAO;EACPC,MAAM;EACNC,MAAM,GAAGlB,aAAa;EACtBmB,QAAQ,GAAGC,QAAQ;EACnBC,YAAY,GAAGD,QAAQ;EACvBE,WAAW,GAAGF,QAAQ;EACtBG,YAAY,GAAG,IAAI,GAAG;AAAI,CACnB;EACP,MAAMd,QAAQ,GAAGJ,aAAa,CAACC,OAAO,CAAC;EACvC,IAAIG,QAAQ,KAAKN,SAAS,EAAE;IAC1Ba,OAAO,CAAC;MAAE1B,IAAI,EAAE;IAAiB,CAAE,CAAC;IACpC,OAAO;MACLkC,KAAK,EAAEd,WAAW;MAClBe,GAAGA,CAAA,GAAI;KACR;;EAGH,MAAMC,KAAK,GAAG;IACZA,KAAK,EAAEtC,KAAK,CAACkB,OAAO;IACpBqB,KAAK,EAAE,CAAC;IACRC,KAAK,EAAE,CAAC;IACRC,OAAO,EAAEnB,WAAW;IACpBT,IAAI,EAAEE,SAA4B;IAClC2B,UAAU,EAAE,CAAC;IACbC,QAAQ,EAAE,CAAC;IACXC,SAAS,EAAE,CAAC;IACZd,MAAM,EAAE,KAAK;IACbe,WAAW,EAAE,EAAuB;IACpCC,SAAS,EAAE;GACZ;EAED,SAASC,QAAQA,CAAA;IACfT,KAAK,CAACA,KAAK,GAAGtC,KAAK,CAACgD,IAAI;IACxBV,KAAK,CAACR,MAAM,GAAG,IAAI;IACnBQ,KAAK,CAACG,OAAO,GAAGnB,WAAW;EAC7B;EAEA,MAAM2B,YAAY,GAAGxE,EAAE,CAAC+C,IAAI,EAAE;EAE9B,MAAM0B,KAAK,GAAGxE,MAAM,CAAC8C,IAAI,CACvB,SAASH,QAAQ,EAAE,EACnB,UAAUkB,KAAK,EAAEY,KAAK;IACpB,IAAIZ,KAAK,KAAK,CAAC,EAAE;MACf;MACAQ,QAAQ,EAAE;MACV;KACD,MAAM,IAAIR,KAAK,KAAKD,KAAK,CAACC,KAAK,EAAE;MAChC,IAAID,KAAK,CAACC,KAAK,GAAG,CAAC,EAAE;QACnB,IAAID,KAAK,CAACR,MAAM,EAAE;UAChBQ,KAAK,CAACG,OAAO,CAAC,IAAI,CAAC;UACnBH,KAAK,CAACK,QAAQ,GAAG,CAAC;SACnB,MAAM;UACL,IAAIL,KAAK,CAACO,WAAW,CAACO,MAAM,KAAK,CAAC,EAAE;YAClCzB,OAAO,CAACW,KAAK,CAACzB,IAAI,EAAEyB,KAAK,CAACO,WAAW,CAAC,CAAC,CAAC,CAAC;WAC1C,MAAM;YACL,MAAMQ,GAAG,GAAG,IAAIC,UAAU,CAAChB,KAAK,CAACQ,SAAS,CAAC;YAC3C,IAAIS,MAAM,GAAG,CAAC;YACd,KAAK,IAAIzD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwC,KAAK,CAACO,WAAW,CAACO,MAAM,EAAEtD,CAAC,EAAE,EAAE;cACjD,MAAMqD,KAAK,GAAGb,KAAK,CAACO,WAAW,CAAC/C,CAAC,CAAC;cAClCuD,GAAG,CAACtD,GAAG,CAACoD,KAAK,EAAEI,MAAM,CAAC;cACtBA,MAAM,IAAIJ,KAAK,CAACC,MAAM;;YAExBzB,OAAO,CAACW,KAAK,CAACzB,IAAI,EAAEwC,GAAG,CAAC;;UAE1Bf,KAAK,CAACQ,SAAS,GAAG,CAAC;UACnBR,KAAK,CAACO,WAAW,GAAG,EAAE;;;MAI1BP,KAAK,CAACA,KAAK,GAAGtC,KAAK,CAACkB,OAAO;MAC3BoB,KAAK,CAACC,KAAK,GAAGA,KAAK;MACnBD,KAAK,CAACI,UAAU,GAAG,CAAC,EAAC;MAErB;MACA,IAAIS,KAAK,CAAC,CAAC,CAAC,KAAK,EAAE,IAAIA,KAAK,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;QACtC,OAAOtB,MAAM,EAAE;;MAGjBS,KAAK,CAACE,KAAK,EAAE;MACb,IAAIF,KAAK,CAACE,KAAK,GAAGT,QAAQ,EAAE;QAC1BH,OAAO,CAACxB,WAAW,CAAC;;;IAIxB,IAAI,CAACkC,KAAK,CAACK,QAAQ,IAAIQ,KAAK,CAACC,MAAM,IAAIlB,WAAW,EAAE;MAClDN,OAAO,CAACrB,cAAc,CAAC;;IAGzB,IAAI+B,KAAK,CAACA,KAAK,KAAKtC,KAAK,CAACkB,OAAO,EAAE;MACjC,MAAMsC,MAAM,GAAGP,YAAY,CAACE,KAAK,EAAEb,KAAK,CAACI,UAAU,CAAC;MACpDJ,KAAK,CAACI,UAAU,GAAG,CAAC;MAEpB,IAAIc,MAAM,CAACtD,IAAI,KAAK,UAAU,EAAE;QAC9B;OACD,MAAM,IAAIsD,MAAM,CAACtD,IAAI,KAAK,SAAS,EAAE;QACpC6C,QAAQ,EAAE;QACV,OAAOnB,OAAO,CAAC;UAAE1B,IAAI,EAAE,YAAY;UAAEuD,KAAK,EAAED;QAAM,CAAE,CAAC;;MAGvD,MAAMxC,WAAW,GAAG1C,EAAE,CAAC6C,KAAK,CAACqC,MAAM,CAACtC,OAAO,CAAC,cAAc,CAAW,CAAC;MACtE,MAAMwC,kBAAkB,GAAGpF,EAAE,CAAC6C,KAAK,CACjCqC,MAAM,CAACtC,OAAO,CAAC,qBAAqB,CAAW,EAC/C,IAAI,CACL;MAED,IACE,WAAW,KAAKwC,kBAAkB,CAACC,KAAK,IACxC,EAAE,MAAM,IAAID,kBAAkB,CAACtC,UAAU,CAAC,EAC1C;QACA2B,QAAQ,EAAE;QACV,OAAOnB,OAAO,CAAC3B,qBAAqB,CAAC;;MAGvC,IAAI2D,eAAmC;MACvC,IAAI,WAAW,IAAIF,kBAAkB,CAACtC,UAAU,EAAE;QAChD,MAAMoB,KAAK,GAAGkB,kBAAkB,CAACtC,UAAU,CAAC,WAAW,CAAC,CAAC8B,KAAK,CAAC,IAAI,CAAC;QACpE,IAAIV,KAAK,CAACY,MAAM,KAAK,CAAC,EAAE;UACtBQ,eAAe,GAAGC,kBAAkB,CAACrB,KAAK,CAAC,CAAC,CAAC,CAAC;;;MAIlDF,KAAK,CAACzB,IAAI,GAAG;QACXiD,IAAI,EAAEJ,kBAAkB,CAACtC,UAAU,CAAC0C,IAAI,IAAI,EAAE;QAC9ChD,QAAQ,EAAE8C,eAAe,IAAIF,kBAAkB,CAACtC,UAAU,CAACN,QAAQ;QACnEE,WAAW,EACTA,WAAW,CAAC2C,KAAK,KAAK,EAAE,GACpBD,kBAAkB,CAACtC,UAAU,CAACN,QAAQ,KAAKC,SAAS,GAClD,0BAA0B,GAC1B,YAAY,GACdC,WAAW,CAAC2C,KAAK;QACvBI,qBAAqB,EAAE/C,WAAW,CAACI,UAAU;QAC7CsC,kBAAkB,EAAEA,kBAAkB,CAACC,KAAK;QAC5CK,4BAA4B,EAAEN,kBAAkB,CAACtC,UAAiB;QAClEF,OAAO,EAAEsC,MAAM,CAACtC;OACjB;MAEDoB,KAAK,CAACA,KAAK,GAAGtC,KAAK,CAACgD,IAAI;MACxBV,KAAK,CAACR,MAAM,GAAGA,MAAM,CAACQ,KAAK,CAACzB,IAAI,CAAC;MAEjC,IAAIyB,KAAK,CAACR,MAAM,EAAE;QAChBQ,KAAK,CAACG,OAAO,GAAGf,MAAM,CAACY,KAAK,CAACzB,IAAI,CAAC;;MAGpC,IAAI2C,MAAM,CAACS,WAAW,GAAGd,KAAK,CAACC,MAAM,EAAE;QACrC,IAAId,KAAK,CAACR,MAAM,EAAE;UAChBQ,KAAK,CAACG,OAAO,CAACU,KAAK,CAACe,QAAQ,CAACV,MAAM,CAACS,WAAW,CAAC,CAAC;SAClD,MAAM;UACL,MAAMZ,GAAG,GAAGF,KAAK,CAACe,QAAQ,CAACV,MAAM,CAACS,WAAW,CAAC;UAC9C,IAAI,CAAC3B,KAAK,CAACQ,SAAS,IAAIO,GAAG,CAACD,MAAM,IAAIjB,YAAY,EAAE;YAClDP,OAAO,CAACpB,eAAe,CAAC;;UAE1B8B,KAAK,CAACO,WAAW,CAACsB,IAAI,CAACd,GAAG,CAAC;;;KAGhC,MAAM,IAAIf,KAAK,CAACR,MAAM,EAAE;MACvBQ,KAAK,CAACG,OAAO,CAACU,KAAK,CAAC;KACrB,MAAM;MACL,IAAI,CAACb,KAAK,CAACQ,SAAS,IAAIK,KAAK,CAACC,MAAM,IAAIjB,YAAY,EAAE;QACpDP,OAAO,CAACpB,eAAe,CAAC;;MAE1B8B,KAAK,CAACO,WAAW,CAACsB,IAAI,CAAChB,KAAK,CAAC;;EAEjC,CAAC,EACD1C,OAAO,CACR;EAED,OAAO;IACL2B,KAAKA,CAACe,KAAiB;MACrB,IAAI,CAACb,KAAK,CAACM,SAAS,IAAIO,KAAK,CAACC,MAAM,IAAInB,YAAY,EAAE;QACpD,OAAOL,OAAO,CAACtB,eAAe,CAAC;;MAEjC,OAAO4C,KAAK,CAACd,KAAK,CAACe,KAAK,CAAC;IAC3B,CAAC;IACDd,GAAGA,CAAA;MACDa,KAAK,CAACb,GAAG,EAAE;MACX,IAAIC,KAAK,CAACA,KAAK,KAAKtC,KAAK,CAACgD,IAAI,EAAE;QAC9BpB,OAAO,CAACzB,gBAAgB,CAAC;;MAG3BmC,KAAK,CAACA,KAAK,GAAGtC,KAAK,CAACkB,OAAO;MAC3BoB,KAAK,CAACC,KAAK,GAAG,CAAC;MACfD,KAAK,CAACE,KAAK,GAAG,CAAC;MACfF,KAAK,CAACG,OAAO,GAAGnB,WAAW;MAC3BgB,KAAK,CAACzB,IAAI,GAAGE,SAA4B;MACzCuB,KAAK,CAACM,SAAS,GAAG,CAAC;MACnBN,KAAK,CAACK,QAAQ,GAAG,CAAC;MAClBL,KAAK,CAACO,WAAW,GAAG,EAAE;MACtBP,KAAK,CAACQ,SAAS,GAAG,CAAC;IACrB;GACQ;AACZ;AAEA,MAAMsB,WAAW,gBAAG,IAAIC,WAAW,CAAC,OAAO,CAAC;AAC5C,SAASC,UAAUA,CAACC,OAAe;EACjC,IAAIA,OAAO,KAAK,OAAO,IAAIA,OAAO,KAAK,MAAM,IAAIA,OAAO,KAAK,EAAE,EAAE;IAC/D,OAAOH,WAAW;;EAGpB,IAAI;IACF,OAAO,IAAIC,WAAW,CAACE,OAAO,CAAC;GAChC,CAAC,OAAOd,KAAK,EAAE;IACd,OAAOW,WAAW;;AAEtB;AAEM,SAAUI,WAAWA,CAAC3D,IAAc,EAAE8C,KAAiB;EAC3D,OAAOW,UAAU,CAACzD,IAAI,CAACkD,qBAAqB,CAACQ,OAAO,IAAI,OAAO,CAAC,CAACE,MAAM,CAACd,KAAK,CAAC;AAChF"}