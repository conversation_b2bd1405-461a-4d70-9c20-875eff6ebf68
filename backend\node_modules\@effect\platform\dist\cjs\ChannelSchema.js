"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.encodeUnknown = exports.encode = exports.duplexUnknown = exports.duplex = exports.decodeUnknown = exports.decode = void 0;
var Channel = _interopRequireWildcard(require("effect/Channel"));
var _Function = require("effect/Function");
var Schema = _interopRequireWildcard(require("effect/Schema"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
/**
 * @since 1.0.0
 * @category constructors
 */
const encode = schema => () => {
  const encode = Schema.encode(Schema.ChunkFromSelf(schema));
  const loop = Channel.readWithCause({
    onInput: input => Channel.zipRight(Channel.flatMap(encode(input), Channel.write), loop),
    onFailure: cause => Channel.failCause(cause),
    onDone: Channel.succeed
  });
  return loop;
};
/**
 * @since 1.0.0
 * @category constructors
 */
exports.encode = encode;
const encodeUnknown = exports.encodeUnknown = encode;
/**
 * @since 1.0.0
 * @category constructors
 */
const decode = schema => () => {
  const decode = Schema.decode(Schema.ChunkFromSelf(schema));
  const loop = Channel.readWithCause({
    onInput(chunk) {
      return decode(chunk).pipe(Channel.flatMap(Channel.write), Channel.zipRight(loop));
    },
    onFailure(cause) {
      return Channel.failCause(cause);
    },
    onDone(done) {
      return Channel.succeed(done);
    }
  });
  return loop;
};
/**
 * @since 1.0.0
 * @category constructors
 */
exports.decode = decode;
const decodeUnknown = exports.decodeUnknown = decode;
/**
 * @since 1.0.0
 * @category combinators
 */
const duplex = exports.duplex = /*#__PURE__*/(0, _Function.dual)(2, (self, options) => {
  const decode = Schema.decode(Schema.ChunkFromSelf(options.outputSchema));
  return (0, _Function.pipe)(encode(options.inputSchema)(), Channel.pipeTo(self), Channel.mapOutEffect(decode));
});
/**
 * @since 1.0.0
 * @category combinators
 */
const duplexUnknown = exports.duplexUnknown = duplex;
//# sourceMappingURL=ChannelSchema.js.map