var Function = require('effect/Function');
var Micro = require('effect/Micro');
var shared = require('@uploadthing/shared');

function _interopNamespace(e) {
  if (e && e.__esModule) return e;
  var n = Object.create(null);
  if (e) {
    Object.keys(e).forEach(function (k) {
      if (k !== 'default') {
        var d = Object.getOwnPropertyDescriptor(e, k);
        Object.defineProperty(n, k, d.get ? d : {
          enumerable: true,
          get: function () { return e[k]; }
        });
      }
    });
  }
  n.default = e;
  return n;
}

var Micro__namespace = /*#__PURE__*/_interopNamespace(Micro);

var version = "7.7.2";

const createAPIRequestUrl = (config)=>{
    const url = new URL(config.url);
    const queryParams = new URLSearchParams(url.search);
    queryParams.set("actionType", config.actionType);
    queryParams.set("slug", config.slug);
    url.search = queryParams.toString();
    return url;
};
/**
 * Creates a "client" for reporting events to the UploadThing server via the user's API endpoint.
 * Events are handled in "./handler.ts starting at L112"
 */ const createUTReporter = (cfg)=>(type, payload)=>Micro__namespace.gen(function*() {
            const url = createAPIRequestUrl({
                url: cfg.url,
                slug: cfg.endpoint,
                actionType: type
            });
            const headers = new Headers((yield* Micro__namespace.promise(async ()=>typeof cfg.headers === "function" ? await cfg.headers() : cfg.headers)));
            if (cfg.package) {
                headers.set("x-uploadthing-package", cfg.package);
            }
            headers.set("x-uploadthing-version", version);
            headers.set("Content-Type", "application/json");
            const response = yield* shared.fetchEff(url, {
                method: "POST",
                body: JSON.stringify(payload),
                headers
            }).pipe(Micro__namespace.andThen(shared.parseResponseJson), /**
         * We don't _need_ to validate the response here, just cast it for now.
         * As of now, @effect/schema includes quite a few bytes we cut out by this...
         * We have "strong typing" on the backend that ensures the shape should match.
         */ Micro__namespace.map(Function.unsafeCoerce), Micro__namespace.catchTag("FetchError", (e)=>Micro__namespace.fail(new shared.UploadThingError({
                    code: "INTERNAL_CLIENT_ERROR",
                    message: `Failed to report event "${type}" to UploadThing server`,
                    cause: e
                }))), Micro__namespace.catchTag("BadRequestError", (e)=>Micro__namespace.fail(new shared.UploadThingError({
                    code: shared.getErrorTypeFromStatusCode(e.status),
                    message: e.getMessage(),
                    cause: e.json
                }))), Micro__namespace.catchTag("InvalidJson", (e)=>Micro__namespace.fail(new shared.UploadThingError({
                    code: "INTERNAL_CLIENT_ERROR",
                    message: "Failed to parse response from UploadThing server",
                    cause: e
                }))));
            return response;
        });

exports.createUTReporter = createUTReporter;
