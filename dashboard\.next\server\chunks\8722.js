exports.id=8722,exports.ids=[8722],exports.modules={20559:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},23709:(e,t,r)=>{"use strict";r.d(t,{HydrationFix:()=>o});let o=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call HydrationFix() from the server but HydrationFix is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\components\\hydration-fix.tsx","HydrationFix")},28807:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},32415:(e,t,r)=>{Promise.resolve().then(r.bind(r,50196)),Promise.resolve().then(r.bind(r,87653)),Promise.resolve().then(r.bind(r,68291)),Promise.resolve().then(r.bind(r,92892)),Promise.resolve().then(r.bind(r,69794))},42382:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>o});let o=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\components\\auth\\auth-provider.tsx","AuthProvider")},50196:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>n});var o=r(60687),s=r(99208);function n({children:e}){return(0,o.jsx)(s.CP,{children:e})}},50903:(e,t,r)=>{"use strict";r.d(t,{DarkModeProvider:()=>s,DarkModeScript:()=>n});var o=r(12907);let s=(0,o.registerClientReference)(function(){throw Error("Attempted to call DarkModeProvider() from the server but DarkModeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\components\\DarkModeProvider.tsx","DarkModeProvider"),n=(0,o.registerClientReference)(function(){throw Error("Attempted to call DarkModeScript() from the server but DarkModeScript is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\components\\DarkModeProvider.tsx","DarkModeScript")},52358:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>o});let o=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\components\\ui\\toaster.tsx","Toaster")},60566:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f,generateStaticParams:()=>h,metadata:()=>p});var o=r(37413),s=r(78901),n=r.n(s);r(82704);var i=r(83066),a=r(52358),d=r(24729);let l=["en","ar"];var c=r(42382),m=r(23709),u=r(50903);let p={title:"Real Estate AI Dashboard",description:"Admin dashboard for WhatsApp AI assistant for real estate"};async function h(){return l.map(e=>({lng:e}))}function f({children:e,params:{lng:t="en"}}){return(0,o.jsxs)("html",{lang:t,dir:(0,d.y_)(t),suppressHydrationWarning:!0,className:"dark",children:[(0,o.jsx)("head",{children:(0,o.jsx)(u.DarkModeScript,{})}),(0,o.jsxs)("body",{className:n().className,suppressHydrationWarning:!0,children:[(0,o.jsx)(m.HydrationFix,{}),(0,o.jsx)(u.DarkModeProvider,{children:(0,o.jsx)(c.AuthProvider,{children:(0,o.jsxs)(i.ThemeProvider,{attribute:"class",defaultTheme:"dark",enableSystem:!0,children:[e,(0,o.jsx)(a.Toaster,{})]})})})]})]})}},68291:(e,t,r)=>{"use strict";function o(){return null}r.d(t,{HydrationFix:()=>o}),r(43210)},69794:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>b});var o=r(60687),s=r(71702),n=r(43210),i=r(47313),a=r(24224),d=r(11860),l=r(96241);let c=i.Kq,m=n.forwardRef(({className:e,...t},r)=>(0,o.jsx)(i.LM,{ref:r,className:(0,l.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));m.displayName=i.LM.displayName;let u=(0,a.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),p=n.forwardRef(({className:e,variant:t,...r},s)=>(0,o.jsx)(i.bL,{ref:s,className:(0,l.cn)(u({variant:t}),e),...r}));p.displayName=i.bL.displayName,n.forwardRef(({className:e,...t},r)=>(0,o.jsx)(i.rc,{ref:r,className:(0,l.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t})).displayName=i.rc.displayName;let h=n.forwardRef(({className:e,...t},r)=>(0,o.jsx)(i.bm,{ref:r,className:(0,l.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:(0,o.jsx)(d.A,{className:"h-4 w-4"})}));h.displayName=i.bm.displayName;let f=n.forwardRef(({className:e,...t},r)=>(0,o.jsx)(i.hE,{ref:r,className:(0,l.cn)("text-sm font-semibold",e),...t}));f.displayName=i.hE.displayName;let v=n.forwardRef(({className:e,...t},r)=>(0,o.jsx)(i.VY,{ref:r,className:(0,l.cn)("text-sm opacity-90",e),...t}));function b(){let{toasts:e}=(0,s.dj)();return(0,o.jsxs)(c,{children:[e.map(function({id:e,title:t,description:r,action:s,...n}){return(0,o.jsxs)(p,{...n,children:[(0,o.jsxs)("div",{className:"grid gap-1",children:[t&&(0,o.jsx)(f,{children:t}),r&&(0,o.jsx)(v,{children:r})]}),s,(0,o.jsx)(h,{})]},e)}),(0,o.jsx)(m,{})]})}v.displayName=i.VY.displayName},71702:(e,t,r)=>{"use strict";r.d(t,{dj:()=>u});var o=r(43210);let s=0,n=new Map,i=e=>{if(n.has(e))return;let t=setTimeout(()=>{n.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,t)},a=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?i(r):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},d=[],l={toasts:[]};function c(e){l=a(l,e),d.forEach(e=>{e(l)})}function m({...e}){let t=(s=(s+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>c({type:"DISMISS_TOAST",toastId:t});return c({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||r()}}}),{id:t,dismiss:r,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function u(){let[e,t]=o.useState(l);return o.useEffect(()=>(d.push(t),()=>{let e=d.indexOf(t);e>-1&&d.splice(e,1)}),[e]),{...e,toast:m,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},82704:()=>{},83066:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>o});let o=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\components\\theme-provider.tsx","ThemeProvider")},87653:(e,t,r)=>{"use strict";r.d(t,{DarkModeProvider:()=>s,DarkModeScript:()=>n});var o=r(60687);function s({children:e}){return(0,o.jsx)(o.Fragment,{children:e})}function n(){let e=`
    (function() {
      try {
        const theme = localStorage.getItem('properties-theme') || 'dark';
        if (theme === 'dark') {
          document.documentElement.classList.add('dark');
          document.documentElement.style.colorScheme = 'dark';
        } else {
          document.documentElement.classList.add('light');
          document.documentElement.style.colorScheme = 'light';
        }
      } catch (e) {
        // Fallback to dark mode
        document.documentElement.classList.add('dark');
        document.documentElement.style.colorScheme = 'dark';
      }
    })();
  `;return(0,o.jsx)("script",{dangerouslySetInnerHTML:{__html:e},suppressHydrationWarning:!0})}r(43210)},92892:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>n});var o=r(60687);r(43210);var s=r(10218);function n({children:e,...t}){return(0,o.jsx)(s.N,{...t,children:e})}},93095:(e,t,r)=>{Promise.resolve().then(r.bind(r,42382)),Promise.resolve().then(r.bind(r,50903)),Promise.resolve().then(r.bind(r,23709)),Promise.resolve().then(r.bind(r,83066)),Promise.resolve().then(r.bind(r,52358))},96241:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var o=r(49384),s=r(82348);function n(...e){return(0,s.QP)((0,o.$)(e))}}};