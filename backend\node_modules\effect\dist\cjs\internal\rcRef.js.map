{"version": 3, "file": "rcRef.js", "names": ["Context", "_interopRequireWildcard", "require", "Duration", "Effectable", "_Function", "Readable", "coreEffect", "core", "circular", "fiberRuntime", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "TypeId", "exports", "Symbol", "for", "stateEmpty", "_tag", "stateClosed", "variance", "_A", "identity", "_E", "RcRefImpl", "Class", "acquire", "context", "scope", "idleTimeToLive", "state", "semaphore", "unsafeMakeSemaphore", "constructor", "commit", "make", "options", "withFiberRuntime", "fiber", "getFiberRef", "currentContext", "scopeTag", "ref", "decode", "undefined", "as", "addFinalizer", "withPermits", "suspend", "close", "scopeClose", "exitVoid", "void", "self_", "self", "uninterruptibleMask", "restore", "interrupt", "refCount", "interruptFiber", "succeed", "scopeMake", "pipe", "bindTo", "bind", "fiberRefLocally", "add", "map", "value", "tap", "sleep", "interruptible", "zipRight", "ensuring", "sync", "forkIn"], "sources": ["../../../src/internal/rcRef.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,QAAA,GAAAF,uBAAA,CAAAC,OAAA;AAEA,IAAAE,UAAA,GAAAH,uBAAA,CAAAC,OAAA;AAEA,IAAAG,SAAA,GAAAH,OAAA;AAEA,IAAAI,QAAA,GAAAL,uBAAA,CAAAC,OAAA;AAEA,IAAAK,UAAA,GAAAN,uBAAA,CAAAC,OAAA;AACA,IAAAM,IAAA,GAAAP,uBAAA,CAAAC,OAAA;AACA,IAAAO,QAAA,GAAAR,uBAAA,CAAAC,OAAA;AACA,IAAAQ,YAAA,GAAAT,uBAAA,CAAAC,OAAA;AAAiD,SAAAS,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAX,wBAAAW,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAEjD;AACO,MAAMW,MAAM,GAAAC,OAAA,CAAAD,MAAA,gBAAiBE,MAAM,CAACC,GAAG,CAAC,cAAc,CAAiB;AAsB9E,MAAMC,UAAU,GAAiB;EAAEC,IAAI,EAAE;AAAO,CAAE;AAClD,MAAMC,WAAW,GAAiB;EAAED,IAAI,EAAE;AAAQ,CAAE;AAEpD,MAAME,QAAQ,GAAmC;EAC/CC,EAAE,EAAEC,kBAAQ;EACZC,EAAE,EAAED;CACL;AAED,MAAME,SAAgB,SAAQtC,UAAU,CAACuC,KAAwB;EAQpDC,OAAA;EACAC,OAAA;EACAC,KAAA;EACAC,cAAA;EAVF,CAAChB,MAAM,IAAgCO,QAAQ;EAC/C,CAAChC,QAAQ,CAACyB,MAAM,IAAqBzB,QAAQ,CAACyB,MAAM;EAE7DiB,KAAK,GAAab,UAAU;EACnBc,SAAS,gBAAGxC,QAAQ,CAACyC,mBAAmB,CAAC,CAAC,CAAC;EAEpDC,YACWP,OAAkC,EAClCC,OAA+B,EAC/BC,KAAkB,EAClBC,cAA6C;IAEtD,KAAK,EAAE;IALE,KAAAH,OAAO,GAAPA,OAAO;IACP,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,cAAc,GAAdA,cAAc;IAGvB,IAAI,CAAC5B,GAAG,GAAGA,GAAG,CAAC,IAAI,CAAC;EACtB;EACSA,GAAG;EAEZiC,MAAMA,CAAA;IACJ,OAAO,IAAI,CAACjC,GAAG;EACjB;;AAGF;AACO,MAAMkC,IAAI,GAAaC,OAG7B,IACC9C,IAAI,CAAC+C,gBAAgB,CAA6CC,KAAK,IAAI;EACzE,MAAMX,OAAO,GAAGW,KAAK,CAACC,WAAW,CAACjD,IAAI,CAACkD,cAAc,CAAqC;EAC1F,MAAMZ,KAAK,GAAG9C,OAAO,CAACmB,GAAG,CAAC0B,OAAO,EAAEnC,YAAY,CAACiD,QAAQ,CAAC;EACzD,MAAMC,GAAG,GAAG,IAAIlB,SAAS,CACvBY,OAAO,CAACV,OAAoC,EAC5CC,OAAO,EACPC,KAAK,EACLQ,OAAO,CAACP,cAAc,GAAG5C,QAAQ,CAAC0D,MAAM,CAACP,OAAO,CAACP,cAAc,CAAC,GAAGe,SAAS,CAC7E;EACD,OAAOtD,IAAI,CAACuD,EAAE,CACZjB,KAAK,CAACkB,YAAY,CAAC,MACjBJ,GAAG,CAACX,SAAS,CAACgB,WAAW,CAAC,CAAC,CAAC,CAACzD,IAAI,CAAC0D,OAAO,CAAC,MAAK;IAC7C,MAAMC,KAAK,GAAGP,GAAG,CAACZ,KAAK,CAACZ,IAAI,KAAK,UAAU,GACvC5B,IAAI,CAAC4D,UAAU,CAACR,GAAG,CAACZ,KAAK,CAACF,KAAK,EAAEtC,IAAI,CAAC6D,QAAQ,CAAC,GAC/C7D,IAAI,CAAC8D,IAAI;IACbV,GAAG,CAACZ,KAAK,GAAGX,WAAW;IACvB,OAAO8B,KAAK;EACd,CAAC,CAAC,CAAC,CACJ,EACDP,GAAG,CACJ;AACH,CAAC,CAAC;AAEJ;AAAA5B,OAAA,CAAAqB,IAAA,GAAAA,IAAA;AACO,MAAMlC,GAAG,GACdoD,KAAwB,IACK;EAC7B,MAAMC,IAAI,GAAGD,KAAwB;EACrC,OAAO/D,IAAI,CAACiE,mBAAmB,CAAEC,OAAO,IACtClE,IAAI,CAAC0D,OAAO,CAAC,MAAK;IAChB,QAAQM,IAAI,CAACxB,KAAK,CAACZ,IAAI;MACrB,KAAK,QAAQ;QAAE;UACb,OAAO5B,IAAI,CAACmE,SAAS;QACvB;MACA,KAAK,UAAU;QAAE;UACfH,IAAI,CAACxB,KAAK,CAAC4B,QAAQ,EAAE;UACrB,OAAOJ,IAAI,CAACxB,KAAK,CAACQ,KAAK,GACnBhD,IAAI,CAACuD,EAAE,CAACvD,IAAI,CAACqE,cAAc,CAACL,IAAI,CAACxB,KAAK,CAACQ,KAAK,CAAC,EAAEgB,IAAI,CAACxB,KAAK,CAAC,GAC1DxC,IAAI,CAACsE,OAAO,CAACN,IAAI,CAACxB,KAAK,CAAC;QAC9B;MACA,KAAK,OAAO;QAAE;UACZ,OAAOtC,YAAY,CAACqE,SAAS,EAAE,CAACC,IAAI,CAClCzE,UAAU,CAAC0E,MAAM,CAAC,OAAO,CAAC,EAC1B1E,UAAU,CAAC2E,IAAI,CAAC,OAAO,EAAE,CAAC;YAAEpC;UAAK,CAAE,KACjC4B,OAAO,CAAClE,IAAI,CAAC2E,eAAe,CAC1BX,IAAI,CAAC5B,OAAuB,EAC5BpC,IAAI,CAACkD,cAAc,EACnB1D,OAAO,CAACoF,GAAG,CAACZ,IAAI,CAAC3B,OAAO,EAAEnC,YAAY,CAACiD,QAAQ,EAAEb,KAAK,CAAC,CACxD,CAAC,CAAC,EACLtC,IAAI,CAAC6E,GAAG,CAAC,CAAC;YAAEvC,KAAK;YAAEwC;UAAK,CAAE,KAAI;YAC5B,MAAMtC,KAAK,GAAsB;cAC/BZ,IAAI,EAAE,UAAU;cAChBkD,KAAK;cACLxC,KAAK;cACLU,KAAK,EAAEM,SAAS;cAChBc,QAAQ,EAAE;aACX;YACDJ,IAAI,CAACxB,KAAK,GAAGA,KAAK;YAClB,OAAOA,KAAK;UACd,CAAC,CAAC,CACH;QACH;IACF;EACF,CAAC,CAAC,CACH,CAACgC,IAAI,CACJR,IAAI,CAACvB,SAAS,CAACgB,WAAW,CAAC,CAAC,CAAC,EAC7B1D,UAAU,CAAC0E,MAAM,CAAC,OAAO,CAAC,EAC1B1E,UAAU,CAAC2E,IAAI,CAAC,OAAO,EAAE,MAAMxE,YAAY,CAACiD,QAAQ,CAAC,EACrDnD,IAAI,CAAC+E,GAAG,CAAC,CAAC;IAAEzC,KAAK;IAAEE;EAAK,CAAE,KACxBF,KAAK,CAACkB,YAAY,CAAC,MACjBxD,IAAI,CAAC0D,OAAO,CAAC,MAAK;IAChBlB,KAAK,CAAC4B,QAAQ,EAAE;IAChB,IAAI5B,KAAK,CAAC4B,QAAQ,GAAG,CAAC,EAAE;MACtB,OAAOpE,IAAI,CAAC8D,IAAI;IAClB;IACA,IAAIE,IAAI,CAACzB,cAAc,KAAKe,SAAS,EAAE;MACrCU,IAAI,CAACxB,KAAK,GAAGb,UAAU;MACvB,OAAO3B,IAAI,CAAC4D,UAAU,CAACpB,KAAK,CAACF,KAAK,EAAEtC,IAAI,CAAC6D,QAAQ,CAAC;IACpD;IACA,OAAO9D,UAAU,CAACiF,KAAK,CAAChB,IAAI,CAACzB,cAAc,CAAC,CAACiC,IAAI,CAC/CxE,IAAI,CAACiF,aAAa,EAClBjF,IAAI,CAACkF,QAAQ,CAAClF,IAAI,CAAC0D,OAAO,CAAC,MAAK;MAC9B,IAAIM,IAAI,CAACxB,KAAK,CAACZ,IAAI,KAAK,UAAU,IAAIoC,IAAI,CAACxB,KAAK,CAAC4B,QAAQ,KAAK,CAAC,EAAE;QAC/DJ,IAAI,CAACxB,KAAK,GAAGb,UAAU;QACvB,OAAO3B,IAAI,CAAC4D,UAAU,CAACpB,KAAK,CAACF,KAAK,EAAEtC,IAAI,CAAC6D,QAAQ,CAAC;MACpD;MACA,OAAO7D,IAAI,CAAC8D,IAAI;IAClB,CAAC,CAAC,CAAC,EACH5D,YAAY,CAACiF,QAAQ,CAACnF,IAAI,CAACoF,IAAI,CAAC,MAAK;MACnC5C,KAAK,CAACQ,KAAK,GAAGM,SAAS;IACzB,CAAC,CAAC,CAAC,EACHrD,QAAQ,CAACoF,MAAM,CAACrB,IAAI,CAAC1B,KAAK,CAAC,EAC3BtC,IAAI,CAAC+E,GAAG,CAAE/B,KAAK,IAAI;MACjBR,KAAK,CAACQ,KAAK,GAAGA,KAAK;IACrB,CAAC,CAAC,EACFgB,IAAI,CAACvB,SAAS,CAACgB,WAAW,CAAC,CAAC,CAAC,CAC9B;EACH,CAAC,CAAC,CACH,CACF,EACDzD,IAAI,CAAC6E,GAAG,CAAC,CAAC;IAAErC;EAAK,CAAE,KAAKA,KAAK,CAACsC,KAAK,CAAC,CACrC;AACH,CAAC;AAAAtD,OAAA,CAAAb,GAAA,GAAAA,GAAA", "ignoreList": []}