import { HttpClient, HttpClientRequest } from '@effect/platform';
import * as Effect from 'effect/Effect';
import { unsafeCoerce } from 'effect/Function';
import { UploadThingError } from '@uploadthing/shared';
import { logDeprecationWarning } from './deprecations.js';
import { logHttpClientError } from './logger.js';

var version = "7.7.2";

const uploadWithoutProgress = (file, presigned)=>Effect.gen(function*() {
        const formData = new FormData();
        formData.append("file", file);
        const httpClient = (yield* HttpClient.HttpClient).pipe(HttpClient.filterStatusOk);
        const json = yield* HttpClientRequest.put(presigned.url).pipe(HttpClientRequest.bodyFormData(formData), HttpClientRequest.setHeader("Range", "bytes=0-"), HttpClientRequest.setHeader("x-uploadthing-version", version), httpClient.execute, Effect.tapError(logHttpClientError("Failed to upload file")), Effect.mapError((e)=>new UploadThingError({
                code: "UPLOAD_FAILED",
                message: "Failed to upload file",
                cause: e
            })), Effect.andThen((_)=>_.json), Effect.andThen(unsafeCoerce), Effect.scoped);
        yield* Effect.logDebug(`File ${file.name} uploaded successfully`).pipe(Effect.annotateLogs("json", json));
        return {
            ...json,
            get url () {
                logDeprecationWarning("`file.url` is deprecated and will be removed in uploadthing v9. Use `file.ufsUrl` instead.");
                return json.url;
            },
            get appUrl () {
                logDeprecationWarning("`file.appUrl` is deprecated and will be removed in uploadthing v9. Use `file.ufsUrl` instead.");
                return json.appUrl;
            }
        };
    });

export { uploadWithoutProgress };
