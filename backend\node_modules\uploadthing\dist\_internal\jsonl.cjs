var Effect = require('effect/Effect');
var S = require('effect/Schema');
var Stream = require('effect/Stream');

function _interopNamespace(e) {
  if (e && e.__esModule) return e;
  var n = Object.create(null);
  if (e) {
    Object.keys(e).forEach(function (k) {
      if (k !== 'default') {
        var d = Object.getOwnPropertyDescriptor(e, k);
        Object.defineProperty(n, k, d.get ? d : {
          enumerable: true,
          get: function () { return e[k]; }
        });
      }
    });
  }
  n.default = e;
  return n;
}

var Effect__namespace = /*#__PURE__*/_interopNamespace(Effect);
var S__namespace = /*#__PURE__*/_interopNamespace(S);
var Stream__namespace = /*#__PURE__*/_interopNamespace(Stream);

const handleJsonLineStream = (schema, onChunk)=>(stream)=>{
        let buf = "";
        return stream.pipe(Stream__namespace.decodeText(), Stream__namespace.mapEffect((chunk)=>Effect__namespace.gen(function*() {
                buf += chunk;
                // Scan buffer for newlines
                const parts = buf.split("\n");
                const validChunks = [];
                for (const part of parts){
                    try {
                        // Attempt to parse chunk as JSON
                        validChunks.push(JSON.parse(part));
                        // Advance buffer if parsing succeeded
                        buf = buf.slice(part.length + 1);
                    } catch  {
                    //
                    }
                }
                yield* Effect__namespace.logDebug("Received chunks").pipe(Effect__namespace.annotateLogs("chunk", chunk), Effect__namespace.annotateLogs("parsedChunks", validChunks), Effect__namespace.annotateLogs("buf", buf));
                return validChunks;
            })), Stream__namespace.mapEffect(S__namespace.decodeUnknown(S__namespace.Array(schema))), Stream__namespace.mapEffect(Effect__namespace.forEach((part)=>onChunk(part))), Stream__namespace.runDrain, Effect__namespace.withLogSpan("handleJsonLineStream"));
    };

exports.handleJsonLineStream = handleJsonLineStream;
