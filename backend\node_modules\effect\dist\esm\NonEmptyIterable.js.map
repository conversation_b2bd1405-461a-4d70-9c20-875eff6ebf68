{"version": 3, "file": "NonEmptyIterable.js", "names": ["unprepend", "self", "iterator", "Symbol", "next", "done", "Error", "value"], "sources": ["../../src/NonEmptyIterable.ts"], "sourcesContent": [null], "mappings": "AAAA;;;AAkBA;;;;AAIA,OAAO,MAAMA,SAAS,GAAOC,IAAyB,IAAuD;EAC3G,MAAMC,QAAQ,GAAGD,IAAI,CAACE,MAAM,CAACD,QAAQ,CAAC,EAAE;EACxC,MAAME,IAAI,GAAGF,QAAQ,CAACE,IAAI,EAAE;EAC5B,IAAIA,IAAI,CAACC,IAAI,EAAE;IACb,MAAM,IAAIC,KAAK,CACb,kHAAkH,CACnH;EACH;EACA,OAAO,CAACF,IAAI,CAACG,KAAK,EAAEL,QAAQ,CAAC;AAC/B,CAAC", "ignoreList": []}