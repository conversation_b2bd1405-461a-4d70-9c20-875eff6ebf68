{"version": 3, "file": "RequestResolver.js", "names": ["Context", "Effect", "core", "internal", "RequestResolverTypeId", "contextFromEffect", "self", "contextWith", "_", "provideContext", "contextFromServices", "services", "pick", "isRequestResolver", "make", "makeWithEntry", "makeBatched", "around", "aroundRequests", "batchN", "mapInputContext", "eitherWith", "fromFunction", "fromFunctionBatched", "fromEffect", "fromEffectTagged", "never", "race", "locally", "resolverLocally"], "sources": ["../../src/RequestResolver.ts"], "sourcesContent": [null], "mappings": "AAAA;;;AAKA,OAAO,KAAKA,OAAO,MAAM,cAAc;AACvC,OAAO,KAAKC,MAAM,MAAM,aAAa;AAIrC,OAAO,KAAKC,IAAI,MAAM,oBAAoB;AAC1C,OAAO,KAAKC,QAAQ,MAAM,0BAA0B;AAKpD;;;;AAIA,OAAO,MAAMC,qBAAqB,GAAkBF,IAAI,CAACE,qBAAqB;AA6D9E;;;;AAIA,OAAO,MAAMC,iBAAiB,GAA4CC,IAA2B,IACnGL,MAAM,CAACM,WAAW,CAAEC,CAAqB,IAAKC,cAAc,CAACH,IAAI,EAAEE,CAAC,CAAC,CAAC;AAExE;;;;AAIA,OAAO,MAAME,mBAAmB,GAC9BA,CAAgD,GAAGC,QAAkB,KAEnEL,IAA2B,IAKxBL,MAAM,CAACM,WAAW,CAAEC,CAAC,IAAKC,cAAc,CAACH,IAAW,EAAEN,OAAO,CAACY,IAAI,CAAC,GAAGD,QAAQ,CAAC,CAACH,CAAQ,CAAC,CAAC,CAAC;AAElG;;;;;;AAMA,OAAO,MAAMK,iBAAiB,GAA2DX,IAAI,CAACW,iBAAiB;AAE/G;;;;;;;AAOA,OAAO,MAAMC,IAAI,GAEYX,QAAQ,CAACW,IAAI;AAE1C;;;;;;;AAOA,OAAO,MAAMC,aAAa,GAEGZ,QAAQ,CAACY,aAAa;AAEnD;;;;;;AAMA,OAAO,MAAMC,WAAW,GAEKb,QAAQ,CAACa,WAAW;AAEjD;;;;;;;AAOA,OAAO,MAAMC,MAAM,GAwBfd,QAAQ,CAACc,MAAM;AAEnB;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA,OAAO,MAAMC,cAAc,GAgEvBf,QAAQ,CAACe,cAAc;AAE3B;;;;;;AAMA,OAAO,MAAMC,MAAM,GAefhB,QAAQ,CAACgB,MAAM;AAEnB;;;;;;AAMA,OAAO,MAAMC,eAAe,GAkBxBjB,QAAQ,CAACiB,eAAe;AAE5B;;;;;;;;AAQA,OAAO,MAAMC,UAAU,GAgCnBlB,QAAQ,CAACkB,UAAU;AAEvB;;;;;;AAMA,OAAO,MAAMC,YAAY,GAECnB,QAAQ,CAACmB,YAAY;AAE/C;;;;;;;;AAQA,OAAO,MAAMC,mBAAmB,GAENpB,QAAQ,CAACoB,mBAAmB;AAEtD;;;;;;AAMA,OAAO,MAAMC,UAAU,GAEMrB,QAAQ,CAACqB,UAAU;AAEhD;;;;;;;;;AASA,OAAO,MAAMC,gBAAgB,GAW3BtB,QAAQ,CAACsB,gBAAgB;AAE3B;;;;;;AAMA,OAAO,MAAMC,KAAK,GAA2BvB,QAAQ,CAACuB,KAAK;AAE3D;;;;;;AAMA,OAAO,MAAMjB,cAAc,GAevBN,QAAQ,CAACM,cAAc;AAE3B;;;;;;;;AAQA,OAAO,MAAMkB,IAAI,GAmBbxB,QAAQ,CAACwB,IAAI;AAEjB;;;;;;AAMA,OAAO,MAAMC,OAAO,GAehB1B,IAAI,CAAC2B,eAAe", "ignoreList": []}