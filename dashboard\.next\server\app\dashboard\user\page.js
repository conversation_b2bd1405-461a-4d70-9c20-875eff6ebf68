(()=>{var e={};e.id=8733,e.ids=[8733],e.modules={1708:e=>{"use strict";e.exports=require("node:process")},2426:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>o});var t=r(37413),i=r(51358),d=r(41098),a=r(39916);async function o(){let e=await (0,d.HW)();return e||(0,a.redirect)("/sign-in?redirect_url=/dashboard/user"),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"flex items-center justify-between",children:(0,t.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"User Dashboard"})}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-3",children:[(0,t.jsxs)(i.Zp,{children:[(0,t.jsx)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,t.jsx)(i.ZB,{className:"text-sm font-medium",children:"Welcome"})}),(0,t.jsxs)(i.Wu,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:e.name||e.email}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Role: ",e.role]})]})]}),(0,t.jsxs)(i.Zp,{children:[(0,t.jsx)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,t.jsx)(i.ZB,{className:"text-sm font-medium",children:"Account Status"})}),(0,t.jsxs)(i.Wu,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"Active"}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Last login: ",new Date().toLocaleDateString()]})]})]}),(0,t.jsxs)(i.Zp,{children:[(0,t.jsx)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,t.jsx)(i.ZB,{className:"text-sm font-medium",children:"Actions"})}),(0,t.jsx)(i.Wu,{children:(0,t.jsx)("div",{className:"text-sm",children:(0,t.jsxs)("ul",{className:"space-y-2",children:[(0,t.jsx)("li",{children:"• View your profile"}),(0,t.jsx)("li",{children:"• Update your information"}),(0,t.jsx)("li",{children:"• Change password"})]})})})]})]}),(0,t.jsxs)(i.Zp,{children:[(0,t.jsx)(i.aR,{children:(0,t.jsx)(i.ZB,{children:"User Information"})}),(0,t.jsx)(i.Wu,{children:(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium",children:"Email"}),(0,t.jsx)("p",{children:e.email})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium",children:"Name"}),(0,t.jsx)("p",{children:e.name||"Not provided"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium",children:"Role"}),(0,t.jsx)("p",{children:e.role})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium",children:"Account Created"}),(0,t.jsx)("p",{children:new Date().toLocaleDateString()})]})]})})})]})]})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5486:e=>{"use strict";e.exports=require("bcrypt")},7066:e=>{"use strict";e.exports=require("node:tty")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},48161:e=>{"use strict";e.exports=require("node:os")},51358:(e,s,r)=>{"use strict";r.d(s,{BT:()=>l,Wu:()=>c,ZB:()=>n,Zp:()=>a,aR:()=>o});var t=r(37413),i=r(61120),d=r(66819);let a=i.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,d.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));a.displayName="Card";let o=i.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,d.cn)("flex flex-col space-y-1.5 p-6",e),...s}));o.displayName="CardHeader";let n=i.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,d.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));n.displayName="CardTitle";let l=i.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,d.cn)("text-sm text-muted-foreground",e),...s}));l.displayName="CardDescription";let c=i.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,d.cn)("p-6 pt-0",e),...s}));c.displayName="CardContent",i.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,d.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},51455:e=>{"use strict";e.exports=require("node:fs/promises")},55511:e=>{"use strict";e.exports=require("crypto")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66819:(e,s,r)=>{"use strict";r.d(s,{cn:()=>d});var t=r(75986),i=r(8974);function d(...e){return(0,i.QP)((0,t.$)(e))}},73024:e=>{"use strict";e.exports=require("node:fs")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},78335:()=>{},78474:e=>{"use strict";e.exports=require("node:events")},79172:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>a.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>u,tree:()=>l});var t=r(65239),i=r(48088),d=r(88170),a=r.n(d),o=r(30893),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);r.d(s,n);let l={children:["",{children:["dashboard",{children:["user",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,2426)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\user\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,83249)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,60566)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\user\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/dashboard/user/page",pathname:"/dashboard/user",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[7719,9656,2190,3903,5153,3555,4017,8722,9464,381],()=>r(79172));module.exports=t})();