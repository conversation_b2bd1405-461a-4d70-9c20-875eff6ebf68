"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/properties/create/page",{

/***/ "(app-pages-browser)/./hooks/useSimpleLanguage.tsx":
/*!*************************************!*\
  !*** ./hooks/useSimpleLanguage.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSimpleLanguage: () => (/* binding */ useSimpleLanguage)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useSimpleLanguage auto */ var _s = $RefreshSig$();\n\n/**\n * Simple language hook for Properties system\n * Returns the expected interface for language management\n * Defaults to Arabic (ar) as the primary language\n */ function useSimpleLanguage() {\n    _s();\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('ar');\n    // Load language from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSimpleLanguage.useEffect\": ()=>{\n            const savedLanguage = localStorage.getItem('language');\n            if (savedLanguage === 'ar' || savedLanguage === 'en') {\n                setLanguage(savedLanguage);\n            }\n        }\n    }[\"useSimpleLanguage.useEffect\"], []);\n    // Save language to localStorage when it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSimpleLanguage.useEffect\": ()=>{\n            localStorage.setItem('language', language);\n            // Update document direction\n            document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr';\n            document.documentElement.lang = language;\n        }\n    }[\"useSimpleLanguage.useEffect\"], [\n        language\n    ]);\n    const changeLanguage = (newLanguage)=>{\n        setLanguage(newLanguage);\n    };\n    return {\n        language,\n        setLanguage: changeLanguage,\n        isRTL: language === 'ar'\n    };\n}\n_s(useSimpleLanguage, \"iBs+XNlLZDTp3CbhC97v1VRCllI=\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2hvb2tzL3VzZVNpbXBsZUxhbmd1YWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBRTRDO0FBRTVDOzs7O0NBSUMsR0FDTSxTQUFTRTs7SUFDZCxNQUFNLENBQUNDLFVBQVVDLFlBQVksR0FBR0osK0NBQVFBLENBQWM7SUFFdEQsMkNBQTJDO0lBQzNDQyxnREFBU0E7dUNBQUM7WUFDUixNQUFNSSxnQkFBZ0JDLGFBQWFDLE9BQU8sQ0FBQztZQUMzQyxJQUFJRixrQkFBa0IsUUFBUUEsa0JBQWtCLE1BQU07Z0JBQ3BERCxZQUFZQztZQUNkO1FBQ0Y7c0NBQUcsRUFBRTtJQUVMLGdEQUFnRDtJQUNoREosZ0RBQVNBO3VDQUFDO1lBQ1JLLGFBQWFFLE9BQU8sQ0FBQyxZQUFZTDtZQUVqQyw0QkFBNEI7WUFDNUJNLFNBQVNDLGVBQWUsQ0FBQ0MsR0FBRyxHQUFHUixhQUFhLE9BQU8sUUFBUTtZQUMzRE0sU0FBU0MsZUFBZSxDQUFDRSxJQUFJLEdBQUdUO1FBQ2xDO3NDQUFHO1FBQUNBO0tBQVM7SUFFYixNQUFNVSxpQkFBaUIsQ0FBQ0M7UUFDdEJWLFlBQVlVO0lBQ2Q7SUFFQSxPQUFPO1FBQ0xYO1FBQ0FDLGFBQWFTO1FBQ2JFLE9BQU9aLGFBQWE7SUFDdEI7QUFDRjtHQTdCZ0JEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFobWVkXFxEZXNrdG9wXFxjb2RlXFxib290XFxkYXNoYm9hcmRcXGhvb2tzXFx1c2VTaW1wbGVMYW5ndWFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuXG4vKipcbiAqIFNpbXBsZSBsYW5ndWFnZSBob29rIGZvciBQcm9wZXJ0aWVzIHN5c3RlbVxuICogUmV0dXJucyB0aGUgZXhwZWN0ZWQgaW50ZXJmYWNlIGZvciBsYW5ndWFnZSBtYW5hZ2VtZW50XG4gKiBEZWZhdWx0cyB0byBBcmFiaWMgKGFyKSBhcyB0aGUgcHJpbWFyeSBsYW5ndWFnZVxuICovXG5leHBvcnQgZnVuY3Rpb24gdXNlU2ltcGxlTGFuZ3VhZ2UoKSB7XG4gIGNvbnN0IFtsYW5ndWFnZSwgc2V0TGFuZ3VhZ2VdID0gdXNlU3RhdGU8J2VuJyB8ICdhcic+KCdhcicpO1xuXG4gIC8vIExvYWQgbGFuZ3VhZ2UgZnJvbSBsb2NhbFN0b3JhZ2Ugb24gbW91bnRcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBzYXZlZExhbmd1YWdlID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2xhbmd1YWdlJyk7XG4gICAgaWYgKHNhdmVkTGFuZ3VhZ2UgPT09ICdhcicgfHwgc2F2ZWRMYW5ndWFnZSA9PT0gJ2VuJykge1xuICAgICAgc2V0TGFuZ3VhZ2Uoc2F2ZWRMYW5ndWFnZSk7XG4gICAgfVxuICB9LCBbXSk7XG5cbiAgLy8gU2F2ZSBsYW5ndWFnZSB0byBsb2NhbFN0b3JhZ2Ugd2hlbiBpdCBjaGFuZ2VzXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2xhbmd1YWdlJywgbGFuZ3VhZ2UpO1xuXG4gICAgLy8gVXBkYXRlIGRvY3VtZW50IGRpcmVjdGlvblxuICAgIGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5kaXIgPSBsYW5ndWFnZSA9PT0gJ2FyJyA/ICdydGwnIDogJ2x0cic7XG4gICAgZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LmxhbmcgPSBsYW5ndWFnZTtcbiAgfSwgW2xhbmd1YWdlXSk7XG5cbiAgY29uc3QgY2hhbmdlTGFuZ3VhZ2UgPSAobmV3TGFuZ3VhZ2U6ICdlbicgfCAnYXInKSA9PiB7XG4gICAgc2V0TGFuZ3VhZ2UobmV3TGFuZ3VhZ2UpO1xuICB9O1xuXG4gIHJldHVybiB7XG4gICAgbGFuZ3VhZ2UsXG4gICAgc2V0TGFuZ3VhZ2U6IGNoYW5nZUxhbmd1YWdlLFxuICAgIGlzUlRMOiBsYW5ndWFnZSA9PT0gJ2FyJyxcbiAgfTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVNpbXBsZUxhbmd1YWdlIiwibGFuZ3VhZ2UiLCJzZXRMYW5ndWFnZSIsInNhdmVkTGFuZ3VhZ2UiLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwic2V0SXRlbSIsImRvY3VtZW50IiwiZG9jdW1lbnRFbGVtZW50IiwiZGlyIiwibGFuZyIsImNoYW5nZUxhbmd1YWdlIiwibmV3TGFuZ3VhZ2UiLCJpc1JUTCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/useSimpleLanguage.tsx\n"));

/***/ })

});