import * as effect_ParseResult from 'effect/ParseResult';
import * as Effect from 'effect/Effect';
import * as S from 'effect/Schema';
import * as Stream from 'effect/Stream';

declare const handleJsonLineStream: <TChunk>(schema: S.Schema<TChunk>, onChunk: (chunk: TChunk) => Effect.Effect<void>) => <E, R>(stream: Stream.Stream<Uint8Array, E, R>) => Effect.Effect<void, E | effect_ParseResult.ParseError, R>;

export { handleJsonLineStream };
