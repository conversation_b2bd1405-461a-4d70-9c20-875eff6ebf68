{"version": 3, "file": "Cache.js", "names": ["internal", "CacheTypeId", "ConsumerCacheTypeId", "make", "makeWith", "makeCacheStats", "makeEntryStats"], "sources": ["../../src/Cache.ts"], "sourcesContent": [null], "mappings": "AAOA,OAAO,KAAKA,QAAQ,MAAM,qBAAqB;AAK/C;;;;AAIA,OAAO,MAAMC,WAAW,GAAkBD,QAAQ,CAACC,WAAW;AAQ9D;;;;AAIA,OAAO,MAAMC,mBAAmB,GAAkBF,QAAQ,CAACE,mBAAmB;AAsK9E;;;;;;;AAOA,OAAO,MAAMC,IAAI,GAMkDH,QAAQ,CAACG,IAAI;AAEhF;;;;;;;;AAQA,OAAO,MAAMC,QAAQ,GAM8CJ,QAAQ,CAACI,QAAQ;AAepF;;;;;;AAMA,OAAO,MAAMC,cAAc,GAMTL,QAAQ,CAACK,cAAc;AAYzC;;;;;;AAMA,OAAO,MAAMC,cAAc,GAAyCN,QAAQ,CAACM,cAAc", "ignoreList": []}