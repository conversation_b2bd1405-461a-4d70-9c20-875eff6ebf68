{"version": 3, "file": "Reloadable.js", "names": ["internal", "ReloadableTypeId", "auto", "autoFromConfig", "get", "manual", "reload", "tag", "reloadableTag", "reloadFork"], "sources": ["../../src/Reloadable.ts"], "sourcesContent": [null], "mappings": "AAKA,OAAO,KAAKA,QAAQ,MAAM,0BAA0B;AAMpD;;;;AAIA,OAAO,MAAMC,gBAAgB,GAAkBD,QAAQ,CAACC,gBAAgB;AAyCxE;;;;;;;;AAQA,OAAO,MAAMC,IAAI,GAG4BF,QAAQ,CAACE,IAAI;AAE1D;;;;;;;;AAQA,OAAO,MAAMC,cAAc,GAMkBH,QAAQ,CAACG,cAAc;AAEpE;;;;;;AAMA,OAAO,MAAMC,GAAG,GAA6EJ,QAAQ,CAACI,GAAG;AAEzG;;;;;;;AAOA,OAAO,MAAMC,MAAM,GAGsBL,QAAQ,CAACK,MAAM;AAExD;;;;;;AAMA,OAAO,MAAMC,MAAM,GAAkFN,QAAQ,CAACM,MAAM;AAEpH;;;;AAIA,OAAO,MAAMC,GAAG,GAAgFP,QAAQ,CAACQ,aAAa;AAEtH;;;;;;AAMA,OAAO,MAAMC,UAAU,GACrBT,QAAQ,CAACS,UAAU", "ignoreList": []}