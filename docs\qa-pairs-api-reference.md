# QA Pairs API Reference

## Overview

The QA Pairs system provides intelligent response generation with a 90% similarity threshold mechanism. This document covers the complete API reference for managing QA pairs and understanding the intelligent response system.

## 📊 System Architecture

```mermaid
graph TD
    A[📱 User Message] --> B[🔍 Find QA Pairs]
    B --> C[🧮 Calculate Similarity]
    C --> D{🎯 ≥90% Match?}
    D -->|Yes| E[📚 QA Database Response]
    D -->|No| F[🤖 AI Agent Response]
    E --> G[📤 Send Response]
    F --> G
    G --> H[💾 Save with Source]
```

## 🔧 API Endpoints

### QA Pairs Management

#### GET /api/v1/qa-pairs
Get all QA pairs with filtering and pagination.

**Query Parameters:**
```typescript
{
  search?: string;        // Search in questions and answers
  category?: string;      // Filter by category
  language?: string;      // Filter by language (en, ar, both)
  isActive?: boolean;     // Filter by active status
  page?: number;          // Page number (default: 1)
  limit?: number;         // Items per page (default: 10)
  sortBy?: string;        // Sort field (priority, createdAt, etc.)
  sortOrder?: 'asc' | 'desc'; // Sort direction
}
```

**Response:**
```typescript
{
  success: boolean;
  data: {
    qaPairs: QAPair[];
    pagination: {
      total: number;
      page: number;
      limit: number;
      totalPages: number;
    };
    filters: {
      categories: string[];
      languages: string[];
    };
  };
}
```

#### GET /api/v1/qa-pairs/:id
Get a specific QA pair by ID.

**Response:**
```typescript
{
  success: boolean;
  data: QAPair;
}
```

#### POST /api/v1/qa-pairs
Create a new QA pair.

**Request Body:**
```typescript
{
  question: string;       // Required
  answer: string;         // Required
  category?: string;      // Default: "general"
  language?: string;      // Default: "en" (en, ar, both)
  tags?: string[];        // Array of tags
  priority?: number;      // Default: 0 (0-10 scale)
  isActive?: boolean;     // Default: true
}
```

**Response:**
```typescript
{
  success: boolean;
  data: QAPair;
  message: string;
}
```

#### PUT /api/v1/qa-pairs/:id
Update an existing QA pair.

**Request Body:** Same as POST (all fields optional)

**Response:**
```typescript
{
  success: boolean;
  data: QAPair;
  message: string;
}
```

#### DELETE /api/v1/qa-pairs/:id
Delete a QA pair.

**Response:**
```typescript
{
  success: boolean;
  message: string;
}
```

### Advanced Operations

#### GET /api/v1/qa-pairs/stats/overview
Get comprehensive statistics about QA pairs.

**Response:**
```typescript
{
  success: boolean;
  data: {
    total: number;
    active: number;
    inactive: number;
    byCategory: { [category: string]: number };
    byLanguage: { [language: string]: number };
    byPriority: { [priority: string]: number };
    recentActivity: {
      created: number;
      updated: number;
      period: string;
    };
  };
}
```

#### GET /api/v1/qa-pairs/search/ai
AI-powered search for finding relevant QA pairs.

**Query Parameters:**
```typescript
{
  message: string;        // Required: User message
  language?: string;      // Detected automatically if not provided
  limit?: number;         // Default: 5
  threshold?: number;     // Similarity threshold (default: 20)
}
```

**Response:**
```typescript
{
  success: boolean;
  data: {
    matches: QAPairWithSimilarity[];
    bestMatch?: QAPairWithSimilarity;
    searchMetrics: {
      totalScanned: number;
      processingTime: number;
      algorithm: string;
    };
  };
}
```

#### POST /api/v1/qa-pairs/bulk
Bulk operations for QA pairs.

**Request Body:**
```typescript
{
  operation: 'create' | 'update' | 'delete' | 'activate' | 'deactivate';
  data: QAPair[] | string[]; // QAPair objects for create/update, IDs for others
}
```

**Response:**
```typescript
{
  success: boolean;
  data: {
    processed: number;
    successful: number;
    failed: number;
    errors: string[];
  };
  message: string;
}
```

## 🧮 Similarity Calculation

### Algorithm Details

The system uses four different similarity algorithms:

1. **Levenshtein Distance** (Character-level)
2. **Jaccard Similarity** (Word-level)
3. **Cosine Similarity** (Frequency-based)
4. **Keyword Overlap** (Semantic)

### Weighted Combination

```typescript
const weights = {
  levenshtein: 0.2,  // Character-level similarity
  jaccard: 0.3,      // Word-level similarity
  cosine: 0.3,       // Frequency-based similarity
  keyword: 0.2       // Keyword overlap
};

const finalScore = 
  (levenshtein * weights.levenshtein) +
  (jaccard * weights.jaccard) +
  (cosine * weights.cosine) +
  (keyword * weights.keyword);
```

### Contextual Enhancement

The system applies additional scoring boosts:

- **Tag Matching**: +5% per matching tag (max 15%)
- **Answer Similarity**: +10% if answer content matches (max 10%)
- **Priority Weighting**: Higher priority items get preference in ties

## 🤖 AI Agent Integration

### Response Generation Logic

```typescript
// 90% Similarity Threshold Logic
if (bestMatch.similarity >= 90) {
  // Use QA Database Response
  response = bestMatch.answer;
  source = "QA Database";
  confidence = bestMatch.similarity;
} else {
  // Use AI Agent Response
  response = await generateAIResponse(message, language);
  source = "AI Agent";
  confidence = aiResponse.confidence;
}
```

### AI Agent Capabilities

The AI agent provides context-aware responses for:

- **Property Inquiries**: Buying, selling, renting
- **Appointment Scheduling**: Booking and management
- **Investment Advice**: ROI and market insights
- **General Support**: Office hours, contact info
- **Multilingual**: English and Arabic support

## 📊 Data Models

### QAPair Model

```typescript
interface QAPair {
  id: string;
  question: string;
  answer: string;
  category: string;
  language: string;        // "en" | "ar" | "both"
  isActive: boolean;
  tags: string[];
  priority: number;        // 0-10 scale
  createdAt: Date;
  updatedAt: Date;
}
```

### QAPairWithSimilarity Model

```typescript
interface QAPairWithSimilarity extends QAPair {
  similarity: number;      // 0-100 percentage
}
```

### AIResponse Model

```typescript
interface AIResponse {
  response: string;
  confidence: number;      // 0-1 scale
  source: 'ai_agent';
}
```

## 🔍 Search & Filtering

### Search Capabilities

- **Full-text search** in questions and answers
- **Tag-based filtering**
- **Category filtering**
- **Language filtering**
- **Status filtering** (active/inactive)
- **Priority-based sorting**

### Advanced Filtering

```typescript
// Example: Find high-priority Arabic appointment questions
GET /api/v1/qa-pairs?category=appointment&language=ar&priority=8&sortBy=priority&sortOrder=desc
```

## 📈 Performance Metrics

### Response Times

- **QA Database Match**: 400-1300ms
- **AI Agent Response**: 1700-2200ms
- **Similarity Calculation**: <100ms
- **Database Query**: <200ms

### Accuracy Rates

- **Exact Matches**: 100% accuracy
- **High Similarity (≥90%)**: 95%+ accuracy
- **AI Agent**: 70-95% confidence
- **Overall Success**: 98%+ delivery rate

## 🔐 Security & Validation

### Input Validation

- **Question length**: 1-500 characters
- **Answer length**: 1-2000 characters
- **Tag validation**: Alphanumeric + spaces only
- **Category validation**: Predefined categories
- **Language validation**: en, ar, or both

### Rate Limiting

- **Search API**: 100 requests/minute
- **CRUD Operations**: 50 requests/minute
- **Bulk Operations**: 10 requests/minute

## 🚀 Best Practices

### QA Pair Creation

1. **Clear Questions**: Use natural language patterns
2. **Comprehensive Answers**: Provide complete information
3. **Appropriate Tags**: Use relevant, searchable tags
4. **Priority Setting**: Reserve high priority (8-10) for critical info
5. **Language Consistency**: Match question and answer languages

### Performance Optimization

1. **Use Pagination**: Limit large result sets
2. **Cache Frequently Used**: Cache popular QA pairs
3. **Monitor Similarity**: Track similarity score distributions
4. **Regular Cleanup**: Remove outdated or unused pairs

### Monitoring & Analytics

1. **Track Usage**: Monitor which QA pairs are used most
2. **Similarity Analysis**: Analyze similarity score patterns
3. **Response Sources**: Monitor QA vs AI agent usage
4. **Performance Metrics**: Track response times and accuracy
