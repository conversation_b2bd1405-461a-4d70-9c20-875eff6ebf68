import * as Data from 'effect/Data';
import * as Effect from 'effect/Effect';
import { matchFileType, objectKeys, InvalidRouteConfigError, fileSizeToBytes, UploadThingError, fillInputRouteConfig, bytesToFileSize } from '@uploadthing/shared';

class FileSizeMismatch extends Data.Error {
    constructor(type, max, actual){
        const reason = `You uploaded a ${type} file that was ${bytesToFileSize(actual)}, but the limit for that type is ${max}`;
        super({
            reason
        }), this._tag = "FileSizeMismatch", this.name = "FileSizeMismatchError";
    }
}
class FileCountMismatch extends Data.Error {
    constructor(type, boundtype, bound, actual){
        const reason = `You uploaded ${actual} file(s) of type '${type}', but the ${boundtype} for that type is ${bound}`;
        super({
            reason
        }), this._tag = "FileCountMismatch", this.name = "FileCountMismatchError";
    }
}
// Verify that the uploaded files doesn't violate the route config,
// e.g. uploading more videos than allowed, or a file that is larger than allowed.
// This is double-checked on infra side, but we want to fail early to avoid network latency.
const assertFilesMeetConfig = (files, routeConfig)=>Effect.gen(function*() {
        const counts = {};
        for (const file of files){
            const type = yield* matchFileType(file, objectKeys(routeConfig));
            counts[type] = (counts[type] ?? 0) + 1;
            const sizeLimit = routeConfig[type]?.maxFileSize;
            if (!sizeLimit) {
                return yield* new InvalidRouteConfigError(type, "maxFileSize");
            }
            const sizeLimitBytes = yield* fileSizeToBytes(sizeLimit);
            if (file.size > sizeLimitBytes) {
                return yield* new FileSizeMismatch(type, sizeLimit, file.size);
            }
        }
        for(const _key in counts){
            const key = _key;
            const config = routeConfig[key];
            if (!config) return yield* new InvalidRouteConfigError(key);
            const count = counts[key];
            const min = config.minFileCount;
            const max = config.maxFileCount;
            if (min > max) {
                return yield* new UploadThingError({
                    code: "BAD_REQUEST",
                    message: "Invalid config during file count - minFileCount > maxFileCount",
                    cause: `minFileCount must be less than maxFileCount for key ${key}. got: ${min} > ${max}`
                });
            }
            if (count != null && count < min) {
                return yield* new FileCountMismatch(key, "minimum", min, count);
            }
            if (count != null && count > max) {
                return yield* new FileCountMismatch(key, "maximum", max, count);
            }
        }
        return null;
    });
const extractRouterConfig = (router)=>Effect.forEach(objectKeys(router), (slug)=>Effect.map(fillInputRouteConfig(router[slug].routerConfig), (config)=>({
                slug,
                config
            })));

export { assertFilesMeetConfig, extractRouterConfig };
