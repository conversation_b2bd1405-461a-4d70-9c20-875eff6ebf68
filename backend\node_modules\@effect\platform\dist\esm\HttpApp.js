/**
 * @since 1.0.0
 */
import * as Context from "effect/Context";
import * as Effect from "effect/Effect";
import * as Exit from "effect/Exit";
import * as FiberRef from "effect/FiberRef";
import * as Layer from "effect/Layer";
import * as Runtime from "effect/Runtime";
import * as Scope from "effect/Scope";
import { unify } from "effect/Unify";
import * as ServerError from "./HttpServerError.js";
import * as ServerRequest from "./HttpServerRequest.js";
import * as ServerResponse from "./HttpServerResponse.js";
import * as internal from "./internal/httpApp.js";
import * as internalMiddleware from "./internal/httpMiddleware.js";
const handledSymbol = /*#__PURE__*/Symbol.for("@effect/platform/HttpApp/handled");
/**
 * @since 1.0.0
 * @category combinators
 */
export const toHandled = (self, handleResponse, middleware) => {
  const responded = Effect.withFiberRuntime(fiber => Effect.flatMap(self, response => {
    const request = Context.unsafeGet(fiber.currentContext, ServerRequest.HttpServerRequest);
    const handler = fiber.getFiberRef(currentPreResponseHandlers);
    if (handler._tag === "None") {
      ;
      request[handledSymbol] = true;
      return Effect.as(handleResponse(request, response), response);
    }
    return Effect.tap(handler.value(request, response), response => {
      ;
      request[handledSymbol] = true;
      return handleResponse(request, response);
    });
  }));
  const withErrorHandling = Effect.catchAllCause(responded, cause => Effect.withFiberRuntime(fiber => Effect.flatMap(ServerError.causeResponse(cause), ([response, cause]) => {
    const request = Context.unsafeGet(fiber.currentContext, ServerRequest.HttpServerRequest);
    const handler = fiber.getFiberRef(currentPreResponseHandlers);
    if (handler._tag === "None") {
      ;
      request[handledSymbol] = true;
      return Effect.zipRight(handleResponse(request, response), Effect.failCause(cause));
    }
    return Effect.zipRight(Effect.tap(handler.value(request, response), response => {
      ;
      request[handledSymbol] = true;
      return handleResponse(request, response);
    }), Effect.failCause(cause));
  })));
  const withMiddleware = unify(middleware === undefined ? internalMiddleware.tracer(withErrorHandling) : Effect.matchCauseEffect(middleware(internalMiddleware.tracer(withErrorHandling)), {
    onFailure: cause => Effect.withFiberRuntime(fiber => {
      const request = Context.unsafeGet(fiber.currentContext, ServerRequest.HttpServerRequest);
      if (handledSymbol in request) {
        return Effect.void;
      }
      return Effect.matchCauseEffect(ServerError.causeResponse(cause), {
        onFailure: _cause => handleResponse(request, ServerResponse.empty({
          status: 500
        })),
        onSuccess: ([response]) => handleResponse(request, response)
      });
    }),
    onSuccess: response => Effect.withFiberRuntime(fiber => {
      const request = Context.unsafeGet(fiber.currentContext, ServerRequest.HttpServerRequest);
      return handledSymbol in request ? Effect.void : handleResponse(request, response);
    })
  }));
  return Effect.uninterruptible(Effect.scoped(withMiddleware));
};
/**
 * @since 1.0.0
 * @category fiber refs
 */
export const currentPreResponseHandlers = internal.currentPreResponseHandlers;
/**
 * @since 1.0.0
 * @category fiber refs
 */
export const appendPreResponseHandler = internal.appendPreResponseHandler;
/**
 * @since 1.0.0
 * @category fiber refs
 */
export const withPreResponseHandler = internal.withPreResponseHandler;
/**
 * @since 1.0.0
 * @category conversions
 */
export const toWebHandlerRuntime = runtime => {
  const run = Runtime.runFork(runtime);
  return (self, middleware) => {
    const resolveSymbol = Symbol.for("@effect/platform/HttpApp/resolve");
    const httpApp = toHandled(self, (request, response) => {
      ;
      request[resolveSymbol](ServerResponse.toWeb(response, {
        withoutBody: request.method === "HEAD",
        runtime
      }));
      return Effect.void;
    }, middleware);
    return (request, context) => new Promise(resolve => {
      const contextMap = new Map(runtime.context.unsafeMap);
      if (Context.isContext(context)) {
        for (const [key, value] of context.unsafeMap) {
          contextMap.set(key, value);
        }
      }
      const httpServerRequest = ServerRequest.fromWeb(request);
      contextMap.set(ServerRequest.HttpServerRequest.key, httpServerRequest);
      httpServerRequest[resolveSymbol] = resolve;
      const fiber = run(Effect.locally(httpApp, FiberRef.currentContext, Context.unsafeMake(contextMap)));
      request.signal?.addEventListener("abort", () => {
        fiber.unsafeInterruptAsFork(ServerError.clientAbortFiberId);
      }, {
        once: true
      });
    });
  };
};
/**
 * @since 1.0.0
 * @category conversions
 */
export const toWebHandler = /*#__PURE__*/toWebHandlerRuntime(Runtime.defaultRuntime);
/**
 * @since 1.0.0
 * @category conversions
 */
export const toWebHandlerLayer = (self, layer, middleware) => {
  const scope = Effect.runSync(Scope.make());
  const close = () => Effect.runPromise(Scope.close(scope, Exit.void));
  const build = Effect.map(Layer.toRuntime(layer), _ => toWebHandlerRuntime(_)(self, middleware));
  const runner = Effect.runPromise(Scope.extend(build, scope));
  const handler = (request, context) => runner.then(handler => handler(request, context));
  return {
    close,
    handler
  };
};
//# sourceMappingURL=HttpApp.js.map