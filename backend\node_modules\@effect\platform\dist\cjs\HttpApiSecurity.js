"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.bearer = exports.basic = exports.apiKey = exports.annotateContext = exports.annotate = exports.TypeId = void 0;
var Context = _interopRequireWildcard(require("effect/Context"));
var _Function = require("effect/Function");
var _Pipeable = require("effect/Pipeable");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
/**
 * @since 1.0.0
 */

/**
 * @since 1.0.0
 * @category type ids
 */
const TypeId = exports.TypeId = /*#__PURE__*/Symbol.for("@effect/platform/HttpApiSecurity");
const Proto = {
  [TypeId]: TypeId,
  pipe() {
    return (0, _Pipeable.pipeArguments)(this, arguments);
  }
};
/**
 * Create an Bearer token security scheme.
 *
 * You can implement some api middleware for this security scheme using
 * `HttpApiBuilder.middlewareSecurity`.
 *
 * @since 1.0.0
 * @category constructors
 */
const bearer = exports.bearer = /*#__PURE__*/Object.assign( /*#__PURE__*/Object.create(Proto), {
  _tag: "Bearer",
  annotations: /*#__PURE__*/Context.empty()
});
/**
 * Create an API key security scheme.
 *
 * You can implement some api middleware for this security scheme using
 * `HttpApiBuilder.middlewareSecurity`.
 *
 * To set the correct cookie in a handler, you can use
 * `HttpApiBuilder.securitySetCookie`.
 *
 * The default value for `in` is "header".
 *
 * @since 1.0.0
 * @category constructors
 */
const apiKey = options => Object.assign(Object.create(Proto), {
  _tag: "ApiKey",
  key: options.key,
  in: options.in ?? "header",
  annotations: Context.empty()
});
/**
 * @since 1.0.0
 * @category constructors
 */
exports.apiKey = apiKey;
const basic = exports.basic = /*#__PURE__*/Object.assign( /*#__PURE__*/Object.create(Proto), {
  _tag: "Basic",
  annotations: /*#__PURE__*/Context.empty()
});
/**
 * @since 1.0.0
 * @category annotations
 */
const annotateContext = exports.annotateContext = /*#__PURE__*/(0, _Function.dual)(2, (self, context) => Object.assign(Object.create(Proto), {
  ...self,
  annotations: Context.merge(self.annotations, context)
}));
/**
 * @since 1.0.0
 * @category annotations
 */
const annotate = exports.annotate = /*#__PURE__*/(0, _Function.dual)(3, (self, tag, value) => Object.assign(Object.create(Proto), {
  ...self,
  annotations: Context.add(self.annotations, tag, value)
}));
//# sourceMappingURL=HttpApiSecurity.js.map