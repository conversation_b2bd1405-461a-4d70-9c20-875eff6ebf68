(()=>{var e={};e.id=9483,e.ids=[9483],e.modules={2710:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(60687),o=r(43210),a=r(99208),n=r(16189);function i(){let[e,t]=(0,o.useState)("<EMAIL>"),[r,i]=(0,o.useState)("password123"),[d,l]=(0,o.useState)(!1),[u,c]=(0,o.useState)(""),[p,m]=(0,o.useState)(""),h=(0,n.useRouter)(),g=async t=>{t.preventDefault(),l(!0),c(""),m("");try{console.log("Checking credentials first:",{email:e,password:r});let t=await fetch("/api/auth/check-credentials",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:r})}),s=await t.json();if(console.log("Credential check result:",s),!s.success){c(s.message||"Invalid credentials"),l(!1);return}console.log("Credentials valid, attempting to sign in with NextAuth");let o=await (0,a.Jv)("credentials",{email:e,password:r,redirect:!1});console.log("Sign in result:",o),o?.error?c(o.error):(m("Login successful!"),setTimeout(()=>{h.push("/dashboard/analytics")},1e3))}catch(e){console.error("Login error:",e),c("An unexpected error occurred")}finally{l(!1)}};return(0,s.jsx)("div",{className:"flex min-h-screen flex-col items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"w-full max-w-md rounded-lg border p-6 shadow-md",children:[(0,s.jsx)("h1",{className:"mb-6 text-2xl font-bold",children:"Test Login"}),u&&(0,s.jsx)("div",{className:"mb-4 rounded-md bg-red-50 p-4 text-red-700",children:u}),p&&(0,s.jsx)("div",{className:"mb-4 rounded-md bg-green-50 p-4 text-green-700",children:p}),(0,s.jsxs)("form",{onSubmit:g,className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium",children:"Email"}),(0,s.jsx)("input",{id:"email",type:"email",value:e,onChange:e=>t(e.target.value),className:"mt-1 block w-full rounded-md border border-gray-300 p-2",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium",children:"Password"}),(0,s.jsx)("input",{id:"password",type:"password",value:r,onChange:e=>i(e.target.value),className:"mt-1 block w-full rounded-md border border-gray-300 p-2",required:!0})]}),(0,s.jsx)("button",{type:"submit",disabled:d,className:"w-full rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700 disabled:opacity-50",children:d?"Logging in...":"Login"})]})]})})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14327:(e,t,r)=>{Promise.resolve().then(r.bind(r,61556))},16189:(e,t,r)=>{"use strict";var s=r(65773);r.o(s,"useParams")&&r.d(t,{useParams:function(){return s.useParams}}),r.o(s,"usePathname")&&r.d(t,{usePathname:function(){return s.usePathname}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},42351:(e,t,r)=>{Promise.resolve().then(r.bind(r,2710))},54560:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>p,tree:()=>l});var s=r(65239),o=r(48088),a=r(88170),n=r.n(a),i=r(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);r.d(t,d);let l={children:["",{children:["test-login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,61556)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\test-login\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,60566)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\test-login\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/test-login/page",pathname:"/test-login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},61556:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\test-login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\test-login\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7719,9656,8722],()=>r(54560));module.exports=s})();