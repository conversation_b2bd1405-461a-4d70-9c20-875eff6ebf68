'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Moon, Sun, Palette } from 'lucide-react';
import { useTheme } from '@/hooks/useTheme';
import { useSimpleLanguage } from '@/hooks/useSimpleLanguage';

/**
 * Theme switcher component for dark/light mode
 * Supports smooth transitions and theme persistence
 */
export function ThemeSwitcher() {
  const { theme, toggleTheme, isDark } = useTheme();
  const { language, isArabic } = useSimpleLanguage();

  const themeText = {
    ar: {
      light: 'الوضع الفاتح',
      dark: 'الوضع المظلم',
      toggle: 'تبديل المظهر'
    },
    en: {
      light: 'Light Mode',
      dark: 'Dark Mode',
      toggle: 'Toggle Theme'
    }
  };

  const t = themeText[language];

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={toggleTheme}
      className={`
        flex items-center gap-2 px-4 py-2 h-10
        bg-white/80 dark:bg-slate-800/80 backdrop-blur-md
        border-2 border-slate-200 dark:border-slate-700
        hover:bg-white dark:hover:bg-slate-800
        hover:border-amber-300 dark:hover:border-amber-600
        hover:shadow-lg transition-all duration-300
        text-slate-700 dark:text-slate-300
        rounded-xl font-medium
        ${isArabic ? 'flex-row-reverse' : 'flex-row'}
      `}
      dir={isArabic ? 'rtl' : 'ltr'}
      title={t.toggle}
    >
      <div className="flex items-center gap-2">
        {isDark ? (
          <Moon className="h-4 w-4 text-amber-600 dark:text-amber-400" />
        ) : (
          <Sun className="h-4 w-4 text-amber-600 dark:text-amber-400" />
        )}
        <span className="font-bold">
          {isDark ? t.dark : t.light}
        </span>
      </div>
    </Button>
  );
}

/**
 * Compact theme switcher for smaller spaces
 */
export function CompactThemeSwitcher() {
  const { toggleTheme, isDark } = useTheme();

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={toggleTheme}
      className="
        w-12 h-12 p-0 rounded-xl
        bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm
        hover:bg-white/90 dark:hover:bg-slate-800/90
        border border-slate-200 dark:border-slate-700
        hover:border-amber-300 dark:hover:border-amber-600
        transition-all duration-300
      "
    >
      {isDark ? (
        <Moon className="h-5 w-5 text-amber-600 dark:text-amber-400" />
      ) : (
        <Sun className="h-5 w-5 text-amber-600 dark:text-amber-400" />
      )}
    </Button>
  );
}
