import * as internal from "./internal/effectable.js";
/**
 * @since 2.0.0
 * @category type ids
 */
export const EffectTypeId = internal.EffectTypeId;
/**
 * @since 2.0.0
 * @category type ids
 */
export const StreamTypeId = internal.StreamTypeId;
/**
 * @since 2.0.0
 * @category type ids
 */
export const SinkTypeId = internal.SinkTypeId;
/**
 * @since 2.0.0
 * @category type ids
 */
export const ChannelTypeId = internal.ChannelTypeId;
/**
 * @since 2.0.0
 * @category prototypes
 */
export const EffectPrototype = internal.EffectPrototype;
/**
 * @since 2.0.0
 * @category prototypes
 */
export const CommitPrototype = internal.CommitPrototype;
/**
 * @since 2.0.0
 * @category prototypes
 */
export const StructuralCommitPrototype = internal.StructuralCommitPrototype;
const Base = internal.Base;
const StructuralBase = internal.StructuralBase;
/**
 * @since 2.0.0
 * @category constructors
 */
export class Class extends Base {}
/**
 * @since 2.0.0
 * @category constructors
 */
export class StructuralClass extends StructuralBase {}
//# sourceMappingURL=Effectable.js.map