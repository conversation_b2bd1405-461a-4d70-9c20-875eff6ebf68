const image = {
    "image/aces": {
        source: "iana",
        extensions: [
            "exr"
        ]
    },
    "image/avci": {
        source: "iana",
        extensions: [
            "avci"
        ]
    },
    "image/avcs": {
        source: "iana",
        extensions: [
            "avcs"
        ]
    },
    "image/avif": {
        source: "iana",
        extensions: [
            "avif"
        ]
    },
    "image/bmp": {
        source: "iana",
        extensions: [
            "bmp"
        ]
    },
    "image/cgm": {
        source: "iana",
        extensions: [
            "cgm"
        ]
    },
    "image/dicom-rle": {
        source: "iana",
        extensions: [
            "drle"
        ]
    },
    "image/emf": {
        source: "iana",
        extensions: [
            "emf"
        ]
    },
    "image/fits": {
        source: "iana",
        extensions: [
            "fits"
        ]
    },
    "image/g3fax": {
        source: "iana",
        extensions: [
            "g3"
        ]
    },
    "image/gif": {
        source: "iana",
        extensions: [
            "gif"
        ]
    },
    "image/heic": {
        source: "iana",
        extensions: [
            "heic"
        ]
    },
    "image/heic-sequence": {
        source: "iana",
        extensions: [
            "heics"
        ]
    },
    "image/heif": {
        source: "iana",
        extensions: [
            "heif"
        ]
    },
    "image/heif-sequence": {
        source: "iana",
        extensions: [
            "heifs"
        ]
    },
    "image/hej2k": {
        source: "iana",
        extensions: [
            "hej2"
        ]
    },
    "image/hsj2": {
        source: "iana",
        extensions: [
            "hsj2"
        ]
    },
    "image/ief": {
        source: "iana",
        extensions: [
            "ief"
        ]
    },
    "image/jls": {
        source: "iana",
        extensions: [
            "jls"
        ]
    },
    "image/jp2": {
        source: "iana",
        extensions: [
            "jp2",
            "jpg2"
        ]
    },
    "image/jpeg": {
        source: "iana",
        extensions: [
            "jpeg",
            "jpg",
            "jpe",
            "jfif",
            "pjpeg",
            "pjp"
        ]
    },
    "image/jph": {
        source: "iana",
        extensions: [
            "jph"
        ]
    },
    "image/jphc": {
        source: "iana",
        extensions: [
            "jhc"
        ]
    },
    "image/jpm": {
        source: "iana",
        extensions: [
            "jpm"
        ]
    },
    "image/jpx": {
        source: "iana",
        extensions: [
            "jpx",
            "jpf"
        ]
    },
    "image/jxr": {
        source: "iana",
        extensions: [
            "jxr"
        ]
    },
    "image/jxra": {
        source: "iana",
        extensions: [
            "jxra"
        ]
    },
    "image/jxrs": {
        source: "iana",
        extensions: [
            "jxrs"
        ]
    },
    "image/jxs": {
        source: "iana",
        extensions: [
            "jxs"
        ]
    },
    "image/jxsc": {
        source: "iana",
        extensions: [
            "jxsc"
        ]
    },
    "image/jxsi": {
        source: "iana",
        extensions: [
            "jxsi"
        ]
    },
    "image/jxss": {
        source: "iana",
        extensions: [
            "jxss"
        ]
    },
    "image/ktx": {
        source: "iana",
        extensions: [
            "ktx"
        ]
    },
    "image/ktx2": {
        source: "iana",
        extensions: [
            "ktx2"
        ]
    },
    "image/png": {
        source: "iana",
        extensions: [
            "png"
        ]
    },
    "image/prs.btif": {
        source: "iana",
        extensions: [
            "btif"
        ]
    },
    "image/prs.pti": {
        source: "iana",
        extensions: [
            "pti"
        ]
    },
    "image/sgi": {
        source: "apache",
        extensions: [
            "sgi"
        ]
    },
    "image/svg+xml": {
        source: "iana",
        extensions: [
            "svg",
            "svgz"
        ]
    },
    "image/t38": {
        source: "iana",
        extensions: [
            "t38"
        ]
    },
    "image/tiff": {
        source: "iana",
        extensions: [
            "tif",
            "tiff"
        ]
    },
    "image/tiff-fx": {
        source: "iana",
        extensions: [
            "tfx"
        ]
    },
    "image/vnd.adobe.photoshop": {
        source: "iana",
        extensions: [
            "psd"
        ]
    },
    "image/vnd.airzip.accelerator.azv": {
        source: "iana",
        extensions: [
            "azv"
        ]
    },
    "image/vnd.dece.graphic": {
        source: "iana",
        extensions: [
            "uvi",
            "uvvi",
            "uvg",
            "uvvg"
        ]
    },
    "image/vnd.djvu": {
        source: "iana",
        extensions: [
            "djvu",
            "djv"
        ]
    },
    "image/vnd.dvb.subtitle": {
        source: "iana",
        extensions: [
            "sub"
        ]
    },
    "image/vnd.dwg": {
        source: "iana",
        extensions: [
            "dwg"
        ]
    },
    "image/vnd.dxf": {
        source: "iana",
        extensions: [
            "dxf"
        ]
    },
    "image/vnd.fastbidsheet": {
        source: "iana",
        extensions: [
            "fbs"
        ]
    },
    "image/vnd.fpx": {
        source: "iana",
        extensions: [
            "fpx"
        ]
    },
    "image/vnd.fst": {
        source: "iana",
        extensions: [
            "fst"
        ]
    },
    "image/vnd.fujixerox.edmics-mmr": {
        source: "iana",
        extensions: [
            "mmr"
        ]
    },
    "image/vnd.fujixerox.edmics-rlc": {
        source: "iana",
        extensions: [
            "rlc"
        ]
    },
    "image/vnd.microsoft.icon": {
        source: "iana",
        extensions: [
            "ico"
        ]
    },
    "image/vnd.ms-modi": {
        source: "iana",
        extensions: [
            "mdi"
        ]
    },
    "image/vnd.ms-photo": {
        source: "apache",
        extensions: [
            "wdp"
        ]
    },
    "image/vnd.net-fpx": {
        source: "iana",
        extensions: [
            "npx"
        ]
    },
    "image/vnd.pco.b16": {
        source: "iana",
        extensions: [
            "b16"
        ]
    },
    "image/vnd.tencent.tap": {
        source: "iana",
        extensions: [
            "tap"
        ]
    },
    "image/vnd.valve.source.texture": {
        source: "iana",
        extensions: [
            "vtf"
        ]
    },
    "image/vnd.wap.wbmp": {
        source: "iana",
        extensions: [
            "wbmp"
        ]
    },
    "image/vnd.xiff": {
        source: "iana",
        extensions: [
            "xif"
        ]
    },
    "image/vnd.zbrush.pcx": {
        source: "iana",
        extensions: [
            "pcx"
        ]
    },
    "image/webp": {
        source: "apache",
        extensions: [
            "webp"
        ]
    },
    "image/wmf": {
        source: "iana",
        extensions: [
            "wmf"
        ]
    },
    "image/x-3ds": {
        source: "apache",
        extensions: [
            "3ds"
        ]
    },
    "image/x-cmu-raster": {
        source: "apache",
        extensions: [
            "ras"
        ]
    },
    "image/x-cmx": {
        source: "apache",
        extensions: [
            "cmx"
        ]
    },
    "image/x-freehand": {
        source: "apache",
        extensions: [
            "fh",
            "fhc",
            "fh4",
            "fh5",
            "fh7"
        ]
    },
    "image/x-icon": {
        source: "apache",
        extensions: [
            "ico"
        ]
    },
    "image/x-jng": {
        source: "nginx",
        extensions: [
            "jng"
        ]
    },
    "image/x-mrsid-image": {
        source: "apache",
        extensions: [
            "sid"
        ]
    },
    "image/x-ms-bmp": {
        source: "nginx",
        extensions: [
            "bmp"
        ]
    },
    "image/x-pcx": {
        source: "apache",
        extensions: [
            "pcx"
        ]
    },
    "image/x-pict": {
        source: "apache",
        extensions: [
            "pic",
            "pct"
        ]
    },
    "image/x-portable-anymap": {
        source: "apache",
        extensions: [
            "pnm"
        ]
    },
    "image/x-portable-bitmap": {
        source: "apache",
        extensions: [
            "pbm"
        ]
    },
    "image/x-portable-graymap": {
        source: "apache",
        extensions: [
            "pgm"
        ]
    },
    "image/x-portable-pixmap": {
        source: "apache",
        extensions: [
            "ppm"
        ]
    },
    "image/x-rgb": {
        source: "apache",
        extensions: [
            "rgb"
        ]
    },
    "image/x-tga": {
        source: "apache",
        extensions: [
            "tga"
        ]
    },
    "image/x-xbitmap": {
        source: "apache",
        extensions: [
            "xbm"
        ]
    },
    "image/x-xpixmap": {
        source: "apache",
        extensions: [
            "xpm"
        ]
    },
    "image/x-xwindowdump": {
        source: "apache",
        extensions: [
            "xwd"
        ]
    }
};

exports.image = image;
