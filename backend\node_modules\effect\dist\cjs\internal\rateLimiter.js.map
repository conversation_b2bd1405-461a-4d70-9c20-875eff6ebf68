{"version": 3, "file": "rateLimiter.js", "names": ["Duration", "_interopRequireWildcard", "require", "Effect", "FiberRef", "_Function", "_GlobalValue", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "make", "algorithm", "interval", "limit", "fixedWindow", "tokenBucket", "exports", "window", "gen", "millisPerToken", "Math", "ceil", "<PERSON><PERSON><PERSON><PERSON>", "semaphore", "makeSemaphore", "latch", "refill", "sleep", "pipe", "zipRight", "releaseAll", "release", "flatMap", "free", "void", "take", "forever", "forkScoped", "interruptible", "uninterruptibleMask", "restore", "currentCost", "cost", "effect", "globalValue", "Symbol", "for", "unsafeMake", "withCost", "locally"], "sources": ["../../../src/internal/rateLimiter.ts"], "sourcesContent": [null], "mappings": ";;;;;;AACA,IAAAA,QAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAF,uBAAA,CAAAC,OAAA;AACA,IAAAE,QAAA,GAAAH,uBAAA,CAAAC,OAAA;AACA,IAAAG,SAAA,GAAAH,OAAA;AACA,IAAAI,YAAA,GAAAJ,OAAA;AAA+C,SAAAK,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAP,wBAAAO,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAI/C;AACO,MAAMW,IAAI,GAAGA,CAAC;EACnBC,SAAS,GAAG,cAAc;EAC1BC,QAAQ;EACRC;AAAK,CAC2B,KAI9B;EACF,QAAQF,SAAS;IACf,KAAK,cAAc;MAAE;QACnB,OAAOG,WAAW,CAACD,KAAK,EAAED,QAAQ,CAAC;MACrC;IACA,KAAK,cAAc;MAAE;QACnB,OAAOG,WAAW,CAACF,KAAK,EAAED,QAAQ,CAAC;MACrC;EACF;AACF,CAAC;AAAAI,OAAA,CAAAN,IAAA,GAAAA,IAAA;AAED,MAAMK,WAAW,GAAGA,CAACF,KAAa,EAAEI,MAAqB,KAKvD/B,MAAM,CAACgC,GAAG,CAAC,aAAS;EAClB,MAAMC,cAAc,GAAGC,IAAI,CAACC,IAAI,CAACtC,QAAQ,CAACuC,QAAQ,CAACL,MAAM,CAAC,GAAGJ,KAAK,CAAC;EACnE,MAAMU,SAAS,GAAG,OAAOrC,MAAM,CAACsC,aAAa,CAACX,KAAK,CAAC;EACpD,MAAMY,KAAK,GAAG,OAAOvC,MAAM,CAACsC,aAAa,CAAC,CAAC,CAAC;EAC5C,MAAME,MAAM,GAAwBxC,MAAM,CAACyC,KAAK,CAACR,cAAc,CAAC,CAACS,IAAI,CACnE1C,MAAM,CAAC2C,QAAQ,CAACJ,KAAK,CAACK,UAAU,CAAC,EACjC5C,MAAM,CAAC2C,QAAQ,CAACN,SAAS,CAACQ,OAAO,CAAC,CAAC,CAAC,CAAC,EACrC7C,MAAM,CAAC8C,OAAO,CAAEC,IAAI,IAAKA,IAAI,KAAKpB,KAAK,GAAG3B,MAAM,CAACgD,IAAI,GAAGR,MAAM,CAAC,CAChE;EACD,OAAO,IAAAE,cAAI,EACTH,KAAK,CAACU,IAAI,CAAC,CAAC,CAAC,EACbjD,MAAM,CAAC2C,QAAQ,CAACH,MAAM,CAAC,EACvBxC,MAAM,CAACkD,OAAO,EACdlD,MAAM,CAACmD,UAAU,EACjBnD,MAAM,CAACoD,aAAa,CACrB;EACD,MAAMH,IAAI,GAAGjD,MAAM,CAACqD,mBAAmB,CAAEC,OAAO,IAC9CtD,MAAM,CAAC8C,OAAO,CACZ7C,QAAQ,CAACW,GAAG,CAAC2C,WAAW,CAAC,EACxBC,IAAI,IAAKxD,MAAM,CAAC2C,QAAQ,CAACW,OAAO,CAACjB,SAAS,CAACY,IAAI,CAACO,IAAI,CAAC,CAAC,EAAEjB,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,CAAC,CAC3E,CACF;EACD,OAAQY,MAAM,IAAKzD,MAAM,CAAC2C,QAAQ,CAACM,IAAI,EAAEQ,MAAM,CAAC;AAClD,CAAC,CAAC;AAEJ,MAAM7B,WAAW,GAAGA,CAACD,KAAa,EAAEI,MAAqB,KAKvD/B,MAAM,CAACgC,GAAG,CAAC,aAAS;EAClB,MAAMK,SAAS,GAAG,OAAOrC,MAAM,CAACsC,aAAa,CAACX,KAAK,CAAC;EACpD,MAAMY,KAAK,GAAG,OAAOvC,MAAM,CAACsC,aAAa,CAAC,CAAC,CAAC;EAC5C,OAAO,IAAAI,cAAI,EACTH,KAAK,CAACU,IAAI,CAAC,CAAC,CAAC,EACbjD,MAAM,CAAC2C,QAAQ,CAAC3C,MAAM,CAACyC,KAAK,CAACV,MAAM,CAAC,CAAC,EACrC/B,MAAM,CAAC2C,QAAQ,CAACJ,KAAK,CAACK,UAAU,CAAC,EACjC5C,MAAM,CAAC2C,QAAQ,CAACN,SAAS,CAACO,UAAU,CAAC,EACrC5C,MAAM,CAACkD,OAAO,EACdlD,MAAM,CAACmD,UAAU,EACjBnD,MAAM,CAACoD,aAAa,CACrB;EACD,MAAMH,IAAI,GAAGjD,MAAM,CAACqD,mBAAmB,CAAEC,OAAO,IAC9CtD,MAAM,CAAC8C,OAAO,CACZ7C,QAAQ,CAACW,GAAG,CAAC2C,WAAW,CAAC,EACxBC,IAAI,IAAKxD,MAAM,CAAC2C,QAAQ,CAACW,OAAO,CAACjB,SAAS,CAACY,IAAI,CAACO,IAAI,CAAC,CAAC,EAAEjB,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,CAAC,CAC3E,CACF;EACD,OAAQY,MAAM,IAAKzD,MAAM,CAAC2C,QAAQ,CAACM,IAAI,EAAEQ,MAAM,CAAC;AAClD,CAAC,CAAC;AAEJ;AACA,MAAMF,WAAW,gBAAG,IAAAG,wBAAW,gBAC7BC,MAAM,CAACC,GAAG,CAAC,gCAAgC,CAAC,EAC5C,MAAM3D,QAAQ,CAAC4D,UAAU,CAAC,CAAC,CAAC,CAC7B;AAED;AACO,MAAMC,QAAQ,GAAIN,IAAY,IAAKxD,MAAM,CAAC+D,OAAO,CAACR,WAAW,EAAEC,IAAI,CAAC;AAAA1B,OAAA,CAAAgC,QAAA,GAAAA,QAAA", "ignoreList": []}