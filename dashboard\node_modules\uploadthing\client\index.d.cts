import { ExpandedRouteConfig } from '@uploadthing/shared';
export { UploadAbortedError, UploadPausedError, allowedContentTextLabelGenerator, bytesToFileSize, generateClientDropzoneAccept, generateMimeTypes, generatePermittedFileTypes } from '@uploadthing/shared';
import { FileRouter, GenerateUploaderOptions, EndpointArg, UploadFilesOptions, ClientUploadedFileData, inferEndpointOutput, CreateUploadOptions, RouteRegistry } from '../types/index.js';

declare const version: string;

/**
 * Validate that a file is of a valid type given a route config
 * @public
 */
declare const isValidFileType: (file: File, routeConfig: ExpandedRouteConfig) => boolean;
/**
 * Validate that a file is of a valid size given a route config
 * @public
 */
declare const isValidFileSize: (file: File, routeConfig: ExpandedRouteConfig) => boolean;
/**
 * Generate a typed uploader for a given FileRouter
 * @public
 */
declare const genUploader: <TRouter extends FileRouter>(initOpts?: GenerateUploaderOptions) => {
    uploadFiles: <TEndpoint extends keyof TRouter>(slug: EndpointArg<TRouter, TEndpoint>, opts: Omit<UploadFilesOptions<TRouter[TEndpoint]>, keyof GenerateUploaderOptions>) => Promise<ClientUploadedFileData<inferEndpointOutput<TRouter[TEndpoint]>>[]>;
    createUpload: <TEndpoint extends keyof TRouter, TServerOutput = inferEndpointOutput<TRouter[TEndpoint]>>(slug: EndpointArg<TRouter, TEndpoint>, opts: Omit<CreateUploadOptions<TRouter[TEndpoint]>, keyof GenerateUploaderOptions>) => Promise<{
        pauseUpload: (file?: File) => void;
        resumeUpload: (file?: File) => void;
        done: <T extends File | void = void>(file?: T) => Promise<T extends File ? ClientUploadedFileData<TServerOutput> : ClientUploadedFileData<TServerOutput>[]>;
    }>;
    /**
     * Identity object that can be used instead of raw strings
     * that allows "Go to definition" in your IDE to bring you
     * to the backend definition of a route.
     */
    routeRegistry: RouteRegistry<TRouter>;
};

export { genUploader, isValidFileSize, isValidFileType, version };
