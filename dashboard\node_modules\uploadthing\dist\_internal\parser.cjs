var Cause = require('effect/Cause');
var Data = require('effect/Data');
var Runtime = require('effect/Runtime');
var Schema = require('effect/Schema');

function _interopNamespace(e) {
  if (e && e.__esModule) return e;
  var n = Object.create(null);
  if (e) {
    Object.keys(e).forEach(function (k) {
      if (k !== 'default') {
        var d = Object.getOwnPropertyDescriptor(e, k);
        Object.defineProperty(n, k, d.get ? d : {
          enumerable: true,
          get: function () { return e[k]; }
        });
      }
    });
  }
  n.default = e;
  return n;
}

var Cause__namespace = /*#__PURE__*/_interopNamespace(Cause);
var Data__namespace = /*#__PURE__*/_interopNamespace(Data);
var Runtime__namespace = /*#__PURE__*/_interopNamespace(Runtime);
var Schema__namespace = /*#__PURE__*/_interopNamespace(Schema);

class ParserError extends Data__namespace.TaggedError("ParserError") {
    constructor(...args){
        super(...args), this.message = "Input validation failed. The original error with it's validation issues is in the error cause.";
    }
}
function getParseFn(parser) {
    if ("parseAsync" in parser && typeof parser.parseAsync === "function") {
        /**
     * Zod
     * TODO (next major): Consider wrapping ZodError in ParserError
     */ return parser.parseAsync;
    }
    if (Schema__namespace.isSchema(parser)) {
        /**
     * Effect Schema
     */ return (value)=>Schema__namespace.decodeUnknownPromise(parser)(value).catch((error)=>{
                throw new ParserError({
                    cause: Cause__namespace.squash(error[Runtime__namespace.FiberFailureCauseId])
                });
            });
    }
    if ("~standard" in parser) {
        /**
     * Standard Schema
     * TODO (next major): Consider moving this to the top of the function
     */ return async (value)=>{
            const result = await parser["~standard"].validate(value);
            if (result.issues) {
                throw new ParserError({
                    cause: result.issues
                });
            }
            return result.value;
        };
    }
    throw new Error("Invalid parser");
}

exports.ParserError = ParserError;
exports.getParseFn = getParseFn;
