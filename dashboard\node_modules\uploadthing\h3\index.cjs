var Effect = require('effect/Effect');
var h3 = require('h3');
var handler_cjs = require('../dist/_internal/handler.cjs');
var uploadBuilder_cjs = require('../dist/_internal/upload-builder.cjs');
var types_cjs = require('../dist/_internal/types.cjs');

function _interopNamespace(e) {
  if (e && e.__esModule) return e;
  var n = Object.create(null);
  if (e) {
    Object.keys(e).forEach(function (k) {
      if (k !== 'default') {
        var d = Object.getOwnPropertyDescriptor(e, k);
        Object.defineProperty(n, k, d.get ? d : {
          enumerable: true,
          get: function () { return e[k]; }
        });
      }
    });
  }
  n.default = e;
  return n;
}

var Effect__namespace = /*#__PURE__*/_interopNamespace(Effect);

const createUploadthing = (opts)=>uploadBuilder_cjs.createBuilder(opts);
const createRouteHandler = (opts)=>{
    const handler = handler_cjs.makeAdapterHandler((event)=>Effect__namespace.succeed({
            event
        }), (event)=>Effect__namespace.succeed(h3.toWebRequest(event)), opts, "h3");
    return h3.defineEventHandler(handler);
};

Object.defineProperty(exports, "UTFiles", {
  enumerable: true,
  get: function () { return types_cjs.UTFiles; }
});
Object.defineProperty(exports, "experimental_UTRegion", {
  enumerable: true,
  get: function () { return types_cjs.UTRegion; }
});
exports.createRouteHandler = createRouteHandler;
exports.createUploadthing = createUploadthing;
