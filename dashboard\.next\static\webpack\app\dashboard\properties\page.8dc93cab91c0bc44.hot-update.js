"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/properties/page",{

/***/ "(app-pages-browser)/./hooks/useSimpleLanguage.tsx":
/*!*************************************!*\
  !*** ./hooks/useSimpleLanguage.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSimpleLanguage: () => (/* binding */ useSimpleLanguage)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_cookieCleanup__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/cookieCleanup */ \"(app-pages-browser)/./lib/cookieCleanup.ts\");\n/* __next_internal_client_entry_do_not_use__ useSimpleLanguage auto */ var _s = $RefreshSig$();\n\n\n/**\n * Arabic-only language hook for Properties system\n * Pure Arabic interface with enhanced RTL support and cookie cleanup\n */ function useSimpleLanguage() {\n    _s();\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('ar');\n    // Initialize Arabic-only interface with cleanup\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSimpleLanguage.useEffect\": ()=>{\n            // Initialize Arabic environment and clean cookies\n            (0,_lib_cookieCleanup__WEBPACK_IMPORTED_MODULE_1__.initializeArabicEnvironment)();\n            (0,_lib_cookieCleanup__WEBPACK_IMPORTED_MODULE_1__.safeLog)('🏠 Arabic Properties system initialized');\n        }\n    }[\"useSimpleLanguage.useEffect\"], []);\n    return {\n        language: 'ar',\n        setLanguage: ()=>{},\n        isRTL: true,\n        isArabicOnly: true\n    };\n}\n_s(useSimpleLanguage, \"uNEeEraTehRiBcHGM2i1R+nM1u0=\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2hvb2tzL3VzZVNpbXBsZUxhbmd1YWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUU0QztBQUMrQjtBQUUzRTs7O0NBR0MsR0FDTSxTQUFTSTs7SUFDZCxNQUFNLENBQUNDLFVBQVVDLFlBQVksR0FBR04sK0NBQVFBLENBQU87SUFFL0MsZ0RBQWdEO0lBQ2hEQyxnREFBU0E7dUNBQUM7WUFDUixrREFBa0Q7WUFDbERDLCtFQUEyQkE7WUFFM0JDLDJEQUFPQSxDQUFDO1FBQ1Y7c0NBQUcsRUFBRTtJQUVMLE9BQU87UUFDTEUsVUFBVTtRQUNWQyxhQUFhLEtBQU87UUFDcEJDLE9BQU87UUFDUEMsY0FBYztJQUNoQjtBQUNGO0dBakJnQkoiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQWhtZWRcXERlc2t0b3BcXGNvZGVcXGJvb3RcXGRhc2hib2FyZFxcaG9va3NcXHVzZVNpbXBsZUxhbmd1YWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBpbml0aWFsaXplQXJhYmljRW52aXJvbm1lbnQsIHNhZmVMb2cgfSBmcm9tICdAL2xpYi9jb29raWVDbGVhbnVwJztcblxuLyoqXG4gKiBBcmFiaWMtb25seSBsYW5ndWFnZSBob29rIGZvciBQcm9wZXJ0aWVzIHN5c3RlbVxuICogUHVyZSBBcmFiaWMgaW50ZXJmYWNlIHdpdGggZW5oYW5jZWQgUlRMIHN1cHBvcnQgYW5kIGNvb2tpZSBjbGVhbnVwXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB1c2VTaW1wbGVMYW5ndWFnZSgpIHtcbiAgY29uc3QgW2xhbmd1YWdlLCBzZXRMYW5ndWFnZV0gPSB1c2VTdGF0ZTwnYXInPignYXInKTtcblxuICAvLyBJbml0aWFsaXplIEFyYWJpYy1vbmx5IGludGVyZmFjZSB3aXRoIGNsZWFudXBcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBJbml0aWFsaXplIEFyYWJpYyBlbnZpcm9ubWVudCBhbmQgY2xlYW4gY29va2llc1xuICAgIGluaXRpYWxpemVBcmFiaWNFbnZpcm9ubWVudCgpO1xuXG4gICAgc2FmZUxvZygn8J+PoCBBcmFiaWMgUHJvcGVydGllcyBzeXN0ZW0gaW5pdGlhbGl6ZWQnKTtcbiAgfSwgW10pO1xuXG4gIHJldHVybiB7XG4gICAgbGFuZ3VhZ2U6ICdhcicgYXMgY29uc3QsXG4gICAgc2V0TGFuZ3VhZ2U6ICgpID0+IHt9LCAvLyBObyBsYW5ndWFnZSBzd2l0Y2hpbmcgaW4gQXJhYmljLW9ubHkgbW9kZVxuICAgIGlzUlRMOiB0cnVlLFxuICAgIGlzQXJhYmljT25seTogdHJ1ZSxcbiAgfTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsImluaXRpYWxpemVBcmFiaWNFbnZpcm9ubWVudCIsInNhZmVMb2ciLCJ1c2VTaW1wbGVMYW5ndWFnZSIsImxhbmd1YWdlIiwic2V0TGFuZ3VhZ2UiLCJpc1JUTCIsImlzQXJhYmljT25seSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/useSimpleLanguage.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/cookieCleanup.ts":
/*!******************************!*\
  !*** ./lib/cookieCleanup.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cleanupCookies: () => (/* binding */ cleanupCookies),\n/* harmony export */   initializeArabicEnvironment: () => (/* binding */ initializeArabicEnvironment),\n/* harmony export */   isDevelopment: () => (/* binding */ isDevelopment),\n/* harmony export */   safeLog: () => (/* binding */ safeLog)\n/* harmony export */ });\n/* __next_internal_client_entry_do_not_use__ cleanupCookies,initializeArabicEnvironment,isDevelopment,safeLog auto */ /**\n * Cookie cleanup utility for Arabic Properties system\n * Removes unnecessary cookies and fixes session issues\n */ function cleanupCookies() {\n    if (false) {}\n    // List of cookies to remove\n    const cookiesToRemove = [\n        '__next_hmr_refresh_hash__',\n        '__clerk_db_jwt_NFfxy5s4',\n        '__refresh_NFfxy5s4',\n        '__session_NFfxy5s4',\n        '__client_uat_NFfxy5s4',\n        '__clerk_db_jwt_TYLMw0H7',\n        '__refresh_TYLMw0H7',\n        '__session_TYLMw0H7',\n        '__client_uat_TYLMw0H7',\n        '__clerk_db_jwt',\n        '__clerk_db_jwt_kCaGdcWF',\n        '__client_uat_kCaGdcWF',\n        '__client_uat',\n        'NEXT_LOCALE',\n        'authjs.csrf-token',\n        'authjs.callback-url'\n    ];\n    // Remove each cookie\n    cookiesToRemove.forEach((cookieName)=>{\n        // Remove from current domain\n        document.cookie = \"\".concat(cookieName, \"=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;\");\n        // Remove from all possible domains\n        const domains = [\n            window.location.hostname,\n            \".\".concat(window.location.hostname),\n            'localhost',\n            '.localhost'\n        ];\n        domains.forEach((domain)=>{\n            document.cookie = \"\".concat(cookieName, \"=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=\").concat(domain, \";\");\n        });\n    });\n    // Set Arabic language preference\n    document.cookie = 'language=ar; path=/; max-age=31536000'; // 1 year\n    console.log('🧹 Cookies cleaned up for Arabic Properties system');\n}\n/**\n * Initialize Arabic-only environment\n */ function initializeArabicEnvironment() {\n    if (false) {}\n    // Clean up cookies first\n    cleanupCookies();\n    // Set document language and direction\n    document.documentElement.lang = 'ar';\n    document.documentElement.dir = 'rtl';\n    document.documentElement.className = 'rtl arabic-interface';\n    // Set Arabic fonts\n    document.body.style.fontFamily = \"'Cairo', 'Noto Sans Arabic', 'Tajawal', 'Amiri', sans-serif\";\n    // Add Arabic CSS class to body\n    document.body.classList.add('lang-ar', 'rtl-container');\n    console.log('🌟 Arabic environment initialized');\n}\n/**\n * Check if running in development mode\n */ function isDevelopment() {\n    return \"development\" === 'development';\n}\n/**\n * Safe console log for production\n */ function safeLog(message) {\n    for(var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n        args[_key - 1] = arguments[_key];\n    }\n    if (isDevelopment()) {\n        console.log(message, ...args);\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/cookieCleanup.ts\n"));

/***/ })

});