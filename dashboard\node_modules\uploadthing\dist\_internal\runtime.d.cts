import * as _uploadthing_shared from '@uploadthing/shared';
import { FetchEsque } from '@uploadthing/shared';
import * as _effect_platform_HttpClient from '@effect/platform/HttpClient';
import { FetchHttpClient } from '@effect/platform';
import * as ManagedRuntime from 'effect/ManagedRuntime';

declare const makeRuntime: (fetch: FetchEsque, config: unknown) => ManagedRuntime.ManagedRuntime<_effect_platform_HttpClient.HttpClient | FetchHttpClient.Fetch, _uploadthing_shared.UploadThingError<{
    message: string;
}>>;

export { makeRuntime };
