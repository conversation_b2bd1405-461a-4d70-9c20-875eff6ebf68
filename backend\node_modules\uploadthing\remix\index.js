import * as Effect from 'effect/Effect';
import { makeAdapter<PERSON>and<PERSON> } from '../dist/_internal/handler.js';
import { createBuilder } from '../dist/_internal/upload-builder.js';
export { UTFiles, UTRegion as experimental_UTRegion } from '../dist/_internal/types.js';

const createUploadthing = (opts)=>createBuilder(opts);
const createRouteHandler = (opts)=>{
    const handler = makeAdapterHandler((args)=>Effect.succeed({
            event: args
        }), (args)=>Effect.succeed(args.request), opts, "remix");
    return {
        action: handler,
        loader: handler
    };
};

export { createRouteHandler, createUploadthing };
