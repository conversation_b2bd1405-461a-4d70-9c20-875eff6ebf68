import * as effect_ConfigError from 'effect/ConfigError';
import * as effect_Redacted from 'effect/Redacted';
import * as Config from 'effect/Config';
import * as ConfigProvider from 'effect/ConfigProvider';
import * as Effect from 'effect/Effect';
import { UploadThingError } from '@uploadthing/shared';

declare let version: string;

/**
 * Config provider that merges the options from the object
 * and environment variables prefixed with `UPLOADTHING_`.
 * @remarks Options take precedence over environment variables.
 */
declare const configProvider: (options: unknown) => ConfigProvider.ConfigProvider;
declare const IsDevelopment: Config.Config<boolean>;
declare const UTToken: Effect.Effect<{
    readonly apiKey: effect_Redacted.Redacted<string>;
    readonly appId: string;
    readonly regions: readonly [string, ...string[]];
    readonly ingestHost: string;
}, UploadThingError<{
    message: string;
}>, never>;
declare const ApiUrl: Config.Config<string>;
declare const IngestUrl: (preferredRegion: string | undefined) => Effect.Effect<string, effect_ConfigError.ConfigError | UploadThingError<{
    message: string;
}>, never>;
declare const UtfsHost: Config.Config<string>;
declare const UfsHost: Config.Config<string>;

export { ApiUrl, IngestUrl, IsDevelopment, version as UPLOADTHING_VERSION, UTToken, UfsHost, UtfsHost, configProvider };
