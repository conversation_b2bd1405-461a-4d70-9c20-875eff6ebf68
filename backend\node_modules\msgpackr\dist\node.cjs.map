{"version": 3, "file": "node.cjs", "sources": ["../unpack.js", "../pack.js", "../struct.js", "../stream.js", "../iterators.js", "../node-index.js"], "sourcesContent": ["var decoder\ntry {\n\tdecoder = new TextDecoder()\n} catch(error) {}\nvar src\nvar srcEnd\nvar position = 0\nvar alreadySet\nconst EMPTY_ARRAY = []\nvar strings = EMPTY_ARRAY\nvar stringPosition = 0\nvar currentUnpackr = {}\nvar currentStructures\nvar srcString\nvar srcStringStart = 0\nvar srcStringEnd = 0\nvar bundledStrings\nvar referenceMap\nvar currentExtensions = []\nvar dataView\nvar defaultOptions = {\n\tuseRecords: false,\n\tmapsAsObjects: true\n}\nexport class C1Type {}\nexport const C1 = new C1Type()\nC1.name = 'MessagePack 0xC1'\nvar sequentialMode = false\nvar inlineObjectReadThreshold = 2\nvar readStruct, onLoadedStructures, onSaveState\nvar BlockedFunction // we use search and replace to change the next call to BlockedFunction to avoid CSP issues for\n// no-eval build\ntry {\n\tnew Function('')\n} catch(error) {\n\t// if eval variants are not supported, do not create inline object readers ever\n\tinlineObjectReadThreshold = Infinity\n}\n\nexport class Unpackr {\n\tconstructor(options) {\n\t\tif (options) {\n\t\t\tif (options.useRecords === false && options.mapsAsObjects === undefined)\n\t\t\t\toptions.mapsAsObjects = true\n\t\t\tif (options.sequential && options.trusted !== false) {\n\t\t\t\toptions.trusted = true;\n\t\t\t\tif (!options.structures && options.useRecords != false) {\n\t\t\t\t\toptions.structures = []\n\t\t\t\t\tif (!options.maxSharedStructures)\n\t\t\t\t\t\toptions.maxSharedStructures = 0\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (options.structures)\n\t\t\t\toptions.structures.sharedLength = options.structures.length\n\t\t\telse if (options.getStructures) {\n\t\t\t\t(options.structures = []).uninitialized = true // this is what we use to denote an uninitialized structures\n\t\t\t\toptions.structures.sharedLength = 0\n\t\t\t}\n\t\t\tif (options.int64AsNumber) {\n\t\t\t\toptions.int64AsType = 'number'\n\t\t\t}\n\t\t}\n\t\tObject.assign(this, options)\n\t}\n\tunpack(source, options) {\n\t\tif (src) {\n\t\t\t// re-entrant execution, save the state and restore it after we do this unpack\n\t\t\treturn saveState(() => {\n\t\t\t\tclearSource()\n\t\t\t\treturn this ? this.unpack(source, options) : Unpackr.prototype.unpack.call(defaultOptions, source, options)\n\t\t\t})\n\t\t}\n\t\tif (!source.buffer && source.constructor === ArrayBuffer)\n\t\t\tsource = typeof Buffer !== 'undefined' ? Buffer.from(source) : new Uint8Array(source);\n\t\tif (typeof options === 'object') {\n\t\t\tsrcEnd = options.end || source.length\n\t\t\tposition = options.start || 0\n\t\t} else {\n\t\t\tposition = 0\n\t\t\tsrcEnd = options > -1 ? options : source.length\n\t\t}\n\t\tstringPosition = 0\n\t\tsrcStringEnd = 0\n\t\tsrcString = null\n\t\tstrings = EMPTY_ARRAY\n\t\tbundledStrings = null\n\t\tsrc = source\n\t\t// this provides cached access to the data view for a buffer if it is getting reused, which is a recommend\n\t\t// technique for getting data from a database where it can be copied into an existing buffer instead of creating\n\t\t// new ones\n\t\ttry {\n\t\t\tdataView = source.dataView || (source.dataView = new DataView(source.buffer, source.byteOffset, source.byteLength))\n\t\t} catch(error) {\n\t\t\t// if it doesn't have a buffer, maybe it is the wrong type of object\n\t\t\tsrc = null\n\t\t\tif (source instanceof Uint8Array)\n\t\t\t\tthrow error\n\t\t\tthrow new Error('Source must be a Uint8Array or Buffer but was a ' + ((source && typeof source == 'object') ? source.constructor.name : typeof source))\n\t\t}\n\t\tif (this instanceof Unpackr) {\n\t\t\tcurrentUnpackr = this\n\t\t\tif (this.structures) {\n\t\t\t\tcurrentStructures = this.structures\n\t\t\t\treturn checkedRead(options)\n\t\t\t} else if (!currentStructures || currentStructures.length > 0) {\n\t\t\t\tcurrentStructures = []\n\t\t\t}\n\t\t} else {\n\t\t\tcurrentUnpackr = defaultOptions\n\t\t\tif (!currentStructures || currentStructures.length > 0)\n\t\t\t\tcurrentStructures = []\n\t\t}\n\t\treturn checkedRead(options)\n\t}\n\tunpackMultiple(source, forEach) {\n\t\tlet values, lastPosition = 0\n\t\ttry {\n\t\t\tsequentialMode = true\n\t\t\tlet size = source.length\n\t\t\tlet value = this ? this.unpack(source, size) : defaultUnpackr.unpack(source, size)\n\t\t\tif (forEach) {\n\t\t\t\tif (forEach(value, lastPosition, position) === false) return;\n\t\t\t\twhile(position < size) {\n\t\t\t\t\tlastPosition = position\n\t\t\t\t\tif (forEach(checkedRead(), lastPosition, position) === false) {\n\t\t\t\t\t\treturn\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\telse {\n\t\t\t\tvalues = [ value ]\n\t\t\t\twhile(position < size) {\n\t\t\t\t\tlastPosition = position\n\t\t\t\t\tvalues.push(checkedRead())\n\t\t\t\t}\n\t\t\t\treturn values\n\t\t\t}\n\t\t} catch(error) {\n\t\t\terror.lastPosition = lastPosition\n\t\t\terror.values = values\n\t\t\tthrow error\n\t\t} finally {\n\t\t\tsequentialMode = false\n\t\t\tclearSource()\n\t\t}\n\t}\n\t_mergeStructures(loadedStructures, existingStructures) {\n\t\tif (onLoadedStructures)\n\t\t\tloadedStructures = onLoadedStructures.call(this, loadedStructures);\n\t\tloadedStructures = loadedStructures || []\n\t\tif (Object.isFrozen(loadedStructures))\n\t\t\tloadedStructures = loadedStructures.map(structure => structure.slice(0))\n\t\tfor (let i = 0, l = loadedStructures.length; i < l; i++) {\n\t\t\tlet structure = loadedStructures[i]\n\t\t\tif (structure) {\n\t\t\t\tstructure.isShared = true\n\t\t\t\tif (i >= 32)\n\t\t\t\t\tstructure.highByte = (i - 32) >> 5\n\t\t\t}\n\t\t}\n\t\tloadedStructures.sharedLength = loadedStructures.length\n\t\tfor (let id in existingStructures || []) {\n\t\t\tif (id >= 0) {\n\t\t\t\tlet structure = loadedStructures[id]\n\t\t\t\tlet existing = existingStructures[id]\n\t\t\t\tif (existing) {\n\t\t\t\t\tif (structure)\n\t\t\t\t\t\t(loadedStructures.restoreStructures || (loadedStructures.restoreStructures = []))[id] = structure\n\t\t\t\t\tloadedStructures[id] = existing\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn this.structures = loadedStructures\n\t}\n\tdecode(source, options) {\n\t\treturn this.unpack(source, options)\n\t}\n}\nexport function getPosition() {\n\treturn position\n}\nexport function checkedRead(options) {\n\ttry {\n\t\tif (!currentUnpackr.trusted && !sequentialMode) {\n\t\t\tlet sharedLength = currentStructures.sharedLength || 0\n\t\t\tif (sharedLength < currentStructures.length)\n\t\t\t\tcurrentStructures.length = sharedLength\n\t\t}\n\t\tlet result\n\t\tif (currentUnpackr.randomAccessStructure && src[position] < 0x40 && src[position] >= 0x20 && readStruct) {\n\t\t\tresult = readStruct(src, position, srcEnd, currentUnpackr)\n\t\t\tsrc = null // dispose of this so that recursive unpack calls don't save state\n\t\t\tif (!(options && options.lazy) && result)\n\t\t\t\tresult = result.toJSON()\n\t\t\tposition = srcEnd\n\t\t} else\n\t\t\tresult = read()\n\t\tif (bundledStrings) { // bundled strings to skip past\n\t\t\tposition = bundledStrings.postBundlePosition\n\t\t\tbundledStrings = null\n\t\t}\n\t\tif (sequentialMode)\n\t\t\t// we only need to restore the structures if there was an error, but if we completed a read,\n\t\t\t// we can clear this out and keep the structures we read\n\t\t\tcurrentStructures.restoreStructures = null\n\n\t\tif (position == srcEnd) {\n\t\t\t// finished reading this source, cleanup references\n\t\t\tif (currentStructures && currentStructures.restoreStructures)\n\t\t\t\trestoreStructures()\n\t\t\tcurrentStructures = null\n\t\t\tsrc = null\n\t\t\tif (referenceMap)\n\t\t\t\treferenceMap = null\n\t\t} else if (position > srcEnd) {\n\t\t\t// over read\n\t\t\tthrow new Error('Unexpected end of MessagePack data')\n\t\t} else if (!sequentialMode) {\n\t\t\tlet jsonView;\n\t\t\ttry {\n\t\t\t\tjsonView = JSON.stringify(result, (_, value) => typeof value === \"bigint\" ? `${value}n` : value).slice(0, 100)\n\t\t\t} catch(error) {\n\t\t\t\tjsonView = '(JSON view not available ' + error + ')'\n\t\t\t}\n\t\t\tthrow new Error('Data read, but end of buffer not reached ' + jsonView)\n\t\t}\n\t\t// else more to read, but we are reading sequentially, so don't clear source yet\n\t\treturn result\n\t} catch(error) {\n\t\tif (currentStructures && currentStructures.restoreStructures)\n\t\t\trestoreStructures()\n\t\tclearSource()\n\t\tif (error instanceof RangeError || error.message.startsWith('Unexpected end of buffer') || position > srcEnd) {\n\t\t\terror.incomplete = true\n\t\t}\n\t\tthrow error\n\t}\n}\n\nfunction restoreStructures() {\n\tfor (let id in currentStructures.restoreStructures) {\n\t\tcurrentStructures[id] = currentStructures.restoreStructures[id]\n\t}\n\tcurrentStructures.restoreStructures = null\n}\n\nexport function read() {\n\tlet token = src[position++]\n\tif (token < 0xa0) {\n\t\tif (token < 0x80) {\n\t\t\tif (token < 0x40)\n\t\t\t\treturn token\n\t\t\telse {\n\t\t\t\tlet structure = currentStructures[token & 0x3f] ||\n\t\t\t\t\tcurrentUnpackr.getStructures && loadStructures()[token & 0x3f]\n\t\t\t\tif (structure) {\n\t\t\t\t\tif (!structure.read) {\n\t\t\t\t\t\tstructure.read = createStructureReader(structure, token & 0x3f)\n\t\t\t\t\t}\n\t\t\t\t\treturn structure.read()\n\t\t\t\t} else\n\t\t\t\t\treturn token\n\t\t\t}\n\t\t} else if (token < 0x90) {\n\t\t\t// map\n\t\t\ttoken -= 0x80\n\t\t\tif (currentUnpackr.mapsAsObjects) {\n\t\t\t\tlet object = {}\n\t\t\t\tfor (let i = 0; i < token; i++) {\n\t\t\t\t\tlet key = readKey()\n\t\t\t\t\tif (key === '__proto__')\n\t\t\t\t\t\tkey = '__proto_'\n\t\t\t\t\tobject[key] = read()\n\t\t\t\t}\n\t\t\t\treturn object\n\t\t\t} else {\n\t\t\t\tlet map = new Map()\n\t\t\t\tfor (let i = 0; i < token; i++) {\n\t\t\t\t\tmap.set(read(), read())\n\t\t\t\t}\n\t\t\t\treturn map\n\t\t\t}\n\t\t} else {\n\t\t\ttoken -= 0x90\n\t\t\tlet array = new Array(token)\n\t\t\tfor (let i = 0; i < token; i++) {\n\t\t\t\tarray[i] = read()\n\t\t\t}\n\t\t\tif (currentUnpackr.freezeData)\n\t\t\t\treturn Object.freeze(array)\n\t\t\treturn array\n\t\t}\n\t} else if (token < 0xc0) {\n\t\t// fixstr\n\t\tlet length = token - 0xa0\n\t\tif (srcStringEnd >= position) {\n\t\t\treturn srcString.slice(position - srcStringStart, (position += length) - srcStringStart)\n\t\t}\n\t\tif (srcStringEnd == 0 && srcEnd < 140) {\n\t\t\t// for small blocks, avoiding the overhead of the extract call is helpful\n\t\t\tlet string = length < 16 ? shortStringInJS(length) : longStringInJS(length)\n\t\t\tif (string != null)\n\t\t\t\treturn string\n\t\t}\n\t\treturn readFixedString(length)\n\t} else {\n\t\tlet value\n\t\tswitch (token) {\n\t\t\tcase 0xc0: return null\n\t\t\tcase 0xc1:\n\t\t\t\tif (bundledStrings) {\n\t\t\t\t\tvalue = read() // followed by the length of the string in characters (not bytes!)\n\t\t\t\t\tif (value > 0)\n\t\t\t\t\t\treturn bundledStrings[1].slice(bundledStrings.position1, bundledStrings.position1 += value)\n\t\t\t\t\telse\n\t\t\t\t\t\treturn bundledStrings[0].slice(bundledStrings.position0, bundledStrings.position0 -= value)\n\t\t\t\t}\n\t\t\t\treturn C1; // \"never-used\", return special object to denote that\n\t\t\tcase 0xc2: return false\n\t\t\tcase 0xc3: return true\n\t\t\tcase 0xc4:\n\t\t\t\t// bin 8\n\t\t\t\tvalue = src[position++]\n\t\t\t\tif (value === undefined)\n\t\t\t\t\tthrow new Error('Unexpected end of buffer')\n\t\t\t\treturn readBin(value)\n\t\t\tcase 0xc5:\n\t\t\t\t// bin 16\n\t\t\t\tvalue = dataView.getUint16(position)\n\t\t\t\tposition += 2\n\t\t\t\treturn readBin(value)\n\t\t\tcase 0xc6:\n\t\t\t\t// bin 32\n\t\t\t\tvalue = dataView.getUint32(position)\n\t\t\t\tposition += 4\n\t\t\t\treturn readBin(value)\n\t\t\tcase 0xc7:\n\t\t\t\t// ext 8\n\t\t\t\treturn readExt(src[position++])\n\t\t\tcase 0xc8:\n\t\t\t\t// ext 16\n\t\t\t\tvalue = dataView.getUint16(position)\n\t\t\t\tposition += 2\n\t\t\t\treturn readExt(value)\n\t\t\tcase 0xc9:\n\t\t\t\t// ext 32\n\t\t\t\tvalue = dataView.getUint32(position)\n\t\t\t\tposition += 4\n\t\t\t\treturn readExt(value)\n\t\t\tcase 0xca:\n\t\t\t\tvalue = dataView.getFloat32(position)\n\t\t\t\tif (currentUnpackr.useFloat32 > 2) {\n\t\t\t\t\t// this does rounding of numbers that were encoded in 32-bit float to nearest significant decimal digit that could be preserved\n\t\t\t\t\tlet multiplier = mult10[((src[position] & 0x7f) << 1) | (src[position + 1] >> 7)]\n\t\t\t\t\tposition += 4\n\t\t\t\t\treturn ((multiplier * value + (value > 0 ? 0.5 : -0.5)) >> 0) / multiplier\n\t\t\t\t}\n\t\t\t\tposition += 4\n\t\t\t\treturn value\n\t\t\tcase 0xcb:\n\t\t\t\tvalue = dataView.getFloat64(position)\n\t\t\t\tposition += 8\n\t\t\t\treturn value\n\t\t\t// uint handlers\n\t\t\tcase 0xcc:\n\t\t\t\treturn src[position++]\n\t\t\tcase 0xcd:\n\t\t\t\tvalue = dataView.getUint16(position)\n\t\t\t\tposition += 2\n\t\t\t\treturn value\n\t\t\tcase 0xce:\n\t\t\t\tvalue = dataView.getUint32(position)\n\t\t\t\tposition += 4\n\t\t\t\treturn value\n\t\t\tcase 0xcf:\n\t\t\t\tif (currentUnpackr.int64AsType === 'number') {\n\t\t\t\t\tvalue = dataView.getUint32(position) * 0x100000000\n\t\t\t\t\tvalue += dataView.getUint32(position + 4)\n\t\t\t\t} else if (currentUnpackr.int64AsType === 'string') {\n\t\t\t\t\tvalue = dataView.getBigUint64(position).toString()\n\t\t\t\t} else if (currentUnpackr.int64AsType === 'auto') {\n\t\t\t\t\tvalue = dataView.getBigUint64(position)\n\t\t\t\t\tif (value<=BigInt(2)<<BigInt(52)) value=Number(value)\n\t\t\t\t} else\n\t\t\t\t\tvalue = dataView.getBigUint64(position)\n\t\t\t\tposition += 8\n\t\t\t\treturn value\n\n\t\t\t// int handlers\n\t\t\tcase 0xd0:\n\t\t\t\treturn dataView.getInt8(position++)\n\t\t\tcase 0xd1:\n\t\t\t\tvalue = dataView.getInt16(position)\n\t\t\t\tposition += 2\n\t\t\t\treturn value\n\t\t\tcase 0xd2:\n\t\t\t\tvalue = dataView.getInt32(position)\n\t\t\t\tposition += 4\n\t\t\t\treturn value\n\t\t\tcase 0xd3:\n\t\t\t\tif (currentUnpackr.int64AsType === 'number') {\n\t\t\t\t\tvalue = dataView.getInt32(position) * 0x100000000\n\t\t\t\t\tvalue += dataView.getUint32(position + 4)\n\t\t\t\t} else if (currentUnpackr.int64AsType === 'string') {\n\t\t\t\t\tvalue = dataView.getBigInt64(position).toString()\n\t\t\t\t} else if (currentUnpackr.int64AsType === 'auto') {\n\t\t\t\t\tvalue = dataView.getBigInt64(position)\n\t\t\t\t\tif (value>=BigInt(-2)<<BigInt(52)&&value<=BigInt(2)<<BigInt(52)) value=Number(value)\n\t\t\t\t} else\n\t\t\t\t\tvalue = dataView.getBigInt64(position)\n\t\t\t\tposition += 8\n\t\t\t\treturn value\n\n\t\t\tcase 0xd4:\n\t\t\t\t// fixext 1\n\t\t\t\tvalue = src[position++]\n\t\t\t\tif (value == 0x72) {\n\t\t\t\t\treturn recordDefinition(src[position++] & 0x3f)\n\t\t\t\t} else {\n\t\t\t\t\tlet extension = currentExtensions[value]\n\t\t\t\t\tif (extension) {\n\t\t\t\t\t\tif (extension.read) {\n\t\t\t\t\t\t\tposition++ // skip filler byte\n\t\t\t\t\t\t\treturn extension.read(read())\n\t\t\t\t\t\t} else if (extension.noBuffer) {\n\t\t\t\t\t\t\tposition++ // skip filler byte\n\t\t\t\t\t\t\treturn extension()\n\t\t\t\t\t\t} else\n\t\t\t\t\t\t\treturn extension(src.subarray(position, ++position))\n\t\t\t\t\t} else\n\t\t\t\t\t\tthrow new Error('Unknown extension ' + value)\n\t\t\t\t}\n\t\t\tcase 0xd5:\n\t\t\t\t// fixext 2\n\t\t\t\tvalue = src[position]\n\t\t\t\tif (value == 0x72) {\n\t\t\t\t\tposition++\n\t\t\t\t\treturn recordDefinition(src[position++] & 0x3f, src[position++])\n\t\t\t\t} else\n\t\t\t\t\treturn readExt(2)\n\t\t\tcase 0xd6:\n\t\t\t\t// fixext 4\n\t\t\t\treturn readExt(4)\n\t\t\tcase 0xd7:\n\t\t\t\t// fixext 8\n\t\t\t\treturn readExt(8)\n\t\t\tcase 0xd8:\n\t\t\t\t// fixext 16\n\t\t\t\treturn readExt(16)\n\t\t\tcase 0xd9:\n\t\t\t// str 8\n\t\t\t\tvalue = src[position++]\n\t\t\t\tif (srcStringEnd >= position) {\n\t\t\t\t\treturn srcString.slice(position - srcStringStart, (position += value) - srcStringStart)\n\t\t\t\t}\n\t\t\t\treturn readString8(value)\n\t\t\tcase 0xda:\n\t\t\t// str 16\n\t\t\t\tvalue = dataView.getUint16(position)\n\t\t\t\tposition += 2\n\t\t\t\tif (srcStringEnd >= position) {\n\t\t\t\t\treturn srcString.slice(position - srcStringStart, (position += value) - srcStringStart)\n\t\t\t\t}\n\t\t\t\treturn readString16(value)\n\t\t\tcase 0xdb:\n\t\t\t// str 32\n\t\t\t\tvalue = dataView.getUint32(position)\n\t\t\t\tposition += 4\n\t\t\t\tif (srcStringEnd >= position) {\n\t\t\t\t\treturn srcString.slice(position - srcStringStart, (position += value) - srcStringStart)\n\t\t\t\t}\n\t\t\t\treturn readString32(value)\n\t\t\tcase 0xdc:\n\t\t\t// array 16\n\t\t\t\tvalue = dataView.getUint16(position)\n\t\t\t\tposition += 2\n\t\t\t\treturn readArray(value)\n\t\t\tcase 0xdd:\n\t\t\t// array 32\n\t\t\t\tvalue = dataView.getUint32(position)\n\t\t\t\tposition += 4\n\t\t\t\treturn readArray(value)\n\t\t\tcase 0xde:\n\t\t\t// map 16\n\t\t\t\tvalue = dataView.getUint16(position)\n\t\t\t\tposition += 2\n\t\t\t\treturn readMap(value)\n\t\t\tcase 0xdf:\n\t\t\t// map 32\n\t\t\t\tvalue = dataView.getUint32(position)\n\t\t\t\tposition += 4\n\t\t\t\treturn readMap(value)\n\t\t\tdefault: // negative int\n\t\t\t\tif (token >= 0xe0)\n\t\t\t\t\treturn token - 0x100\n\t\t\t\tif (token === undefined) {\n\t\t\t\t\tlet error = new Error('Unexpected end of MessagePack data')\n\t\t\t\t\terror.incomplete = true\n\t\t\t\t\tthrow error\n\t\t\t\t}\n\t\t\t\tthrow new Error('Unknown MessagePack token ' + token)\n\n\t\t}\n\t}\n}\nconst validName = /^[a-zA-Z_$][a-zA-Z\\d_$]*$/\nfunction createStructureReader(structure, firstId) {\n\tfunction readObject() {\n\t\t// This initial function is quick to instantiate, but runs slower. After several iterations pay the cost to build the faster function\n\t\tif (readObject.count++ > inlineObjectReadThreshold) {\n\t\t\tlet readObject = structure.read = (new Function('r', 'return function(){return ' + (currentUnpackr.freezeData ? 'Object.freeze' : '') +\n\t\t\t\t'({' + structure.map(key => key === '__proto__' ? '__proto_:r()' : validName.test(key) ? key + ':r()' : ('[' + JSON.stringify(key) + ']:r()')).join(',') + '})}'))(read)\n\t\t\tif (structure.highByte === 0)\n\t\t\t\tstructure.read = createSecondByteReader(firstId, structure.read)\n\t\t\treturn readObject() // second byte is already read, if there is one so immediately read object\n\t\t}\n\t\tlet object = {}\n\t\tfor (let i = 0, l = structure.length; i < l; i++) {\n\t\t\tlet key = structure[i]\n\t\t\tif (key === '__proto__')\n\t\t\t\tkey = '__proto_'\n\t\t\tobject[key] = read()\n\t\t}\n\t\tif (currentUnpackr.freezeData)\n\t\t\treturn Object.freeze(object);\n\t\treturn object\n\t}\n\treadObject.count = 0\n\tif (structure.highByte === 0) {\n\t\treturn createSecondByteReader(firstId, readObject)\n\t}\n\treturn readObject\n}\n\nconst createSecondByteReader = (firstId, read0) => {\n\treturn function() {\n\t\tlet highByte = src[position++]\n\t\tif (highByte === 0)\n\t\t\treturn read0()\n\t\tlet id = firstId < 32 ? -(firstId + (highByte << 5)) : firstId + (highByte << 5)\n\t\tlet structure = currentStructures[id] || loadStructures()[id]\n\t\tif (!structure) {\n\t\t\tthrow new Error('Record id is not defined for ' + id)\n\t\t}\n\t\tif (!structure.read)\n\t\t\tstructure.read = createStructureReader(structure, firstId)\n\t\treturn structure.read()\n\t}\n}\n\nexport function loadStructures() {\n\tlet loadedStructures = saveState(() => {\n\t\t// save the state in case getStructures modifies our buffer\n\t\tsrc = null\n\t\treturn currentUnpackr.getStructures()\n\t})\n\treturn currentStructures = currentUnpackr._mergeStructures(loadedStructures, currentStructures)\n}\n\nvar readFixedString = readStringJS\nvar readString8 = readStringJS\nvar readString16 = readStringJS\nvar readString32 = readStringJS\nexport let isNativeAccelerationEnabled = false\n\nexport function setExtractor(extractStrings) {\n\tisNativeAccelerationEnabled = true\n\treadFixedString = readString(1)\n\treadString8 = readString(2)\n\treadString16 = readString(3)\n\treadString32 = readString(5)\n\tfunction readString(headerLength) {\n\t\treturn function readString(length) {\n\t\t\tlet string = strings[stringPosition++]\n\t\t\tif (string == null) {\n\t\t\t\tif (bundledStrings)\n\t\t\t\t\treturn readStringJS(length)\n\t\t\t\tlet byteOffset = src.byteOffset\n\t\t\t\tlet extraction = extractStrings(position - headerLength + byteOffset, srcEnd + byteOffset, src.buffer)\n\t\t\t\tif (typeof extraction == 'string') {\n\t\t\t\t\tstring = extraction\n\t\t\t\t\tstrings = EMPTY_ARRAY\n\t\t\t\t} else {\n\t\t\t\t\tstrings = extraction\n\t\t\t\t\tstringPosition = 1\n\t\t\t\t\tsrcStringEnd = 1 // even if a utf-8 string was decoded, must indicate we are in the midst of extracted strings and can't skip strings\n\t\t\t\t\tstring = strings[0]\n\t\t\t\t\tif (string === undefined)\n\t\t\t\t\t\tthrow new Error('Unexpected end of buffer')\n\t\t\t\t}\n\t\t\t}\n\t\t\tlet srcStringLength = string.length\n\t\t\tif (srcStringLength <= length) {\n\t\t\t\tposition += length\n\t\t\t\treturn string\n\t\t\t}\n\t\t\tsrcString = string\n\t\t\tsrcStringStart = position\n\t\t\tsrcStringEnd = position + srcStringLength\n\t\t\tposition += length\n\t\t\treturn string.slice(0, length) // we know we just want the beginning\n\t\t}\n\t}\n}\nfunction readStringJS(length) {\n\tlet result\n\tif (length < 16) {\n\t\tif (result = shortStringInJS(length))\n\t\t\treturn result\n\t}\n\tif (length > 64 && decoder)\n\t\treturn decoder.decode(src.subarray(position, position += length))\n\tconst end = position + length\n\tconst units = []\n\tresult = ''\n\twhile (position < end) {\n\t\tconst byte1 = src[position++]\n\t\tif ((byte1 & 0x80) === 0) {\n\t\t\t// 1 byte\n\t\t\tunits.push(byte1)\n\t\t} else if ((byte1 & 0xe0) === 0xc0) {\n\t\t\t// 2 bytes\n\t\t\tconst byte2 = src[position++] & 0x3f\n\t\t\tunits.push(((byte1 & 0x1f) << 6) | byte2)\n\t\t} else if ((byte1 & 0xf0) === 0xe0) {\n\t\t\t// 3 bytes\n\t\t\tconst byte2 = src[position++] & 0x3f\n\t\t\tconst byte3 = src[position++] & 0x3f\n\t\t\tunits.push(((byte1 & 0x1f) << 12) | (byte2 << 6) | byte3)\n\t\t} else if ((byte1 & 0xf8) === 0xf0) {\n\t\t\t// 4 bytes\n\t\t\tconst byte2 = src[position++] & 0x3f\n\t\t\tconst byte3 = src[position++] & 0x3f\n\t\t\tconst byte4 = src[position++] & 0x3f\n\t\t\tlet unit = ((byte1 & 0x07) << 0x12) | (byte2 << 0x0c) | (byte3 << 0x06) | byte4\n\t\t\tif (unit > 0xffff) {\n\t\t\t\tunit -= 0x10000\n\t\t\t\tunits.push(((unit >>> 10) & 0x3ff) | 0xd800)\n\t\t\t\tunit = 0xdc00 | (unit & 0x3ff)\n\t\t\t}\n\t\t\tunits.push(unit)\n\t\t} else {\n\t\t\tunits.push(byte1)\n\t\t}\n\n\t\tif (units.length >= 0x1000) {\n\t\t\tresult += fromCharCode.apply(String, units)\n\t\t\tunits.length = 0\n\t\t}\n\t}\n\n\tif (units.length > 0) {\n\t\tresult += fromCharCode.apply(String, units)\n\t}\n\n\treturn result\n}\nexport function readString(source, start, length) {\n\tlet existingSrc = src;\n\tsrc = source;\n\tposition = start;\n\ttry {\n\t\treturn readStringJS(length);\n\t} finally {\n\t\tsrc = existingSrc;\n\t}\n}\n\nfunction readArray(length) {\n\tlet array = new Array(length)\n\tfor (let i = 0; i < length; i++) {\n\t\tarray[i] = read()\n\t}\n\tif (currentUnpackr.freezeData)\n\t\treturn Object.freeze(array)\n\treturn array\n}\n\nfunction readMap(length) {\n\tif (currentUnpackr.mapsAsObjects) {\n\t\tlet object = {}\n\t\tfor (let i = 0; i < length; i++) {\n\t\t\tlet key = readKey()\n\t\t\tif (key === '__proto__')\n\t\t\t\tkey = '__proto_';\n\t\t\tobject[key] = read()\n\t\t}\n\t\treturn object\n\t} else {\n\t\tlet map = new Map()\n\t\tfor (let i = 0; i < length; i++) {\n\t\t\tmap.set(read(), read())\n\t\t}\n\t\treturn map\n\t}\n}\n\nvar fromCharCode = String.fromCharCode\nfunction longStringInJS(length) {\n\tlet start = position\n\tlet bytes = new Array(length)\n\tfor (let i = 0; i < length; i++) {\n\t\tconst byte = src[position++];\n\t\tif ((byte & 0x80) > 0) {\n\t\t\t\tposition = start\n\t\t\t\treturn\n\t\t\t}\n\t\t\tbytes[i] = byte\n\t\t}\n\t\treturn fromCharCode.apply(String, bytes)\n}\nfunction shortStringInJS(length) {\n\tif (length < 4) {\n\t\tif (length < 2) {\n\t\t\tif (length === 0)\n\t\t\t\treturn ''\n\t\t\telse {\n\t\t\t\tlet a = src[position++]\n\t\t\t\tif ((a & 0x80) > 1) {\n\t\t\t\t\tposition -= 1\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\treturn fromCharCode(a)\n\t\t\t}\n\t\t} else {\n\t\t\tlet a = src[position++]\n\t\t\tlet b = src[position++]\n\t\t\tif ((a & 0x80) > 0 || (b & 0x80) > 0) {\n\t\t\t\tposition -= 2\n\t\t\t\treturn\n\t\t\t}\n\t\t\tif (length < 3)\n\t\t\t\treturn fromCharCode(a, b)\n\t\t\tlet c = src[position++]\n\t\t\tif ((c & 0x80) > 0) {\n\t\t\t\tposition -= 3\n\t\t\t\treturn\n\t\t\t}\n\t\t\treturn fromCharCode(a, b, c)\n\t\t}\n\t} else {\n\t\tlet a = src[position++]\n\t\tlet b = src[position++]\n\t\tlet c = src[position++]\n\t\tlet d = src[position++]\n\t\tif ((a & 0x80) > 0 || (b & 0x80) > 0 || (c & 0x80) > 0 || (d & 0x80) > 0) {\n\t\t\tposition -= 4\n\t\t\treturn\n\t\t}\n\t\tif (length < 6) {\n\t\t\tif (length === 4)\n\t\t\t\treturn fromCharCode(a, b, c, d)\n\t\t\telse {\n\t\t\t\tlet e = src[position++]\n\t\t\t\tif ((e & 0x80) > 0) {\n\t\t\t\t\tposition -= 5\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\treturn fromCharCode(a, b, c, d, e)\n\t\t\t}\n\t\t} else if (length < 8) {\n\t\t\tlet e = src[position++]\n\t\t\tlet f = src[position++]\n\t\t\tif ((e & 0x80) > 0 || (f & 0x80) > 0) {\n\t\t\t\tposition -= 6\n\t\t\t\treturn\n\t\t\t}\n\t\t\tif (length < 7)\n\t\t\t\treturn fromCharCode(a, b, c, d, e, f)\n\t\t\tlet g = src[position++]\n\t\t\tif ((g & 0x80) > 0) {\n\t\t\t\tposition -= 7\n\t\t\t\treturn\n\t\t\t}\n\t\t\treturn fromCharCode(a, b, c, d, e, f, g)\n\t\t} else {\n\t\t\tlet e = src[position++]\n\t\t\tlet f = src[position++]\n\t\t\tlet g = src[position++]\n\t\t\tlet h = src[position++]\n\t\t\tif ((e & 0x80) > 0 || (f & 0x80) > 0 || (g & 0x80) > 0 || (h & 0x80) > 0) {\n\t\t\t\tposition -= 8\n\t\t\t\treturn\n\t\t\t}\n\t\t\tif (length < 10) {\n\t\t\t\tif (length === 8)\n\t\t\t\t\treturn fromCharCode(a, b, c, d, e, f, g, h)\n\t\t\t\telse {\n\t\t\t\t\tlet i = src[position++]\n\t\t\t\t\tif ((i & 0x80) > 0) {\n\t\t\t\t\t\tposition -= 9\n\t\t\t\t\t\treturn\n\t\t\t\t\t}\n\t\t\t\t\treturn fromCharCode(a, b, c, d, e, f, g, h, i)\n\t\t\t\t}\n\t\t\t} else if (length < 12) {\n\t\t\t\tlet i = src[position++]\n\t\t\t\tlet j = src[position++]\n\t\t\t\tif ((i & 0x80) > 0 || (j & 0x80) > 0) {\n\t\t\t\t\tposition -= 10\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tif (length < 11)\n\t\t\t\t\treturn fromCharCode(a, b, c, d, e, f, g, h, i, j)\n\t\t\t\tlet k = src[position++]\n\t\t\t\tif ((k & 0x80) > 0) {\n\t\t\t\t\tposition -= 11\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\treturn fromCharCode(a, b, c, d, e, f, g, h, i, j, k)\n\t\t\t} else {\n\t\t\t\tlet i = src[position++]\n\t\t\t\tlet j = src[position++]\n\t\t\t\tlet k = src[position++]\n\t\t\t\tlet l = src[position++]\n\t\t\t\tif ((i & 0x80) > 0 || (j & 0x80) > 0 || (k & 0x80) > 0 || (l & 0x80) > 0) {\n\t\t\t\t\tposition -= 12\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tif (length < 14) {\n\t\t\t\t\tif (length === 12)\n\t\t\t\t\t\treturn fromCharCode(a, b, c, d, e, f, g, h, i, j, k, l)\n\t\t\t\t\telse {\n\t\t\t\t\t\tlet m = src[position++]\n\t\t\t\t\t\tif ((m & 0x80) > 0) {\n\t\t\t\t\t\t\tposition -= 13\n\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn fromCharCode(a, b, c, d, e, f, g, h, i, j, k, l, m)\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tlet m = src[position++]\n\t\t\t\t\tlet n = src[position++]\n\t\t\t\t\tif ((m & 0x80) > 0 || (n & 0x80) > 0) {\n\t\t\t\t\t\tposition -= 14\n\t\t\t\t\t\treturn\n\t\t\t\t\t}\n\t\t\t\t\tif (length < 15)\n\t\t\t\t\t\treturn fromCharCode(a, b, c, d, e, f, g, h, i, j, k, l, m, n)\n\t\t\t\t\tlet o = src[position++]\n\t\t\t\t\tif ((o & 0x80) > 0) {\n\t\t\t\t\t\tposition -= 15\n\t\t\t\t\t\treturn\n\t\t\t\t\t}\n\t\t\t\t\treturn fromCharCode(a, b, c, d, e, f, g, h, i, j, k, l, m, n, o)\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\nfunction readOnlyJSString() {\n\tlet token = src[position++]\n\tlet length\n\tif (token < 0xc0) {\n\t\t// fixstr\n\t\tlength = token - 0xa0\n\t} else {\n\t\tswitch(token) {\n\t\t\tcase 0xd9:\n\t\t\t// str 8\n\t\t\t\tlength = src[position++]\n\t\t\t\tbreak\n\t\t\tcase 0xda:\n\t\t\t// str 16\n\t\t\t\tlength = dataView.getUint16(position)\n\t\t\t\tposition += 2\n\t\t\t\tbreak\n\t\t\tcase 0xdb:\n\t\t\t// str 32\n\t\t\t\tlength = dataView.getUint32(position)\n\t\t\t\tposition += 4\n\t\t\t\tbreak\n\t\t\tdefault:\n\t\t\t\tthrow new Error('Expected string')\n\t\t}\n\t}\n\treturn readStringJS(length)\n}\n\n\nfunction readBin(length) {\n\treturn currentUnpackr.copyBuffers ?\n\t\t// specifically use the copying slice (not the node one)\n\t\tUint8Array.prototype.slice.call(src, position, position += length) :\n\t\tsrc.subarray(position, position += length)\n}\nfunction readExt(length) {\n\tlet type = src[position++]\n\tif (currentExtensions[type]) {\n\t\tlet end\n\t\treturn currentExtensions[type](src.subarray(position, end = (position += length)), (readPosition) => {\n\t\t\tposition = readPosition;\n\t\t\ttry {\n\t\t\t\treturn read();\n\t\t\t} finally {\n\t\t\t\tposition = end;\n\t\t\t}\n\t\t})\n\t}\n\telse\n\t\tthrow new Error('Unknown extension type ' + type)\n}\n\nvar keyCache = new Array(4096)\nfunction readKey() {\n\tlet length = src[position++]\n\tif (length >= 0xa0 && length < 0xc0) {\n\t\t// fixstr, potentially use key cache\n\t\tlength = length - 0xa0\n\t\tif (srcStringEnd >= position) // if it has been extracted, must use it (and faster anyway)\n\t\t\treturn srcString.slice(position - srcStringStart, (position += length) - srcStringStart)\n\t\telse if (!(srcStringEnd == 0 && srcEnd < 180))\n\t\t\treturn readFixedString(length)\n\t} else { // not cacheable, go back and do a standard read\n\t\tposition--\n\t\treturn asSafeString(read())\n\t}\n\tlet key = ((length << 5) ^ (length > 1 ? dataView.getUint16(position) : length > 0 ? src[position] : 0)) & 0xfff\n\tlet entry = keyCache[key]\n\tlet checkPosition = position\n\tlet end = position + length - 3\n\tlet chunk\n\tlet i = 0\n\tif (entry && entry.bytes == length) {\n\t\twhile (checkPosition < end) {\n\t\t\tchunk = dataView.getUint32(checkPosition)\n\t\t\tif (chunk != entry[i++]) {\n\t\t\t\tcheckPosition = 0x70000000\n\t\t\t\tbreak\n\t\t\t}\n\t\t\tcheckPosition += 4\n\t\t}\n\t\tend += 3\n\t\twhile (checkPosition < end) {\n\t\t\tchunk = src[checkPosition++]\n\t\t\tif (chunk != entry[i++]) {\n\t\t\t\tcheckPosition = 0x70000000\n\t\t\t\tbreak\n\t\t\t}\n\t\t}\n\t\tif (checkPosition === end) {\n\t\t\tposition = checkPosition\n\t\t\treturn entry.string\n\t\t}\n\t\tend -= 3\n\t\tcheckPosition = position\n\t}\n\tentry = []\n\tkeyCache[key] = entry\n\tentry.bytes = length\n\twhile (checkPosition < end) {\n\t\tchunk = dataView.getUint32(checkPosition)\n\t\tentry.push(chunk)\n\t\tcheckPosition += 4\n\t}\n\tend += 3\n\twhile (checkPosition < end) {\n\t\tchunk = src[checkPosition++]\n\t\tentry.push(chunk)\n\t}\n\t// for small blocks, avoiding the overhead of the extract call is helpful\n\tlet string = length < 16 ? shortStringInJS(length) : longStringInJS(length)\n\tif (string != null)\n\t\treturn entry.string = string\n\treturn entry.string = readFixedString(length)\n}\n\nfunction asSafeString(property) {\n\t// protect against expensive (DoS) string conversions\n\tif (typeof property === 'string') return property;\n\tif (typeof property === 'number' || typeof property === 'boolean' || typeof property === 'bigint') return property.toString();\n\tif (property == null) return property + '';\n\tif (currentUnpackr.allowArraysInMapKeys && Array.isArray(property) && property.flat().every(item => ['string', 'number', 'boolean', 'bigint'].includes(typeof item))) {\n\t\treturn property.flat().toString();\n\t}\n\tthrow new Error(`Invalid property type for record: ${typeof property}`);\n}\n// the registration of the record definition extension (as \"r\")\nconst recordDefinition = (id, highByte) => {\n\tlet structure = read().map(asSafeString) // ensure that all keys are strings and\n\t// that the array is mutable\n\tlet firstByte = id\n\tif (highByte !== undefined) {\n\t\tid = id < 32 ? -((highByte << 5) + id) : ((highByte << 5) + id)\n\t\tstructure.highByte = highByte\n\t}\n\tlet existingStructure = currentStructures[id]\n\t// If it is a shared structure, we need to restore any changes after reading.\n\t// Also in sequential mode, we may get incomplete reads and thus errors, and we need to restore\n\t// to the state prior to an incomplete read in order to properly resume.\n\tif (existingStructure && (existingStructure.isShared || sequentialMode)) {\n\t\t(currentStructures.restoreStructures || (currentStructures.restoreStructures = []))[id] = existingStructure\n\t}\n\tcurrentStructures[id] = structure\n\tstructure.read = createStructureReader(structure, firstByte)\n\treturn structure.read()\n}\ncurrentExtensions[0] = () => {} // notepack defines extension 0 to mean undefined, so use that as the default here\ncurrentExtensions[0].noBuffer = true\n\ncurrentExtensions[0x42] = (data) => {\n\t// decode bigint\n\tlet length = data.length;\n\tlet value = BigInt(data[0] & 0x80 ? data[0] - 0x100 : data[0]);\n\tfor (let i = 1; i < length; i++) {\n\t\tvalue <<= BigInt(8);\n\t\tvalue += BigInt(data[i]);\n\t}\n\treturn value;\n}\n\nlet errors = { Error, TypeError, ReferenceError };\ncurrentExtensions[0x65] = () => {\n\tlet data = read()\n\treturn (errors[data[0]] || Error)(data[1], { cause: data[2] })\n}\n\ncurrentExtensions[0x69] = (data) => {\n\t// id extension (for structured clones)\n\tif (currentUnpackr.structuredClone === false) throw new Error('Structured clone extension is disabled')\n\tlet id = dataView.getUint32(position - 4)\n\tif (!referenceMap)\n\t\treferenceMap = new Map()\n\tlet token = src[position]\n\tlet target\n\t// TODO: handle any other types that can cycle and make the code more robust if there are other extensions\n\tif (token >= 0x90 && token < 0xa0 || token == 0xdc || token == 0xdd)\n\t\ttarget = []\n\telse if (token >= 0x80 && token < 0x90 || token == 0xde || token == 0xdf)\n\t\ttarget = new Map()\n\telse if ((token >= 0xc7 && token <= 0xc9 || token >= 0xd4 && token <= 0xd8) && src[position + 1] === 0x73)\n\t\ttarget = new Set()\n\telse\n\t\ttarget = {}\n\n\tlet refEntry = { target } // a placeholder object\n\treferenceMap.set(id, refEntry)\n\tlet targetProperties = read() // read the next value as the target object to id\n\tif (!refEntry.used) {\n\t\t// no cycle, can just use the returned read object\n\t\treturn refEntry.target = targetProperties // replace the placeholder with the real one\n\t} else {\n\t\t// there is a cycle, so we have to assign properties to original target\n\t\tObject.assign(target, targetProperties)\n\t}\n\n\t// copy over map/set entries if we're able to\n\tif (target instanceof Map)\n\t\tfor (let [k, v] of targetProperties.entries()) target.set(k, v)\n\tif (target instanceof Set)\n\t\tfor (let i of Array.from(targetProperties)) target.add(i)\n\treturn target\n}\n\ncurrentExtensions[0x70] = (data) => {\n\t// pointer extension (for structured clones)\n\tif (currentUnpackr.structuredClone === false) throw new Error('Structured clone extension is disabled')\n\tlet id = dataView.getUint32(position - 4)\n\tlet refEntry = referenceMap.get(id)\n\trefEntry.used = true\n\treturn refEntry.target\n}\n\ncurrentExtensions[0x73] = () => new Set(read())\n\nexport const typedArrays = ['Int8','Uint8','Uint8Clamped','Int16','Uint16','Int32','Uint32','Float32','Float64','BigInt64','BigUint64'].map(type => type + 'Array')\n\nlet glbl = typeof globalThis === 'object' ? globalThis : window;\ncurrentExtensions[0x74] = (data) => {\n\tlet typeCode = data[0]\n\t// we always have to slice to get a new ArrayBuffer that is aligned\n\tlet buffer = Uint8Array.prototype.slice.call(data, 1).buffer\n\n\tlet typedArrayName = typedArrays[typeCode]\n\tif (!typedArrayName) {\n\t\tif (typeCode === 16) return buffer\n\t\tif (typeCode === 17) return new DataView(buffer)\n\t\tthrow new Error('Could not find typed array for code ' + typeCode)\n\t}\n\treturn new glbl[typedArrayName](buffer)\n}\ncurrentExtensions[0x78] = () => {\n\tlet data = read()\n\treturn new RegExp(data[0], data[1])\n}\nconst TEMP_BUNDLE = []\ncurrentExtensions[0x62] = (data) => {\n\tlet dataSize = (data[0] << 24) + (data[1] << 16) + (data[2] << 8) + data[3]\n\tlet dataPosition = position\n\tposition += dataSize - data.length\n\tbundledStrings = TEMP_BUNDLE\n\tbundledStrings = [readOnlyJSString(), readOnlyJSString()]\n\tbundledStrings.position0 = 0\n\tbundledStrings.position1 = 0\n\tbundledStrings.postBundlePosition = position\n\tposition = dataPosition\n\treturn read()\n}\n\ncurrentExtensions[0xff] = (data) => {\n\t// 32-bit date extension\n\tif (data.length == 4)\n\t\treturn new Date((data[0] * 0x1000000 + (data[1] << 16) + (data[2] << 8) + data[3]) * 1000)\n\telse if (data.length == 8)\n\t\treturn new Date(\n\t\t\t((data[0] << 22) + (data[1] << 14) + (data[2] << 6) + (data[3] >> 2)) / 1000000 +\n\t\t\t((data[3] & 0x3) * 0x100000000 + data[4] * 0x1000000 + (data[5] << 16) + (data[6] << 8) + data[7]) * 1000)\n\telse if (data.length == 12)\n\t\treturn new Date(\n\t\t\t((data[0] << 24) + (data[1] << 16) + (data[2] << 8) + data[3]) / 1000000 +\n\t\t\t(((data[4] & 0x80) ? -0x1000000000000 : 0) + data[6] * 0x10000000000 + data[7] * 0x100000000 + data[8] * 0x1000000 + (data[9] << 16) + (data[10] << 8) + data[11]) * 1000)\n\telse\n\t\treturn new Date('invalid')\n}\n// registration of bulk record definition?\n// currentExtensions[0x52] = () =>\n\nfunction saveState(callback) {\n\tif (onSaveState)\n\t\tonSaveState();\n\tlet savedSrcEnd = srcEnd\n\tlet savedPosition = position\n\tlet savedStringPosition = stringPosition\n\tlet savedSrcStringStart = srcStringStart\n\tlet savedSrcStringEnd = srcStringEnd\n\tlet savedSrcString = srcString\n\tlet savedStrings = strings\n\tlet savedReferenceMap = referenceMap\n\tlet savedBundledStrings = bundledStrings\n\n\t// TODO: We may need to revisit this if we do more external calls to user code (since it could be slow)\n\tlet savedSrc = new Uint8Array(src.slice(0, srcEnd)) // we copy the data in case it changes while external data is processed\n\tlet savedStructures = currentStructures\n\tlet savedStructuresContents = currentStructures.slice(0, currentStructures.length)\n\tlet savedPackr = currentUnpackr\n\tlet savedSequentialMode = sequentialMode\n\tlet value = callback()\n\tsrcEnd = savedSrcEnd\n\tposition = savedPosition\n\tstringPosition = savedStringPosition\n\tsrcStringStart = savedSrcStringStart\n\tsrcStringEnd = savedSrcStringEnd\n\tsrcString = savedSrcString\n\tstrings = savedStrings\n\treferenceMap = savedReferenceMap\n\tbundledStrings = savedBundledStrings\n\tsrc = savedSrc\n\tsequentialMode = savedSequentialMode\n\tcurrentStructures = savedStructures\n\tcurrentStructures.splice(0, currentStructures.length, ...savedStructuresContents)\n\tcurrentUnpackr = savedPackr\n\tdataView = new DataView(src.buffer, src.byteOffset, src.byteLength)\n\treturn value\n}\nexport function clearSource() {\n\tsrc = null\n\treferenceMap = null\n\tcurrentStructures = null\n}\n\nexport function addExtension(extension) {\n\tif (extension.unpack)\n\t\tcurrentExtensions[extension.type] = extension.unpack\n\telse\n\t\tcurrentExtensions[extension.type] = extension\n}\n\nexport const mult10 = new Array(147) // this is a table matching binary exponents to the multiplier to determine significant digit rounding\nfor (let i = 0; i < 256; i++) {\n\tmult10[i] = +('1e' + Math.floor(45.15 - i * 0.30103))\n}\nexport const Decoder = Unpackr\nvar defaultUnpackr = new Unpackr({ useRecords: false })\nexport const unpack = defaultUnpackr.unpack\nexport const unpackMultiple = defaultUnpackr.unpackMultiple\nexport const decode = defaultUnpackr.unpack\nexport const FLOAT32_OPTIONS = {\n\tNEVER: 0,\n\tALWAYS: 1,\n\tDECIMAL_ROUND: 3,\n\tDECIMAL_FIT: 4\n}\nlet f32Array = new Float32Array(1)\nlet u8Array = new Uint8Array(f32Array.buffer, 0, 4)\nexport function roundFloat32(float32Number) {\n\tf32Array[0] = float32Number\n\tlet multiplier = mult10[((u8Array[3] & 0x7f) << 1) | (u8Array[2] >> 7)]\n\treturn ((multiplier * float32Number + (float32Number > 0 ? 0.5 : -0.5)) >> 0) / multiplier\n}\nexport function setReadStruct(updatedReadStruct, loadedStructs, saveState) {\n\treadStruct = updatedReadStruct;\n\tonLoadedStructures = loadedStructs;\n\tonSaveState = saveState;\n}\n", "import { Unpackr, mult10, C1Type, typedArrays, addExtension as unpackAddExtension } from './unpack.js'\nlet textEncoder\ntry {\n\ttextEncoder = new TextEncoder()\n} catch (error) {}\nlet extensions, extensionClasses\nconst hasNodeBuffer = typeof Buffer !== 'undefined'\nconst ByteArrayAllocate = hasNodeBuffer ?\n\tfunction(length) { return Buffer.allocUnsafeSlow(length) } : Uint8Array\nconst ByteArray = hasNodeBuffer ? Buffer : Uint8Array\nconst MAX_BUFFER_SIZE = hasNodeBuffer ? 0x100000000 : 0x7fd00000\nlet target, keysTarget\nlet targetView\nlet position = 0\nlet safeEnd\nlet bundledStrings = null\nlet writeStructSlots\nconst MAX_BUNDLE_SIZE = 0x5500 // maximum characters such that the encoded bytes fits in 16 bits.\nconst hasNonLatin = /[\\u0080-\\uFFFF]/\nexport const RECORD_SYMBOL = Symbol('record-id')\nexport class Packr extends Unpackr {\n\tconstructor(options) {\n\t\tsuper(options)\n\t\tthis.offset = 0\n\t\tlet typeBuffer\n\t\tlet start\n\t\tlet hasSharedUpdate\n\t\tlet structures\n\t\tlet referenceMap\n\t\tlet encodeUtf8 = ByteArray.prototype.utf8Write ? function(string, position) {\n\t\t\treturn target.utf8Write(string, position, target.byteLength - position)\n\t\t} : (textEncoder && textEncoder.encodeInto) ?\n\t\t\tfunction(string, position) {\n\t\t\t\treturn textEncoder.encodeInto(string, target.subarray(position)).written\n\t\t\t} : false\n\n\t\tlet packr = this\n\t\tif (!options)\n\t\t\toptions = {}\n\t\tlet isSequential = options && options.sequential\n\t\tlet hasSharedStructures = options.structures || options.saveStructures\n\t\tlet maxSharedStructures = options.maxSharedStructures\n\t\tif (maxSharedStructures == null)\n\t\t\tmaxSharedStructures = hasSharedStructures ? 32 : 0\n\t\tif (maxSharedStructures > 8160)\n\t\t\tthrow new Error('Maximum maxSharedStructure is 8160')\n\t\tif (options.structuredClone && options.moreTypes == undefined) {\n\t\t\tthis.moreTypes = true\n\t\t}\n\t\tlet maxOwnStructures = options.maxOwnStructures\n\t\tif (maxOwnStructures == null)\n\t\t\tmaxOwnStructures = hasSharedStructures ? 32 : 64\n\t\tif (!this.structures && options.useRecords != false)\n\t\t\tthis.structures = []\n\t\t// two byte record ids for shared structures\n\t\tlet useTwoByteRecords = maxSharedStructures > 32 || (maxOwnStructures + maxSharedStructures > 64)\n\t\tlet sharedLimitId = maxSharedStructures + 0x40\n\t\tlet maxStructureId = maxSharedStructures + maxOwnStructures + 0x40\n\t\tif (maxStructureId > 8256) {\n\t\t\tthrow new Error('Maximum maxSharedStructure + maxOwnStructure is 8192')\n\t\t}\n\t\tlet recordIdsToRemove = []\n\t\tlet transitionsCount = 0\n\t\tlet serializationsSinceTransitionRebuild = 0\n\n\t\tthis.pack = this.encode = function(value, encodeOptions) {\n\t\t\tif (!target) {\n\t\t\t\ttarget = new ByteArrayAllocate(8192)\n\t\t\t\ttargetView = target.dataView || (target.dataView = new DataView(target.buffer, 0, 8192))\n\t\t\t\tposition = 0\n\t\t\t}\n\t\t\tsafeEnd = target.length - 10\n\t\t\tif (safeEnd - position < 0x800) {\n\t\t\t\t// don't start too close to the end,\n\t\t\t\ttarget = new ByteArrayAllocate(target.length)\n\t\t\t\ttargetView = target.dataView || (target.dataView = new DataView(target.buffer, 0, target.length))\n\t\t\t\tsafeEnd = target.length - 10\n\t\t\t\tposition = 0\n\t\t\t} else\n\t\t\t\tposition = (position + 7) & 0x7ffffff8 // Word align to make any future copying of this buffer faster\n\t\t\tstart = position\n\t\t\tif (encodeOptions & RESERVE_START_SPACE) position += (encodeOptions & 0xff)\n\t\t\treferenceMap = packr.structuredClone ? new Map() : null\n\t\t\tif (packr.bundleStrings && typeof value !== 'string') {\n\t\t\t\tbundledStrings = []\n\t\t\t\tbundledStrings.size = Infinity // force a new bundle start on first string\n\t\t\t} else\n\t\t\t\tbundledStrings = null\n\t\t\tstructures = packr.structures\n\t\t\tif (structures) {\n\t\t\t\tif (structures.uninitialized)\n\t\t\t\t\tstructures = packr._mergeStructures(packr.getStructures())\n\t\t\t\tlet sharedLength = structures.sharedLength || 0\n\t\t\t\tif (sharedLength > maxSharedStructures) {\n\t\t\t\t\t//if (maxSharedStructures <= 32 && structures.sharedLength > 32) // TODO: could support this, but would need to update the limit ids\n\t\t\t\t\tthrow new Error('Shared structures is larger than maximum shared structures, try increasing maxSharedStructures to ' + structures.sharedLength)\n\t\t\t\t}\n\t\t\t\tif (!structures.transitions) {\n\t\t\t\t\t// rebuild our structure transitions\n\t\t\t\t\tstructures.transitions = Object.create(null)\n\t\t\t\t\tfor (let i = 0; i < sharedLength; i++) {\n\t\t\t\t\t\tlet keys = structures[i]\n\t\t\t\t\t\tif (!keys)\n\t\t\t\t\t\t\tcontinue\n\t\t\t\t\t\tlet nextTransition, transition = structures.transitions\n\t\t\t\t\t\tfor (let j = 0, l = keys.length; j < l; j++) {\n\t\t\t\t\t\t\tlet key = keys[j]\n\t\t\t\t\t\t\tnextTransition = transition[key]\n\t\t\t\t\t\t\tif (!nextTransition) {\n\t\t\t\t\t\t\t\tnextTransition = transition[key] = Object.create(null)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\ttransition = nextTransition\n\t\t\t\t\t\t}\n\t\t\t\t\t\ttransition[RECORD_SYMBOL] = i + 0x40\n\t\t\t\t\t}\n\t\t\t\t\tthis.lastNamedStructuresLength = sharedLength\n\t\t\t\t}\n\t\t\t\tif (!isSequential) {\n\t\t\t\t\tstructures.nextId = sharedLength + 0x40\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (hasSharedUpdate)\n\t\t\t\thasSharedUpdate = false\n\t\t\tlet encodingError;\n\t\t\ttry {\n\t\t\t\tif (packr.randomAccessStructure && value && value.constructor && value.constructor === Object)\n\t\t\t\t\twriteStruct(value);\n\t\t\t\telse\n\t\t\t\t\tpack(value)\n\t\t\t\tlet lastBundle = bundledStrings;\n\t\t\t\tif (bundledStrings)\n\t\t\t\t\twriteBundles(start, pack, 0)\n\t\t\t\tif (referenceMap && referenceMap.idsToInsert) {\n\t\t\t\t\tlet idsToInsert = referenceMap.idsToInsert.sort((a, b) => a.offset > b.offset ? 1 : -1);\n\t\t\t\t\tlet i = idsToInsert.length;\n\t\t\t\t\tlet incrementPosition = -1;\n\t\t\t\t\twhile (lastBundle && i > 0) {\n\t\t\t\t\t\tlet insertionPoint = idsToInsert[--i].offset + start;\n\t\t\t\t\t\tif (insertionPoint < (lastBundle.stringsPosition + start) && incrementPosition === -1)\n\t\t\t\t\t\t\tincrementPosition = 0;\n\t\t\t\t\t\tif (insertionPoint > (lastBundle.position + start)) {\n\t\t\t\t\t\t\tif (incrementPosition >= 0)\n\t\t\t\t\t\t\t\tincrementPosition += 6;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tif (incrementPosition >= 0) {\n\t\t\t\t\t\t\t\t// update the bundle reference now\n\t\t\t\t\t\t\t\ttargetView.setUint32(lastBundle.position + start,\n\t\t\t\t\t\t\t\t\ttargetView.getUint32(lastBundle.position + start) + incrementPosition)\n\t\t\t\t\t\t\t\tincrementPosition = -1; // reset\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tlastBundle = lastBundle.previous;\n\t\t\t\t\t\t\ti++;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif (incrementPosition >= 0 && lastBundle) {\n\t\t\t\t\t\t// update the bundle reference now\n\t\t\t\t\t\ttargetView.setUint32(lastBundle.position + start,\n\t\t\t\t\t\t\ttargetView.getUint32(lastBundle.position + start) + incrementPosition)\n\t\t\t\t\t}\n\t\t\t\t\tposition += idsToInsert.length * 6;\n\t\t\t\t\tif (position > safeEnd)\n\t\t\t\t\t\tmakeRoom(position)\n\t\t\t\t\tpackr.offset = position\n\t\t\t\t\tlet serialized = insertIds(target.subarray(start, position), idsToInsert)\n\t\t\t\t\treferenceMap = null\n\t\t\t\t\treturn serialized\n\t\t\t\t}\n\t\t\t\tpackr.offset = position // update the offset so next serialization doesn't write over our buffer, but can continue writing to same buffer sequentially\n\t\t\t\tif (encodeOptions & REUSE_BUFFER_MODE) {\n\t\t\t\t\ttarget.start = start\n\t\t\t\t\ttarget.end = position\n\t\t\t\t\treturn target\n\t\t\t\t}\n\t\t\t\treturn target.subarray(start, position) // position can change if we call pack again in saveStructures, so we get the buffer now\n\t\t\t} catch(error) {\n\t\t\t\tencodingError = error;\n\t\t\t\tthrow error;\n\t\t\t} finally {\n\t\t\t\tif (structures) {\n\t\t\t\t\tresetStructures();\n\t\t\t\t\tif (hasSharedUpdate && packr.saveStructures) {\n\t\t\t\t\t\tlet sharedLength = structures.sharedLength || 0\n\t\t\t\t\t\t// we can't rely on start/end with REUSE_BUFFER_MODE since they will (probably) change when we save\n\t\t\t\t\t\tlet returnBuffer = target.subarray(start, position)\n\t\t\t\t\t\tlet newSharedData = prepareStructures(structures, packr);\n\t\t\t\t\t\tif (!encodingError) { // TODO: If there is an encoding error, should make the structures as uninitialized so they get rebuilt next time\n\t\t\t\t\t\t\tif (packr.saveStructures(newSharedData, newSharedData.isCompatible) === false) {\n\t\t\t\t\t\t\t\t// get updated structures and try again if the update failed\n\t\t\t\t\t\t\t\treturn packr.pack(value, encodeOptions)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tpackr.lastNamedStructuresLength = sharedLength\n\t\t\t\t\t\t\t// don't keep large buffers around\n\t\t\t\t\t\t\tif (target.length > 0x40000000) target = null\n\t\t\t\t\t\t\treturn returnBuffer\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t// don't keep large buffers around, they take too much memory and cause problems (limit at 1GB)\n\t\t\t\tif (target.length > 0x40000000) target = null\n\t\t\t\tif (encodeOptions & RESET_BUFFER_MODE)\n\t\t\t\t\tposition = start\n\t\t\t}\n\t\t}\n\t\tconst resetStructures = () => {\n\t\t\tif (serializationsSinceTransitionRebuild < 10)\n\t\t\t\tserializationsSinceTransitionRebuild++\n\t\t\tlet sharedLength = structures.sharedLength || 0\n\t\t\tif (structures.length > sharedLength && !isSequential)\n\t\t\t\tstructures.length = sharedLength\n\t\t\tif (transitionsCount > 10000) {\n\t\t\t\t// force a rebuild occasionally after a lot of transitions so it can get cleaned up\n\t\t\t\tstructures.transitions = null\n\t\t\t\tserializationsSinceTransitionRebuild = 0\n\t\t\t\ttransitionsCount = 0\n\t\t\t\tif (recordIdsToRemove.length > 0)\n\t\t\t\t\trecordIdsToRemove = []\n\t\t\t} else if (recordIdsToRemove.length > 0 && !isSequential) {\n\t\t\t\tfor (let i = 0, l = recordIdsToRemove.length; i < l; i++) {\n\t\t\t\t\trecordIdsToRemove[i][RECORD_SYMBOL] = 0\n\t\t\t\t}\n\t\t\t\trecordIdsToRemove = []\n\t\t\t}\n\t\t}\n\t\tconst packArray = (value) => {\n\t\t\tvar length = value.length\n\t\t\tif (length < 0x10) {\n\t\t\t\ttarget[position++] = 0x90 | length\n\t\t\t} else if (length < 0x10000) {\n\t\t\t\ttarget[position++] = 0xdc\n\t\t\t\ttarget[position++] = length >> 8\n\t\t\t\ttarget[position++] = length & 0xff\n\t\t\t} else {\n\t\t\t\ttarget[position++] = 0xdd\n\t\t\t\ttargetView.setUint32(position, length)\n\t\t\t\tposition += 4\n\t\t\t}\n\t\t\tfor (let i = 0; i < length; i++) {\n\t\t\t\tpack(value[i])\n\t\t\t}\n\t\t}\n\t\tconst pack = (value) => {\n\t\t\tif (position > safeEnd)\n\t\t\t\ttarget = makeRoom(position)\n\n\t\t\tvar type = typeof value\n\t\t\tvar length\n\t\t\tif (type === 'string') {\n\t\t\t\tlet strLength = value.length\n\t\t\t\tif (bundledStrings && strLength >= 4 && strLength < 0x1000) {\n\t\t\t\t\tif ((bundledStrings.size += strLength) > MAX_BUNDLE_SIZE) {\n\t\t\t\t\t\tlet extStart\n\t\t\t\t\t\tlet maxBytes = (bundledStrings[0] ? bundledStrings[0].length * 3 + bundledStrings[1].length : 0) + 10\n\t\t\t\t\t\tif (position + maxBytes > safeEnd)\n\t\t\t\t\t\t\ttarget = makeRoom(position + maxBytes)\n\t\t\t\t\t\tlet lastBundle\n\t\t\t\t\t\tif (bundledStrings.position) { // here we use the 0x62 extension to write the last bundle and reserve space for the reference pointer to the next/current bundle\n\t\t\t\t\t\t\tlastBundle = bundledStrings\n\t\t\t\t\t\t\ttarget[position] = 0xc8 // ext 16\n\t\t\t\t\t\t\tposition += 3 // reserve for the writing bundle size\n\t\t\t\t\t\t\ttarget[position++] = 0x62 // 'b'\n\t\t\t\t\t\t\textStart = position - start\n\t\t\t\t\t\t\tposition += 4 // reserve for writing bundle reference\n\t\t\t\t\t\t\twriteBundles(start, pack, 0) // write the last bundles\n\t\t\t\t\t\t\ttargetView.setUint16(extStart + start - 3, position - start - extStart)\n\t\t\t\t\t\t} else { // here we use the 0x62 extension just to reserve the space for the reference pointer to the bundle (will be updated once the bundle is written)\n\t\t\t\t\t\t\ttarget[position++] = 0xd6 // fixext 4\n\t\t\t\t\t\t\ttarget[position++] = 0x62 // 'b'\n\t\t\t\t\t\t\textStart = position - start\n\t\t\t\t\t\t\tposition += 4 // reserve for writing bundle reference\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbundledStrings = ['', ''] // create new ones\n\t\t\t\t\t\tbundledStrings.previous = lastBundle;\n\t\t\t\t\t\tbundledStrings.size = 0\n\t\t\t\t\t\tbundledStrings.position = extStart\n\t\t\t\t\t}\n\t\t\t\t\tlet twoByte = hasNonLatin.test(value)\n\t\t\t\t\tbundledStrings[twoByte ? 0 : 1] += value\n\t\t\t\t\ttarget[position++] = 0xc1\n\t\t\t\t\tpack(twoByte ? -strLength : strLength);\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tlet headerSize\n\t\t\t\t// first we estimate the header size, so we can write to the correct location\n\t\t\t\tif (strLength < 0x20) {\n\t\t\t\t\theaderSize = 1\n\t\t\t\t} else if (strLength < 0x100) {\n\t\t\t\t\theaderSize = 2\n\t\t\t\t} else if (strLength < 0x10000) {\n\t\t\t\t\theaderSize = 3\n\t\t\t\t} else {\n\t\t\t\t\theaderSize = 5\n\t\t\t\t}\n\t\t\t\tlet maxBytes = strLength * 3\n\t\t\t\tif (position + maxBytes > safeEnd)\n\t\t\t\t\ttarget = makeRoom(position + maxBytes)\n\n\t\t\t\tif (strLength < 0x40 || !encodeUtf8) {\n\t\t\t\t\tlet i, c1, c2, strPosition = position + headerSize\n\t\t\t\t\tfor (i = 0; i < strLength; i++) {\n\t\t\t\t\t\tc1 = value.charCodeAt(i)\n\t\t\t\t\t\tif (c1 < 0x80) {\n\t\t\t\t\t\t\ttarget[strPosition++] = c1\n\t\t\t\t\t\t} else if (c1 < 0x800) {\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 >> 6 | 0xc0\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 & 0x3f | 0x80\n\t\t\t\t\t\t} else if (\n\t\t\t\t\t\t\t(c1 & 0xfc00) === 0xd800 &&\n\t\t\t\t\t\t\t((c2 = value.charCodeAt(i + 1)) & 0xfc00) === 0xdc00\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tc1 = 0x10000 + ((c1 & 0x03ff) << 10) + (c2 & 0x03ff)\n\t\t\t\t\t\t\ti++\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 >> 18 | 0xf0\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 >> 12 & 0x3f | 0x80\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 >> 6 & 0x3f | 0x80\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 & 0x3f | 0x80\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 >> 12 | 0xe0\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 >> 6 & 0x3f | 0x80\n\t\t\t\t\t\t\ttarget[strPosition++] = c1 & 0x3f | 0x80\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tlength = strPosition - position - headerSize\n\t\t\t\t} else {\n\t\t\t\t\tlength = encodeUtf8(value, position + headerSize)\n\t\t\t\t}\n\n\t\t\t\tif (length < 0x20) {\n\t\t\t\t\ttarget[position++] = 0xa0 | length\n\t\t\t\t} else if (length < 0x100) {\n\t\t\t\t\tif (headerSize < 2) {\n\t\t\t\t\t\ttarget.copyWithin(position + 2, position + 1, position + 1 + length)\n\t\t\t\t\t}\n\t\t\t\t\ttarget[position++] = 0xd9\n\t\t\t\t\ttarget[position++] = length\n\t\t\t\t} else if (length < 0x10000) {\n\t\t\t\t\tif (headerSize < 3) {\n\t\t\t\t\t\ttarget.copyWithin(position + 3, position + 2, position + 2 + length)\n\t\t\t\t\t}\n\t\t\t\t\ttarget[position++] = 0xda\n\t\t\t\t\ttarget[position++] = length >> 8\n\t\t\t\t\ttarget[position++] = length & 0xff\n\t\t\t\t} else {\n\t\t\t\t\tif (headerSize < 5) {\n\t\t\t\t\t\ttarget.copyWithin(position + 5, position + 3, position + 3 + length)\n\t\t\t\t\t}\n\t\t\t\t\ttarget[position++] = 0xdb\n\t\t\t\t\ttargetView.setUint32(position, length)\n\t\t\t\t\tposition += 4\n\t\t\t\t}\n\t\t\t\tposition += length\n\t\t\t} else if (type === 'number') {\n\t\t\t\tif (value >>> 0 === value) {// positive integer, 32-bit or less\n\t\t\t\t\t// positive uint\n\t\t\t\t\tif (value < 0x20 || (value < 0x80 && this.useRecords === false) || (value < 0x40 && !this.randomAccessStructure)) {\n\t\t\t\t\t\ttarget[position++] = value\n\t\t\t\t\t} else if (value < 0x100) {\n\t\t\t\t\t\ttarget[position++] = 0xcc\n\t\t\t\t\t\ttarget[position++] = value\n\t\t\t\t\t} else if (value < 0x10000) {\n\t\t\t\t\t\ttarget[position++] = 0xcd\n\t\t\t\t\t\ttarget[position++] = value >> 8\n\t\t\t\t\t\ttarget[position++] = value & 0xff\n\t\t\t\t\t} else {\n\t\t\t\t\t\ttarget[position++] = 0xce\n\t\t\t\t\t\ttargetView.setUint32(position, value)\n\t\t\t\t\t\tposition += 4\n\t\t\t\t\t}\n\t\t\t\t} else if (value >> 0 === value) { // negative integer\n\t\t\t\t\tif (value >= -0x20) {\n\t\t\t\t\t\ttarget[position++] = 0x100 + value\n\t\t\t\t\t} else if (value >= -0x80) {\n\t\t\t\t\t\ttarget[position++] = 0xd0\n\t\t\t\t\t\ttarget[position++] = value + 0x100\n\t\t\t\t\t} else if (value >= -0x8000) {\n\t\t\t\t\t\ttarget[position++] = 0xd1\n\t\t\t\t\t\ttargetView.setInt16(position, value)\n\t\t\t\t\t\tposition += 2\n\t\t\t\t\t} else {\n\t\t\t\t\t\ttarget[position++] = 0xd2\n\t\t\t\t\t\ttargetView.setInt32(position, value)\n\t\t\t\t\t\tposition += 4\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tlet useFloat32\n\t\t\t\t\tif ((useFloat32 = this.useFloat32) > 0 && value < 0x100000000 && value >= -0x80000000) {\n\t\t\t\t\t\ttarget[position++] = 0xca\n\t\t\t\t\t\ttargetView.setFloat32(position, value)\n\t\t\t\t\t\tlet xShifted\n\t\t\t\t\t\tif (useFloat32 < 4 ||\n\t\t\t\t\t\t\t\t// this checks for rounding of numbers that were encoded in 32-bit float to nearest significant decimal digit that could be preserved\n\t\t\t\t\t\t\t\t((xShifted = value * mult10[((target[position] & 0x7f) << 1) | (target[position + 1] >> 7)]) >> 0) === xShifted) {\n\t\t\t\t\t\t\tposition += 4\n\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t} else\n\t\t\t\t\t\t\tposition-- // move back into position for writing a double\n\t\t\t\t\t}\n\t\t\t\t\ttarget[position++] = 0xcb\n\t\t\t\t\ttargetView.setFloat64(position, value)\n\t\t\t\t\tposition += 8\n\t\t\t\t}\n\t\t\t} else if (type === 'object' || type === 'function') {\n\t\t\t\tif (!value)\n\t\t\t\t\ttarget[position++] = 0xc0\n\t\t\t\telse {\n\t\t\t\t\tif (referenceMap) {\n\t\t\t\t\t\tlet referee = referenceMap.get(value)\n\t\t\t\t\t\tif (referee) {\n\t\t\t\t\t\t\tif (!referee.id) {\n\t\t\t\t\t\t\t\tlet idsToInsert = referenceMap.idsToInsert || (referenceMap.idsToInsert = [])\n\t\t\t\t\t\t\t\treferee.id = idsToInsert.push(referee)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\ttarget[position++] = 0xd6 // fixext 4\n\t\t\t\t\t\t\ttarget[position++] = 0x70 // \"p\" for pointer\n\t\t\t\t\t\t\ttargetView.setUint32(position, referee.id)\n\t\t\t\t\t\t\tposition += 4\n\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t} else\n\t\t\t\t\t\t\treferenceMap.set(value, { offset: position - start })\n\t\t\t\t\t}\n\t\t\t\t\tlet constructor = value.constructor\n\t\t\t\t\tif (constructor === Object) {\n\t\t\t\t\t\twriteObject(value)\n\t\t\t\t\t} else if (constructor === Array) {\n\t\t\t\t\t\tpackArray(value)\n\t\t\t\t\t} else if (constructor === Map) {\n\t\t\t\t\t\tif (this.mapAsEmptyObject) target[position++] = 0x80\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\tlength = value.size\n\t\t\t\t\t\t\tif (length < 0x10) {\n\t\t\t\t\t\t\t\ttarget[position++] = 0x80 | length\n\t\t\t\t\t\t\t} else if (length < 0x10000) {\n\t\t\t\t\t\t\t\ttarget[position++] = 0xde\n\t\t\t\t\t\t\t\ttarget[position++] = length >> 8\n\t\t\t\t\t\t\t\ttarget[position++] = length & 0xff\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\ttarget[position++] = 0xdf\n\t\t\t\t\t\t\t\ttargetView.setUint32(position, length)\n\t\t\t\t\t\t\t\tposition += 4\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tfor (let [key, entryValue] of value) {\n\t\t\t\t\t\t\t\tpack(key)\n\t\t\t\t\t\t\t\tpack(entryValue)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tfor (let i = 0, l = extensions.length; i < l; i++) {\n\t\t\t\t\t\t\tlet extensionClass = extensionClasses[i]\n\t\t\t\t\t\t\tif (value instanceof extensionClass) {\n\t\t\t\t\t\t\t\tlet extension = extensions[i]\n\t\t\t\t\t\t\t\tif (extension.write) {\n\t\t\t\t\t\t\t\t\tif (extension.type) {\n\t\t\t\t\t\t\t\t\t\ttarget[position++] = 0xd4 // one byte \"tag\" extension\n\t\t\t\t\t\t\t\t\t\ttarget[position++] = extension.type\n\t\t\t\t\t\t\t\t\t\ttarget[position++] = 0\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tlet writeResult = extension.write.call(this, value)\n\t\t\t\t\t\t\t\t\tif (writeResult === value) { // avoid infinite recursion\n\t\t\t\t\t\t\t\t\t\tif (Array.isArray(value)) {\n\t\t\t\t\t\t\t\t\t\t\tpackArray(value)\n\t\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\t\twriteObject(value)\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\tpack(writeResult)\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tlet currentTarget = target\n\t\t\t\t\t\t\t\tlet currentTargetView = targetView\n\t\t\t\t\t\t\t\tlet currentPosition = position\n\t\t\t\t\t\t\t\ttarget = null\n\t\t\t\t\t\t\t\tlet result\n\t\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\t\tresult = extension.pack.call(this, value, (size) => {\n\t\t\t\t\t\t\t\t\t\t// restore target and use it\n\t\t\t\t\t\t\t\t\t\ttarget = currentTarget\n\t\t\t\t\t\t\t\t\t\tcurrentTarget = null\n\t\t\t\t\t\t\t\t\t\tposition += size\n\t\t\t\t\t\t\t\t\t\tif (position > safeEnd)\n\t\t\t\t\t\t\t\t\t\t\tmakeRoom(position)\n\t\t\t\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t\t\t\ttarget, targetView, position: position - size\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}, pack)\n\t\t\t\t\t\t\t\t} finally {\n\t\t\t\t\t\t\t\t\t// restore current target information (unless already restored)\n\t\t\t\t\t\t\t\t\tif (currentTarget) {\n\t\t\t\t\t\t\t\t\t\ttarget = currentTarget\n\t\t\t\t\t\t\t\t\t\ttargetView = currentTargetView\n\t\t\t\t\t\t\t\t\t\tposition = currentPosition\n\t\t\t\t\t\t\t\t\t\tsafeEnd = target.length - 10\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif (result) {\n\t\t\t\t\t\t\t\t\tif (result.length + position > safeEnd)\n\t\t\t\t\t\t\t\t\t\tmakeRoom(result.length + position)\n\t\t\t\t\t\t\t\t\tposition = writeExtensionData(result, target, position, extension.type)\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// check isArray after extensions, because extensions can extend Array\n\t\t\t\t\t\tif (Array.isArray(value)) {\n\t\t\t\t\t\t\tpackArray(value)\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// use this as an alternate mechanism for expressing how to serialize\n\t\t\t\t\t\t\tif (value.toJSON) {\n\t\t\t\t\t\t\t\tconst json = value.toJSON()\n\t\t\t\t\t\t\t\t// if for some reason value.toJSON returns itself it'll loop forever\n\t\t\t\t\t\t\t\tif (json !== value)\n\t\t\t\t\t\t\t\t\treturn pack(json)\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t// if there is a writeFunction, use it, otherwise just encode as undefined\n\t\t\t\t\t\t\tif (type === 'function')\n\t\t\t\t\t\t\t\treturn pack(this.writeFunction && this.writeFunction(value));\n\n\t\t\t\t\t\t\t// no extension found, write as plain object\n\t\t\t\t\t\t\twriteObject(value)\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else if (type === 'boolean') {\n\t\t\t\ttarget[position++] = value ? 0xc3 : 0xc2\n\t\t\t} else if (type === 'bigint') {\n\t\t\t\tif (value < 0x8000000000000000 && value >= -0x8000000000000000) {\n\t\t\t\t\t// use a signed int as long as it fits\n\t\t\t\t\ttarget[position++] = 0xd3\n\t\t\t\t\ttargetView.setBigInt64(position, value)\n\t\t\t\t} else if (value < 0x10000000000000000 && value > 0) {\n\t\t\t\t\t// if we can fit an unsigned int, use that\n\t\t\t\t\ttarget[position++] = 0xcf\n\t\t\t\t\ttargetView.setBigUint64(position, value)\n\t\t\t\t} else {\n\t\t\t\t\t// overflow\n\t\t\t\t\tif (this.largeBigIntToFloat) {\n\t\t\t\t\t\ttarget[position++] = 0xcb\n\t\t\t\t\t\ttargetView.setFloat64(position, Number(value))\n\t\t\t\t\t} else if (this.largeBigIntToString) {\n\t\t\t\t\t\treturn pack(value.toString());\n\t\t\t\t\t} else if ((this.useBigIntExtension || this.moreTypes) && value < BigInt(2)**BigInt(1023) && value > -(BigInt(2)**BigInt(1023))) {\n\t\t\t\t\t\ttarget[position++] = 0xc7\n\t\t\t\t\t\tposition++;\n\t\t\t\t\t\ttarget[position++] = 0x42 // \"B\" for BigInt\n\t\t\t\t\t\tlet bytes = [];\n\t\t\t\t\t\tlet alignedSign;\n\t\t\t\t\t\tdo {\n\t\t\t\t\t\t\tlet byte = value & BigInt(0xff);\n\t\t\t\t\t\t\talignedSign = (byte & BigInt(0x80)) === (value < BigInt(0) ? BigInt(0x80) : BigInt(0));\n\t\t\t\t\t\t\tbytes.push(byte);\n\t\t\t\t\t\t\tvalue >>= BigInt(8);\n\t\t\t\t\t\t} while (!((value === BigInt(0) || value === BigInt(-1)) && alignedSign));\n\t\t\t\t\t\ttarget[position-2] = bytes.length;\n\t\t\t\t\t\tfor (let i = bytes.length; i > 0;) {\n\t\t\t\t\t\t\ttarget[position++] = Number(bytes[--i]);\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new RangeError(value + ' was too large to fit in MessagePack 64-bit integer format, use' +\n\t\t\t\t\t\t\t' useBigIntExtension, or set largeBigIntToFloat to convert to float-64, or set' +\n\t\t\t\t\t\t\t' largeBigIntToString to convert to string')\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tposition += 8\n\t\t\t} else if (type === 'undefined') {\n\t\t\t\tif (this.encodeUndefinedAsNil)\n\t\t\t\t\ttarget[position++] = 0xc0\n\t\t\t\telse {\n\t\t\t\t\ttarget[position++] = 0xd4 // a number of implementations use fixext1 with type 0, data 0 to denote undefined, so we follow suite\n\t\t\t\t\ttarget[position++] = 0\n\t\t\t\t\ttarget[position++] = 0\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tthrow new Error('Unknown type: ' + type)\n\t\t\t}\n\t\t}\n\n\t\tconst writePlainObject = (this.variableMapSize || this.coercibleKeyAsNumber || this.skipValues) ? (object) => {\n\t\t\t// this method is slightly slower, but generates \"preferred serialization\" (optimally small for smaller objects)\n\t\t\tlet keys;\n\t\t\tif (this.skipValues) {\n\t\t\t\tkeys = [];\n\t\t\t\tfor (let key in object) {\n\t\t\t\t\tif ((typeof object.hasOwnProperty !== 'function' || object.hasOwnProperty(key)) &&\n\t\t\t\t\t\t!this.skipValues.includes(object[key]))\n\t\t\t\t\t\tkeys.push(key);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tkeys = Object.keys(object)\n\t\t\t}\n\t\t\tlet length = keys.length\n\t\t\tif (length < 0x10) {\n\t\t\t\ttarget[position++] = 0x80 | length\n\t\t\t} else if (length < 0x10000) {\n\t\t\t\ttarget[position++] = 0xde\n\t\t\t\ttarget[position++] = length >> 8\n\t\t\t\ttarget[position++] = length & 0xff\n\t\t\t} else {\n\t\t\t\ttarget[position++] = 0xdf\n\t\t\t\ttargetView.setUint32(position, length)\n\t\t\t\tposition += 4\n\t\t\t}\n\t\t\tlet key\n\t\t\tif (this.coercibleKeyAsNumber) {\n\t\t\t\tfor (let i = 0; i < length; i++) {\n\t\t\t\t\tkey = keys[i]\n\t\t\t\t\tlet num = Number(key)\n\t\t\t\t\tpack(isNaN(num) ? key : num)\n\t\t\t\t\tpack(object[key])\n\t\t\t\t}\n\n\t\t\t} else {\n\t\t\t\tfor (let i = 0; i < length; i++) {\n\t\t\t\t\tpack(key = keys[i])\n\t\t\t\t\tpack(object[key])\n\t\t\t\t}\n\t\t\t}\n\t\t} :\n\t\t(object) => {\n\t\t\ttarget[position++] = 0xde // always using map 16, so we can preallocate and set the length afterwards\n\t\t\tlet objectOffset = position - start\n\t\t\tposition += 2\n\t\t\tlet size = 0\n\t\t\tfor (let key in object) {\n\t\t\t\tif (typeof object.hasOwnProperty !== 'function' || object.hasOwnProperty(key)) {\n\t\t\t\t\tpack(key)\n\t\t\t\t\tpack(object[key])\n\t\t\t\t\tsize++\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (size > 0xffff) {\n\t\t\t\tthrow new Error('Object is too large to serialize with fast 16-bit map size,' +\n\t\t\t\t' use the \"variableMapSize\" option to serialize this object');\n\t\t\t}\n\t\t\ttarget[objectOffset++ + start] = size >> 8\n\t\t\ttarget[objectOffset + start] = size & 0xff\n\t\t}\n\n\t\tconst writeRecord = this.useRecords === false ? writePlainObject :\n\t\t(options.progressiveRecords && !useTwoByteRecords) ?  // this is about 2% faster for highly stable structures, since it only requires one for-in loop (but much more expensive when new structure needs to be written)\n\t\t(object) => {\n\t\t\tlet nextTransition, transition = structures.transitions || (structures.transitions = Object.create(null))\n\t\t\tlet objectOffset = position++ - start\n\t\t\tlet wroteKeys\n\t\t\tfor (let key in object) {\n\t\t\t\tif (typeof object.hasOwnProperty !== 'function' || object.hasOwnProperty(key)) {\n\t\t\t\t\tnextTransition = transition[key]\n\t\t\t\t\tif (nextTransition)\n\t\t\t\t\t\ttransition = nextTransition\n\t\t\t\t\telse {\n\t\t\t\t\t\t// record doesn't exist, create full new record and insert it\n\t\t\t\t\t\tlet keys = Object.keys(object)\n\t\t\t\t\t\tlet lastTransition = transition\n\t\t\t\t\t\ttransition = structures.transitions\n\t\t\t\t\t\tlet newTransitions = 0\n\t\t\t\t\t\tfor (let i = 0, l = keys.length; i < l; i++) {\n\t\t\t\t\t\t\tlet key = keys[i]\n\t\t\t\t\t\t\tnextTransition = transition[key]\n\t\t\t\t\t\t\tif (!nextTransition) {\n\t\t\t\t\t\t\t\tnextTransition = transition[key] = Object.create(null)\n\t\t\t\t\t\t\t\tnewTransitions++\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\ttransition = nextTransition\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (objectOffset + start + 1 == position) {\n\t\t\t\t\t\t\t// first key, so we don't need to insert, we can just write record directly\n\t\t\t\t\t\t\tposition--\n\t\t\t\t\t\t\tnewRecord(transition, keys, newTransitions)\n\t\t\t\t\t\t} else // otherwise we need to insert the record, moving existing data after the record\n\t\t\t\t\t\t\tinsertNewRecord(transition, keys, objectOffset, newTransitions)\n\t\t\t\t\t\twroteKeys = true\n\t\t\t\t\t\ttransition = lastTransition[key]\n\t\t\t\t\t}\n\t\t\t\t\tpack(object[key])\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (!wroteKeys) {\n\t\t\t\tlet recordId = transition[RECORD_SYMBOL]\n\t\t\t\tif (recordId)\n\t\t\t\t\ttarget[objectOffset + start] = recordId\n\t\t\t\telse\n\t\t\t\t\tinsertNewRecord(transition, Object.keys(object), objectOffset, 0)\n\t\t\t}\n\t\t} :\n\t\t(object) => {\n\t\t\tlet nextTransition, transition = structures.transitions || (structures.transitions = Object.create(null))\n\t\t\tlet newTransitions = 0\n\t\t\tfor (let key in object) if (typeof object.hasOwnProperty !== 'function' || object.hasOwnProperty(key)) {\n\t\t\t\tnextTransition = transition[key]\n\t\t\t\tif (!nextTransition) {\n\t\t\t\t\tnextTransition = transition[key] = Object.create(null)\n\t\t\t\t\tnewTransitions++\n\t\t\t\t}\n\t\t\t\ttransition = nextTransition\n\t\t\t}\n\t\t\tlet recordId = transition[RECORD_SYMBOL]\n\t\t\tif (recordId) {\n\t\t\t\tif (recordId >= 0x60 && useTwoByteRecords) {\n\t\t\t\t\ttarget[position++] = ((recordId -= 0x60) & 0x1f) + 0x60\n\t\t\t\t\ttarget[position++] = recordId >> 5\n\t\t\t\t} else\n\t\t\t\t\ttarget[position++] = recordId\n\t\t\t} else {\n\t\t\t\tnewRecord(transition, transition.__keys__ || Object.keys(object), newTransitions)\n\t\t\t}\n\t\t\t// now write the values\n\t\t\tfor (let key in object)\n\t\t\t\tif (typeof object.hasOwnProperty !== 'function' || object.hasOwnProperty(key)) {\n\t\t\t\t\tpack(object[key])\n\t\t\t\t}\n\t\t}\n\n\t\t// create reference to useRecords if useRecords is a function\n\t\tconst checkUseRecords = typeof this.useRecords == 'function' && this.useRecords;\n\n\t\tconst writeObject = checkUseRecords ? (object) => {\n\t\t\tcheckUseRecords(object) ? writeRecord(object) : writePlainObject(object)\n\t\t} : writeRecord\n\n\t\tconst makeRoom = (end) => {\n\t\t\tlet newSize\n\t\t\tif (end > 0x1000000) {\n\t\t\t\t// special handling for really large buffers\n\t\t\t\tif ((end - start) > MAX_BUFFER_SIZE)\n\t\t\t\t\tthrow new Error('Packed buffer would be larger than maximum buffer size')\n\t\t\t\tnewSize = Math.min(MAX_BUFFER_SIZE,\n\t\t\t\t\tMath.round(Math.max((end - start) * (end > 0x4000000 ? 1.25 : 2), 0x400000) / 0x1000) * 0x1000)\n\t\t\t} else // faster handling for smaller buffers\n\t\t\t\tnewSize = ((Math.max((end - start) << 2, target.length - 1) >> 12) + 1) << 12\n\t\t\tlet newBuffer = new ByteArrayAllocate(newSize)\n\t\t\ttargetView = newBuffer.dataView || (newBuffer.dataView = new DataView(newBuffer.buffer, 0, newSize))\n\t\t\tend = Math.min(end, target.length)\n\t\t\tif (target.copy)\n\t\t\t\ttarget.copy(newBuffer, 0, start, end)\n\t\t\telse\n\t\t\t\tnewBuffer.set(target.slice(start, end))\n\t\t\tposition -= start\n\t\t\tstart = 0\n\t\t\tsafeEnd = newBuffer.length - 10\n\t\t\treturn target = newBuffer\n\t\t}\n\t\tconst newRecord = (transition, keys, newTransitions) => {\n\t\t\tlet recordId = structures.nextId\n\t\t\tif (!recordId)\n\t\t\t\trecordId = 0x40\n\t\t\tif (recordId < sharedLimitId && this.shouldShareStructure && !this.shouldShareStructure(keys)) {\n\t\t\t\trecordId = structures.nextOwnId\n\t\t\t\tif (!(recordId < maxStructureId))\n\t\t\t\t\trecordId = sharedLimitId\n\t\t\t\tstructures.nextOwnId = recordId + 1\n\t\t\t} else {\n\t\t\t\tif (recordId >= maxStructureId)// cycle back around\n\t\t\t\t\trecordId = sharedLimitId\n\t\t\t\tstructures.nextId = recordId + 1\n\t\t\t}\n\t\t\tlet highByte = keys.highByte = recordId >= 0x60 && useTwoByteRecords ? (recordId - 0x60) >> 5 : -1\n\t\t\ttransition[RECORD_SYMBOL] = recordId\n\t\t\ttransition.__keys__ = keys\n\t\t\tstructures[recordId - 0x40] = keys\n\n\t\t\tif (recordId < sharedLimitId) {\n\t\t\t\tkeys.isShared = true\n\t\t\t\tstructures.sharedLength = recordId - 0x3f\n\t\t\t\thasSharedUpdate = true\n\t\t\t\tif (highByte >= 0) {\n\t\t\t\t\ttarget[position++] = (recordId & 0x1f) + 0x60\n\t\t\t\t\ttarget[position++] = highByte\n\t\t\t\t} else {\n\t\t\t\t\ttarget[position++] = recordId\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tif (highByte >= 0) {\n\t\t\t\t\ttarget[position++] = 0xd5 // fixext 2\n\t\t\t\t\ttarget[position++] = 0x72 // \"r\" record defintion extension type\n\t\t\t\t\ttarget[position++] = (recordId & 0x1f) + 0x60\n\t\t\t\t\ttarget[position++] = highByte\n\t\t\t\t} else {\n\t\t\t\t\ttarget[position++] = 0xd4 // fixext 1\n\t\t\t\t\ttarget[position++] = 0x72 // \"r\" record defintion extension type\n\t\t\t\t\ttarget[position++] = recordId\n\t\t\t\t}\n\n\t\t\t\tif (newTransitions)\n\t\t\t\t\ttransitionsCount += serializationsSinceTransitionRebuild * newTransitions\n\t\t\t\t// record the removal of the id, we can maintain our shared structure\n\t\t\t\tif (recordIdsToRemove.length >= maxOwnStructures)\n\t\t\t\t\trecordIdsToRemove.shift()[RECORD_SYMBOL] = 0 // we are cycling back through, and have to remove old ones\n\t\t\t\trecordIdsToRemove.push(transition)\n\t\t\t\tpack(keys)\n\t\t\t}\n\t\t}\n\t\tconst insertNewRecord = (transition, keys, insertionOffset, newTransitions) => {\n\t\t\tlet mainTarget = target\n\t\t\tlet mainPosition = position\n\t\t\tlet mainSafeEnd = safeEnd\n\t\t\tlet mainStart = start\n\t\t\ttarget = keysTarget\n\t\t\tposition = 0\n\t\t\tstart = 0\n\t\t\tif (!target)\n\t\t\t\tkeysTarget = target = new ByteArrayAllocate(8192)\n\t\t\tsafeEnd = target.length - 10\n\t\t\tnewRecord(transition, keys, newTransitions)\n\t\t\tkeysTarget = target\n\t\t\tlet keysPosition = position\n\t\t\ttarget = mainTarget\n\t\t\tposition = mainPosition\n\t\t\tsafeEnd = mainSafeEnd\n\t\t\tstart = mainStart\n\t\t\tif (keysPosition > 1) {\n\t\t\t\tlet newEnd = position + keysPosition - 1\n\t\t\t\tif (newEnd > safeEnd)\n\t\t\t\t\tmakeRoom(newEnd)\n\t\t\t\tlet insertionPosition = insertionOffset + start\n\t\t\t\ttarget.copyWithin(insertionPosition + keysPosition, insertionPosition + 1, position)\n\t\t\t\ttarget.set(keysTarget.slice(0, keysPosition), insertionPosition)\n\t\t\t\tposition = newEnd\n\t\t\t} else {\n\t\t\t\ttarget[insertionOffset + start] = keysTarget[0]\n\t\t\t}\n\t\t}\n\t\tconst writeStruct = (object) => {\n\t\t\tlet newPosition = writeStructSlots(object, target, start, position, structures, makeRoom, (value, newPosition, notifySharedUpdate) => {\n\t\t\t\tif (notifySharedUpdate)\n\t\t\t\t\treturn hasSharedUpdate = true;\n\t\t\t\tposition = newPosition;\n\t\t\t\tlet startTarget = target;\n\t\t\t\tpack(value);\n\t\t\t\tresetStructures();\n\t\t\t\tif (startTarget !== target) {\n\t\t\t\t\treturn { position, targetView, target }; // indicate the buffer was re-allocated\n\t\t\t\t}\n\t\t\t\treturn position;\n\t\t\t}, this);\n\t\t\tif (newPosition === 0) // bail and go to a msgpack object\n\t\t\t\treturn writeObject(object);\n\t\t\tposition = newPosition;\n\t\t}\n\t}\n\tuseBuffer(buffer) {\n\t\t// this means we are finished using our own buffer and we can write over it safely\n\t\ttarget = buffer\n\t\ttarget.dataView || (target.dataView = new DataView(target.buffer, target.byteOffset, target.byteLength))\n\t\tposition = 0\n\t}\n\tset position (value) {\n\t\tposition = value;\n\t}\n\tget position() {\n\t\treturn position;\n\t}\n\tclearSharedData() {\n\t\tif (this.structures)\n\t\t\tthis.structures = []\n\t\tif (this.typedStructs)\n\t\t\tthis.typedStructs = []\n\t}\n}\n\nextensionClasses = [ Date, Set, Error, RegExp, ArrayBuffer, Object.getPrototypeOf(Uint8Array.prototype).constructor /*TypedArray*/, DataView, C1Type ]\nextensions = [{\n\tpack(date, allocateForWrite, pack) {\n\t\tlet seconds = date.getTime() / 1000\n\t\tif ((this.useTimestamp32 || date.getMilliseconds() === 0) && seconds >= 0 && seconds < 0x100000000) {\n\t\t\t// Timestamp 32\n\t\t\tlet { target, targetView, position} = allocateForWrite(6)\n\t\t\ttarget[position++] = 0xd6\n\t\t\ttarget[position++] = 0xff\n\t\t\ttargetView.setUint32(position, seconds)\n\t\t} else if (seconds > 0 && seconds < 0x100000000) {\n\t\t\t// Timestamp 64\n\t\t\tlet { target, targetView, position} = allocateForWrite(10)\n\t\t\ttarget[position++] = 0xd7\n\t\t\ttarget[position++] = 0xff\n\t\t\ttargetView.setUint32(position, date.getMilliseconds() * 4000000 + ((seconds / 1000 / 0x100000000) >> 0))\n\t\t\ttargetView.setUint32(position + 4, seconds)\n\t\t} else if (isNaN(seconds)) {\n\t\t\tif (this.onInvalidDate) {\n\t\t\t\tallocateForWrite(0)\n\t\t\t\treturn pack(this.onInvalidDate())\n\t\t\t}\n\t\t\t// Intentionally invalid timestamp\n\t\t\tlet { target, targetView, position} = allocateForWrite(3)\n\t\t\ttarget[position++] = 0xd4\n\t\t\ttarget[position++] = 0xff\n\t\t\ttarget[position++] = 0xff\n\t\t} else {\n\t\t\t// Timestamp 96\n\t\t\tlet { target, targetView, position} = allocateForWrite(15)\n\t\t\ttarget[position++] = 0xc7\n\t\t\ttarget[position++] = 12\n\t\t\ttarget[position++] = 0xff\n\t\t\ttargetView.setUint32(position, date.getMilliseconds() * 1000000)\n\t\t\ttargetView.setBigInt64(position + 4, BigInt(Math.floor(seconds)))\n\t\t}\n\t}\n}, {\n\tpack(set, allocateForWrite, pack) {\n\t\tif (this.setAsEmptyObject) {\n\t\t\tallocateForWrite(0);\n\t\t\treturn pack({})\n\t\t}\n\t\tlet array = Array.from(set)\n\t\tlet { target, position} = allocateForWrite(this.moreTypes ? 3 : 0)\n\t\tif (this.moreTypes) {\n\t\t\ttarget[position++] = 0xd4\n\t\t\ttarget[position++] = 0x73 // 's' for Set\n\t\t\ttarget[position++] = 0\n\t\t}\n\t\tpack(array)\n\t}\n}, {\n\tpack(error, allocateForWrite, pack) {\n\t\tlet { target, position} = allocateForWrite(this.moreTypes ? 3 : 0)\n\t\tif (this.moreTypes) {\n\t\t\ttarget[position++] = 0xd4\n\t\t\ttarget[position++] = 0x65 // 'e' for error\n\t\t\ttarget[position++] = 0\n\t\t}\n\t\tpack([ error.name, error.message, error.cause ])\n\t}\n}, {\n\tpack(regex, allocateForWrite, pack) {\n\t\tlet { target, position} = allocateForWrite(this.moreTypes ? 3 : 0)\n\t\tif (this.moreTypes) {\n\t\t\ttarget[position++] = 0xd4\n\t\t\ttarget[position++] = 0x78 // 'x' for regeXp\n\t\t\ttarget[position++] = 0\n\t\t}\n\t\tpack([ regex.source, regex.flags ])\n\t}\n}, {\n\tpack(arrayBuffer, allocateForWrite) {\n\t\tif (this.moreTypes)\n\t\t\twriteExtBuffer(arrayBuffer, 0x10, allocateForWrite)\n\t\telse\n\t\t\twriteBuffer(hasNodeBuffer ? Buffer.from(arrayBuffer) : new Uint8Array(arrayBuffer), allocateForWrite)\n\t}\n}, {\n\tpack(typedArray, allocateForWrite) {\n\t\tlet constructor = typedArray.constructor\n\t\tif (constructor !== ByteArray && this.moreTypes)\n\t\t\twriteExtBuffer(typedArray, typedArrays.indexOf(constructor.name), allocateForWrite)\n\t\telse\n\t\t\twriteBuffer(typedArray, allocateForWrite)\n\t}\n}, {\n\tpack(arrayBuffer, allocateForWrite) {\n\t\tif (this.moreTypes)\n\t\t\twriteExtBuffer(arrayBuffer, 0x11, allocateForWrite)\n\t\telse\n\t\t\twriteBuffer(hasNodeBuffer ? Buffer.from(arrayBuffer) : new Uint8Array(arrayBuffer), allocateForWrite)\n\t}\n}, {\n\tpack(c1, allocateForWrite) { // specific 0xC1 object\n\t\tlet { target, position} = allocateForWrite(1)\n\t\ttarget[position] = 0xc1\n\t}\n}]\n\nfunction writeExtBuffer(typedArray, type, allocateForWrite, encode) {\n\tlet length = typedArray.byteLength\n\tif (length + 1 < 0x100) {\n\t\tvar { target, position } = allocateForWrite(4 + length)\n\t\ttarget[position++] = 0xc7\n\t\ttarget[position++] = length + 1\n\t} else if (length + 1 < 0x10000) {\n\t\tvar { target, position } = allocateForWrite(5 + length)\n\t\ttarget[position++] = 0xc8\n\t\ttarget[position++] = (length + 1) >> 8\n\t\ttarget[position++] = (length + 1) & 0xff\n\t} else {\n\t\tvar { target, position, targetView } = allocateForWrite(7 + length)\n\t\ttarget[position++] = 0xc9\n\t\ttargetView.setUint32(position, length + 1) // plus one for the type byte\n\t\tposition += 4\n\t}\n\ttarget[position++] = 0x74 // \"t\" for typed array\n\ttarget[position++] = type\n\tif (!typedArray.buffer) typedArray = new Uint8Array(typedArray)\n\ttarget.set(new Uint8Array(typedArray.buffer, typedArray.byteOffset, typedArray.byteLength), position)\n}\nfunction writeBuffer(buffer, allocateForWrite) {\n\tlet length = buffer.byteLength\n\tvar target, position\n\tif (length < 0x100) {\n\t\tvar { target, position } = allocateForWrite(length + 2)\n\t\ttarget[position++] = 0xc4\n\t\ttarget[position++] = length\n\t} else if (length < 0x10000) {\n\t\tvar { target, position } = allocateForWrite(length + 3)\n\t\ttarget[position++] = 0xc5\n\t\ttarget[position++] = length >> 8\n\t\ttarget[position++] = length & 0xff\n\t} else {\n\t\tvar { target, position, targetView } = allocateForWrite(length + 5)\n\t\ttarget[position++] = 0xc6\n\t\ttargetView.setUint32(position, length)\n\t\tposition += 4\n\t}\n\ttarget.set(buffer, position)\n}\n\nfunction writeExtensionData(result, target, position, type) {\n\tlet length = result.length\n\tswitch (length) {\n\t\tcase 1:\n\t\t\ttarget[position++] = 0xd4\n\t\t\tbreak\n\t\tcase 2:\n\t\t\ttarget[position++] = 0xd5\n\t\t\tbreak\n\t\tcase 4:\n\t\t\ttarget[position++] = 0xd6\n\t\t\tbreak\n\t\tcase 8:\n\t\t\ttarget[position++] = 0xd7\n\t\t\tbreak\n\t\tcase 16:\n\t\t\ttarget[position++] = 0xd8\n\t\t\tbreak\n\t\tdefault:\n\t\t\tif (length < 0x100) {\n\t\t\t\ttarget[position++] = 0xc7\n\t\t\t\ttarget[position++] = length\n\t\t\t} else if (length < 0x10000) {\n\t\t\t\ttarget[position++] = 0xc8\n\t\t\t\ttarget[position++] = length >> 8\n\t\t\t\ttarget[position++] = length & 0xff\n\t\t\t} else {\n\t\t\t\ttarget[position++] = 0xc9\n\t\t\t\ttarget[position++] = length >> 24\n\t\t\t\ttarget[position++] = (length >> 16) & 0xff\n\t\t\t\ttarget[position++] = (length >> 8) & 0xff\n\t\t\t\ttarget[position++] = length & 0xff\n\t\t\t}\n\t}\n\ttarget[position++] = type\n\ttarget.set(result, position)\n\tposition += length\n\treturn position\n}\n\nfunction insertIds(serialized, idsToInsert) {\n\t// insert the ids that need to be referenced for structured clones\n\tlet nextId\n\tlet distanceToMove = idsToInsert.length * 6\n\tlet lastEnd = serialized.length - distanceToMove\n\twhile (nextId = idsToInsert.pop()) {\n\t\tlet offset = nextId.offset\n\t\tlet id = nextId.id\n\t\tserialized.copyWithin(offset + distanceToMove, offset, lastEnd)\n\t\tdistanceToMove -= 6\n\t\tlet position = offset + distanceToMove\n\t\tserialized[position++] = 0xd6\n\t\tserialized[position++] = 0x69 // 'i'\n\t\tserialized[position++] = id >> 24\n\t\tserialized[position++] = (id >> 16) & 0xff\n\t\tserialized[position++] = (id >> 8) & 0xff\n\t\tserialized[position++] = id & 0xff\n\t\tlastEnd = offset\n\t}\n\treturn serialized\n}\n\nfunction writeBundles(start, pack, incrementPosition) {\n\tif (bundledStrings.length > 0) {\n\t\ttargetView.setUint32(bundledStrings.position + start, position + incrementPosition - bundledStrings.position - start)\n\t\tbundledStrings.stringsPosition = position - start;\n\t\tlet writeStrings = bundledStrings\n\t\tbundledStrings = null\n\t\tpack(writeStrings[0])\n\t\tpack(writeStrings[1])\n\t}\n}\n\nexport function addExtension(extension) {\n\tif (extension.Class) {\n\t\tif (!extension.pack && !extension.write)\n\t\t\tthrow new Error('Extension has no pack or write function')\n\t\tif (extension.pack && !extension.type)\n\t\t\tthrow new Error('Extension has no type (numeric code to identify the extension)')\n\t\textensionClasses.unshift(extension.Class)\n\t\textensions.unshift(extension)\n\t}\n\tunpackAddExtension(extension)\n}\nfunction prepareStructures(structures, packr) {\n\tstructures.isCompatible = (existingStructures) => {\n\t\tlet compatible = !existingStructures || ((packr.lastNamedStructuresLength || 0) === existingStructures.length)\n\t\tif (!compatible) // we want to merge these existing structures immediately since we already have it and we are in the right transaction\n\t\t\tpackr._mergeStructures(existingStructures);\n\t\treturn compatible;\n\t}\n\treturn structures\n}\nexport function setWriteStructSlots(writeSlots, makeStructures) {\n\twriteStructSlots = writeSlots;\n\tprepareStructures = makeStructures;\n}\n\nlet defaultPackr = new Packr({ useRecords: false })\nexport const pack = defaultPackr.pack\nexport const encode = defaultPackr.pack\nexport const Encoder = Packr\nexport { FLOAT32_OPTIONS } from './unpack.js'\nimport { FLOAT32_OPTIONS } from './unpack.js'\nexport const { NEVER, ALWAYS, DECIMAL_ROUND, DECIMAL_FIT } = FLOAT32_OPTIONS\nexport const REUSE_BUFFER_MODE = 512\nexport const RESET_BUFFER_MODE = 1024\nexport const RESERVE_START_SPACE = 2048", "\n/*\n\nFor \"any-data\":\n32-55 - record with record ids (-32)\n56 - 8-bit record ids\n57 - 16-bit record ids\n58 - 24-bit record ids\n59 - 32-bit record ids\n250-255 - followed by typed fixed width values\n64-250 msgpackr/cbor/paired data\narrays and strings within arrays are handled by paired encoding\n\nStructure encoding:\n(type - string (using paired encoding))+\n\nType encoding\nencoding byte - fixed width byte - next reference+\n\nEncoding byte:\nfirst bit:\n\t0 - inline\n\t1 - reference\nsecond bit:\n\t0 - data or number\n\t1 - string\n\nremaining bits:\n\tcharacter encoding - ISO-8859-x\n\n\nnull (0xff)+ 0xf6\nnull (0xff)+ 0xf7\n\n*/\n\n\nimport {setWriteStructSlots, RECORD_SYMBOL, addExtension} from './pack.js'\nimport {setReadStruct, mult10, readString} from './unpack.js';\nconst ASCII = 3; // the MIBenum from https://www.iana.org/assignments/character-sets/character-sets.xhtml (and other character encodings could be referenced by MIBenum)\nconst NUMBER = 0;\nconst UTF8 = 2;\nconst OBJECT_DATA = 1;\nconst DATE = 16;\nconst TYPE_NAMES = ['num', 'object', 'string', 'ascii'];\nTYPE_NAMES[DATE] = 'date';\nconst float32Headers = [false, true, true, false, false, true, true, false];\nlet evalSupported;\ntry {\n\tnew Function('');\n\tevalSupported = true;\n} catch(error) {\n\t// if eval variants are not supported, do not create inline object readers ever\n}\n\nlet updatedPosition;\nconst hasNodeBuffer = typeof Buffer !== 'undefined'\nlet textEncoder, currentSource;\ntry {\n\ttextEncoder = new TextEncoder()\n} catch (error) {}\nconst encodeUtf8 = hasNodeBuffer ? function(target, string, position) {\n\treturn target.utf8Write(string, position, target.byteLength - position)\n} : (textEncoder && textEncoder.encodeInto) ?\n\tfunction(target, string, position) {\n\t\treturn textEncoder.encodeInto(string, target.subarray(position)).written\n\t} : false\n\nconst TYPE = Symbol('type');\nconst PARENT = Symbol('parent');\nsetWriteStructSlots(writeStruct, prepareStructures);\nfunction writeStruct(object, target, encodingStart, position, structures, makeRoom, pack, packr) {\n\tlet typedStructs = packr.typedStructs || (packr.typedStructs = []);\n\t// note that we rely on pack.js to load stored structures before we get to this point\n\tlet targetView = target.dataView;\n\tlet refsStartPosition = (typedStructs.lastStringStart || 100) + position;\n\tlet safeEnd = target.length - 10;\n\tlet start = position;\n\tif (position > safeEnd) {\n\t\ttarget = makeRoom(position);\n\t\ttargetView = target.dataView;\n\t\tposition -= encodingStart;\n\t\tstart -= encodingStart;\n\t\trefsStartPosition -= encodingStart;\n\t\tencodingStart = 0;\n\t\tsafeEnd = target.length - 10;\n\t}\n\n\tlet refOffset, refPosition = refsStartPosition;\n\n\tlet transition = typedStructs.transitions || (typedStructs.transitions = Object.create(null));\n\tlet nextId = typedStructs.nextId || typedStructs.length;\n\tlet headerSize =\n\t\tnextId < 0xf ? 1 :\n\t\t\tnextId < 0xf0 ? 2 :\n\t\t\t\tnextId < 0xf000 ? 3 :\n\t\t\t\t\tnextId < 0xf00000 ? 4 : 0;\n\tif (headerSize === 0)\n\t\treturn 0;\n\tposition += headerSize;\n\tlet queuedReferences = [];\n\tlet usedAscii0;\n\tlet keyIndex = 0;\n\tfor (let key in object) {\n\t\tlet value = object[key];\n\t\tlet nextTransition = transition[key];\n\t\tif (!nextTransition) {\n\t\t\ttransition[key] = nextTransition = {\n\t\t\t\tkey,\n\t\t\t\tparent: transition,\n\t\t\t\tenumerationOffset: 0,\n\t\t\t\tascii0: null,\n\t\t\t\tascii8: null,\n\t\t\t\tnum8: null,\n\t\t\t\tstring16: null,\n\t\t\t\tobject16: null,\n\t\t\t\tnum32: null,\n\t\t\t\tfloat64: null,\n\t\t\t\tdate64: null\n\t\t\t};\n\t\t}\n\t\tif (position > safeEnd) {\n\t\t\ttarget = makeRoom(position);\n\t\t\ttargetView = target.dataView;\n\t\t\tposition -= encodingStart;\n\t\t\tstart -= encodingStart;\n\t\t\trefsStartPosition -= encodingStart;\n\t\t\trefPosition -= encodingStart;\n\t\t\tencodingStart = 0;\n\t\t\tsafeEnd = target.length - 10\n\t\t}\n\t\tswitch (typeof value) {\n\t\t\tcase 'number':\n\t\t\t\tlet number = value;\n\t\t\t\t// first check to see if we are using a lot of ids and should default to wide/common format\n\t\t\t\tif (nextId < 200 || !nextTransition.num64) {\n\t\t\t\t\tif (number >> 0 === number && number < 0x20000000 && number > -0x1f000000) {\n\t\t\t\t\t\tif (number < 0xf6 && number >= 0 && (nextTransition.num8 && !(nextId > 200 && nextTransition.num32) || number < 0x20 && !nextTransition.num32)) {\n\t\t\t\t\t\t\ttransition = nextTransition.num8 || createTypeTransition(nextTransition, NUMBER, 1);\n\t\t\t\t\t\t\ttarget[position++] = number;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\ttransition = nextTransition.num32 || createTypeTransition(nextTransition, NUMBER, 4);\n\t\t\t\t\t\t\ttargetView.setUint32(position, number, true);\n\t\t\t\t\t\t\tposition += 4;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t} else if (number < 0x100000000 && number >= -0x80000000) {\n\t\t\t\t\t\ttargetView.setFloat32(position, number, true);\n\t\t\t\t\t\tif (float32Headers[target[position + 3] >>> 5]) {\n\t\t\t\t\t\t\tlet xShifted\n\t\t\t\t\t\t\t// this checks for rounding of numbers that were encoded in 32-bit float to nearest significant decimal digit that could be preserved\n\t\t\t\t\t\t\tif (((xShifted = number * mult10[((target[position + 3] & 0x7f) << 1) | (target[position + 2] >> 7)]) >> 0) === xShifted) {\n\t\t\t\t\t\t\t\ttransition = nextTransition.num32 || createTypeTransition(nextTransition, NUMBER, 4);\n\t\t\t\t\t\t\t\tposition += 4;\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\ttransition = nextTransition.num64 || createTypeTransition(nextTransition, NUMBER, 8);\n\t\t\t\ttargetView.setFloat64(position, number, true);\n\t\t\t\tposition += 8;\n\t\t\t\tbreak;\n\t\t\tcase 'string':\n\t\t\t\tlet strLength = value.length;\n\t\t\t\trefOffset = refPosition - refsStartPosition;\n\t\t\t\tif ((strLength << 2) + refPosition > safeEnd) {\n\t\t\t\t\ttarget = makeRoom((strLength << 2) + refPosition);\n\t\t\t\t\ttargetView = target.dataView;\n\t\t\t\t\tposition -= encodingStart;\n\t\t\t\t\tstart -= encodingStart;\n\t\t\t\t\trefsStartPosition -= encodingStart;\n\t\t\t\t\trefPosition -= encodingStart;\n\t\t\t\t\tencodingStart = 0;\n\t\t\t\t\tsafeEnd = target.length - 10\n\t\t\t\t}\n\t\t\t\tif (strLength > ((0xff00 + refOffset) >> 2)) {\n\t\t\t\t\tqueuedReferences.push(key, value, position - start);\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t\tlet isNotAscii\n\t\t\t\tlet strStart = refPosition;\n\t\t\t\tif (strLength < 0x40) {\n\t\t\t\t\tlet i, c1, c2;\n\t\t\t\t\tfor (i = 0; i < strLength; i++) {\n\t\t\t\t\t\tc1 = value.charCodeAt(i)\n\t\t\t\t\t\tif (c1 < 0x80) {\n\t\t\t\t\t\t\ttarget[refPosition++] = c1\n\t\t\t\t\t\t} else if (c1 < 0x800) {\n\t\t\t\t\t\t\tisNotAscii = true;\n\t\t\t\t\t\t\ttarget[refPosition++] = c1 >> 6 | 0xc0\n\t\t\t\t\t\t\ttarget[refPosition++] = c1 & 0x3f | 0x80\n\t\t\t\t\t\t} else if (\n\t\t\t\t\t\t\t(c1 & 0xfc00) === 0xd800 &&\n\t\t\t\t\t\t\t((c2 = value.charCodeAt(i + 1)) & 0xfc00) === 0xdc00\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tisNotAscii = true;\n\t\t\t\t\t\t\tc1 = 0x10000 + ((c1 & 0x03ff) << 10) + (c2 & 0x03ff)\n\t\t\t\t\t\t\ti++\n\t\t\t\t\t\t\ttarget[refPosition++] = c1 >> 18 | 0xf0\n\t\t\t\t\t\t\ttarget[refPosition++] = c1 >> 12 & 0x3f | 0x80\n\t\t\t\t\t\t\ttarget[refPosition++] = c1 >> 6 & 0x3f | 0x80\n\t\t\t\t\t\t\ttarget[refPosition++] = c1 & 0x3f | 0x80\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tisNotAscii = true;\n\t\t\t\t\t\t\ttarget[refPosition++] = c1 >> 12 | 0xe0\n\t\t\t\t\t\t\ttarget[refPosition++] = c1 >> 6 & 0x3f | 0x80\n\t\t\t\t\t\t\ttarget[refPosition++] = c1 & 0x3f | 0x80\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\trefPosition += encodeUtf8(target, value, refPosition);\n\t\t\t\t\tisNotAscii = refPosition - strStart > strLength;\n\t\t\t\t}\n\t\t\t\tif (refOffset < 0xa0 || (refOffset < 0xf6 && (nextTransition.ascii8 || nextTransition.string8))) {\n\t\t\t\t\t// short strings\n\t\t\t\t\tif (isNotAscii) {\n\t\t\t\t\t\tif (!(transition = nextTransition.string8)) {\n\t\t\t\t\t\t\tif (typedStructs.length > 10 && (transition = nextTransition.ascii8)) {\n\t\t\t\t\t\t\t\t// we can safely change ascii to utf8 in place since they are compatible\n\t\t\t\t\t\t\t\ttransition.__type = UTF8;\n\t\t\t\t\t\t\t\tnextTransition.ascii8 = null;\n\t\t\t\t\t\t\t\tnextTransition.string8 = transition;\n\t\t\t\t\t\t\t\tpack(null, 0, true); // special call to notify that structures have been updated\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\ttransition = createTypeTransition(nextTransition, UTF8, 1);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t} else if (refOffset === 0 && !usedAscii0) {\n\t\t\t\t\t\tusedAscii0 = true;\n\t\t\t\t\t\ttransition = nextTransition.ascii0 || createTypeTransition(nextTransition, ASCII, 0);\n\t\t\t\t\t\tbreak; // don't increment position\n\t\t\t\t\t}// else ascii:\n\t\t\t\t\telse if (!(transition = nextTransition.ascii8) && !(typedStructs.length > 10 && (transition = nextTransition.string8)))\n\t\t\t\t\t\ttransition = createTypeTransition(nextTransition, ASCII, 1);\n\t\t\t\t\ttarget[position++] = refOffset;\n\t\t\t\t} else {\n\t\t\t\t\t// TODO: Enable ascii16 at some point, but get the logic right\n\t\t\t\t\t//if (isNotAscii)\n\t\t\t\t\t\ttransition = nextTransition.string16 || createTypeTransition(nextTransition, UTF8, 2);\n\t\t\t\t\t//else\n\t\t\t\t\t\t//transition = nextTransition.ascii16 || createTypeTransition(nextTransition, ASCII, 2);\n\t\t\t\t\ttargetView.setUint16(position, refOffset, true);\n\t\t\t\t\tposition += 2;\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\tcase 'object':\n\t\t\t\tif (value) {\n\t\t\t\t\tif (value.constructor === Date) {\n\t\t\t\t\t\ttransition = nextTransition.date64 || createTypeTransition(nextTransition, DATE, 8);\n\t\t\t\t\t\ttargetView.setFloat64(position, value.getTime(), true);\n\t\t\t\t\t\tposition += 8;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tqueuedReferences.push(key, value, keyIndex);\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\t\t\t\t} else { // null\n\t\t\t\t\tnextTransition = anyType(nextTransition, position, targetView, -10); // match CBOR with this\n\t\t\t\t\tif (nextTransition) {\n\t\t\t\t\t\ttransition = nextTransition;\n\t\t\t\t\t\tposition = updatedPosition;\n\t\t\t\t\t} else queuedReferences.push(key, value, keyIndex);\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\tcase 'boolean':\n\t\t\t\ttransition = nextTransition.num8 || nextTransition.ascii8 || createTypeTransition(nextTransition, NUMBER, 1);\n\t\t\t\ttarget[position++] = value ? 0xf9 : 0xf8; // match CBOR with these\n\t\t\t\tbreak;\n\t\t\tcase 'undefined':\n\t\t\t\tnextTransition = anyType(nextTransition, position, targetView, -9); // match CBOR with this\n\t\t\t\tif (nextTransition) {\n\t\t\t\t\ttransition = nextTransition;\n\t\t\t\t\tposition = updatedPosition;\n\t\t\t\t} else queuedReferences.push(key, value, keyIndex);\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tqueuedReferences.push(key, value, keyIndex);\n\t\t}\n\t\tkeyIndex++;\n\t}\n\n\tfor (let i = 0, l = queuedReferences.length; i < l;) {\n\t\tlet key = queuedReferences[i++];\n\t\tlet value = queuedReferences[i++];\n\t\tlet propertyIndex = queuedReferences[i++];\n\t\tlet nextTransition = transition[key];\n\t\tif (!nextTransition) {\n\t\t\ttransition[key] = nextTransition = {\n\t\t\t\tkey,\n\t\t\t\tparent: transition,\n\t\t\t\tenumerationOffset: propertyIndex - keyIndex,\n\t\t\t\tascii0: null,\n\t\t\t\tascii8: null,\n\t\t\t\tnum8: null,\n\t\t\t\tstring16: null,\n\t\t\t\tobject16: null,\n\t\t\t\tnum32: null,\n\t\t\t\tfloat64: null\n\t\t\t};\n\t\t}\n\t\tlet newPosition;\n\t\tif (value) {\n\t\t\t/*if (typeof value === 'string') { // TODO: we could re-enable long strings\n\t\t\t\tif (position + value.length * 3 > safeEnd) {\n\t\t\t\t\ttarget = makeRoom(position + value.length * 3);\n\t\t\t\t\tposition -= start;\n\t\t\t\t\ttargetView = target.dataView;\n\t\t\t\t\tstart = 0;\n\t\t\t\t}\n\t\t\t\tnewPosition = position + target.utf8Write(value, position, 0xffffffff);\n\t\t\t} else { */\n\t\t\tlet size;\n\t\t\trefOffset = refPosition - refsStartPosition;\n\t\t\tif (refOffset < 0xff00) {\n\t\t\t\ttransition = nextTransition.object16;\n\t\t\t\tif (transition)\n\t\t\t\t\tsize = 2;\n\t\t\t\telse if ((transition = nextTransition.object32))\n\t\t\t\t\tsize = 4;\n\t\t\t\telse {\n\t\t\t\t\ttransition = createTypeTransition(nextTransition, OBJECT_DATA, 2);\n\t\t\t\t\tsize = 2;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\ttransition = nextTransition.object32 || createTypeTransition(nextTransition, OBJECT_DATA, 4);\n\t\t\t\tsize = 4;\n\t\t\t}\n\t\t\tnewPosition = pack(value, refPosition);\n\t\t\t//}\n\t\t\tif (typeof newPosition === 'object') {\n\t\t\t\t// re-allocated\n\t\t\t\trefPosition = newPosition.position;\n\t\t\t\ttargetView = newPosition.targetView;\n\t\t\t\ttarget = newPosition.target;\n\t\t\t\trefsStartPosition -= encodingStart;\n\t\t\t\tposition -= encodingStart;\n\t\t\t\tstart -= encodingStart;\n\t\t\t\tencodingStart = 0;\n\t\t\t} else\n\t\t\t\trefPosition = newPosition;\n\t\t\tif (size === 2) {\n\t\t\t\ttargetView.setUint16(position, refOffset, true);\n\t\t\t\tposition += 2;\n\t\t\t} else {\n\t\t\t\ttargetView.setUint32(position, refOffset, true);\n\t\t\t\tposition += 4;\n\t\t\t}\n\t\t} else { // null or undefined\n\t\t\ttransition = nextTransition.object16 || createTypeTransition(nextTransition, OBJECT_DATA, 2);\n\t\t\ttargetView.setInt16(position, value === null ? -10 : -9, true);\n\t\t\tposition += 2;\n\t\t}\n\t\tkeyIndex++;\n\t}\n\n\n\tlet recordId = transition[RECORD_SYMBOL];\n\tif (recordId == null) {\n\t\trecordId = packr.typedStructs.length;\n\t\tlet structure = [];\n\t\tlet nextTransition = transition;\n\t\tlet key, type;\n\t\twhile ((type = nextTransition.__type) !== undefined) {\n\t\t\tlet size = nextTransition.__size;\n\t\t\tnextTransition = nextTransition.__parent;\n\t\t\tkey = nextTransition.key;\n\t\t\tlet property = [type, size, key];\n\t\t\tif (nextTransition.enumerationOffset)\n\t\t\t\tproperty.push(nextTransition.enumerationOffset);\n\t\t\tstructure.push(property);\n\t\t\tnextTransition = nextTransition.parent;\n\t\t}\n\t\tstructure.reverse();\n\t\ttransition[RECORD_SYMBOL] = recordId;\n\t\tpackr.typedStructs[recordId] = structure;\n\t\tpack(null, 0, true); // special call to notify that structures have been updated\n\t}\n\n\n\tswitch (headerSize) {\n\t\tcase 1:\n\t\t\tif (recordId >= 0x10) return 0;\n\t\t\ttarget[start] = recordId + 0x20;\n\t\t\tbreak;\n\t\tcase 2:\n\t\t\tif (recordId >= 0x100) return 0;\n\t\t\ttarget[start] = 0x38;\n\t\t\ttarget[start + 1] = recordId;\n\t\t\tbreak;\n\t\tcase 3:\n\t\t\tif (recordId >= 0x10000) return 0;\n\t\t\ttarget[start] = 0x39;\n\t\t\ttargetView.setUint16(start + 1, recordId, true);\n\t\t\tbreak;\n\t\tcase 4:\n\t\t\tif (recordId >= 0x1000000) return 0;\n\t\t\ttargetView.setUint32(start, (recordId << 8) + 0x3a, true);\n\t\t\tbreak;\n\t}\n\n\tif (position < refsStartPosition) {\n\t\tif (refsStartPosition === refPosition)\n\t\t\treturn position; // no refs\n\t\t// adjust positioning\n\t\ttarget.copyWithin(position, refsStartPosition, refPosition);\n\t\trefPosition += position - refsStartPosition;\n\t\ttypedStructs.lastStringStart = position - start;\n\t} else if (position > refsStartPosition) {\n\t\tif (refsStartPosition === refPosition)\n\t\t\treturn position; // no refs\n\t\ttypedStructs.lastStringStart = position - start;\n\t\treturn writeStruct(object, target, encodingStart, start, structures, makeRoom, pack, packr);\n\t}\n\treturn refPosition;\n}\nfunction anyType(transition, position, targetView, value) {\n\tlet nextTransition;\n\tif ((nextTransition = transition.ascii8 || transition.num8)) {\n\t\ttargetView.setInt8(position, value, true);\n\t\tupdatedPosition = position + 1;\n\t\treturn nextTransition;\n\t}\n\tif ((nextTransition = transition.string16 || transition.object16)) {\n\t\ttargetView.setInt16(position, value, true);\n\t\tupdatedPosition = position + 2;\n\t\treturn nextTransition;\n\t}\n\tif (nextTransition = transition.num32) {\n\t\ttargetView.setUint32(position, 0xe0000100 + value, true);\n\t\tupdatedPosition = position + 4;\n\t\treturn nextTransition;\n\t}\n\t// transition.float64\n\tif (nextTransition = transition.num64) {\n\t\ttargetView.setFloat64(position, NaN, true);\n\t\ttargetView.setInt8(position, value);\n\t\tupdatedPosition = position + 8;\n\t\treturn nextTransition;\n\t}\n\tupdatedPosition = position;\n\t// TODO: can we do an \"any\" type where we defer the decision?\n\treturn;\n}\nfunction createTypeTransition(transition, type, size) {\n\tlet typeName = TYPE_NAMES[type] + (size << 3);\n\tlet newTransition = transition[typeName] || (transition[typeName] = Object.create(null));\n\tnewTransition.__type = type;\n\tnewTransition.__size = size;\n\tnewTransition.__parent = transition;\n\treturn newTransition;\n}\nfunction onLoadedStructures(sharedData) {\n\tif (!(sharedData instanceof Map))\n\t\treturn sharedData;\n\tlet typed = sharedData.get('typed') || [];\n\tif (Object.isFrozen(typed))\n\t\ttyped = typed.map(structure => structure.slice(0));\n\tlet named = sharedData.get('named');\n\tlet transitions = Object.create(null);\n\tfor (let i = 0, l = typed.length; i < l; i++) {\n\t\tlet structure = typed[i];\n\t\tlet transition = transitions;\n\t\tfor (let [type, size, key] of structure) {\n\t\t\tlet nextTransition = transition[key];\n\t\t\tif (!nextTransition) {\n\t\t\t\ttransition[key] = nextTransition = {\n\t\t\t\t\tkey,\n\t\t\t\t\tparent: transition,\n\t\t\t\t\tenumerationOffset: 0,\n\t\t\t\t\tascii0: null,\n\t\t\t\t\tascii8: null,\n\t\t\t\t\tnum8: null,\n\t\t\t\t\tstring16: null,\n\t\t\t\t\tobject16: null,\n\t\t\t\t\tnum32: null,\n\t\t\t\t\tfloat64: null,\n\t\t\t\t\tdate64: null,\n\t\t\t\t};\n\t\t\t}\n\t\t\ttransition = createTypeTransition(nextTransition, type, size);\n\t\t}\n\t\ttransition[RECORD_SYMBOL] = i;\n\t}\n\ttyped.transitions = transitions;\n\tthis.typedStructs = typed;\n\tthis.lastTypedStructuresLength = typed.length;\n\treturn named;\n}\nvar sourceSymbol = Symbol.for('source')\nfunction readStruct(src, position, srcEnd, unpackr) {\n\tlet recordId = src[position++] - 0x20;\n\tif (recordId >= 24) {\n\t\tswitch(recordId) {\n\t\t\tcase 24: recordId = src[position++]; break;\n\t\t\t// little endian:\n\t\t\tcase 25: recordId = src[position++] + (src[position++] << 8); break;\n\t\t\tcase 26: recordId = src[position++] + (src[position++] << 8) + (src[position++] << 16); break;\n\t\t\tcase 27: recordId = src[position++] + (src[position++] << 8) + (src[position++] << 16) + (src[position++] << 24); break;\n\t\t}\n\t}\n\tlet structure = unpackr.typedStructs && unpackr.typedStructs[recordId];\n\tif (!structure) {\n\t\t// copy src buffer because getStructures will override it\n\t\tsrc = Uint8Array.prototype.slice.call(src, position, srcEnd);\n\t\tsrcEnd -= position;\n\t\tposition = 0;\n\t\tif (!unpackr.getStructures)\n\t\t\tthrow new Error(`Reference to shared structure ${recordId} without getStructures method`);\n\t\tunpackr._mergeStructures(unpackr.getStructures());\n\t\tif (!unpackr.typedStructs)\n\t\t\tthrow new Error('Could not find any shared typed structures');\n\t\tunpackr.lastTypedStructuresLength = unpackr.typedStructs.length;\n\t\tstructure = unpackr.typedStructs[recordId];\n\t\tif (!structure)\n\t\t\tthrow new Error('Could not find typed structure ' + recordId);\n\t}\n\tvar construct = structure.construct;\n\tvar fullConstruct = structure.fullConstruct;\n\tif (!construct) {\n\t\tconstruct = structure.construct = function LazyObject() {\n\t\t}\n\t\tfullConstruct = structure.fullConstruct = function LoadedObject() {\n\t\t}\n\t\tfullConstruct.prototype = unpackr.structPrototype ?? {};\n\t\tvar prototype = construct.prototype = unpackr.structPrototype ? Object.create(unpackr.structPrototype) : {};\n\t\tlet properties = [];\n\t\tlet currentOffset = 0;\n\t\tlet lastRefProperty;\n\t\tfor (let i = 0, l = structure.length; i < l; i++) {\n\t\t\tlet definition = structure[i];\n\t\t\tlet [ type, size, key, enumerationOffset ] = definition;\n\t\t\tif (key === '__proto__')\n\t\t\t\tkey = '__proto_';\n\t\t\tlet property = {\n\t\t\t\tkey,\n\t\t\t\toffset: currentOffset,\n\t\t\t}\n\t\t\tif (enumerationOffset)\n\t\t\t\tproperties.splice(i + enumerationOffset, 0, property);\n\t\t\telse\n\t\t\t\tproperties.push(property);\n\t\t\tlet getRef;\n\t\t\tswitch(size) { // TODO: Move into a separate function\n\t\t\t\tcase 0: getRef = () => 0; break;\n\t\t\t\tcase 1:\n\t\t\t\t\tgetRef = (source, position) => {\n\t\t\t\t\t\tlet ref = source.bytes[position + property.offset];\n\t\t\t\t\t\treturn ref >= 0xf6 ? toConstant(ref) : ref;\n\t\t\t\t\t};\n\t\t\t\t\tbreak;\n\t\t\t\tcase 2:\n\t\t\t\t\tgetRef = (source, position) => {\n\t\t\t\t\t\tlet src = source.bytes;\n\t\t\t\t\t\tlet dataView = src.dataView || (src.dataView = new DataView(src.buffer, src.byteOffset, src.byteLength));\n\t\t\t\t\t\tlet ref = dataView.getUint16(position + property.offset, true);\n\t\t\t\t\t\treturn ref >= 0xff00 ? toConstant(ref & 0xff) : ref;\n\t\t\t\t\t};\n\t\t\t\t\tbreak;\n\t\t\t\tcase 4:\n\t\t\t\t\tgetRef = (source, position) => {\n\t\t\t\t\t\tlet src = source.bytes;\n\t\t\t\t\t\tlet dataView = src.dataView || (src.dataView = new DataView(src.buffer, src.byteOffset, src.byteLength));\n\t\t\t\t\t\tlet ref = dataView.getUint32(position + property.offset, true);\n\t\t\t\t\t\treturn ref >= 0xffffff00 ? toConstant(ref & 0xff) : ref;\n\t\t\t\t\t};\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t\tproperty.getRef = getRef;\n\t\t\tcurrentOffset += size;\n\t\t\tlet get;\n\t\t\tswitch(type) {\n\t\t\t\tcase ASCII:\n\t\t\t\t\tif (lastRefProperty && !lastRefProperty.next)\n\t\t\t\t\t\tlastRefProperty.next = property;\n\t\t\t\t\tlastRefProperty = property;\n\t\t\t\t\tproperty.multiGetCount = 0;\n\t\t\t\t\tget = function(source) {\n\t\t\t\t\t\tlet src = source.bytes;\n\t\t\t\t\t\tlet position = source.position;\n\t\t\t\t\t\tlet refStart = currentOffset + position;\n\t\t\t\t\t\tlet ref = getRef(source, position);\n\t\t\t\t\t\tif (typeof ref !== 'number') return ref;\n\n\t\t\t\t\t\tlet end, next = property.next;\n\t\t\t\t\t\twhile(next) {\n\t\t\t\t\t\t\tend = next.getRef(source, position);\n\t\t\t\t\t\t\tif (typeof end === 'number')\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\telse\n\t\t\t\t\t\t\t\tend = null;\n\t\t\t\t\t\t\tnext = next.next;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (end == null)\n\t\t\t\t\t\t\tend = source.bytesEnd - refStart;\n\t\t\t\t\t\tif (source.srcString) {\n\t\t\t\t\t\t\treturn source.srcString.slice(ref, end);\n\t\t\t\t\t\t}\n\t\t\t\t\t\t/*if (property.multiGetCount > 0) {\n\t\t\t\t\t\t\tlet asciiEnd;\n\t\t\t\t\t\t\tnext = firstRefProperty;\n\t\t\t\t\t\t\tlet dataView = src.dataView || (src.dataView = new DataView(src.buffer, src.byteOffset, src.byteLength));\n\t\t\t\t\t\t\tdo {\n\t\t\t\t\t\t\t\tasciiEnd = dataView.getUint16(source.position + next.offset, true);\n\t\t\t\t\t\t\t\tif (asciiEnd < 0xff00)\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\telse\n\t\t\t\t\t\t\t\t\tasciiEnd = null;\n\t\t\t\t\t\t\t} while((next = next.next));\n\t\t\t\t\t\t\tif (asciiEnd == null)\n\t\t\t\t\t\t\t\tasciiEnd = source.bytesEnd - refStart\n\t\t\t\t\t\t\tsource.srcString = src.toString('latin1', refStart, refStart + asciiEnd);\n\t\t\t\t\t\t\treturn source.srcString.slice(ref, end);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (source.prevStringGet) {\n\t\t\t\t\t\t\tsource.prevStringGet.multiGetCount += 2;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tsource.prevStringGet = property;\n\t\t\t\t\t\t\tproperty.multiGetCount--;\n\t\t\t\t\t\t}*/\n\t\t\t\t\t\treturn readString(src, ref + refStart, end - ref);\n\t\t\t\t\t\t//return src.toString('latin1', ref + refStart, end + refStart);\n\t\t\t\t\t};\n\t\t\t\t\tbreak;\n\t\t\t\tcase UTF8: case OBJECT_DATA:\n\t\t\t\t\tif (lastRefProperty && !lastRefProperty.next)\n\t\t\t\t\t\tlastRefProperty.next = property;\n\t\t\t\t\tlastRefProperty = property;\n\t\t\t\t\tget = function(source) {\n\t\t\t\t\t\tlet position = source.position;\n\t\t\t\t\t\tlet refStart = currentOffset + position;\n\t\t\t\t\t\tlet ref = getRef(source, position);\n\t\t\t\t\t\tif (typeof ref !== 'number') return ref;\n\t\t\t\t\t\tlet src = source.bytes;\n\t\t\t\t\t\tlet end, next = property.next;\n\t\t\t\t\t\twhile(next) {\n\t\t\t\t\t\t\tend = next.getRef(source, position);\n\t\t\t\t\t\t\tif (typeof end === 'number')\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\telse\n\t\t\t\t\t\t\t\tend = null;\n\t\t\t\t\t\t\tnext = next.next;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (end == null)\n\t\t\t\t\t\t\tend = source.bytesEnd - refStart;\n\t\t\t\t\t\tif (type === UTF8) {\n\t\t\t\t\t\t\treturn src.toString('utf8', ref + refStart, end + refStart);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tcurrentSource = source;\n\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\treturn unpackr.unpack(src, { start: ref + refStart, end: end + refStart });\n\t\t\t\t\t\t\t} finally {\n\t\t\t\t\t\t\t\tcurrentSource = null;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t};\n\t\t\t\t\tbreak;\n\t\t\t\tcase NUMBER:\n\t\t\t\t\tswitch(size) {\n\t\t\t\t\t\tcase 4:\n\t\t\t\t\t\t\tget = function (source) {\n\t\t\t\t\t\t\t\tlet src = source.bytes;\n\t\t\t\t\t\t\t\tlet dataView = src.dataView || (src.dataView = new DataView(src.buffer, src.byteOffset, src.byteLength));\n\t\t\t\t\t\t\t\tlet position = source.position + property.offset;\n\t\t\t\t\t\t\t\tlet value = dataView.getInt32(position, true)\n\t\t\t\t\t\t\t\tif (value < 0x20000000) {\n\t\t\t\t\t\t\t\t\tif (value > -0x1f000000)\n\t\t\t\t\t\t\t\t\t\treturn value;\n\t\t\t\t\t\t\t\t\tif (value > -0x20000000)\n\t\t\t\t\t\t\t\t\t\treturn toConstant(value & 0xff);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tlet fValue = dataView.getFloat32(position, true);\n\t\t\t\t\t\t\t\t// this does rounding of numbers that were encoded in 32-bit float to nearest significant decimal digit that could be preserved\n\t\t\t\t\t\t\t\tlet multiplier = mult10[((src[position + 3] & 0x7f) << 1) | (src[position + 2] >> 7)]\n\t\t\t\t\t\t\t\treturn ((multiplier * fValue + (fValue > 0 ? 0.5 : -0.5)) >> 0) / multiplier;\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 8:\n\t\t\t\t\t\t\tget = function (source) {\n\t\t\t\t\t\t\t\tlet src = source.bytes;\n\t\t\t\t\t\t\t\tlet dataView = src.dataView || (src.dataView = new DataView(src.buffer, src.byteOffset, src.byteLength));\n\t\t\t\t\t\t\t\tlet value = dataView.getFloat64(source.position + property.offset, true);\n\t\t\t\t\t\t\t\tif (isNaN(value)) {\n\t\t\t\t\t\t\t\t\tlet byte = src[source.position + property.offset];\n\t\t\t\t\t\t\t\t\tif (byte >= 0xf6)\n\t\t\t\t\t\t\t\t\t\treturn toConstant(byte);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\treturn value;\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 1:\n\t\t\t\t\t\t\tget = function (source) {\n\t\t\t\t\t\t\t\tlet src = source.bytes;\n\t\t\t\t\t\t\t\tlet value = src[source.position + property.offset];\n\t\t\t\t\t\t\t\treturn value < 0xf6 ? value : toConstant(value);\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\t\t\t\tcase DATE:\n\t\t\t\t\tget = function (source) {\n\t\t\t\t\t\tlet src = source.bytes;\n\t\t\t\t\t\tlet dataView = src.dataView || (src.dataView = new DataView(src.buffer, src.byteOffset, src.byteLength));\n\t\t\t\t\t\treturn new Date(dataView.getFloat64(source.position + property.offset, true));\n\t\t\t\t\t};\n\t\t\t\t\tbreak;\n\n\t\t\t}\n\t\t\tproperty.get = get;\n\t\t}\n\t\t// TODO: load the srcString for faster string decoding on toJSON\n\t\tif (evalSupported) {\n\t\t\tlet objectLiteralProperties = [];\n\t\t\tlet args = [];\n\t\t\tlet i = 0;\n\t\t\tlet hasInheritedProperties;\n\t\t\tfor (let property of properties) { // assign in enumeration order\n\t\t\t\tif (unpackr.alwaysLazyProperty && unpackr.alwaysLazyProperty(property.key)) {\n\t\t\t\t\t// these properties are not eagerly evaluated and this can be used for creating properties\n\t\t\t\t\t// that are not serialized as JSON\n\t\t\t\t\thasInheritedProperties = true;\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tObject.defineProperty(prototype, property.key, { get: withSource(property.get), enumerable: true });\n\t\t\t\tlet valueFunction = 'v' + i++;\n\t\t\t\targs.push(valueFunction);\n\t\t\t\tobjectLiteralProperties.push('o[' + JSON.stringify(property.key) + ']=' + valueFunction + '(s)');\n\t\t\t}\n\t\t\tif (hasInheritedProperties) {\n\t\t\t\tobjectLiteralProperties.push('__proto__:this');\n\t\t\t}\n\t\t\tlet toObject = (new Function(...args, 'var c=this;return function(s){var o=new c();' + objectLiteralProperties.join(';') + ';return o;}')).apply(fullConstruct, properties.map(prop => prop.get));\n\t\t\tObject.defineProperty(prototype, 'toJSON', {\n\t\t\t\tvalue(omitUnderscoredProperties) {\n\t\t\t\t\treturn toObject.call(this, this[sourceSymbol]);\n\t\t\t\t}\n\t\t\t});\n\t\t} else {\n\t\t\tObject.defineProperty(prototype, 'toJSON', {\n\t\t\t\tvalue(omitUnderscoredProperties) {\n\t\t\t\t\t// return an enumerable object with own properties to JSON stringify\n\t\t\t\t\tlet resolved = {};\n\t\t\t\t\tfor (let i = 0, l = properties.length; i < l; i++) {\n\t\t\t\t\t\t// TODO: check alwaysLazyProperty\n\t\t\t\t\t\tlet key = properties[i].key;\n\n\t\t\t\t\t\tresolved[key] = this[key];\n\t\t\t\t\t}\n\t\t\t\t\treturn resolved;\n\t\t\t\t},\n\t\t\t\t// not enumerable or anything\n\t\t\t});\n\t\t}\n\t}\n\tvar instance = new construct();\n\tinstance[sourceSymbol] = {\n\t\tbytes: src,\n\t\tposition,\n\t\tsrcString: '',\n\t\tbytesEnd: srcEnd\n\t}\n\treturn instance;\n}\nfunction toConstant(code) {\n\tswitch(code) {\n\t\tcase 0xf6: return null;\n\t\tcase 0xf7: return undefined;\n\t\tcase 0xf8: return false;\n\t\tcase 0xf9: return true;\n\t}\n\tthrow new Error('Unknown constant');\n}\nfunction withSource(get) {\n\treturn function() {\n\t\treturn get(this[sourceSymbol]);\n\t}\n}\n\nfunction saveState() {\n\tif (currentSource) {\n\t\tcurrentSource.bytes = Uint8Array.prototype.slice.call(currentSource.bytes, currentSource.position, currentSource.bytesEnd);\n\t\tcurrentSource.position = 0;\n\t\tcurrentSource.bytesEnd = currentSource.bytes.length;\n\t}\n}\nfunction prepareStructures(structures, packr) {\n\tif (packr.typedStructs) {\n\t\tlet structMap = new Map();\n\t\tstructMap.set('named', structures);\n\t\tstructMap.set('typed', packr.typedStructs);\n\t\tstructures = structMap;\n\t}\n\tlet lastTypedStructuresLength = packr.lastTypedStructuresLength || 0;\n\tstructures.isCompatible = existing => {\n\t\tlet compatible = true;\n\t\tif (existing instanceof Map) {\n\t\t\tlet named = existing.get('named') || [];\n\t\t\tif (named.length !== (packr.lastNamedStructuresLength || 0))\n\t\t\t\tcompatible = false;\n\t\t\tlet typed = existing.get('typed') || [];\n\t\t\tif (typed.length !== lastTypedStructuresLength)\n\t\t\t\tcompatible = false;\n\t\t} else if (existing instanceof Array || Array.isArray(existing)) {\n\t\t\tif (existing.length !== (packr.lastNamedStructuresLength || 0))\n\t\t\t\tcompatible = false;\n\t\t}\n\t\tif (!compatible)\n\t\t\tpackr._mergeStructures(existing);\n\t\treturn compatible;\n\t};\n\tpackr.lastTypedStructuresLength = packr.typedStructs && packr.typedStructs.length;\n\treturn structures;\n}\n\nsetReadStruct(readStruct, onLoadedStructures, saveState);\n\n", "import { Transform } from 'stream'\nimport { Packr } from './pack.js'\nimport { Unpackr } from './unpack.js'\nvar DEFAULT_OPTIONS = {objectMode: true}\n\nexport class PackrStream extends Transform {\n\tconstructor(options) {\n\t\tif (!options)\n\t\t\toptions = {}\n\t\toptions.writableObjectMode = true\n\t\tsuper(options)\n\t\toptions.sequential = true\n\t\tthis.packr = options.packr || new Packr(options)\n\t}\n\t_transform(value, encoding, callback) {\n\t\tthis.push(this.packr.pack(value))\n\t\tcallback()\n\t}\n}\n\nexport class UnpackrStream extends Transform {\n\tconstructor(options) {\n\t\tif (!options)\n\t\t\toptions = {}\n\t\toptions.objectMode = true\n\t\tsuper(options)\n\t\toptions.structures = []\n\t\tthis.unpackr = options.unpackr || new Unpackr(options)\n\t}\n\t_transform(chunk, encoding, callback) {\n\t\tif (this.incompleteBuffer) {\n\t\t\tchunk = Buffer.concat([this.incompleteBuffer, chunk])\n\t\t\tthis.incompleteBuffer = null\n\t\t}\n\t\tlet values\n\t\ttry {\n\t\t\tvalues = this.unpackr.unpackMultiple(chunk)\n\t\t} catch(error) {\n\t\t\tif (error.incomplete) {\n\t\t\t\tthis.incompleteBuffer = chunk.slice(error.lastPosition)\n\t\t\t\tvalues = error.values\n\t\t\t}\n\t\t\telse\n\t\t\t\tthrow error\n\t\t} finally {\n\t\t\tfor (let value of values || []) {\n\t\t\t\tif (value === null)\n\t\t\t\t\tvalue = this.getNullValue()\n\t\t\t\tthis.push(value)\n\t\t\t}\n\t\t}\n\t\tif (callback) callback()\n\t}\n\tgetNullValue() {\n\t\treturn Symbol.for(null)\n\t}\n}\n", "import { Packr } from './pack.js'\nimport { Unpackr } from './unpack.js'\n\n/**\n * Given an Iterable first argument, returns an Iterable where each value is packed as a Buffer\n * If the argument is only Async Iterable, the return value will be an Async Iterable.\n * @param {Iterable|Iterator|AsyncIterable|AsyncIterator} objectIterator - iterable source, like a Readable object stream, an array, Set, or custom object\n * @param {options} [options] - msgpackr pack options\n * @returns {IterableIterator|Promise.<AsyncIterableIterator>}\n */\nexport function packIter (objectIterator, options = {}) {\n  if (!objectIterator || typeof objectIterator !== 'object') {\n    throw new Error('first argument must be an Iterable, Async Iterable, or a Promise for an Async Iterable')\n  } else if (typeof objectIterator[Symbol.iterator] === 'function') {\n    return packIterSync(objectIterator, options)\n  } else if (typeof objectIterator.then === 'function' || typeof objectIterator[Symbol.asyncIterator] === 'function') {\n    return packIterAsync(objectIterator, options)\n  } else {\n    throw new Error('first argument must be an Iterable, Async Iterable, Iterator, Async Iterator, or a Promise')\n  }\n}\n\nfunction * packIterSync (objectIterator, options) {\n  const packr = new Packr(options)\n  for (const value of objectIterator) {\n    yield packr.pack(value)\n  }\n}\n\nasync function * packIterAsync (objectIterator, options) {\n  const packr = new Packr(options)\n  for await (const value of objectIterator) {\n    yield packr.pack(value)\n  }\n}\n\n/**\n * Given an Iterable/Iterator input which yields buffers, returns an IterableIterator which yields sync decoded objects\n * Or, given an Async Iterable/Iterator which yields promises resolving in buffers, returns an AsyncIterableIterator.\n * @param {Iterable|Iterator|AsyncIterable|AsyncIterableIterator} bufferIterator\n * @param {object} [options] - unpackr options\n * @returns {IterableIterator|Promise.<AsyncIterableIterator}\n */\nexport function unpackIter (bufferIterator, options = {}) {\n  if (!bufferIterator || typeof bufferIterator !== 'object') {\n    throw new Error('first argument must be an Iterable, Async Iterable, Iterator, Async Iterator, or a promise')\n  }\n\n  const unpackr = new Unpackr(options)\n  let incomplete\n  const parser = (chunk) => {\n    let yields\n    // if there's incomplete data from previous chunk, concatinate and try again\n    if (incomplete) {\n      chunk = Buffer.concat([incomplete, chunk])\n      incomplete = undefined\n    }\n\n    try {\n      yields = unpackr.unpackMultiple(chunk)\n    } catch (err) {\n      if (err.incomplete) {\n        incomplete = chunk.slice(err.lastPosition)\n        yields = err.values\n      } else {\n        throw err\n      }\n    }\n    return yields\n  }\n\n  if (typeof bufferIterator[Symbol.iterator] === 'function') {\n    return (function * iter () {\n      for (const value of bufferIterator) {\n        yield * parser(value)\n      }\n    })()\n  } else if (typeof bufferIterator[Symbol.asyncIterator] === 'function') {\n    return (async function * iter () {\n      for await (const value of bufferIterator) {\n        yield * parser(value)\n      }\n    })()\n  }\n}\nexport const decodeIter = unpackIter\nexport const encodeIter = packIter", "export { Packr, Encoder, addExtension, pack, encode, NEVER, ALWAYS, DECIMAL_ROUND, DECIMAL_FIT } from './pack.js'\nexport { Unpackr, Decoder, C1, unpack, unpackMultiple, decode, FLOAT32_OPTIONS, clearSource, roundFloat32, isNativeAccelerationEnabled } from './unpack.js'\nimport './struct.js'\nexport { PackrStream, UnpackrStream, PackrStream as EncoderStream, UnpackrStream as DecoderStream } from './stream.js'\nexport { decodeIter, encodeIter } from './iterators.js'\nexport const useRecords = false\nexport const mapsAsObjects = true\nimport { setExtractor } from './unpack.js'\nimport { createRequire } from 'module'\n\nconst nativeAccelerationDisabled = process.env.MSGPACKR_NATIVE_ACCELERATION_DISABLED !== undefined && process.env.MSGPACKR_NATIVE_ACCELERATION_DISABLED.toLowerCase() === 'true';\n\nif (!nativeAccelerationDisabled) {\n\tlet extractor\n\ttry {\n\t\tif (typeof require == 'function')\n\t\t\textractor = require('msgpackr-extract')\n\t\telse\n\t\t\textractor = createRequire(import.meta.url)('msgpackr-extract')\n\t\tif (extractor)\n\t\t\tsetExtractor(extractor.extractStrings)\n\t} catch (error) {\n\t\t// native module is optional\n\t}\n}"], "names": ["position", "bundledStrings", "readStruct", "onLoadedStructures", "saveState", "isNativeAccelerationEnabled", "addExtension", "textEncoder", "hasNodeBuffer", "prepareStructures", "unpackAddExtension", "Transform", "createRequire"], "mappings": ";;;;;AAAA,IAAI,QAAO;AACX,IAAI;AACJ,CAAC,OAAO,GAAG,IAAI,WAAW,GAAE;AAC5B,CAAC,CAAC,MAAM,KAAK,EAAE,EAAE;AACjB,IAAI,IAAG;AACP,IAAI,OAAM;AACV,IAAIA,UAAQ,GAAG,EAAC;AAEhB,MAAM,WAAW,GAAG,GAAE;AACtB,IAAI,OAAO,GAAG,YAAW;AACzB,IAAI,cAAc,GAAG,EAAC;AACtB,IAAI,cAAc,GAAG,GAAE;AACvB,IAAI,kBAAiB;AACrB,IAAI,UAAS;AACb,IAAI,cAAc,GAAG,EAAC;AACtB,IAAI,YAAY,GAAG,EAAC;AACpB,IAAIC,iBAAc;AAClB,IAAI,aAAY;AAChB,IAAI,iBAAiB,GAAG,GAAE;AAC1B,IAAI,SAAQ;AACZ,IAAI,cAAc,GAAG;AACrB,CAAC,UAAU,EAAE,KAAK;AAClB,CAAC,aAAa,EAAE,IAAI;AACpB,EAAC;AACM,MAAM,MAAM,CAAC,EAAE;AACV,MAAC,EAAE,GAAG,IAAI,MAAM,GAAE;AAC9B,EAAE,CAAC,IAAI,GAAG,mBAAkB;AAC5B,IAAI,cAAc,GAAG,MAAK;AAC1B,IAAI,yBAAyB,GAAG,EAAC;AACjC,IAAIC,YAAU,EAAEC,oBAAkB,EAAE,YAAW;AAE/C;AACA,IAAI;AACJ,CAAC,IAAI,QAAQ,CAAC,EAAE,EAAC;AACjB,CAAC,CAAC,MAAM,KAAK,EAAE;AACf;AACA,CAAC,yBAAyB,GAAG,SAAQ;AACrC,CAAC;AACD;AACO,MAAM,OAAO,CAAC;AACrB,CAAC,WAAW,CAAC,OAAO,EAAE;AACtB,EAAE,IAAI,OAAO,EAAE;AACf,GAAG,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,IAAI,OAAO,CAAC,aAAa,KAAK,SAAS;AAC1E,IAAI,OAAO,CAAC,aAAa,GAAG,KAAI;AAChC,GAAG,IAAI,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,EAAE;AACxD,IAAI,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;AAC3B,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,UAAU,IAAI,KAAK,EAAE;AAC5D,KAAK,OAAO,CAAC,UAAU,GAAG,GAAE;AAC5B,KAAK,IAAI,CAAC,OAAO,CAAC,mBAAmB;AACrC,MAAM,OAAO,CAAC,mBAAmB,GAAG,EAAC;AACrC,KAAK;AACL,IAAI;AACJ,GAAG,IAAI,OAAO,CAAC,UAAU;AACzB,IAAI,OAAO,CAAC,UAAU,CAAC,YAAY,GAAG,OAAO,CAAC,UAAU,CAAC,OAAM;AAC/D,QAAQ,IAAI,OAAO,CAAC,aAAa,EAAE;AACnC,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE,EAAE,aAAa,GAAG,KAAI;AAClD,IAAI,OAAO,CAAC,UAAU,CAAC,YAAY,GAAG,EAAC;AACvC,IAAI;AACJ,GAAG,IAAI,OAAO,CAAC,aAAa,EAAE;AAC9B,IAAI,OAAO,CAAC,WAAW,GAAG,SAAQ;AAClC,IAAI;AACJ,GAAG;AACH,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,EAAC;AAC9B,EAAE;AACF,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,EAAE;AACzB,EAAE,IAAI,GAAG,EAAE;AACX;AACA,GAAG,OAAOC,WAAS,CAAC,MAAM;AAC1B,IAAI,WAAW,GAAE;AACjB,IAAI,OAAO,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,MAAM,EAAE,OAAO,CAAC;AAC/G,IAAI,CAAC;AACL,GAAG;AACH,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,WAAW,KAAK,WAAW;AAC1D,GAAG,MAAM,GAAG,OAAO,MAAM,KAAK,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;AACzF,EAAE,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;AACnC,GAAG,MAAM,GAAG,OAAO,CAAC,GAAG,IAAI,MAAM,CAAC,OAAM;AACxC,GAAGJ,UAAQ,GAAG,OAAO,CAAC,KAAK,IAAI,EAAC;AAChC,GAAG,MAAM;AACT,GAAGA,UAAQ,GAAG,EAAC;AACf,GAAG,MAAM,GAAG,OAAO,GAAG,CAAC,CAAC,GAAG,OAAO,GAAG,MAAM,CAAC,OAAM;AAClD,GAAG;AACH,EAAE,cAAc,GAAG,EAAC;AACpB,EAAE,YAAY,GAAG,EAAC;AAClB,EAAE,SAAS,GAAG,KAAI;AAClB,EAAE,OAAO,GAAG,YAAW;AACvB,EAAEC,gBAAc,GAAG,KAAI;AACvB,EAAE,GAAG,GAAG,OAAM;AACd;AACA;AACA;AACA,EAAE,IAAI;AACN,GAAG,QAAQ,GAAG,MAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC,EAAC;AACtH,GAAG,CAAC,MAAM,KAAK,EAAE;AACjB;AACA,GAAG,GAAG,GAAG,KAAI;AACb,GAAG,IAAI,MAAM,YAAY,UAAU;AACnC,IAAI,MAAM,KAAK;AACf,GAAG,MAAM,IAAI,KAAK,CAAC,kDAAkD,IAAI,CAAC,MAAM,IAAI,OAAO,MAAM,IAAI,QAAQ,IAAI,MAAM,CAAC,WAAW,CAAC,IAAI,GAAG,OAAO,MAAM,CAAC,CAAC;AAC1J,GAAG;AACH,EAAE,IAAI,IAAI,YAAY,OAAO,EAAE;AAC/B,GAAG,cAAc,GAAG,KAAI;AACxB,GAAG,IAAI,IAAI,CAAC,UAAU,EAAE;AACxB,IAAI,iBAAiB,GAAG,IAAI,CAAC,WAAU;AACvC,IAAI,OAAO,WAAW,CAAC,OAAO,CAAC;AAC/B,IAAI,MAAM,IAAI,CAAC,iBAAiB,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;AAClE,IAAI,iBAAiB,GAAG,GAAE;AAC1B,IAAI;AACJ,GAAG,MAAM;AACT,GAAG,cAAc,GAAG,eAAc;AAClC,GAAG,IAAI,CAAC,iBAAiB,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC;AACzD,IAAI,iBAAiB,GAAG,GAAE;AAC1B,GAAG;AACH,EAAE,OAAO,WAAW,CAAC,OAAO,CAAC;AAC7B,EAAE;AACF,CAAC,cAAc,CAAC,MAAM,EAAE,OAAO,EAAE;AACjC,EAAE,IAAI,MAAM,EAAE,YAAY,GAAG,EAAC;AAC9B,EAAE,IAAI;AACN,GAAG,cAAc,GAAG,KAAI;AACxB,GAAG,IAAI,IAAI,GAAG,MAAM,CAAC,OAAM;AAC3B,GAAG,IAAI,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,cAAc,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,EAAC;AACrF,GAAG,IAAI,OAAO,EAAE;AAChB,IAAI,IAAI,OAAO,CAAC,KAAK,EAAE,YAAY,EAAED,UAAQ,CAAC,KAAK,KAAK,EAAE,OAAO;AACjE,IAAI,MAAMA,UAAQ,GAAG,IAAI,EAAE;AAC3B,KAAK,YAAY,GAAGA,WAAQ;AAC5B,KAAK,IAAI,OAAO,CAAC,WAAW,EAAE,EAAE,YAAY,EAAEA,UAAQ,CAAC,KAAK,KAAK,EAAE;AACnE,MAAM,MAAM;AACZ,MAAM;AACN,KAAK;AACL,IAAI;AACJ,QAAQ;AACR,IAAI,MAAM,GAAG,EAAE,KAAK,GAAE;AACtB,IAAI,MAAMA,UAAQ,GAAG,IAAI,EAAE;AAC3B,KAAK,YAAY,GAAGA,WAAQ;AAC5B,KAAK,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,EAAC;AAC/B,KAAK;AACL,IAAI,OAAO,MAAM;AACjB,IAAI;AACJ,GAAG,CAAC,MAAM,KAAK,EAAE;AACjB,GAAG,KAAK,CAAC,YAAY,GAAG,aAAY;AACpC,GAAG,KAAK,CAAC,MAAM,GAAG,OAAM;AACxB,GAAG,MAAM,KAAK;AACd,GAAG,SAAS;AACZ,GAAG,cAAc,GAAG,MAAK;AACzB,GAAG,WAAW,GAAE;AAChB,GAAG;AACH,EAAE;AACF,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,kBAAkB,EAAE;AACxD,EAAE,IAAIG,oBAAkB;AACxB,GAAG,gBAAgB,GAAGA,oBAAkB,CAAC,IAAI,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;AACtE,EAAE,gBAAgB,GAAG,gBAAgB,IAAI,GAAE;AAC3C,EAAE,IAAI,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC;AACvC,GAAG,gBAAgB,GAAG,gBAAgB,CAAC,GAAG,CAAC,SAAS,IAAI,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAAC;AAC3E,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC3D,GAAG,IAAI,SAAS,GAAG,gBAAgB,CAAC,CAAC,EAAC;AACtC,GAAG,IAAI,SAAS,EAAE;AAClB,IAAI,SAAS,CAAC,QAAQ,GAAG,KAAI;AAC7B,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,KAAK,SAAS,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAC;AACvC,IAAI;AACJ,GAAG;AACH,EAAE,gBAAgB,CAAC,YAAY,GAAG,gBAAgB,CAAC,OAAM;AACzD,EAAE,KAAK,IAAI,EAAE,IAAI,kBAAkB,IAAI,EAAE,EAAE;AAC3C,GAAG,IAAI,EAAE,IAAI,CAAC,EAAE;AAChB,IAAI,IAAI,SAAS,GAAG,gBAAgB,CAAC,EAAE,EAAC;AACxC,IAAI,IAAI,QAAQ,GAAG,kBAAkB,CAAC,EAAE,EAAC;AACzC,IAAI,IAAI,QAAQ,EAAE;AAClB,KAAK,IAAI,SAAS;AAClB,MAAM,CAAC,gBAAgB,CAAC,iBAAiB,KAAK,gBAAgB,CAAC,iBAAiB,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,UAAS;AACvG,KAAK,gBAAgB,CAAC,EAAE,CAAC,GAAG,SAAQ;AACpC,KAAK;AACL,IAAI;AACJ,GAAG;AACH,EAAE,OAAO,IAAI,CAAC,UAAU,GAAG,gBAAgB;AAC3C,EAAE;AACF,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,EAAE;AACzB,EAAE,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC;AACrC,EAAE;AACF,CAAC;AAIM,SAAS,WAAW,CAAC,OAAO,EAAE;AACrC,CAAC,IAAI;AACL,EAAE,IAAI,CAAC,cAAc,CAAC,OAAO,IAAI,CAAC,cAAc,EAAE;AAClD,GAAG,IAAI,YAAY,GAAG,iBAAiB,CAAC,YAAY,IAAI,EAAC;AACzD,GAAG,IAAI,YAAY,GAAG,iBAAiB,CAAC,MAAM;AAC9C,IAAI,iBAAiB,CAAC,MAAM,GAAG,aAAY;AAC3C,GAAG;AACH,EAAE,IAAI,OAAM;AACZ,EAAE,IAAI,cAAc,CAAC,qBAAqB,IAAI,GAAG,CAACH,UAAQ,CAAC,GAAG,IAAI,IAAI,GAAG,CAACA,UAAQ,CAAC,IAAI,IAAI,IAAIE,YAAU,EAAE;AAC3G,GAAG,MAAM,GAAGA,YAAU,CAAC,GAAG,EAAEF,UAAQ,EAAE,MAAM,EAAE,cAAc,EAAC;AAC7D,GAAG,GAAG,GAAG,KAAI;AACb,GAAG,IAAI,EAAE,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,MAAM;AAC3C,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM,GAAE;AAC5B,GAAGA,UAAQ,GAAG,OAAM;AACpB,GAAG;AACH,GAAG,MAAM,GAAG,IAAI,GAAE;AAClB,EAAE,IAAIC,gBAAc,EAAE;AACtB,GAAGD,UAAQ,GAAGC,gBAAc,CAAC,mBAAkB;AAC/C,GAAGA,gBAAc,GAAG,KAAI;AACxB,GAAG;AACH,EAAE,IAAI,cAAc;AACpB;AACA;AACA,GAAG,iBAAiB,CAAC,iBAAiB,GAAG,KAAI;AAC7C;AACA,EAAE,IAAID,UAAQ,IAAI,MAAM,EAAE;AAC1B;AACA,GAAG,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,iBAAiB;AAC/D,IAAI,iBAAiB,GAAE;AACvB,GAAG,iBAAiB,GAAG,KAAI;AAC3B,GAAG,GAAG,GAAG,KAAI;AACb,GAAG,IAAI,YAAY;AACnB,IAAI,YAAY,GAAG,KAAI;AACvB,GAAG,MAAM,IAAIA,UAAQ,GAAG,MAAM,EAAE;AAChC;AACA,GAAG,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC;AACxD,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE;AAC9B,GAAG,IAAI,QAAQ,CAAC;AAChB,GAAG,IAAI;AACP,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,KAAK,KAAK,OAAO,KAAK,KAAK,QAAQ,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,EAAC;AAClH,IAAI,CAAC,MAAM,KAAK,EAAE;AAClB,IAAI,QAAQ,GAAG,2BAA2B,GAAG,KAAK,GAAG,IAAG;AACxD,IAAI;AACJ,GAAG,MAAM,IAAI,KAAK,CAAC,2CAA2C,GAAG,QAAQ,CAAC;AAC1E,GAAG;AACH;AACA,EAAE,OAAO,MAAM;AACf,EAAE,CAAC,MAAM,KAAK,EAAE;AAChB,EAAE,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,iBAAiB;AAC9D,GAAG,iBAAiB,GAAE;AACtB,EAAE,WAAW,GAAE;AACf,EAAE,IAAI,KAAK,YAAY,UAAU,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,0BAA0B,CAAC,IAAIA,UAAQ,GAAG,MAAM,EAAE;AAChH,GAAG,KAAK,CAAC,UAAU,GAAG,KAAI;AAC1B,GAAG;AACH,EAAE,MAAM,KAAK;AACb,EAAE;AACF,CAAC;AACD;AACA,SAAS,iBAAiB,GAAG;AAC7B,CAAC,KAAK,IAAI,EAAE,IAAI,iBAAiB,CAAC,iBAAiB,EAAE;AACrD,EAAE,iBAAiB,CAAC,EAAE,CAAC,GAAG,iBAAiB,CAAC,iBAAiB,CAAC,EAAE,EAAC;AACjE,EAAE;AACF,CAAC,iBAAiB,CAAC,iBAAiB,GAAG,KAAI;AAC3C,CAAC;AACD;AACO,SAAS,IAAI,GAAG;AACvB,CAAC,IAAI,KAAK,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;AAC5B,CAAC,IAAI,KAAK,GAAG,IAAI,EAAE;AACnB,EAAE,IAAI,KAAK,GAAG,IAAI,EAAE;AACpB,GAAG,IAAI,KAAK,GAAG,IAAI;AACnB,IAAI,OAAO,KAAK;AAChB,QAAQ;AACR,IAAI,IAAI,SAAS,GAAG,iBAAiB,CAAC,KAAK,GAAG,IAAI,CAAC;AACnD,KAAK,cAAc,CAAC,aAAa,IAAI,cAAc,EAAE,CAAC,KAAK,GAAG,IAAI,EAAC;AACnE,IAAI,IAAI,SAAS,EAAE;AACnB,KAAK,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;AAC1B,MAAM,SAAS,CAAC,IAAI,GAAG,qBAAqB,CAAC,SAAS,EAAE,KAAK,GAAG,IAAI,EAAC;AACrE,MAAM;AACN,KAAK,OAAO,SAAS,CAAC,IAAI,EAAE;AAC5B,KAAK;AACL,KAAK,OAAO,KAAK;AACjB,IAAI;AACJ,GAAG,MAAM,IAAI,KAAK,GAAG,IAAI,EAAE;AAC3B;AACA,GAAG,KAAK,IAAI,KAAI;AAChB,GAAG,IAAI,cAAc,CAAC,aAAa,EAAE;AACrC,IAAI,IAAI,MAAM,GAAG,GAAE;AACnB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;AACpC,KAAK,IAAI,GAAG,GAAG,OAAO,GAAE;AACxB,KAAK,IAAI,GAAG,KAAK,WAAW;AAC5B,MAAM,GAAG,GAAG,WAAU;AACtB,KAAK,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,GAAE;AACzB,KAAK;AACL,IAAI,OAAO,MAAM;AACjB,IAAI,MAAM;AACV,IAAI,IAAI,GAAG,GAAG,IAAI,GAAG,GAAE;AACvB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;AACpC,KAAK,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE,EAAC;AAC5B,KAAK;AACL,IAAI,OAAO,GAAG;AACd,IAAI;AACJ,GAAG,MAAM;AACT,GAAG,KAAK,IAAI,KAAI;AAChB,GAAG,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC,KAAK,EAAC;AAC/B,GAAG,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;AACnC,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,GAAE;AACrB,IAAI;AACJ,GAAG,IAAI,cAAc,CAAC,UAAU;AAChC,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC;AAC/B,GAAG,OAAO,KAAK;AACf,GAAG;AACH,EAAE,MAAM,IAAI,KAAK,GAAG,IAAI,EAAE;AAC1B;AACA,EAAE,IAAI,MAAM,GAAG,KAAK,GAAG,KAAI;AAC3B,EAAE,IAAI,YAAY,IAAIA,UAAQ,EAAE;AAChC,GAAG,OAAO,SAAS,CAAC,KAAK,CAACA,UAAQ,GAAG,cAAc,EAAE,CAACA,UAAQ,IAAI,MAAM,IAAI,cAAc,CAAC;AAC3F,GAAG;AACH,EAAE,IAAI,YAAY,IAAI,CAAC,IAAI,MAAM,GAAG,GAAG,EAAE;AACzC;AACA,GAAG,IAAI,MAAM,GAAG,MAAM,GAAG,EAAE,GAAG,eAAe,CAAC,MAAM,CAAC,GAAG,cAAc,CAAC,MAAM,EAAC;AAC9E,GAAG,IAAI,MAAM,IAAI,IAAI;AACrB,IAAI,OAAO,MAAM;AACjB,GAAG;AACH,EAAE,OAAO,eAAe,CAAC,MAAM,CAAC;AAChC,EAAE,MAAM;AACR,EAAE,IAAI,MAAK;AACX,EAAE,QAAQ,KAAK;AACf,GAAG,KAAK,IAAI,EAAE,OAAO,IAAI;AACzB,GAAG,KAAK,IAAI;AACZ,IAAI,IAAIC,gBAAc,EAAE;AACxB,KAAK,KAAK,GAAG,IAAI,GAAE;AACnB,KAAK,IAAI,KAAK,GAAG,CAAC;AAClB,MAAM,OAAOA,gBAAc,CAAC,CAAC,CAAC,CAAC,KAAK,CAACA,gBAAc,CAAC,SAAS,EAAEA,gBAAc,CAAC,SAAS,IAAI,KAAK,CAAC;AACjG;AACA,MAAM,OAAOA,gBAAc,CAAC,CAAC,CAAC,CAAC,KAAK,CAACA,gBAAc,CAAC,SAAS,EAAEA,gBAAc,CAAC,SAAS,IAAI,KAAK,CAAC;AACjG,KAAK;AACL,IAAI,OAAO,EAAE,CAAC;AACd,GAAG,KAAK,IAAI,EAAE,OAAO,KAAK;AAC1B,GAAG,KAAK,IAAI,EAAE,OAAO,IAAI;AACzB,GAAG,KAAK,IAAI;AACZ;AACA,IAAI,KAAK,GAAG,GAAG,CAACD,UAAQ,EAAE,EAAC;AAC3B,IAAI,IAAI,KAAK,KAAK,SAAS;AAC3B,KAAK,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC;AAChD,IAAI,OAAO,OAAO,CAAC,KAAK,CAAC;AACzB,GAAG,KAAK,IAAI;AACZ;AACA,IAAI,KAAK,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;AACxC,IAAIA,UAAQ,IAAI,EAAC;AACjB,IAAI,OAAO,OAAO,CAAC,KAAK,CAAC;AACzB,GAAG,KAAK,IAAI;AACZ;AACA,IAAI,KAAK,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;AACxC,IAAIA,UAAQ,IAAI,EAAC;AACjB,IAAI,OAAO,OAAO,CAAC,KAAK,CAAC;AACzB,GAAG,KAAK,IAAI;AACZ;AACA,IAAI,OAAO,OAAO,CAAC,GAAG,CAACA,UAAQ,EAAE,CAAC,CAAC;AACnC,GAAG,KAAK,IAAI;AACZ;AACA,IAAI,KAAK,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;AACxC,IAAIA,UAAQ,IAAI,EAAC;AACjB,IAAI,OAAO,OAAO,CAAC,KAAK,CAAC;AACzB,GAAG,KAAK,IAAI;AACZ;AACA,IAAI,KAAK,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;AACxC,IAAIA,UAAQ,IAAI,EAAC;AACjB,IAAI,OAAO,OAAO,CAAC,KAAK,CAAC;AACzB,GAAG,KAAK,IAAI;AACZ,IAAI,KAAK,GAAG,QAAQ,CAAC,UAAU,CAACA,UAAQ,EAAC;AACzC,IAAI,IAAI,cAAc,CAAC,UAAU,GAAG,CAAC,EAAE;AACvC;AACA,KAAK,IAAI,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAACA,UAAQ,CAAC,GAAG,IAAI,KAAK,CAAC,KAAK,GAAG,CAACA,UAAQ,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAC;AACtF,KAAKA,UAAQ,IAAI,EAAC;AAClB,KAAK,OAAO,CAAC,CAAC,UAAU,GAAG,KAAK,IAAI,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU;AAC/E,KAAK;AACL,IAAIA,UAAQ,IAAI,EAAC;AACjB,IAAI,OAAO,KAAK;AAChB,GAAG,KAAK,IAAI;AACZ,IAAI,KAAK,GAAG,QAAQ,CAAC,UAAU,CAACA,UAAQ,EAAC;AACzC,IAAIA,UAAQ,IAAI,EAAC;AACjB,IAAI,OAAO,KAAK;AAChB;AACA,GAAG,KAAK,IAAI;AACZ,IAAI,OAAO,GAAG,CAACA,UAAQ,EAAE,CAAC;AAC1B,GAAG,KAAK,IAAI;AACZ,IAAI,KAAK,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;AACxC,IAAIA,UAAQ,IAAI,EAAC;AACjB,IAAI,OAAO,KAAK;AAChB,GAAG,KAAK,IAAI;AACZ,IAAI,KAAK,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;AACxC,IAAIA,UAAQ,IAAI,EAAC;AACjB,IAAI,OAAO,KAAK;AAChB,GAAG,KAAK,IAAI;AACZ,IAAI,IAAI,cAAc,CAAC,WAAW,KAAK,QAAQ,EAAE;AACjD,KAAK,KAAK,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,CAAC,GAAG,YAAW;AACvD,KAAK,KAAK,IAAI,QAAQ,CAAC,SAAS,CAACA,UAAQ,GAAG,CAAC,EAAC;AAC9C,KAAK,MAAM,IAAI,cAAc,CAAC,WAAW,KAAK,QAAQ,EAAE;AACxD,KAAK,KAAK,GAAG,QAAQ,CAAC,YAAY,CAACA,UAAQ,CAAC,CAAC,QAAQ,GAAE;AACvD,KAAK,MAAM,IAAI,cAAc,CAAC,WAAW,KAAK,MAAM,EAAE;AACtD,KAAK,KAAK,GAAG,QAAQ,CAAC,YAAY,CAACA,UAAQ,EAAC;AAC5C,KAAK,IAAI,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,EAAC;AAC1D,KAAK;AACL,KAAK,KAAK,GAAG,QAAQ,CAAC,YAAY,CAACA,UAAQ,EAAC;AAC5C,IAAIA,UAAQ,IAAI,EAAC;AACjB,IAAI,OAAO,KAAK;AAChB;AACA;AACA,GAAG,KAAK,IAAI;AACZ,IAAI,OAAO,QAAQ,CAAC,OAAO,CAACA,UAAQ,EAAE,CAAC;AACvC,GAAG,KAAK,IAAI;AACZ,IAAI,KAAK,GAAG,QAAQ,CAAC,QAAQ,CAACA,UAAQ,EAAC;AACvC,IAAIA,UAAQ,IAAI,EAAC;AACjB,IAAI,OAAO,KAAK;AAChB,GAAG,KAAK,IAAI;AACZ,IAAI,KAAK,GAAG,QAAQ,CAAC,QAAQ,CAACA,UAAQ,EAAC;AACvC,IAAIA,UAAQ,IAAI,EAAC;AACjB,IAAI,OAAO,KAAK;AAChB,GAAG,KAAK,IAAI;AACZ,IAAI,IAAI,cAAc,CAAC,WAAW,KAAK,QAAQ,EAAE;AACjD,KAAK,KAAK,GAAG,QAAQ,CAAC,QAAQ,CAACA,UAAQ,CAAC,GAAG,YAAW;AACtD,KAAK,KAAK,IAAI,QAAQ,CAAC,SAAS,CAACA,UAAQ,GAAG,CAAC,EAAC;AAC9C,KAAK,MAAM,IAAI,cAAc,CAAC,WAAW,KAAK,QAAQ,EAAE;AACxD,KAAK,KAAK,GAAG,QAAQ,CAAC,WAAW,CAACA,UAAQ,CAAC,CAAC,QAAQ,GAAE;AACtD,KAAK,MAAM,IAAI,cAAc,CAAC,WAAW,KAAK,MAAM,EAAE;AACtD,KAAK,KAAK,GAAG,QAAQ,CAAC,WAAW,CAACA,UAAQ,EAAC;AAC3C,KAAK,IAAI,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,EAAC;AACzF,KAAK;AACL,KAAK,KAAK,GAAG,QAAQ,CAAC,WAAW,CAACA,UAAQ,EAAC;AAC3C,IAAIA,UAAQ,IAAI,EAAC;AACjB,IAAI,OAAO,KAAK;AAChB;AACA,GAAG,KAAK,IAAI;AACZ;AACA,IAAI,KAAK,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;AAC3B,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE;AACvB,KAAK,OAAO,gBAAgB,CAAC,GAAG,CAACA,UAAQ,EAAE,CAAC,GAAG,IAAI,CAAC;AACpD,KAAK,MAAM;AACX,KAAK,IAAI,SAAS,GAAG,iBAAiB,CAAC,KAAK,EAAC;AAC7C,KAAK,IAAI,SAAS,EAAE;AACpB,MAAM,IAAI,SAAS,CAAC,IAAI,EAAE;AAC1B,OAAOA,UAAQ,GAAE;AACjB,OAAO,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;AACpC,OAAO,MAAM,IAAI,SAAS,CAAC,QAAQ,EAAE;AACrC,OAAOA,UAAQ,GAAE;AACjB,OAAO,OAAO,SAAS,EAAE;AACzB,OAAO;AACP,OAAO,OAAO,SAAS,CAAC,GAAG,CAAC,QAAQ,CAACA,UAAQ,EAAE,EAAEA,UAAQ,CAAC,CAAC;AAC3D,MAAM;AACN,MAAM,MAAM,IAAI,KAAK,CAAC,oBAAoB,GAAG,KAAK,CAAC;AACnD,KAAK;AACL,GAAG,KAAK,IAAI;AACZ;AACA,IAAI,KAAK,GAAG,GAAG,CAACA,UAAQ,EAAC;AACzB,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE;AACvB,KAAKA,UAAQ,GAAE;AACf,KAAK,OAAO,gBAAgB,CAAC,GAAG,CAACA,UAAQ,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,CAACA,UAAQ,EAAE,CAAC,CAAC;AACrE,KAAK;AACL,KAAK,OAAO,OAAO,CAAC,CAAC,CAAC;AACtB,GAAG,KAAK,IAAI;AACZ;AACA,IAAI,OAAO,OAAO,CAAC,CAAC,CAAC;AACrB,GAAG,KAAK,IAAI;AACZ;AACA,IAAI,OAAO,OAAO,CAAC,CAAC,CAAC;AACrB,GAAG,KAAK,IAAI;AACZ;AACA,IAAI,OAAO,OAAO,CAAC,EAAE,CAAC;AACtB,GAAG,KAAK,IAAI;AACZ;AACA,IAAI,KAAK,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;AAC3B,IAAI,IAAI,YAAY,IAAIA,UAAQ,EAAE;AAClC,KAAK,OAAO,SAAS,CAAC,KAAK,CAACA,UAAQ,GAAG,cAAc,EAAE,CAACA,UAAQ,IAAI,KAAK,IAAI,cAAc,CAAC;AAC5F,KAAK;AACL,IAAI,OAAO,WAAW,CAAC,KAAK,CAAC;AAC7B,GAAG,KAAK,IAAI;AACZ;AACA,IAAI,KAAK,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;AACxC,IAAIA,UAAQ,IAAI,EAAC;AACjB,IAAI,IAAI,YAAY,IAAIA,UAAQ,EAAE;AAClC,KAAK,OAAO,SAAS,CAAC,KAAK,CAACA,UAAQ,GAAG,cAAc,EAAE,CAACA,UAAQ,IAAI,KAAK,IAAI,cAAc,CAAC;AAC5F,KAAK;AACL,IAAI,OAAO,YAAY,CAAC,KAAK,CAAC;AAC9B,GAAG,KAAK,IAAI;AACZ;AACA,IAAI,KAAK,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;AACxC,IAAIA,UAAQ,IAAI,EAAC;AACjB,IAAI,IAAI,YAAY,IAAIA,UAAQ,EAAE;AAClC,KAAK,OAAO,SAAS,CAAC,KAAK,CAACA,UAAQ,GAAG,cAAc,EAAE,CAACA,UAAQ,IAAI,KAAK,IAAI,cAAc,CAAC;AAC5F,KAAK;AACL,IAAI,OAAO,YAAY,CAAC,KAAK,CAAC;AAC9B,GAAG,KAAK,IAAI;AACZ;AACA,IAAI,KAAK,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;AACxC,IAAIA,UAAQ,IAAI,EAAC;AACjB,IAAI,OAAO,SAAS,CAAC,KAAK,CAAC;AAC3B,GAAG,KAAK,IAAI;AACZ;AACA,IAAI,KAAK,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;AACxC,IAAIA,UAAQ,IAAI,EAAC;AACjB,IAAI,OAAO,SAAS,CAAC,KAAK,CAAC;AAC3B,GAAG,KAAK,IAAI;AACZ;AACA,IAAI,KAAK,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;AACxC,IAAIA,UAAQ,IAAI,EAAC;AACjB,IAAI,OAAO,OAAO,CAAC,KAAK,CAAC;AACzB,GAAG,KAAK,IAAI;AACZ;AACA,IAAI,KAAK,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;AACxC,IAAIA,UAAQ,IAAI,EAAC;AACjB,IAAI,OAAO,OAAO,CAAC,KAAK,CAAC;AACzB,GAAG;AACH,IAAI,IAAI,KAAK,IAAI,IAAI;AACrB,KAAK,OAAO,KAAK,GAAG,KAAK;AACzB,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE;AAC7B,KAAK,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC,oCAAoC,EAAC;AAChE,KAAK,KAAK,CAAC,UAAU,GAAG,KAAI;AAC5B,KAAK,MAAM,KAAK;AAChB,KAAK;AACL,IAAI,MAAM,IAAI,KAAK,CAAC,4BAA4B,GAAG,KAAK,CAAC;AACzD;AACA,GAAG;AACH,EAAE;AACF,CAAC;AACD,MAAM,SAAS,GAAG,4BAA2B;AAC7C,SAAS,qBAAqB,CAAC,SAAS,EAAE,OAAO,EAAE;AACnD,CAAC,SAAS,UAAU,GAAG;AACvB;AACA,EAAE,IAAI,UAAU,CAAC,KAAK,EAAE,GAAG,yBAAyB,EAAE;AACtD,GAAG,IAAI,UAAU,GAAG,SAAS,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ,CAAC,GAAG,EAAE,2BAA2B,IAAI,cAAc,CAAC,UAAU,GAAG,eAAe,GAAG,EAAE,CAAC;AACxI,IAAI,IAAI,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,KAAK,WAAW,GAAG,cAAc,GAAG,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,MAAM,IAAI,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,EAAE,IAAI,EAAC;AAC5K,GAAG,IAAI,SAAS,CAAC,QAAQ,KAAK,CAAC;AAC/B,IAAI,SAAS,CAAC,IAAI,GAAG,sBAAsB,CAAC,OAAO,EAAE,SAAS,CAAC,IAAI,EAAC;AACpE,GAAG,OAAO,UAAU,EAAE;AACtB,GAAG;AACH,EAAE,IAAI,MAAM,GAAG,GAAE;AACjB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AACpD,GAAG,IAAI,GAAG,GAAG,SAAS,CAAC,CAAC,EAAC;AACzB,GAAG,IAAI,GAAG,KAAK,WAAW;AAC1B,IAAI,GAAG,GAAG,WAAU;AACpB,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,GAAE;AACvB,GAAG;AACH,EAAE,IAAI,cAAc,CAAC,UAAU;AAC/B,GAAG,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAChC,EAAE,OAAO,MAAM;AACf,EAAE;AACF,CAAC,UAAU,CAAC,KAAK,GAAG,EAAC;AACrB,CAAC,IAAI,SAAS,CAAC,QAAQ,KAAK,CAAC,EAAE;AAC/B,EAAE,OAAO,sBAAsB,CAAC,OAAO,EAAE,UAAU,CAAC;AACpD,EAAE;AACF,CAAC,OAAO,UAAU;AAClB,CAAC;AACD;AACA,MAAM,sBAAsB,GAAG,CAAC,OAAO,EAAE,KAAK,KAAK;AACnD,CAAC,OAAO,WAAW;AACnB,EAAE,IAAI,QAAQ,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;AAChC,EAAE,IAAI,QAAQ,KAAK,CAAC;AACpB,GAAG,OAAO,KAAK,EAAE;AACjB,EAAE,IAAI,EAAE,GAAG,OAAO,GAAG,EAAE,GAAG,EAAE,OAAO,IAAI,QAAQ,IAAI,CAAC,CAAC,CAAC,GAAG,OAAO,IAAI,QAAQ,IAAI,CAAC,EAAC;AAClF,EAAE,IAAI,SAAS,GAAG,iBAAiB,CAAC,EAAE,CAAC,IAAI,cAAc,EAAE,CAAC,EAAE,EAAC;AAC/D,EAAE,IAAI,CAAC,SAAS,EAAE;AAClB,GAAG,MAAM,IAAI,KAAK,CAAC,+BAA+B,GAAG,EAAE,CAAC;AACxD,GAAG;AACH,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI;AACrB,GAAG,SAAS,CAAC,IAAI,GAAG,qBAAqB,CAAC,SAAS,EAAE,OAAO,EAAC;AAC7D,EAAE,OAAO,SAAS,CAAC,IAAI,EAAE;AACzB,EAAE;AACF,EAAC;AACD;AACO,SAAS,cAAc,GAAG;AACjC,CAAC,IAAI,gBAAgB,GAAGI,WAAS,CAAC,MAAM;AACxC;AACA,EAAE,GAAG,GAAG,KAAI;AACZ,EAAE,OAAO,cAAc,CAAC,aAAa,EAAE;AACvC,EAAE,EAAC;AACH,CAAC,OAAO,iBAAiB,GAAG,cAAc,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,iBAAiB,CAAC;AAChG,CAAC;AACD;AACA,IAAI,eAAe,GAAG,aAAY;AAClC,IAAI,WAAW,GAAG,aAAY;AAC9B,IAAI,YAAY,GAAG,aAAY;AAC/B,IAAI,YAAY,GAAG,aAAY;AACpBC,mCAA2B,GAAG,MAAK;AAC9C;AACO,SAAS,YAAY,CAAC,cAAc,EAAE;AAC7C,CAACA,mCAA2B,GAAG,KAAI;AACnC,CAAC,eAAe,GAAG,UAAU,CAAC,CAAC,EAAC;AAChC,CAAC,WAAW,GAAG,UAAU,CAAC,CAAC,EAAC;AAC5B,CAAC,YAAY,GAAG,UAAU,CAAC,CAAC,EAAC;AAC7B,CAAC,YAAY,GAAG,UAAU,CAAC,CAAC,EAAC;AAC7B,CAAC,SAAS,UAAU,CAAC,YAAY,EAAE;AACnC,EAAE,OAAO,SAAS,UAAU,CAAC,MAAM,EAAE;AACrC,GAAG,IAAI,MAAM,GAAG,OAAO,CAAC,cAAc,EAAE,EAAC;AACzC,GAAG,IAAI,MAAM,IAAI,IAAI,EAAE;AACvB,IAAI,IAAIJ,gBAAc;AACtB,KAAK,OAAO,YAAY,CAAC,MAAM,CAAC;AAChC,IAAI,IAAI,UAAU,GAAG,GAAG,CAAC,WAAU;AACnC,IAAI,IAAI,UAAU,GAAG,cAAc,CAACD,UAAQ,GAAG,YAAY,GAAG,UAAU,EAAE,MAAM,GAAG,UAAU,EAAE,GAAG,CAAC,MAAM,EAAC;AAC1G,IAAI,IAAI,OAAO,UAAU,IAAI,QAAQ,EAAE;AACvC,KAAK,MAAM,GAAG,WAAU;AACxB,KAAK,OAAO,GAAG,YAAW;AAC1B,KAAK,MAAM;AACX,KAAK,OAAO,GAAG,WAAU;AACzB,KAAK,cAAc,GAAG,EAAC;AACvB,KAAK,YAAY,GAAG,EAAC;AACrB,KAAK,MAAM,GAAG,OAAO,CAAC,CAAC,EAAC;AACxB,KAAK,IAAI,MAAM,KAAK,SAAS;AAC7B,MAAM,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC;AACjD,KAAK;AACL,IAAI;AACJ,GAAG,IAAI,eAAe,GAAG,MAAM,CAAC,OAAM;AACtC,GAAG,IAAI,eAAe,IAAI,MAAM,EAAE;AAClC,IAAIA,UAAQ,IAAI,OAAM;AACtB,IAAI,OAAO,MAAM;AACjB,IAAI;AACJ,GAAG,SAAS,GAAG,OAAM;AACrB,GAAG,cAAc,GAAGA,WAAQ;AAC5B,GAAG,YAAY,GAAGA,UAAQ,GAAG,gBAAe;AAC5C,GAAGA,UAAQ,IAAI,OAAM;AACrB,GAAG,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC;AACjC,GAAG;AACH,EAAE;AACF,CAAC;AACD,SAAS,YAAY,CAAC,MAAM,EAAE;AAC9B,CAAC,IAAI,OAAM;AACX,CAAC,IAAI,MAAM,GAAG,EAAE,EAAE;AAClB,EAAE,IAAI,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC;AACtC,GAAG,OAAO,MAAM;AAChB,EAAE;AACF,CAAC,IAAI,MAAM,GAAG,EAAE,IAAI,OAAO;AAC3B,EAAE,OAAO,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAACA,UAAQ,EAAEA,UAAQ,IAAI,MAAM,CAAC,CAAC;AACnE,CAAC,MAAM,GAAG,GAAGA,UAAQ,GAAG,OAAM;AAC9B,CAAC,MAAM,KAAK,GAAG,GAAE;AACjB,CAAC,MAAM,GAAG,GAAE;AACZ,CAAC,OAAOA,UAAQ,GAAG,GAAG,EAAE;AACxB,EAAE,MAAM,KAAK,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;AAC/B,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,EAAE;AAC5B;AACA,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,EAAC;AACpB,GAAG,MAAM,IAAI,CAAC,KAAK,GAAG,IAAI,MAAM,IAAI,EAAE;AACtC;AACA,GAAG,MAAM,KAAK,GAAG,GAAG,CAACA,UAAQ,EAAE,CAAC,GAAG,KAAI;AACvC,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,KAAK,EAAC;AAC5C,GAAG,MAAM,IAAI,CAAC,KAAK,GAAG,IAAI,MAAM,IAAI,EAAE;AACtC;AACA,GAAG,MAAM,KAAK,GAAG,GAAG,CAACA,UAAQ,EAAE,CAAC,GAAG,KAAI;AACvC,GAAG,MAAM,KAAK,GAAG,GAAG,CAACA,UAAQ,EAAE,CAAC,GAAG,KAAI;AACvC,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,KAAK,EAAE,KAAK,KAAK,IAAI,CAAC,CAAC,GAAG,KAAK,EAAC;AAC5D,GAAG,MAAM,IAAI,CAAC,KAAK,GAAG,IAAI,MAAM,IAAI,EAAE;AACtC;AACA,GAAG,MAAM,KAAK,GAAG,GAAG,CAACA,UAAQ,EAAE,CAAC,GAAG,KAAI;AACvC,GAAG,MAAM,KAAK,GAAG,GAAG,CAACA,UAAQ,EAAE,CAAC,GAAG,KAAI;AACvC,GAAG,MAAM,KAAK,GAAG,GAAG,CAACA,UAAQ,EAAE,CAAC,GAAG,KAAI;AACvC,GAAG,IAAI,IAAI,GAAG,CAAC,CAAC,KAAK,GAAG,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,GAAG,MAAK;AAClF,GAAG,IAAI,IAAI,GAAG,MAAM,EAAE;AACtB,IAAI,IAAI,IAAI,QAAO;AACnB,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK,EAAE,IAAI,KAAK,IAAI,MAAM,EAAC;AAChD,IAAI,IAAI,GAAG,MAAM,IAAI,IAAI,GAAG,KAAK,EAAC;AAClC,IAAI;AACJ,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,EAAC;AACnB,GAAG,MAAM;AACT,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,EAAC;AACpB,GAAG;AACH;AACA,EAAE,IAAI,KAAK,CAAC,MAAM,IAAI,MAAM,EAAE;AAC9B,GAAG,MAAM,IAAI,YAAY,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,EAAC;AAC9C,GAAG,KAAK,CAAC,MAAM,GAAG,EAAC;AACnB,GAAG;AACH,EAAE;AACF;AACA,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;AACvB,EAAE,MAAM,IAAI,YAAY,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,EAAC;AAC7C,EAAE;AACF;AACA,CAAC,OAAO,MAAM;AACd,CAAC;AACM,SAAS,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE;AAClD,CAAC,IAAI,WAAW,GAAG,GAAG,CAAC;AACvB,CAAC,GAAG,GAAG,MAAM,CAAC;AACd,CAACA,UAAQ,GAAG,KAAK,CAAC;AAClB,CAAC,IAAI;AACL,EAAE,OAAO,YAAY,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,SAAS;AACX,EAAE,GAAG,GAAG,WAAW,CAAC;AACpB,EAAE;AACF,CAAC;AACD;AACA,SAAS,SAAS,CAAC,MAAM,EAAE;AAC3B,CAAC,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC,MAAM,EAAC;AAC9B,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;AAClC,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,GAAE;AACnB,EAAE;AACF,CAAC,IAAI,cAAc,CAAC,UAAU;AAC9B,EAAE,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC;AAC7B,CAAC,OAAO,KAAK;AACb,CAAC;AACD;AACA,SAAS,OAAO,CAAC,MAAM,EAAE;AACzB,CAAC,IAAI,cAAc,CAAC,aAAa,EAAE;AACnC,EAAE,IAAI,MAAM,GAAG,GAAE;AACjB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;AACnC,GAAG,IAAI,GAAG,GAAG,OAAO,GAAE;AACtB,GAAG,IAAI,GAAG,KAAK,WAAW;AAC1B,IAAI,GAAG,GAAG,UAAU,CAAC;AACrB,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,GAAE;AACvB,GAAG;AACH,EAAE,OAAO,MAAM;AACf,EAAE,MAAM;AACR,EAAE,IAAI,GAAG,GAAG,IAAI,GAAG,GAAE;AACrB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;AACnC,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE,EAAC;AAC1B,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,EAAE;AACF,CAAC;AACD;AACA,IAAI,YAAY,GAAG,MAAM,CAAC,aAAY;AACtC,SAAS,cAAc,CAAC,MAAM,EAAE;AAChC,CAAC,IAAI,KAAK,GAAGA,WAAQ;AACrB,CAAC,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC,MAAM,EAAC;AAC9B,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;AAClC,EAAE,MAAM,IAAI,GAAG,GAAG,CAACA,UAAQ,EAAE,CAAC,CAAC;AAC/B,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,CAAC,EAAE;AACzB,IAAIA,UAAQ,GAAG,MAAK;AACpB,IAAI,MAAM;AACV,IAAI;AACJ,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,KAAI;AAClB,GAAG;AACH,EAAE,OAAO,YAAY,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC;AAC1C,CAAC;AACD,SAAS,eAAe,CAAC,MAAM,EAAE;AACjC,CAAC,IAAI,MAAM,GAAG,CAAC,EAAE;AACjB,EAAE,IAAI,MAAM,GAAG,CAAC,EAAE;AAClB,GAAG,IAAI,MAAM,KAAK,CAAC;AACnB,IAAI,OAAO,EAAE;AACb,QAAQ;AACR,IAAI,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;AAC3B,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;AACxB,KAAKA,UAAQ,IAAI,EAAC;AAClB,KAAK,MAAM;AACX,KAAK;AACL,IAAI,OAAO,YAAY,CAAC,CAAC,CAAC;AAC1B,IAAI;AACJ,GAAG,MAAM;AACT,GAAG,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;AAC1B,GAAG,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;AAC1B,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;AACzC,IAAIA,UAAQ,IAAI,EAAC;AACjB,IAAI,MAAM;AACV,IAAI;AACJ,GAAG,IAAI,MAAM,GAAG,CAAC;AACjB,IAAI,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC;AAC7B,GAAG,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;AAC1B,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;AACvB,IAAIA,UAAQ,IAAI,EAAC;AACjB,IAAI,MAAM;AACV,IAAI;AACJ,GAAG,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC/B,GAAG;AACH,EAAE,MAAM;AACR,EAAE,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;AACzB,EAAE,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;AACzB,EAAE,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;AACzB,EAAE,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;AACzB,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;AAC5E,GAAGA,UAAQ,IAAI,EAAC;AAChB,GAAG,MAAM;AACT,GAAG;AACH,EAAE,IAAI,MAAM,GAAG,CAAC,EAAE;AAClB,GAAG,IAAI,MAAM,KAAK,CAAC;AACnB,IAAI,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACnC,QAAQ;AACR,IAAI,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;AAC3B,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;AACxB,KAAKA,UAAQ,IAAI,EAAC;AAClB,KAAK,MAAM;AACX,KAAK;AACL,IAAI,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACtC,IAAI;AACJ,GAAG,MAAM,IAAI,MAAM,GAAG,CAAC,EAAE;AACzB,GAAG,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;AAC1B,GAAG,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;AAC1B,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;AACzC,IAAIA,UAAQ,IAAI,EAAC;AACjB,IAAI,MAAM;AACV,IAAI;AACJ,GAAG,IAAI,MAAM,GAAG,CAAC;AACjB,IAAI,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACzC,GAAG,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;AAC1B,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;AACvB,IAAIA,UAAQ,IAAI,EAAC;AACjB,IAAI,MAAM;AACV,IAAI;AACJ,GAAG,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC3C,GAAG,MAAM;AACT,GAAG,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;AAC1B,GAAG,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;AAC1B,GAAG,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;AAC1B,GAAG,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;AAC1B,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;AAC7E,IAAIA,UAAQ,IAAI,EAAC;AACjB,IAAI,MAAM;AACV,IAAI;AACJ,GAAG,IAAI,MAAM,GAAG,EAAE,EAAE;AACpB,IAAI,IAAI,MAAM,KAAK,CAAC;AACpB,KAAK,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAChD,SAAS;AACT,KAAK,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;AAC5B,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;AACzB,MAAMA,UAAQ,IAAI,EAAC;AACnB,MAAM,MAAM;AACZ,MAAM;AACN,KAAK,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACnD,KAAK;AACL,IAAI,MAAM,IAAI,MAAM,GAAG,EAAE,EAAE;AAC3B,IAAI,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;AAC3B,IAAI,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;AAC3B,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;AAC1C,KAAKA,UAAQ,IAAI,GAAE;AACnB,KAAK,MAAM;AACX,KAAK;AACL,IAAI,IAAI,MAAM,GAAG,EAAE;AACnB,KAAK,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACtD,IAAI,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;AAC3B,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;AACxB,KAAKA,UAAQ,IAAI,GAAE;AACnB,KAAK,MAAM;AACX,KAAK;AACL,IAAI,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACxD,IAAI,MAAM;AACV,IAAI,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;AAC3B,IAAI,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;AAC3B,IAAI,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;AAC3B,IAAI,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;AAC3B,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;AAC9E,KAAKA,UAAQ,IAAI,GAAE;AACnB,KAAK,MAAM;AACX,KAAK;AACL,IAAI,IAAI,MAAM,GAAG,EAAE,EAAE;AACrB,KAAK,IAAI,MAAM,KAAK,EAAE;AACtB,MAAM,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC7D,UAAU;AACV,MAAM,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;AAC7B,MAAM,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;AAC1B,OAAOA,UAAQ,IAAI,GAAE;AACrB,OAAO,MAAM;AACb,OAAO;AACP,MAAM,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAChE,MAAM;AACN,KAAK,MAAM;AACX,KAAK,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;AAC5B,KAAK,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;AAC5B,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;AAC3C,MAAMA,UAAQ,IAAI,GAAE;AACpB,MAAM,MAAM;AACZ,MAAM;AACN,KAAK,IAAI,MAAM,GAAG,EAAE;AACpB,MAAM,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACnE,KAAK,IAAI,CAAC,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;AAC5B,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE;AACzB,MAAMA,UAAQ,IAAI,GAAE;AACpB,MAAM,MAAM;AACZ,MAAM;AACN,KAAK,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACrE,KAAK;AACL,IAAI;AACJ,GAAG;AACH,EAAE;AACF,CAAC;AACD;AACA,SAAS,gBAAgB,GAAG;AAC5B,CAAC,IAAI,KAAK,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;AAC5B,CAAC,IAAI,OAAM;AACX,CAAC,IAAI,KAAK,GAAG,IAAI,EAAE;AACnB;AACA,EAAE,MAAM,GAAG,KAAK,GAAG,KAAI;AACvB,EAAE,MAAM;AACR,EAAE,OAAO,KAAK;AACd,GAAG,KAAK,IAAI;AACZ;AACA,IAAI,MAAM,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;AAC5B,IAAI,KAAK;AACT,GAAG,KAAK,IAAI;AACZ;AACA,IAAI,MAAM,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;AACzC,IAAIA,UAAQ,IAAI,EAAC;AACjB,IAAI,KAAK;AACT,GAAG,KAAK,IAAI;AACZ;AACA,IAAI,MAAM,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,EAAC;AACzC,IAAIA,UAAQ,IAAI,EAAC;AACjB,IAAI,KAAK;AACT,GAAG;AACH,IAAI,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC;AACtC,GAAG;AACH,EAAE;AACF,CAAC,OAAO,YAAY,CAAC,MAAM,CAAC;AAC5B,CAAC;AACD;AACA;AACA,SAAS,OAAO,CAAC,MAAM,EAAE;AACzB,CAAC,OAAO,cAAc,CAAC,WAAW;AAClC;AACA,EAAE,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAEA,UAAQ,EAAEA,UAAQ,IAAI,MAAM,CAAC;AACpE,EAAE,GAAG,CAAC,QAAQ,CAACA,UAAQ,EAAEA,UAAQ,IAAI,MAAM,CAAC;AAC5C,CAAC;AACD,SAAS,OAAO,CAAC,MAAM,EAAE;AACzB,CAAC,IAAI,IAAI,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;AAC3B,CAAC,IAAI,iBAAiB,CAAC,IAAI,CAAC,EAAE;AAC9B,EAAE,IAAI,IAAG;AACT,EAAE,OAAO,iBAAiB,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,QAAQ,CAACA,UAAQ,EAAE,GAAG,IAAIA,UAAQ,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,YAAY,KAAK;AACvG,GAAGA,UAAQ,GAAG,YAAY,CAAC;AAC3B,GAAG,IAAI;AACP,IAAI,OAAO,IAAI,EAAE,CAAC;AAClB,IAAI,SAAS;AACb,IAAIA,UAAQ,GAAG,GAAG,CAAC;AACnB,IAAI;AACJ,GAAG,CAAC;AACJ,EAAE;AACF;AACA,EAAE,MAAM,IAAI,KAAK,CAAC,yBAAyB,GAAG,IAAI,CAAC;AACnD,CAAC;AACD;AACA,IAAI,QAAQ,GAAG,IAAI,KAAK,CAAC,IAAI,EAAC;AAC9B,SAAS,OAAO,GAAG;AACnB,CAAC,IAAI,MAAM,GAAG,GAAG,CAACA,UAAQ,EAAE,EAAC;AAC7B,CAAC,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,GAAG,IAAI,EAAE;AACtC;AACA,EAAE,MAAM,GAAG,MAAM,GAAG,KAAI;AACxB,EAAE,IAAI,YAAY,IAAIA,UAAQ;AAC9B,GAAG,OAAO,SAAS,CAAC,KAAK,CAACA,UAAQ,GAAG,cAAc,EAAE,CAACA,UAAQ,IAAI,MAAM,IAAI,cAAc,CAAC;AAC3F,OAAO,IAAI,EAAE,YAAY,IAAI,CAAC,IAAI,MAAM,GAAG,GAAG,CAAC;AAC/C,GAAG,OAAO,eAAe,CAAC,MAAM,CAAC;AACjC,EAAE,MAAM;AACR,EAAEA,UAAQ,GAAE;AACZ,EAAE,OAAO,YAAY,CAAC,IAAI,EAAE,CAAC;AAC7B,EAAE;AACF,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,MAAM,IAAI,CAAC,KAAK,MAAM,GAAG,CAAC,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,GAAG,CAACA,UAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,MAAK;AACjH,CAAC,IAAI,KAAK,GAAG,QAAQ,CAAC,GAAG,EAAC;AAC1B,CAAC,IAAI,aAAa,GAAGA,WAAQ;AAC7B,CAAC,IAAI,GAAG,GAAGA,UAAQ,GAAG,MAAM,GAAG,EAAC;AAChC,CAAC,IAAI,MAAK;AACV,CAAC,IAAI,CAAC,GAAG,EAAC;AACV,CAAC,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,IAAI,MAAM,EAAE;AACrC,EAAE,OAAO,aAAa,GAAG,GAAG,EAAE;AAC9B,GAAG,KAAK,GAAG,QAAQ,CAAC,SAAS,CAAC,aAAa,EAAC;AAC5C,GAAG,IAAI,KAAK,IAAI,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE;AAC5B,IAAI,aAAa,GAAG,WAAU;AAC9B,IAAI,KAAK;AACT,IAAI;AACJ,GAAG,aAAa,IAAI,EAAC;AACrB,GAAG;AACH,EAAE,GAAG,IAAI,EAAC;AACV,EAAE,OAAO,aAAa,GAAG,GAAG,EAAE;AAC9B,GAAG,KAAK,GAAG,GAAG,CAAC,aAAa,EAAE,EAAC;AAC/B,GAAG,IAAI,KAAK,IAAI,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE;AAC5B,IAAI,aAAa,GAAG,WAAU;AAC9B,IAAI,KAAK;AACT,IAAI;AACJ,GAAG;AACH,EAAE,IAAI,aAAa,KAAK,GAAG,EAAE;AAC7B,GAAGA,UAAQ,GAAG,cAAa;AAC3B,GAAG,OAAO,KAAK,CAAC,MAAM;AACtB,GAAG;AACH,EAAE,GAAG,IAAI,EAAC;AACV,EAAE,aAAa,GAAGA,WAAQ;AAC1B,EAAE;AACF,CAAC,KAAK,GAAG,GAAE;AACX,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,MAAK;AACtB,CAAC,KAAK,CAAC,KAAK,GAAG,OAAM;AACrB,CAAC,OAAO,aAAa,GAAG,GAAG,EAAE;AAC7B,EAAE,KAAK,GAAG,QAAQ,CAAC,SAAS,CAAC,aAAa,EAAC;AAC3C,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,EAAC;AACnB,EAAE,aAAa,IAAI,EAAC;AACpB,EAAE;AACF,CAAC,GAAG,IAAI,EAAC;AACT,CAAC,OAAO,aAAa,GAAG,GAAG,EAAE;AAC7B,EAAE,KAAK,GAAG,GAAG,CAAC,aAAa,EAAE,EAAC;AAC9B,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,EAAC;AACnB,EAAE;AACF;AACA,CAAC,IAAI,MAAM,GAAG,MAAM,GAAG,EAAE,GAAG,eAAe,CAAC,MAAM,CAAC,GAAG,cAAc,CAAC,MAAM,EAAC;AAC5E,CAAC,IAAI,MAAM,IAAI,IAAI;AACnB,EAAE,OAAO,KAAK,CAAC,MAAM,GAAG,MAAM;AAC9B,CAAC,OAAO,KAAK,CAAC,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC;AAC9C,CAAC;AACD;AACA,SAAS,YAAY,CAAC,QAAQ,EAAE;AAChC;AACA,CAAC,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,OAAO,QAAQ,CAAC;AACnD,CAAC,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,OAAO,QAAQ,KAAK,SAAS,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,OAAO,QAAQ,CAAC,QAAQ,EAAE,CAAC;AAC/H,CAAC,IAAI,QAAQ,IAAI,IAAI,EAAE,OAAO,QAAQ,GAAG,EAAE,CAAC;AAC5C,CAAC,IAAI,cAAc,CAAC,oBAAoB,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,OAAO,IAAI,CAAC,CAAC,EAAE;AACvK,EAAE,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE,CAAC;AACpC,EAAE;AACF,CAAC,MAAM,IAAI,KAAK,CAAC,CAAC,kCAAkC,EAAE,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC;AACzE,CAAC;AACD;AACA,MAAM,gBAAgB,GAAG,CAAC,EAAE,EAAE,QAAQ,KAAK;AAC3C,CAAC,IAAI,SAAS,GAAG,IAAI,EAAE,CAAC,GAAG,CAAC,YAAY,EAAC;AACzC;AACA,CAAC,IAAI,SAAS,GAAG,GAAE;AACnB,CAAC,IAAI,QAAQ,KAAK,SAAS,EAAE;AAC7B,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,EAAE,EAAC;AACjE,EAAE,SAAS,CAAC,QAAQ,GAAG,SAAQ;AAC/B,EAAE;AACF,CAAC,IAAI,iBAAiB,GAAG,iBAAiB,CAAC,EAAE,EAAC;AAC9C;AACA;AACA;AACA,CAAC,IAAI,iBAAiB,KAAK,iBAAiB,CAAC,QAAQ,IAAI,cAAc,CAAC,EAAE;AAC1E,EAAE,CAAC,iBAAiB,CAAC,iBAAiB,KAAK,iBAAiB,CAAC,iBAAiB,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,kBAAiB;AAC7G,EAAE;AACF,CAAC,iBAAiB,CAAC,EAAE,CAAC,GAAG,UAAS;AAClC,CAAC,SAAS,CAAC,IAAI,GAAG,qBAAqB,CAAC,SAAS,EAAE,SAAS,EAAC;AAC7D,CAAC,OAAO,SAAS,CAAC,IAAI,EAAE;AACxB,EAAC;AACD,iBAAiB,CAAC,CAAC,CAAC,GAAG,MAAM,GAAE;AAC/B,iBAAiB,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAI;AACpC;AACA,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK;AACpC;AACA,CAAC,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAC1B,CAAC,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAChE,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;AAClC,EAAE,KAAK,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC;AACtB,EAAE,KAAK,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3B,EAAE;AACF,CAAC,OAAO,KAAK,CAAC;AACd,EAAC;AACD;AACA,IAAI,MAAM,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,cAAc,EAAE,CAAC;AAClD,iBAAiB,CAAC,IAAI,CAAC,GAAG,MAAM;AAChC,CAAC,IAAI,IAAI,GAAG,IAAI,GAAE;AAClB,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;AAC/D,EAAC;AACD;AACA,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK;AACpC;AACA,CAAC,IAAI,cAAc,CAAC,eAAe,KAAK,KAAK,EAAE,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC;AACxG,CAAC,IAAI,EAAE,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,GAAG,CAAC,EAAC;AAC1C,CAAC,IAAI,CAAC,YAAY;AAClB,EAAE,YAAY,GAAG,IAAI,GAAG,GAAE;AAC1B,CAAC,IAAI,KAAK,GAAG,GAAG,CAACA,UAAQ,EAAC;AAC1B,CAAC,IAAI,OAAM;AACX;AACA,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI;AACpE,EAAE,MAAM,GAAG,GAAE;AACb,MAAM,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI;AACzE,EAAE,MAAM,GAAG,IAAI,GAAG,GAAE;AACpB,MAAM,IAAI,CAAC,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,GAAG,CAACA,UAAQ,GAAG,CAAC,CAAC,KAAK,IAAI;AAC1G,EAAE,MAAM,GAAG,IAAI,GAAG,GAAE;AACpB;AACA,EAAE,MAAM,GAAG,GAAE;AACb;AACA,CAAC,IAAI,QAAQ,GAAG,EAAE,MAAM,GAAE;AAC1B,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,EAAE,QAAQ,EAAC;AAC/B,CAAC,IAAI,gBAAgB,GAAG,IAAI,GAAE;AAC9B,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;AACrB;AACA,EAAE,OAAO,QAAQ,CAAC,MAAM,GAAG,gBAAgB;AAC3C,EAAE,MAAM;AACR;AACA,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,gBAAgB,EAAC;AACzC,EAAE;AACF;AACA;AACA,CAAC,IAAI,MAAM,YAAY,GAAG;AAC1B,EAAE,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,gBAAgB,CAAC,OAAO,EAAE,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAC;AACjE,CAAC,IAAI,MAAM,YAAY,GAAG;AAC1B,EAAE,KAAK,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,EAAC;AAC3D,CAAC,OAAO,MAAM;AACd,EAAC;AACD;AACA,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK;AACpC;AACA,CAAC,IAAI,cAAc,CAAC,eAAe,KAAK,KAAK,EAAE,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC;AACxG,CAAC,IAAI,EAAE,GAAG,QAAQ,CAAC,SAAS,CAACA,UAAQ,GAAG,CAAC,EAAC;AAC1C,CAAC,IAAI,QAAQ,GAAG,YAAY,CAAC,GAAG,CAAC,EAAE,EAAC;AACpC,CAAC,QAAQ,CAAC,IAAI,GAAG,KAAI;AACrB,CAAC,OAAO,QAAQ,CAAC,MAAM;AACvB,EAAC;AACD;AACA,iBAAiB,CAAC,IAAI,CAAC,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,EAAE,EAAC;AAC/C;AACO,MAAM,WAAW,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,GAAG,OAAO,EAAC;AACnK;AACA,IAAI,IAAI,GAAG,OAAO,UAAU,KAAK,QAAQ,GAAG,UAAU,GAAG,MAAM,CAAC;AAChE,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK;AACpC,CAAC,IAAI,QAAQ,GAAG,IAAI,CAAC,CAAC,EAAC;AACvB;AACA,CAAC,IAAI,MAAM,GAAG,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,OAAM;AAC7D;AACA,CAAC,IAAI,cAAc,GAAG,WAAW,CAAC,QAAQ,EAAC;AAC3C,CAAC,IAAI,CAAC,cAAc,EAAE;AACtB,EAAE,IAAI,QAAQ,KAAK,EAAE,EAAE,OAAO,MAAM;AACpC,EAAE,IAAI,QAAQ,KAAK,EAAE,EAAE,OAAO,IAAI,QAAQ,CAAC,MAAM,CAAC;AAClD,EAAE,MAAM,IAAI,KAAK,CAAC,sCAAsC,GAAG,QAAQ,CAAC;AACpE,EAAE;AACF,CAAC,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC;AACxC,EAAC;AACD,iBAAiB,CAAC,IAAI,CAAC,GAAG,MAAM;AAChC,CAAC,IAAI,IAAI,GAAG,IAAI,GAAE;AAClB,CAAC,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AACpC,EAAC;AACD,MAAM,WAAW,GAAG,GAAE;AACtB,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK;AACpC,CAAC,IAAI,QAAQ,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAC;AAC5E,CAAC,IAAI,YAAY,GAAGA,WAAQ;AAC5B,CAACA,UAAQ,IAAI,QAAQ,GAAG,IAAI,CAAC,OAAM;AACnC,CAACC,gBAAc,GAAG,YAAW;AAC7B,CAACA,gBAAc,GAAG,CAAC,gBAAgB,EAAE,EAAE,gBAAgB,EAAE,EAAC;AAC1D,CAACA,gBAAc,CAAC,SAAS,GAAG,EAAC;AAC7B,CAACA,gBAAc,CAAC,SAAS,GAAG,EAAC;AAC7B,CAACA,gBAAc,CAAC,kBAAkB,GAAGD,WAAQ;AAC7C,CAACA,UAAQ,GAAG,aAAY;AACxB,CAAC,OAAO,IAAI,EAAE;AACd,EAAC;AACD;AACA,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK;AACpC;AACA,CAAC,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC;AACrB,EAAE,OAAO,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;AAC5F,MAAM,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC;AAC1B,EAAE,OAAO,IAAI,IAAI;AACjB,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,OAAO;AAClF,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,WAAW,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;AAC7G,MAAM,IAAI,IAAI,CAAC,MAAM,IAAI,EAAE;AAC3B,EAAE,OAAO,IAAI,IAAI;AACjB,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,OAAO;AAC3E,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,eAAe,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,aAAa,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,WAAW,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC;AAC7K;AACA,EAAE,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC;AAC5B,EAAC;AACD;AACA;AACA;AACA,SAASI,WAAS,CAAC,QAAQ,EAAE;AAC7B,CAAC,IAAI,WAAW;AAChB,EAAE,WAAW,EAAE,CAAC;AAChB,CAAC,IAAI,WAAW,GAAG,OAAM;AACzB,CAAC,IAAI,aAAa,GAAGJ,WAAQ;AAC7B,CAAC,IAAI,mBAAmB,GAAG,eAAc;AACzC,CAAC,IAAI,mBAAmB,GAAG,eAAc;AACzC,CAAC,IAAI,iBAAiB,GAAG,aAAY;AACrC,CAAC,IAAI,cAAc,GAAG,UAAS;AAC/B,CAAC,IAAI,YAAY,GAAG,QAAO;AAC3B,CAAC,IAAI,iBAAiB,GAAG,aAAY;AACrC,CAAC,IAAI,mBAAmB,GAAGC,iBAAc;AACzC;AACA;AACA,CAAC,IAAI,QAAQ,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,EAAC;AACpD,CAAC,IAAI,eAAe,GAAG,kBAAiB;AACxC,CAAC,IAAI,uBAAuB,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC,EAAE,iBAAiB,CAAC,MAAM,EAAC;AACnF,CAAC,IAAI,UAAU,GAAG,eAAc;AAChC,CAAC,IAAI,mBAAmB,GAAG,eAAc;AACzC,CAAC,IAAI,KAAK,GAAG,QAAQ,GAAE;AACvB,CAAC,MAAM,GAAG,YAAW;AACrB,CAACD,UAAQ,GAAG,cAAa;AACzB,CAAC,cAAc,GAAG,oBAAmB;AACrC,CAAC,cAAc,GAAG,oBAAmB;AACrC,CAAC,YAAY,GAAG,kBAAiB;AACjC,CAAC,SAAS,GAAG,eAAc;AAC3B,CAAC,OAAO,GAAG,aAAY;AACvB,CAAC,YAAY,GAAG,kBAAiB;AACjC,CAACC,gBAAc,GAAG,oBAAmB;AACrC,CAAC,GAAG,GAAG,SAAQ;AACf,CAAC,cAAc,GAAG,oBAAmB;AACrC,CAAC,iBAAiB,GAAG,gBAAe;AACpC,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,EAAE,iBAAiB,CAAC,MAAM,EAAE,GAAG,uBAAuB,EAAC;AAClF,CAAC,cAAc,GAAG,WAAU;AAC5B,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,EAAC;AACpE,CAAC,OAAO,KAAK;AACb,CAAC;AACM,SAAS,WAAW,GAAG;AAC9B,CAAC,GAAG,GAAG,KAAI;AACX,CAAC,YAAY,GAAG,KAAI;AACpB,CAAC,iBAAiB,GAAG,KAAI;AACzB,CAAC;AACD;AACO,SAASK,cAAY,CAAC,SAAS,EAAE;AACxC,CAAC,IAAI,SAAS,CAAC,MAAM;AACrB,EAAE,iBAAiB,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,OAAM;AACtD;AACA,EAAE,iBAAiB,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,UAAS;AAC/C,CAAC;AACD;AACO,MAAM,MAAM,GAAG,IAAI,KAAK,CAAC,GAAG,EAAC;AACpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAC9B,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,GAAG,OAAO,CAAC,EAAC;AACtD,CAAC;AACW,MAAC,OAAO,GAAG,QAAO;AAC9B,IAAI,cAAc,GAAG,IAAI,OAAO,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,EAAC;AAC3C,MAAC,MAAM,GAAG,cAAc,CAAC,OAAM;AAC/B,MAAC,cAAc,GAAG,cAAc,CAAC,eAAc;AAC/C,MAAC,MAAM,GAAG,cAAc,CAAC,OAAM;AAC/B,MAAC,eAAe,GAAG;AAC/B,CAAC,KAAK,EAAE,CAAC;AACT,CAAC,MAAM,EAAE,CAAC;AACV,CAAC,aAAa,EAAE,CAAC;AACjB,CAAC,WAAW,EAAE,CAAC;AACf,EAAC;AACD,IAAI,QAAQ,GAAG,IAAI,YAAY,CAAC,CAAC,EAAC;AAClC,IAAI,OAAO,GAAG,IAAI,UAAU,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAC;AAC5C,SAAS,YAAY,CAAC,aAAa,EAAE;AAC5C,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,cAAa;AAC5B,CAAC,IAAI,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAC;AACxE,CAAC,OAAO,CAAC,CAAC,UAAU,GAAG,aAAa,IAAI,aAAa,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU;AAC3F,CAAC;AACM,SAAS,aAAa,CAAC,iBAAiB,EAAE,aAAa,EAAE,SAAS,EAAE;AAC3E,CAACJ,YAAU,GAAG,iBAAiB,CAAC;AAChC,CAACC,oBAAkB,GAAG,aAAa,CAAC;AACpC,CAAC,WAAW,GAAG,SAAS,CAAC;AACzB;;ACzqCA,IAAII,cAAW;AACf,IAAI;AACJ,CAACA,aAAW,GAAG,IAAI,WAAW,GAAE;AAChC,CAAC,CAAC,OAAO,KAAK,EAAE,EAAE;AAClB,IAAI,UAAU,EAAE,iBAAgB;AAChC,MAAMC,eAAa,GAAG,OAAO,MAAM,KAAK,YAAW;AACnD,MAAM,iBAAiB,GAAGA,eAAa;AACvC,CAAC,SAAS,MAAM,EAAE,EAAE,OAAO,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,GAAG,WAAU;AACxE,MAAM,SAAS,GAAGA,eAAa,GAAG,MAAM,GAAG,WAAU;AACrD,MAAM,eAAe,GAAGA,eAAa,GAAG,WAAW,GAAG,WAAU;AAChE,IAAI,MAAM,EAAE,WAAU;AACtB,IAAI,WAAU;AACd,IAAI,QAAQ,GAAG,EAAC;AAChB,IAAI,QAAO;AACX,IAAI,cAAc,GAAG,KAAI;AACzB,IAAI,iBAAgB;AACpB,MAAM,eAAe,GAAG,OAAM;AAC9B,MAAM,WAAW,GAAG,kBAAiB;AAC9B,MAAM,aAAa,GAAG,MAAM,CAAC,WAAW,EAAC;AACzC,MAAM,KAAK,SAAS,OAAO,CAAC;AACnC,CAAC,WAAW,CAAC,OAAO,EAAE;AACtB,EAAE,KAAK,CAAC,OAAO,EAAC;AAChB,EAAE,IAAI,CAAC,MAAM,GAAG,EAAC;AAEjB,EAAE,IAAI,MAAK;AACX,EAAE,IAAI,gBAAe;AACrB,EAAE,IAAI,WAAU;AAChB,EAAE,IAAI,aAAY;AAClB,EAAE,IAAI,UAAU,GAAG,SAAS,CAAC,SAAS,CAAC,SAAS,GAAG,SAAS,MAAM,EAAE,QAAQ,EAAE;AAC9E,GAAG,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAC,UAAU,GAAG,QAAQ,CAAC;AAC1E,GAAG,GAAG,CAACD,aAAW,IAAIA,aAAW,CAAC,UAAU;AAC5C,GAAG,SAAS,MAAM,EAAE,QAAQ,EAAE;AAC9B,IAAI,OAAOA,aAAW,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO;AAC5E,IAAI,GAAG,MAAK;AACZ;AACA,EAAE,IAAI,KAAK,GAAG,KAAI;AAClB,EAAE,IAAI,CAAC,OAAO;AACd,GAAG,OAAO,GAAG,GAAE;AACf,EAAE,IAAI,YAAY,GAAG,OAAO,IAAI,OAAO,CAAC,WAAU;AAClD,EAAE,IAAI,mBAAmB,GAAG,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,eAAc;AACxE,EAAE,IAAI,mBAAmB,GAAG,OAAO,CAAC,oBAAmB;AACvD,EAAE,IAAI,mBAAmB,IAAI,IAAI;AACjC,GAAG,mBAAmB,GAAG,mBAAmB,GAAG,EAAE,GAAG,EAAC;AACrD,EAAE,IAAI,mBAAmB,GAAG,IAAI;AAChC,GAAG,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC;AACxD,EAAE,IAAI,OAAO,CAAC,eAAe,IAAI,OAAO,CAAC,SAAS,IAAI,SAAS,EAAE;AACjE,GAAG,IAAI,CAAC,SAAS,GAAG,KAAI;AACxB,GAAG;AACH,EAAE,IAAI,gBAAgB,GAAG,OAAO,CAAC,iBAAgB;AACjD,EAAE,IAAI,gBAAgB,IAAI,IAAI;AAC9B,GAAG,gBAAgB,GAAG,mBAAmB,GAAG,EAAE,GAAG,GAAE;AACnD,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,OAAO,CAAC,UAAU,IAAI,KAAK;AACrD,GAAG,IAAI,CAAC,UAAU,GAAG,GAAE;AACvB;AACA,EAAE,IAAI,iBAAiB,GAAG,mBAAmB,GAAG,EAAE,KAAK,gBAAgB,GAAG,mBAAmB,GAAG,EAAE,EAAC;AACnG,EAAE,IAAI,aAAa,GAAG,mBAAmB,GAAG,KAAI;AAChD,EAAE,IAAI,cAAc,GAAG,mBAAmB,GAAG,gBAAgB,GAAG,KAAI;AACpE,EAAE,IAAI,cAAc,GAAG,IAAI,EAAE;AAC7B,GAAG,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC;AAC1E,GAAG;AACH,EAAE,IAAI,iBAAiB,GAAG,GAAE;AAC5B,EAAE,IAAI,gBAAgB,GAAG,EAAC;AAC1B,EAAE,IAAI,oCAAoC,GAAG,EAAC;AAC9C;AACA,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,SAAS,KAAK,EAAE,aAAa,EAAE;AAC3D,GAAG,IAAI,CAAC,MAAM,EAAE;AAChB,IAAI,MAAM,GAAG,IAAI,iBAAiB,CAAC,IAAI,EAAC;AACxC,IAAI,UAAU,GAAG,MAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,EAAC;AAC5F,IAAI,QAAQ,GAAG,EAAC;AAChB,IAAI;AACJ,GAAG,OAAO,GAAG,MAAM,CAAC,MAAM,GAAG,GAAE;AAC/B,GAAG,IAAI,OAAO,GAAG,QAAQ,GAAG,KAAK,EAAE;AACnC;AACA,IAAI,MAAM,GAAG,IAAI,iBAAiB,CAAC,MAAM,CAAC,MAAM,EAAC;AACjD,IAAI,UAAU,GAAG,MAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,EAAC;AACrG,IAAI,OAAO,GAAG,MAAM,CAAC,MAAM,GAAG,GAAE;AAChC,IAAI,QAAQ,GAAG,EAAC;AAChB,IAAI;AACJ,IAAI,QAAQ,GAAG,CAAC,QAAQ,GAAG,CAAC,IAAI,WAAU;AAC1C,GAAG,KAAK,GAAG,SAAQ;AACnB,GAAG,IAAI,aAAa,GAAG,mBAAmB,EAAE,QAAQ,KAAK,aAAa,GAAG,IAAI,EAAC;AAC9E,GAAG,YAAY,GAAG,KAAK,CAAC,eAAe,GAAG,IAAI,GAAG,EAAE,GAAG,KAAI;AAC1D,GAAG,IAAI,KAAK,CAAC,aAAa,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AACzD,IAAI,cAAc,GAAG,GAAE;AACvB,IAAI,cAAc,CAAC,IAAI,GAAG,SAAQ;AAClC,IAAI;AACJ,IAAI,cAAc,GAAG,KAAI;AACzB,GAAG,UAAU,GAAG,KAAK,CAAC,WAAU;AAChC,GAAG,IAAI,UAAU,EAAE;AACnB,IAAI,IAAI,UAAU,CAAC,aAAa;AAChC,KAAK,UAAU,GAAG,KAAK,CAAC,gBAAgB,CAAC,KAAK,CAAC,aAAa,EAAE,EAAC;AAC/D,IAAI,IAAI,YAAY,GAAG,UAAU,CAAC,YAAY,IAAI,EAAC;AACnD,IAAI,IAAI,YAAY,GAAG,mBAAmB,EAAE;AAC5C;AACA,KAAK,MAAM,IAAI,KAAK,CAAC,oGAAoG,GAAG,UAAU,CAAC,YAAY,CAAC;AACpJ,KAAK;AACL,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE;AACjC;AACA,KAAK,UAAU,CAAC,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,EAAC;AACjD,KAAK,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,EAAE;AAC5C,MAAM,IAAI,IAAI,GAAG,UAAU,CAAC,CAAC,EAAC;AAC9B,MAAM,IAAI,CAAC,IAAI;AACf,OAAO,QAAQ;AACf,MAAM,IAAI,cAAc,EAAE,UAAU,GAAG,UAAU,CAAC,YAAW;AAC7D,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AACnD,OAAO,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC,EAAC;AACxB,OAAO,cAAc,GAAG,UAAU,CAAC,GAAG,EAAC;AACvC,OAAO,IAAI,CAAC,cAAc,EAAE;AAC5B,QAAQ,cAAc,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,EAAC;AAC9D,QAAQ;AACR,OAAO,UAAU,GAAG,eAAc;AAClC,OAAO;AACP,MAAM,UAAU,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,KAAI;AAC1C,MAAM;AACN,KAAK,IAAI,CAAC,yBAAyB,GAAG,aAAY;AAClD,KAAK;AACL,IAAI,IAAI,CAAC,YAAY,EAAE;AACvB,KAAK,UAAU,CAAC,MAAM,GAAG,YAAY,GAAG,KAAI;AAC5C,KAAK;AACL,IAAI;AACJ,GAAG,IAAI,eAAe;AACtB,IAAI,eAAe,GAAG,MAAK;AAC3B,GAAG,IAAI,aAAa,CAAC;AACrB,GAAG,IAAI;AACP,IAAI,IAAI,KAAK,CAAC,qBAAqB,IAAI,KAAK,IAAI,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,WAAW,KAAK,MAAM;AACjG,KAAK,WAAW,CAAC,KAAK,CAAC,CAAC;AACxB;AACA,KAAK,IAAI,CAAC,KAAK,EAAC;AAChB,IAAI,IAAI,UAAU,GAAG,cAAc,CAAC;AACpC,IAAI,IAAI,cAAc;AACtB,KAAK,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,EAAC;AACjC,IAAI,IAAI,YAAY,IAAI,YAAY,CAAC,WAAW,EAAE;AAClD,KAAK,IAAI,WAAW,GAAG,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7F,KAAK,IAAI,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC;AAChC,KAAK,IAAI,iBAAiB,GAAG,CAAC,CAAC,CAAC;AAChC,KAAK,OAAO,UAAU,IAAI,CAAC,GAAG,CAAC,EAAE;AACjC,MAAM,IAAI,cAAc,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC;AAC3D,MAAM,IAAI,cAAc,IAAI,UAAU,CAAC,eAAe,GAAG,KAAK,CAAC,IAAI,iBAAiB,KAAK,CAAC,CAAC;AAC3F,OAAO,iBAAiB,GAAG,CAAC,CAAC;AAC7B,MAAM,IAAI,cAAc,IAAI,UAAU,CAAC,QAAQ,GAAG,KAAK,CAAC,EAAE;AAC1D,OAAO,IAAI,iBAAiB,IAAI,CAAC;AACjC,QAAQ,iBAAiB,IAAI,CAAC,CAAC;AAC/B,OAAO,MAAM;AACb,OAAO,IAAI,iBAAiB,IAAI,CAAC,EAAE;AACnC;AACA,QAAQ,UAAU,CAAC,SAAS,CAAC,UAAU,CAAC,QAAQ,GAAG,KAAK;AACxD,SAAS,UAAU,CAAC,SAAS,CAAC,UAAU,CAAC,QAAQ,GAAG,KAAK,CAAC,GAAG,iBAAiB,EAAC;AAC/E,QAAQ,iBAAiB,GAAG,CAAC,CAAC,CAAC;AAC/B,QAAQ;AACR,OAAO,UAAU,GAAG,UAAU,CAAC,QAAQ,CAAC;AACxC,OAAO,CAAC,EAAE,CAAC;AACX,OAAO;AACP,MAAM;AACN,KAAK,IAAI,iBAAiB,IAAI,CAAC,IAAI,UAAU,EAAE;AAC/C;AACA,MAAM,UAAU,CAAC,SAAS,CAAC,UAAU,CAAC,QAAQ,GAAG,KAAK;AACtD,OAAO,UAAU,CAAC,SAAS,CAAC,UAAU,CAAC,QAAQ,GAAG,KAAK,CAAC,GAAG,iBAAiB,EAAC;AAC7E,MAAM;AACN,KAAK,QAAQ,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;AACxC,KAAK,IAAI,QAAQ,GAAG,OAAO;AAC3B,MAAM,QAAQ,CAAC,QAAQ,EAAC;AACxB,KAAK,KAAK,CAAC,MAAM,GAAG,SAAQ;AAC5B,KAAK,IAAI,UAAU,GAAG,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE,WAAW,EAAC;AAC9E,KAAK,YAAY,GAAG,KAAI;AACxB,KAAK,OAAO,UAAU;AACtB,KAAK;AACL,IAAI,KAAK,CAAC,MAAM,GAAG,SAAQ;AAC3B,IAAI,IAAI,aAAa,GAAG,iBAAiB,EAAE;AAC3C,KAAK,MAAM,CAAC,KAAK,GAAG,MAAK;AACzB,KAAK,MAAM,CAAC,GAAG,GAAG,SAAQ;AAC1B,KAAK,OAAO,MAAM;AAClB,KAAK;AACL,IAAI,OAAO,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC;AAC3C,IAAI,CAAC,MAAM,KAAK,EAAE;AAClB,IAAI,aAAa,GAAG,KAAK,CAAC;AAC1B,IAAI,MAAM,KAAK,CAAC;AAChB,IAAI,SAAS;AACb,IAAI,IAAI,UAAU,EAAE;AACpB,KAAK,eAAe,EAAE,CAAC;AACvB,KAAK,IAAI,eAAe,IAAI,KAAK,CAAC,cAAc,EAAE;AAClD,MAAM,IAAI,YAAY,GAAG,UAAU,CAAC,YAAY,IAAI,EAAC;AACrD;AACA,MAAM,IAAI,YAAY,GAAG,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,EAAC;AACzD,MAAM,IAAI,aAAa,GAAGE,mBAAiB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;AAC/D,MAAM,IAAI,CAAC,aAAa,EAAE;AAC1B,OAAO,IAAI,KAAK,CAAC,cAAc,CAAC,aAAa,EAAE,aAAa,CAAC,YAAY,CAAC,KAAK,KAAK,EAAE;AACtF;AACA,QAAQ,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC;AAC/C,QAAQ;AACR,OAAO,KAAK,CAAC,yBAAyB,GAAG,aAAY;AACrD;AACA,OAAO,IAAI,MAAM,CAAC,MAAM,GAAG,UAAU,EAAE,MAAM,GAAG,KAAI;AACpD,OAAO,OAAO,YAAY;AAC1B,OAAO;AACP,MAAM;AACN,KAAK;AACL;AACA,IAAI,IAAI,MAAM,CAAC,MAAM,GAAG,UAAU,EAAE,MAAM,GAAG,KAAI;AACjD,IAAI,IAAI,aAAa,GAAG,iBAAiB;AACzC,KAAK,QAAQ,GAAG,MAAK;AACrB,IAAI;AACJ,IAAG;AACH,EAAE,MAAM,eAAe,GAAG,MAAM;AAChC,GAAG,IAAI,oCAAoC,GAAG,EAAE;AAChD,IAAI,oCAAoC,GAAE;AAC1C,GAAG,IAAI,YAAY,GAAG,UAAU,CAAC,YAAY,IAAI,EAAC;AAClD,GAAG,IAAI,UAAU,CAAC,MAAM,GAAG,YAAY,IAAI,CAAC,YAAY;AACxD,IAAI,UAAU,CAAC,MAAM,GAAG,aAAY;AACpC,GAAG,IAAI,gBAAgB,GAAG,KAAK,EAAE;AACjC;AACA,IAAI,UAAU,CAAC,WAAW,GAAG,KAAI;AACjC,IAAI,oCAAoC,GAAG,EAAC;AAC5C,IAAI,gBAAgB,GAAG,EAAC;AACxB,IAAI,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC;AACpC,KAAK,iBAAiB,GAAG,GAAE;AAC3B,IAAI,MAAM,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE;AAC7D,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,iBAAiB,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC9D,KAAK,iBAAiB,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,GAAG,EAAC;AAC5C,KAAK;AACL,IAAI,iBAAiB,GAAG,GAAE;AAC1B,IAAI;AACJ,IAAG;AACH,EAAE,MAAM,SAAS,GAAG,CAAC,KAAK,KAAK;AAC/B,GAAG,IAAI,MAAM,GAAG,KAAK,CAAC,OAAM;AAC5B,GAAG,IAAI,MAAM,GAAG,IAAI,EAAE;AACtB,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,IAAI,GAAG,OAAM;AACtC,IAAI,MAAM,IAAI,MAAM,GAAG,OAAO,EAAE;AAChC,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC7B,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,IAAI,EAAC;AACpC,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,GAAG,KAAI;AACtC,IAAI,MAAM;AACV,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC7B,IAAI,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,EAAC;AAC1C,IAAI,QAAQ,IAAI,EAAC;AACjB,IAAI;AACJ,GAAG,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;AACpC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAC;AAClB,IAAI;AACJ,IAAG;AACH,EAAE,MAAM,IAAI,GAAG,CAAC,KAAK,KAAK;AAC1B,GAAG,IAAI,QAAQ,GAAG,OAAO;AACzB,IAAI,MAAM,GAAG,QAAQ,CAAC,QAAQ,EAAC;AAC/B;AACA,GAAG,IAAI,IAAI,GAAG,OAAO,MAAK;AAC1B,GAAG,IAAI,OAAM;AACb,GAAG,IAAI,IAAI,KAAK,QAAQ,EAAE;AAC1B,IAAI,IAAI,SAAS,GAAG,KAAK,CAAC,OAAM;AAChC,IAAI,IAAI,cAAc,IAAI,SAAS,IAAI,CAAC,IAAI,SAAS,GAAG,MAAM,EAAE;AAChE,KAAK,IAAI,CAAC,cAAc,CAAC,IAAI,IAAI,SAAS,IAAI,eAAe,EAAE;AAC/D,MAAM,IAAI,SAAQ;AAClB,MAAM,IAAI,QAAQ,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,GAAE;AAC3G,MAAM,IAAI,QAAQ,GAAG,QAAQ,GAAG,OAAO;AACvC,OAAO,MAAM,GAAG,QAAQ,CAAC,QAAQ,GAAG,QAAQ,EAAC;AAC7C,MAAM,IAAI,WAAU;AACpB,MAAM,IAAI,cAAc,CAAC,QAAQ,EAAE;AACnC,OAAO,UAAU,GAAG,eAAc;AAClC,OAAO,MAAM,CAAC,QAAQ,CAAC,GAAG,KAAI;AAC9B,OAAO,QAAQ,IAAI,EAAC;AACpB,OAAO,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAChC,OAAO,QAAQ,GAAG,QAAQ,GAAG,MAAK;AAClC,OAAO,QAAQ,IAAI,EAAC;AACpB,OAAO,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,EAAC;AACnC,OAAO,UAAU,CAAC,SAAS,CAAC,QAAQ,GAAG,KAAK,GAAG,CAAC,EAAE,QAAQ,GAAG,KAAK,GAAG,QAAQ,EAAC;AAC9E,OAAO,MAAM;AACb,OAAO,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAChC,OAAO,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAChC,OAAO,QAAQ,GAAG,QAAQ,GAAG,MAAK;AAClC,OAAO,QAAQ,IAAI,EAAC;AACpB,OAAO;AACP,MAAM,cAAc,GAAG,CAAC,EAAE,EAAE,EAAE,EAAC;AAC/B,MAAM,cAAc,CAAC,QAAQ,GAAG,UAAU,CAAC;AAC3C,MAAM,cAAc,CAAC,IAAI,GAAG,EAAC;AAC7B,MAAM,cAAc,CAAC,QAAQ,GAAG,SAAQ;AACxC,MAAM;AACN,KAAK,IAAI,OAAO,GAAG,WAAW,CAAC,IAAI,CAAC,KAAK,EAAC;AAC1C,KAAK,cAAc,CAAC,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,MAAK;AAC7C,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC9B,KAAK,IAAI,CAAC,OAAO,GAAG,CAAC,SAAS,GAAG,SAAS,CAAC,CAAC;AAC5C,KAAK,MAAM;AACX,KAAK;AACL,IAAI,IAAI,WAAU;AAClB;AACA,IAAI,IAAI,SAAS,GAAG,IAAI,EAAE;AAC1B,KAAK,UAAU,GAAG,EAAC;AACnB,KAAK,MAAM,IAAI,SAAS,GAAG,KAAK,EAAE;AAClC,KAAK,UAAU,GAAG,EAAC;AACnB,KAAK,MAAM,IAAI,SAAS,GAAG,OAAO,EAAE;AACpC,KAAK,UAAU,GAAG,EAAC;AACnB,KAAK,MAAM;AACX,KAAK,UAAU,GAAG,EAAC;AACnB,KAAK;AACL,IAAI,IAAI,QAAQ,GAAG,SAAS,GAAG,EAAC;AAChC,IAAI,IAAI,QAAQ,GAAG,QAAQ,GAAG,OAAO;AACrC,KAAK,MAAM,GAAG,QAAQ,CAAC,QAAQ,GAAG,QAAQ,EAAC;AAC3C;AACA,IAAI,IAAI,SAAS,GAAG,IAAI,IAAI,CAAC,UAAU,EAAE;AACzC,KAAK,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,WAAW,GAAG,QAAQ,GAAG,WAAU;AACvD,KAAK,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;AACrC,MAAM,EAAE,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,EAAC;AAC9B,MAAM,IAAI,EAAE,GAAG,IAAI,EAAE;AACrB,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,GAAE;AACjC,OAAO,MAAM,IAAI,EAAE,GAAG,KAAK,EAAE;AAC7B,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,KAAI;AAC7C,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,GAAG,KAAI;AAC/C,OAAO,MAAM;AACb,OAAO,CAAC,EAAE,GAAG,MAAM,MAAM,MAAM;AAC/B,OAAO,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,MAAM,MAAM,MAAM;AAC3D,QAAQ;AACR,OAAO,EAAE,GAAG,OAAO,IAAI,CAAC,EAAE,GAAG,MAAM,KAAK,EAAE,CAAC,IAAI,EAAE,GAAG,MAAM,EAAC;AAC3D,OAAO,CAAC,GAAE;AACV,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,KAAI;AAC9C,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,GAAG,KAAI;AACrD,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,GAAG,KAAI;AACpD,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,GAAG,KAAI;AAC/C,OAAO,MAAM;AACb,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,KAAI;AAC9C,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,GAAG,KAAI;AACpD,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,GAAG,KAAI;AAC/C,OAAO;AACP,MAAM;AACN,KAAK,MAAM,GAAG,WAAW,GAAG,QAAQ,GAAG,WAAU;AACjD,KAAK,MAAM;AACX,KAAK,MAAM,GAAG,UAAU,CAAC,KAAK,EAAE,QAAQ,GAAG,UAAU,EAAC;AACtD,KAAK;AACL;AACA,IAAI,IAAI,MAAM,GAAG,IAAI,EAAE;AACvB,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,IAAI,GAAG,OAAM;AACvC,KAAK,MAAM,IAAI,MAAM,GAAG,KAAK,EAAE;AAC/B,KAAK,IAAI,UAAU,GAAG,CAAC,EAAE;AACzB,MAAM,MAAM,CAAC,UAAU,CAAC,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,CAAC,GAAG,MAAM,EAAC;AAC1E,MAAM;AACN,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC9B,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,OAAM;AAChC,KAAK,MAAM,IAAI,MAAM,GAAG,OAAO,EAAE;AACjC,KAAK,IAAI,UAAU,GAAG,CAAC,EAAE;AACzB,MAAM,MAAM,CAAC,UAAU,CAAC,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,CAAC,GAAG,MAAM,EAAC;AAC1E,MAAM;AACN,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC9B,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,IAAI,EAAC;AACrC,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,GAAG,KAAI;AACvC,KAAK,MAAM;AACX,KAAK,IAAI,UAAU,GAAG,CAAC,EAAE;AACzB,MAAM,MAAM,CAAC,UAAU,CAAC,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,CAAC,GAAG,MAAM,EAAC;AAC1E,MAAM;AACN,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC9B,KAAK,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,EAAC;AAC3C,KAAK,QAAQ,IAAI,EAAC;AAClB,KAAK;AACL,IAAI,QAAQ,IAAI,OAAM;AACtB,IAAI,MAAM,IAAI,IAAI,KAAK,QAAQ,EAAE;AACjC,IAAI,IAAI,KAAK,KAAK,CAAC,KAAK,KAAK,EAAE;AAC/B;AACA,KAAK,IAAI,KAAK,GAAG,IAAI,KAAK,KAAK,GAAG,IAAI,IAAI,IAAI,CAAC,UAAU,KAAK,KAAK,CAAC,KAAK,KAAK,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE;AACvH,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAK;AAChC,MAAM,MAAM,IAAI,KAAK,GAAG,KAAK,EAAE;AAC/B,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC/B,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAK;AAChC,MAAM,MAAM,IAAI,KAAK,GAAG,OAAO,EAAE;AACjC,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC/B,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,IAAI,EAAC;AACrC,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,GAAG,KAAI;AACvC,MAAM,MAAM;AACZ,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC/B,MAAM,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,KAAK,EAAC;AAC3C,MAAM,QAAQ,IAAI,EAAC;AACnB,MAAM;AACN,KAAK,MAAM,IAAI,KAAK,IAAI,CAAC,KAAK,KAAK,EAAE;AACrC,KAAK,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;AACzB,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,GAAG,MAAK;AACxC,MAAM,MAAM,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;AAChC,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC/B,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,GAAG,MAAK;AACxC,MAAM,MAAM,IAAI,KAAK,IAAI,CAAC,MAAM,EAAE;AAClC,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC/B,MAAM,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAE,KAAK,EAAC;AAC1C,MAAM,QAAQ,IAAI,EAAC;AACnB,MAAM,MAAM;AACZ,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC/B,MAAM,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAE,KAAK,EAAC;AAC1C,MAAM,QAAQ,IAAI,EAAC;AACnB,MAAM;AACN,KAAK,MAAM;AACX,KAAK,IAAI,WAAU;AACnB,KAAK,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,KAAK,GAAG,WAAW,IAAI,KAAK,IAAI,CAAC,UAAU,EAAE;AAC5F,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC/B,MAAM,UAAU,CAAC,UAAU,CAAC,QAAQ,EAAE,KAAK,EAAC;AAC5C,MAAM,IAAI,SAAQ;AAClB,MAAM,IAAI,UAAU,GAAG,CAAC;AACxB;AACA,QAAQ,CAAC,CAAC,QAAQ,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,IAAI,KAAK,CAAC,KAAK,MAAM,CAAC,QAAQ,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,QAAQ,EAAE;AACzH,OAAO,QAAQ,IAAI,EAAC;AACpB,OAAO,MAAM;AACb,OAAO;AACP,OAAO,QAAQ,GAAE;AACjB,MAAM;AACN,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC9B,KAAK,UAAU,CAAC,UAAU,CAAC,QAAQ,EAAE,KAAK,EAAC;AAC3C,KAAK,QAAQ,IAAI,EAAC;AAClB,KAAK;AACL,IAAI,MAAM,IAAI,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,UAAU,EAAE;AACxD,IAAI,IAAI,CAAC,KAAK;AACd,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC9B,SAAS;AACT,KAAK,IAAI,YAAY,EAAE;AACvB,MAAM,IAAI,OAAO,GAAG,YAAY,CAAC,GAAG,CAAC,KAAK,EAAC;AAC3C,MAAM,IAAI,OAAO,EAAE;AACnB,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE;AACxB,QAAQ,IAAI,WAAW,GAAG,YAAY,CAAC,WAAW,KAAK,YAAY,CAAC,WAAW,GAAG,EAAE,EAAC;AACrF,QAAQ,OAAO,CAAC,EAAE,GAAG,WAAW,CAAC,IAAI,CAAC,OAAO,EAAC;AAC9C,QAAQ;AACR,OAAO,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAChC,OAAO,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAChC,OAAO,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAE,EAAC;AACjD,OAAO,QAAQ,IAAI,EAAC;AACpB,OAAO,MAAM;AACb,OAAO;AACP,OAAO,YAAY,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,MAAM,EAAE,QAAQ,GAAG,KAAK,EAAE,EAAC;AAC5D,MAAM;AACN,KAAK,IAAI,WAAW,GAAG,KAAK,CAAC,YAAW;AACxC,KAAK,IAAI,WAAW,KAAK,MAAM,EAAE;AACjC,MAAM,WAAW,CAAC,KAAK,EAAC;AACxB,MAAM,MAAM,IAAI,WAAW,KAAK,KAAK,EAAE;AACvC,MAAM,SAAS,CAAC,KAAK,EAAC;AACtB,MAAM,MAAM,IAAI,WAAW,KAAK,GAAG,EAAE;AACrC,MAAM,IAAI,IAAI,CAAC,gBAAgB,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC1D,WAAW;AACX,OAAO,MAAM,GAAG,KAAK,CAAC,KAAI;AAC1B,OAAO,IAAI,MAAM,GAAG,IAAI,EAAE;AAC1B,QAAQ,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,IAAI,GAAG,OAAM;AAC1C,QAAQ,MAAM,IAAI,MAAM,GAAG,OAAO,EAAE;AACpC,QAAQ,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AACjC,QAAQ,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,IAAI,EAAC;AACxC,QAAQ,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,GAAG,KAAI;AAC1C,QAAQ,MAAM;AACd,QAAQ,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AACjC,QAAQ,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,EAAC;AAC9C,QAAQ,QAAQ,IAAI,EAAC;AACrB,QAAQ;AACR,OAAO,KAAK,IAAI,CAAC,GAAG,EAAE,UAAU,CAAC,IAAI,KAAK,EAAE;AAC5C,QAAQ,IAAI,CAAC,GAAG,EAAC;AACjB,QAAQ,IAAI,CAAC,UAAU,EAAC;AACxB,QAAQ;AACR,OAAO;AACP,MAAM,MAAM;AACZ,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AACzD,OAAO,IAAI,cAAc,GAAG,gBAAgB,CAAC,CAAC,EAAC;AAC/C,OAAO,IAAI,KAAK,YAAY,cAAc,EAAE;AAC5C,QAAQ,IAAI,SAAS,GAAG,UAAU,CAAC,CAAC,EAAC;AACrC,QAAQ,IAAI,SAAS,CAAC,KAAK,EAAE;AAC7B,SAAS,IAAI,SAAS,CAAC,IAAI,EAAE;AAC7B,UAAU,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AACnC,UAAU,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,SAAS,CAAC,KAAI;AAC7C,UAAU,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAC;AAChC,UAAU;AACV,SAAS,IAAI,WAAW,GAAG,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAC;AAC5D,SAAS,IAAI,WAAW,KAAK,KAAK,EAAE;AACpC,UAAU,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AACpC,WAAW,SAAS,CAAC,KAAK,EAAC;AAC3B,WAAW,MAAM;AACjB,WAAW,WAAW,CAAC,KAAK,EAAC;AAC7B,WAAW;AACX,UAAU,MAAM;AAChB,UAAU,IAAI,CAAC,WAAW,EAAC;AAC3B,UAAU;AACV,SAAS,MAAM;AACf,SAAS;AACT,QAAQ,IAAI,aAAa,GAAG,OAAM;AAClC,QAAQ,IAAI,iBAAiB,GAAG,WAAU;AAC1C,QAAQ,IAAI,eAAe,GAAG,SAAQ;AACtC,QAAQ,MAAM,GAAG,KAAI;AACrB,QAAQ,IAAI,OAAM;AAClB,QAAQ,IAAI;AACZ,SAAS,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,IAAI,KAAK;AAC7D;AACA,UAAU,MAAM,GAAG,cAAa;AAChC,UAAU,aAAa,GAAG,KAAI;AAC9B,UAAU,QAAQ,IAAI,KAAI;AAC1B,UAAU,IAAI,QAAQ,GAAG,OAAO;AAChC,WAAW,QAAQ,CAAC,QAAQ,EAAC;AAC7B,UAAU,OAAO;AACjB,WAAW,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,GAAG,IAAI;AACxD,WAAW;AACX,UAAU,EAAE,IAAI,EAAC;AACjB,SAAS,SAAS;AAClB;AACA,SAAS,IAAI,aAAa,EAAE;AAC5B,UAAU,MAAM,GAAG,cAAa;AAChC,UAAU,UAAU,GAAG,kBAAiB;AACxC,UAAU,QAAQ,GAAG,gBAAe;AACpC,UAAU,OAAO,GAAG,MAAM,CAAC,MAAM,GAAG,GAAE;AACtC,UAAU;AACV,SAAS;AACT,QAAQ,IAAI,MAAM,EAAE;AACpB,SAAS,IAAI,MAAM,CAAC,MAAM,GAAG,QAAQ,GAAG,OAAO;AAC/C,UAAU,QAAQ,CAAC,MAAM,CAAC,MAAM,GAAG,QAAQ,EAAC;AAC5C,SAAS,QAAQ,GAAG,kBAAkB,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,CAAC,IAAI,EAAC;AAChF,SAAS;AACT,QAAQ,MAAM;AACd,QAAQ;AACR,OAAO;AACP;AACA,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AAChC,OAAO,SAAS,CAAC,KAAK,EAAC;AACvB,OAAO,MAAM;AACb;AACA,OAAO,IAAI,KAAK,CAAC,MAAM,EAAE;AACzB,QAAQ,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,GAAE;AACnC;AACA,QAAQ,IAAI,IAAI,KAAK,KAAK;AAC1B,SAAS,OAAO,IAAI,CAAC,IAAI,CAAC;AAC1B,QAAQ;AACR;AACA;AACA,OAAO,IAAI,IAAI,KAAK,UAAU;AAC9B,QAAQ,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;AACrE;AACA;AACA,OAAO,WAAW,CAAC,KAAK,EAAC;AACzB,OAAO;AACP,MAAM;AACN,KAAK;AACL,IAAI,MAAM,IAAI,IAAI,KAAK,SAAS,EAAE;AAClC,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,GAAG,IAAI,GAAG,KAAI;AAC5C,IAAI,MAAM,IAAI,IAAI,KAAK,QAAQ,EAAE;AACjC,IAAI,IAAI,KAAK,GAAG,kBAAkB,IAAI,KAAK,IAAI,CAAC,kBAAkB,EAAE;AACpE;AACA,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC9B,KAAK,UAAU,CAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,EAAC;AAC5C,KAAK,MAAM,IAAI,KAAK,GAAG,mBAAmB,IAAI,KAAK,GAAG,CAAC,EAAE;AACzD;AACA,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC9B,KAAK,UAAU,CAAC,YAAY,CAAC,QAAQ,EAAE,KAAK,EAAC;AAC7C,KAAK,MAAM;AACX;AACA,KAAK,IAAI,IAAI,CAAC,kBAAkB,EAAE;AAClC,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC/B,MAAM,UAAU,CAAC,UAAU,CAAC,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,EAAC;AACpD,MAAM,MAAM,IAAI,IAAI,CAAC,mBAAmB,EAAE;AAC1C,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;AACpC,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;AACtI,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC/B,MAAM,QAAQ,EAAE,CAAC;AACjB,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC/B,MAAM,IAAI,KAAK,GAAG,EAAE,CAAC;AACrB,MAAM,IAAI,WAAW,CAAC;AACtB,MAAM,GAAG;AACT,OAAO,IAAI,IAAI,GAAG,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;AACvC,OAAO,WAAW,GAAG,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9F,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACxB,OAAO,KAAK,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC;AAC3B,OAAO,QAAQ,EAAE,CAAC,KAAK,KAAK,MAAM,CAAC,CAAC,CAAC,IAAI,KAAK,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,WAAW,CAAC,EAAE;AAChF,MAAM,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;AACxC,MAAM,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG;AACzC,OAAO,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC/C,OAAO;AACP,MAAM,MAAM;AACZ,MAAM,MAAM;AACZ,MAAM,MAAM,IAAI,UAAU,CAAC,KAAK,GAAG,iEAAiE;AACpG,OAAO,+EAA+E;AACtF,OAAO,2CAA2C,CAAC;AACnD,MAAM;AACN,KAAK;AACL,IAAI,QAAQ,IAAI,EAAC;AACjB,IAAI,MAAM,IAAI,IAAI,KAAK,WAAW,EAAE;AACpC,IAAI,IAAI,IAAI,CAAC,oBAAoB;AACjC,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC9B,SAAS;AACT,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC9B,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAC;AAC3B,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAC;AAC3B,KAAK;AACL,IAAI,MAAM;AACV,IAAI,MAAM,IAAI,KAAK,CAAC,gBAAgB,GAAG,IAAI,CAAC;AAC5C,IAAI;AACJ,IAAG;AACH;AACA,EAAE,MAAM,gBAAgB,GAAG,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,MAAM,KAAK;AAChH;AACA,GAAG,IAAI,IAAI,CAAC;AACZ,GAAG,IAAI,IAAI,CAAC,UAAU,EAAE;AACxB,IAAI,IAAI,GAAG,EAAE,CAAC;AACd,IAAI,KAAK,IAAI,GAAG,IAAI,MAAM,EAAE;AAC5B,KAAK,IAAI,CAAC,OAAO,MAAM,CAAC,cAAc,KAAK,UAAU,IAAI,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC;AACnF,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAC5C,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACrB,KAAK;AACL,IAAI,MAAM;AACV,IAAI,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,EAAC;AAC9B,IAAI;AACJ,GAAG,IAAI,MAAM,GAAG,IAAI,CAAC,OAAM;AAC3B,GAAG,IAAI,MAAM,GAAG,IAAI,EAAE;AACtB,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,IAAI,GAAG,OAAM;AACtC,IAAI,MAAM,IAAI,MAAM,GAAG,OAAO,EAAE;AAChC,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC7B,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,IAAI,EAAC;AACpC,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,GAAG,KAAI;AACtC,IAAI,MAAM;AACV,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC7B,IAAI,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,EAAC;AAC1C,IAAI,QAAQ,IAAI,EAAC;AACjB,IAAI;AACJ,GAAG,IAAI,IAAG;AACV,GAAG,IAAI,IAAI,CAAC,oBAAoB,EAAE;AAClC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;AACrC,KAAK,GAAG,GAAG,IAAI,CAAC,CAAC,EAAC;AAClB,KAAK,IAAI,GAAG,GAAG,MAAM,CAAC,GAAG,EAAC;AAC1B,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,EAAC;AACjC,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAC;AACtB,KAAK;AACL;AACA,IAAI,MAAM;AACV,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;AACrC,KAAK,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,EAAC;AACxB,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAC;AACtB,KAAK;AACL,IAAI;AACJ,GAAG;AACH,EAAE,CAAC,MAAM,KAAK;AACd,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC5B,GAAG,IAAI,YAAY,GAAG,QAAQ,GAAG,MAAK;AACtC,GAAG,QAAQ,IAAI,EAAC;AAChB,GAAG,IAAI,IAAI,GAAG,EAAC;AACf,GAAG,KAAK,IAAI,GAAG,IAAI,MAAM,EAAE;AAC3B,IAAI,IAAI,OAAO,MAAM,CAAC,cAAc,KAAK,UAAU,IAAI,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;AACnF,KAAK,IAAI,CAAC,GAAG,EAAC;AACd,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAC;AACtB,KAAK,IAAI,GAAE;AACX,KAAK;AACL,IAAI;AACJ,GAAG,IAAI,IAAI,GAAG,MAAM,EAAE;AACtB,IAAI,MAAM,IAAI,KAAK,CAAC,6DAA6D;AACjF,IAAI,4DAA4D,CAAC,CAAC;AAClE,IAAI;AACJ,GAAG,MAAM,CAAC,YAAY,EAAE,GAAG,KAAK,CAAC,GAAG,IAAI,IAAI,EAAC;AAC7C,GAAG,MAAM,CAAC,YAAY,GAAG,KAAK,CAAC,GAAG,IAAI,GAAG,KAAI;AAC7C,IAAG;AACH;AACA,EAAE,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,KAAK,KAAK,GAAG,gBAAgB;AAClE,EAAE,CAAC,OAAO,CAAC,kBAAkB,IAAI,CAAC,iBAAiB;AACnD,EAAE,CAAC,MAAM,KAAK;AACd,GAAG,IAAI,cAAc,EAAE,UAAU,GAAG,UAAU,CAAC,WAAW,KAAK,UAAU,CAAC,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAC;AAC5G,GAAG,IAAI,YAAY,GAAG,QAAQ,EAAE,GAAG,MAAK;AACxC,GAAG,IAAI,UAAS;AAChB,GAAG,KAAK,IAAI,GAAG,IAAI,MAAM,EAAE;AAC3B,IAAI,IAAI,OAAO,MAAM,CAAC,cAAc,KAAK,UAAU,IAAI,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;AACnF,KAAK,cAAc,GAAG,UAAU,CAAC,GAAG,EAAC;AACrC,KAAK,IAAI,cAAc;AACvB,MAAM,UAAU,GAAG,eAAc;AACjC,UAAU;AACV;AACA,MAAM,IAAI,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,EAAC;AACpC,MAAM,IAAI,cAAc,GAAG,WAAU;AACrC,MAAM,UAAU,GAAG,UAAU,CAAC,YAAW;AACzC,MAAM,IAAI,cAAc,GAAG,EAAC;AAC5B,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AACnD,OAAO,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC,EAAC;AACxB,OAAO,cAAc,GAAG,UAAU,CAAC,GAAG,EAAC;AACvC,OAAO,IAAI,CAAC,cAAc,EAAE;AAC5B,QAAQ,cAAc,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,EAAC;AAC9D,QAAQ,cAAc,GAAE;AACxB,QAAQ;AACR,OAAO,UAAU,GAAG,eAAc;AAClC,OAAO;AACP,MAAM,IAAI,YAAY,GAAG,KAAK,GAAG,CAAC,IAAI,QAAQ,EAAE;AAChD;AACA,OAAO,QAAQ,GAAE;AACjB,OAAO,SAAS,CAAC,UAAU,EAAE,IAAI,EAAE,cAAc,EAAC;AAClD,OAAO;AACP,OAAO,eAAe,CAAC,UAAU,EAAE,IAAI,EAAE,YAAY,EAAE,cAAc,EAAC;AACtE,MAAM,SAAS,GAAG,KAAI;AACtB,MAAM,UAAU,GAAG,cAAc,CAAC,GAAG,EAAC;AACtC,MAAM;AACN,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAC;AACtB,KAAK;AACL,IAAI;AACJ,GAAG,IAAI,CAAC,SAAS,EAAE;AACnB,IAAI,IAAI,QAAQ,GAAG,UAAU,CAAC,aAAa,EAAC;AAC5C,IAAI,IAAI,QAAQ;AAChB,KAAK,MAAM,CAAC,YAAY,GAAG,KAAK,CAAC,GAAG,SAAQ;AAC5C;AACA,KAAK,eAAe,CAAC,UAAU,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,YAAY,EAAE,CAAC,EAAC;AACtE,IAAI;AACJ,GAAG;AACH,EAAE,CAAC,MAAM,KAAK;AACd,GAAG,IAAI,cAAc,EAAE,UAAU,GAAG,UAAU,CAAC,WAAW,KAAK,UAAU,CAAC,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAC;AAC5G,GAAG,IAAI,cAAc,GAAG,EAAC;AACzB,GAAG,KAAK,IAAI,GAAG,IAAI,MAAM,EAAE,IAAI,OAAO,MAAM,CAAC,cAAc,KAAK,UAAU,IAAI,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;AAC1G,IAAI,cAAc,GAAG,UAAU,CAAC,GAAG,EAAC;AACpC,IAAI,IAAI,CAAC,cAAc,EAAE;AACzB,KAAK,cAAc,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,EAAC;AAC3D,KAAK,cAAc,GAAE;AACrB,KAAK;AACL,IAAI,UAAU,GAAG,eAAc;AAC/B,IAAI;AACJ,GAAG,IAAI,QAAQ,GAAG,UAAU,CAAC,aAAa,EAAC;AAC3C,GAAG,IAAI,QAAQ,EAAE;AACjB,IAAI,IAAI,QAAQ,IAAI,IAAI,IAAI,iBAAiB,EAAE;AAC/C,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,CAAC,QAAQ,IAAI,IAAI,IAAI,IAAI,IAAI,KAAI;AAC5D,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,QAAQ,IAAI,EAAC;AACvC,KAAK;AACL,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,SAAQ;AAClC,IAAI,MAAM;AACV,IAAI,SAAS,CAAC,UAAU,EAAE,UAAU,CAAC,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,cAAc,EAAC;AACrF,IAAI;AACJ;AACA,GAAG,KAAK,IAAI,GAAG,IAAI,MAAM;AACzB,IAAI,IAAI,OAAO,MAAM,CAAC,cAAc,KAAK,UAAU,IAAI,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;AACnF,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAC;AACtB,KAAK;AACL,IAAG;AACH;AACA;AACA,EAAE,MAAM,eAAe,GAAG,OAAO,IAAI,CAAC,UAAU,IAAI,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC;AAClF;AACA,EAAE,MAAM,WAAW,GAAG,eAAe,GAAG,CAAC,MAAM,KAAK;AACpD,GAAG,eAAe,CAAC,MAAM,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAC;AAC3E,GAAG,GAAG,YAAW;AACjB;AACA,EAAE,MAAM,QAAQ,GAAG,CAAC,GAAG,KAAK;AAC5B,GAAG,IAAI,QAAO;AACd,GAAG,IAAI,GAAG,GAAG,SAAS,EAAE;AACxB;AACA,IAAI,IAAI,CAAC,GAAG,GAAG,KAAK,IAAI,eAAe;AACvC,KAAK,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC;AAC9E,IAAI,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe;AACtC,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,KAAK,KAAK,GAAG,GAAG,SAAS,GAAG,IAAI,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,EAAC;AACpG,IAAI;AACJ,IAAI,OAAO,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,KAAK,KAAK,CAAC,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,GAAE;AACjF,GAAG,IAAI,SAAS,GAAG,IAAI,iBAAiB,CAAC,OAAO,EAAC;AACjD,GAAG,UAAU,GAAG,SAAS,CAAC,QAAQ,KAAK,SAAS,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,OAAO,CAAC,EAAC;AACvG,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,MAAM,EAAC;AACrC,GAAG,IAAI,MAAM,CAAC,IAAI;AAClB,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,EAAC;AACzC;AACA,IAAI,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,EAAC;AAC3C,GAAG,QAAQ,IAAI,MAAK;AACpB,GAAG,KAAK,GAAG,EAAC;AACZ,GAAG,OAAO,GAAG,SAAS,CAAC,MAAM,GAAG,GAAE;AAClC,GAAG,OAAO,MAAM,GAAG,SAAS;AAC5B,IAAG;AACH,EAAE,MAAM,SAAS,GAAG,CAAC,UAAU,EAAE,IAAI,EAAE,cAAc,KAAK;AAC1D,GAAG,IAAI,QAAQ,GAAG,UAAU,CAAC,OAAM;AACnC,GAAG,IAAI,CAAC,QAAQ;AAChB,IAAI,QAAQ,GAAG,KAAI;AACnB,GAAG,IAAI,QAAQ,GAAG,aAAa,IAAI,IAAI,CAAC,oBAAoB,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE;AAClG,IAAI,QAAQ,GAAG,UAAU,CAAC,UAAS;AACnC,IAAI,IAAI,EAAE,QAAQ,GAAG,cAAc,CAAC;AACpC,KAAK,QAAQ,GAAG,cAAa;AAC7B,IAAI,UAAU,CAAC,SAAS,GAAG,QAAQ,GAAG,EAAC;AACvC,IAAI,MAAM;AACV,IAAI,IAAI,QAAQ,IAAI,cAAc;AAClC,KAAK,QAAQ,GAAG,cAAa;AAC7B,IAAI,UAAU,CAAC,MAAM,GAAG,QAAQ,GAAG,EAAC;AACpC,IAAI;AACJ,GAAG,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,QAAQ,IAAI,IAAI,IAAI,iBAAiB,GAAG,CAAC,QAAQ,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,EAAC;AACrG,GAAG,UAAU,CAAC,aAAa,CAAC,GAAG,SAAQ;AACvC,GAAG,UAAU,CAAC,QAAQ,GAAG,KAAI;AAC7B,GAAG,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,KAAI;AACrC;AACA,GAAG,IAAI,QAAQ,GAAG,aAAa,EAAE;AACjC,IAAI,IAAI,CAAC,QAAQ,GAAG,KAAI;AACxB,IAAI,UAAU,CAAC,YAAY,GAAG,QAAQ,GAAG,KAAI;AAC7C,IAAI,eAAe,GAAG,KAAI;AAC1B,IAAI,IAAI,QAAQ,IAAI,CAAC,EAAE;AACvB,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,IAAI,KAAI;AAClD,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,SAAQ;AAClC,KAAK,MAAM;AACX,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,SAAQ;AAClC,KAAK;AACL,IAAI,MAAM;AACV,IAAI,IAAI,QAAQ,IAAI,CAAC,EAAE;AACvB,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC9B,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC9B,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,IAAI,KAAI;AAClD,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,SAAQ;AAClC,KAAK,MAAM;AACX,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC9B,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC9B,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,SAAQ;AAClC,KAAK;AACL;AACA,IAAI,IAAI,cAAc;AACtB,KAAK,gBAAgB,IAAI,oCAAoC,GAAG,eAAc;AAC9E;AACA,IAAI,IAAI,iBAAiB,CAAC,MAAM,IAAI,gBAAgB;AACpD,KAAK,iBAAiB,CAAC,KAAK,EAAE,CAAC,aAAa,CAAC,GAAG,EAAC;AACjD,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,EAAC;AACtC,IAAI,IAAI,CAAC,IAAI,EAAC;AACd,IAAI;AACJ,IAAG;AACH,EAAE,MAAM,eAAe,GAAG,CAAC,UAAU,EAAE,IAAI,EAAE,eAAe,EAAE,cAAc,KAAK;AACjF,GAAG,IAAI,UAAU,GAAG,OAAM;AAC1B,GAAG,IAAI,YAAY,GAAG,SAAQ;AAC9B,GAAG,IAAI,WAAW,GAAG,QAAO;AAC5B,GAAG,IAAI,SAAS,GAAG,MAAK;AACxB,GAAG,MAAM,GAAG,WAAU;AACtB,GAAG,QAAQ,GAAG,EAAC;AACf,GAAG,KAAK,GAAG,EAAC;AACZ,GAAG,IAAI,CAAC,MAAM;AACd,IAAI,UAAU,GAAG,MAAM,GAAG,IAAI,iBAAiB,CAAC,IAAI,EAAC;AACrD,GAAG,OAAO,GAAG,MAAM,CAAC,MAAM,GAAG,GAAE;AAC/B,GAAG,SAAS,CAAC,UAAU,EAAE,IAAI,EAAE,cAAc,EAAC;AAC9C,GAAG,UAAU,GAAG,OAAM;AACtB,GAAG,IAAI,YAAY,GAAG,SAAQ;AAC9B,GAAG,MAAM,GAAG,WAAU;AACtB,GAAG,QAAQ,GAAG,aAAY;AAC1B,GAAG,OAAO,GAAG,YAAW;AACxB,GAAG,KAAK,GAAG,UAAS;AACpB,GAAG,IAAI,YAAY,GAAG,CAAC,EAAE;AACzB,IAAI,IAAI,MAAM,GAAG,QAAQ,GAAG,YAAY,GAAG,EAAC;AAC5C,IAAI,IAAI,MAAM,GAAG,OAAO;AACxB,KAAK,QAAQ,CAAC,MAAM,EAAC;AACrB,IAAI,IAAI,iBAAiB,GAAG,eAAe,GAAG,MAAK;AACnD,IAAI,MAAM,CAAC,UAAU,CAAC,iBAAiB,GAAG,YAAY,EAAE,iBAAiB,GAAG,CAAC,EAAE,QAAQ,EAAC;AACxF,IAAI,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,CAAC,EAAE,iBAAiB,EAAC;AACpE,IAAI,QAAQ,GAAG,OAAM;AACrB,IAAI,MAAM;AACV,IAAI,MAAM,CAAC,eAAe,GAAG,KAAK,CAAC,GAAG,UAAU,CAAC,CAAC,EAAC;AACnD,IAAI;AACJ,IAAG;AACH,EAAE,MAAM,WAAW,GAAG,CAAC,MAAM,KAAK;AAClC,GAAG,IAAI,WAAW,GAAG,gBAAgB,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC,KAAK,EAAE,WAAW,EAAE,kBAAkB,KAAK;AACzI,IAAI,IAAI,kBAAkB;AAC1B,KAAK,OAAO,eAAe,GAAG,IAAI,CAAC;AACnC,IAAI,QAAQ,GAAG,WAAW,CAAC;AAC3B,IAAI,IAAI,WAAW,GAAG,MAAM,CAAC;AAC7B,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;AAChB,IAAI,eAAe,EAAE,CAAC;AACtB,IAAI,IAAI,WAAW,KAAK,MAAM,EAAE;AAChC,KAAK,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,CAAC;AAC7C,KAAK;AACL,IAAI,OAAO,QAAQ,CAAC;AACpB,IAAI,EAAE,IAAI,CAAC,CAAC;AACZ,GAAG,IAAI,WAAW,KAAK,CAAC;AACxB,IAAI,OAAO,WAAW,CAAC,MAAM,CAAC,CAAC;AAC/B,GAAG,QAAQ,GAAG,WAAW,CAAC;AAC1B,IAAG;AACH,EAAE;AACF,CAAC,SAAS,CAAC,MAAM,EAAE;AACnB;AACA,EAAE,MAAM,GAAG,OAAM;AACjB,EAAE,MAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC,EAAC;AAC1G,EAAE,QAAQ,GAAG,EAAC;AACd,EAAE;AACF,CAAC,IAAI,QAAQ,CAAC,CAAC,KAAK,EAAE;AACtB,EAAE,QAAQ,GAAG,KAAK,CAAC;AACnB,EAAE;AACF,CAAC,IAAI,QAAQ,GAAG;AAChB,EAAE,OAAO,QAAQ,CAAC;AAClB,EAAE;AACF,CAAC,eAAe,GAAG;AACnB,EAAE,IAAI,IAAI,CAAC,UAAU;AACrB,GAAG,IAAI,CAAC,UAAU,GAAG,GAAE;AACvB,EAAE,IAAI,IAAI,CAAC,YAAY;AACvB,GAAG,IAAI,CAAC,YAAY,GAAG,GAAE;AACzB,EAAE;AACF,CAAC;AACD;AACA,gBAAgB,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,WAAW,iBAAiB,QAAQ,EAAE,MAAM,GAAE;AACtJ,UAAU,GAAG,CAAC;AACd,CAAC,IAAI,CAAC,IAAI,EAAE,gBAAgB,EAAE,IAAI,EAAE;AACpC,EAAE,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,EAAE,GAAG,KAAI;AACrC,EAAE,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,eAAe,EAAE,KAAK,CAAC,KAAK,OAAO,IAAI,CAAC,IAAI,OAAO,GAAG,WAAW,EAAE;AACtG;AACA,GAAG,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,CAAC,GAAG,gBAAgB,CAAC,CAAC,EAAC;AAC5D,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC5B,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC5B,GAAG,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,OAAO,EAAC;AAC1C,GAAG,MAAM,IAAI,OAAO,GAAG,CAAC,IAAI,OAAO,GAAG,WAAW,EAAE;AACnD;AACA,GAAG,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,CAAC,GAAG,gBAAgB,CAAC,EAAE,EAAC;AAC7D,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC5B,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC5B,GAAG,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC,eAAe,EAAE,GAAG,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,WAAW,KAAK,CAAC,CAAC,EAAC;AAC3G,GAAG,UAAU,CAAC,SAAS,CAAC,QAAQ,GAAG,CAAC,EAAE,OAAO,EAAC;AAC9C,GAAG,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,EAAE;AAC7B,GAAG,IAAI,IAAI,CAAC,aAAa,EAAE;AAC3B,IAAI,gBAAgB,CAAC,CAAC,EAAC;AACvB,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;AACrC,IAAI;AACJ;AACA,GAAG,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,CAAC,GAAG,gBAAgB,CAAC,CAAC,EAAC;AAC5D,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC5B,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC5B,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC5B,GAAG,MAAM;AACT;AACA,GAAG,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,CAAC,GAAG,gBAAgB,CAAC,EAAE,EAAC;AAC7D,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC5B,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,GAAE;AAC1B,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC5B,GAAG,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC,eAAe,EAAE,GAAG,OAAO,EAAC;AACnE,GAAG,UAAU,CAAC,WAAW,CAAC,QAAQ,GAAG,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,EAAC;AACpE,GAAG;AACH,EAAE;AACF,CAAC,EAAE;AACH,CAAC,IAAI,CAAC,GAAG,EAAE,gBAAgB,EAAE,IAAI,EAAE;AACnC,EAAE,IAAI,IAAI,CAAC,gBAAgB,EAAE;AAC7B,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;AACvB,GAAG,OAAO,IAAI,CAAC,EAAE,CAAC;AAClB,GAAG;AACH,EAAE,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,EAAC;AAC7B,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC,GAAG,gBAAgB,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,GAAG,CAAC,EAAC;AACpE,EAAE,IAAI,IAAI,CAAC,SAAS,EAAE;AACtB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC5B,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC5B,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAC;AACzB,GAAG;AACH,EAAE,IAAI,CAAC,KAAK,EAAC;AACb,EAAE;AACF,CAAC,EAAE;AACH,CAAC,IAAI,CAAC,KAAK,EAAE,gBAAgB,EAAE,IAAI,EAAE;AACrC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC,GAAG,gBAAgB,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,GAAG,CAAC,EAAC;AACpE,EAAE,IAAI,IAAI,CAAC,SAAS,EAAE;AACtB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC5B,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC5B,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAC;AACzB,GAAG;AACH,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,KAAK,EAAE,EAAC;AAClD,EAAE;AACF,CAAC,EAAE;AACH,CAAC,IAAI,CAAC,KAAK,EAAE,gBAAgB,EAAE,IAAI,EAAE;AACrC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC,GAAG,gBAAgB,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,GAAG,CAAC,EAAC;AACpE,EAAE,IAAI,IAAI,CAAC,SAAS,EAAE;AACtB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC5B,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC5B,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAC;AACzB,GAAG;AACH,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,KAAK,EAAE,EAAC;AACrC,EAAE;AACF,CAAC,EAAE;AACH,CAAC,IAAI,CAAC,WAAW,EAAE,gBAAgB,EAAE;AACrC,EAAE,IAAI,IAAI,CAAC,SAAS;AACpB,GAAG,cAAc,CAAC,WAAW,EAAE,IAAI,EAAE,gBAAgB,EAAC;AACtD;AACA,GAAG,WAAW,CAACD,eAAa,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,IAAI,UAAU,CAAC,WAAW,CAAC,EAAE,gBAAgB,EAAC;AACxG,EAAE;AACF,CAAC,EAAE;AACH,CAAC,IAAI,CAAC,UAAU,EAAE,gBAAgB,EAAE;AACpC,EAAE,IAAI,WAAW,GAAG,UAAU,CAAC,YAAW;AAC1C,EAAE,IAAI,WAAW,KAAK,SAAS,IAAI,IAAI,CAAC,SAAS;AACjD,GAAG,cAAc,CAAC,UAAU,EAAE,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,gBAAgB,EAAC;AACtF;AACA,GAAG,WAAW,CAAC,UAAU,EAAE,gBAAgB,EAAC;AAC5C,EAAE;AACF,CAAC,EAAE;AACH,CAAC,IAAI,CAAC,WAAW,EAAE,gBAAgB,EAAE;AACrC,EAAE,IAAI,IAAI,CAAC,SAAS;AACpB,GAAG,cAAc,CAAC,WAAW,EAAE,IAAI,EAAE,gBAAgB,EAAC;AACtD;AACA,GAAG,WAAW,CAACA,eAAa,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,IAAI,UAAU,CAAC,WAAW,CAAC,EAAE,gBAAgB,EAAC;AACxG,EAAE;AACF,CAAC,EAAE;AACH,CAAC,IAAI,CAAC,EAAE,EAAE,gBAAgB,EAAE;AAC5B,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC,GAAG,gBAAgB,CAAC,CAAC,EAAC;AAC/C,EAAE,MAAM,CAAC,QAAQ,CAAC,GAAG,KAAI;AACzB,EAAE;AACF,CAAC,EAAC;AACF;AACA,SAAS,cAAc,CAAC,UAAU,EAAE,IAAI,EAAE,gBAAgB,EAAE,MAAM,EAAE;AACpE,CAAC,IAAI,MAAM,GAAG,UAAU,CAAC,WAAU;AACnC,CAAC,IAAI,MAAM,GAAG,CAAC,GAAG,KAAK,EAAE;AACzB,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,gBAAgB,CAAC,CAAC,GAAG,MAAM,EAAC;AACzD,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC3B,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,GAAG,EAAC;AACjC,EAAE,MAAM,IAAI,MAAM,GAAG,CAAC,GAAG,OAAO,EAAE;AAClC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,gBAAgB,CAAC,CAAC,GAAG,MAAM,EAAC;AACzD,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC3B,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,KAAK,EAAC;AACxC,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,KAAI;AAC1C,EAAE,MAAM;AACR,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,gBAAgB,CAAC,CAAC,GAAG,MAAM,EAAC;AACrE,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC3B,EAAE,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,GAAG,CAAC,EAAC;AAC5C,EAAE,QAAQ,IAAI,EAAC;AACf,EAAE;AACF,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC1B,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC1B,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,UAAU,GAAG,IAAI,UAAU,CAAC,UAAU,EAAC;AAChE,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC,UAAU,CAAC,MAAM,EAAE,UAAU,CAAC,UAAU,EAAE,UAAU,CAAC,UAAU,CAAC,EAAE,QAAQ,EAAC;AACtG,CAAC;AACD,SAAS,WAAW,CAAC,MAAM,EAAE,gBAAgB,EAAE;AAC/C,CAAC,IAAI,MAAM,GAAG,MAAM,CAAC,WAAU;AAC/B,CAAC,IAAI,MAAM,EAAE,SAAQ;AACrB,CAAC,IAAI,MAAM,GAAG,KAAK,EAAE;AACrB,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAC;AACzD,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC3B,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,OAAM;AAC7B,EAAE,MAAM,IAAI,MAAM,GAAG,OAAO,EAAE;AAC9B,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAC;AACzD,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC3B,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,IAAI,EAAC;AAClC,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,GAAG,KAAI;AACpC,EAAE,MAAM;AACR,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAC;AACrE,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC3B,EAAE,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,EAAC;AACxC,EAAE,QAAQ,IAAI,EAAC;AACf,EAAE;AACF,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAC;AAC7B,CAAC;AACD;AACA,SAAS,kBAAkB,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE;AAC5D,CAAC,IAAI,MAAM,GAAG,MAAM,CAAC,OAAM;AAC3B,CAAC,QAAQ,MAAM;AACf,EAAE,KAAK,CAAC;AACR,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC5B,GAAG,KAAK;AACR,EAAE,KAAK,CAAC;AACR,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC5B,GAAG,KAAK;AACR,EAAE,KAAK,CAAC;AACR,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC5B,GAAG,KAAK;AACR,EAAE,KAAK,CAAC;AACR,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC5B,GAAG,KAAK;AACR,EAAE,KAAK,EAAE;AACT,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC5B,GAAG,KAAK;AACR,EAAE;AACF,GAAG,IAAI,MAAM,GAAG,KAAK,EAAE;AACvB,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC7B,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,OAAM;AAC/B,IAAI,MAAM,IAAI,MAAM,GAAG,OAAO,EAAE;AAChC,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC7B,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,IAAI,EAAC;AACpC,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,GAAG,KAAI;AACtC,IAAI,MAAM;AACV,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC7B,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,IAAI,GAAE;AACrC,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,MAAM,IAAI,EAAE,IAAI,KAAI;AAC9C,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI,KAAI;AAC7C,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,GAAG,KAAI;AACtC,IAAI;AACJ,EAAE;AACF,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC1B,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAC;AAC7B,CAAC,QAAQ,IAAI,OAAM;AACnB,CAAC,OAAO,QAAQ;AAChB,CAAC;AACD;AACA,SAAS,SAAS,CAAC,UAAU,EAAE,WAAW,EAAE;AAC5C;AACA,CAAC,IAAI,OAAM;AACX,CAAC,IAAI,cAAc,GAAG,WAAW,CAAC,MAAM,GAAG,EAAC;AAC5C,CAAC,IAAI,OAAO,GAAG,UAAU,CAAC,MAAM,GAAG,eAAc;AACjD,CAAC,OAAO,MAAM,GAAG,WAAW,CAAC,GAAG,EAAE,EAAE;AACpC,EAAE,IAAI,MAAM,GAAG,MAAM,CAAC,OAAM;AAC5B,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC,GAAE;AACpB,EAAE,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,cAAc,EAAE,MAAM,EAAE,OAAO,EAAC;AACjE,EAAE,cAAc,IAAI,EAAC;AACrB,EAAE,IAAI,QAAQ,GAAG,MAAM,GAAG,eAAc;AACxC,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC/B,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAI;AAC/B,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,IAAI,GAAE;AACnC,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,IAAI,KAAI;AAC5C,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,IAAI,KAAI;AAC3C,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,KAAI;AACpC,EAAE,OAAO,GAAG,OAAM;AAClB,EAAE;AACF,CAAC,OAAO,UAAU;AAClB,CAAC;AACD;AACA,SAAS,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,iBAAiB,EAAE;AACtD,CAAC,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;AAChC,EAAE,UAAU,CAAC,SAAS,CAAC,cAAc,CAAC,QAAQ,GAAG,KAAK,EAAE,QAAQ,GAAG,iBAAiB,GAAG,cAAc,CAAC,QAAQ,GAAG,KAAK,EAAC;AACvH,EAAE,cAAc,CAAC,eAAe,GAAG,QAAQ,GAAG,KAAK,CAAC;AACpD,EAAE,IAAI,YAAY,GAAG,eAAc;AACnC,EAAE,cAAc,GAAG,KAAI;AACvB,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAC;AACvB,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAC;AACvB,EAAE;AACF,CAAC;AACD;AACO,SAAS,YAAY,CAAC,SAAS,EAAE;AACxC,CAAC,IAAI,SAAS,CAAC,KAAK,EAAE;AACtB,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK;AACzC,GAAG,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC;AAC7D,EAAE,IAAI,SAAS,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI;AACvC,GAAG,MAAM,IAAI,KAAK,CAAC,gEAAgE,CAAC;AACpF,EAAE,gBAAgB,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,EAAC;AAC3C,EAAE,UAAU,CAAC,OAAO,CAAC,SAAS,EAAC;AAC/B,EAAE;AACF,CAACE,cAAkB,CAAC,SAAS,EAAC;AAC9B,CAAC;AACD,SAASD,mBAAiB,CAAC,UAAU,EAAE,KAAK,EAAE;AAC9C,CAAC,UAAU,CAAC,YAAY,GAAG,CAAC,kBAAkB,KAAK;AACnD,EAAE,IAAI,UAAU,GAAG,CAAC,kBAAkB,KAAK,CAAC,KAAK,CAAC,yBAAyB,IAAI,CAAC,MAAM,kBAAkB,CAAC,MAAM,EAAC;AAChH,EAAE,IAAI,CAAC,UAAU;AACjB,GAAG,KAAK,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;AAC9C,EAAE,OAAO,UAAU,CAAC;AACpB,GAAE;AACF,CAAC,OAAO,UAAU;AAClB,CAAC;AACM,SAAS,mBAAmB,CAAC,UAAU,EAAE,cAAc,EAAE;AAChE,CAAC,gBAAgB,GAAG,UAAU,CAAC;AAC/B,CAACA,mBAAiB,GAAG,cAAc,CAAC;AACpC,CAAC;AACD;AACA,IAAI,YAAY,GAAG,IAAI,KAAK,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,EAAC;AACvC,MAAC,IAAI,GAAG,YAAY,CAAC,KAAI;AACzB,MAAC,MAAM,GAAG,YAAY,CAAC,KAAI;AAC3B,MAAC,OAAO,GAAG,MAAK;AAGhB,MAAC,EAAE,KAAK,EAAE,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,gBAAe;AACrE,MAAM,iBAAiB,GAAG,IAAG;AAC7B,MAAM,iBAAiB,GAAG,KAAI;AAC9B,MAAM,mBAAmB,GAAG;;AC/iCnC,MAAM,KAAK,GAAG,CAAC,CAAC;AAChB,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,MAAM,IAAI,GAAG,CAAC,CAAC;AACf,MAAM,WAAW,GAAG,CAAC,CAAC;AACtB,MAAM,IAAI,GAAG,EAAE,CAAC;AAChB,MAAM,UAAU,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;AACxD,UAAU,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AAC1B,MAAM,cAAc,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AAC5E,IAAI,aAAa,CAAC;AAClB,IAAI;AACJ,CAAC,IAAI,QAAQ,CAAC,EAAE,CAAC,CAAC;AAClB,CAAC,aAAa,GAAG,IAAI,CAAC;AACtB,CAAC,CAAC,MAAM,KAAK,EAAE;AACf;AACA,CAAC;AACD;AACA,IAAI,eAAe,CAAC;AACpB,MAAM,aAAa,GAAG,OAAO,MAAM,KAAK,YAAW;AACnD,IAAI,WAAW,EAAE,aAAa,CAAC;AAC/B,IAAI;AACJ,CAAC,WAAW,GAAG,IAAI,WAAW,GAAE;AAChC,CAAC,CAAC,OAAO,KAAK,EAAE,EAAE;AAClB,MAAM,UAAU,GAAG,aAAa,GAAG,SAAS,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE;AACtE,CAAC,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAC,UAAU,GAAG,QAAQ,CAAC;AACxE,CAAC,GAAG,CAAC,WAAW,IAAI,WAAW,CAAC,UAAU;AAC1C,CAAC,SAAS,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE;AACpC,EAAE,OAAO,WAAW,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO;AAC1E,EAAE,GAAG,MAAK;AAIV,mBAAmB,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;AACpD,SAAS,WAAW,CAAC,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE;AACjG,CAAC,IAAI,YAAY,GAAG,KAAK,CAAC,YAAY,KAAK,KAAK,CAAC,YAAY,GAAG,EAAE,CAAC,CAAC;AACpE;AACA,CAAC,IAAI,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC;AAClC,CAAC,IAAI,iBAAiB,GAAG,CAAC,YAAY,CAAC,eAAe,IAAI,GAAG,IAAI,QAAQ,CAAC;AAC1E,CAAC,IAAI,OAAO,GAAG,MAAM,CAAC,MAAM,GAAG,EAAE,CAAC;AAClC,CAAC,IAAI,KAAK,GAAG,QAAQ,CAAC;AACtB,CAAC,IAAI,QAAQ,GAAG,OAAO,EAAE;AACzB,EAAE,MAAM,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAC9B,EAAE,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC/B,EAAE,QAAQ,IAAI,aAAa,CAAC;AAC5B,EAAE,KAAK,IAAI,aAAa,CAAC;AACzB,EAAE,iBAAiB,IAAI,aAAa,CAAC;AACrC,EAAE,aAAa,GAAG,CAAC,CAAC;AACpB,EAAE,OAAO,GAAG,MAAM,CAAC,MAAM,GAAG,EAAE,CAAC;AAC/B,EAAE;AACF;AACA,CAAC,IAAI,SAAS,EAAE,WAAW,GAAG,iBAAiB,CAAC;AAChD;AACA,CAAC,IAAI,UAAU,GAAG,YAAY,CAAC,WAAW,KAAK,YAAY,CAAC,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;AAC/F,CAAC,IAAI,MAAM,GAAG,YAAY,CAAC,MAAM,IAAI,YAAY,CAAC,MAAM,CAAC;AACzD,CAAC,IAAI,UAAU;AACf,EAAE,MAAM,GAAG,GAAG,GAAG,CAAC;AAClB,GAAG,MAAM,GAAG,IAAI,GAAG,CAAC;AACpB,IAAI,MAAM,GAAG,MAAM,GAAG,CAAC;AACvB,KAAK,MAAM,GAAG,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC;AAC/B,CAAC,IAAI,UAAU,KAAK,CAAC;AACrB,EAAE,OAAO,CAAC,CAAC;AACX,CAAC,QAAQ,IAAI,UAAU,CAAC;AACxB,CAAC,IAAI,gBAAgB,GAAG,EAAE,CAAC;AAC3B,CAAC,IAAI,UAAU,CAAC;AAChB,CAAC,IAAI,QAAQ,GAAG,CAAC,CAAC;AAClB,CAAC,KAAK,IAAI,GAAG,IAAI,MAAM,EAAE;AACzB,EAAE,IAAI,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;AAC1B,EAAE,IAAI,cAAc,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;AACvC,EAAE,IAAI,CAAC,cAAc,EAAE;AACvB,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,cAAc,GAAG;AACtC,IAAI,GAAG;AACP,IAAI,MAAM,EAAE,UAAU;AACtB,IAAI,iBAAiB,EAAE,CAAC;AACxB,IAAI,MAAM,EAAE,IAAI;AAChB,IAAI,MAAM,EAAE,IAAI;AAChB,IAAI,IAAI,EAAE,IAAI;AACd,IAAI,QAAQ,EAAE,IAAI;AAClB,IAAI,QAAQ,EAAE,IAAI;AAClB,IAAI,KAAK,EAAE,IAAI;AACf,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,MAAM,EAAE,IAAI;AAChB,IAAI,CAAC;AACL,GAAG;AACH,EAAE,IAAI,QAAQ,GAAG,OAAO,EAAE;AAC1B,GAAG,MAAM,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAC/B,GAAG,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC;AAChC,GAAG,QAAQ,IAAI,aAAa,CAAC;AAC7B,GAAG,KAAK,IAAI,aAAa,CAAC;AAC1B,GAAG,iBAAiB,IAAI,aAAa,CAAC;AACtC,GAAG,WAAW,IAAI,aAAa,CAAC;AAChC,GAAG,aAAa,GAAG,CAAC,CAAC;AACrB,GAAG,OAAO,GAAG,MAAM,CAAC,MAAM,GAAG,GAAE;AAC/B,GAAG;AACH,EAAE,QAAQ,OAAO,KAAK;AACtB,GAAG,KAAK,QAAQ;AAChB,IAAI,IAAI,MAAM,GAAG,KAAK,CAAC;AACvB;AACA,IAAI,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE;AAC/C,KAAK,IAAI,MAAM,IAAI,CAAC,KAAK,MAAM,IAAI,MAAM,GAAG,UAAU,IAAI,MAAM,GAAG,CAAC,UAAU,EAAE;AAChF,MAAM,IAAI,MAAM,GAAG,IAAI,IAAI,MAAM,IAAI,CAAC,KAAK,cAAc,CAAC,IAAI,IAAI,EAAE,MAAM,GAAG,GAAG,IAAI,cAAc,CAAC,KAAK,CAAC,IAAI,MAAM,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;AACtJ,OAAO,UAAU,GAAG,cAAc,CAAC,IAAI,IAAI,oBAAoB,CAAC,cAAc,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;AAC3F,OAAO,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,MAAM,CAAC;AACnC,OAAO,MAAM;AACb,OAAO,UAAU,GAAG,cAAc,CAAC,KAAK,IAAI,oBAAoB,CAAC,cAAc,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;AAC5F,OAAO,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;AACpD,OAAO,QAAQ,IAAI,CAAC,CAAC;AACrB,OAAO;AACP,MAAM,MAAM;AACZ,MAAM,MAAM,IAAI,MAAM,GAAG,WAAW,IAAI,MAAM,IAAI,CAAC,UAAU,EAAE;AAC/D,MAAM,UAAU,CAAC,UAAU,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;AACpD,MAAM,IAAI,cAAc,CAAC,MAAM,CAAC,QAAQ,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;AACtD,OAAO,IAAI,SAAQ;AACnB;AACA,OAAO,IAAI,CAAC,CAAC,QAAQ,GAAG,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,KAAK,MAAM,CAAC,QAAQ,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,QAAQ,EAAE;AACjI,QAAQ,UAAU,GAAG,cAAc,CAAC,KAAK,IAAI,oBAAoB,CAAC,cAAc,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;AAC7F,QAAQ,QAAQ,IAAI,CAAC,CAAC;AACtB,QAAQ,MAAM;AACd,QAAQ;AACR,OAAO;AACP,MAAM;AACN,KAAK;AACL,IAAI,UAAU,GAAG,cAAc,CAAC,KAAK,IAAI,oBAAoB,CAAC,cAAc,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;AACzF,IAAI,UAAU,CAAC,UAAU,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;AAClD,IAAI,QAAQ,IAAI,CAAC,CAAC;AAClB,IAAI,MAAM;AACV,GAAG,KAAK,QAAQ;AAChB,IAAI,IAAI,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC;AACjC,IAAI,SAAS,GAAG,WAAW,GAAG,iBAAiB,CAAC;AAChD,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,WAAW,GAAG,OAAO,EAAE;AAClD,KAAK,MAAM,GAAG,QAAQ,CAAC,CAAC,SAAS,IAAI,CAAC,IAAI,WAAW,CAAC,CAAC;AACvD,KAAK,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC;AAClC,KAAK,QAAQ,IAAI,aAAa,CAAC;AAC/B,KAAK,KAAK,IAAI,aAAa,CAAC;AAC5B,KAAK,iBAAiB,IAAI,aAAa,CAAC;AACxC,KAAK,WAAW,IAAI,aAAa,CAAC;AAClC,KAAK,aAAa,GAAG,CAAC,CAAC;AACvB,KAAK,OAAO,GAAG,MAAM,CAAC,MAAM,GAAG,GAAE;AACjC,KAAK;AACL,IAAI,IAAI,SAAS,IAAI,CAAC,MAAM,GAAG,SAAS,KAAK,CAAC,CAAC,EAAE;AACjD,KAAK,gBAAgB,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,QAAQ,GAAG,KAAK,CAAC,CAAC;AACzD,KAAK,MAAM;AACX,KAAK;AACL,IAAI,IAAI,WAAU;AAClB,IAAI,IAAI,QAAQ,GAAG,WAAW,CAAC;AAC/B,IAAI,IAAI,SAAS,GAAG,IAAI,EAAE;AAC1B,KAAK,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;AACnB,KAAK,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;AACrC,MAAM,EAAE,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,EAAC;AAC9B,MAAM,IAAI,EAAE,GAAG,IAAI,EAAE;AACrB,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,GAAE;AACjC,OAAO,MAAM,IAAI,EAAE,GAAG,KAAK,EAAE;AAC7B,OAAO,UAAU,GAAG,IAAI,CAAC;AACzB,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,KAAI;AAC7C,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,GAAG,KAAI;AAC/C,OAAO,MAAM;AACb,OAAO,CAAC,EAAE,GAAG,MAAM,MAAM,MAAM;AAC/B,OAAO,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,MAAM,MAAM,MAAM;AAC3D,QAAQ;AACR,OAAO,UAAU,GAAG,IAAI,CAAC;AACzB,OAAO,EAAE,GAAG,OAAO,IAAI,CAAC,EAAE,GAAG,MAAM,KAAK,EAAE,CAAC,IAAI,EAAE,GAAG,MAAM,EAAC;AAC3D,OAAO,CAAC,GAAE;AACV,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,KAAI;AAC9C,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,GAAG,KAAI;AACrD,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,GAAG,KAAI;AACpD,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,GAAG,KAAI;AAC/C,OAAO,MAAM;AACb,OAAO,UAAU,GAAG,IAAI,CAAC;AACzB,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,KAAI;AAC9C,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,GAAG,KAAI;AACpD,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,GAAG,KAAI;AAC/C,OAAO;AACP,MAAM;AACN,KAAK,MAAM;AACX,KAAK,WAAW,IAAI,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;AAC3D,KAAK,UAAU,GAAG,WAAW,GAAG,QAAQ,GAAG,SAAS,CAAC;AACrD,KAAK;AACL,IAAI,IAAI,SAAS,GAAG,IAAI,KAAK,SAAS,GAAG,IAAI,KAAK,cAAc,CAAC,MAAM,IAAI,cAAc,CAAC,OAAO,CAAC,CAAC,EAAE;AACrG;AACA,KAAK,IAAI,UAAU,EAAE;AACrB,MAAM,IAAI,EAAE,UAAU,GAAG,cAAc,CAAC,OAAO,CAAC,EAAE;AAClD,OAAO,IAAI,YAAY,CAAC,MAAM,GAAG,EAAE,KAAK,UAAU,GAAG,cAAc,CAAC,MAAM,CAAC,EAAE;AAC7E;AACA,QAAQ,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC;AACjC,QAAQ,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC;AACrC,QAAQ,cAAc,CAAC,OAAO,GAAG,UAAU,CAAC;AAC5C,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;AAC5B,QAAQ,MAAM;AACd,QAAQ,UAAU,GAAG,oBAAoB,CAAC,cAAc,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AACnE,QAAQ;AACR,OAAO;AACP,MAAM,MAAM,IAAI,SAAS,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE;AAChD,MAAM,UAAU,GAAG,IAAI,CAAC;AACxB,MAAM,UAAU,GAAG,cAAc,CAAC,MAAM,IAAI,oBAAoB,CAAC,cAAc,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;AAC3F,MAAM,MAAM;AACZ,MAAM;AACN,UAAU,IAAI,EAAE,UAAU,GAAG,cAAc,CAAC,MAAM,CAAC,IAAI,EAAE,YAAY,CAAC,MAAM,GAAG,EAAE,KAAK,UAAU,GAAG,cAAc,CAAC,OAAO,CAAC,CAAC;AAC3H,MAAM,UAAU,GAAG,oBAAoB,CAAC,cAAc,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;AAClE,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,SAAS,CAAC;AACpC,KAAK,MAAM;AACX;AACA;AACA,MAAM,UAAU,GAAG,cAAc,CAAC,QAAQ,IAAI,oBAAoB,CAAC,cAAc,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AAC5F;AACA;AACA,KAAK,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;AACrD,KAAK,QAAQ,IAAI,CAAC,CAAC;AACnB,KAAK;AACL,IAAI,MAAM;AACV,GAAG,KAAK,QAAQ;AAChB,IAAI,IAAI,KAAK,EAAE;AACf,KAAK,IAAI,KAAK,CAAC,WAAW,KAAK,IAAI,EAAE;AACrC,MAAM,UAAU,GAAG,cAAc,CAAC,MAAM,IAAI,oBAAoB,CAAC,cAAc,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AAC1F,MAAM,UAAU,CAAC,UAAU,CAAC,QAAQ,EAAE,KAAK,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,CAAC;AAC7D,MAAM,QAAQ,IAAI,CAAC,CAAC;AACpB,MAAM,MAAM;AACZ,MAAM,gBAAgB,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;AAClD,MAAM;AACN,KAAK,MAAM;AACX,KAAK,MAAM;AACX,KAAK,cAAc,GAAG,OAAO,CAAC,cAAc,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;AACzE,KAAK,IAAI,cAAc,EAAE;AACzB,MAAM,UAAU,GAAG,cAAc,CAAC;AAClC,MAAM,QAAQ,GAAG,eAAe,CAAC;AACjC,MAAM,MAAM,gBAAgB,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;AACxD,KAAK;AACL,IAAI,MAAM;AACV,GAAG,KAAK,SAAS;AACjB,IAAI,UAAU,GAAG,cAAc,CAAC,IAAI,IAAI,cAAc,CAAC,MAAM,IAAI,oBAAoB,CAAC,cAAc,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;AACjH,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC;AAC7C,IAAI,MAAM;AACV,GAAG,KAAK,WAAW;AACnB,IAAI,cAAc,GAAG,OAAO,CAAC,cAAc,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC;AACvE,IAAI,IAAI,cAAc,EAAE;AACxB,KAAK,UAAU,GAAG,cAAc,CAAC;AACjC,KAAK,QAAQ,GAAG,eAAe,CAAC;AAChC,KAAK,MAAM,gBAAgB,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;AACvD,IAAI,MAAM;AACV,GAAG;AACH,IAAI,gBAAgB,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;AAChD,GAAG;AACH,EAAE,QAAQ,EAAE,CAAC;AACb,EAAE;AACF;AACA,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG;AACtD,EAAE,IAAI,GAAG,GAAG,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC;AAClC,EAAE,IAAI,KAAK,GAAG,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC;AACpC,EAAE,IAAI,aAAa,GAAG,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC;AAC5C,EAAE,IAAI,cAAc,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;AACvC,EAAE,IAAI,CAAC,cAAc,EAAE;AACvB,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,cAAc,GAAG;AACtC,IAAI,GAAG;AACP,IAAI,MAAM,EAAE,UAAU;AACtB,IAAI,iBAAiB,EAAE,aAAa,GAAG,QAAQ;AAC/C,IAAI,MAAM,EAAE,IAAI;AAChB,IAAI,MAAM,EAAE,IAAI;AAChB,IAAI,IAAI,EAAE,IAAI;AACd,IAAI,QAAQ,EAAE,IAAI;AAClB,IAAI,QAAQ,EAAE,IAAI;AAClB,IAAI,KAAK,EAAE,IAAI;AACf,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,CAAC;AACL,GAAG;AACH,EAAE,IAAI,WAAW,CAAC;AAClB,EAAE,IAAI,KAAK,EAAE;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,IAAI,IAAI,CAAC;AACZ,GAAG,SAAS,GAAG,WAAW,GAAG,iBAAiB,CAAC;AAC/C,GAAG,IAAI,SAAS,GAAG,MAAM,EAAE;AAC3B,IAAI,UAAU,GAAG,cAAc,CAAC,QAAQ,CAAC;AACzC,IAAI,IAAI,UAAU;AAClB,KAAK,IAAI,GAAG,CAAC,CAAC;AACd,SAAS,KAAK,UAAU,GAAG,cAAc,CAAC,QAAQ;AAClD,KAAK,IAAI,GAAG,CAAC,CAAC;AACd,SAAS;AACT,KAAK,UAAU,GAAG,oBAAoB,CAAC,cAAc,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC;AACvE,KAAK,IAAI,GAAG,CAAC,CAAC;AACd,KAAK;AACL,IAAI,MAAM;AACV,IAAI,UAAU,GAAG,cAAc,CAAC,QAAQ,IAAI,oBAAoB,CAAC,cAAc,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC;AACjG,IAAI,IAAI,GAAG,CAAC,CAAC;AACb,IAAI;AACJ,GAAG,WAAW,GAAG,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;AAC1C;AACA,GAAG,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;AACxC;AACA,IAAI,WAAW,GAAG,WAAW,CAAC,QAAQ,CAAC;AACvC,IAAI,UAAU,GAAG,WAAW,CAAC,UAAU,CAAC;AACxC,IAAI,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;AAChC,IAAI,iBAAiB,IAAI,aAAa,CAAC;AACvC,IAAI,QAAQ,IAAI,aAAa,CAAC;AAC9B,IAAI,KAAK,IAAI,aAAa,CAAC;AAC3B,IAAI,aAAa,GAAG,CAAC,CAAC;AACtB,IAAI;AACJ,IAAI,WAAW,GAAG,WAAW,CAAC;AAC9B,GAAG,IAAI,IAAI,KAAK,CAAC,EAAE;AACnB,IAAI,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;AACpD,IAAI,QAAQ,IAAI,CAAC,CAAC;AAClB,IAAI,MAAM;AACV,IAAI,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;AACpD,IAAI,QAAQ,IAAI,CAAC,CAAC;AAClB,IAAI;AACJ,GAAG,MAAM;AACT,GAAG,UAAU,GAAG,cAAc,CAAC,QAAQ,IAAI,oBAAoB,CAAC,cAAc,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC;AAChG,GAAG,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAE,KAAK,KAAK,IAAI,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AAClE,GAAG,QAAQ,IAAI,CAAC,CAAC;AACjB,GAAG;AACH,EAAE,QAAQ,EAAE,CAAC;AACb,EAAE;AACF;AACA;AACA,CAAC,IAAI,QAAQ,GAAG,UAAU,CAAC,aAAa,CAAC,CAAC;AAC1C,CAAC,IAAI,QAAQ,IAAI,IAAI,EAAE;AACvB,EAAE,QAAQ,GAAG,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC;AACvC,EAAE,IAAI,SAAS,GAAG,EAAE,CAAC;AACrB,EAAE,IAAI,cAAc,GAAG,UAAU,CAAC;AAClC,EAAE,IAAI,GAAG,EAAE,IAAI,CAAC;AAChB,EAAE,OAAO,CAAC,IAAI,GAAG,cAAc,CAAC,MAAM,MAAM,SAAS,EAAE;AACvD,GAAG,IAAI,IAAI,GAAG,cAAc,CAAC,MAAM,CAAC;AACpC,GAAG,cAAc,GAAG,cAAc,CAAC,QAAQ,CAAC;AAC5C,GAAG,GAAG,GAAG,cAAc,CAAC,GAAG,CAAC;AAC5B,GAAG,IAAI,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;AACpC,GAAG,IAAI,cAAc,CAAC,iBAAiB;AACvC,IAAI,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;AACpD,GAAG,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC5B,GAAG,cAAc,GAAG,cAAc,CAAC,MAAM,CAAC;AAC1C,GAAG;AACH,EAAE,SAAS,CAAC,OAAO,EAAE,CAAC;AACtB,EAAE,UAAU,CAAC,aAAa,CAAC,GAAG,QAAQ,CAAC;AACvC,EAAE,KAAK,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAC;AAC3C,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;AACtB,EAAE;AACF;AACA;AACA,CAAC,QAAQ,UAAU;AACnB,EAAE,KAAK,CAAC;AACR,GAAG,IAAI,QAAQ,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;AAClC,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,QAAQ,GAAG,IAAI,CAAC;AACnC,GAAG,MAAM;AACT,EAAE,KAAK,CAAC;AACR,GAAG,IAAI,QAAQ,IAAI,KAAK,EAAE,OAAO,CAAC,CAAC;AACnC,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;AACxB,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC;AAChC,GAAG,MAAM;AACT,EAAE,KAAK,CAAC;AACR,GAAG,IAAI,QAAQ,IAAI,OAAO,EAAE,OAAO,CAAC,CAAC;AACrC,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;AACxB,GAAG,UAAU,CAAC,SAAS,CAAC,KAAK,GAAG,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;AACnD,GAAG,MAAM;AACT,EAAE,KAAK,CAAC;AACR,GAAG,IAAI,QAAQ,IAAI,SAAS,EAAE,OAAO,CAAC,CAAC;AACvC,GAAG,UAAU,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,QAAQ,IAAI,CAAC,IAAI,IAAI,EAAE,IAAI,CAAC,CAAC;AAC7D,GAAG,MAAM;AACT,EAAE;AACF;AACA,CAAC,IAAI,QAAQ,GAAG,iBAAiB,EAAE;AACnC,EAAE,IAAI,iBAAiB,KAAK,WAAW;AACvC,GAAG,OAAO,QAAQ,CAAC;AACnB;AACA,EAAE,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,iBAAiB,EAAE,WAAW,CAAC,CAAC;AAC9D,EAAE,WAAW,IAAI,QAAQ,GAAG,iBAAiB,CAAC;AAC9C,EAAE,YAAY,CAAC,eAAe,GAAG,QAAQ,GAAG,KAAK,CAAC;AAClD,EAAE,MAAM,IAAI,QAAQ,GAAG,iBAAiB,EAAE;AAC1C,EAAE,IAAI,iBAAiB,KAAK,WAAW;AACvC,GAAG,OAAO,QAAQ,CAAC;AACnB,EAAE,YAAY,CAAC,eAAe,GAAG,QAAQ,GAAG,KAAK,CAAC;AAClD,EAAE,OAAO,WAAW,CAAC,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AAC9F,EAAE;AACF,CAAC,OAAO,WAAW,CAAC;AACpB,CAAC;AACD,SAAS,OAAO,CAAC,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,KAAK,EAAE;AAC1D,CAAC,IAAI,cAAc,CAAC;AACpB,CAAC,KAAK,cAAc,GAAG,UAAU,CAAC,MAAM,IAAI,UAAU,CAAC,IAAI,GAAG;AAC9D,EAAE,UAAU,CAAC,OAAO,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;AAC5C,EAAE,eAAe,GAAG,QAAQ,GAAG,CAAC,CAAC;AACjC,EAAE,OAAO,cAAc,CAAC;AACxB,EAAE;AACF,CAAC,KAAK,cAAc,GAAG,UAAU,CAAC,QAAQ,IAAI,UAAU,CAAC,QAAQ,GAAG;AACpE,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;AAC7C,EAAE,eAAe,GAAG,QAAQ,GAAG,CAAC,CAAC;AACjC,EAAE,OAAO,cAAc,CAAC;AACxB,EAAE;AACF,CAAC,IAAI,cAAc,GAAG,UAAU,CAAC,KAAK,EAAE;AACxC,EAAE,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,UAAU,GAAG,KAAK,EAAE,IAAI,CAAC,CAAC;AAC3D,EAAE,eAAe,GAAG,QAAQ,GAAG,CAAC,CAAC;AACjC,EAAE,OAAO,cAAc,CAAC;AACxB,EAAE;AACF;AACA,CAAC,IAAI,cAAc,GAAG,UAAU,CAAC,KAAK,EAAE;AACxC,EAAE,UAAU,CAAC,UAAU,CAAC,QAAQ,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;AAC7C,EAAE,UAAU,CAAC,OAAO,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;AACtC,EAAE,eAAe,GAAG,QAAQ,GAAG,CAAC,CAAC;AACjC,EAAE,OAAO,cAAc,CAAC;AACxB,EAAE;AACF,CAAC,eAAe,GAAG,QAAQ,CAAC;AAC5B;AACA,CAAC,OAAO;AACR,CAAC;AACD,SAAS,oBAAoB,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,EAAE;AACtD,CAAC,IAAI,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC;AAC/C,CAAC,IAAI,aAAa,GAAG,UAAU,CAAC,QAAQ,CAAC,KAAK,UAAU,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;AAC1F,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC;AAC7B,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC;AAC7B,CAAC,aAAa,CAAC,QAAQ,GAAG,UAAU,CAAC;AACrC,CAAC,OAAO,aAAa,CAAC;AACtB,CAAC;AACD,SAAS,kBAAkB,CAAC,UAAU,EAAE;AACxC,CAAC,IAAI,EAAE,UAAU,YAAY,GAAG,CAAC;AACjC,EAAE,OAAO,UAAU,CAAC;AACpB,CAAC,IAAI,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;AAC3C,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC;AAC3B,EAAE,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,SAAS,IAAI,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACrD,CAAC,IAAI,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;AACrC,CAAC,IAAI,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACvC,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC/C,EAAE,IAAI,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AAC3B,EAAE,IAAI,UAAU,GAAG,WAAW,CAAC;AAC/B,EAAE,KAAK,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,SAAS,EAAE;AAC3C,GAAG,IAAI,cAAc,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;AACxC,GAAG,IAAI,CAAC,cAAc,EAAE;AACxB,IAAI,UAAU,CAAC,GAAG,CAAC,GAAG,cAAc,GAAG;AACvC,KAAK,GAAG;AACR,KAAK,MAAM,EAAE,UAAU;AACvB,KAAK,iBAAiB,EAAE,CAAC;AACzB,KAAK,MAAM,EAAE,IAAI;AACjB,KAAK,MAAM,EAAE,IAAI;AACjB,KAAK,IAAI,EAAE,IAAI;AACf,KAAK,QAAQ,EAAE,IAAI;AACnB,KAAK,QAAQ,EAAE,IAAI;AACnB,KAAK,KAAK,EAAE,IAAI;AAChB,KAAK,OAAO,EAAE,IAAI;AAClB,KAAK,MAAM,EAAE,IAAI;AACjB,KAAK,CAAC;AACN,IAAI;AACJ,GAAG,UAAU,GAAG,oBAAoB,CAAC,cAAc,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AACjE,GAAG;AACH,EAAE,UAAU,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;AAChC,EAAE;AACF,CAAC,KAAK,CAAC,WAAW,GAAG,WAAW,CAAC;AACjC,CAAC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;AAC3B,CAAC,IAAI,CAAC,yBAAyB,GAAG,KAAK,CAAC,MAAM,CAAC;AAC/C,CAAC,OAAO,KAAK,CAAC;AACd,CAAC;AACD,IAAI,YAAY,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAC;AACvC,SAAS,UAAU,CAAC,GAAG,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE;AACpD,CAAC,IAAI,QAAQ,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC,GAAG,IAAI,CAAC;AACvC,CAAC,IAAI,QAAQ,IAAI,EAAE,EAAE;AACrB,EAAE,OAAO,QAAQ;AACjB,GAAG,KAAK,EAAE,EAAE,QAAQ,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,MAAM;AAC9C;AACA,GAAG,KAAK,EAAE,EAAE,QAAQ,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC,IAAI,GAAG,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM;AACvE,GAAG,KAAK,EAAE,EAAE,QAAQ,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC,IAAI,GAAG,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,MAAM;AACjG,GAAG,KAAK,EAAE,EAAE,QAAQ,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC,IAAI,GAAG,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,MAAM;AAC3H,GAAG;AACH,EAAE;AACF,CAAC,IAAI,SAAS,GAAG,OAAO,CAAC,YAAY,IAAI,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;AACxE,CAAC,IAAI,CAAC,SAAS,EAAE;AACjB;AACA,EAAE,GAAG,GAAG,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;AAC/D,EAAE,MAAM,IAAI,QAAQ,CAAC;AACrB,EAAE,QAAQ,GAAG,CAAC,CAAC;AACf,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa;AAC5B,GAAG,MAAM,IAAI,KAAK,CAAC,CAAC,8BAA8B,EAAE,QAAQ,CAAC,6BAA6B,CAAC,CAAC,CAAC;AAC7F,EAAE,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC;AACpD,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY;AAC3B,GAAG,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;AACjE,EAAE,OAAO,CAAC,yBAAyB,GAAG,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC;AAClE,EAAE,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;AAC7C,EAAE,IAAI,CAAC,SAAS;AAChB,GAAG,MAAM,IAAI,KAAK,CAAC,iCAAiC,GAAG,QAAQ,CAAC,CAAC;AACjE,EAAE;AACF,CAAC,IAAI,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC;AACrC,CAAC,IAAI,aAAa,GAAG,SAAS,CAAC,aAAa,CAAC;AAC7C,CAAC,IAAI,CAAC,SAAS,EAAE;AACjB,EAAE,SAAS,GAAG,SAAS,CAAC,SAAS,GAAG,SAAS,UAAU,GAAG;AAC1D,IAAG;AACH,EAAE,aAAa,GAAG,SAAS,CAAC,aAAa,GAAG,SAAS,YAAY,GAAG;AACpE,IAAG;AACH,EAAE,aAAa,CAAC,SAAS,GAAG,OAAO,CAAC,eAAe,IAAI,EAAE,CAAC;AAC1D,EAAE,IAAI,SAAS,GAAG,SAAS,CAAC,SAAS,GAAG,OAAO,CAAC,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,EAAE,CAAC;AAC9G,EAAE,IAAI,UAAU,GAAG,EAAE,CAAC;AACtB,EAAE,IAAI,aAAa,GAAG,CAAC,CAAC;AACxB,EAAE,IAAI,eAAe,CAAC;AACtB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AACpD,GAAG,IAAI,UAAU,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;AACjC,GAAG,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,iBAAiB,EAAE,GAAG,UAAU,CAAC;AAC3D,GAAG,IAAI,GAAG,KAAK,WAAW;AAC1B,IAAI,GAAG,GAAG,UAAU,CAAC;AACrB,GAAG,IAAI,QAAQ,GAAG;AAClB,IAAI,GAAG;AACP,IAAI,MAAM,EAAE,aAAa;AACzB,KAAI;AACJ,GAAG,IAAI,iBAAiB;AACxB,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,iBAAiB,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC;AAC1D;AACA,IAAI,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC9B,GAAG,IAAI,MAAM,CAAC;AACd,GAAG,OAAO,IAAI;AACd,IAAI,KAAK,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM;AACpC,IAAI,KAAK,CAAC;AACV,KAAK,MAAM,GAAG,CAAC,MAAM,EAAE,QAAQ,KAAK;AACpC,MAAM,IAAI,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;AACzD,MAAM,OAAO,GAAG,IAAI,IAAI,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;AACjD,MAAM,CAAC;AACP,KAAK,MAAM;AACX,IAAI,KAAK,CAAC;AACV,KAAK,MAAM,GAAG,CAAC,MAAM,EAAE,QAAQ,KAAK;AACpC,MAAM,IAAI,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC;AAC7B,MAAM,IAAI,QAAQ,GAAG,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;AAC/G,MAAM,IAAI,GAAG,GAAG,QAAQ,CAAC,SAAS,CAAC,QAAQ,GAAG,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AACrE,MAAM,OAAO,GAAG,IAAI,MAAM,GAAG,UAAU,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;AAC1D,MAAM,CAAC;AACP,KAAK,MAAM;AACX,IAAI,KAAK,CAAC;AACV,KAAK,MAAM,GAAG,CAAC,MAAM,EAAE,QAAQ,KAAK;AACpC,MAAM,IAAI,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC;AAC7B,MAAM,IAAI,QAAQ,GAAG,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;AAC/G,MAAM,IAAI,GAAG,GAAG,QAAQ,CAAC,SAAS,CAAC,QAAQ,GAAG,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AACrE,MAAM,OAAO,GAAG,IAAI,UAAU,GAAG,UAAU,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;AAC9D,MAAM,CAAC;AACP,KAAK,MAAM;AACX,IAAI;AACJ,GAAG,QAAQ,CAAC,MAAM,GAAG,MAAM,CAAC;AAC5B,GAAG,aAAa,IAAI,IAAI,CAAC;AACzB,GAAG,IAAI,GAAG,CAAC;AACX,GAAG,OAAO,IAAI;AACd,IAAI,KAAK,KAAK;AACd,KAAK,IAAI,eAAe,IAAI,CAAC,eAAe,CAAC,IAAI;AACjD,MAAM,eAAe,CAAC,IAAI,GAAG,QAAQ,CAAC;AACtC,KAAK,eAAe,GAAG,QAAQ,CAAC;AAChC,KAAK,QAAQ,CAAC,aAAa,GAAG,CAAC,CAAC;AAChC,KAAK,GAAG,GAAG,SAAS,MAAM,EAAE;AAC5B,MAAM,IAAI,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC;AAC7B,MAAM,IAAI,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;AACrC,MAAM,IAAI,QAAQ,GAAG,aAAa,GAAG,QAAQ,CAAC;AAC9C,MAAM,IAAI,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AACzC,MAAM,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,OAAO,GAAG,CAAC;AAC9C;AACA,MAAM,IAAI,GAAG,EAAE,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;AACpC,MAAM,MAAM,IAAI,EAAE;AAClB,OAAO,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC3C,OAAO,IAAI,OAAO,GAAG,KAAK,QAAQ;AAClC,QAAQ,MAAM;AACd;AACA,QAAQ,GAAG,GAAG,IAAI,CAAC;AACnB,OAAO,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACxB,OAAO;AACP,MAAM,IAAI,GAAG,IAAI,IAAI;AACrB,OAAO,GAAG,GAAG,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACxC,MAAM,IAAI,MAAM,CAAC,SAAS,EAAE;AAC5B,OAAO,OAAO,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AAC/C,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,OAAO,UAAU,CAAC,GAAG,EAAE,GAAG,GAAG,QAAQ,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC;AACxD;AACA,MAAM,CAAC;AACP,KAAK,MAAM;AACX,IAAI,KAAK,IAAI,CAAC,CAAC,KAAK,WAAW;AAC/B,KAAK,IAAI,eAAe,IAAI,CAAC,eAAe,CAAC,IAAI;AACjD,MAAM,eAAe,CAAC,IAAI,GAAG,QAAQ,CAAC;AACtC,KAAK,eAAe,GAAG,QAAQ,CAAC;AAChC,KAAK,GAAG,GAAG,SAAS,MAAM,EAAE;AAC5B,MAAM,IAAI,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;AACrC,MAAM,IAAI,QAAQ,GAAG,aAAa,GAAG,QAAQ,CAAC;AAC9C,MAAM,IAAI,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AACzC,MAAM,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,OAAO,GAAG,CAAC;AAC9C,MAAM,IAAI,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC;AAC7B,MAAM,IAAI,GAAG,EAAE,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;AACpC,MAAM,MAAM,IAAI,EAAE;AAClB,OAAO,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC3C,OAAO,IAAI,OAAO,GAAG,KAAK,QAAQ;AAClC,QAAQ,MAAM;AACd;AACA,QAAQ,GAAG,GAAG,IAAI,CAAC;AACnB,OAAO,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACxB,OAAO;AACP,MAAM,IAAI,GAAG,IAAI,IAAI;AACrB,OAAO,GAAG,GAAG,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACxC,MAAM,IAAI,IAAI,KAAK,IAAI,EAAE;AACzB,OAAO,OAAO,GAAG,CAAC,QAAQ,CAAC,MAAM,EAAE,GAAG,GAAG,QAAQ,EAAE,GAAG,GAAG,QAAQ,CAAC,CAAC;AACnE,OAAO,MAAM;AACb,OAAO,aAAa,GAAG,MAAM,CAAC;AAC9B,OAAO,IAAI;AACX,QAAQ,OAAO,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,GAAG,GAAG,QAAQ,EAAE,GAAG,EAAE,GAAG,GAAG,QAAQ,EAAE,CAAC,CAAC;AACnF,QAAQ,SAAS;AACjB,QAAQ,aAAa,GAAG,IAAI,CAAC;AAC7B,QAAQ;AACR,OAAO;AACP,MAAM,CAAC;AACP,KAAK,MAAM;AACX,IAAI,KAAK,MAAM;AACf,KAAK,OAAO,IAAI;AAChB,MAAM,KAAK,CAAC;AACZ,OAAO,GAAG,GAAG,UAAU,MAAM,EAAE;AAC/B,QAAQ,IAAI,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC;AAC/B,QAAQ,IAAI,QAAQ,GAAG,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;AACjH,QAAQ,IAAI,QAAQ,GAAG,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC;AACzD,QAAQ,IAAI,KAAK,GAAG,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,EAAC;AACrD,QAAQ,IAAI,KAAK,GAAG,UAAU,EAAE;AAChC,SAAS,IAAI,KAAK,GAAG,CAAC,UAAU;AAChC,UAAU,OAAO,KAAK,CAAC;AACvB,SAAS,IAAI,KAAK,GAAG,CAAC,UAAU;AAChC,UAAU,OAAO,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;AAC1C,SAAS;AACT,QAAQ,IAAI,MAAM,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AACzD;AACA,QAAQ,IAAI,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,KAAK,GAAG,CAAC,QAAQ,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAC;AAC7F,QAAQ,OAAO,CAAC,CAAC,UAAU,GAAG,MAAM,IAAI,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC;AACrF,QAAQ,CAAC;AACT,OAAO,MAAM;AACb,MAAM,KAAK,CAAC;AACZ,OAAO,GAAG,GAAG,UAAU,MAAM,EAAE;AAC/B,QAAQ,IAAI,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC;AAC/B,QAAQ,IAAI,QAAQ,GAAG,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;AACjH,QAAQ,IAAI,KAAK,GAAG,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AACjF,QAAQ,IAAI,KAAK,CAAC,KAAK,CAAC,EAAE;AAC1B,SAAS,IAAI,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC3D,SAAS,IAAI,IAAI,IAAI,IAAI;AACzB,UAAU,OAAO,UAAU,CAAC,IAAI,CAAC,CAAC;AAClC,SAAS;AACT,QAAQ,OAAO,KAAK,CAAC;AACrB,QAAQ,CAAC;AACT,OAAO,MAAM;AACb,MAAM,KAAK,CAAC;AACZ,OAAO,GAAG,GAAG,UAAU,MAAM,EAAE;AAC/B,QAAQ,IAAI,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC;AAC/B,QAAQ,IAAI,KAAK,GAAG,GAAG,CAAC,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC3D,QAAQ,OAAO,KAAK,GAAG,IAAI,GAAG,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;AACxD,QAAQ,CAAC;AACT,OAAO,MAAM;AACb,MAAM;AACN,KAAK,MAAM;AACX,IAAI,KAAK,IAAI;AACb,KAAK,GAAG,GAAG,UAAU,MAAM,EAAE;AAC7B,MAAM,IAAI,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC;AAC7B,MAAM,IAAI,QAAQ,GAAG,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;AAC/G,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;AACpF,MAAM,CAAC;AACP,KAAK,MAAM;AACX;AACA,IAAI;AACJ,GAAG,QAAQ,CAAC,GAAG,GAAG,GAAG,CAAC;AACtB,GAAG;AACH;AACA,EAAE,IAAI,aAAa,EAAE;AACrB,GAAG,IAAI,uBAAuB,GAAG,EAAE,CAAC;AACpC,GAAG,IAAI,IAAI,GAAG,EAAE,CAAC;AACjB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;AACb,GAAG,IAAI,sBAAsB,CAAC;AAC9B,GAAG,KAAK,IAAI,QAAQ,IAAI,UAAU,EAAE;AACpC,IAAI,IAAI,OAAO,CAAC,kBAAkB,IAAI,OAAO,CAAC,kBAAkB,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AAChF;AACA;AACA,KAAK,sBAAsB,GAAG,IAAI,CAAC;AACnC,KAAK,SAAS;AACd,KAAK;AACL,IAAI,MAAM,CAAC,cAAc,CAAC,SAAS,EAAE,QAAQ,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;AACxG,IAAI,IAAI,aAAa,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC;AAClC,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAC7B,IAAI,uBAAuB,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,aAAa,GAAG,KAAK,CAAC,CAAC;AACrG,IAAI;AACJ,GAAG,IAAI,sBAAsB,EAAE;AAC/B,IAAI,uBAAuB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;AACnD,IAAI;AACJ,GAAG,IAAI,QAAQ,GAAG,CAAC,IAAI,QAAQ,CAAC,GAAG,IAAI,EAAE,8CAA8C,GAAG,uBAAuB,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,EAAE,KAAK,CAAC,aAAa,EAAE,UAAU,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AACrM,GAAG,MAAM,CAAC,cAAc,CAAC,SAAS,EAAE,QAAQ,EAAE;AAC9C,IAAI,KAAK,CAAC,yBAAyB,EAAE;AACrC,KAAK,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;AACpD,KAAK;AACL,IAAI,CAAC,CAAC;AACN,GAAG,MAAM;AACT,GAAG,MAAM,CAAC,cAAc,CAAC,SAAS,EAAE,QAAQ,EAAE;AAC9C,IAAI,KAAK,CAAC,yBAAyB,EAAE;AACrC;AACA,KAAK,IAAI,QAAQ,GAAG,EAAE,CAAC;AACvB,KAAK,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AACxD;AACA,MAAM,IAAI,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;AAClC;AACA,MAAM,QAAQ,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;AAChC,MAAM;AACN,KAAK,OAAO,QAAQ,CAAC;AACrB,KAAK;AACL;AACA,IAAI,CAAC,CAAC;AACN,GAAG;AACH,EAAE;AACF,CAAC,IAAI,QAAQ,GAAG,IAAI,SAAS,EAAE,CAAC;AAChC,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG;AAC1B,EAAE,KAAK,EAAE,GAAG;AACZ,EAAE,QAAQ;AACV,EAAE,SAAS,EAAE,EAAE;AACf,EAAE,QAAQ,EAAE,MAAM;AAClB,GAAE;AACF,CAAC,OAAO,QAAQ,CAAC;AACjB,CAAC;AACD,SAAS,UAAU,CAAC,IAAI,EAAE;AAC1B,CAAC,OAAO,IAAI;AACZ,EAAE,KAAK,IAAI,EAAE,OAAO,IAAI,CAAC;AACzB,EAAE,KAAK,IAAI,EAAE,OAAO,SAAS,CAAC;AAC9B,EAAE,KAAK,IAAI,EAAE,OAAO,KAAK,CAAC;AAC1B,EAAE,KAAK,IAAI,EAAE,OAAO,IAAI,CAAC;AACzB,EAAE;AACF,CAAC,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;AACrC,CAAC;AACD,SAAS,UAAU,CAAC,GAAG,EAAE;AACzB,CAAC,OAAO,WAAW;AACnB,EAAE,OAAO,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;AACjC,EAAE;AACF,CAAC;AACD;AACA,SAAS,SAAS,GAAG;AACrB,CAAC,IAAI,aAAa,EAAE;AACpB,EAAE,aAAa,CAAC,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,aAAa,CAAC,QAAQ,EAAE,aAAa,CAAC,QAAQ,CAAC,CAAC;AAC7H,EAAE,aAAa,CAAC,QAAQ,GAAG,CAAC,CAAC;AAC7B,EAAE,aAAa,CAAC,QAAQ,GAAG,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC;AACtD,EAAE;AACF,CAAC;AACD,SAAS,iBAAiB,CAAC,UAAU,EAAE,KAAK,EAAE;AAC9C,CAAC,IAAI,KAAK,CAAC,YAAY,EAAE;AACzB,EAAE,IAAI,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;AAC5B,EAAE,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;AACrC,EAAE,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,YAAY,CAAC,CAAC;AAC7C,EAAE,UAAU,GAAG,SAAS,CAAC;AACzB,EAAE;AACF,CAAC,IAAI,yBAAyB,GAAG,KAAK,CAAC,yBAAyB,IAAI,CAAC,CAAC;AACtE,CAAC,UAAU,CAAC,YAAY,GAAG,QAAQ,IAAI;AACvC,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC;AACxB,EAAE,IAAI,QAAQ,YAAY,GAAG,EAAE;AAC/B,GAAG,IAAI,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;AAC3C,GAAG,IAAI,KAAK,CAAC,MAAM,MAAM,KAAK,CAAC,yBAAyB,IAAI,CAAC,CAAC;AAC9D,IAAI,UAAU,GAAG,KAAK,CAAC;AACvB,GAAG,IAAI,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;AAC3C,GAAG,IAAI,KAAK,CAAC,MAAM,KAAK,yBAAyB;AACjD,IAAI,UAAU,GAAG,KAAK,CAAC;AACvB,GAAG,MAAM,IAAI,QAAQ,YAAY,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AACnE,GAAG,IAAI,QAAQ,CAAC,MAAM,MAAM,KAAK,CAAC,yBAAyB,IAAI,CAAC,CAAC;AACjE,IAAI,UAAU,GAAG,KAAK,CAAC;AACvB,GAAG;AACH,EAAE,IAAI,CAAC,UAAU;AACjB,GAAG,KAAK,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;AACpC,EAAE,OAAO,UAAU,CAAC;AACpB,EAAE,CAAC;AACH,CAAC,KAAK,CAAC,yBAAyB,GAAG,KAAK,CAAC,YAAY,IAAI,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC;AACnF,CAAC,OAAO,UAAU,CAAC;AACnB,CAAC;AACD;AACA,aAAa,CAAC,UAAU,EAAE,kBAAkB,EAAE,SAAS,CAAC;;ACxyBjD,MAAM,WAAW,SAASE,gBAAS,CAAC;AAC3C,CAAC,WAAW,CAAC,OAAO,EAAE;AACtB,EAAE,IAAI,CAAC,OAAO;AACd,GAAG,OAAO,GAAG,GAAE;AACf,EAAE,OAAO,CAAC,kBAAkB,GAAG,KAAI;AACnC,EAAE,KAAK,CAAC,OAAO,EAAC;AAChB,EAAE,OAAO,CAAC,UAAU,GAAG,KAAI;AAC3B,EAAE,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,IAAI,KAAK,CAAC,OAAO,EAAC;AAClD,EAAE;AACF,CAAC,UAAU,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE;AACvC,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAC;AACnC,EAAE,QAAQ,GAAE;AACZ,EAAE;AACF,CAAC;AACD;AACO,MAAM,aAAa,SAASA,gBAAS,CAAC;AAC7C,CAAC,WAAW,CAAC,OAAO,EAAE;AACtB,EAAE,IAAI,CAAC,OAAO;AACd,GAAG,OAAO,GAAG,GAAE;AACf,EAAE,OAAO,CAAC,UAAU,GAAG,KAAI;AAC3B,EAAE,KAAK,CAAC,OAAO,EAAC;AAChB,EAAE,OAAO,CAAC,UAAU,GAAG,GAAE;AACzB,EAAE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,IAAI,OAAO,CAAC,OAAO,EAAC;AACxD,EAAE;AACF,CAAC,UAAU,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE;AACvC,EAAE,IAAI,IAAI,CAAC,gBAAgB,EAAE;AAC7B,GAAG,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,gBAAgB,EAAE,KAAK,CAAC,EAAC;AACxD,GAAG,IAAI,CAAC,gBAAgB,GAAG,KAAI;AAC/B,GAAG;AACH,EAAE,IAAI,OAAM;AACZ,EAAE,IAAI;AACN,GAAG,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,EAAC;AAC9C,GAAG,CAAC,MAAM,KAAK,EAAE;AACjB,GAAG,IAAI,KAAK,CAAC,UAAU,EAAE;AACzB,IAAI,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,YAAY,EAAC;AAC3D,IAAI,MAAM,GAAG,KAAK,CAAC,OAAM;AACzB,IAAI;AACJ;AACA,IAAI,MAAM,KAAK;AACf,GAAG,SAAS;AACZ,GAAG,KAAK,IAAI,KAAK,IAAI,MAAM,IAAI,EAAE,EAAE;AACnC,IAAI,IAAI,KAAK,KAAK,IAAI;AACtB,KAAK,KAAK,GAAG,IAAI,CAAC,YAAY,GAAE;AAChC,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAC;AACpB,IAAI;AACJ,GAAG;AACH,EAAE,IAAI,QAAQ,EAAE,QAAQ,GAAE;AAC1B,EAAE;AACF,CAAC,YAAY,GAAG;AAChB,EAAE,OAAO,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;AACzB,EAAE;AACF;;ACrDA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,QAAQ,EAAE,cAAc,EAAE,OAAO,GAAG,EAAE,EAAE;AACxD,EAAE,IAAI,CAAC,cAAc,IAAI,OAAO,cAAc,KAAK,QAAQ,EAAE;AAC7D,IAAI,MAAM,IAAI,KAAK,CAAC,wFAAwF,CAAC;AAC7G,GAAG,MAAM,IAAI,OAAO,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,UAAU,EAAE;AACpE,IAAI,OAAO,YAAY,CAAC,cAAc,EAAE,OAAO,CAAC;AAChD,GAAG,MAAM,IAAI,OAAO,cAAc,CAAC,IAAI,KAAK,UAAU,IAAI,OAAO,cAAc,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,UAAU,EAAE;AACtH,IAAI,OAAO,aAAa,CAAC,cAAc,EAAE,OAAO,CAAC;AACjD,GAAG,MAAM;AACT,IAAI,MAAM,IAAI,KAAK,CAAC,4FAA4F,CAAC;AACjH,GAAG;AACH,CAAC;AACD;AACA,WAAW,YAAY,EAAE,cAAc,EAAE,OAAO,EAAE;AAClD,EAAE,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,OAAO,EAAC;AAClC,EAAE,KAAK,MAAM,KAAK,IAAI,cAAc,EAAE;AACtC,IAAI,MAAM,KAAK,CAAC,IAAI,CAAC,KAAK,EAAC;AAC3B,GAAG;AACH,CAAC;AACD;AACA,iBAAiB,aAAa,EAAE,cAAc,EAAE,OAAO,EAAE;AACzD,EAAE,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,OAAO,EAAC;AAClC,EAAE,WAAW,MAAM,KAAK,IAAI,cAAc,EAAE;AAC5C,IAAI,MAAM,KAAK,CAAC,IAAI,CAAC,KAAK,EAAC;AAC3B,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,UAAU,EAAE,cAAc,EAAE,OAAO,GAAG,EAAE,EAAE;AAC1D,EAAE,IAAI,CAAC,cAAc,IAAI,OAAO,cAAc,KAAK,QAAQ,EAAE;AAC7D,IAAI,MAAM,IAAI,KAAK,CAAC,4FAA4F,CAAC;AACjH,GAAG;AACH;AACA,EAAE,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,OAAO,EAAC;AACtC,EAAE,IAAI,WAAU;AAChB,EAAE,MAAM,MAAM,GAAG,CAAC,KAAK,KAAK;AAC5B,IAAI,IAAI,OAAM;AACd;AACA,IAAI,IAAI,UAAU,EAAE;AACpB,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,KAAK,CAAC,EAAC;AAChD,MAAM,UAAU,GAAG,UAAS;AAC5B,KAAK;AACL;AACA,IAAI,IAAI;AACR,MAAM,MAAM,GAAG,OAAO,CAAC,cAAc,CAAC,KAAK,EAAC;AAC5C,KAAK,CAAC,OAAO,GAAG,EAAE;AAClB,MAAM,IAAI,GAAG,CAAC,UAAU,EAAE;AAC1B,QAAQ,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,YAAY,EAAC;AAClD,QAAQ,MAAM,GAAG,GAAG,CAAC,OAAM;AAC3B,OAAO,MAAM;AACb,QAAQ,MAAM,GAAG;AACjB,OAAO;AACP,KAAK;AACL,IAAI,OAAO,MAAM;AACjB,IAAG;AACH;AACA,EAAE,IAAI,OAAO,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,UAAU,EAAE;AAC7D,IAAI,OAAO,CAAC,WAAW,IAAI,IAAI;AAC/B,MAAM,KAAK,MAAM,KAAK,IAAI,cAAc,EAAE;AAC1C,QAAQ,QAAQ,MAAM,CAAC,KAAK,EAAC;AAC7B,OAAO;AACP,KAAK,GAAG;AACR,GAAG,MAAM,IAAI,OAAO,cAAc,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,UAAU,EAAE;AACzE,IAAI,OAAO,CAAC,iBAAiB,IAAI,IAAI;AACrC,MAAM,WAAW,MAAM,KAAK,IAAI,cAAc,EAAE;AAChD,QAAQ,QAAQ,MAAM,CAAC,KAAK,EAAC;AAC7B,OAAO;AACP,KAAK,GAAG;AACR,GAAG;AACH,CAAC;AACW,MAAC,UAAU,GAAG,WAAU;AACxB,MAAC,UAAU,GAAG;;ACjFd,MAAC,UAAU,GAAG,MAAK;AACnB,MAAC,aAAa,GAAG,KAAI;AAGjC;AACA,MAAM,0BAA0B,GAAG,OAAO,CAAC,GAAG,CAAC,qCAAqC,KAAK,SAAS,IAAI,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,WAAW,EAAE,KAAK,MAAM,CAAC;AACjL;AACA,IAAI,CAAC,0BAA0B,EAAE;AACjC,CAAC,IAAI,UAAS;AACd,CAAC,IAAI;AACL,EAAE,IAAI,OAAO,OAAO,IAAI,UAAU;AAClC,GAAG,SAAS,GAAG,OAAO,CAAC,kBAAkB,EAAC;AAC1C;AACA,GAAG,SAAS,GAAGC,sBAAa,CAAC,mMAAe,CAAC,CAAC,kBAAkB,EAAC;AACjE,EAAE,IAAI,SAAS;AACf,GAAG,YAAY,CAAC,SAAS,CAAC,cAAc,EAAC;AACzC,EAAE,CAAC,OAAO,KAAK,EAAE;AACjB;AACA,EAAE;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}