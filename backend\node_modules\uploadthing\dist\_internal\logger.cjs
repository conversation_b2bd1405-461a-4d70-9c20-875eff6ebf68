var Config = require('effect/Config');
var ConfigError = require('effect/ConfigError');
var Effect = require('effect/Effect');
var Either = require('effect/Either');
var Layer = require('effect/Layer');
var Logger = require('effect/Logger');
var LogLevel = require('effect/LogLevel');
var shared = require('@uploadthing/shared');
var config_cjs = require('./config.cjs');

function _interopNamespace(e) {
  if (e && e.__esModule) return e;
  var n = Object.create(null);
  if (e) {
    Object.keys(e).forEach(function (k) {
      if (k !== 'default') {
        var d = Object.getOwnPropertyDescriptor(e, k);
        Object.defineProperty(n, k, d.get ? d : {
          enumerable: true,
          get: function () { return e[k]; }
        });
      }
    });
  }
  n.default = e;
  return n;
}

var Config__namespace = /*#__PURE__*/_interopNamespace(Config);
var ConfigError__namespace = /*#__PURE__*/_interopNamespace(ConfigError);
var Effect__namespace = /*#__PURE__*/_interopNamespace(Effect);
var Either__namespace = /*#__PURE__*/_interopNamespace(Either);
var Layer__namespace = /*#__PURE__*/_interopNamespace(Layer);
var Logger__namespace = /*#__PURE__*/_interopNamespace(Logger);
var LogLevel__namespace = /*#__PURE__*/_interopNamespace(LogLevel);

/**
 * Config.logLevel counter-intuitively accepts LogLevel["label"]
 * instead of a literal, ripping it and changing to accept literal
 * Effect 4.0 will change this to accept a literal and then we can
 * remove this and go back to the built-in validator.
 */ const ConfigLogLevel = (name)=>{
    const config = Config__namespace.mapOrFail(Config__namespace.string(), (literal)=>{
        const level = LogLevel__namespace.allLevels.find((level)=>level._tag === literal);
        return level === undefined ? Either__namespace.left(ConfigError__namespace.InvalidData([], `Expected a log level but received ${literal}`)) : Either__namespace.right(level);
    });
    return name === undefined ? config : Config__namespace.nested(config, name);
};
const withMinimalLogLevel = ConfigLogLevel("logLevel").pipe(Config__namespace.withDefault(LogLevel__namespace.Info), Effect__namespace.andThen((level)=>Logger__namespace.minimumLogLevel(level)), Effect__namespace.tapError((e)=>Effect__namespace.logError("Invalid log level").pipe(Effect__namespace.annotateLogs("error", e))), Effect__namespace.catchTag("ConfigError", (e)=>new shared.UploadThingError({
        code: "INVALID_SERVER_CONFIG",
        message: "Invalid server configuration",
        cause: e
    })), Layer__namespace.unwrapEffect);
const LogFormat = Config__namespace.literal("json", "logFmt", "structured", "pretty")("logFormat");
const withLogFormat = Effect__namespace.gen(function*() {
    const isDev = yield* config_cjs.IsDevelopment;
    const logFormat = yield* LogFormat.pipe(Config__namespace.withDefault(isDev ? "pretty" : "json"));
    return Logger__namespace[logFormat];
}).pipe(Effect__namespace.catchTag("ConfigError", (e)=>new shared.UploadThingError({
        code: "INVALID_SERVER_CONFIG",
        message: "Invalid server configuration",
        cause: e
    })), Layer__namespace.unwrapEffect);
const logHttpClientResponse = (message, opts)=>{
    const mixin = opts?.mixin ?? "json";
    const level = LogLevel__namespace.fromLiteral(opts?.level ?? "Debug");
    return (response)=>Effect__namespace.flatMap(mixin !== "None" ? response[mixin] : Effect__namespace.void, ()=>Effect__namespace.logWithLevel(level, `${message} (${response.status})`).pipe(Effect__namespace.annotateLogs("response", response)));
};
const logHttpClientError = (message)=>(err)=>err._tag === "ResponseError" ? logHttpClientResponse(message, {
            level: "Error"
        })(err.response) : Effect__namespace.logError(message).pipe(Effect__namespace.annotateLogs("error", err));

exports.LogFormat = LogFormat;
exports.logHttpClientError = logHttpClientError;
exports.logHttpClientResponse = logHttpClientResponse;
exports.withLogFormat = withLogFormat;
exports.withMinimalLogLevel = withMinimalLogLevel;
