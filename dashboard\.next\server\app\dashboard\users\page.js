(()=>{var e={};e.id=9242,e.ids=[9242],e.modules={1614:(e,t,r)=>{"use strict";r.d(t,{InviteUserDialog:()=>f});var n=r(60687),a=r(43210),s=r(24934),i=r(37826),o=r(68988),l=r(39390),d=r(63974),c=r(71702),u=r(41862);function f({children:e}){let[t,r]=(0,a.useState)(!1),[f,m]=(0,a.useState)(""),[h,p]=(0,a.useState)(""),[g,x]=(0,a.useState)(""),[b,w]=(0,a.useState)(""),[v,y]=(0,a.useState)([]),[j,N]=(0,a.useState)(!1),[C,k]=(0,a.useState)(!1),{toast:D}=(0,c.dj)(),P=async()=>{if(!f||!b){D({title:"Missing information",description:"Please fill in all required fields.",variant:"destructive"});return}try{k(!0),await new Promise(e=>setTimeout(e,1e3)),D({title:"Invitation sent",description:`An invitation has been sent to ${f}.`}),R(),r(!1)}catch(e){console.error("Error sending invitation:",e),D({title:"Error",description:"Failed to send invitation. Please try again.",variant:"destructive"})}finally{k(!1)}},R=()=>{m(""),p(""),x(""),w("")};return(0,n.jsxs)(i.lG,{open:t,onOpenChange:e=>{r(e),e||R()},children:[(0,n.jsx)(i.zM,{asChild:!0,children:e}),(0,n.jsxs)(i.Cf,{className:"sm:max-w-[425px]",children:[(0,n.jsxs)(i.c7,{children:[(0,n.jsx)(i.L3,{children:"Invite User"}),(0,n.jsx)(i.rr,{children:"Send an invitation to a new user to join the dashboard."})]}),(0,n.jsxs)("div",{className:"space-y-4 py-4",children:[(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(l.J,{htmlFor:"email",children:"Email *"}),(0,n.jsx)(o.p,{id:"email",placeholder:"Enter email address",type:"email",value:f,onChange:e=>m(e.target.value),required:!0})]}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(l.J,{htmlFor:"firstName",children:"First Name"}),(0,n.jsx)(o.p,{id:"firstName",placeholder:"First name",value:h,onChange:e=>p(e.target.value)})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(l.J,{htmlFor:"lastName",children:"Last Name"}),(0,n.jsx)(o.p,{id:"lastName",placeholder:"Last name",value:g,onChange:e=>x(e.target.value)})]})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(l.J,{htmlFor:"role",children:"Role *"}),j?(0,n.jsxs)("div",{className:"flex items-center space-x-2 h-10 px-3 border rounded-md",children:[(0,n.jsx)(u.A,{className:"h-4 w-4 animate-spin"}),(0,n.jsx)("span",{className:"text-sm text-muted-foreground",children:"Loading roles..."})]}):(0,n.jsxs)(d.l6,{value:b,onValueChange:w,children:[(0,n.jsx)(d.bq,{id:"role",children:(0,n.jsx)(d.yv,{placeholder:"Select role"})}),(0,n.jsx)(d.gC,{children:v.map(e=>(0,n.jsxs)(d.eb,{value:e.id,children:[(0,n.jsx)("span",{className:"capitalize",children:e.name}),e.description&&(0,n.jsxs)("span",{className:"text-muted-foreground ml-2 text-xs",children:["- ",e.description]})]},e.id))})]})]})]}),(0,n.jsxs)(i.Es,{children:[(0,n.jsx)(s.$,{variant:"outline",onClick:()=>r(!1),disabled:C,children:"Cancel"}),(0,n.jsx)(s.$,{onClick:P,disabled:C||j,children:C?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(u.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Sending..."]}):"Send Invitation"})]})]})]})}},1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4144:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>f,tree:()=>d});var n=r(65239),a=r(48088),s=r(88170),i=r.n(s),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["dashboard",{children:["users",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,13444)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\users\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,83249)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,60566)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\users\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},f=new n.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/users/page",pathname:"/dashboard/users",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},5486:e=>{"use strict";e.exports=require("bcrypt")},7066:e=>{"use strict";e.exports=require("node:tty")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13444:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>N});var n=r(37413),a=r(88063),s=r(61120);function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}var o=s.forwardRef((e,t)=>{let{children:r,...a}=e,i=s.Children.toArray(r),o=i.find(c);if(o){let e=o.props.children,r=i.map(t=>t!==o?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,n.jsx)(l,{...a,ref:t,children:s.isValidElement(e)?s.cloneElement(e,void 0,r):null})}return(0,n.jsx)(l,{...a,ref:t,children:r})});o.displayName="Slot";var l=s.forwardRef((e,t)=>{let{children:r,...n}=e;if(s.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r);return s.cloneElement(r,{...function(e,t){let r={...t};for(let n in t){let a=e[n],s=t[n];/^on[A-Z]/.test(n)?a&&s?r[n]=(...e)=>{s(...e),a(...e)}:a&&(r[n]=a):"style"===n?r[n]={...a,...s}:"className"===n&&(r[n]=[a,s].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props),ref:t?function(...e){return t=>{let r=!1,n=e.map(e=>{let n=i(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():i(e[t],null)}}}}(t,e):e})}return s.Children.count(r)>1?s.Children.only(null):null});l.displayName="SlotClone";var d=({children:e})=>(0,n.jsx)(n.Fragment,{children:e});function c(e){return s.isValidElement(e)&&e.type===d}var u=r(75986);let f=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,m=u.$;var h=r(66819);let p=((e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return m(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:a,defaultVariants:s}=t,i=Object.keys(a).map(e=>{let t=null==r?void 0:r[e],n=null==s?void 0:s[e];if(null===t)return null;let i=f(t)||f(n);return a[e][i]}),o=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return m(e,i,null==t?void 0:null===(n=t.compoundVariants)||void 0===n?void 0:n.reduce((e,t)=>{let{class:r,className:n,...a}=t;return Object.entries(a).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...s,...o}[t]):({...s,...o})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)})("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),g=s.forwardRef(({className:e,variant:t,size:r,asChild:a=!1,...s},i)=>{let l=a?o:"button";return(0,n.jsx)(l,{className:(0,h.cn)(p({variant:t,size:r,className:e})),ref:i,...s})});g.displayName="Button";let x=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),b=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim();var w={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let v=(0,s.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:n,className:a="",children:i,iconNode:o,...l},d)=>(0,s.createElement)("svg",{ref:d,...w,width:t,height:t,stroke:e,strokeWidth:n?24*Number(r)/Number(t):r,className:b("lucide",a),...l},[...o.map(([e,t])=>(0,s.createElement)(e,t)),...Array.isArray(i)?i:[i]])),y=((e,t)=>{let r=(0,s.forwardRef)(({className:r,...n},a)=>(0,s.createElement)(v,{ref:a,iconNode:t,className:b(`lucide-${x(e)}`,r),...n}));return r.displayName=`${e}`,r})("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]]);var j=r(95180);function N(){return(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Users"}),(0,n.jsx)(j.InviteUserDialog,{children:(0,n.jsxs)(g,{children:[(0,n.jsx)(y,{className:"mr-2 h-4 w-4"}),"Invite User"]})})]}),(0,n.jsx)(a.UsersTable,{})]})}},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22506:(e,t,r)=>{Promise.resolve().then(r.bind(r,95180)),Promise.resolve().then(r.bind(r,88063))},26134:(e,t,r)=>{"use strict";r.d(t,{G$:()=>W,Hs:()=>y,UC:()=>et,VY:()=>en,ZL:()=>K,bL:()=>X,bm:()=>ea,hE:()=>er,hJ:()=>ee,l9:()=>Z});var n=r(43210),a=r(70569),s=r(98599),i=r(11273),o=r(96963),l=r(65551),d=r(31355),c=r(32547),u=r(25028),f=r(46059),m=r(14163),h=r(1359),p=r(11490),g=r(63376),x=r(8730),b=r(60687),w="Dialog",[v,y]=(0,i.A)(w),[j,N]=v(w),C=e=>{let{__scopeDialog:t,children:r,open:a,defaultOpen:s,onOpenChange:i,modal:d=!0}=e,c=n.useRef(null),u=n.useRef(null),[f=!1,m]=(0,l.i)({prop:a,defaultProp:s,onChange:i});return(0,b.jsx)(j,{scope:t,triggerRef:c,contentRef:u,contentId:(0,o.B)(),titleId:(0,o.B)(),descriptionId:(0,o.B)(),open:f,onOpenChange:m,onOpenToggle:n.useCallback(()=>m(e=>!e),[m]),modal:d,children:r})};C.displayName=w;var k="DialogTrigger",D=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=N(k,r),o=(0,s.s)(t,i.triggerRef);return(0,b.jsx)(m.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":G(i.open),...n,ref:o,onClick:(0,a.m)(e.onClick,i.onOpenToggle)})});D.displayName=k;var P="DialogPortal",[R,E]=v(P,{forceMount:void 0}),S=e=>{let{__scopeDialog:t,forceMount:r,children:a,container:s}=e,i=N(P,t);return(0,b.jsx)(R,{scope:t,forceMount:r,children:n.Children.map(a,e=>(0,b.jsx)(f.C,{present:r||i.open,children:(0,b.jsx)(u.Z,{asChild:!0,container:s,children:e})}))})};S.displayName=P;var M="DialogOverlay",q=n.forwardRef((e,t)=>{let r=E(M,e.__scopeDialog),{forceMount:n=r.forceMount,...a}=e,s=N(M,e.__scopeDialog);return s.modal?(0,b.jsx)(f.C,{present:n||s.open,children:(0,b.jsx)(A,{...a,ref:t})}):null});q.displayName=M;var A=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=N(M,r);return(0,b.jsx)(p.A,{as:x.DX,allowPinchZoom:!0,shards:[a.contentRef],children:(0,b.jsx)(m.sG.div,{"data-state":G(a.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),O="DialogContent",T=n.forwardRef((e,t)=>{let r=E(O,e.__scopeDialog),{forceMount:n=r.forceMount,...a}=e,s=N(O,e.__scopeDialog);return(0,b.jsx)(f.C,{present:n||s.open,children:s.modal?(0,b.jsx)(F,{...a,ref:t}):(0,b.jsx)(I,{...a,ref:t})})});T.displayName=O;var F=n.forwardRef((e,t)=>{let r=N(O,e.__scopeDialog),i=n.useRef(null),o=(0,s.s)(t,r.contentRef,i);return n.useEffect(()=>{let e=i.current;if(e)return(0,g.Eq)(e)},[]),(0,b.jsx)(L,{...e,ref:o,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,a.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,a.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,a.m)(e.onFocusOutside,e=>e.preventDefault())})}),I=n.forwardRef((e,t)=>{let r=N(O,e.__scopeDialog),a=n.useRef(!1),s=n.useRef(!1);return(0,b.jsx)(L,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(a.current||r.triggerRef.current?.focus(),t.preventDefault()),a.current=!1,s.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(a.current=!0,"pointerdown"!==t.detail.originalEvent.type||(s.current=!0));let n=t.target;r.triggerRef.current?.contains(n)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&s.current&&t.preventDefault()}})}),L=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:a,onOpenAutoFocus:i,onCloseAutoFocus:o,...l}=e,u=N(O,r),f=n.useRef(null),m=(0,s.s)(t,f);return(0,h.Oh)(),(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(c.n,{asChild:!0,loop:!0,trapped:a,onMountAutoFocus:i,onUnmountAutoFocus:o,children:(0,b.jsx)(d.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":G(u.open),...l,ref:m,onDismiss:()=>u.onOpenChange(!1)})}),(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(V,{titleId:u.titleId}),(0,b.jsx)(J,{contentRef:f,descriptionId:u.descriptionId})]})]})}),_="DialogTitle",$=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=N(_,r);return(0,b.jsx)(m.sG.h2,{id:a.titleId,...n,ref:t})});$.displayName=_;var z="DialogDescription",H=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=N(z,r);return(0,b.jsx)(m.sG.p,{id:a.descriptionId,...n,ref:t})});H.displayName=z;var U="DialogClose",Y=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,s=N(U,r);return(0,b.jsx)(m.sG.button,{type:"button",...n,ref:t,onClick:(0,a.m)(e.onClick,()=>s.onOpenChange(!1))})});function G(e){return e?"open":"closed"}Y.displayName=U;var B="DialogTitleWarning",[W,Q]=(0,i.q)(B,{contentName:O,titleName:_,docsSlug:"dialog"}),V=({titleId:e})=>{let t=Q(B),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return n.useEffect(()=>{e&&!document.getElementById(e)&&console.error(r)},[r,e]),null},J=({contentRef:e,descriptionId:t})=>{let r=Q("DialogDescriptionWarning"),a=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return n.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&!document.getElementById(t)&&console.warn(a)},[a,e,t]),null},X=C,Z=D,K=S,ee=q,et=T,er=$,en=H,ea=Y},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},37826:(e,t,r)=>{"use strict";r.d(t,{Cf:()=>f,Es:()=>h,L3:()=>p,c7:()=>m,lG:()=>l,rr:()=>g,zM:()=>d});var n=r(60687),a=r(43210),s=r(26134),i=r(11860),o=r(96241);let l=s.bL,d=s.l9,c=s.ZL;s.bm;let u=a.forwardRef(({className:e,...t},r)=>(0,n.jsx)(s.hJ,{ref:r,className:(0,o.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));u.displayName=s.hJ.displayName;let f=a.forwardRef(({className:e,children:t,...r},a)=>(0,n.jsxs)(c,{children:[(0,n.jsx)(u,{}),(0,n.jsxs)(s.UC,{ref:a,className:(0,o.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...r,children:[t,(0,n.jsxs)(s.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,n.jsx)(i.A,{className:"h-4 w-4"}),(0,n.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));f.displayName=s.UC.displayName;let m=({className:e,...t})=>(0,n.jsx)("div",{className:(0,o.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});m.displayName="DialogHeader";let h=({className:e,...t})=>(0,n.jsx)("div",{className:(0,o.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});h.displayName="DialogFooter";let p=a.forwardRef(({className:e,...t},r)=>(0,n.jsx)(s.hE,{ref:r,className:(0,o.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));p.displayName=s.hE.displayName;let g=a.forwardRef(({className:e,...t},r)=>(0,n.jsx)(s.VY,{ref:r,className:(0,o.cn)("text-sm text-muted-foreground",e),...t}));g.displayName=s.VY.displayName},39390:(e,t,r)=>{"use strict";r.d(t,{J:()=>d});var n=r(60687),a=r(43210),s=r(78148),i=r(24224),o=r(96241);let l=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef(({className:e,...t},r)=>(0,n.jsx)(s.b,{ref:r,className:(0,o.cn)(l(),e),...t}));d.displayName=s.b.displayName},48161:e=>{"use strict";e.exports=require("node:os")},51455:e=>{"use strict";e.exports=require("node:fs/promises")},55511:e=>{"use strict";e.exports=require("crypto")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63974:(e,t,r)=>{"use strict";r.d(t,{bq:()=>f,eb:()=>g,gC:()=>p,l6:()=>c,yv:()=>u});var n=r(60687),a=r(43210),s=r(22670),i=r(78272),o=r(3589),l=r(13964),d=r(96241);let c=s.bL;s.YJ;let u=s.WT,f=a.forwardRef(({className:e,children:t,...r},a)=>(0,n.jsxs)(s.l9,{ref:a,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...r,children:[t,(0,n.jsx)(s.In,{asChild:!0,children:(0,n.jsx)(i.A,{className:"h-4 w-4 opacity-50"})})]}));f.displayName=s.l9.displayName;let m=a.forwardRef(({className:e,...t},r)=>(0,n.jsx)(s.PP,{ref:r,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,n.jsx)(o.A,{className:"h-4 w-4"})}));m.displayName=s.PP.displayName;let h=a.forwardRef(({className:e,...t},r)=>(0,n.jsx)(s.wn,{ref:r,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,n.jsx)(i.A,{className:"h-4 w-4"})}));h.displayName=s.wn.displayName;let p=a.forwardRef(({className:e,children:t,position:r="popper",...a},i)=>(0,n.jsx)(s.ZL,{children:(0,n.jsxs)(s.UC,{ref:i,className:(0,d.cn)("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...a,children:[(0,n.jsx)(m,{}),(0,n.jsx)(s.LM,{className:(0,d.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,n.jsx)(h,{})]})}));p.displayName=s.UC.displayName,a.forwardRef(({className:e,...t},r)=>(0,n.jsx)(s.JU,{ref:r,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=s.JU.displayName;let g=a.forwardRef(({className:e,children:t,...r},a)=>(0,n.jsxs)(s.q7,{ref:a,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...r,children:[(0,n.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,n.jsx)(s.VF,{children:(0,n.jsx)(l.A,{className:"h-4 w-4"})})}),(0,n.jsx)(s.p4,{children:t})]}));g.displayName=s.q7.displayName,a.forwardRef(({className:e,...t},r)=>(0,n.jsx)(s.wv,{ref:r,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=s.wv.displayName},66819:(e,t,r)=>{"use strict";r.d(t,{cn:()=>s});var n=r(75986),a=r(8974);function s(...e){return(0,a.QP)((0,n.$)(e))}},68988:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var n=r(60687),a=r(43210),s=r(96241);let i=a.forwardRef(({className:e,type:t,...r},a)=>(0,n.jsx)("input",{type:t,className:(0,s.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...r}));i.displayName="Input"},73024:e=>{"use strict";e.exports=require("node:fs")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},77759:(e,t,r)=>{"use strict";r.d(t,{UsersTable:()=>V});var n=r(60687),a=r(43210),s=r(96752),i=r(55629),o=r(24934),l=r(41862),d=r(93661);let c=(0,r(62688).A)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);var u=r(63143),f=r(96362),m=r(70373),h=r(59821),p=r(37826),g=r(63974),x=r(39390),b=r(71702),w=r(64722),v=r(78872),y=r(31504),j=r(29789),N=r(58505),C=r(23711);function k(e,t){let r=(0,C.a)(e,t?.in);return r.setHours(0,0,0,0),r}function D(e,t){let r=(0,v.q)(),n=t?.weekStartsOn??t?.locale?.options?.weekStartsOn??r.weekStartsOn??r.locale?.options?.weekStartsOn??0,a=(0,C.a)(e,t?.in),s=a.getDay();return a.setDate(a.getDate()-(7*(s<n)+s-n)),a.setHours(0,0,0,0),a}function P(e,t){return D(e,{...t,weekStartsOn:1})}var R=r(87981);function E(e,t){let r=(0,C.a)(e,t?.in),n=r.getFullYear(),a=(0,R.w)(r,0);a.setFullYear(n+1,0,4),a.setHours(0,0,0,0);let s=P(a),i=(0,R.w)(r,0);i.setFullYear(n,0,4),i.setHours(0,0,0,0);let o=P(i);return r.getTime()>=s.getTime()?n+1:r.getTime()>=o.getTime()?n:n-1}function S(e,t){let r=(0,C.a)(e,t?.in),n=r.getFullYear(),a=(0,v.q)(),s=t?.firstWeekContainsDate??t?.locale?.options?.firstWeekContainsDate??a.firstWeekContainsDate??a.locale?.options?.firstWeekContainsDate??1,i=(0,R.w)(t?.in||e,0);i.setFullYear(n+1,0,s),i.setHours(0,0,0,0);let o=D(i,t),l=(0,R.w)(t?.in||e,0);l.setFullYear(n,0,s),l.setHours(0,0,0,0);let d=D(l,t);return+r>=+o?n+1:+r>=+d?n:n-1}function M(e,t){let r=Math.abs(e).toString().padStart(t,"0");return(e<0?"-":"")+r}let q={y(e,t){let r=e.getFullYear(),n=r>0?r:1-r;return M("yy"===t?n%100:n,t.length)},M(e,t){let r=e.getMonth();return"M"===t?String(r+1):M(r+1,2)},d:(e,t)=>M(e.getDate(),t.length),a(e,t){let r=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return r.toUpperCase();case"aaa":return r;case"aaaaa":return r[0];default:return"am"===r?"a.m.":"p.m."}},h:(e,t)=>M(e.getHours()%12||12,t.length),H:(e,t)=>M(e.getHours(),t.length),m:(e,t)=>M(e.getMinutes(),t.length),s:(e,t)=>M(e.getSeconds(),t.length),S(e,t){let r=t.length;return M(Math.trunc(e.getMilliseconds()*Math.pow(10,r-3)),t.length)}},A={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},O={G:function(e,t,r){let n=+(e.getFullYear()>0);switch(t){case"G":case"GG":case"GGG":return r.era(n,{width:"abbreviated"});case"GGGGG":return r.era(n,{width:"narrow"});default:return r.era(n,{width:"wide"})}},y:function(e,t,r){if("yo"===t){let t=e.getFullYear();return r.ordinalNumber(t>0?t:1-t,{unit:"year"})}return q.y(e,t)},Y:function(e,t,r,n){let a=S(e,n),s=a>0?a:1-a;return"YY"===t?M(s%100,2):"Yo"===t?r.ordinalNumber(s,{unit:"year"}):M(s,t.length)},R:function(e,t){return M(E(e),t.length)},u:function(e,t){return M(e.getFullYear(),t.length)},Q:function(e,t,r){let n=Math.ceil((e.getMonth()+1)/3);switch(t){case"Q":return String(n);case"QQ":return M(n,2);case"Qo":return r.ordinalNumber(n,{unit:"quarter"});case"QQQ":return r.quarter(n,{width:"abbreviated",context:"formatting"});case"QQQQQ":return r.quarter(n,{width:"narrow",context:"formatting"});default:return r.quarter(n,{width:"wide",context:"formatting"})}},q:function(e,t,r){let n=Math.ceil((e.getMonth()+1)/3);switch(t){case"q":return String(n);case"qq":return M(n,2);case"qo":return r.ordinalNumber(n,{unit:"quarter"});case"qqq":return r.quarter(n,{width:"abbreviated",context:"standalone"});case"qqqqq":return r.quarter(n,{width:"narrow",context:"standalone"});default:return r.quarter(n,{width:"wide",context:"standalone"})}},M:function(e,t,r){let n=e.getMonth();switch(t){case"M":case"MM":return q.M(e,t);case"Mo":return r.ordinalNumber(n+1,{unit:"month"});case"MMM":return r.month(n,{width:"abbreviated",context:"formatting"});case"MMMMM":return r.month(n,{width:"narrow",context:"formatting"});default:return r.month(n,{width:"wide",context:"formatting"})}},L:function(e,t,r){let n=e.getMonth();switch(t){case"L":return String(n+1);case"LL":return M(n+1,2);case"Lo":return r.ordinalNumber(n+1,{unit:"month"});case"LLL":return r.month(n,{width:"abbreviated",context:"standalone"});case"LLLLL":return r.month(n,{width:"narrow",context:"standalone"});default:return r.month(n,{width:"wide",context:"standalone"})}},w:function(e,t,r,n){let a=function(e,t){let r=(0,C.a)(e,t?.in);return Math.round((+D(r,t)-+function(e,t){let r=(0,v.q)(),n=t?.firstWeekContainsDate??t?.locale?.options?.firstWeekContainsDate??r.firstWeekContainsDate??r.locale?.options?.firstWeekContainsDate??1,a=S(e,t),s=(0,R.w)(t?.in||e,0);return s.setFullYear(a,0,n),s.setHours(0,0,0,0),D(s,t)}(r,t))/N.my)+1}(e,n);return"wo"===t?r.ordinalNumber(a,{unit:"week"}):M(a,t.length)},I:function(e,t,r){let n=function(e,t){let r=(0,C.a)(e,void 0);return Math.round((+P(r)-+function(e,t){let r=E(e,void 0),n=(0,R.w)((void 0)||e,0);return n.setFullYear(r,0,4),n.setHours(0,0,0,0),P(n)}(r))/N.my)+1}(e);return"Io"===t?r.ordinalNumber(n,{unit:"week"}):M(n,t.length)},d:function(e,t,r){return"do"===t?r.ordinalNumber(e.getDate(),{unit:"date"}):q.d(e,t)},D:function(e,t,r){let n=function(e,t){let r=(0,C.a)(e,void 0);return function(e,t,r){let[n,a]=(0,j.x)(void 0,e,t),s=k(n),i=k(a);return Math.round((+s-(0,y.G)(s)-(+i-(0,y.G)(i)))/N.w4)}(r,function(e,t){let r=(0,C.a)(e,void 0);return r.setFullYear(r.getFullYear(),0,1),r.setHours(0,0,0,0),r}(r))+1}(e);return"Do"===t?r.ordinalNumber(n,{unit:"dayOfYear"}):M(n,t.length)},E:function(e,t,r){let n=e.getDay();switch(t){case"E":case"EE":case"EEE":return r.day(n,{width:"abbreviated",context:"formatting"});case"EEEEE":return r.day(n,{width:"narrow",context:"formatting"});case"EEEEEE":return r.day(n,{width:"short",context:"formatting"});default:return r.day(n,{width:"wide",context:"formatting"})}},e:function(e,t,r,n){let a=e.getDay(),s=(a-n.weekStartsOn+8)%7||7;switch(t){case"e":return String(s);case"ee":return M(s,2);case"eo":return r.ordinalNumber(s,{unit:"day"});case"eee":return r.day(a,{width:"abbreviated",context:"formatting"});case"eeeee":return r.day(a,{width:"narrow",context:"formatting"});case"eeeeee":return r.day(a,{width:"short",context:"formatting"});default:return r.day(a,{width:"wide",context:"formatting"})}},c:function(e,t,r,n){let a=e.getDay(),s=(a-n.weekStartsOn+8)%7||7;switch(t){case"c":return String(s);case"cc":return M(s,t.length);case"co":return r.ordinalNumber(s,{unit:"day"});case"ccc":return r.day(a,{width:"abbreviated",context:"standalone"});case"ccccc":return r.day(a,{width:"narrow",context:"standalone"});case"cccccc":return r.day(a,{width:"short",context:"standalone"});default:return r.day(a,{width:"wide",context:"standalone"})}},i:function(e,t,r){let n=e.getDay(),a=0===n?7:n;switch(t){case"i":return String(a);case"ii":return M(a,t.length);case"io":return r.ordinalNumber(a,{unit:"day"});case"iii":return r.day(n,{width:"abbreviated",context:"formatting"});case"iiiii":return r.day(n,{width:"narrow",context:"formatting"});case"iiiiii":return r.day(n,{width:"short",context:"formatting"});default:return r.day(n,{width:"wide",context:"formatting"})}},a:function(e,t,r){let n=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return r.dayPeriod(n,{width:"abbreviated",context:"formatting"});case"aaa":return r.dayPeriod(n,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return r.dayPeriod(n,{width:"narrow",context:"formatting"});default:return r.dayPeriod(n,{width:"wide",context:"formatting"})}},b:function(e,t,r){let n;let a=e.getHours();switch(n=12===a?A.noon:0===a?A.midnight:a/12>=1?"pm":"am",t){case"b":case"bb":return r.dayPeriod(n,{width:"abbreviated",context:"formatting"});case"bbb":return r.dayPeriod(n,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return r.dayPeriod(n,{width:"narrow",context:"formatting"});default:return r.dayPeriod(n,{width:"wide",context:"formatting"})}},B:function(e,t,r){let n;let a=e.getHours();switch(n=a>=17?A.evening:a>=12?A.afternoon:a>=4?A.morning:A.night,t){case"B":case"BB":case"BBB":return r.dayPeriod(n,{width:"abbreviated",context:"formatting"});case"BBBBB":return r.dayPeriod(n,{width:"narrow",context:"formatting"});default:return r.dayPeriod(n,{width:"wide",context:"formatting"})}},h:function(e,t,r){if("ho"===t){let t=e.getHours()%12;return 0===t&&(t=12),r.ordinalNumber(t,{unit:"hour"})}return q.h(e,t)},H:function(e,t,r){return"Ho"===t?r.ordinalNumber(e.getHours(),{unit:"hour"}):q.H(e,t)},K:function(e,t,r){let n=e.getHours()%12;return"Ko"===t?r.ordinalNumber(n,{unit:"hour"}):M(n,t.length)},k:function(e,t,r){let n=e.getHours();return(0===n&&(n=24),"ko"===t)?r.ordinalNumber(n,{unit:"hour"}):M(n,t.length)},m:function(e,t,r){return"mo"===t?r.ordinalNumber(e.getMinutes(),{unit:"minute"}):q.m(e,t)},s:function(e,t,r){return"so"===t?r.ordinalNumber(e.getSeconds(),{unit:"second"}):q.s(e,t)},S:function(e,t){return q.S(e,t)},X:function(e,t,r){let n=e.getTimezoneOffset();if(0===n)return"Z";switch(t){case"X":return F(n);case"XXXX":case"XX":return I(n);default:return I(n,":")}},x:function(e,t,r){let n=e.getTimezoneOffset();switch(t){case"x":return F(n);case"xxxx":case"xx":return I(n);default:return I(n,":")}},O:function(e,t,r){let n=e.getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+T(n,":");default:return"GMT"+I(n,":")}},z:function(e,t,r){let n=e.getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+T(n,":");default:return"GMT"+I(n,":")}},t:function(e,t,r){return M(Math.trunc(+e/1e3),t.length)},T:function(e,t,r){return M(+e,t.length)}};function T(e,t=""){let r=e>0?"-":"+",n=Math.abs(e),a=Math.trunc(n/60),s=n%60;return 0===s?r+String(a):r+String(a)+t+M(s,2)}function F(e,t){return e%60==0?(e>0?"-":"+")+M(Math.abs(e)/60,2):I(e,t)}function I(e,t=""){let r=Math.abs(e);return(e>0?"-":"+")+M(Math.trunc(r/60),2)+t+M(r%60,2)}let L=(e,t)=>{switch(e){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});default:return t.date({width:"full"})}},_=(e,t)=>{switch(e){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});default:return t.time({width:"full"})}},$={p:_,P:(e,t)=>{let r;let n=e.match(/(P+)(p+)?/)||[],a=n[1],s=n[2];if(!s)return L(e,t);switch(a){case"P":r=t.dateTime({width:"short"});break;case"PP":r=t.dateTime({width:"medium"});break;case"PPP":r=t.dateTime({width:"long"});break;default:r=t.dateTime({width:"full"})}return r.replace("{{date}}",L(a,t)).replace("{{time}}",_(s,t))}},z=/^D+$/,H=/^Y+$/,U=["D","DD","YY","YYYY"],Y=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,G=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,B=/^'([^]*?)'?$/,W=/''/g,Q=/[a-zA-Z]/;function V(){let[e,t]=(0,a.useState)([]),[r,y]=(0,a.useState)([]),[j,N]=(0,a.useState)(!0),[k,D]=(0,a.useState)(null),[P,R]=(0,a.useState)(null),[E,S]=(0,a.useState)(""),[M,q]=(0,a.useState)(!1),[A,T]=(0,a.useState)(!1),{toast:F}=(0,b.dj)(),I=e=>{R(e),S(e.role.id),q(!0)},L=async()=>{if(P&&E)try{T(!0);let n=await fetch(`/api/users/${P.id}/role`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({roleId:E})});if(!n.ok)throw Error("Failed to update user role");let a=await n.json();t(e.map(e=>e.id===P.id?a:e)),F({title:"Role updated",description:`${P.firstName||P.email} is now a ${r.find(e=>e.id===E)?.name||"user"}.`}),q(!1)}catch(e){console.error("Error updating role:",e),F({title:"Error",description:"Failed to update user role. Please try again.",variant:"destructive"})}finally{T(!1)}},_=e=>e.firstName&&e.lastName?`${e.firstName} ${e.lastName}`:e.email.split("@")[0],V=e=>e.firstName&&e.lastName?`${e.firstName[0]}${e.lastName[0]}`.toUpperCase():e.email.substring(0,2).toUpperCase(),J=e=>{try{return function(e,t,r){let n=(0,v.q)(),a=(void 0)??n.locale??w.c,s=(void 0)??r?.locale?.options?.firstWeekContainsDate??n.firstWeekContainsDate??n.locale?.options?.firstWeekContainsDate??1,i=r?.weekStartsOn??r?.locale?.options?.weekStartsOn??n.weekStartsOn??n.locale?.options?.weekStartsOn??0,o=(0,C.a)(e,r?.in);if(!(o instanceof Date||"object"==typeof o&&"[object Date]"===Object.prototype.toString.call(o))&&"number"!=typeof o||isNaN(+(0,C.a)(o)))throw RangeError("Invalid time value");let l=t.match(G).map(e=>{let t=e[0];return"p"===t||"P"===t?(0,$[t])(e,a.formatLong):e}).join("").match(Y).map(e=>{if("''"===e)return{isToken:!1,value:"'"};let t=e[0];if("'"===t)return{isToken:!1,value:function(e){let t=e.match(B);return t?t[1].replace(W,"'"):e}(e)};if(O[t])return{isToken:!0,value:e};if(t.match(Q))throw RangeError("Format string contains an unescaped latin alphabet character `"+t+"`");return{isToken:!1,value:e}});a.localize.preprocessor&&(l=a.localize.preprocessor(o,l));let d={firstWeekContainsDate:s,weekStartsOn:i,locale:a};return l.map(n=>{if(!n.isToken)return n.value;let s=n.value;return(!r?.useAdditionalWeekYearTokens&&H.test(s)||!r?.useAdditionalDayOfYearTokens&&z.test(s))&&!function(e,t,r){let n=function(e,t,r){let n="Y"===e[0]?"years":"days of the month";return`Use \`${e.toLowerCase()}\` instead of \`${e}\` (in \`${t}\`) for formatting ${n} to the input \`${r}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}(e,t,r);if(console.warn(n),U.includes(e))throw RangeError(n)}(s,t,String(e)),(0,O[s[0]])(o,s,a.localize,d)}).join("")}(new Date(e),"MMM d, yyyy")}catch(t){return e}};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"rounded-md border",children:j?(0,n.jsx)("div",{className:"flex items-center justify-center p-8",children:(0,n.jsx)(l.A,{className:"h-8 w-8 animate-spin text-primary"})}):k?(0,n.jsx)("div",{className:"p-4 text-center text-muted-foreground",children:(0,n.jsx)("p",{children:k})}):(0,n.jsxs)(s.XI,{children:[(0,n.jsx)(s.A0,{children:(0,n.jsxs)(s.Hj,{children:[(0,n.jsx)(s.nd,{children:"Name"}),(0,n.jsx)(s.nd,{children:"Email"}),(0,n.jsx)(s.nd,{children:"Role"}),(0,n.jsx)(s.nd,{children:"Status"}),(0,n.jsx)(s.nd,{children:"Last Active"}),(0,n.jsx)(s.nd,{className:"w-[80px]"})]})}),(0,n.jsx)(s.BF,{children:0===e.length?(0,n.jsx)(s.Hj,{children:(0,n.jsx)(s.nA,{colSpan:6,className:"text-center py-8 text-muted-foreground",children:"No users found"})}):e.map(e=>(0,n.jsxs)(s.Hj,{children:[(0,n.jsx)(s.nA,{className:"font-medium",children:(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsxs)(m.eu,{className:"h-8 w-8",children:[(0,n.jsx)(m.BK,{src:e.profileImage||"/placeholder.svg?height=32&width=32"}),(0,n.jsx)(m.q5,{children:V(e)})]}),_(e)]})}),(0,n.jsx)(s.nA,{children:e.email}),(0,n.jsx)(s.nA,{children:(0,n.jsx)(h.E,{variant:"admin"===e.role.name?"default":"secondary",children:e.role.name})}),(0,n.jsx)(s.nA,{children:(0,n.jsx)(h.E,{variant:"ACTIVE"===e.status?"outline":"destructive",children:e.status.toLowerCase()})}),(0,n.jsx)(s.nA,{children:J(e.lastActive)}),(0,n.jsx)(s.nA,{children:(0,n.jsxs)(i.rI,{children:[(0,n.jsx)(i.ty,{asChild:!0,children:(0,n.jsxs)(o.$,{variant:"ghost",size:"icon",children:[(0,n.jsx)(d.A,{className:"h-4 w-4"}),(0,n.jsx)("span",{className:"sr-only",children:"Open menu"})]})}),(0,n.jsxs)(i.SQ,{align:"end",children:[(0,n.jsxs)(i._2,{onClick:()=>I(e),children:[(0,n.jsx)(c,{className:"mr-2 h-4 w-4"}),(0,n.jsx)("span",{children:"Change Role"})]}),(0,n.jsxs)(i._2,{children:[(0,n.jsx)(u.A,{className:"mr-2 h-4 w-4"}),(0,n.jsx)("span",{children:"Edit"})]}),(0,n.jsxs)(i._2,{className:"text-destructive",children:[(0,n.jsx)(f.A,{className:"mr-2 h-4 w-4"}),(0,n.jsx)("span",{children:"Delete"})]})]})]})})]},e.id))})]})}),(0,n.jsx)(p.lG,{open:M,onOpenChange:q,children:(0,n.jsxs)(p.Cf,{children:[(0,n.jsxs)(p.c7,{children:[(0,n.jsx)(p.L3,{children:"Change User Role"}),(0,n.jsxs)(p.rr,{children:["Change the role for ",P?_(P):"user",". This will affect their permissions in the system."]})]}),(0,n.jsxs)("div",{className:"py-4",children:[(0,n.jsx)(x.J,{htmlFor:"role",children:"Role"}),(0,n.jsxs)(g.l6,{value:E,onValueChange:S,children:[(0,n.jsx)(g.bq,{id:"role",children:(0,n.jsx)(g.yv,{placeholder:"Select role"})}),(0,n.jsx)(g.gC,{children:r.map(e=>(0,n.jsxs)(g.eb,{value:e.id,children:[(0,n.jsx)("span",{className:"capitalize",children:e.name}),e.description&&(0,n.jsxs)("span",{className:"text-muted-foreground ml-2 text-xs",children:["- ",e.description]})]},e.id))})]})]}),(0,n.jsxs)(p.Es,{children:[(0,n.jsx)(o.$,{variant:"outline",onClick:()=>q(!1),disabled:A,children:"Cancel"}),(0,n.jsx)(o.$,{onClick:L,disabled:A,children:A?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(l.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Saving..."]}):"Save Changes"})]})]})})]})}new Date().toISOString(),new Date().toISOString()},78148:(e,t,r)=>{"use strict";r.d(t,{b:()=>o});var n=r(43210),a=r(14163),s=r(60687),i=n.forwardRef((e,t)=>(0,s.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var o=i},78474:e=>{"use strict";e.exports=require("node:events")},85554:(e,t,r)=>{Promise.resolve().then(r.bind(r,1614)),Promise.resolve().then(r.bind(r,77759))},88063:(e,t,r)=>{"use strict";r.d(t,{UsersTable:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call UsersTable() from the server but UsersTable is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\components\\users\\users-table.tsx","UsersTable")},95180:(e,t,r)=>{"use strict";r.d(t,{InviteUserDialog:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call InviteUserDialog() from the server but InviteUserDialog is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\components\\users\\invite-user-dialog.tsx","InviteUserDialog")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96752:(e,t,r)=>{"use strict";r.d(t,{A0:()=>o,BF:()=>l,Hj:()=>d,XI:()=>i,nA:()=>u,nd:()=>c});var n=r(60687),a=r(43210),s=r(96241);let i=a.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{className:"relative w-full overflow-auto",children:(0,n.jsx)("table",{ref:r,className:(0,s.cn)("w-full caption-bottom text-sm",e),...t})}));i.displayName="Table";let o=a.forwardRef(({className:e,...t},r)=>(0,n.jsx)("thead",{ref:r,className:(0,s.cn)("[&_tr]:border-b",e),...t}));o.displayName="TableHeader";let l=a.forwardRef(({className:e,...t},r)=>(0,n.jsx)("tbody",{ref:r,className:(0,s.cn)("[&_tr:last-child]:border-0",e),...t}));l.displayName="TableBody",a.forwardRef(({className:e,...t},r)=>(0,n.jsx)("tfoot",{ref:r,className:(0,s.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...t})).displayName="TableFooter";let d=a.forwardRef(({className:e,...t},r)=>(0,n.jsx)("tr",{ref:r,className:(0,s.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...t}));d.displayName="TableRow";let c=a.forwardRef(({className:e,...t},r)=>(0,n.jsx)("th",{ref:r,className:(0,s.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...t}));c.displayName="TableHead";let u=a.forwardRef(({className:e,...t},r)=>(0,n.jsx)("td",{ref:r,className:(0,s.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...t}));u.displayName="TableCell",a.forwardRef(({className:e,...t},r)=>(0,n.jsx)("caption",{ref:r,className:(0,s.cn)("mt-4 text-sm text-muted-foreground",e),...t})).displayName="TableCaption"}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[7719,9656,2190,3903,5153,3555,4097,4017,9474,8722,9464,381],()=>r(4144));module.exports=n})();