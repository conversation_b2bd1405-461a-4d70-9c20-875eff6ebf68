{"version": 3, "file": "Pipeable.js", "names": ["pipeArguments", "self", "args", "length", "ret", "i", "len"], "sources": ["../../src/Pipeable.ts"], "sourcesContent": [null], "mappings": "AAAA;;;AA0eA;;;AAGA,OAAO,MAAMA,aAAa,GAAGA,CAAIC,IAAO,EAAEC,IAAgB,KAAa;EACrE,QAAQA,IAAI,CAACC,MAAM;IACjB,KAAK,CAAC;MACJ,OAAOF,IAAI;IACb,KAAK,CAAC;MACJ,OAAOC,IAAI,CAAC,CAAC,CAAC,CAACD,IAAI,CAAC;IACtB,KAAK,CAAC;MACJ,OAAOC,IAAI,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,CAAC,CAAC,CAACD,IAAI,CAAC,CAAC;IAC/B,KAAK,CAAC;MACJ,OAAOC,IAAI,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,CAAC,CAAC,CAACD,IAAI,CAAC,CAAC,CAAC;IACxC,KAAK,CAAC;MACJ,OAAOC,IAAI,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,CAAC,CAAC,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC;IACjD,KAAK,CAAC;MACJ,OAAOC,IAAI,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,CAAC,CAAC,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1D,KAAK,CAAC;MACJ,OAAOC,IAAI,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,CAAC,CAAC,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnE,KAAK,CAAC;MACJ,OAAOC,IAAI,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,CAAC,CAAC,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5E,KAAK,CAAC;MACJ,OAAOC,IAAI,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,CAAC,CAAC,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrF,KAAK,CAAC;MACJ,OAAOC,IAAI,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,CAAC,CAAC,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9F;MAAS;QACP,IAAIG,GAAG,GAAGH,IAAI;QACd,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGJ,IAAI,CAACC,MAAM,EAAEE,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;UAC/CD,GAAG,GAAGF,IAAI,CAACG,CAAC,CAAC,CAACD,GAAG,CAAC;QACpB;QACA,OAAOA,GAAG;MACZ;EACF;AACF,CAAC", "ignoreList": []}