"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/properties/edit/[id]/page",{

/***/ "(app-pages-browser)/./app/dashboard/properties/create/property-form-steps.tsx":
/*!*****************************************************************!*\
  !*** ./app/dashboard/properties/create/property-form-steps.tsx ***!
  \*****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PropertyFormSteps: () => (/* binding */ PropertyFormSteps)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,Plus,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,Plus,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,Plus,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,Plus,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,Plus,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,Plus,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/hooks/useSimpleLanguage */ \"(app-pages-browser)/./hooks/useSimpleLanguage.tsx\");\n/* harmony import */ var _components_SimpleImageUpload__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/SimpleImageUpload */ \"(app-pages-browser)/./components/SimpleImageUpload.tsx\");\n/* __next_internal_client_entry_do_not_use__ PropertyFormSteps auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction PropertyFormSteps(param) {\n    let { onSave, loading, initialData, isEdit = false, propertyId } = param;\n    _s();\n    const { language } = (0,_hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_11__.useSimpleLanguage)();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const totalSteps = 4;\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const defaultFormData = {\n        title: '',\n        titleAr: '',\n        description: '',\n        descriptionAr: '',\n        price: '',\n        currency: 'SAR',\n        type: 'APARTMENT',\n        status: 'AVAILABLE',\n        bedrooms: '',\n        bathrooms: '',\n        area: '',\n        location: '',\n        locationAr: '',\n        address: '',\n        addressAr: '',\n        city: '',\n        cityAr: '',\n        country: 'Saudi Arabia',\n        countryAr: 'المملكة العربية السعودية',\n        images: [],\n        features: [],\n        featuresAr: [],\n        amenities: [],\n        amenitiesAr: [],\n        yearBuilt: '',\n        parking: '',\n        furnished: false,\n        petFriendly: false,\n        utilities: '',\n        utilitiesAr: '',\n        contactInfo: '',\n        isFeatured: false,\n        isActive: true\n    };\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialData || defaultFormData);\n    const [newFeature, setNewFeature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newFeatureAr, setNewFeatureAr] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newAmenity, setNewAmenity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newAmenityAr, setNewAmenityAr] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Auto-save functionality (only for create mode)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PropertyFormSteps.useEffect\": ()=>{\n            if (!isEdit && !initialData) {\n                const savedData = localStorage.getItem('property-draft');\n                if (savedData) {\n                    try {\n                        const parsed = JSON.parse(savedData);\n                        setFormData(parsed);\n                    } catch (error) {\n                        console.error('Error loading draft:', error);\n                    }\n                }\n            }\n        }\n    }[\"PropertyFormSteps.useEffect\"], [\n        isEdit,\n        initialData\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PropertyFormSteps.useEffect\": ()=>{\n            if (!isEdit) {\n                const timer = setTimeout({\n                    \"PropertyFormSteps.useEffect.timer\": ()=>{\n                        localStorage.setItem('property-draft', JSON.stringify(formData));\n                    }\n                }[\"PropertyFormSteps.useEffect.timer\"], 1000);\n                return ({\n                    \"PropertyFormSteps.useEffect\": ()=>clearTimeout(timer)\n                })[\"PropertyFormSteps.useEffect\"];\n            }\n        }\n    }[\"PropertyFormSteps.useEffect\"], [\n        formData,\n        isEdit\n    ]);\n    // Initialize form data when initialData changes (for edit mode)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PropertyFormSteps.useEffect\": ()=>{\n            if (initialData && isEdit) {\n                setFormData(initialData);\n            }\n        }\n    }[\"PropertyFormSteps.useEffect\"], [\n        initialData,\n        isEdit\n    ]);\n    // Comprehensive bilingual translations\n    const translations = {\n        ar: {\n            step: 'الخطوة',\n            of: 'من',\n            next: 'التالي',\n            previous: 'السابق',\n            save: 'حفظ العقار',\n            required: 'مطلوب',\n            optional: 'اختياري',\n            basicInfo: 'المعلومات الأساسية',\n            propertyDetails: 'تفاصيل العقار',\n            locationInfo: 'معلومات الموقع',\n            additionalInfo: 'معلومات إضافية',\n            title: 'عنوان العقار',\n            description: 'وصف العقار',\n            price: 'السعر',\n            currency: 'العملة',\n            propertyType: 'نوع العقار',\n            status: 'حالة العقار',\n            bedrooms: 'غرف النوم',\n            bathrooms: 'دورات المياه',\n            area: 'المساحة (متر مربع)',\n            yearBuilt: 'سنة البناء',\n            parking: 'مواقف السيارات',\n            location: 'الموقع',\n            address: 'العنوان',\n            city: 'المدينة',\n            country: 'الدولة',\n            images: 'صور العقار',\n            features: 'مميزات العقار',\n            amenities: 'المرافق والخدمات',\n            utilities: 'الخدمات المشمولة',\n            contactInfo: 'معلومات التواصل',\n            furnished: 'مفروش',\n            petFriendly: 'يسمح بالحيوانات الأليفة',\n            featured: 'عقار مميز',\n            active: 'نشط',\n            addFeature: 'إضافة ميزة',\n            addAmenity: 'إضافة مرفق',\n            uploadImages: 'رفع صور العقار',\n            dragDropImages: 'اسحب وأفلت الصور هنا، أو انقر للاختيار',\n            titlePlaceholder: 'أدخل عنوان العقار...',\n            descriptionPlaceholder: 'اكتب وصفاً مفصلاً للعقار...',\n            locationPlaceholder: 'أدخل موقع العقار...',\n            addressPlaceholder: 'أدخل العنوان الكامل...',\n            cityPlaceholder: 'أدخل اسم المدينة...',\n            featurePlaceholder: 'أضف ميزة جديدة...',\n            amenityPlaceholder: 'أضف مرفق جديد...',\n            utilitiesPlaceholder: 'اذكر الخدمات المشمولة...',\n            contactPlaceholder: 'أدخل معلومات التواصل...',\n            stepDescription1: 'أدخل المعلومات الأساسية للعقار',\n            stepDescription2: 'حدد تفاصيل ومواصفات العقار',\n            stepDescription3: 'أضف معلومات الموقع والعنوان',\n            stepDescription4: 'أضف الصور والمعلومات الإضافية',\n            completed: 'مكتمل',\n            current: 'الحالي',\n            pending: 'في الانتظار',\n            imageGallery: 'معرض الصور',\n            mainImage: 'الصورة الرئيسية',\n            additionalImages: 'الصور الإضافية',\n            imageTips: 'نصائح للصور',\n            noFeatures: 'لا توجد مميزات مضافة',\n            noAmenities: 'لا توجد مرافق مضافة',\n            noImages: 'لم يتم رفع صور بعد',\n            setAsMain: 'تعيين كصورة رئيسية',\n            removeImage: 'حذف الصورة',\n            saving: 'جاري الحفظ...',\n            success: 'تم بنجاح',\n            error: 'حدث خطأ'\n        },\n        en: {\n            step: 'Step',\n            of: 'of',\n            next: 'Next',\n            previous: 'Previous',\n            save: 'Save Property',\n            required: 'Required',\n            optional: 'Optional',\n            basicInfo: 'Basic Information',\n            propertyDetails: 'Property Details',\n            locationInfo: 'Location Information',\n            additionalInfo: 'Additional Information',\n            title: 'Property Title',\n            description: 'Property Description',\n            price: 'Price',\n            currency: 'Currency',\n            propertyType: 'Property Type',\n            status: 'Property Status',\n            bedrooms: 'Bedrooms',\n            bathrooms: 'Bathrooms',\n            area: 'Area (sqm)',\n            yearBuilt: 'Year Built',\n            parking: 'Parking Spaces',\n            location: 'Location',\n            address: 'Address',\n            city: 'City',\n            country: 'Country',\n            images: 'Property Images',\n            features: 'Property Features',\n            amenities: 'Amenities & Services',\n            utilities: 'Included Utilities',\n            contactInfo: 'Contact Information',\n            furnished: 'Furnished',\n            petFriendly: 'Pet Friendly',\n            featured: 'Featured Property',\n            active: 'Active',\n            addFeature: 'Add Feature',\n            addAmenity: 'Add Amenity',\n            uploadImages: 'Upload Property Images',\n            dragDropImages: 'Drag and drop images here, or click to select',\n            titlePlaceholder: 'Enter property title...',\n            descriptionPlaceholder: 'Write a detailed property description...',\n            locationPlaceholder: 'Enter property location...',\n            addressPlaceholder: 'Enter full address...',\n            cityPlaceholder: 'Enter city name...',\n            featurePlaceholder: 'Add new feature...',\n            amenityPlaceholder: 'Add new amenity...',\n            utilitiesPlaceholder: 'List included utilities...',\n            contactPlaceholder: 'Enter contact information...',\n            stepDescription1: 'Enter basic property information',\n            stepDescription2: 'Specify property details and specifications',\n            stepDescription3: 'Add location and address information',\n            stepDescription4: 'Add images and additional information',\n            completed: 'Completed',\n            current: 'Current',\n            pending: 'Pending',\n            imageGallery: 'Image Gallery',\n            mainImage: 'Main Image',\n            additionalImages: 'Additional Images',\n            imageTips: 'Image Tips',\n            noFeatures: 'No features added',\n            noAmenities: 'No amenities added',\n            noImages: 'No images uploaded yet',\n            setAsMain: 'Set as Main Image',\n            removeImage: 'Remove Image',\n            saving: 'Saving...',\n            success: 'Success',\n            error: 'Error'\n        }\n    };\n    const t = translations[language];\n    // Bilingual property types\n    const propertyTypes = {\n        ar: {\n            APARTMENT: 'شقة سكنية',\n            VILLA: 'فيلا',\n            TOWNHOUSE: 'تاون هاوس',\n            PENTHOUSE: 'بنتهاوس',\n            STUDIO: 'استوديو',\n            OFFICE: 'مكتب تجاري',\n            SHOP: 'محل تجاري',\n            WAREHOUSE: 'مستودع',\n            LAND: 'قطعة أرض',\n            BUILDING: 'مبنى كامل'\n        },\n        en: {\n            APARTMENT: 'Apartment',\n            VILLA: 'Villa',\n            TOWNHOUSE: 'Townhouse',\n            PENTHOUSE: 'Penthouse',\n            STUDIO: 'Studio',\n            OFFICE: 'Office',\n            SHOP: 'Shop',\n            WAREHOUSE: 'Warehouse',\n            LAND: 'Land',\n            BUILDING: 'Building'\n        }\n    };\n    // Bilingual property statuses\n    const propertyStatuses = {\n        ar: {\n            AVAILABLE: 'متاح للبيع',\n            SOLD: 'تم البيع',\n            RENTED: 'مؤجر',\n            RESERVED: 'محجوز',\n            OFF_MARKET: 'غير متاح'\n        },\n        en: {\n            AVAILABLE: 'Available',\n            SOLD: 'Sold',\n            RENTED: 'Rented',\n            RESERVED: 'Reserved',\n            OFF_MARKET: 'Off Market'\n        }\n    };\n    const stepTitles = [\n        t.basicInfo,\n        t.propertyDetails,\n        t.locationInfo,\n        t.additionalInfo\n    ];\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Wait for any ongoing uploads to complete\n        if (isUploading) {\n            return;\n        }\n        try {\n            await onSave(formData);\n            // Clear draft after successful save\n            localStorage.removeItem('property-draft');\n            // Auto-redirect after successful save\n            setTimeout(()=>{\n                window.location.href = '/dashboard/properties';\n            }, 1500);\n        } catch (error) {\n            console.error('Save failed:', error);\n        }\n    };\n    // Auto-save function for images\n    const handleImageAutoSave = async (images)=>{\n        if (!isEdit || !propertyId) return;\n        try {\n            const response = await fetch(\"/api/v1/properties/\".concat(propertyId, \"/images\"), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    images\n                })\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.message || 'Failed to auto-save images');\n            }\n            const result = await response.json();\n            console.log('Images auto-saved successfully:', result);\n        } catch (error) {\n            console.error('Auto-save error:', error);\n            throw error;\n        }\n    };\n    const addFeature = ()=>{\n        if (newFeature.trim()) {\n            setFormData((prev)=>({\n                    ...prev,\n                    features: [\n                        ...prev.features,\n                        newFeature.trim()\n                    ]\n                }));\n            setNewFeature('');\n        }\n    };\n    const addFeatureAr = ()=>{\n        if (newFeatureAr.trim()) {\n            setFormData((prev)=>({\n                    ...prev,\n                    featuresAr: [\n                        ...prev.featuresAr,\n                        newFeatureAr.trim()\n                    ]\n                }));\n            setNewFeatureAr('');\n        }\n    };\n    const addAmenity = ()=>{\n        if (newAmenity.trim()) {\n            setFormData((prev)=>({\n                    ...prev,\n                    amenities: [\n                        ...prev.amenities,\n                        newAmenity.trim()\n                    ]\n                }));\n            setNewAmenity('');\n        }\n    };\n    const addAmenityAr = ()=>{\n        if (newAmenityAr.trim()) {\n            setFormData((prev)=>({\n                    ...prev,\n                    amenitiesAr: [\n                        ...prev.amenitiesAr,\n                        newAmenityAr.trim()\n                    ]\n                }));\n            setNewAmenityAr('');\n        }\n    };\n    const removeFeature = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                features: prev.features.filter((_, i)=>i !== index)\n            }));\n    };\n    const removeFeatureAr = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                featuresAr: prev.featuresAr.filter((_, i)=>i !== index)\n            }));\n    };\n    const removeAmenity = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                amenities: prev.amenities.filter((_, i)=>i !== index)\n            }));\n    };\n    const removeAmenityAr = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                amenitiesAr: prev.amenitiesAr.filter((_, i)=>i !== index)\n            }));\n    };\n    const removeImage = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                images: prev.images.filter((_, i)=>i !== index)\n            }));\n    };\n    const moveImage = (fromIndex, toIndex)=>{\n        setFormData((prev)=>{\n            const newImages = [\n                ...prev.images\n            ];\n            const [movedImage] = newImages.splice(fromIndex, 1);\n            newImages.splice(toIndex, 0, movedImage);\n            return {\n                ...prev,\n                images: newImages\n            };\n        });\n    };\n    const setMainImage = (index)=>{\n        if (index === 0) return; // Already main image\n        moveImage(index, 0);\n    };\n    const nextStep = ()=>{\n        if (currentStep < totalSteps) {\n            setCurrentStep(currentStep + 1);\n        }\n    };\n    const prevStep = ()=>{\n        if (currentStep > 1) {\n            setCurrentStep(currentStep - 1);\n        }\n    };\n    const isStepValid = (step)=>{\n        switch(step){\n            case 1:\n                return formData.title && formData.description && formData.price && formData.type;\n            case 2:\n                return true; // Property details are optional\n            case 3:\n                return formData.location && formData.address && formData.city;\n            case 4:\n                return true; // Additional info is optional\n            default:\n                return false;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(language === 'ar' ? 'rtl' : 'ltr'),\n        dir: language === 'ar' ? 'rtl' : 'ltr',\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-r from-emerald-100/50 to-teal-100/50 dark:from-emerald-900/20 dark:to-teal-900/20 rounded-2xl\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 476,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between \".concat(language === 'ar' ? 'flex-row-reverse' : ''),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4 \".concat(language === 'ar' ? 'flex-row-reverse' : ''),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-14 h-14 bg-gradient-to-br from-emerald-500 via-teal-500 to-cyan-600 rounded-2xl flex items-center justify-center shadow-xl shadow-emerald-200 dark:shadow-emerald-900/50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-black text-lg\",\n                                                                children: currentStep\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 482,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 481,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute -top-1 \".concat(language === 'ar' ? '-right-1' : '-left-1', \" w-6 h-6 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center animate-pulse\"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white text-xs font-bold\",\n                                                                children: \"✦\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 485,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 484,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1 \".concat(language === 'ar' ? 'text-right' : 'text-left'),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2 \".concat(language === 'ar' ? 'flex-row-reverse' : ''),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xl font-black text-slate-800 dark:text-white\",\n                                                                    children: [\n                                                                        t.step,\n                                                                        \" \",\n                                                                        currentStep,\n                                                                        \" \",\n                                                                        t.of,\n                                                                        \" \",\n                                                                        totalSteps\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 490,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"px-3 py-1 bg-emerald-100 dark:bg-emerald-900/30 rounded-full\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-bold text-emerald-700 dark:text-emerald-300\",\n                                                                        children: t.current\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 494,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 493,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-lg font-bold text-slate-700 dark:text-slate-300\",\n                                                            children: stepTitles[currentStep - 1]\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 497,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-slate-600 dark:text-slate-400\",\n                                                            children: [\n                                                                currentStep === 1 && t.stepDescription1,\n                                                                currentStep === 2 && t.stepDescription2,\n                                                                currentStep === 3 && t.stepDescription3,\n                                                                currentStep === 4 && t.stepDescription4\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 500,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 488,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 479,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"\".concat(language === 'ar' ? 'text-right' : 'text-left'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-4xl font-black bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent\",\n                                                    children: [\n                                                        Math.round(currentStep / totalSteps * 100),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-bold text-emerald-600 dark:text-emerald-400\",\n                                                    children: t.completed\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 513,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 509,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                    lineNumber: 478,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 477,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                        lineNumber: 475,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full bg-slate-200 dark:bg-slate-700 rounded-full h-4 shadow-inner\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-r from-emerald-500 via-teal-500 to-cyan-500 h-4 rounded-full transition-all duration-1000 ease-out shadow-lg relative overflow-hidden\",\n                                style: {\n                                    width: \"\".concat(currentStep / totalSteps * 100, \"%\")\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 528,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-r from-emerald-400/50 to-teal-400/50 animate-pulse delay-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 529,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 524,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                            lineNumber: 523,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                        lineNumber: 522,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-4 gap-4\",\n                        children: stepTitles.map((title, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 rounded-2xl flex items-center justify-center text-lg font-black transition-all duration-500 shadow-xl \".concat(index + 1 <= currentStep ? 'bg-gradient-to-br from-emerald-500 via-teal-500 to-cyan-600 text-white shadow-emerald-200 dark:shadow-emerald-900/50 scale-110' : index + 1 === currentStep + 1 ? 'bg-gradient-to-br from-slate-300 to-slate-400 text-slate-700 shadow-slate-200 dark:shadow-slate-800 scale-105' : 'bg-slate-200 dark:bg-slate-700 text-slate-500 dark:text-slate-400'),\n                                        children: index + 1 < currentStep ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-7 w-7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 548,\n                                            columnNumber: 19\n                                        }, this) : index + 1 === currentStep ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 bg-white rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 550,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-black\",\n                                            children: index + 1\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 552,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 538,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-bold leading-tight \".concat(index + 1 <= currentStep ? 'text-emerald-700 dark:text-emerald-300' : index + 1 === currentStep + 1 ? 'text-slate-600 dark:text-slate-400' : 'text-slate-500 dark:text-slate-500'),\n                                                children: title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs mt-1 \".concat(index + 1 <= currentStep ? 'text-emerald-600 dark:text-emerald-400' : 'text-slate-400 dark:text-slate-500'),\n                                                children: index + 1 <= currentStep ? t.completed : t.pending\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 565,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 555,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 537,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                        lineNumber: 535,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                lineNumber: 473,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"space-y-8\",\n                children: [\n                    currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                        className: \"border border-slate-700 bg-slate-800\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                className: \"pb-6 border-b border-slate-700\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                    className: \"text-xl font-bold text-white flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-emerald-600 text-white rounded-lg flex items-center justify-center text-lg font-bold\",\n                                            children: \"1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 584,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xl\",\n                                                    children: t.basicInfo\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 588,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-normal text-slate-400 mt-1\",\n                                                    children: language === 'ar' ? 'المعلومات الأساسية' : 'Basic Information'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 589,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 587,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                    lineNumber: 583,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 582,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                className: \"space-y-8 p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"title\",\n                                                        className: \"text-sm font-medium text-white\",\n                                                        children: [\n                                                            t.title,\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-400\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 600,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 599,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"title\",\n                                                        value: formData.title,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    title: e.target.value\n                                                                })),\n                                                        placeholder: t.titlePlaceholder,\n                                                        required: true,\n                                                        dir: language === 'ar' ? 'rtl' : 'ltr',\n                                                        className: \"h-12 border border-slate-600 focus:border-emerald-500 bg-slate-700 text-white placeholder:text-slate-400 rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 602,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 598,\n                                                columnNumber: 17\n                                            }, this),\n                                            language === 'en' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"titleEn\",\n                                                        className: \"text-base font-semibold text-slate-700 dark:text-slate-300 flex items-center gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-6 h-6 bg-slate-100 dark:bg-slate-700 rounded-lg flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-slate-600 dark:text-slate-400 text-sm\",\n                                                                    children: \"EN\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 616,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 615,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            t.titleEn,\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-slate-400 text-sm\",\n                                                                children: [\n                                                                    \"(\",\n                                                                    t.optional,\n                                                                    \")\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 619,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 614,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"titleEn\",\n                                                        value: formData.titleAr,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    titleAr: e.target.value\n                                                                })),\n                                                        placeholder: t.titleEnPlaceholder,\n                                                        dir: \"ltr\",\n                                                        className: \"h-12 border-2 border-slate-200 dark:border-slate-600 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-300 rounded-xl bg-white/30 dark:bg-slate-800/30\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 621,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 613,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 597,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"description\",\n                                                    className: \"text-base font-bold text-slate-800 dark:text-slate-200 flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-6 h-6 bg-emerald-100 dark:bg-emerald-900/30 rounded-lg flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-emerald-600 dark:text-emerald-400 text-sm font-bold\",\n                                                                children: \"2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 638,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 637,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        t.description,\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-500 text-lg\",\n                                                            children: \"*\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 641,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 636,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                    id: \"description\",\n                                                    value: formData.description,\n                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                description: e.target.value\n                                                            })),\n                                                    placeholder: t.descriptionPlaceholder,\n                                                    required: true,\n                                                    rows: 6,\n                                                    dir: \"rtl\",\n                                                    className: \"border-2 border-slate-200 dark:border-slate-600 focus:border-emerald-500 dark:focus:border-emerald-400 transition-all duration-300 resize-none rounded-xl text-lg bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm shadow-sm hover:shadow-md focus:shadow-lg\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 643,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 635,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 634,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"price\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2\",\n                                                        children: [\n                                                            t.price,\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500 text-lg\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 661,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 659,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"price\",\n                                                        type: \"number\",\n                                                        value: formData.price,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    price: e.target.value\n                                                                })),\n                                                        placeholder: \"0\",\n                                                        required: true,\n                                                        className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-200 rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 663,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 658,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"currency\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.currency\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 674,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                        value: formData.currency,\n                                                        onValueChange: (value)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    currency: value\n                                                                })),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                                className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 rounded-lg\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 679,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 678,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                        value: \"SAR\",\n                                                                        children: \"SAR - ريال سعودي\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 682,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                        value: \"AED\",\n                                                                        children: \"AED - درهم إماراتي\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 683,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                        value: \"USD\",\n                                                                        children: \"USD - دولار أمريكي\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 684,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                        value: \"EUR\",\n                                                                        children: \"EUR - يورو\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 685,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                        value: \"GBP\",\n                                                                        children: \"GBP - جنيه إسترليني\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 686,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 681,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 677,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 673,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"type\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2\",\n                                                        children: [\n                                                            t.propertyType,\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500 text-lg\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 693,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 691,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                        value: formData.type,\n                                                        onValueChange: (value)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    type: value\n                                                                })),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                                className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 rounded-lg\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 697,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 696,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                                children: Object.entries(propertyTypes[language]).map((param)=>{\n                                                                    let [key, value] = param;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                        value: key,\n                                                                        children: value\n                                                                    }, key, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 701,\n                                                                        columnNumber: 25\n                                                                    }, this);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 699,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 695,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 690,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 657,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"status\",\n                                                    className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                    children: t.status\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 711,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                    value: formData.status,\n                                                    onValueChange: (value)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                status: value\n                                                            })),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                            className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 rounded-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 716,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 715,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                            children: Object.entries(propertyStatuses[language]).map((param)=>{\n                                                                let [key, value] = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                    value: key,\n                                                                    children: value\n                                                                }, key, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 720,\n                                                                    columnNumber: 25\n                                                                }, this);\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 718,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 714,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 710,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 709,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 595,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                        lineNumber: 581,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                        className: \"shadow-xl border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                className: \"pb-6 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-t-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg\",\n                                            children: \"2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 735,\n                                            columnNumber: 17\n                                        }, this),\n                                        t.propertyDetails\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                    lineNumber: 734,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 733,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                className: \"space-y-8 p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"bedrooms\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.bedrooms\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 745,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"bedrooms\",\n                                                        type: \"number\",\n                                                        value: formData.bedrooms,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    bedrooms: e.target.value\n                                                                })),\n                                                        placeholder: \"0\",\n                                                        min: \"0\",\n                                                        className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 748,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 744,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"bathrooms\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.bathrooms\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 759,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"bathrooms\",\n                                                        type: \"number\",\n                                                        value: formData.bathrooms,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    bathrooms: e.target.value\n                                                                })),\n                                                        placeholder: \"0\",\n                                                        min: \"0\",\n                                                        className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 762,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 758,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"area\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.area\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 773,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"area\",\n                                                        type: \"number\",\n                                                        value: formData.area,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    area: e.target.value\n                                                                })),\n                                                        placeholder: \"0\",\n                                                        min: \"0\",\n                                                        className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 776,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 772,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"yearBuilt\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.yearBuilt\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 787,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"yearBuilt\",\n                                                        type: \"number\",\n                                                        value: formData.yearBuilt,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    yearBuilt: e.target.value\n                                                                })),\n                                                        placeholder: \"2024\",\n                                                        min: \"1900\",\n                                                        max: \"2030\",\n                                                        className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 790,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 786,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"parking\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.parking\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 802,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"parking\",\n                                                        type: \"number\",\n                                                        value: formData.parking,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    parking: e.target.value\n                                                                })),\n                                                        placeholder: \"0\",\n                                                        min: \"0\",\n                                                        className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 805,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 801,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 743,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_10__.Separator, {\n                                        className: \"my-8\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 817,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                    children: t.features\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 822,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                            value: newFeature,\n                                                            onChange: (e)=>setNewFeature(e.target.value),\n                                                            placeholder: t.featurePlaceholder,\n                                                            onKeyPress: (e)=>e.key === 'Enter' && (e.preventDefault(), addFeature()),\n                                                            className: \"h-10 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 826,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            type: \"button\",\n                                                            onClick: addFeature,\n                                                            size: \"sm\",\n                                                            className: \"bg-green-600 hover:bg-green-700 px-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 834,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 833,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 825,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-2 min-h-[40px] p-3 border-2 border-dashed border-gray-200 dark:border-gray-600 rounded-lg\",\n                                                    children: [\n                                                        formData.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                variant: \"secondary\",\n                                                                className: \"flex items-center gap-1 px-3 py-1 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200\",\n                                                                children: [\n                                                                    feature,\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-3 w-3 cursor-pointer hover:text-red-600\",\n                                                                        onClick: ()=>removeFeature(index)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 841,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 839,\n                                                                columnNumber: 23\n                                                            }, this)),\n                                                        formData.features.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: t.noFeatures\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 848,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 837,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 821,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 820,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                    children: t.amenities\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 857,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                            value: newAmenity,\n                                                            onChange: (e)=>setNewAmenity(e.target.value),\n                                                            placeholder: t.amenityPlaceholder,\n                                                            onKeyPress: (e)=>e.key === 'Enter' && (e.preventDefault(), addAmenity()),\n                                                            className: \"h-10 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 861,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            type: \"button\",\n                                                            onClick: addAmenity,\n                                                            size: \"sm\",\n                                                            className: \"bg-green-600 hover:bg-green-700 px-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 869,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 868,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 860,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-2 min-h-[40px] p-3 border-2 border-dashed border-gray-200 dark:border-gray-600 rounded-lg\",\n                                                    children: [\n                                                        formData.amenities.map((amenity, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                variant: \"secondary\",\n                                                                className: \"flex items-center gap-1 px-3 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200\",\n                                                                children: [\n                                                                    amenity,\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-3 w-3 cursor-pointer hover:text-red-600\",\n                                                                        onClick: ()=>removeAmenity(index)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 876,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 874,\n                                                                columnNumber: 23\n                                                            }, this)),\n                                                        formData.amenities.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: t.noAmenities\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 883,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 872,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 856,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 855,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 741,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                        lineNumber: 732,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                        className: \"shadow-xl border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                className: \"pb-6 bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-t-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg\",\n                                            children: \"3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 897,\n                                            columnNumber: 17\n                                        }, this),\n                                        t.locationInfo\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                    lineNumber: 896,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 895,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                className: \"space-y-8 p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"location\",\n                                                    className: \"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2\",\n                                                    children: [\n                                                        t.location,\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-500 text-lg\",\n                                                            children: \"*\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 909,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 907,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                    id: \"location\",\n                                                    value: formData.location,\n                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                location: e.target.value\n                                                            })),\n                                                    placeholder: t.locationPlaceholder,\n                                                    required: true,\n                                                    className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 911,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 906,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 905,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"address\",\n                                                    className: \"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2\",\n                                                    children: [\n                                                        t.address,\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-500 text-lg\",\n                                                            children: \"*\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 928,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 926,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                    id: \"address\",\n                                                    value: formData.address,\n                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                address: e.target.value\n                                                            })),\n                                                    placeholder: t.addressPlaceholder,\n                                                    required: true,\n                                                    className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 930,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 925,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 924,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"city\",\n                                                                className: \"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2\",\n                                                                children: [\n                                                                    t.city,\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-500 text-lg\",\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 948,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 946,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                                value: formData.city,\n                                                                onValueChange: (value)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            city: value\n                                                                        })),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                                        className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                                            placeholder: language === 'ar' ? 'اختر المدينة السعودية' : 'Select Saudi City'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                            lineNumber: 952,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 951,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Riyadh\",\n                                                                                children: \"Riyadh - الرياض\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 955,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Jeddah\",\n                                                                                children: \"Jeddah - جدة\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 956,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Mecca\",\n                                                                                children: \"Mecca - مكة المكرمة\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 957,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Medina\",\n                                                                                children: \"Medina - المدينة المنورة\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 958,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Dammam\",\n                                                                                children: \"Dammam - الدمام\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 959,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Khobar\",\n                                                                                children: \"Khobar - الخبر\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 960,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Dhahran\",\n                                                                                children: \"Dhahran - الظهران\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 961,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Taif\",\n                                                                                children: \"Taif - الطائف\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 962,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Buraidah\",\n                                                                                children: \"Buraidah - بريدة\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 963,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Tabuk\",\n                                                                                children: \"Tabuk - تبوك\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 964,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Hail\",\n                                                                                children: \"Hail - حائل\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 965,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Abha\",\n                                                                                children: \"Abha - أبها\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 966,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Yanbu\",\n                                                                                children: \"Yanbu - ينبع\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 967,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Jubail\",\n                                                                                children: \"Jubail - الجبيل\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 968,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Najran\",\n                                                                                children: \"Najran - نجران\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 969,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 954,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 950,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 945,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"country\",\n                                                                className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                                children: t.country\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 974,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                                value: formData.country,\n                                                                onValueChange: (value)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            country: value\n                                                                        })),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                                        className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 rounded-lg\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {}, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                            lineNumber: 979,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 978,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Saudi Arabia\",\n                                                                                children: \"Saudi Arabia - المملكة العربية السعودية\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 982,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"UAE\",\n                                                                                children: \"UAE - الإمارات العربية المتحدة\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 983,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Qatar\",\n                                                                                children: \"Qatar - قطر\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 984,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Kuwait\",\n                                                                                children: \"Kuwait - الكويت\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 985,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Bahrain\",\n                                                                                children: \"Bahrain - البحرين\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 986,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Oman\",\n                                                                                children: \"Oman - عُمان\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 987,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Jordan\",\n                                                                                children: \"Jordan - الأردن\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 988,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Egypt\",\n                                                                                children: \"Egypt - مصر\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 989,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 981,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 977,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 973,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 944,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"cityAr\",\n                                                                className: \"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2\",\n                                                                children: [\n                                                                    t.cityAr,\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-400 text-sm\",\n                                                                        children: [\n                                                                            \"(\",\n                                                                            t.optional,\n                                                                            \")\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 998,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 996,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                id: \"cityAr\",\n                                                                value: formData.cityAr,\n                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            cityAr: e.target.value\n                                                                        })),\n                                                                placeholder: t.cityArPlaceholder,\n                                                                dir: \"rtl\",\n                                                                className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 1000,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 995,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"countryAr\",\n                                                                className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                                children: t.countryAr\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 1010,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                id: \"countryAr\",\n                                                                value: formData.countryAr,\n                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            countryAr: e.target.value\n                                                                        })),\n                                                                placeholder: \"أدخل اسم البلد بالعربية\",\n                                                                dir: \"rtl\",\n                                                                className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 1013,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1009,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 994,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 943,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 903,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                        lineNumber: 894,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                        className: \"shadow-xl border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                className: \"pb-6 bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-900/20 dark:to-amber-900/20 rounded-t-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg\",\n                                            children: \"4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 1033,\n                                            columnNumber: 17\n                                        }, this),\n                                        t.additionalInfo\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                    lineNumber: 1032,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 1031,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                className: \"space-y-8 p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SimpleImageUpload__WEBPACK_IMPORTED_MODULE_12__.SimpleImageUpload, {\n                                        images: formData.images,\n                                        onImagesChange: (images)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    images\n                                                })),\n                                        onAutoSave: isEdit ? handleImageAutoSave : undefined,\n                                        onUploadStatusChange: setIsUploading,\n                                        propertyId: propertyId,\n                                        maxImages: 10,\n                                        disabled: loading\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 1041,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_10__.Separator, {\n                                        className: \"my-8\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 1053,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"utilities\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.utilities\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1058,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                        id: \"utilities\",\n                                                        value: formData.utilities,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    utilities: e.target.value\n                                                                })),\n                                                        placeholder: t.utilitiesPlaceholder,\n                                                        rows: 3,\n                                                        className: \"border-2 border-gray-200 dark:border-gray-600 focus:border-orange-500 dark:focus:border-orange-400 transition-all duration-200 resize-none rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1061,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1057,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"utilitiesAr\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.utilitiesAr\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1071,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                        id: \"utilitiesAr\",\n                                                        value: formData.utilitiesAr,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    utilitiesAr: e.target.value\n                                                                })),\n                                                        placeholder: t.utilitiesArPlaceholder,\n                                                        dir: \"rtl\",\n                                                        rows: 3,\n                                                        className: \"border-2 border-gray-200 dark:border-gray-600 focus:border-orange-500 dark:focus:border-orange-400 transition-all duration-200 resize-none rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1074,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1070,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 1056,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"contactInfo\",\n                                                className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                children: t.contactInfo\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1087,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                id: \"contactInfo\",\n                                                value: formData.contactInfo,\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            contactInfo: e.target.value\n                                                        })),\n                                                placeholder: t.contactPlaceholder,\n                                                rows: 3,\n                                                className: \"border-2 border-gray-200 dark:border-gray-600 focus:border-orange-500 dark:focus:border-orange-400 transition-all duration-200 resize-none rounded-lg\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1090,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 1086,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_10__.Separator, {\n                                        className: \"my-8\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 1100,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-4 gap-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-orange-400 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_7__.Switch, {\n                                                        id: \"furnished\",\n                                                        checked: formData.furnished,\n                                                        onCheckedChange: (checked)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    furnished: checked\n                                                                })),\n                                                        className: \"data-[state=checked]:bg-orange-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1105,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"furnished\",\n                                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer\",\n                                                        children: t.furnished\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1111,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1104,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-orange-400 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_7__.Switch, {\n                                                        id: \"petFriendly\",\n                                                        checked: formData.petFriendly,\n                                                        onCheckedChange: (checked)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    petFriendly: checked\n                                                                })),\n                                                        className: \"data-[state=checked]:bg-orange-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1116,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"petFriendly\",\n                                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer\",\n                                                        children: t.petFriendly\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1122,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1115,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-orange-400 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_7__.Switch, {\n                                                        id: \"isFeatured\",\n                                                        checked: formData.isFeatured,\n                                                        onCheckedChange: (checked)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    isFeatured: checked\n                                                                })),\n                                                        className: \"data-[state=checked]:bg-orange-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1127,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"isFeatured\",\n                                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer\",\n                                                        children: t.featured\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1133,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1126,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-orange-400 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_7__.Switch, {\n                                                        id: \"isActive\",\n                                                        checked: formData.isActive,\n                                                        onCheckedChange: (checked)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    isActive: checked\n                                                                })),\n                                                        className: \"data-[state=checked]:bg-orange-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1138,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"isActive\",\n                                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer\",\n                                                        children: t.active\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1144,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1137,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 1103,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 1039,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                        lineNumber: 1030,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center pt-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                type: \"button\",\n                                variant: \"outline\",\n                                onClick: prevStep,\n                                disabled: currentStep === 1,\n                                className: \"flex items-center gap-2 px-6 py-3 h-12 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 1162,\n                                        columnNumber: 13\n                                    }, this),\n                                    t.previous\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 1155,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-4\",\n                                children: currentStep < totalSteps ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"button\",\n                                    onClick: nextStep,\n                                    disabled: !isStepValid(currentStep),\n                                    className: \"flex items-center gap-2 px-8 py-3 h-12 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl\",\n                                    children: [\n                                        t.next,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 1175,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                    lineNumber: 1168,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"submit\",\n                                    disabled: loading || isUploading || !isStepValid(currentStep),\n                                    className: \"flex items-center gap-2 px-8 py-3 h-12 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl\",\n                                    children: [\n                                        loading || isUploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 1184,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 1186,\n                                            columnNumber: 19\n                                        }, this),\n                                        isUploading ? language === 'ar' ? 'جاري رفع الصور...' : 'Uploading images...' : t.save\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                    lineNumber: 1178,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 1166,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                        lineNumber: 1154,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                lineNumber: 578,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n        lineNumber: 471,\n        columnNumber: 5\n    }, this);\n}\n_s(PropertyFormSteps, \"MaOwaAOGs49JLOCSkgePFHwIOnI=\", false, function() {\n    return [\n        _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_11__.useSimpleLanguage\n    ];\n});\n_c = PropertyFormSteps;\nvar _c;\n$RefreshReg$(_c, \"PropertyFormSteps\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/properties/create/property-form-steps.tsx\n"));

/***/ })

});