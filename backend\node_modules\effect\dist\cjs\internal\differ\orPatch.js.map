{"version": 3, "file": "orPatch.js", "names": ["Chunk", "_interopRequireWildcard", "require", "E", "Equal", "Dual", "_data", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "OrPatchTypeId", "exports", "Symbol", "for", "variance", "PatchProto", "Structural", "prototype", "_Value", "_Key", "_Patch", "EmptyProto", "assign", "create", "_tag", "_empty", "empty", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "makeAndThen", "first", "second", "o", "SetLeftProto", "makeSetLeft", "value", "SetRightProto", "makeSetRight", "UpdateLeftProto", "makeUpdateLeft", "patch", "UpdateRightProto", "makeUpdateRight", "diff", "options", "oldValue", "newValue", "valuePatch", "left", "equals", "right", "combine", "dual", "self", "that", "patches", "of", "result", "isNonEmpty", "head", "headNonEmpty", "tail", "tailNonEmpty", "prepend"], "sources": ["../../../../src/internal/differ/orPatch.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AAGA,IAAAC,CAAA,GAAAF,uBAAA,CAAAC,OAAA;AACA,IAAAE,KAAA,GAAAH,uBAAA,CAAAC,OAAA;AACA,IAAAG,IAAA,GAAAJ,uBAAA,CAAAC,OAAA;AACA,IAAAI,KAAA,GAAAJ,OAAA;AAAuC,SAAAK,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAP,wBAAAO,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAEvC;AACO,MAAMW,aAAa,GAAAC,OAAA,CAAAD,aAAA,gBAAqBE,MAAM,CAACC,GAAG,CAAC,sBAAsB,CAAqB;AAErG,SAASC,QAAQA,CAAOb,CAAI;EAC1B,OAAOA,CAAiB;AAC1B;AAEA;AACA,MAAMc,UAAU,GAAG;EACjB,GAAGC,gBAAU,CAACC,SAAS;EACvB,CAACP,aAAa,GAAG;IACfQ,MAAM,EAAEJ,QAAQ;IAChBK,IAAI,EAAEL,QAAQ;IACdM,MAAM,EAAEN;;CAEX;AASD,MAAMO,UAAU,gBAAGnB,MAAM,CAACoB,MAAM,eAACpB,MAAM,CAACqB,MAAM,CAACR,UAAU,CAAC,EAAE;EAC1DS,IAAI,EAAE;CACP,CAAC;AAEF,MAAMC,MAAM,gBAAGvB,MAAM,CAACqB,MAAM,CAACF,UAAU,CAAC;AAExC;AACO,MAAMK,KAAK,GAAGA,CAAA,KAKhBD,MAAM;AAAAd,OAAA,CAAAe,KAAA,GAAAA,KAAA;AAWX,MAAMC,YAAY,gBAAGzB,MAAM,CAACoB,MAAM,eAACpB,MAAM,CAACqB,MAAM,CAACR,UAAU,CAAC,EAAE;EAC5DS,IAAI,EAAE;CACP,CAAC;AAEF;AACO,MAAMI,WAAW,GAAGA,CACzBC,KAAoD,EACpDC,MAAqD,KAMnD;EACF,MAAMC,CAAC,GAAG7B,MAAM,CAACqB,MAAM,CAACI,YAAY,CAAC;EACrCI,CAAC,CAACF,KAAK,GAAGA,KAAK;EACfE,CAAC,CAACD,MAAM,GAAGA,MAAM;EACjB,OAAOC,CAAC;AACV,CAAC;AAAApB,OAAA,CAAAiB,WAAA,GAAAA,WAAA;AAUD,MAAMI,YAAY,gBAAG9B,MAAM,CAACoB,MAAM,eAACpB,MAAM,CAACqB,MAAM,CAACR,UAAU,CAAC,EAAE;EAC5DS,IAAI,EAAE;CACP,CAAC;AAEF;AACO,MAAMS,WAAW,GACtBC,KAAY,IAMV;EACF,MAAMH,CAAC,GAAG7B,MAAM,CAACqB,MAAM,CAACS,YAAY,CAAC;EACrCD,CAAC,CAACG,KAAK,GAAGA,KAAK;EACf,OAAOH,CAAC;AACV,CAAC;AAAApB,OAAA,CAAAsB,WAAA,GAAAA,WAAA;AAUD,MAAME,aAAa,gBAAGjC,MAAM,CAACoB,MAAM,eAACpB,MAAM,CAACqB,MAAM,CAACR,UAAU,CAAC,EAAE;EAC7DS,IAAI,EAAE;CACP,CAAC;AAEF;AACO,MAAMY,YAAY,GACvBF,KAAa,IAMX;EACF,MAAMH,CAAC,GAAG7B,MAAM,CAACqB,MAAM,CAACY,aAAa,CAAC;EACtCJ,CAAC,CAACG,KAAK,GAAGA,KAAK;EACf,OAAOH,CAAC;AACV,CAAC;AAAApB,OAAA,CAAAyB,YAAA,GAAAA,YAAA;AAUD,MAAMC,eAAe,gBAAGnC,MAAM,CAACoB,MAAM,eAACpB,MAAM,CAACqB,MAAM,CAACR,UAAU,CAAC,EAAE;EAC/DS,IAAI,EAAE;CACP,CAAC;AAEF;AACO,MAAMc,cAAc,GACzBC,KAAY,IAMV;EACF,MAAMR,CAAC,GAAG7B,MAAM,CAACqB,MAAM,CAACc,eAAe,CAAC;EACxCN,CAAC,CAACQ,KAAK,GAAGA,KAAK;EACf,OAAOR,CAAC;AACV,CAAC;AAAApB,OAAA,CAAA2B,cAAA,GAAAA,cAAA;AAUD,MAAME,gBAAgB,gBAAGtC,MAAM,CAACoB,MAAM,eAACpB,MAAM,CAACqB,MAAM,CAACR,UAAU,CAAC,EAAE;EAChES,IAAI,EAAE;CACP,CAAC;AAEF;AACO,MAAMiB,eAAe,GAC1BF,KAAa,IAMX;EACF,MAAMR,CAAC,GAAG7B,MAAM,CAACqB,MAAM,CAACiB,gBAAgB,CAAC;EACzCT,CAAC,CAACQ,KAAK,GAAGA,KAAK;EACf,OAAOR,CAAC;AACV,CAAC;AAUD;AAAApB,OAAA,CAAA8B,eAAA,GAAAA,eAAA;AACO,MAAMC,IAAI,GACfC,OAKC,IACgD;EACjD,QAAQA,OAAO,CAACC,QAAQ,CAACpB,IAAI;IAC3B,KAAK,MAAM;MAAE;QACX,QAAQmB,OAAO,CAACE,QAAQ,CAACrB,IAAI;UAC3B,KAAK,MAAM;YAAE;cACX,MAAMsB,UAAU,GAAGH,OAAO,CAACI,IAAI,CAACL,IAAI,CAACC,OAAO,CAACC,QAAQ,CAACG,IAAI,EAAEJ,OAAO,CAACE,QAAQ,CAACE,IAAI,CAAC;cAClF,IAAI5D,KAAK,CAAC6D,MAAM,CAACF,UAAU,EAAEH,OAAO,CAACI,IAAI,CAACrB,KAAK,CAAC,EAAE;gBAChD,OAAOA,KAAK,EAAE;cAChB;cACA,OAAOY,cAAc,CAACQ,UAAU,CAAC;YACnC;UACA,KAAK,OAAO;YAAE;cACZ,OAAOV,YAAY,CAACO,OAAO,CAACE,QAAQ,CAACI,KAAK,CAAC;YAC7C;QACF;MACF;IACA,KAAK,OAAO;MAAE;QACZ,QAAQN,OAAO,CAACE,QAAQ,CAACrB,IAAI;UAC3B,KAAK,MAAM;YAAE;cACX,OAAOS,WAAW,CAACU,OAAO,CAACE,QAAQ,CAACE,IAAI,CAAC;YAC3C;UACA,KAAK,OAAO;YAAE;cACZ,MAAMD,UAAU,GAAGH,OAAO,CAACM,KAAK,CAACP,IAAI,CAACC,OAAO,CAACC,QAAQ,CAACK,KAAK,EAAEN,OAAO,CAACE,QAAQ,CAACI,KAAK,CAAC;cACrF,IAAI9D,KAAK,CAAC6D,MAAM,CAACF,UAAU,EAAEH,OAAO,CAACM,KAAK,CAACvB,KAAK,CAAC,EAAE;gBACjD,OAAOA,KAAK,EAAE;cAChB;cACA,OAAOe,eAAe,CAACK,UAAU,CAAC;YACpC;QACF;MACF;EACF;AACF,CAAC;AAED;AAAAnC,OAAA,CAAA+B,IAAA,GAAAA,IAAA;AACO,MAAMQ,OAAO,GAAAvC,OAAA,CAAAuC,OAAA,gBAAG9D,IAAI,CAAC+D,IAAI,CAU9B,CAAC,EAAE,CAACC,IAAI,EAAEC,IAAI,KAAKzB,WAAW,CAACwB,IAAI,EAAEC,IAAI,CAAC,CAAC;AAE7C;AACO,MAAMd,KAAK,GAAA5B,OAAA,CAAA4B,KAAA,gBAAGnD,IAAI,CAAC+D,IAAI,CAgB5B,CAAC,EAAE,CACHC,IAAmD,EACnD;EAAEL,IAAI;EAAEH,QAAQ;EAAEK;AAAK,CAItB,KACC;EACF,IAAKG,IAAoB,CAAC5B,IAAI,KAAK,OAAO,EAAE;IAC1C,OAAOoB,QAAQ;EACjB;EACA,IAAIU,OAAO,GAA+DvE,KAAK,CAACwE,EAAE,CAACH,IAAI,CAAC;EACxF,IAAII,MAAM,GAAGZ,QAAQ;EACrB,OAAO7D,KAAK,CAAC0E,UAAU,CAACH,OAAO,CAAC,EAAE;IAChC,MAAMI,IAAI,GAAgB3E,KAAK,CAAC4E,YAAY,CAACL,OAAO,CAAgB;IACpE,MAAMM,IAAI,GAAG7E,KAAK,CAAC8E,YAAY,CAACP,OAAO,CAAC;IACxC,QAAQI,IAAI,CAAClC,IAAI;MACf,KAAK,OAAO;QAAE;UACZ8B,OAAO,GAAGM,IAAI;UACd;QACF;MACA,KAAK,SAAS;QAAE;UACdN,OAAO,GAAGvE,KAAK,CAAC+E,OAAO,CAACJ,IAAI,CAAC7B,KAAK,CAAC,CAAC9C,KAAK,CAAC+E,OAAO,CAACJ,IAAI,CAAC5B,MAAM,CAAC,CAAC8B,IAAI,CAAC,CAAC;UACrE;QACF;MACA,KAAK,YAAY;QAAE;UACjB,IAAIJ,MAAM,CAAChC,IAAI,KAAK,MAAM,EAAE;YAC1BgC,MAAM,GAAGtE,CAAC,CAAC6D,IAAI,CAACA,IAAI,CAACR,KAAK,CAACmB,IAAI,CAACnB,KAAK,EAAEiB,MAAM,CAACT,IAAI,CAAC,CAAC;UACtD;UACAO,OAAO,GAAGM,IAAI;UACd;QACF;MACA,KAAK,aAAa;QAAE;UAClB,IAAIJ,MAAM,CAAChC,IAAI,KAAK,OAAO,EAAE;YAC3BgC,MAAM,GAAGtE,CAAC,CAAC+D,KAAK,CAACA,KAAK,CAACV,KAAK,CAACmB,IAAI,CAACnB,KAAK,EAAEiB,MAAM,CAACP,KAAK,CAAC,CAAC;UACzD;UACAK,OAAO,GAAGM,IAAI;UACd;QACF;MACA,KAAK,SAAS;QAAE;UACdJ,MAAM,GAAGtE,CAAC,CAAC6D,IAAI,CAACW,IAAI,CAACxB,KAAK,CAAC;UAC3BoB,OAAO,GAAGM,IAAI;UACd;QACF;MACA,KAAK,UAAU;QAAE;UACfJ,MAAM,GAAGtE,CAAC,CAAC+D,KAAK,CAACS,IAAI,CAACxB,KAAK,CAAC;UAC5BoB,OAAO,GAAGM,IAAI;UACd;QACF;IACF;EACF;EACA,OAAOJ,MAAM;AACf,CAAC,CAAC", "ignoreList": []}