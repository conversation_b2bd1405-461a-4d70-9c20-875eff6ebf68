import * as _effect_platform_HttpClient from '@effect/platform/HttpClient';
import * as effect_Cause from 'effect/Cause';
import * as effect_ConfigError from 'effect/ConfigError';
import * as _effect_platform_HttpBody from '@effect/platform/HttpBody';
import * as _effect_platform_HttpServerError from '@effect/platform/HttpServerError';
import * as _effect_platform_HttpServerResponse from '@effect/platform/HttpServerResponse';
import * as ___internal_types from '../dist/_internal/types.cjs';
export { UTFiles, UTRegion as experimental_UTRegion } from '../dist/_internal/types.cjs';
import * as _uploadthing_shared from '@uploadthing/shared';
import { Json } from '@uploadthing/shared';
import { HttpServerRequest } from '@effect/platform';
import * as Effect from 'effect/Effect';
import { CreateBuilderOptions } from '../dist/_internal/upload-builder.cjs';
import { FileRouter, RouteHandlerConfig } from '../types/index.js';
export { FileRouter } from '../types/index.js';

type AdapterArgs = {
    req: HttpServerRequest.HttpServerRequest;
};
declare const createUploadthing: <TErrorShape extends Json>(opts?: CreateBuilderOptions<TErrorShape>) => <TRouteOptions extends _uploadthing_shared.RouteOptions>(input: _uploadthing_shared.FileRouterInputConfig, config?: TRouteOptions | undefined) => ___internal_types.UploadBuilder<{
    _routeOptions: TRouteOptions;
    _input: {
        in: ___internal_types.UnsetMarker;
        out: ___internal_types.UnsetMarker;
    };
    _metadata: ___internal_types.UnsetMarker;
    _adapterFnArgs: AdapterArgs;
    _errorShape: TErrorShape;
    _errorFn: ___internal_types.UnsetMarker;
    _output: ___internal_types.UnsetMarker;
}>;
declare const createRouteHandler: <TRouter extends FileRouter>(opts: {
    router: TRouter;
    /**
     * @remarks In order to obey by Effect conventions, we have omitted the `config.fetch` and `config.logLevel` options.
     * You can provide these layers on your own if you need to.
     *
     * @example
     * ```ts
     * import { Effect, Layer, Logger, LogLevel } from "effect";
     * import { HttpClient } from "@effect/platform";
  
     * // Set logLevel
     * Logger.withMinimumLogLevel(LogLevel.Debug)
     *
     * // Override fetch implementation
     * Layer.succeed(
     *   HttpClient.Fetch,
     *   myFetchImplementation,
     * );
     * ```
     */
    config?: Omit<RouteHandlerConfig, "fetch" | "logLevel">;
}) => Effect.Effect<_effect_platform_HttpServerResponse.HttpServerResponse, _effect_platform_HttpServerError.RequestError | _effect_platform_HttpServerError.RouteNotFound | _effect_platform_HttpBody.HttpBodyError | effect_ConfigError.ConfigError | effect_Cause.NoSuchElementException, HttpServerRequest.HttpServerRequest | _effect_platform_HttpClient.HttpClient>;

export { createRouteHandler, createUploadthing };
