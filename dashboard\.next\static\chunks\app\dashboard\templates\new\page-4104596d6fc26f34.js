(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7260],{11246:(e,a,s)=>{Promise.resolve().then(s.bind(s,12679))},12679:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>f});var t=s(95155),r=s(12115),l=s(35695),n=s(97168),i=s(89852),c=s(82714),d=s(99474),m=s(95784),o=s(88482),h=s(35169),x=s(84616),u=s(54416),p=s(51154),j=s(4229),g=s(14503),v=s(14353),b=s(88145),N=s(6874),y=s.n(N);function f(){let e=(0,l.useRouter)(),{addTemplate:a}=(0,v.U)(),[s,N]=(0,r.useState)({name:"",content:"",category:"custom",variables:[],language:"en"}),[f,C]=(0,r.useState)(""),[w,E]=(0,r.useState)(!1),{toast:T}=(0,g.dj)(),k=e=>{let{name:a,value:s}=e.target;N(e=>({...e,[a]:s}))},A=e=>{N(a=>({...a,variables:a.variables.filter(a=>a!==e)}))},F=async t=>{if(t.preventDefault(),!s.name.trim()){T({title:"Error",description:"Template name is required",variant:"destructive"});return}if(!s.content.trim()){T({title:"Error",description:"Template content is required",variant:"destructive"});return}E(!0);try{await a(s)?(T({title:"Template created",description:"The template has been successfully created."}),e.push("/dashboard/templates")):T({title:"Error",description:"Failed to create the template. Please try again.",variant:"destructive"})}catch(e){console.error("Error creating template:",e),T({title:"Error",description:"An unexpected error occurred. Please try again.",variant:"destructive"})}finally{E(!1)}};return(0,t.jsxs)("div",{className:"container mx-auto py-6 px-4",children:[(0,t.jsxs)("div",{className:"flex items-center mb-6",children:[(0,t.jsx)(y(),{href:"/dashboard/templates",children:(0,t.jsxs)(n.$,{variant:"outline",size:"sm",className:"mr-4",children:[(0,t.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"Back to Templates"]})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold",children:"Create New Template"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Create a new WhatsApp message template"})]})]}),(0,t.jsxs)(o.Zp,{children:[(0,t.jsxs)(o.aR,{children:[(0,t.jsx)(o.ZB,{children:"Template Details"}),(0,t.jsx)(o.BT,{children:"Fill in the details to create a new template"})]}),(0,t.jsxs)("form",{onSubmit:F,children:[(0,t.jsxs)(o.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(c.J,{htmlFor:"name",children:"Template Name"}),(0,t.jsx)(i.p,{id:"name",name:"name",value:s.name,onChange:k,placeholder:"Enter template name"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(c.J,{htmlFor:"category",children:"Category"}),(0,t.jsxs)(m.l6,{value:s.category,onValueChange:e=>{N(a=>({...a,category:e}))},children:[(0,t.jsx)(m.bq,{id:"category",children:(0,t.jsx)(m.yv,{placeholder:"Select category"})}),(0,t.jsxs)(m.gC,{children:[(0,t.jsx)(m.eb,{value:"greeting",children:"Greeting"}),(0,t.jsx)(m.eb,{value:"reminder",children:"Reminder"}),(0,t.jsx)(m.eb,{value:"update",children:"Update"}),(0,t.jsx)(m.eb,{value:"payment",children:"Payment"}),(0,t.jsx)(m.eb,{value:"marketing",children:"Marketing"}),(0,t.jsx)(m.eb,{value:"custom",children:"Custom"})]})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(c.J,{htmlFor:"language",children:"Language"}),(0,t.jsxs)(m.l6,{value:s.language,onValueChange:e=>{N(a=>({...a,language:e}))},children:[(0,t.jsx)(m.bq,{id:"language",children:(0,t.jsx)(m.yv,{placeholder:"Select language"})}),(0,t.jsxs)(m.gC,{children:[(0,t.jsx)(m.eb,{value:"en",children:"English"}),(0,t.jsx)(m.eb,{value:"ar",children:"Arabic"})]})]}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Select the language for this template. You can create templates in multiple languages."})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(c.J,{htmlFor:"content",children:"Template Content"}),(0,t.jsx)(d.T,{id:"content",name:"content",value:s.content,onChange:k,placeholder:"Enter template content",rows:5}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Use ","{{",(0,t.jsx)("span",{children:"variable"}),"}}"," syntax for personalization variables."]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(c.J,{children:"Variables"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(i.p,{value:f,onChange:e=>C(e.target.value),placeholder:"Add a variable"}),(0,t.jsx)(n.$,{type:"button",onClick:()=>{f.trim()&&!s.variables.includes(f.trim())&&(N(e=>({...e,variables:[...e.variables,f.trim()]})),C(""))},size:"sm",children:(0,t.jsx)(x.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{className:"flex flex-wrap gap-2 mt-2",children:[s.variables.map((e,a)=>(0,t.jsxs)(b.E,{variant:"secondary",className:"flex items-center gap-1",children:[e,(0,t.jsx)("button",{type:"button",onClick:()=>A(e),className:"text-muted-foreground hover:text-foreground",children:(0,t.jsx)(u.A,{className:"h-3 w-3"})})]},a)),0===s.variables.length&&(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"No variables added"})]})]})]}),(0,t.jsx)(o.wL,{className:"flex justify-end",children:(0,t.jsx)(n.$,{type:"submit",disabled:w,children:w?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(p.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Creating..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(j.A,{className:"mr-2 h-4 w-4"}),"Create Template"]})})})]})]})]})}}},e=>{var a=a=>e(e.s=a);e.O(0,[4277,6071,9509,9855,3464,6874,7196,8441,1684,7358],()=>a(11246)),_N_E=e.O()}]);