/** @internal */
export const OP_DIE = "Die" as const

/** @internal */
export type OP_DIE = typeof OP_DIE

/** @internal */
export const OP_EMPTY = "Empty" as const

/** @internal */
export type OP_EMPTY = typeof OP_EMPTY

/** @internal */
export const OP_FAIL = "Fail" as const

/** @internal */
export type OP_FAIL = typeof OP_FAIL

/** @internal */
export const OP_INTERRUPT = "Interrupt" as const

/** @internal */
export type OP_INTERRUPT = typeof OP_INTERRUPT

/** @internal */
export const OP_PARALLEL = "Parallel" as const

/** @internal */
export type OP_PARALLEL = typeof OP_PARALLEL

/** @internal */
export const OP_SEQUENTIAL = "Sequential" as const

/** @internal */
export type OP_SEQUENTIAL = typeof OP_SEQUENTIAL
