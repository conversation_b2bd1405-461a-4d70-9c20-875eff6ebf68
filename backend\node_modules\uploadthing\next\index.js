import * as Effect from 'effect/Effect';
import { makeAdapter<PERSON>and<PERSON> } from '../dist/_internal/handler.js';
import { createBuilder } from '../dist/_internal/upload-builder.js';
export { UTFiles, UTRegion as experimental_UTRegion } from '../dist/_internal/types.js';

const createUploadthing = (opts)=>createBuilder(opts);
const createRouteHandler = (opts)=>{
    const handler = makeAdapterHandler((req)=>Effect.succeed({
            req
        }), (req)=>Effect.succeed(req), opts, "nextjs-app");
    return {
        POST: handler,
        GET: handler
    };
};

export { createRouteHandler, createUploadthing };
