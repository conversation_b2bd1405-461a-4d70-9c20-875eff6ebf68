function defaultErrorFormatter(error) {
    return {
        message: error.message
    };
}
function formatError(error, router) {
    const firstSlug = Object.keys(router)[0];
    const errorFormatter = firstSlug ? router[firstSlug]?.errorFormatter ?? defaultErrorFormatter : defaultErrorFormatter;
    return errorFormatter(error);
}

exports.defaultErrorFormatter = defaultErrorFormatter;
exports.formatError = formatError;
