"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.unnested = exports.nested = exports.mapName = exports.empty = exports.andThen = void 0;
var internal = _interopRequireWildcard(require("./internal/configProvider/pathPatch.js"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
/**
 * @since 2.0.0
 */

/**
 * @since 2.0.0
 * @category constructors
 */
const empty = exports.empty = internal.empty;
/**
 * @since 2.0.0
 * @category constructors
 */
const andThen = exports.andThen = internal.andThen;
/**
 * @since 2.0.0
 * @category constructors
 */
const mapName = exports.mapName = internal.mapName;
/**
 * @since 2.0.0
 * @category constructors
 */
const nested = exports.nested = internal.nested;
/**
 * @since 2.0.0
 * @category constructors
 */
const unnested = exports.unnested = internal.unnested;
//# sourceMappingURL=ConfigProviderPathPatch.js.map