{"version": 3, "file": "multipart.js", "names": ["CT", "HP", "Search", "State", "errInvalidDisposition", "_tag", "errEnd<PERSON>otReached", "errMaxParts", "limit", "errMaxTotalSize", "errMaxPartSize", "errMaxFieldSize", "constCR", "TextEncoder", "encode", "defaultIsFile", "info", "filename", "undefined", "contentType", "parseBoundary", "headers", "parse", "parameters", "boundary", "noopOnChunk", "_chunk", "make", "onFile", "onPart", "onField", "onError", "onDone", "isFile", "maxParts", "Infinity", "maxTotalSize", "maxPartSize", "maxFieldSize", "write", "end", "state", "index", "parts", "onChunk", "headerSkip", "partSize", "totalSize", "fieldChunks", "fieldSize", "<PERSON><PERSON><PERSON>", "body", "<PERSON><PERSON><PERSON><PERSON>", "split", "chunk", "length", "buf", "Uint8Array", "offset", "i", "set", "result", "error", "contentDisposition", "value", "encodedFilename", "decodeURIComponent", "name", "contentTypeParameters", "contentDispositionParameters", "endPosition", "subarray", "push", "utf8Decoder", "TextDecoder", "getDecoder", "charset", "decodeField", "decode"], "sources": ["../../../src/internal/multipart.ts"], "sourcesContent": [null], "mappings": "AACA,OAAO,KAAKA,EAAE,MAAM,kBAAkB;AACtC,OAAO,KAAKC,EAAE,MAAM,cAAc;AAClC,OAAO,KAAKC,MAAM,MAAM,aAAa;AAErC,IAAWC,KAGV;AAHD,WAAWA,KAAK;EACdA,KAAA,CAAAA,KAAA,4BAAO;EACPA,KAAA,CAAAA,KAAA,sBAAI;AACN,CAAC,EAHUA,KAAK,KAALA,KAAK;AAKhB,MAAMC,qBAAqB,GAAmB;EAAEC,IAAI,EAAE;AAAoB,CAAE;AAC5E,MAAMC,gBAAgB,GAAmB;EAAED,IAAI,EAAE;AAAe,CAAE;AAClE,MAAME,WAAW,GAAmB;EAAEF,IAAI,EAAE,cAAc;EAAEG,KAAK,EAAE;AAAU,CAAE;AAC/E,MAAMC,eAAe,GAAmB;EACtCJ,IAAI,EAAE,cAAc;EACpBG,KAAK,EAAE;CACR;AACD,MAAME,cAAc,GAAmB;EACrCL,IAAI,EAAE,cAAc;EACpBG,KAAK,EAAE;CACR;AACD,MAAMG,eAAe,GAAmB;EACtCN,IAAI,EAAE,cAAc;EACpBG,KAAK,EAAE;CACR;AAED,MAAMI,OAAO,gBAAG,IAAIC,WAAW,EAAE,CAACC,MAAM,CAAC,MAAM,CAAC;AAEhD,OAAM,SAAUC,aAAaA,CAACC,IAAc;EAC1C,OACEA,IAAI,CAACC,QAAQ,KAAKC,SAAS,IAC3BF,IAAI,CAACG,WAAW,KAAK,0BAA0B;AAEnD;AAEA,SAASC,aAAaA,CAACC,OAA+B;EACpD,MAAMF,WAAW,GAAGnB,EAAE,CAACsB,KAAK,CAACD,OAAO,CAAC,cAAc,CAAC,CAAC;EACrD,OAAOF,WAAW,CAACI,UAAU,CAACC,QAAQ;AACxC;AAEA,SAASC,WAAWA,CAACC,MAAyB,GAAG;AAEjD,OAAM,SAAUC,IAAIA,CAAC;EACnBN,OAAO;EACPO,MAAM,EAAEC,MAAM;EACdC,OAAO;EACPC,OAAO;EACPC,MAAM;EACNC,MAAM,GAAGlB,aAAa;EACtBmB,QAAQ,GAAGC,QAAQ;EACnBC,YAAY,GAAGD,QAAQ;EACvBE,WAAW,GAAGF,QAAQ;EACtBG,YAAY,GAAG,IAAI,GAAG;AAAI,CACnB;EACP,MAAMd,QAAQ,GAAGJ,aAAa,CAACC,OAAO,CAAC;EACvC,IAAIG,QAAQ,KAAKN,SAAS,EAAE;IAC1Ba,OAAO,CAAC;MAAE1B,IAAI,EAAE;IAAiB,CAAE,CAAC;IACpC,OAAO;MACLkC,KAAK,EAAEd,WAAW;MAClBe,GAAGA,CAAA,GAAI;KACR;;EAGH,MAAMC,KAAK,GAAG;IACZA,KAAK,EAAEtC,KAAK,CAACkB,OAAO;IACpBqB,KAAK,EAAE,CAAC;IACRC,KAAK,EAAE,CAAC;IACRC,OAAO,EAAEnB,WAAW;IACpBT,IAAI,EAAEE,SAA4B;IAClC2B,UAAU,EAAE,CAAC;IACbC,QAAQ,EAAE,CAAC;IACXC,SAAS,EAAE,CAAC;IACZd,MAAM,EAAE,KAAK;IACbe,WAAW,EAAE,EAAuB;IACpCC,SAAS,EAAE;GACZ;EAED,SAASC,QAAQA,CAAA;IACfT,KAAK,CAACA,KAAK,GAAGtC,KAAK,CAACgD,IAAI;IACxBV,KAAK,CAACR,MAAM,GAAG,IAAI;IACnBQ,KAAK,CAACG,OAAO,GAAGnB,WAAW;EAC7B;EAEA,MAAM2B,YAAY,GAAGnD,EAAE,CAAC0B,IAAI,EAAE;EAE9B,MAAM0B,KAAK,GAAGnD,MAAM,CAACyB,IAAI,CACvB,SAASH,QAAQ,EAAE,EACnB,UAAUkB,KAAK,EAAEY,KAAK;IACpB,IAAIZ,KAAK,KAAK,CAAC,EAAE;MACf;MACAQ,QAAQ,EAAE;MACV;KACD,MAAM,IAAIR,KAAK,KAAKD,KAAK,CAACC,KAAK,EAAE;MAChC,IAAID,KAAK,CAACC,KAAK,GAAG,CAAC,EAAE;QACnB,IAAID,KAAK,CAACR,MAAM,EAAE;UAChBQ,KAAK,CAACG,OAAO,CAAC,IAAI,CAAC;UACnBH,KAAK,CAACK,QAAQ,GAAG,CAAC;SACnB,MAAM;UACL,IAAIL,KAAK,CAACO,WAAW,CAACO,MAAM,KAAK,CAAC,EAAE;YAClCzB,OAAO,CAACW,KAAK,CAACzB,IAAI,EAAEyB,KAAK,CAACO,WAAW,CAAC,CAAC,CAAC,CAAC;WAC1C,MAAM;YACL,MAAMQ,GAAG,GAAG,IAAIC,UAAU,CAAChB,KAAK,CAACQ,SAAS,CAAC;YAC3C,IAAIS,MAAM,GAAG,CAAC;YACd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlB,KAAK,CAACO,WAAW,CAACO,MAAM,EAAEI,CAAC,EAAE,EAAE;cACjD,MAAML,KAAK,GAAGb,KAAK,CAACO,WAAW,CAACW,CAAC,CAAC;cAClCH,GAAG,CAACI,GAAG,CAACN,KAAK,EAAEI,MAAM,CAAC;cACtBA,MAAM,IAAIJ,KAAK,CAACC,MAAM;;YAExBzB,OAAO,CAACW,KAAK,CAACzB,IAAI,EAAEwC,GAAG,CAAC;;UAE1Bf,KAAK,CAACQ,SAAS,GAAG,CAAC;UACnBR,KAAK,CAACO,WAAW,GAAG,EAAE;;;MAI1BP,KAAK,CAACA,KAAK,GAAGtC,KAAK,CAACkB,OAAO;MAC3BoB,KAAK,CAACC,KAAK,GAAGA,KAAK;MACnBD,KAAK,CAACI,UAAU,GAAG,CAAC,EAAC;MAErB;MACA,IAAIS,KAAK,CAAC,CAAC,CAAC,KAAK,EAAE,IAAIA,KAAK,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;QACtC,OAAOtB,MAAM,EAAE;;MAGjBS,KAAK,CAACE,KAAK,EAAE;MACb,IAAIF,KAAK,CAACE,KAAK,GAAGT,QAAQ,EAAE;QAC1BH,OAAO,CAACxB,WAAW,CAAC;;;IAIxB,IAAI,CAACkC,KAAK,CAACK,QAAQ,IAAIQ,KAAK,CAACC,MAAM,IAAIlB,WAAW,EAAE;MAClDN,OAAO,CAACrB,cAAc,CAAC;;IAGzB,IAAI+B,KAAK,CAACA,KAAK,KAAKtC,KAAK,CAACkB,OAAO,EAAE;MACjC,MAAMwC,MAAM,GAAGT,YAAY,CAACE,KAAK,EAAEb,KAAK,CAACI,UAAU,CAAC;MACpDJ,KAAK,CAACI,UAAU,GAAG,CAAC;MAEpB,IAAIgB,MAAM,CAACxD,IAAI,KAAK,UAAU,EAAE;QAC9B;OACD,MAAM,IAAIwD,MAAM,CAACxD,IAAI,KAAK,SAAS,EAAE;QACpC6C,QAAQ,EAAE;QACV,OAAOnB,OAAO,CAAC;UAAE1B,IAAI,EAAE,YAAY;UAAEyD,KAAK,EAAED;QAAM,CAAE,CAAC;;MAGvD,MAAM1C,WAAW,GAAGnB,EAAE,CAACsB,KAAK,CAACuC,MAAM,CAACxC,OAAO,CAAC,cAAc,CAAW,CAAC;MACtE,MAAM0C,kBAAkB,GAAG/D,EAAE,CAACsB,KAAK,CACjCuC,MAAM,CAACxC,OAAO,CAAC,qBAAqB,CAAW,EAC/C,IAAI,CACL;MAED,IACE,WAAW,KAAK0C,kBAAkB,CAACC,KAAK,IACxC,EAAE,MAAM,IAAID,kBAAkB,CAACxC,UAAU,CAAC,EAC1C;QACA2B,QAAQ,EAAE;QACV,OAAOnB,OAAO,CAAC3B,qBAAqB,CAAC;;MAGvC,IAAI6D,eAAmC;MACvC,IAAI,WAAW,IAAIF,kBAAkB,CAACxC,UAAU,EAAE;QAChD,MAAMoB,KAAK,GAAGoB,kBAAkB,CAACxC,UAAU,CAAC,WAAW,CAAC,CAAC8B,KAAK,CAAC,IAAI,CAAC;QACpE,IAAIV,KAAK,CAACY,MAAM,KAAK,CAAC,EAAE;UACtBU,eAAe,GAAGC,kBAAkB,CAACvB,KAAK,CAAC,CAAC,CAAC,CAAC;;;MAIlDF,KAAK,CAACzB,IAAI,GAAG;QACXmD,IAAI,EAAEJ,kBAAkB,CAACxC,UAAU,CAAC4C,IAAI,IAAI,EAAE;QAC9ClD,QAAQ,EAAEgD,eAAe,IAAIF,kBAAkB,CAACxC,UAAU,CAACN,QAAQ;QACnEE,WAAW,EACTA,WAAW,CAAC6C,KAAK,KAAK,EAAE,GACpBD,kBAAkB,CAACxC,UAAU,CAACN,QAAQ,KAAKC,SAAS,GAClD,0BAA0B,GAC1B,YAAY,GACdC,WAAW,CAAC6C,KAAK;QACvBI,qBAAqB,EAAEjD,WAAW,CAACI,UAAU;QAC7CwC,kBAAkB,EAAEA,kBAAkB,CAACC,KAAK;QAC5CK,4BAA4B,EAAEN,kBAAkB,CAACxC,UAAiB;QAClEF,OAAO,EAAEwC,MAAM,CAACxC;OACjB;MAEDoB,KAAK,CAACA,KAAK,GAAGtC,KAAK,CAACgD,IAAI;MACxBV,KAAK,CAACR,MAAM,GAAGA,MAAM,CAACQ,KAAK,CAACzB,IAAI,CAAC;MAEjC,IAAIyB,KAAK,CAACR,MAAM,EAAE;QAChBQ,KAAK,CAACG,OAAO,GAAGf,MAAM,CAACY,KAAK,CAACzB,IAAI,CAAC;;MAGpC,IAAI6C,MAAM,CAACS,WAAW,GAAGhB,KAAK,CAACC,MAAM,EAAE;QACrC,IAAId,KAAK,CAACR,MAAM,EAAE;UAChBQ,KAAK,CAACG,OAAO,CAACU,KAAK,CAACiB,QAAQ,CAACV,MAAM,CAACS,WAAW,CAAC,CAAC;SAClD,MAAM;UACL,MAAMd,GAAG,GAAGF,KAAK,CAACiB,QAAQ,CAACV,MAAM,CAACS,WAAW,CAAC;UAC9C,IAAI,CAAC7B,KAAK,CAACQ,SAAS,IAAIO,GAAG,CAACD,MAAM,IAAIjB,YAAY,EAAE;YAClDP,OAAO,CAACpB,eAAe,CAAC;;UAE1B8B,KAAK,CAACO,WAAW,CAACwB,IAAI,CAAChB,GAAG,CAAC;;;KAGhC,MAAM,IAAIf,KAAK,CAACR,MAAM,EAAE;MACvBQ,KAAK,CAACG,OAAO,CAACU,KAAK,CAAC;KACrB,MAAM;MACL,IAAI,CAACb,KAAK,CAACQ,SAAS,IAAIK,KAAK,CAACC,MAAM,IAAIjB,YAAY,EAAE;QACpDP,OAAO,CAACpB,eAAe,CAAC;;MAE1B8B,KAAK,CAACO,WAAW,CAACwB,IAAI,CAAClB,KAAK,CAAC;;EAEjC,CAAC,EACD1C,OAAO,CACR;EAED,OAAO;IACL2B,KAAKA,CAACe,KAAiB;MACrB,IAAI,CAACb,KAAK,CAACM,SAAS,IAAIO,KAAK,CAACC,MAAM,IAAInB,YAAY,EAAE;QACpD,OAAOL,OAAO,CAACtB,eAAe,CAAC;;MAEjC,OAAO4C,KAAK,CAACd,KAAK,CAACe,KAAK,CAAC;IAC3B,CAAC;IACDd,GAAGA,CAAA;MACDa,KAAK,CAACb,GAAG,EAAE;MACX,IAAIC,KAAK,CAACA,KAAK,KAAKtC,KAAK,CAACgD,IAAI,EAAE;QAC9BpB,OAAO,CAACzB,gBAAgB,CAAC;;MAG3BmC,KAAK,CAACA,KAAK,GAAGtC,KAAK,CAACkB,OAAO;MAC3BoB,KAAK,CAACC,KAAK,GAAG,CAAC;MACfD,KAAK,CAACE,KAAK,GAAG,CAAC;MACfF,KAAK,CAACG,OAAO,GAAGnB,WAAW;MAC3BgB,KAAK,CAACzB,IAAI,GAAGE,SAA4B;MACzCuB,KAAK,CAACM,SAAS,GAAG,CAAC;MACnBN,KAAK,CAACK,QAAQ,GAAG,CAAC;MAClBL,KAAK,CAACO,WAAW,GAAG,EAAE;MACtBP,KAAK,CAACQ,SAAS,GAAG,CAAC;IACrB;GACQ;AACZ;AAEA,MAAMwB,WAAW,gBAAG,IAAIC,WAAW,CAAC,OAAO,CAAC;AAC5C,SAASC,UAAUA,CAACC,OAAe;EACjC,IAAIA,OAAO,KAAK,OAAO,IAAIA,OAAO,KAAK,MAAM,IAAIA,OAAO,KAAK,EAAE,EAAE;IAC/D,OAAOH,WAAW;;EAGpB,IAAI;IACF,OAAO,IAAIC,WAAW,CAACE,OAAO,CAAC;GAChC,CAAC,OAAOd,KAAK,EAAE;IACd,OAAOW,WAAW;;AAEtB;AAEA,OAAM,SAAUI,WAAWA,CAAC7D,IAAc,EAAEgD,KAAiB;EAC3D,OAAOW,UAAU,CAAC3D,IAAI,CAACoD,qBAAqB,CAACQ,OAAO,IAAI,OAAO,CAAC,CAACE,MAAM,CAACd,KAAK,CAAC;AAChF"}