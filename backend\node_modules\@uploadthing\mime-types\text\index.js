const text = {
    "text/cache-manifest": {
        source: "iana",
        extensions: [
            "appcache",
            "manifest"
        ]
    },
    "text/calendar": {
        source: "iana",
        extensions: [
            "ics",
            "ifb"
        ]
    },
    "text/css": {
        source: "iana",
        charset: "UTF-8",
        extensions: [
            "css"
        ]
    },
    "text/csv": {
        source: "iana",
        extensions: [
            "csv"
        ]
    },
    "text/html": {
        source: "iana",
        extensions: [
            "html",
            "htm",
            "shtml"
        ]
    },
    "text/markdown": {
        source: "iana",
        extensions: [
            "markdown",
            "md"
        ]
    },
    "text/mathml": {
        source: "nginx",
        extensions: [
            "mml"
        ]
    },
    "text/n3": {
        source: "iana",
        charset: "UTF-8",
        extensions: [
            "n3"
        ]
    },
    "text/plain": {
        source: "iana",
        extensions: [
            "txt",
            "text",
            "conf",
            "def",
            "list",
            "log",
            "in",
            "ini"
        ]
    },
    "text/prs.lines.tag": {
        source: "iana",
        extensions: [
            "dsc"
        ]
    },
    "text/richtext": {
        source: "iana",
        extensions: [
            "rtx"
        ]
    },
    "text/rtf": {
        source: "iana",
        extensions: [
            "rtf"
        ]
    },
    "text/sgml": {
        source: "iana",
        extensions: [
            "sgml",
            "sgm"
        ]
    },
    "text/shex": {
        source: "iana",
        extensions: [
            "shex"
        ]
    },
    "text/spdx": {
        source: "iana",
        extensions: [
            "spdx"
        ]
    },
    "text/tab-separated-values": {
        source: "iana",
        extensions: [
            "tsv"
        ]
    },
    "text/troff": {
        source: "iana",
        extensions: [
            "t",
            "tr",
            "roff",
            "man",
            "me",
            "ms"
        ]
    },
    "text/turtle": {
        source: "iana",
        charset: "UTF-8",
        extensions: [
            "ttl"
        ]
    },
    "text/uri-list": {
        source: "iana",
        extensions: [
            "uri",
            "uris",
            "urls"
        ]
    },
    "text/vcard": {
        source: "iana",
        extensions: [
            "vcard"
        ]
    },
    "text/vnd.curl": {
        source: "iana",
        extensions: [
            "curl"
        ]
    },
    "text/vnd.curl.dcurl": {
        source: "apache",
        extensions: [
            "dcurl"
        ]
    },
    "text/vnd.curl.mcurl": {
        source: "apache",
        extensions: [
            "mcurl"
        ]
    },
    "text/vnd.curl.scurl": {
        source: "apache",
        extensions: [
            "scurl"
        ]
    },
    "text/vnd.dvb.subtitle": {
        source: "iana",
        extensions: [
            "sub"
        ]
    },
    "text/vnd.familysearch.gedcom": {
        source: "iana",
        extensions: [
            "ged"
        ]
    },
    "text/vnd.fly": {
        source: "iana",
        extensions: [
            "fly"
        ]
    },
    "text/vnd.fmi.flexstor": {
        source: "iana",
        extensions: [
            "flx"
        ]
    },
    "text/vnd.graphviz": {
        source: "iana",
        extensions: [
            "gv"
        ]
    },
    "text/vnd.in3d.3dml": {
        source: "iana",
        extensions: [
            "3dml"
        ]
    },
    "text/vnd.in3d.spot": {
        source: "iana",
        extensions: [
            "spot"
        ]
    },
    "text/vnd.sun.j2me.app-descriptor": {
        source: "iana",
        charset: "UTF-8",
        extensions: [
            "jad"
        ]
    },
    "text/vnd.wap.wml": {
        source: "iana",
        extensions: [
            "wml"
        ]
    },
    "text/vnd.wap.wmlscript": {
        source: "iana",
        extensions: [
            "wmls"
        ]
    },
    "text/vtt": {
        source: "iana",
        charset: "UTF-8",
        extensions: [
            "vtt"
        ]
    },
    "text/x-asm": {
        source: "apache",
        extensions: [
            "s",
            "asm"
        ]
    },
    "text/x-c": {
        source: "apache",
        extensions: [
            "c",
            "cc",
            "cxx",
            "cpp",
            "h",
            "hh",
            "dic"
        ]
    },
    "text/x-component": {
        source: "nginx",
        extensions: [
            "htc"
        ]
    },
    "text/x-fortran": {
        source: "apache",
        extensions: [
            "f",
            "for",
            "f77",
            "f90"
        ]
    },
    "text/x-java-source": {
        source: "apache",
        extensions: [
            "java"
        ]
    },
    "text/x-nfo": {
        source: "apache",
        extensions: [
            "nfo"
        ]
    },
    "text/x-opml": {
        source: "apache",
        extensions: [
            "opml"
        ]
    },
    "text/x-pascal": {
        source: "apache",
        extensions: [
            "p",
            "pas"
        ]
    },
    "text/x-setext": {
        source: "apache",
        extensions: [
            "etx"
        ]
    },
    "text/x-sfv": {
        source: "apache",
        extensions: [
            "sfv"
        ]
    },
    "text/x-uuencode": {
        source: "apache",
        extensions: [
            "uu"
        ]
    },
    "text/x-vcalendar": {
        source: "apache",
        extensions: [
            "vcs"
        ]
    },
    "text/x-vcard": {
        source: "apache",
        extensions: [
            "vcf"
        ]
    },
    "text/xml": {
        source: "iana",
        extensions: [
            "xml"
        ]
    }
};

export { text };
