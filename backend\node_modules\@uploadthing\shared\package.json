{"name": "@uploadthing/shared", "version": "7.1.8", "type": "module", "sideEffects": false, "license": "MIT", "main": "dist/index.cjs", "module": "dist/index.js", "typings": "dist/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "files": ["dist"], "publishConfig": {"access": "public"}, "dependencies": {"@uploadthing/mime-types": "0.3.5", "effect": "3.14.21", "sqids": "^0.3.0"}}