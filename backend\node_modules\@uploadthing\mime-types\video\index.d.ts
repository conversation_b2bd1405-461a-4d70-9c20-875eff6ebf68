declare const video: {
    readonly "video/3gpp": {
        readonly source: "iana";
        readonly extensions: readonly ["3gp", "3gpp"];
    };
    readonly "video/3gpp2": {
        readonly source: "iana";
        readonly extensions: readonly ["3g2"];
    };
    readonly "video/h261": {
        readonly source: "iana";
        readonly extensions: readonly ["h261"];
    };
    readonly "video/h263": {
        readonly source: "iana";
        readonly extensions: readonly ["h263"];
    };
    readonly "video/h264": {
        readonly source: "iana";
        readonly extensions: readonly ["h264"];
    };
    readonly "video/iso.segment": {
        readonly source: "iana";
        readonly extensions: readonly ["m4s"];
    };
    readonly "video/jpeg": {
        readonly source: "iana";
        readonly extensions: readonly ["jpgv"];
    };
    readonly "video/jpm": {
        readonly source: "apache";
        readonly extensions: readonly ["jpm", "jpgm"];
    };
    readonly "video/mj2": {
        readonly source: "iana";
        readonly extensions: readonly ["mj2", "mjp2"];
    };
    readonly "video/mp2t": {
        readonly source: "iana";
        readonly extensions: readonly ["ts"];
    };
    readonly "video/mp4": {
        readonly source: "iana";
        readonly extensions: readonly ["mp4", "mp4v", "mpg4"];
    };
    readonly "video/mpeg": {
        readonly source: "iana";
        readonly extensions: readonly ["mpeg", "mpg", "mpe", "m1v", "m2v"];
    };
    readonly "video/ogg": {
        readonly source: "iana";
        readonly extensions: readonly ["ogv"];
    };
    readonly "video/quicktime": {
        readonly source: "iana";
        readonly extensions: readonly ["qt", "mov"];
    };
    readonly "video/vnd.dece.hd": {
        readonly source: "iana";
        readonly extensions: readonly ["uvh", "uvvh"];
    };
    readonly "video/vnd.dece.mobile": {
        readonly source: "iana";
        readonly extensions: readonly ["uvm", "uvvm"];
    };
    readonly "video/vnd.dece.pd": {
        readonly source: "iana";
        readonly extensions: readonly ["uvp", "uvvp"];
    };
    readonly "video/vnd.dece.sd": {
        readonly source: "iana";
        readonly extensions: readonly ["uvs", "uvvs"];
    };
    readonly "video/vnd.dece.video": {
        readonly source: "iana";
        readonly extensions: readonly ["uvv", "uvvv"];
    };
    readonly "video/vnd.dvb.file": {
        readonly source: "iana";
        readonly extensions: readonly ["dvb"];
    };
    readonly "video/vnd.fvt": {
        readonly source: "iana";
        readonly extensions: readonly ["fvt"];
    };
    readonly "video/vnd.mpegurl": {
        readonly source: "iana";
        readonly extensions: readonly ["mxu", "m4u"];
    };
    readonly "video/vnd.ms-playready.media.pyv": {
        readonly source: "iana";
        readonly extensions: readonly ["pyv"];
    };
    readonly "video/vnd.uvvu.mp4": {
        readonly source: "iana";
        readonly extensions: readonly ["uvu", "uvvu"];
    };
    readonly "video/vnd.vivo": {
        readonly source: "iana";
        readonly extensions: readonly ["viv"];
    };
    readonly "video/webm": {
        readonly source: "apache";
        readonly extensions: readonly ["webm"];
    };
    readonly "video/x-f4v": {
        readonly source: "apache";
        readonly extensions: readonly ["f4v"];
    };
    readonly "video/x-fli": {
        readonly source: "apache";
        readonly extensions: readonly ["fli"];
    };
    readonly "video/x-flv": {
        readonly source: "apache";
        readonly extensions: readonly ["flv"];
    };
    readonly "video/x-m4v": {
        readonly source: "apache";
        readonly extensions: readonly ["m4v"];
    };
    readonly "video/x-matroska": {
        readonly source: "apache";
        readonly extensions: readonly ["mkv", "mk3d", "mks"];
    };
    readonly "video/x-mng": {
        readonly source: "apache";
        readonly extensions: readonly ["mng"];
    };
    readonly "video/x-ms-asf": {
        readonly source: "apache";
        readonly extensions: readonly ["asf", "asx"];
    };
    readonly "video/x-ms-vob": {
        readonly source: "apache";
        readonly extensions: readonly ["vob"];
    };
    readonly "video/x-ms-wm": {
        readonly source: "apache";
        readonly extensions: readonly ["wm"];
    };
    readonly "video/x-ms-wmv": {
        readonly source: "apache";
        readonly extensions: readonly ["wmv"];
    };
    readonly "video/x-ms-wmx": {
        readonly source: "apache";
        readonly extensions: readonly ["wmx"];
    };
    readonly "video/x-ms-wvx": {
        readonly source: "apache";
        readonly extensions: readonly ["wvx"];
    };
    readonly "video/x-msvideo": {
        readonly source: "apache";
        readonly extensions: readonly ["avi"];
    };
    readonly "video/x-sgi-movie": {
        readonly source: "apache";
        readonly extensions: readonly ["movie"];
    };
    readonly "video/x-smv": {
        readonly source: "apache";
        readonly extensions: readonly ["smv"];
    };
};

export { video };
