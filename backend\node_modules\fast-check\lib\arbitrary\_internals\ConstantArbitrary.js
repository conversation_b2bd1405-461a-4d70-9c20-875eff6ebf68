"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConstantArbitrary = void 0;
const Stream_1 = require("../../stream/Stream");
const Arbitrary_1 = require("../../check/arbitrary/definition/Arbitrary");
const Value_1 = require("../../check/arbitrary/definition/Value");
const symbols_1 = require("../../check/symbols");
const globals_1 = require("../../utils/globals");
const safeObjectIs = Object.is;
class ConstantArbitrary extends Arbitrary_1.Arbitrary {
    constructor(values) {
        super();
        this.values = values;
    }
    generate(mrng, _biasFactor) {
        const idx = this.values.length === 1 ? 0 : mrng.nextInt(0, this.values.length - 1);
        const value = this.values[idx];
        if (!(0, symbols_1.hasCloneMethod)(value)) {
            return new Value_1.Value(value, idx);
        }
        return new Value_1.Value(value, idx, () => value[symbols_1.cloneMethod]());
    }
    canShrinkWithoutContext(value) {
        if (this.values.length === 1) {
            return safeObjectIs(this.values[0], value);
        }
        if (this.fastValues === undefined) {
            this.fastValues = new FastConstantValuesLookup(this.values);
        }
        return this.fastValues.has(value);
    }
    shrink(value, context) {
        if (context === 0 || safeObjectIs(value, this.values[0])) {
            return Stream_1.Stream.nil();
        }
        return Stream_1.Stream.of(new Value_1.Value(this.values[0], 0));
    }
}
exports.ConstantArbitrary = ConstantArbitrary;
class FastConstantValuesLookup {
    constructor(values) {
        this.values = values;
        this.fastValues = new globals_1.Set(this.values);
        let hasMinusZero = false;
        let hasPlusZero = false;
        if ((0, globals_1.safeHas)(this.fastValues, 0)) {
            for (let idx = 0; idx !== this.values.length; ++idx) {
                const value = this.values[idx];
                hasMinusZero = hasMinusZero || safeObjectIs(value, -0);
                hasPlusZero = hasPlusZero || safeObjectIs(value, 0);
            }
        }
        this.hasMinusZero = hasMinusZero;
        this.hasPlusZero = hasPlusZero;
    }
    has(value) {
        if (value === 0) {
            if (safeObjectIs(value, 0)) {
                return this.hasPlusZero;
            }
            return this.hasMinusZero;
        }
        return (0, globals_1.safeHas)(this.fastValues, value);
    }
}
