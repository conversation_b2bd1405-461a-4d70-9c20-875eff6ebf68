{"version": 3, "file": "contentType.js", "names": ["paramRE", "quotedPairRE", "mediaTypeRE", "mediaTypeRENoSlash", "defaultContentType", "value", "parameters", "Object", "create", "parse", "header", "withoutSlash", "index", "indexOf", "type", "slice", "trim", "mediaRE", "test", "result", "toLowerCase", "key", "match", "lastIndex", "exec", "length", "replace"], "sources": ["../../../src/internal/contentType.ts"], "sourcesContent": [null], "mappings": "AAAA;AACA;AAEA;;;;;;;;;;;;;;AAcA,MAAMA,OAAO,GACX,uIAAuI;AAEzI;;;;;;AAMA,MAAMC,YAAY,GAAG,yBAAyB;AAE9C;;;;;;;AAOA,MAAMC,WAAW,GAAG,2CAA2C;AAC/D,MAAMC,kBAAkB,GAAG,uBAAuB;AAElD;AACA,MAAMC,kBAAkB,GAAG;EAAEC,KAAK,EAAE,EAAE;EAAEC,UAAU,eAAEC,MAAM,CAACC,MAAM,CAAC,IAAI;AAAC,CAAE;AAEzE,OAAM,SAAUC,KAAKA,CACnBC,MAA0B,EAC1BC,YAAY,GAAG,KAAK;EAKpB,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;IAC9B,OAAON,kBAAkB;;EAG3B,IAAIQ,KAAK,GAAGF,MAAM,CAACG,OAAO,CAAC,GAAG,CAAC;EAC/B,MAAMC,IAAI,GAAGF,KAAK,KAAK,CAAC,CAAC,GAAGF,MAAM,CAACK,KAAK,CAAC,CAAC,EAAEH,KAAK,CAAC,CAACI,IAAI,EAAE,GAAGN,MAAM,CAACM,IAAI,EAAE;EACzE,MAAMC,OAAO,GAAGN,YAAY,GAAGR,kBAAkB,GAAGD,WAAW;EAE/D,IAAIe,OAAO,CAACC,IAAI,CAACJ,IAAI,CAAC,KAAK,KAAK,EAAE;IAChC,OAAOV,kBAAkB;;EAG3B,MAAMe,MAAM,GAAG;IACbd,KAAK,EAAES,IAAI,CAACM,WAAW,EAAE;IACzBd,UAAU,EAAEC,MAAM,CAACC,MAAM,CAAC,IAAI;GAC/B;EAED;EACA,IAAII,KAAK,KAAK,CAAC,CAAC,EAAE;IAChB,OAAOO,MAAM;;EAGf,IAAIE,GAAW;EACf,IAAIC,KAA6B;EACjC,IAAIjB,KAAa;EAEjBL,OAAO,CAACuB,SAAS,GAAGX,KAAK;EAEzB,OAAQU,KAAK,GAAGtB,OAAO,CAACwB,IAAI,CAACd,MAAM,CAAC,EAAG;IACrC,IAAIY,KAAK,CAACV,KAAK,KAAKA,KAAK,EAAE;MACzB,OAAOR,kBAAkB;;IAG3BQ,KAAK,IAAIU,KAAK,CAAC,CAAC,CAAC,CAACG,MAAM;IACxBJ,GAAG,GAAGC,KAAK,CAAC,CAAC,CAAC,CAACF,WAAW,EAAE;IAC5Bf,KAAK,GAAGiB,KAAK,CAAC,CAAC,CAAC;IAEhB,IAAIjB,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MACpB;MACAA,KAAK,GAAGA,KAAK,CAACU,KAAK,CAAC,CAAC,EAAEV,KAAK,CAACoB,MAAM,GAAG,CAAC,CAAC;MAExC,CAACd,YAAY,IACXV,YAAY,CAACiB,IAAI,CAACb,KAAK,CAAC,KACvBA,KAAK,GAAGA,KAAK,CAACqB,OAAO,CAACzB,YAAY,EAAE,IAAI,CAAC,CAAC;;IAG/CkB,MAAM,CAACb,UAAU,CAACe,GAAG,CAAC,GAAGhB,KAAK;;EAGhC,IAAIO,KAAK,KAAKF,MAAM,CAACe,MAAM,EAAE;IAC3B,OAAOrB,kBAAkB;;EAG3B,OAAOe,MAAM;AACf"}