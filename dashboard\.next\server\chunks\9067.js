"use strict";exports.id=9067,exports.ids=[9067],exports.modules={26134:(e,t,r)=>{r.d(t,{G$:()=>W,Hs:()=>b,UC:()=>et,VY:()=>eo,ZL:()=>Q,bL:()=>K,bm:()=>en,hE:()=>er,hJ:()=>ee,l9:()=>X});var o=r(43210),n=r(70569),a=r(98599),s=r(11273),i=r(96963),l=r(65551),d=r(31355),u=r(32547),c=r(25028),p=r(46059),f=r(14163),g=r(1359),m=r(11490),v=r(63376),h=r(8730),y=r(60687),x="Dialog",[D,b]=(0,s.A)(x),[j,w]=D(x),R=e=>{let{__scopeDialog:t,children:r,open:n,defaultOpen:a,onOpenChange:s,modal:d=!0}=e,u=o.useRef(null),c=o.useRef(null),[p=!1,f]=(0,l.i)({prop:n,defaultProp:a,onChange:s});return(0,y.jsx)(j,{scope:t,triggerRef:u,contentRef:c,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:f,onOpenToggle:o.useCallback(()=>f(e=>!e),[f]),modal:d,children:r})};R.displayName=x;var C="DialogTrigger",A=o.forwardRef((e,t)=>{let{__scopeDialog:r,...o}=e,s=w(C,r),i=(0,a.s)(t,s.triggerRef);return(0,y.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":s.open,"aria-controls":s.contentId,"data-state":S(s.open),...o,ref:i,onClick:(0,n.m)(e.onClick,s.onOpenToggle)})});A.displayName=C;var N="DialogPortal",[I,O]=D(N,{forceMount:void 0}),E=e=>{let{__scopeDialog:t,forceMount:r,children:n,container:a}=e,s=w(N,t);return(0,y.jsx)(I,{scope:t,forceMount:r,children:o.Children.map(n,e=>(0,y.jsx)(p.C,{present:r||s.open,children:(0,y.jsx)(c.Z,{asChild:!0,container:a,children:e})}))})};E.displayName=N;var F="DialogOverlay",P=o.forwardRef((e,t)=>{let r=O(F,e.__scopeDialog),{forceMount:o=r.forceMount,...n}=e,a=w(F,e.__scopeDialog);return a.modal?(0,y.jsx)(p.C,{present:o||a.open,children:(0,y.jsx)(_,{...n,ref:t})}):null});P.displayName=F;var _=o.forwardRef((e,t)=>{let{__scopeDialog:r,...o}=e,n=w(F,r);return(0,y.jsx)(m.A,{as:h.DX,allowPinchZoom:!0,shards:[n.contentRef],children:(0,y.jsx)(f.sG.div,{"data-state":S(n.open),...o,ref:t,style:{pointerEvents:"auto",...o.style}})})}),$="DialogContent",G=o.forwardRef((e,t)=>{let r=O($,e.__scopeDialog),{forceMount:o=r.forceMount,...n}=e,a=w($,e.__scopeDialog);return(0,y.jsx)(p.C,{present:o||a.open,children:a.modal?(0,y.jsx)(L,{...n,ref:t}):(0,y.jsx)(M,{...n,ref:t})})});G.displayName=$;var L=o.forwardRef((e,t)=>{let r=w($,e.__scopeDialog),s=o.useRef(null),i=(0,a.s)(t,r.contentRef,s);return o.useEffect(()=>{let e=s.current;if(e)return(0,v.Eq)(e)},[]),(0,y.jsx)(T,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,n.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,n.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,n.m)(e.onFocusOutside,e=>e.preventDefault())})}),M=o.forwardRef((e,t)=>{let r=w($,e.__scopeDialog),n=o.useRef(!1),a=o.useRef(!1);return(0,y.jsx)(T,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(n.current||r.triggerRef.current?.focus(),t.preventDefault()),n.current=!1,a.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(n.current=!0,"pointerdown"!==t.detail.originalEvent.type||(a.current=!0));let o=t.target;r.triggerRef.current?.contains(o)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),T=o.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:n,onOpenAutoFocus:s,onCloseAutoFocus:i,...l}=e,c=w($,r),p=o.useRef(null),f=(0,a.s)(t,p);return(0,g.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(u.n,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:s,onUnmountAutoFocus:i,children:(0,y.jsx)(d.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":S(c.open),...l,ref:f,onDismiss:()=>c.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(J,{titleId:c.titleId}),(0,y.jsx)(z,{contentRef:p,descriptionId:c.descriptionId})]})]})}),k="DialogTitle",B=o.forwardRef((e,t)=>{let{__scopeDialog:r,...o}=e,n=w(k,r);return(0,y.jsx)(f.sG.h2,{id:n.titleId,...o,ref:t})});B.displayName=k;var Z="DialogDescription",q=o.forwardRef((e,t)=>{let{__scopeDialog:r,...o}=e,n=w(Z,r);return(0,y.jsx)(f.sG.p,{id:n.descriptionId,...o,ref:t})});q.displayName=Z;var V="DialogClose",H=o.forwardRef((e,t)=>{let{__scopeDialog:r,...o}=e,a=w(V,r);return(0,y.jsx)(f.sG.button,{type:"button",...o,ref:t,onClick:(0,n.m)(e.onClick,()=>a.onOpenChange(!1))})});function S(e){return e?"open":"closed"}H.displayName=V;var U="DialogTitleWarning",[W,Y]=(0,s.q)(U,{contentName:$,titleName:k,docsSlug:"dialog"}),J=({titleId:e})=>{let t=Y(U),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return o.useEffect(()=>{e&&!document.getElementById(e)&&console.error(r)},[r,e]),null},z=({contentRef:e,descriptionId:t})=>{let r=Y("DialogDescriptionWarning"),n=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return o.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&!document.getElementById(t)&&console.warn(n)},[n,e,t]),null},K=R,X=A,Q=E,ee=P,et=G,er=B,eo=q,en=H},41862:(e,t,r)=>{r.d(t,{A:()=>o});let o=(0,r(62688).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},78148:(e,t,r)=>{r.d(t,{b:()=>i});var o=r(43210),n=r(14163),a=r(60687),s=o.forwardRef((e,t)=>(0,a.jsx)(n.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));s.displayName="Label";var i=s},97895:(e,t,r)=>{r.d(t,{UC:()=>$,VY:()=>T,ZD:()=>L,ZL:()=>P,bL:()=>E,hE:()=>M,hJ:()=>_,l9:()=>F,rc:()=>G});var o=r(43210),n=r(11273),a=r(98599),s=r(26134),i=r(70569),l=r(8730),d=r(60687),u="AlertDialog",[c,p]=(0,n.A)(u,[s.Hs]),f=(0,s.Hs)(),g=e=>{let{__scopeAlertDialog:t,...r}=e,o=f(t);return(0,d.jsx)(s.bL,{...o,...r,modal:!0})};g.displayName=u;var m=o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...o}=e,n=f(r);return(0,d.jsx)(s.l9,{...n,...o,ref:t})});m.displayName="AlertDialogTrigger";var v=e=>{let{__scopeAlertDialog:t,...r}=e,o=f(t);return(0,d.jsx)(s.ZL,{...o,...r})};v.displayName="AlertDialogPortal";var h=o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...o}=e,n=f(r);return(0,d.jsx)(s.hJ,{...n,...o,ref:t})});h.displayName="AlertDialogOverlay";var y="AlertDialogContent",[x,D]=c(y),b=o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,children:n,...u}=e,c=f(r),p=o.useRef(null),g=(0,a.s)(t,p),m=o.useRef(null);return(0,d.jsx)(s.G$,{contentName:y,titleName:j,docsSlug:"alert-dialog",children:(0,d.jsx)(x,{scope:r,cancelRef:m,children:(0,d.jsxs)(s.UC,{role:"alertdialog",...c,...u,ref:g,onOpenAutoFocus:(0,i.m)(u.onOpenAutoFocus,e=>{e.preventDefault(),m.current?.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,d.jsx)(l.xV,{children:n}),(0,d.jsx)(O,{contentRef:p})]})})})});b.displayName=y;var j="AlertDialogTitle",w=o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...o}=e,n=f(r);return(0,d.jsx)(s.hE,{...n,...o,ref:t})});w.displayName=j;var R="AlertDialogDescription",C=o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...o}=e,n=f(r);return(0,d.jsx)(s.VY,{...n,...o,ref:t})});C.displayName=R;var A=o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...o}=e,n=f(r);return(0,d.jsx)(s.bm,{...n,...o,ref:t})});A.displayName="AlertDialogAction";var N="AlertDialogCancel",I=o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...o}=e,{cancelRef:n}=D(N,r),i=f(r),l=(0,a.s)(t,n);return(0,d.jsx)(s.bm,{...i,...o,ref:l})});I.displayName=N;var O=({contentRef:e})=>{let t=`\`${y}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${y}\` by passing a \`${R}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${y}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return o.useEffect(()=>{document.getElementById(e.current?.getAttribute("aria-describedby"))||console.warn(t)},[t,e]),null},E=g,F=m,P=v,_=h,$=b,G=A,L=I,M=w,T=C}};