"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/properties/edit/[id]/page",{

/***/ "(app-pages-browser)/./app/dashboard/properties/create/property-form-steps.tsx":
/*!*****************************************************************!*\
  !*** ./app/dashboard/properties/create/property-form-steps.tsx ***!
  \*****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PropertyFormSteps: () => (/* binding */ PropertyFormSteps)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,Plus,Save,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,Plus,Save,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,Plus,Save,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,Plus,Save,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,Plus,Save,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,Plus,Save,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,Plus,Save,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/hooks/useSimpleLanguage */ \"(app-pages-browser)/./hooks/useSimpleLanguage.tsx\");\n/* __next_internal_client_entry_do_not_use__ PropertyFormSteps auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction PropertyFormSteps(param) {\n    let { onSave, loading, initialData, isEdit = false } = param;\n    _s();\n    const { language } = (0,_hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_11__.useSimpleLanguage)();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const totalSteps = 4;\n    const defaultFormData = {\n        title: '',\n        titleAr: '',\n        description: '',\n        descriptionAr: '',\n        price: '',\n        currency: 'AED',\n        type: 'APARTMENT',\n        status: 'AVAILABLE',\n        bedrooms: '',\n        bathrooms: '',\n        area: '',\n        location: '',\n        locationAr: '',\n        address: '',\n        addressAr: '',\n        city: '',\n        cityAr: '',\n        country: 'UAE',\n        countryAr: 'الإمارات العربية المتحدة',\n        images: [],\n        features: [],\n        featuresAr: [],\n        amenities: [],\n        amenitiesAr: [],\n        yearBuilt: '',\n        parking: '',\n        furnished: false,\n        petFriendly: false,\n        utilities: '',\n        utilitiesAr: '',\n        contactInfo: '',\n        isFeatured: false,\n        isActive: true\n    };\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialData || defaultFormData);\n    const [newFeature, setNewFeature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newFeatureAr, setNewFeatureAr] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newAmenity, setNewAmenity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newAmenityAr, setNewAmenityAr] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Auto-save functionality (only for create mode)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PropertyFormSteps.useEffect\": ()=>{\n            if (!isEdit && !initialData) {\n                const savedData = localStorage.getItem('property-draft');\n                if (savedData) {\n                    try {\n                        const parsed = JSON.parse(savedData);\n                        setFormData(parsed);\n                    } catch (error) {\n                        console.error('Error loading draft:', error);\n                    }\n                }\n            }\n        }\n    }[\"PropertyFormSteps.useEffect\"], [\n        isEdit,\n        initialData\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PropertyFormSteps.useEffect\": ()=>{\n            if (!isEdit) {\n                const timer = setTimeout({\n                    \"PropertyFormSteps.useEffect.timer\": ()=>{\n                        localStorage.setItem('property-draft', JSON.stringify(formData));\n                    }\n                }[\"PropertyFormSteps.useEffect.timer\"], 1000);\n                return ({\n                    \"PropertyFormSteps.useEffect\": ()=>clearTimeout(timer)\n                })[\"PropertyFormSteps.useEffect\"];\n            }\n        }\n    }[\"PropertyFormSteps.useEffect\"], [\n        formData,\n        isEdit\n    ]);\n    // Initialize form data when initialData changes (for edit mode)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PropertyFormSteps.useEffect\": ()=>{\n            if (initialData && isEdit) {\n                setFormData(initialData);\n            }\n        }\n    }[\"PropertyFormSteps.useEffect\"], [\n        initialData,\n        isEdit\n    ]);\n    // Comprehensive bilingual translations\n    const translations = {\n        ar: {\n            step: 'الخطوة',\n            of: 'من',\n            next: 'التالي',\n            previous: 'السابق',\n            save: 'حفظ العقار',\n            required: 'مطلوب',\n            optional: 'اختياري',\n            basicInfo: 'المعلومات الأساسية',\n            propertyDetails: 'تفاصيل العقار',\n            locationInfo: 'معلومات الموقع',\n            additionalInfo: 'معلومات إضافية',\n            title: 'عنوان العقار',\n            description: 'وصف العقار',\n            price: 'السعر',\n            currency: 'العملة',\n            propertyType: 'نوع العقار',\n            status: 'حالة العقار',\n            bedrooms: 'غرف النوم',\n            bathrooms: 'دورات المياه',\n            area: 'المساحة (متر مربع)',\n            yearBuilt: 'سنة البناء',\n            parking: 'مواقف السيارات',\n            location: 'الموقع',\n            address: 'العنوان',\n            city: 'المدينة',\n            country: 'الدولة',\n            images: 'صور العقار',\n            features: 'مميزات العقار',\n            amenities: 'المرافق والخدمات',\n            utilities: 'الخدمات المشمولة',\n            contactInfo: 'معلومات التواصل',\n            furnished: 'مفروش',\n            petFriendly: 'يسمح بالحيوانات الأليفة',\n            featured: 'عقار مميز',\n            active: 'نشط',\n            addFeature: 'إضافة ميزة',\n            addAmenity: 'إضافة مرفق',\n            uploadImages: 'رفع صور العقار',\n            dragDropImages: 'اسحب وأفلت الصور هنا، أو انقر للاختيار',\n            titlePlaceholder: 'أدخل عنوان العقار...',\n            descriptionPlaceholder: 'اكتب وصفاً مفصلاً للعقار...',\n            locationPlaceholder: 'أدخل موقع العقار...',\n            addressPlaceholder: 'أدخل العنوان الكامل...',\n            cityPlaceholder: 'أدخل اسم المدينة...',\n            featurePlaceholder: 'أضف ميزة جديدة...',\n            amenityPlaceholder: 'أضف مرفق جديد...',\n            utilitiesPlaceholder: 'اذكر الخدمات المشمولة...',\n            contactPlaceholder: 'أدخل معلومات التواصل...',\n            stepDescription1: 'أدخل المعلومات الأساسية للعقار',\n            stepDescription2: 'حدد تفاصيل ومواصفات العقار',\n            stepDescription3: 'أضف معلومات الموقع والعنوان',\n            stepDescription4: 'أضف الصور والمعلومات الإضافية',\n            completed: 'مكتمل',\n            current: 'الحالي',\n            pending: 'في الانتظار',\n            imageGallery: 'معرض الصور',\n            mainImage: 'الصورة الرئيسية',\n            additionalImages: 'الصور الإضافية',\n            imageTips: 'نصائح للصور',\n            noFeatures: 'لا توجد مميزات مضافة',\n            noAmenities: 'لا توجد مرافق مضافة',\n            noImages: 'لم يتم رفع صور بعد',\n            setAsMain: 'تعيين كصورة رئيسية',\n            removeImage: 'حذف الصورة',\n            saving: 'جاري الحفظ...',\n            success: 'تم بنجاح',\n            error: 'حدث خطأ'\n        },\n        en: {\n            step: 'Step',\n            of: 'of',\n            next: 'Next',\n            previous: 'Previous',\n            save: 'Save Property',\n            required: 'Required',\n            optional: 'Optional',\n            basicInfo: 'Basic Information',\n            propertyDetails: 'Property Details',\n            locationInfo: 'Location Information',\n            additionalInfo: 'Additional Information',\n            title: 'Property Title',\n            description: 'Property Description',\n            price: 'Price',\n            currency: 'Currency',\n            propertyType: 'Property Type',\n            status: 'Property Status',\n            bedrooms: 'Bedrooms',\n            bathrooms: 'Bathrooms',\n            area: 'Area (sqm)',\n            yearBuilt: 'Year Built',\n            parking: 'Parking Spaces',\n            location: 'Location',\n            address: 'Address',\n            city: 'City',\n            country: 'Country',\n            images: 'Property Images',\n            features: 'Property Features',\n            amenities: 'Amenities & Services',\n            utilities: 'Included Utilities',\n            contactInfo: 'Contact Information',\n            furnished: 'Furnished',\n            petFriendly: 'Pet Friendly',\n            featured: 'Featured Property',\n            active: 'Active',\n            addFeature: 'Add Feature',\n            addAmenity: 'Add Amenity',\n            uploadImages: 'Upload Property Images',\n            dragDropImages: 'Drag and drop images here, or click to select',\n            titlePlaceholder: 'Enter property title...',\n            descriptionPlaceholder: 'Write a detailed property description...',\n            locationPlaceholder: 'Enter property location...',\n            addressPlaceholder: 'Enter full address...',\n            cityPlaceholder: 'Enter city name...',\n            featurePlaceholder: 'Add new feature...',\n            amenityPlaceholder: 'Add new amenity...',\n            utilitiesPlaceholder: 'List included utilities...',\n            contactPlaceholder: 'Enter contact information...',\n            stepDescription1: 'Enter basic property information',\n            stepDescription2: 'Specify property details and specifications',\n            stepDescription3: 'Add location and address information',\n            stepDescription4: 'Add images and additional information',\n            completed: 'Completed',\n            current: 'Current',\n            pending: 'Pending',\n            imageGallery: 'Image Gallery',\n            mainImage: 'Main Image',\n            additionalImages: 'Additional Images',\n            imageTips: 'Image Tips',\n            noFeatures: 'No features added',\n            noAmenities: 'No amenities added',\n            noImages: 'No images uploaded yet',\n            setAsMain: 'Set as Main Image',\n            removeImage: 'Remove Image',\n            saving: 'Saving...',\n            success: 'Success',\n            error: 'Error'\n        }\n    };\n    const t = translations[language];\n    // Bilingual property types\n    const propertyTypes = {\n        ar: {\n            APARTMENT: 'شقة سكنية',\n            VILLA: 'فيلا',\n            TOWNHOUSE: 'تاون هاوس',\n            PENTHOUSE: 'بنتهاوس',\n            STUDIO: 'استوديو',\n            OFFICE: 'مكتب تجاري',\n            SHOP: 'محل تجاري',\n            WAREHOUSE: 'مستودع',\n            LAND: 'قطعة أرض',\n            BUILDING: 'مبنى كامل'\n        },\n        en: {\n            APARTMENT: 'Apartment',\n            VILLA: 'Villa',\n            TOWNHOUSE: 'Townhouse',\n            PENTHOUSE: 'Penthouse',\n            STUDIO: 'Studio',\n            OFFICE: 'Office',\n            SHOP: 'Shop',\n            WAREHOUSE: 'Warehouse',\n            LAND: 'Land',\n            BUILDING: 'Building'\n        }\n    };\n    // Bilingual property statuses\n    const propertyStatuses = {\n        ar: {\n            AVAILABLE: 'متاح للبيع',\n            SOLD: 'تم البيع',\n            RENTED: 'مؤجر',\n            RESERVED: 'محجوز',\n            OFF_MARKET: 'غير متاح'\n        },\n        en: {\n            AVAILABLE: 'Available',\n            SOLD: 'Sold',\n            RENTED: 'Rented',\n            RESERVED: 'Reserved',\n            OFF_MARKET: 'Off Market'\n        }\n    };\n    const stepTitles = [\n        t.basicInfo,\n        t.propertyDetails,\n        t.locationInfo,\n        t.additionalInfo\n    ];\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        await onSave(formData);\n        // Clear draft after successful save\n        localStorage.removeItem('property-draft');\n    };\n    const addFeature = ()=>{\n        if (newFeature.trim()) {\n            setFormData((prev)=>({\n                    ...prev,\n                    features: [\n                        ...prev.features,\n                        newFeature.trim()\n                    ]\n                }));\n            setNewFeature('');\n        }\n    };\n    const addFeatureAr = ()=>{\n        if (newFeatureAr.trim()) {\n            setFormData((prev)=>({\n                    ...prev,\n                    featuresAr: [\n                        ...prev.featuresAr,\n                        newFeatureAr.trim()\n                    ]\n                }));\n            setNewFeatureAr('');\n        }\n    };\n    const addAmenity = ()=>{\n        if (newAmenity.trim()) {\n            setFormData((prev)=>({\n                    ...prev,\n                    amenities: [\n                        ...prev.amenities,\n                        newAmenity.trim()\n                    ]\n                }));\n            setNewAmenity('');\n        }\n    };\n    const addAmenityAr = ()=>{\n        if (newAmenityAr.trim()) {\n            setFormData((prev)=>({\n                    ...prev,\n                    amenitiesAr: [\n                        ...prev.amenitiesAr,\n                        newAmenityAr.trim()\n                    ]\n                }));\n            setNewAmenityAr('');\n        }\n    };\n    const removeFeature = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                features: prev.features.filter((_, i)=>i !== index)\n            }));\n    };\n    const removeFeatureAr = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                featuresAr: prev.featuresAr.filter((_, i)=>i !== index)\n            }));\n    };\n    const removeAmenity = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                amenities: prev.amenities.filter((_, i)=>i !== index)\n            }));\n    };\n    const removeAmenityAr = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                amenitiesAr: prev.amenitiesAr.filter((_, i)=>i !== index)\n            }));\n    };\n    const removeImage = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                images: prev.images.filter((_, i)=>i !== index)\n            }));\n    };\n    const moveImage = (fromIndex, toIndex)=>{\n        setFormData((prev)=>{\n            const newImages = [\n                ...prev.images\n            ];\n            const [movedImage] = newImages.splice(fromIndex, 1);\n            newImages.splice(toIndex, 0, movedImage);\n            return {\n                ...prev,\n                images: newImages\n            };\n        });\n    };\n    const setMainImage = (index)=>{\n        if (index === 0) return; // Already main image\n        moveImage(index, 0);\n    };\n    const nextStep = ()=>{\n        if (currentStep < totalSteps) {\n            setCurrentStep(currentStep + 1);\n        }\n    };\n    const prevStep = ()=>{\n        if (currentStep > 1) {\n            setCurrentStep(currentStep - 1);\n        }\n    };\n    const isStepValid = (step)=>{\n        switch(step){\n            case 1:\n                return formData.title && formData.description && formData.price && formData.type;\n            case 2:\n                return true; // Property details are optional\n            case 3:\n                return formData.location && formData.address && formData.city;\n            case 4:\n                return true; // Additional info is optional\n            default:\n                return false;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(language === 'ar' ? 'rtl' : 'ltr'),\n        dir: language === 'ar' ? 'rtl' : 'ltr',\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-r from-emerald-100/50 to-teal-100/50 dark:from-emerald-900/20 dark:to-teal-900/20 rounded-2xl\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 434,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between \".concat(language === 'ar' ? 'flex-row-reverse' : ''),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4 \".concat(language === 'ar' ? 'flex-row-reverse' : ''),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-14 h-14 bg-gradient-to-br from-emerald-500 via-teal-500 to-cyan-600 rounded-2xl flex items-center justify-center shadow-xl shadow-emerald-200 dark:shadow-emerald-900/50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-black text-lg\",\n                                                                children: currentStep\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 440,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 439,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute -top-1 \".concat(language === 'ar' ? '-right-1' : '-left-1', \" w-6 h-6 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center animate-pulse\"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white text-xs font-bold\",\n                                                                children: \"✦\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 443,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 442,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 438,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1 \".concat(language === 'ar' ? 'text-right' : 'text-left'),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2 \".concat(language === 'ar' ? 'flex-row-reverse' : ''),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xl font-black text-slate-800 dark:text-white\",\n                                                                    children: [\n                                                                        t.step,\n                                                                        \" \",\n                                                                        currentStep,\n                                                                        \" \",\n                                                                        t.of,\n                                                                        \" \",\n                                                                        totalSteps\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 448,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"px-3 py-1 bg-emerald-100 dark:bg-emerald-900/30 rounded-full\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-bold text-emerald-700 dark:text-emerald-300\",\n                                                                        children: t.current\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 452,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 451,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 447,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-lg font-bold text-slate-700 dark:text-slate-300\",\n                                                            children: stepTitles[currentStep - 1]\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 455,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-slate-600 dark:text-slate-400\",\n                                                            children: [\n                                                                currentStep === 1 && t.stepDescription1,\n                                                                currentStep === 2 && t.stepDescription2,\n                                                                currentStep === 3 && t.stepDescription3,\n                                                                currentStep === 4 && t.stepDescription4\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 458,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"\".concat(language === 'ar' ? 'text-right' : 'text-left'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-4xl font-black bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent\",\n                                                    children: [\n                                                        Math.round(currentStep / totalSteps * 100),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 468,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-bold text-emerald-600 dark:text-emerald-400\",\n                                                    children: t.completed\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 471,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 467,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 435,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                        lineNumber: 433,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full bg-slate-200 dark:bg-slate-700 rounded-full h-4 shadow-inner\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-r from-emerald-500 via-teal-500 to-cyan-500 h-4 rounded-full transition-all duration-1000 ease-out shadow-lg relative overflow-hidden\",\n                                style: {\n                                    width: \"\".concat(currentStep / totalSteps * 100, \"%\")\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-r from-emerald-400/50 to-teal-400/50 animate-pulse delay-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 482,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                            lineNumber: 481,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                        lineNumber: 480,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-4 gap-4\",\n                        children: stepTitles.map((title, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 rounded-2xl flex items-center justify-center text-lg font-black transition-all duration-500 shadow-xl \".concat(index + 1 <= currentStep ? 'bg-gradient-to-br from-emerald-500 via-teal-500 to-cyan-600 text-white shadow-emerald-200 dark:shadow-emerald-900/50 scale-110' : index + 1 === currentStep + 1 ? 'bg-gradient-to-br from-slate-300 to-slate-400 text-slate-700 shadow-slate-200 dark:shadow-slate-800 scale-105' : 'bg-slate-200 dark:bg-slate-700 text-slate-500 dark:text-slate-400'),\n                                        children: index + 1 < currentStep ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-7 w-7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 506,\n                                            columnNumber: 19\n                                        }, this) : index + 1 === currentStep ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 bg-white rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 508,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-black\",\n                                            children: index + 1\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 510,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 496,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-bold leading-tight \".concat(index + 1 <= currentStep ? 'text-emerald-700 dark:text-emerald-300' : index + 1 === currentStep + 1 ? 'text-slate-600 dark:text-slate-400' : 'text-slate-500 dark:text-slate-500'),\n                                                children: title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 514,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs mt-1 \".concat(index + 1 <= currentStep ? 'text-emerald-600 dark:text-emerald-400' : 'text-slate-400 dark:text-slate-500'),\n                                                children: index + 1 <= currentStep ? t.completed : t.pending\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 523,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 495,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                        lineNumber: 493,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                lineNumber: 431,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"space-y-8\",\n                children: [\n                    currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                        className: \"shadow-2xl border-0 bg-white/95 dark:bg-slate-800/95 backdrop-blur-md overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                className: \"pb-8 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-blue-900/30 dark:via-indigo-900/30 dark:to-purple-900/30 border-b border-blue-100 dark:border-blue-800/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                    className: \"text-2xl font-bold text-slate-800 dark:text-white flex items-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-14 h-14 bg-gradient-to-br from-blue-500 via-indigo-500 to-purple-600 text-white rounded-2xl flex items-center justify-center text-lg font-bold shadow-xl shadow-blue-200 dark:shadow-blue-900/50\",\n                                            children: \"1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 542,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl\",\n                                                    children: t.basicInfo\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 546,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-normal text-slate-600 dark:text-slate-400 mt-1\",\n                                                    children: language === 'ar' ? 'أدخل المعلومات الأساسية للعقار' : 'Enter basic property information'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 545,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                    lineNumber: 541,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 540,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                className: \"space-y-8 p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"title\",\n                                                        className: \"text-base font-bold text-slate-800 dark:text-slate-200 flex items-center gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-6 h-6 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-600 dark:text-blue-400 text-sm font-bold\",\n                                                                    children: \"1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 559,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 558,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            t.title,\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500 text-lg\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 562,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 557,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"title\",\n                                                        value: formData.title,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    title: e.target.value\n                                                                })),\n                                                        placeholder: t.titlePlaceholder,\n                                                        required: true,\n                                                        dir: language === 'ar' ? 'rtl' : 'ltr',\n                                                        className: \"h-14 border-2 border-slate-200 dark:border-slate-600 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-300 rounded-xl text-lg bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm shadow-sm hover:shadow-md focus:shadow-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 564,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 17\n                                            }, this),\n                                            language === 'en' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"titleEn\",\n                                                        className: \"text-base font-semibold text-slate-700 dark:text-slate-300 flex items-center gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-6 h-6 bg-slate-100 dark:bg-slate-700 rounded-lg flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-slate-600 dark:text-slate-400 text-sm\",\n                                                                    children: \"EN\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 578,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 577,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            t.titleEn,\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-slate-400 text-sm\",\n                                                                children: [\n                                                                    \"(\",\n                                                                    t.optional,\n                                                                    \")\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 581,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 576,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"titleEn\",\n                                                        value: formData.titleAr,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    titleAr: e.target.value\n                                                                })),\n                                                        placeholder: t.titleEnPlaceholder,\n                                                        dir: \"ltr\",\n                                                        className: \"h-12 border-2 border-slate-200 dark:border-slate-600 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-300 rounded-xl bg-white/30 dark:bg-slate-800/30\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 583,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 575,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 555,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"description\",\n                                                    className: \"text-base font-bold text-slate-800 dark:text-slate-200 flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-6 h-6 bg-emerald-100 dark:bg-emerald-900/30 rounded-lg flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-emerald-600 dark:text-emerald-400 text-sm font-bold\",\n                                                                children: \"2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 600,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 599,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        t.description,\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-500 text-lg\",\n                                                            children: \"*\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 603,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 598,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                    id: \"description\",\n                                                    value: formData.description,\n                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                description: e.target.value\n                                                            })),\n                                                    placeholder: t.descriptionPlaceholder,\n                                                    required: true,\n                                                    rows: 6,\n                                                    dir: \"rtl\",\n                                                    className: \"border-2 border-slate-200 dark:border-slate-600 focus:border-emerald-500 dark:focus:border-emerald-400 transition-all duration-300 resize-none rounded-xl text-lg bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm shadow-sm hover:shadow-md focus:shadow-lg\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 605,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 597,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 596,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"price\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2\",\n                                                        children: [\n                                                            t.price,\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500 text-lg\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 623,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 621,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"price\",\n                                                        type: \"number\",\n                                                        value: formData.price,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    price: e.target.value\n                                                                })),\n                                                        placeholder: \"0\",\n                                                        required: true,\n                                                        className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-200 rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 625,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 620,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"currency\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.currency\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 636,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                        value: formData.currency,\n                                                        onValueChange: (value)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    currency: value\n                                                                })),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                                className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 rounded-lg\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 641,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 640,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                        value: \"AED\",\n                                                                        children: \"AED - درهم إماراتي\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 644,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                        value: \"USD\",\n                                                                        children: \"USD - دولار أمريكي\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 645,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                        value: \"EUR\",\n                                                                        children: \"EUR - يورو\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 646,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                        value: \"GBP\",\n                                                                        children: \"GBP - جنيه إسترليني\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 647,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 643,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 639,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 635,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"type\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2\",\n                                                        children: [\n                                                            t.propertyType,\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500 text-lg\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 654,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 652,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                        value: formData.type,\n                                                        onValueChange: (value)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    type: value\n                                                                })),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                                className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 rounded-lg\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 658,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 657,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                                children: Object.entries(propertyTypes[language]).map((param)=>{\n                                                                    let [key, value] = param;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                        value: key,\n                                                                        children: value\n                                                                    }, key, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 662,\n                                                                        columnNumber: 25\n                                                                    }, this);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 660,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 656,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 651,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 619,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"status\",\n                                                    className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                    children: t.status\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 672,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                    value: formData.status,\n                                                    onValueChange: (value)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                status: value\n                                                            })),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                            className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 rounded-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 677,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 676,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                            children: Object.entries(propertyStatuses[language]).map((param)=>{\n                                                                let [key, value] = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                    value: key,\n                                                                    children: value\n                                                                }, key, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 681,\n                                                                    columnNumber: 25\n                                                                }, this);\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 679,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 675,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 671,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 670,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 553,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                        lineNumber: 539,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                        className: \"shadow-xl border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                className: \"pb-6 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-t-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg\",\n                                            children: \"2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 696,\n                                            columnNumber: 17\n                                        }, this),\n                                        t.propertyDetails\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                    lineNumber: 695,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 694,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                className: \"space-y-8 p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"bedrooms\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.bedrooms\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 706,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"bedrooms\",\n                                                        type: \"number\",\n                                                        value: formData.bedrooms,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    bedrooms: e.target.value\n                                                                })),\n                                                        placeholder: \"0\",\n                                                        min: \"0\",\n                                                        className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 709,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 705,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"bathrooms\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.bathrooms\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 720,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"bathrooms\",\n                                                        type: \"number\",\n                                                        value: formData.bathrooms,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    bathrooms: e.target.value\n                                                                })),\n                                                        placeholder: \"0\",\n                                                        min: \"0\",\n                                                        className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 723,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 719,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"area\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.area\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 734,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"area\",\n                                                        type: \"number\",\n                                                        value: formData.area,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    area: e.target.value\n                                                                })),\n                                                        placeholder: \"0\",\n                                                        min: \"0\",\n                                                        className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 737,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 733,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"yearBuilt\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.yearBuilt\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 748,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"yearBuilt\",\n                                                        type: \"number\",\n                                                        value: formData.yearBuilt,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    yearBuilt: e.target.value\n                                                                })),\n                                                        placeholder: \"2024\",\n                                                        min: \"1900\",\n                                                        max: \"2030\",\n                                                        className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 751,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 747,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"parking\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.parking\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 763,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"parking\",\n                                                        type: \"number\",\n                                                        value: formData.parking,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    parking: e.target.value\n                                                                })),\n                                                        placeholder: \"0\",\n                                                        min: \"0\",\n                                                        className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 766,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 762,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 704,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_10__.Separator, {\n                                        className: \"my-8\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 778,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                    children: t.features\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 783,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                            value: newFeature,\n                                                            onChange: (e)=>setNewFeature(e.target.value),\n                                                            placeholder: t.featurePlaceholder,\n                                                            onKeyPress: (e)=>e.key === 'Enter' && (e.preventDefault(), addFeature()),\n                                                            className: \"h-10 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 787,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            type: \"button\",\n                                                            onClick: addFeature,\n                                                            size: \"sm\",\n                                                            className: \"bg-green-600 hover:bg-green-700 px-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 795,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 794,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 786,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-2 min-h-[40px] p-3 border-2 border-dashed border-gray-200 dark:border-gray-600 rounded-lg\",\n                                                    children: [\n                                                        formData.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                variant: \"secondary\",\n                                                                className: \"flex items-center gap-1 px-3 py-1 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200\",\n                                                                children: [\n                                                                    feature,\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-3 w-3 cursor-pointer hover:text-red-600\",\n                                                                        onClick: ()=>removeFeature(index)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 802,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 800,\n                                                                columnNumber: 23\n                                                            }, this)),\n                                                        formData.features.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: t.noFeatures\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 809,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 798,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 782,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 781,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                    children: t.amenities\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 818,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                            value: newAmenity,\n                                                            onChange: (e)=>setNewAmenity(e.target.value),\n                                                            placeholder: t.amenityPlaceholder,\n                                                            onKeyPress: (e)=>e.key === 'Enter' && (e.preventDefault(), addAmenity()),\n                                                            className: \"h-10 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 822,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            type: \"button\",\n                                                            onClick: addAmenity,\n                                                            size: \"sm\",\n                                                            className: \"bg-green-600 hover:bg-green-700 px-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 830,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 829,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 821,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-2 min-h-[40px] p-3 border-2 border-dashed border-gray-200 dark:border-gray-600 rounded-lg\",\n                                                    children: [\n                                                        formData.amenities.map((amenity, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                variant: \"secondary\",\n                                                                className: \"flex items-center gap-1 px-3 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200\",\n                                                                children: [\n                                                                    amenity,\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-3 w-3 cursor-pointer hover:text-red-600\",\n                                                                        onClick: ()=>removeAmenity(index)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 837,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 835,\n                                                                columnNumber: 23\n                                                            }, this)),\n                                                        formData.amenities.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: t.noAmenities\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 844,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 833,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 817,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 816,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 702,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                        lineNumber: 693,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                        className: \"shadow-xl border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                className: \"pb-6 bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-t-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg\",\n                                            children: \"3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 858,\n                                            columnNumber: 17\n                                        }, this),\n                                        t.locationInfo\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                    lineNumber: 857,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 856,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                className: \"space-y-8 p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"location\",\n                                                    className: \"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2\",\n                                                    children: [\n                                                        t.location,\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-500 text-lg\",\n                                                            children: \"*\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 870,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 868,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                    id: \"location\",\n                                                    value: formData.location,\n                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                location: e.target.value\n                                                            })),\n                                                    placeholder: t.locationPlaceholder,\n                                                    required: true,\n                                                    className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 872,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 867,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 866,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"address\",\n                                                    className: \"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2\",\n                                                    children: [\n                                                        t.address,\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-500 text-lg\",\n                                                            children: \"*\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 889,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 887,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                    id: \"address\",\n                                                    value: formData.address,\n                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                address: e.target.value\n                                                            })),\n                                                    placeholder: t.addressPlaceholder,\n                                                    required: true,\n                                                    className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 891,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 886,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 885,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"city\",\n                                                                className: \"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2\",\n                                                                children: [\n                                                                    t.city,\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-500 text-lg\",\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 909,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 907,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                id: \"city\",\n                                                                value: formData.city,\n                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            city: e.target.value\n                                                                        })),\n                                                                placeholder: t.cityPlaceholder,\n                                                                required: true,\n                                                                className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 911,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 906,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"country\",\n                                                                className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                                children: t.country\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 921,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                                value: formData.country,\n                                                                onValueChange: (value)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            country: value\n                                                                        })),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                                        className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 rounded-lg\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {}, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                            lineNumber: 926,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 925,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"UAE\",\n                                                                                children: \"UAE - الإمارات العربية المتحدة\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 929,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Saudi Arabia\",\n                                                                                children: \"Saudi Arabia - المملكة العربية السعودية\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 930,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Qatar\",\n                                                                                children: \"Qatar - قطر\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 931,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Kuwait\",\n                                                                                children: \"Kuwait - الكويت\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 932,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Bahrain\",\n                                                                                children: \"Bahrain - البحرين\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 933,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Oman\",\n                                                                                children: \"Oman - عُمان\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 934,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 928,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 924,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 920,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 905,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"cityAr\",\n                                                                className: \"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2\",\n                                                                children: [\n                                                                    t.cityAr,\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-400 text-sm\",\n                                                                        children: [\n                                                                            \"(\",\n                                                                            t.optional,\n                                                                            \")\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 943,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 941,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                id: \"cityAr\",\n                                                                value: formData.cityAr,\n                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            cityAr: e.target.value\n                                                                        })),\n                                                                placeholder: t.cityArPlaceholder,\n                                                                dir: \"rtl\",\n                                                                className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 945,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 940,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"countryAr\",\n                                                                className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                                children: t.countryAr\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 955,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                id: \"countryAr\",\n                                                                value: formData.countryAr,\n                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            countryAr: e.target.value\n                                                                        })),\n                                                                placeholder: \"أدخل اسم البلد بالعربية\",\n                                                                dir: \"rtl\",\n                                                                className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 958,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 954,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 939,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 904,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 864,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                        lineNumber: 855,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                        className: \"shadow-xl border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                className: \"pb-6 bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-900/20 dark:to-amber-900/20 rounded-t-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg\",\n                                            children: \"4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 978,\n                                            columnNumber: 17\n                                        }, this),\n                                        t.additionalInfo\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                    lineNumber: 977,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 976,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                className: \"space-y-8 p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                className: \"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2\",\n                                                children: [\n                                                    t.images,\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full\",\n                                                        children: [\n                                                            formData.images.length,\n                                                            \" \",\n                                                            language === 'ar' ? 'صور' : 'images'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 989,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 987,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative border-2 border-dashed border-orange-300 dark:border-orange-600 rounded-xl p-8 hover:border-orange-400 dark:hover:border-orange-500 transition-all duration-300 bg-gradient-to-br from-orange-50/50 to-amber-50/50 dark:from-orange-900/20 dark:to-amber-900/20 hover:shadow-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"mx-auto h-12 w-12 text-orange-400 mb-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 997,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\n                                                                children: t.uploadImages\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 998,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600 dark:text-gray-400 mb-6\",\n                                                                children: t.dragDropImages\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 1001,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 996,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UploadDropzone, {\n                                                        endpoint: \"propertyImageUploader\",\n                                                        onClientUploadComplete: (res)=>{\n                                                            if (res) {\n                                                                const newImages = res.map((file)=>file.url);\n                                                                setFormData((prev)=>({\n                                                                        ...prev,\n                                                                        images: [\n                                                                            ...prev.images,\n                                                                            ...newImages\n                                                                        ]\n                                                                    }));\n                                                            }\n                                                        },\n                                                        onUploadError: (error)=>{\n                                                            console.error('Upload error:', error);\n                                                        },\n                                                        className: \"ut-button:bg-gradient-to-r ut-button:from-orange-600 ut-button:to-orange-700 ut-button:hover:from-orange-700 ut-button:hover:to-orange-800 ut-button:shadow-lg ut-button:hover:shadow-xl ut-button:transition-all ut-button:duration-200\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1005,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 995,\n                                                columnNumber: 17\n                                            }, this),\n                                            formData.images.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                                children: language === 'ar' ? 'معرض الصور' : 'Image Gallery'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 1027,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: [\n                                                                    formData.images.length,\n                                                                    \" \",\n                                                                    language === 'ar' ? 'من الصور' : 'images'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 1030,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1026,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    formData.images.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"aspect-video bg-gray-100 dark:bg-gray-800 rounded-xl overflow-hidden shadow-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: formData.images[0],\n                                                                    alt: \"Main property image\",\n                                                                    className: \"w-full h-full object-cover\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 1039,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute top-4 left-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                        className: \"bg-orange-600 text-white shadow-lg\",\n                                                                        children: language === 'ar' ? 'الصورة الرئيسية' : 'Main Image'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 1045,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 1044,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                    type: \"button\",\n                                                                    variant: \"destructive\",\n                                                                    size: \"sm\",\n                                                                    className: \"absolute top-4 right-4 h-8 w-8 p-0 rounded-full shadow-lg hover:shadow-xl transition-all duration-200\",\n                                                                    onClick: ()=>removeImage(0),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 1056,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 1049,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 1038,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1037,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    formData.images.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                                                children: language === 'ar' ? 'الصور الإضافية' : 'Additional Images'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 1065,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-3 overflow-x-auto pb-2 scrollbar-thin scrollbar-thumb-orange-300 scrollbar-track-gray-100 dark:scrollbar-thumb-orange-600 dark:scrollbar-track-gray-800\",\n                                                                children: formData.images.slice(1).map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative flex-shrink-0 group\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-all duration-200 border-2 border-transparent hover:border-orange-300 cursor-pointer\",\n                                                                                onClick: ()=>setMainImage(index + 1),\n                                                                                title: language === 'ar' ? 'انقر لجعلها الصورة الرئيسية' : 'Click to set as main image',\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                        src: image,\n                                                                                        alt: \"Property image \".concat(index + 2),\n                                                                                        className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-200\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                        lineNumber: 1076,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-200 flex items-center justify-center\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-orange-600 text-white text-xs px-2 py-1 rounded-full font-medium\",\n                                                                                            children: language === 'ar' ? 'رئيسية' : 'Main'\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                            lineNumber: 1083,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                        lineNumber: 1082,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 1071,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                type: \"button\",\n                                                                                variant: \"destructive\",\n                                                                                size: \"sm\",\n                                                                                className: \"absolute -top-2 -right-2 h-6 w-6 p-0 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200 shadow-lg z-10\",\n                                                                                onClick: (e)=>{\n                                                                                    e.stopPropagation();\n                                                                                    removeImage(index + 1);\n                                                                                },\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                    lineNumber: 1098,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 1088,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute bottom-1 left-1 bg-black/50 text-white text-xs px-1 py-0.5 rounded\",\n                                                                                children: index + 2\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 1100,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, index + 1, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 1070,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 1068,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1064,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start gap-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-shrink-0\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-6 h-6 bg-orange-100 dark:bg-orange-800 rounded-full flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-orange-600 dark:text-orange-400 text-xs font-bold\",\n                                                                            children: \"\\uD83D\\uDCA1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                            lineNumber: 1114,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 1113,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 1112,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-orange-800 dark:text-orange-200\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium mb-1\",\n                                                                            children: language === 'ar' ? 'نصائح للصور:' : 'Image Tips:'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                            lineNumber: 1118,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                            className: \"space-y-1 text-xs\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: [\n                                                                                        \"• \",\n                                                                                        language === 'ar' ? 'الصورة الأولى ستكون الصورة الرئيسية' : 'First image will be the main image'\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                    lineNumber: 1122,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: [\n                                                                                        \"• \",\n                                                                                        language === 'ar' ? 'انقر على أي صورة لجعلها الصورة الرئيسية' : 'Click on any image to set it as main image'\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                    lineNumber: 1123,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: [\n                                                                                        \"• \",\n                                                                                        language === 'ar' ? 'استخدم صور عالية الجودة للحصول على أفضل النتائج' : 'Use high-quality images for best results'\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                    lineNumber: 1124,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: [\n                                                                                        \"• \",\n                                                                                        language === 'ar' ? 'يمكنك رفع عدة صور في نفس الوقت' : 'You can upload multiple images at once'\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                    lineNumber: 1125,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: [\n                                                                                        \"• \",\n                                                                                        language === 'ar' ? 'الحد الأقصى لحجم الصورة: 4 ميجابايت' : 'Maximum image size: 4MB'\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                    lineNumber: 1126,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                            lineNumber: 1121,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 1117,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 1111,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1110,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1025,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 986,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_10__.Separator, {\n                                        className: \"my-8\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 1135,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"utilities\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.utilities\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1140,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                        id: \"utilities\",\n                                                        value: formData.utilities,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    utilities: e.target.value\n                                                                })),\n                                                        placeholder: t.utilitiesPlaceholder,\n                                                        rows: 3,\n                                                        className: \"border-2 border-gray-200 dark:border-gray-600 focus:border-orange-500 dark:focus:border-orange-400 transition-all duration-200 resize-none rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1143,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1139,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"utilitiesAr\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.utilitiesAr\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1153,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                        id: \"utilitiesAr\",\n                                                        value: formData.utilitiesAr,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    utilitiesAr: e.target.value\n                                                                })),\n                                                        placeholder: t.utilitiesArPlaceholder,\n                                                        dir: \"rtl\",\n                                                        rows: 3,\n                                                        className: \"border-2 border-gray-200 dark:border-gray-600 focus:border-orange-500 dark:focus:border-orange-400 transition-all duration-200 resize-none rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1156,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1152,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 1138,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"contactInfo\",\n                                                className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                children: t.contactInfo\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1169,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                id: \"contactInfo\",\n                                                value: formData.contactInfo,\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            contactInfo: e.target.value\n                                                        })),\n                                                placeholder: t.contactPlaceholder,\n                                                rows: 3,\n                                                className: \"border-2 border-gray-200 dark:border-gray-600 focus:border-orange-500 dark:focus:border-orange-400 transition-all duration-200 resize-none rounded-lg\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1172,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 1168,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_10__.Separator, {\n                                        className: \"my-8\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 1182,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-4 gap-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-orange-400 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_7__.Switch, {\n                                                        id: \"furnished\",\n                                                        checked: formData.furnished,\n                                                        onCheckedChange: (checked)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    furnished: checked\n                                                                })),\n                                                        className: \"data-[state=checked]:bg-orange-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1187,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"furnished\",\n                                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer\",\n                                                        children: t.furnished\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1193,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1186,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-orange-400 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_7__.Switch, {\n                                                        id: \"petFriendly\",\n                                                        checked: formData.petFriendly,\n                                                        onCheckedChange: (checked)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    petFriendly: checked\n                                                                })),\n                                                        className: \"data-[state=checked]:bg-orange-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1198,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"petFriendly\",\n                                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer\",\n                                                        children: t.petFriendly\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1204,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1197,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-orange-400 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_7__.Switch, {\n                                                        id: \"isFeatured\",\n                                                        checked: formData.isFeatured,\n                                                        onCheckedChange: (checked)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    isFeatured: checked\n                                                                })),\n                                                        className: \"data-[state=checked]:bg-orange-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1209,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"isFeatured\",\n                                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer\",\n                                                        children: t.featured\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1215,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1208,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-orange-400 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_7__.Switch, {\n                                                        id: \"isActive\",\n                                                        checked: formData.isActive,\n                                                        onCheckedChange: (checked)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    isActive: checked\n                                                                })),\n                                                        className: \"data-[state=checked]:bg-orange-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1220,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"isActive\",\n                                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer\",\n                                                        children: t.active\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1226,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1219,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 1185,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 984,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                        lineNumber: 975,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center pt-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                type: \"button\",\n                                variant: \"outline\",\n                                onClick: prevStep,\n                                disabled: currentStep === 1,\n                                className: \"flex items-center gap-2 px-6 py-3 h-12 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 1244,\n                                        columnNumber: 13\n                                    }, this),\n                                    t.previous\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 1237,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-4\",\n                                children: currentStep < totalSteps ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"button\",\n                                    onClick: nextStep,\n                                    disabled: !isStepValid(currentStep),\n                                    className: \"flex items-center gap-2 px-8 py-3 h-12 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl\",\n                                    children: [\n                                        t.next,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 1257,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                    lineNumber: 1250,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"submit\",\n                                    disabled: loading || !isStepValid(currentStep),\n                                    className: \"flex items-center gap-2 px-8 py-3 h-12 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl\",\n                                    children: [\n                                        loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 1266,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 1268,\n                                            columnNumber: 19\n                                        }, this),\n                                        t.save\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                    lineNumber: 1260,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 1248,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                        lineNumber: 1236,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                lineNumber: 536,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n        lineNumber: 429,\n        columnNumber: 5\n    }, this);\n}\n_s(PropertyFormSteps, \"klKuECYLIR0ytoNVuKjv1XNXBHQ=\", false, function() {\n    return [\n        _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_11__.useSimpleLanguage\n    ];\n});\n_c = PropertyFormSteps;\nvar _c;\n$RefreshReg$(_c, \"PropertyFormSteps\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/properties/create/property-form-steps.tsx\n"));

/***/ })

});