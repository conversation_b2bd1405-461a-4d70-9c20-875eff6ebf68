"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/properties/create/page",{

/***/ "(app-pages-browser)/./components/ThemeSwitcher.tsx":
/*!**************************************!*\
  !*** ./components/ThemeSwitcher.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CompactThemeSwitcher: () => (/* binding */ CompactThemeSwitcher),\n/* harmony export */   ThemeSwitcher: () => (/* binding */ ThemeSwitcher)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Moon,Sun!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Moon,Sun!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _hooks_useTheme__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useTheme */ \"(app-pages-browser)/./hooks/useTheme.tsx\");\n/* harmony import */ var _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useSimpleLanguage */ \"(app-pages-browser)/./hooks/useSimpleLanguage.tsx\");\n/* __next_internal_client_entry_do_not_use__ ThemeSwitcher,CompactThemeSwitcher auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n/**\n * Dark mode indicator component (dark mode only)\n * Shows current dark mode status without toggle option\n */ function ThemeSwitcher() {\n    _s();\n    const { language, isArabic } = (0,_hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_3__.useSimpleLanguage)();\n    const themeText = {\n        ar: {\n            dark: 'الوضع المظلم',\n            active: 'مُفعل'\n        },\n        en: {\n            dark: 'Dark Mode',\n            active: 'Active'\n        }\n    };\n    const t = themeText[language];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\\n        flex items-center gap-2 px-4 py-2 h-10\\n        bg-slate-800/90 backdrop-blur-md\\n        border-2 border-slate-700\\n        shadow-lg\\n        text-slate-300\\n        rounded-xl font-medium\\n        \".concat(isArabic ? 'flex-row-reverse' : 'flex-row', \"\\n      \"),\n        dir: isArabic ? 'rtl' : 'ltr',\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4 text-amber-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\ThemeSwitcher.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"font-bold\",\n                    children: t.dark\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\ThemeSwitcher.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\ThemeSwitcher.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-xs text-green-400\",\n                    children: t.active\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\ThemeSwitcher.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\ThemeSwitcher.tsx\",\n            lineNumber: 41,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\ThemeSwitcher.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n_s(ThemeSwitcher, \"2rjfvwuffGHCSE4Q4plIAiVYgTc=\", false, function() {\n    return [\n        _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_3__.useSimpleLanguage\n    ];\n});\n_c = ThemeSwitcher;\n/**\n * Compact theme switcher for smaller spaces\n */ function CompactThemeSwitcher() {\n    _s1();\n    const { toggleTheme, isDark } = (0,_hooks_useTheme__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n        variant: \"ghost\",\n        size: \"sm\",\n        onClick: toggleTheme,\n        className: \" w-12 h-12 p-0 rounded-xl bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm hover:bg-white/90 dark:hover:bg-slate-800/90 border border-slate-200 dark:border-slate-700 hover:border-amber-300 dark:hover:border-amber-600 transition-all duration-300 \",\n        children: isDark ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"h-5 w-5 text-amber-600 dark:text-amber-400\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\ThemeSwitcher.tsx\",\n            lineNumber: 76,\n            columnNumber: 9\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-5 w-5 text-amber-600 dark:text-amber-400\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\ThemeSwitcher.tsx\",\n            lineNumber: 78,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\ThemeSwitcher.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n_s1(CompactThemeSwitcher, \"f0eus6GSIxA6fJ7xs6vYFLn7J/c=\", false, function() {\n    return [\n        _hooks_useTheme__WEBPACK_IMPORTED_MODULE_2__.useTheme\n    ];\n});\n_c1 = CompactThemeSwitcher;\nvar _c, _c1;\n$RefreshReg$(_c, \"ThemeSwitcher\");\n$RefreshReg$(_c1, \"CompactThemeSwitcher\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ThemeSwitcher.tsx\n"));

/***/ })

});