{"version": 3, "file": "ScheduleInterval.js", "names": ["internal", "IntervalTypeId", "make", "empty", "lessThan", "min", "max", "isEmpty", "isNonEmpty", "intersect", "size", "union", "after", "before"], "sources": ["../../src/ScheduleInterval.ts"], "sourcesContent": [null], "mappings": "AAIA,OAAO,KAAKA,QAAQ,MAAM,iCAAiC;AAG3D;;;;AAIA,OAAO,MAAMC,cAAc,GAAkBD,QAAQ,CAACC,cAAc;AAqBpE;;;;;;;;AAQA,OAAO,MAAMC,IAAI,GAAyDF,QAAQ,CAACE,IAAI;AAEvF;;;;;;AAMA,OAAO,MAAMC,KAAK,GAAaH,QAAQ,CAACG,KAAK;AAE7C;;;;;;;AAOA,OAAO,MAAMC,QAAQ,GAiBjBJ,QAAQ,CAACI,QAAQ;AAErB;;;;;;AAMA,OAAO,MAAMC,GAAG,GAeZL,QAAQ,CAACK,GAAG;AAEhB;;;;;;AAMA,OAAO,MAAMC,GAAG,GAeZN,QAAQ,CAACM,GAAG;AAEhB;;;;;;AAMA,OAAO,MAAMC,OAAO,GAAgCP,QAAQ,CAACO,OAAO;AAEpE;;;;;;AAMA,OAAO,MAAMC,UAAU,GAAgCR,QAAQ,CAACQ,UAAU;AAE1E;;;;;;;AAOA,OAAO,MAAMC,SAAS,GAiBlBT,QAAQ,CAACS,SAAS;AAEtB;;;;;;;AAOA,OAAO,MAAMC,IAAI,GAA0CV,QAAQ,CAACU,IAAI;AAExE;;;;;;;;AAQA,OAAO,MAAMC,KAAK,GAmBdX,QAAQ,CAACW,KAAK;AAElB;;;;;;;AAOA,OAAO,MAAMC,KAAK,GAA4CZ,QAAQ,CAACY,KAAK;AAE5E;;;;;;;AAOA,OAAO,MAAMC,MAAM,GAA0Cb,QAAQ,CAACa,MAAM", "ignoreList": []}