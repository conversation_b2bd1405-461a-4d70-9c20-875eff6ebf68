import { FetchHttpClient, Headers } from '@effect/platform';
import * as FiberRef from 'effect/FiberRef';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';
import { configProvider } from './config.js';
import { withLogFormat, withMinimalLogLevel } from './logger.js';

const makeRuntime = (fetch, config)=>{
    const fetchHttpClient = Layer.provideMerge(FetchHttpClient.layer, Layer.succeed(FetchHttpClient.Fetch, fetch));
    const withRedactedHeaders = Layer.effectDiscard(FiberRef.update(Headers.currentRedactedNames, (_)=>_.concat([
            "x-uploadthing-api-key"
        ])));
    const layer = Layer.provide(Layer.mergeAll(withLogFormat, withMinimalLogLevel, fetchHttpClient, withRedactedHeaders), Layer.setConfigProvider(configProvider(config)));
    return ManagedRuntime.make(layer);
};

export { makeRuntime };
