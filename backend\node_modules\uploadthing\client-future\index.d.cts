import { UploadFilesOptions, FailedFile, UploadedFile, AnyFile } from '../dist/_internal/client-future.cjs';
export * from '../dist/_internal/client-future.cjs';
import { FileRouter, GenerateUploaderOptions, EndpointArg, RouteRegistry } from '../types/index.js';
export { UploadAbortedError, UploadPausedError, generateClientDropzoneAccept, generateMimeTypes, generatePermittedFileTypes } from '@uploadthing/shared';

declare const version: string;

/**
 * Generate a typed uploader for a given FileRouter
 * @public
 * @remarks This API is not covered by semver
 */
declare const future_genUploader: <TRouter extends FileRouter>(initOpts?: GenerateUploaderOptions) => {
    uploadFiles: <TEndpoint extends keyof TRouter>(slug: EndpointArg<TRouter, TEndpoint>, opts: Omit<UploadFilesOptions<TRouter[TEndpoint]>, keyof GenerateUploaderOptions>) => Promise<(FailedFile<TRouter[TEndpoint]> | UploadedFile<TRouter[TEndpoint]>)[]>;
    createUpload: <TEndpoint extends keyof TRouter>(slug: EndpointArg<TRouter, TEndpoint>, options: Omit<UploadFilesOptions<TRouter[TEndpoint]>, keyof GenerateUploaderOptions>) => Promise<{
        pauseUpload: (file?: File) => void;
        abortUpload: (file?: File) => never;
        resumeUpload: (file?: File) => void;
        done: <T extends AnyFile<TRouter[TEndpoint]> | void = void>(file?: T) => Promise<T extends AnyFile<TRouter[TEndpoint]> ? UploadedFile<TRouter[TEndpoint]> | FailedFile<TRouter[TEndpoint]> : (UploadedFile<TRouter[TEndpoint]> | FailedFile<TRouter[TEndpoint]>)[]>;
    }>;
    /**
     * Identity object that can be used instead of raw strings
     * that allows "Go to definition" in your IDE to bring you
     * to the backend definition of a route.
     */
    routeRegistry: RouteRegistry<TRouter>;
};

export { future_genUploader, version };
