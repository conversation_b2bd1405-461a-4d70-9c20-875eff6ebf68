(()=>{var e={};e.id=1658,e.ids=[1658],e.modules={1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5486:e=>{"use strict";e.exports=require("bcrypt")},7066:e=>{"use strict";e.exports=require("node:tty")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21353:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(37413),n=s(29639);function a(){return(0,r.jsx)(n.ChatList,{})}},21820:e=>{"use strict";e.exports=require("os")},27900:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29217:(e,t,s)=>{"use strict";s.d(t,{ChatList:()=>f});var r=s(60687),n=s(43210),a=s(68988),i=s(99270),o=s(70373),l=s(96241),c=s(59556),d=s(24934),u=s(41862),p=s(27900),h=s(70333);function m({clientId:e}){let{client:t,messages:s,isLoading:i,error:m,refreshMessages:f,addMessage:x}=function(e){let[t,s]=(0,n.useState)(null),[r,a]=(0,n.useState)([]),[i,o]=(0,n.useState)(!0),[l,d]=(0,n.useState)(null),u=(0,n.useCallback)(async()=>{if(!e){o(!1);return}o(!0),d(null);try{console.log(`Fetching messages for client ${e}`);let t=await c.A.get(`/clients/${e}/messages`);console.log("API Response:",t);let r=t.data||t;if(r&&r.id){if(s({id:r.id,name:r.name||"Unknown",phone:r.phone||"",lastActive:r.lastActive||new Date().toISOString(),lastMessage:r.lastMessage||"",createdAt:r.createdAt||new Date().toISOString(),updatedAt:r.updatedAt||new Date().toISOString(),type:r.type||"client"}),Array.isArray(r.messages)){let e=r.messages.sort((e,t)=>{let s=new Date(e.createdAt||0).getTime(),r=new Date(t.createdAt||0).getTime();return s-r}).map(e=>({id:e.id||`temp-${Date.now()}-${Math.random()}`,content:e.text||e.content||"",sender:e.isBot?"agent":"user",timestamp:new Date(e.createdAt||Date.now()).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}),clientId:e.clientId||r.id,name:r.name||"Unknown"}));console.log(`Formatted ${e.length} messages`),a(e)}else console.warn("No messages array in client data:",r),a([])}else console.error("Invalid client data:",r),d("Invalid client data received from server"),s(null),a([])}catch(e){console.error("Error fetching client messages:",e),d(e.message||"Failed to load client messages"),s(null),a([])}finally{o(!1)}},[e]);return{client:t,messages:r,isLoading:i,error:l,refreshMessages:()=>{u()},addMessage:e=>{a(t=>[...t,e])}}}(e),[g,v]=(0,n.useState)(""),[y,b]=(0,n.useState)(!1),j=(0,n.useRef)(null),{toast:w}=(0,h.dj)(),N=async()=>{if(g.trim()&&t?.phone&&!y){x({id:`temp-${Date.now()}`,content:g,sender:"user",timestamp:new Date().toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}),clientId:e,name:t?.name||"You"}),v(""),b(!0);try{console.log(`Sending message to ${t.phone}: ${g}`);let e=await c.A.post("/sendWhatsAppMessage",{to:t.phone,text:g});console.log("Message sent successfully:",e),w({title:"Message sent",description:"Your message has been sent successfully.",duration:3e3}),setTimeout(()=>{f(),b(!1)},1e3)}catch(e){console.error("Error sending message:",e),w({title:"Failed to send message",description:e.message||"There was an error sending your message.",variant:"destructive",duration:5e3}),setTimeout(()=>{f(),b(!1)},1e3)}}};return i?(0,r.jsx)("div",{className:"flex h-full items-center justify-center",children:(0,r.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,r.jsx)(u.A,{className:"h-8 w-8 animate-spin text-primary"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Loading messages..."})]})}):m?(0,r.jsx)("div",{className:"flex h-full items-center justify-center",children:(0,r.jsxs)("div",{className:"flex flex-col items-center gap-2 max-w-md text-center",children:[(0,r.jsx)("div",{className:"rounded-full bg-red-100 p-3 dark:bg-red-900",children:(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"text-red-500",children:[(0,r.jsx)("circle",{cx:"12",cy:"12",r:"10"}),(0,r.jsx)("line",{x1:"12",y1:"8",x2:"12",y2:"12"}),(0,r.jsx)("line",{x1:"12",y1:"16",x2:"12.01",y2:"16"})]})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-red-500",children:"Error Loading Messages"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:m}),(0,r.jsx)(d.$,{variant:"outline",size:"sm",className:"mt-2",onClick:()=>f(),children:"Try Again"})]})}):(0,r.jsxs)("div",{className:"flex h-full flex-col",children:[(0,r.jsx)("div",{className:"border-b p-4",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsxs)(o.eu,{className:"h-10 w-10",children:[(0,r.jsx)(o.BK,{src:"/placeholder.svg?height=40&width=40"}),(0,r.jsx)(o.q5,{children:t?.name?.substring(0,2)||"CL"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium",children:t?.name||"Client"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:t?.phone||"No phone number"})]})]})}),(0,r.jsxs)("div",{className:"flex-1 overflow-auto p-4 space-y-4",children:[0===s.length?(0,r.jsx)("div",{className:"flex h-full items-center justify-center text-muted-foreground",children:"No messages yet"}):s.map(e=>(0,r.jsxs)("div",{className:(0,l.cn)("flex items-end gap-2 max-w-[80%]","agent"===e.sender?"mr-auto":"ml-auto flex-row-reverse"),children:["agent"===e.sender&&(0,r.jsxs)(o.eu,{className:"h-8 w-8",children:[(0,r.jsx)(o.BK,{src:"/placeholder.svg?height=32&width=32"}),(0,r.jsx)(o.q5,{children:"AG"})]}),(0,r.jsxs)("div",{className:(0,l.cn)("rounded-lg p-3","agent"===e.sender?"bg-muted text-foreground":"bg-primary text-primary-foreground"),children:[(0,r.jsx)("p",{children:e.content}),(0,r.jsx)("span",{className:"mt-1 block text-right text-xs opacity-70",children:e.timestamp})]})]},e.id)),(0,r.jsx)("div",{ref:j})]}),(0,r.jsx)("div",{className:"border-t p-4",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(a.p,{placeholder:"Type a message...",className:"flex-1",value:g,onChange:e=>v(e.target.value),onKeyPress:e=>{"Enter"===e.key&&N()}}),(0,r.jsx)(d.$,{size:"icon",onClick:N,disabled:!g.trim()||y,children:y?(0,r.jsx)(u.A,{className:"h-5 w-5 animate-spin"}):(0,r.jsx)(p.A,{className:"h-5 w-5"})})]})})]})}function f(){let[e,t]=(0,n.useState)(null),[s,d]=(0,n.useState)(""),{clients:u,isLoading:p,error:h,currentPage:f,totalPages:x,hasNext:g,hasPrevious:v,nextPage:y,previousPage:b}=function(e=1,t=10){let[s,r]=(0,n.useState)([]),[a,i]=(0,n.useState)(!0),[o,l]=(0,n.useState)(null),[d,u]=(0,n.useState)(e),[p,h]=(0,n.useState)(0),m=(0,n.useCallback)(async e=>{i(!0),l(null);try{console.log(`Fetching clients page ${e} with limit ${t}`);let s=await c.A.get(`/clients?page=${e}&limit=${t}`);if(console.log("Client list response:",s),s){if(s.data&&s.data.data)console.log(`Received ${s.data.data.length} clients with pagination`),r(s.data.data),h(s.data.pagination?.total||s.data.data.length);else if(s.data&&Array.isArray(s.data))console.log(`Received ${s.data.length} clients as array in data`),r(s.data),h(s.data.length);else if(Array.isArray(s))console.log(`Received ${s.length} clients as direct array`),r(s),h(s.length);else{let e=s.data||s;if(e&&"object"==typeof e){let t=Object.values(e).filter(e=>Array.isArray(e)&&e.length>0&&e[0]&&"object"==typeof e[0]&&"id"in e[0]);if(t.length>0){let e=t[0];console.log(`Found client array with ${e.length} clients`),r(e),h(e.length)}else console.error("Unexpected response format:",s),l("Unexpected response format from server"),r([]),h(0)}else console.error("Unexpected response format:",s),l("Unexpected response format from server"),r([]),h(0)}}else console.error("Empty response received"),l("Empty response received from server"),r([]),h(0)}catch(e){console.error("Error fetching clients:",e),l(e.message||"Failed to load client list"),r([]),h(0)}finally{i(!1)}},[t]),f=Math.max(1,Math.ceil(p/t)),x=d<f,g=d>1;return{clients:s,isLoading:a,error:o,currentPage:d,totalPages:f,hasNext:x,hasPrevious:g,nextPage:()=>{x&&u(e=>e+1)},previousPage:()=>{g&&u(e=>e-1)},refreshClients:()=>m(d)}}(),j=u.filter(e=>e.name.toLowerCase().includes(s.toLowerCase())||e.lastMessage.toLowerCase().includes(s.toLowerCase()));return p?(0,r.jsx)("div",{className:"flex h-full items-center justify-center",children:"Loading conversations..."}):h?(0,r.jsxs)("div",{className:"flex h-full items-center justify-center text-red-500",children:["Error: ",h]}):(0,r.jsxs)("div",{className:"flex h-full",children:[(0,r.jsxs)("div",{className:"w-80 border-r flex flex-col h-full",children:[(0,r.jsx)("div",{className:"border-b p-4",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(i.A,{className:"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground"}),(0,r.jsx)(a.p,{placeholder:"Search conversations...",className:"pl-8",value:s,onChange:e=>d(e.target.value)})]})}),(0,r.jsx)("div",{className:"flex-1 overflow-auto",children:j.length>0?j.map(s=>(0,r.jsxs)("div",{className:(0,l.cn)("flex items-center gap-3 border-b p-4 hover:bg-muted/50 cursor-pointer",s.id===e&&"bg-muted"),onClick:()=>t(s.id),children:[(0,r.jsxs)(o.eu,{className:"h-10 w-10",children:[(0,r.jsx)(o.BK,{src:"/placeholder.svg"}),(0,r.jsx)(o.q5,{children:s.name.substring(0,2)||"UN"})]}),(0,r.jsxs)("div",{className:"flex-1 overflow-hidden",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h3",{className:"font-medium",children:s.name}),(0,r.jsx)("span",{className:"text-xs text-muted-foreground",children:new Date(s.lastActive).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})]}),(0,r.jsx)("p",{className:"truncate text-sm text-muted-foreground",children:s.lastMessage})]})]},s.id)):(0,r.jsx)("div",{className:"flex h-40 items-center justify-center text-muted-foreground",children:"No conversations found"})}),(g||v)&&(0,r.jsxs)("div",{className:"flex justify-between border-t p-2",children:[(0,r.jsx)("button",{className:"px-3 py-1 text-sm disabled:opacity-50",onClick:b,disabled:!v,children:"Previous"}),(0,r.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Page ",f," of ",x]}),(0,r.jsx)("button",{className:"px-3 py-1 text-sm disabled:opacity-50",onClick:y,disabled:!g,children:"Next"})]})]}),(0,r.jsx)("div",{className:"flex-1",children:e?(0,r.jsx)(m,{clientId:e}):(0,r.jsx)("div",{className:"flex h-full items-center justify-center text-muted-foreground",children:"Select a conversation to start chatting"})})]})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29639:(e,t,s)=>{"use strict";s.d(t,{ChatList:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call ChatList() from the server but ChatList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\components\\messaging\\chat-list.tsx","ChatList")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},41862:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},48161:e=>{"use strict";e.exports=require("node:os")},51455:e=>{"use strict";e.exports=require("node:fs/promises")},55438:(e,t,s)=>{Promise.resolve().then(s.bind(s,29217))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57975:e=>{"use strict";e.exports=require("node:util")},59556:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(51060);class n{constructor(){this.isOfflineMode=!1,this.baseURL="http://localhost:5000/api/v1",this.baseURL||console.warn("NEXT_PUBLIC_BACKEND_API_URL is not defined. API requests may fail."),this.client=r.A.create({baseURL:this.baseURL,headers:{"Content-Type":"application/json"},timeout:1e4}),this.client.interceptors.request.use(e=>e,e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,e=>(e.response?(console.error("API Error Response:",e.response.status,e.response.data),e.response.data&&0!==Object.keys(e.response.data).length||(e.response.data={error:`Request failed with status code ${e.response.status}`}),404===e.response.status&&(console.log("Resource not found:",e.config?.url),e.response.data={error:"The requested resource was not found. Please refresh and try again."}),e.responseData=e.response.data):e.request?(console.error("API No Response:",e.request),e.message&&(e.message.includes("Network Error")||e.message.includes("timeout"))?(console.error("Network Error detected. Server might be down or unreachable."),this.setOfflineMode(!0),e.isOffline=!0,e.responseData={error:"Network Error: Unable to connect to the server. Please check your connection or try again later.",isOffline:!0},console.warn("API Client: Server connection failed. If you're running in development mode, make sure your backend server is running."),window.dispatchEvent(new CustomEvent("api:offline",{detail:{isOffline:!0}}))):e.responseData={error:"No response received from server. Please check your connection."}):(console.error("API Request Error:",e.message),e.responseData={error:e.message||"An unexpected error occurred"}),Promise.reject(e)))}async get(e,t){try{if(this.isOfflineMode&&!e.includes("/health"))throw console.log(`API Client: In offline mode, skipping GET request to ${e}`),Error("Network Error: Application is in offline mode");let s=await this.client.get(e,t);return this.isOfflineMode&&this.setOfflineMode(!1),s.data}catch(e){throw e}}async post(e,t,s){return(await this.client.post(e,t,s)).data}async put(e,t,s){return(await this.client.put(e,t,s)).data}async delete(e,t){try{console.log(`Making DELETE request to: ${e}`);let s=await this.client.delete(e,t);if(204===s.status)return console.log(`DELETE request to ${e} successful with 204 status`),null;return s.data}catch(t){throw console.error(`DELETE request to ${e} failed:`,t),t}}async patch(e,t,s){return(await this.client.patch(e,t,s)).data}async upload(e,t,s){let r={...s,headers:{...s?.headers,"Content-Type":"multipart/form-data"}};return(await this.client.post(e,t,r)).data}getBaseURL(){return this.baseURL}isInOfflineMode(){return this.isOfflineMode}setOfflineMode(e){this.isOfflineMode!==e&&(console.log(`API Client: Setting offline mode to ${e}`),this.isOfflineMode=e,window.dispatchEvent(new CustomEvent("api:offline",{detail:{isOffline:e}})))}async tryReconnect(){try{return console.log("API Client: Attempting to reconnect to the server..."),await this.client.get("/health",{timeout:5e3}),console.log("API Client: Reconnection successful"),this.setOfflineMode(!1),!0}catch(e){return console.log("API Client: Reconnection failed, still in offline mode"),!1}}}let a=new n},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64636:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var r=s(65239),n=s(48088),a=s(88170),i=s.n(a),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let c={children:["",{children:["dashboard",{children:["messaging",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,21353)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\messaging\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,83249)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,60566)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\dashboard\\messaging\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/dashboard/messaging/page",pathname:"/dashboard/messaging",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},68988:(e,t,s)=>{"use strict";s.d(t,{p:()=>i});var r=s(60687),n=s(43210),a=s(96241);let i=n.forwardRef(({className:e,type:t,...s},n)=>(0,r.jsx)("input",{type:t,className:(0,a.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:n,...s}));i.displayName="Input"},73024:e=>{"use strict";e.exports=require("node:fs")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85702:(e,t,s)=>{Promise.resolve().then(s.bind(s,29639))},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},99270:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[7719,9656,2190,3903,5153,3555,1060,8722,9464,381],()=>s(64636));module.exports=r})();