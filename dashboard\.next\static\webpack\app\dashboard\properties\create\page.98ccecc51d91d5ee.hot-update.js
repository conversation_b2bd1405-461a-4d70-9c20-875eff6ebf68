"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/properties/create/page",{

/***/ "(app-pages-browser)/./app/dashboard/properties/create/property-form-steps.tsx":
/*!*****************************************************************!*\
  !*** ./app/dashboard/properties/create/property-form-steps.tsx ***!
  \*****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PropertyFormSteps: () => (/* binding */ PropertyFormSteps)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,Plus,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,Plus,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,Plus,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,Plus,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,Plus,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,Plus,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/hooks/useSimpleLanguage */ \"(app-pages-browser)/./hooks/useSimpleLanguage.tsx\");\n/* harmony import */ var _components_SimpleImageUpload__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/SimpleImageUpload */ \"(app-pages-browser)/./components/SimpleImageUpload.tsx\");\n/* __next_internal_client_entry_do_not_use__ PropertyFormSteps auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction PropertyFormSteps(param) {\n    let { onSave, loading, initialData, isEdit = false, propertyId } = param;\n    _s();\n    const { language } = (0,_hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_11__.useSimpleLanguage)();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const totalSteps = 4;\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const defaultFormData = {\n        title: '',\n        titleAr: '',\n        description: '',\n        descriptionAr: '',\n        price: '',\n        currency: 'SAR',\n        type: 'APARTMENT',\n        status: 'AVAILABLE',\n        bedrooms: '',\n        bathrooms: '',\n        area: '',\n        location: '',\n        locationAr: '',\n        address: '',\n        addressAr: '',\n        city: '',\n        cityAr: '',\n        country: 'Saudi Arabia',\n        countryAr: 'المملكة العربية السعودية',\n        images: [],\n        features: [],\n        featuresAr: [],\n        amenities: [],\n        amenitiesAr: [],\n        yearBuilt: '',\n        parking: '',\n        furnished: false,\n        petFriendly: false,\n        utilities: '',\n        utilitiesAr: '',\n        contactInfo: '',\n        isFeatured: false,\n        isActive: true\n    };\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialData || defaultFormData);\n    const [newFeature, setNewFeature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newFeatureAr, setNewFeatureAr] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newAmenity, setNewAmenity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newAmenityAr, setNewAmenityAr] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Auto-save functionality (only for create mode)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PropertyFormSteps.useEffect\": ()=>{\n            if (!isEdit && !initialData) {\n                const savedData = localStorage.getItem('property-draft');\n                if (savedData) {\n                    try {\n                        const parsed = JSON.parse(savedData);\n                        setFormData(parsed);\n                    } catch (error) {\n                        console.error('Error loading draft:', error);\n                    }\n                }\n            }\n        }\n    }[\"PropertyFormSteps.useEffect\"], [\n        isEdit,\n        initialData\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PropertyFormSteps.useEffect\": ()=>{\n            if (!isEdit) {\n                const timer = setTimeout({\n                    \"PropertyFormSteps.useEffect.timer\": ()=>{\n                        localStorage.setItem('property-draft', JSON.stringify(formData));\n                    }\n                }[\"PropertyFormSteps.useEffect.timer\"], 1000);\n                return ({\n                    \"PropertyFormSteps.useEffect\": ()=>clearTimeout(timer)\n                })[\"PropertyFormSteps.useEffect\"];\n            }\n        }\n    }[\"PropertyFormSteps.useEffect\"], [\n        formData,\n        isEdit\n    ]);\n    // Initialize form data when initialData changes (for edit mode)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PropertyFormSteps.useEffect\": ()=>{\n            if (initialData && isEdit) {\n                setFormData(initialData);\n            }\n        }\n    }[\"PropertyFormSteps.useEffect\"], [\n        initialData,\n        isEdit\n    ]);\n    // Comprehensive bilingual translations\n    const translations = {\n        ar: {\n            step: 'الخطوة',\n            of: 'من',\n            next: 'التالي',\n            previous: 'السابق',\n            save: 'حفظ العقار',\n            required: 'مطلوب',\n            optional: 'اختياري',\n            basicInfo: 'المعلومات الأساسية',\n            propertyDetails: 'تفاصيل العقار',\n            locationInfo: 'معلومات الموقع',\n            additionalInfo: 'معلومات إضافية',\n            title: 'عنوان العقار',\n            description: 'وصف العقار',\n            price: 'السعر',\n            currency: 'العملة',\n            propertyType: 'نوع العقار',\n            status: 'حالة العقار',\n            bedrooms: 'غرف النوم',\n            bathrooms: 'دورات المياه',\n            area: 'المساحة (متر مربع)',\n            yearBuilt: 'سنة البناء',\n            parking: 'مواقف السيارات',\n            location: 'الموقع',\n            address: 'العنوان',\n            city: 'المدينة',\n            country: 'الدولة',\n            images: 'صور العقار',\n            features: 'مميزات العقار',\n            amenities: 'المرافق والخدمات',\n            utilities: 'الخدمات المشمولة',\n            contactInfo: 'معلومات التواصل',\n            furnished: 'مفروش',\n            petFriendly: 'يسمح بالحيوانات الأليفة',\n            featured: 'عقار مميز',\n            active: 'نشط',\n            addFeature: 'إضافة ميزة',\n            addAmenity: 'إضافة مرفق',\n            uploadImages: 'رفع صور العقار',\n            dragDropImages: 'اسحب وأفلت الصور هنا، أو انقر للاختيار',\n            titlePlaceholder: 'أدخل عنوان العقار...',\n            descriptionPlaceholder: 'اكتب وصفاً مفصلاً للعقار...',\n            locationPlaceholder: 'أدخل موقع العقار...',\n            addressPlaceholder: 'أدخل العنوان الكامل...',\n            cityPlaceholder: 'أدخل اسم المدينة...',\n            featurePlaceholder: 'أضف ميزة جديدة...',\n            amenityPlaceholder: 'أضف مرفق جديد...',\n            utilitiesPlaceholder: 'اذكر الخدمات المشمولة...',\n            contactPlaceholder: 'أدخل معلومات التواصل...',\n            stepDescription1: 'أدخل المعلومات الأساسية للعقار',\n            stepDescription2: 'حدد تفاصيل ومواصفات العقار',\n            stepDescription3: 'أضف معلومات الموقع والعنوان',\n            stepDescription4: 'أضف الصور والمعلومات الإضافية',\n            completed: 'مكتمل',\n            current: 'الحالي',\n            pending: 'في الانتظار',\n            imageGallery: 'معرض الصور',\n            mainImage: 'الصورة الرئيسية',\n            additionalImages: 'الصور الإضافية',\n            imageTips: 'نصائح للصور',\n            noFeatures: 'لا توجد مميزات مضافة',\n            noAmenities: 'لا توجد مرافق مضافة',\n            noImages: 'لم يتم رفع صور بعد',\n            setAsMain: 'تعيين كصورة رئيسية',\n            removeImage: 'حذف الصورة',\n            saving: 'جاري الحفظ...',\n            success: 'تم بنجاح',\n            error: 'حدث خطأ'\n        },\n        en: {\n            step: 'Step',\n            of: 'of',\n            next: 'Next',\n            previous: 'Previous',\n            save: 'Save Property',\n            required: 'Required',\n            optional: 'Optional',\n            basicInfo: 'Basic Information',\n            propertyDetails: 'Property Details',\n            locationInfo: 'Location Information',\n            additionalInfo: 'Additional Information',\n            title: 'Property Title',\n            description: 'Property Description',\n            price: 'Price',\n            currency: 'Currency',\n            propertyType: 'Property Type',\n            status: 'Property Status',\n            bedrooms: 'Bedrooms',\n            bathrooms: 'Bathrooms',\n            area: 'Area (sqm)',\n            yearBuilt: 'Year Built',\n            parking: 'Parking Spaces',\n            location: 'Location',\n            address: 'Address',\n            city: 'City',\n            country: 'Country',\n            images: 'Property Images',\n            features: 'Property Features',\n            amenities: 'Amenities & Services',\n            utilities: 'Included Utilities',\n            contactInfo: 'Contact Information',\n            furnished: 'Furnished',\n            petFriendly: 'Pet Friendly',\n            featured: 'Featured Property',\n            active: 'Active',\n            addFeature: 'Add Feature',\n            addAmenity: 'Add Amenity',\n            uploadImages: 'Upload Property Images',\n            dragDropImages: 'Drag and drop images here, or click to select',\n            titlePlaceholder: 'Enter property title...',\n            descriptionPlaceholder: 'Write a detailed property description...',\n            locationPlaceholder: 'Enter property location...',\n            addressPlaceholder: 'Enter full address...',\n            cityPlaceholder: 'Enter city name...',\n            featurePlaceholder: 'Add new feature...',\n            amenityPlaceholder: 'Add new amenity...',\n            utilitiesPlaceholder: 'List included utilities...',\n            contactPlaceholder: 'Enter contact information...',\n            stepDescription1: 'Enter basic property information',\n            stepDescription2: 'Specify property details and specifications',\n            stepDescription3: 'Add location and address information',\n            stepDescription4: 'Add images and additional information',\n            completed: 'Completed',\n            current: 'Current',\n            pending: 'Pending',\n            imageGallery: 'Image Gallery',\n            mainImage: 'Main Image',\n            additionalImages: 'Additional Images',\n            imageTips: 'Image Tips',\n            noFeatures: 'No features added',\n            noAmenities: 'No amenities added',\n            noImages: 'No images uploaded yet',\n            setAsMain: 'Set as Main Image',\n            removeImage: 'Remove Image',\n            saving: 'Saving...',\n            success: 'Success',\n            error: 'Error'\n        }\n    };\n    const t = translations[language];\n    // Bilingual property types\n    const propertyTypes = {\n        ar: {\n            APARTMENT: 'شقة سكنية',\n            VILLA: 'فيلا',\n            TOWNHOUSE: 'تاون هاوس',\n            PENTHOUSE: 'بنتهاوس',\n            STUDIO: 'استوديو',\n            OFFICE: 'مكتب تجاري',\n            SHOP: 'محل تجاري',\n            WAREHOUSE: 'مستودع',\n            LAND: 'قطعة أرض',\n            BUILDING: 'مبنى كامل'\n        },\n        en: {\n            APARTMENT: 'Apartment',\n            VILLA: 'Villa',\n            TOWNHOUSE: 'Townhouse',\n            PENTHOUSE: 'Penthouse',\n            STUDIO: 'Studio',\n            OFFICE: 'Office',\n            SHOP: 'Shop',\n            WAREHOUSE: 'Warehouse',\n            LAND: 'Land',\n            BUILDING: 'Building'\n        }\n    };\n    // Bilingual property statuses\n    const propertyStatuses = {\n        ar: {\n            AVAILABLE: 'متاح للبيع',\n            SOLD: 'تم البيع',\n            RENTED: 'مؤجر',\n            RESERVED: 'محجوز',\n            OFF_MARKET: 'غير متاح'\n        },\n        en: {\n            AVAILABLE: 'Available',\n            SOLD: 'Sold',\n            RENTED: 'Rented',\n            RESERVED: 'Reserved',\n            OFF_MARKET: 'Off Market'\n        }\n    };\n    const stepTitles = [\n        t.basicInfo,\n        t.propertyDetails,\n        t.locationInfo,\n        t.additionalInfo\n    ];\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        await onSave(formData);\n        // Clear draft after successful save\n        localStorage.removeItem('property-draft');\n    };\n    // Auto-save function for images\n    const handleImageAutoSave = async (images)=>{\n        if (!isEdit || !propertyId) return;\n        try {\n            const response = await fetch(\"/api/v1/properties/\".concat(propertyId, \"/images\"), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    images\n                })\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.message || 'Failed to auto-save images');\n            }\n            const result = await response.json();\n            console.log('Images auto-saved successfully:', result);\n        } catch (error) {\n            console.error('Auto-save error:', error);\n            throw error;\n        }\n    };\n    const addFeature = ()=>{\n        if (newFeature.trim()) {\n            setFormData((prev)=>({\n                    ...prev,\n                    features: [\n                        ...prev.features,\n                        newFeature.trim()\n                    ]\n                }));\n            setNewFeature('');\n        }\n    };\n    const addFeatureAr = ()=>{\n        if (newFeatureAr.trim()) {\n            setFormData((prev)=>({\n                    ...prev,\n                    featuresAr: [\n                        ...prev.featuresAr,\n                        newFeatureAr.trim()\n                    ]\n                }));\n            setNewFeatureAr('');\n        }\n    };\n    const addAmenity = ()=>{\n        if (newAmenity.trim()) {\n            setFormData((prev)=>({\n                    ...prev,\n                    amenities: [\n                        ...prev.amenities,\n                        newAmenity.trim()\n                    ]\n                }));\n            setNewAmenity('');\n        }\n    };\n    const addAmenityAr = ()=>{\n        if (newAmenityAr.trim()) {\n            setFormData((prev)=>({\n                    ...prev,\n                    amenitiesAr: [\n                        ...prev.amenitiesAr,\n                        newAmenityAr.trim()\n                    ]\n                }));\n            setNewAmenityAr('');\n        }\n    };\n    const removeFeature = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                features: prev.features.filter((_, i)=>i !== index)\n            }));\n    };\n    const removeFeatureAr = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                featuresAr: prev.featuresAr.filter((_, i)=>i !== index)\n            }));\n    };\n    const removeAmenity = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                amenities: prev.amenities.filter((_, i)=>i !== index)\n            }));\n    };\n    const removeAmenityAr = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                amenitiesAr: prev.amenitiesAr.filter((_, i)=>i !== index)\n            }));\n    };\n    const removeImage = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                images: prev.images.filter((_, i)=>i !== index)\n            }));\n    };\n    const moveImage = (fromIndex, toIndex)=>{\n        setFormData((prev)=>{\n            const newImages = [\n                ...prev.images\n            ];\n            const [movedImage] = newImages.splice(fromIndex, 1);\n            newImages.splice(toIndex, 0, movedImage);\n            return {\n                ...prev,\n                images: newImages\n            };\n        });\n    };\n    const setMainImage = (index)=>{\n        if (index === 0) return; // Already main image\n        moveImage(index, 0);\n    };\n    const nextStep = ()=>{\n        if (currentStep < totalSteps) {\n            setCurrentStep(currentStep + 1);\n        }\n    };\n    const prevStep = ()=>{\n        if (currentStep > 1) {\n            setCurrentStep(currentStep - 1);\n        }\n    };\n    const isStepValid = (step)=>{\n        switch(step){\n            case 1:\n                return formData.title && formData.description && formData.price && formData.type;\n            case 2:\n                return true; // Property details are optional\n            case 3:\n                return formData.location && formData.address && formData.city;\n            case 4:\n                return true; // Additional info is optional\n            default:\n                return false;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(language === 'ar' ? 'rtl' : 'ltr'),\n        dir: language === 'ar' ? 'rtl' : 'ltr',\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-r from-emerald-100/50 to-teal-100/50 dark:from-emerald-900/20 dark:to-teal-900/20 rounded-2xl\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 461,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between \".concat(language === 'ar' ? 'flex-row-reverse' : ''),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4 \".concat(language === 'ar' ? 'flex-row-reverse' : ''),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-14 h-14 bg-gradient-to-br from-emerald-500 via-teal-500 to-cyan-600 rounded-2xl flex items-center justify-center shadow-xl shadow-emerald-200 dark:shadow-emerald-900/50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-black text-lg\",\n                                                                children: currentStep\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 467,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 466,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute -top-1 \".concat(language === 'ar' ? '-right-1' : '-left-1', \" w-6 h-6 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center animate-pulse\"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white text-xs font-bold\",\n                                                                children: \"✦\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 470,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 469,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1 \".concat(language === 'ar' ? 'text-right' : 'text-left'),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2 \".concat(language === 'ar' ? 'flex-row-reverse' : ''),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xl font-black text-slate-800 dark:text-white\",\n                                                                    children: [\n                                                                        t.step,\n                                                                        \" \",\n                                                                        currentStep,\n                                                                        \" \",\n                                                                        t.of,\n                                                                        \" \",\n                                                                        totalSteps\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 475,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"px-3 py-1 bg-emerald-100 dark:bg-emerald-900/30 rounded-full\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-bold text-emerald-700 dark:text-emerald-300\",\n                                                                        children: t.current\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 479,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 478,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 474,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-lg font-bold text-slate-700 dark:text-slate-300\",\n                                                            children: stepTitles[currentStep - 1]\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 482,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-slate-600 dark:text-slate-400\",\n                                                            children: [\n                                                                currentStep === 1 && t.stepDescription1,\n                                                                currentStep === 2 && t.stepDescription2,\n                                                                currentStep === 3 && t.stepDescription3,\n                                                                currentStep === 4 && t.stepDescription4\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 485,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 473,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"\".concat(language === 'ar' ? 'text-right' : 'text-left'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-4xl font-black bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent\",\n                                                    children: [\n                                                        Math.round(currentStep / totalSteps * 100),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 495,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-bold text-emerald-600 dark:text-emerald-400\",\n                                                    children: t.completed\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 498,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                    lineNumber: 463,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 462,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                        lineNumber: 460,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full bg-slate-200 dark:bg-slate-700 rounded-full h-4 shadow-inner\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-r from-emerald-500 via-teal-500 to-cyan-500 h-4 rounded-full transition-all duration-1000 ease-out shadow-lg relative overflow-hidden\",\n                                style: {\n                                    width: \"\".concat(currentStep / totalSteps * 100, \"%\")\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-r from-emerald-400/50 to-teal-400/50 animate-pulse delay-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 514,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 509,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                            lineNumber: 508,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                        lineNumber: 507,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-4 gap-4\",\n                        children: stepTitles.map((title, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 rounded-2xl flex items-center justify-center text-lg font-black transition-all duration-500 shadow-xl \".concat(index + 1 <= currentStep ? 'bg-gradient-to-br from-emerald-500 via-teal-500 to-cyan-600 text-white shadow-emerald-200 dark:shadow-emerald-900/50 scale-110' : index + 1 === currentStep + 1 ? 'bg-gradient-to-br from-slate-300 to-slate-400 text-slate-700 shadow-slate-200 dark:shadow-slate-800 scale-105' : 'bg-slate-200 dark:bg-slate-700 text-slate-500 dark:text-slate-400'),\n                                        children: index + 1 < currentStep ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-7 w-7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 533,\n                                            columnNumber: 19\n                                        }, this) : index + 1 === currentStep ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 bg-white rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 535,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-black\",\n                                            children: index + 1\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 537,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 523,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-bold leading-tight \".concat(index + 1 <= currentStep ? 'text-emerald-700 dark:text-emerald-300' : index + 1 === currentStep + 1 ? 'text-slate-600 dark:text-slate-400' : 'text-slate-500 dark:text-slate-500'),\n                                                children: title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs mt-1 \".concat(index + 1 <= currentStep ? 'text-emerald-600 dark:text-emerald-400' : 'text-slate-400 dark:text-slate-500'),\n                                                children: index + 1 <= currentStep ? t.completed : t.pending\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 550,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 540,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 522,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                        lineNumber: 520,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                lineNumber: 458,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"space-y-8\",\n                children: [\n                    currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                        className: \"border border-slate-700 bg-slate-800\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                className: \"pb-6 border-b border-slate-700\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                    className: \"text-xl font-bold text-white flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-emerald-600 text-white rounded-lg flex items-center justify-center text-lg font-bold\",\n                                            children: \"1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 569,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xl\",\n                                                    children: t.basicInfo\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 573,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-normal text-slate-400 mt-1\",\n                                                    children: language === 'ar' ? 'المعلومات الأساسية' : 'Basic Information'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 574,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 572,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                    lineNumber: 568,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 567,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                className: \"space-y-8 p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"title\",\n                                                        className: \"text-base font-bold text-white flex items-center gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-6 h-6 bg-emerald-900/50 rounded-lg flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-emerald-400 text-sm font-bold\",\n                                                                    children: \"1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 586,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 585,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            t.title,\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-400 text-lg\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 589,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 584,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"title\",\n                                                        value: formData.title,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    title: e.target.value\n                                                                })),\n                                                        placeholder: t.titlePlaceholder,\n                                                        required: true,\n                                                        dir: language === 'ar' ? 'rtl' : 'ltr',\n                                                        className: \"h-14 border-2 border-slate-600 focus:border-emerald-500 transition-all duration-300 rounded-xl text-lg bg-slate-700/50 backdrop-blur-sm shadow-sm hover:shadow-md focus:shadow-lg text-white placeholder:text-slate-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 591,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 583,\n                                                columnNumber: 17\n                                            }, this),\n                                            language === 'en' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"titleEn\",\n                                                        className: \"text-base font-semibold text-slate-700 dark:text-slate-300 flex items-center gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-6 h-6 bg-slate-100 dark:bg-slate-700 rounded-lg flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-slate-600 dark:text-slate-400 text-sm\",\n                                                                    children: \"EN\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 605,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 604,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            t.titleEn,\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-slate-400 text-sm\",\n                                                                children: [\n                                                                    \"(\",\n                                                                    t.optional,\n                                                                    \")\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 608,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 603,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"titleEn\",\n                                                        value: formData.titleAr,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    titleAr: e.target.value\n                                                                })),\n                                                        placeholder: t.titleEnPlaceholder,\n                                                        dir: \"ltr\",\n                                                        className: \"h-12 border-2 border-slate-200 dark:border-slate-600 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-300 rounded-xl bg-white/30 dark:bg-slate-800/30\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 610,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 602,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 582,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"description\",\n                                                    className: \"text-base font-bold text-slate-800 dark:text-slate-200 flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-6 h-6 bg-emerald-100 dark:bg-emerald-900/30 rounded-lg flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-emerald-600 dark:text-emerald-400 text-sm font-bold\",\n                                                                children: \"2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 627,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 626,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        t.description,\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-500 text-lg\",\n                                                            children: \"*\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 630,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 625,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                    id: \"description\",\n                                                    value: formData.description,\n                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                description: e.target.value\n                                                            })),\n                                                    placeholder: t.descriptionPlaceholder,\n                                                    required: true,\n                                                    rows: 6,\n                                                    dir: \"rtl\",\n                                                    className: \"border-2 border-slate-200 dark:border-slate-600 focus:border-emerald-500 dark:focus:border-emerald-400 transition-all duration-300 resize-none rounded-xl text-lg bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm shadow-sm hover:shadow-md focus:shadow-lg\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 632,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 624,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 623,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"price\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2\",\n                                                        children: [\n                                                            t.price,\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500 text-lg\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 650,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 648,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"price\",\n                                                        type: \"number\",\n                                                        value: formData.price,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    price: e.target.value\n                                                                })),\n                                                        placeholder: \"0\",\n                                                        required: true,\n                                                        className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-200 rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 652,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"currency\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.currency\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 663,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                        value: formData.currency,\n                                                        onValueChange: (value)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    currency: value\n                                                                })),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                                className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 rounded-lg\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 668,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 667,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                        value: \"SAR\",\n                                                                        children: \"SAR - ريال سعودي\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 671,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                        value: \"AED\",\n                                                                        children: \"AED - درهم إماراتي\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 672,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                        value: \"USD\",\n                                                                        children: \"USD - دولار أمريكي\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 673,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                        value: \"EUR\",\n                                                                        children: \"EUR - يورو\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 674,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                        value: \"GBP\",\n                                                                        children: \"GBP - جنيه إسترليني\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 675,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 670,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 666,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 662,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"type\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2\",\n                                                        children: [\n                                                            t.propertyType,\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500 text-lg\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 682,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 680,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                        value: formData.type,\n                                                        onValueChange: (value)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    type: value\n                                                                })),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                                className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 rounded-lg\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 686,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 685,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                                children: Object.entries(propertyTypes[language]).map((param)=>{\n                                                                    let [key, value] = param;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                        value: key,\n                                                                        children: value\n                                                                    }, key, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 690,\n                                                                        columnNumber: 25\n                                                                    }, this);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 688,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 684,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 679,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 646,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"status\",\n                                                    className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                    children: t.status\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 700,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                    value: formData.status,\n                                                    onValueChange: (value)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                status: value\n                                                            })),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                            className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 rounded-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 705,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 704,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                            children: Object.entries(propertyStatuses[language]).map((param)=>{\n                                                                let [key, value] = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                    value: key,\n                                                                    children: value\n                                                                }, key, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 709,\n                                                                    columnNumber: 25\n                                                                }, this);\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 707,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 703,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 699,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 698,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 580,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                        lineNumber: 566,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                        className: \"shadow-xl border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                className: \"pb-6 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-t-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg\",\n                                            children: \"2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 724,\n                                            columnNumber: 17\n                                        }, this),\n                                        t.propertyDetails\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                    lineNumber: 723,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 722,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                className: \"space-y-8 p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"bedrooms\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.bedrooms\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 734,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"bedrooms\",\n                                                        type: \"number\",\n                                                        value: formData.bedrooms,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    bedrooms: e.target.value\n                                                                })),\n                                                        placeholder: \"0\",\n                                                        min: \"0\",\n                                                        className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 737,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 733,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"bathrooms\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.bathrooms\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 748,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"bathrooms\",\n                                                        type: \"number\",\n                                                        value: formData.bathrooms,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    bathrooms: e.target.value\n                                                                })),\n                                                        placeholder: \"0\",\n                                                        min: \"0\",\n                                                        className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 751,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 747,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"area\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.area\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 762,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"area\",\n                                                        type: \"number\",\n                                                        value: formData.area,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    area: e.target.value\n                                                                })),\n                                                        placeholder: \"0\",\n                                                        min: \"0\",\n                                                        className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 765,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 761,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"yearBuilt\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.yearBuilt\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 776,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"yearBuilt\",\n                                                        type: \"number\",\n                                                        value: formData.yearBuilt,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    yearBuilt: e.target.value\n                                                                })),\n                                                        placeholder: \"2024\",\n                                                        min: \"1900\",\n                                                        max: \"2030\",\n                                                        className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 779,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 775,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"parking\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.parking\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 791,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"parking\",\n                                                        type: \"number\",\n                                                        value: formData.parking,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    parking: e.target.value\n                                                                })),\n                                                        placeholder: \"0\",\n                                                        min: \"0\",\n                                                        className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 794,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 790,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 732,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_10__.Separator, {\n                                        className: \"my-8\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 806,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                    children: t.features\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 811,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                            value: newFeature,\n                                                            onChange: (e)=>setNewFeature(e.target.value),\n                                                            placeholder: t.featurePlaceholder,\n                                                            onKeyPress: (e)=>e.key === 'Enter' && (e.preventDefault(), addFeature()),\n                                                            className: \"h-10 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 815,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            type: \"button\",\n                                                            onClick: addFeature,\n                                                            size: \"sm\",\n                                                            className: \"bg-green-600 hover:bg-green-700 px-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 823,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 822,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 814,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-2 min-h-[40px] p-3 border-2 border-dashed border-gray-200 dark:border-gray-600 rounded-lg\",\n                                                    children: [\n                                                        formData.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                variant: \"secondary\",\n                                                                className: \"flex items-center gap-1 px-3 py-1 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200\",\n                                                                children: [\n                                                                    feature,\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-3 w-3 cursor-pointer hover:text-red-600\",\n                                                                        onClick: ()=>removeFeature(index)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 830,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 828,\n                                                                columnNumber: 23\n                                                            }, this)),\n                                                        formData.features.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: t.noFeatures\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 837,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 826,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 810,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 809,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                    children: t.amenities\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 846,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                            value: newAmenity,\n                                                            onChange: (e)=>setNewAmenity(e.target.value),\n                                                            placeholder: t.amenityPlaceholder,\n                                                            onKeyPress: (e)=>e.key === 'Enter' && (e.preventDefault(), addAmenity()),\n                                                            className: \"h-10 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 850,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            type: \"button\",\n                                                            onClick: addAmenity,\n                                                            size: \"sm\",\n                                                            className: \"bg-green-600 hover:bg-green-700 px-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 858,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 857,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 849,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-2 min-h-[40px] p-3 border-2 border-dashed border-gray-200 dark:border-gray-600 rounded-lg\",\n                                                    children: [\n                                                        formData.amenities.map((amenity, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                variant: \"secondary\",\n                                                                className: \"flex items-center gap-1 px-3 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200\",\n                                                                children: [\n                                                                    amenity,\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-3 w-3 cursor-pointer hover:text-red-600\",\n                                                                        onClick: ()=>removeAmenity(index)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 865,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 863,\n                                                                columnNumber: 23\n                                                            }, this)),\n                                                        formData.amenities.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: t.noAmenities\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 872,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 861,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 845,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 844,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 730,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                        lineNumber: 721,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                        className: \"shadow-xl border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                className: \"pb-6 bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-t-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg\",\n                                            children: \"3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 886,\n                                            columnNumber: 17\n                                        }, this),\n                                        t.locationInfo\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                    lineNumber: 885,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 884,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                className: \"space-y-8 p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"location\",\n                                                    className: \"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2\",\n                                                    children: [\n                                                        t.location,\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-500 text-lg\",\n                                                            children: \"*\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 898,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 896,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                    id: \"location\",\n                                                    value: formData.location,\n                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                location: e.target.value\n                                                            })),\n                                                    placeholder: t.locationPlaceholder,\n                                                    required: true,\n                                                    className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 900,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 895,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 894,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"address\",\n                                                    className: \"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2\",\n                                                    children: [\n                                                        t.address,\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-500 text-lg\",\n                                                            children: \"*\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 917,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 915,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                    id: \"address\",\n                                                    value: formData.address,\n                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                address: e.target.value\n                                                            })),\n                                                    placeholder: t.addressPlaceholder,\n                                                    required: true,\n                                                    className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 919,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 914,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 913,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"city\",\n                                                                className: \"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2\",\n                                                                children: [\n                                                                    t.city,\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-500 text-lg\",\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 937,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 935,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                                value: formData.city,\n                                                                onValueChange: (value)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            city: value\n                                                                        })),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                                        className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                                            placeholder: language === 'ar' ? 'اختر المدينة السعودية' : 'Select Saudi City'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                            lineNumber: 941,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 940,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Riyadh\",\n                                                                                children: \"Riyadh - الرياض\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 944,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Jeddah\",\n                                                                                children: \"Jeddah - جدة\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 945,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Mecca\",\n                                                                                children: \"Mecca - مكة المكرمة\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 946,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Medina\",\n                                                                                children: \"Medina - المدينة المنورة\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 947,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Dammam\",\n                                                                                children: \"Dammam - الدمام\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 948,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Khobar\",\n                                                                                children: \"Khobar - الخبر\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 949,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Dhahran\",\n                                                                                children: \"Dhahran - الظهران\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 950,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Taif\",\n                                                                                children: \"Taif - الطائف\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 951,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Buraidah\",\n                                                                                children: \"Buraidah - بريدة\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 952,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Tabuk\",\n                                                                                children: \"Tabuk - تبوك\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 953,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Hail\",\n                                                                                children: \"Hail - حائل\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 954,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Abha\",\n                                                                                children: \"Abha - أبها\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 955,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Yanbu\",\n                                                                                children: \"Yanbu - ينبع\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 956,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Jubail\",\n                                                                                children: \"Jubail - الجبيل\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 957,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Najran\",\n                                                                                children: \"Najran - نجران\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 958,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 943,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 939,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 934,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"country\",\n                                                                className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                                children: t.country\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 963,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                                value: formData.country,\n                                                                onValueChange: (value)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            country: value\n                                                                        })),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                                        className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 rounded-lg\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {}, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                            lineNumber: 968,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 967,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Saudi Arabia\",\n                                                                                children: \"Saudi Arabia - المملكة العربية السعودية\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 971,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"UAE\",\n                                                                                children: \"UAE - الإمارات العربية المتحدة\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 972,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Qatar\",\n                                                                                children: \"Qatar - قطر\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 973,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Kuwait\",\n                                                                                children: \"Kuwait - الكويت\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 974,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Bahrain\",\n                                                                                children: \"Bahrain - البحرين\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 975,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Oman\",\n                                                                                children: \"Oman - عُمان\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 976,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Jordan\",\n                                                                                children: \"Jordan - الأردن\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 977,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Egypt\",\n                                                                                children: \"Egypt - مصر\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 978,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 970,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 966,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 962,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 933,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"cityAr\",\n                                                                className: \"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2\",\n                                                                children: [\n                                                                    t.cityAr,\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-400 text-sm\",\n                                                                        children: [\n                                                                            \"(\",\n                                                                            t.optional,\n                                                                            \")\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 987,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 985,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                id: \"cityAr\",\n                                                                value: formData.cityAr,\n                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            cityAr: e.target.value\n                                                                        })),\n                                                                placeholder: t.cityArPlaceholder,\n                                                                dir: \"rtl\",\n                                                                className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 989,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 984,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"countryAr\",\n                                                                className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                                children: t.countryAr\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 999,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                id: \"countryAr\",\n                                                                value: formData.countryAr,\n                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            countryAr: e.target.value\n                                                                        })),\n                                                                placeholder: \"أدخل اسم البلد بالعربية\",\n                                                                dir: \"rtl\",\n                                                                className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 1002,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 998,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 983,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 932,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 892,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                        lineNumber: 883,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                        className: \"shadow-xl border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                className: \"pb-6 bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-900/20 dark:to-amber-900/20 rounded-t-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg\",\n                                            children: \"4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 1022,\n                                            columnNumber: 17\n                                        }, this),\n                                        t.additionalInfo\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                    lineNumber: 1021,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 1020,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                className: \"space-y-8 p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SimpleImageUpload__WEBPACK_IMPORTED_MODULE_12__.SimpleImageUpload, {\n                                        images: formData.images,\n                                        onImagesChange: (images)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    images\n                                                })),\n                                        onAutoSave: isEdit ? handleImageAutoSave : undefined,\n                                        onUploadStatusChange: setIsUploading,\n                                        propertyId: propertyId,\n                                        maxImages: 10,\n                                        disabled: loading\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 1030,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_10__.Separator, {\n                                        className: \"my-8\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 1042,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"utilities\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.utilities\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1047,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                        id: \"utilities\",\n                                                        value: formData.utilities,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    utilities: e.target.value\n                                                                })),\n                                                        placeholder: t.utilitiesPlaceholder,\n                                                        rows: 3,\n                                                        className: \"border-2 border-gray-200 dark:border-gray-600 focus:border-orange-500 dark:focus:border-orange-400 transition-all duration-200 resize-none rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1050,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1046,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"utilitiesAr\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.utilitiesAr\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1060,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                        id: \"utilitiesAr\",\n                                                        value: formData.utilitiesAr,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    utilitiesAr: e.target.value\n                                                                })),\n                                                        placeholder: t.utilitiesArPlaceholder,\n                                                        dir: \"rtl\",\n                                                        rows: 3,\n                                                        className: \"border-2 border-gray-200 dark:border-gray-600 focus:border-orange-500 dark:focus:border-orange-400 transition-all duration-200 resize-none rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1063,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1059,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 1045,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"contactInfo\",\n                                                className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                children: t.contactInfo\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1076,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                id: \"contactInfo\",\n                                                value: formData.contactInfo,\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            contactInfo: e.target.value\n                                                        })),\n                                                placeholder: t.contactPlaceholder,\n                                                rows: 3,\n                                                className: \"border-2 border-gray-200 dark:border-gray-600 focus:border-orange-500 dark:focus:border-orange-400 transition-all duration-200 resize-none rounded-lg\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1079,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 1075,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_10__.Separator, {\n                                        className: \"my-8\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 1089,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-4 gap-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-orange-400 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_7__.Switch, {\n                                                        id: \"furnished\",\n                                                        checked: formData.furnished,\n                                                        onCheckedChange: (checked)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    furnished: checked\n                                                                })),\n                                                        className: \"data-[state=checked]:bg-orange-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1094,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"furnished\",\n                                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer\",\n                                                        children: t.furnished\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1100,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1093,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-orange-400 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_7__.Switch, {\n                                                        id: \"petFriendly\",\n                                                        checked: formData.petFriendly,\n                                                        onCheckedChange: (checked)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    petFriendly: checked\n                                                                })),\n                                                        className: \"data-[state=checked]:bg-orange-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1105,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"petFriendly\",\n                                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer\",\n                                                        children: t.petFriendly\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1111,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1104,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-orange-400 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_7__.Switch, {\n                                                        id: \"isFeatured\",\n                                                        checked: formData.isFeatured,\n                                                        onCheckedChange: (checked)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    isFeatured: checked\n                                                                })),\n                                                        className: \"data-[state=checked]:bg-orange-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1116,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"isFeatured\",\n                                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer\",\n                                                        children: t.featured\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1122,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1115,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-orange-400 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_7__.Switch, {\n                                                        id: \"isActive\",\n                                                        checked: formData.isActive,\n                                                        onCheckedChange: (checked)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    isActive: checked\n                                                                })),\n                                                        className: \"data-[state=checked]:bg-orange-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1127,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"isActive\",\n                                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer\",\n                                                        children: t.active\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1133,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1126,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 1092,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 1028,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                        lineNumber: 1019,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center pt-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                type: \"button\",\n                                variant: \"outline\",\n                                onClick: prevStep,\n                                disabled: currentStep === 1,\n                                className: \"flex items-center gap-2 px-6 py-3 h-12 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 1151,\n                                        columnNumber: 13\n                                    }, this),\n                                    t.previous\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 1144,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-4\",\n                                children: currentStep < totalSteps ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"button\",\n                                    onClick: nextStep,\n                                    disabled: !isStepValid(currentStep),\n                                    className: \"flex items-center gap-2 px-8 py-3 h-12 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl\",\n                                    children: [\n                                        t.next,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 1164,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                    lineNumber: 1157,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"submit\",\n                                    disabled: loading || isUploading || !isStepValid(currentStep),\n                                    className: \"flex items-center gap-2 px-8 py-3 h-12 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl\",\n                                    children: [\n                                        loading || isUploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 1173,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 1175,\n                                            columnNumber: 19\n                                        }, this),\n                                        isUploading ? language === 'ar' ? 'جاري رفع الصور...' : 'Uploading images...' : t.save\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                    lineNumber: 1167,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 1155,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                        lineNumber: 1143,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                lineNumber: 563,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n        lineNumber: 456,\n        columnNumber: 5\n    }, this);\n}\n_s(PropertyFormSteps, \"MaOwaAOGs49JLOCSkgePFHwIOnI=\", false, function() {\n    return [\n        _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_11__.useSimpleLanguage\n    ];\n});\n_c = PropertyFormSteps;\nvar _c;\n$RefreshReg$(_c, \"PropertyFormSteps\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/properties/create/property-form-steps.tsx\n"));

/***/ })

});