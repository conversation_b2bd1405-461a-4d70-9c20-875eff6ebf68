(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6502],{40086:(e,s,a)=>{Promise.resolve().then(a.bind(a,59016))},59016:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>w});var l=a(95155),t=a(12115),r=a(72280),i=a(35695),c=a(45493),n=a(62177),d=a(90221),o=a(55594),m=a(97168),u=a(89852),x=a(82714),h=a(78519),p=a(56671);let j=o.Ik({email:o.Yj().email(),password:o.Yj().min(6)});function f(){let{t:e}=(0,r.B)(),s=(0,i.useRouter)(),a=(0,i.useSearchParams)().get("redirect_url")||"/dashboard/analytics";console.log("Redirect URL:",a);let[o,f]=(0,t.useState)(!1),{register:g,handleSubmit:v,formState:{errors:N}}=(0,n.mN)({resolver:(0,d.u)(j)});async function w(l){f(!0);try{let t=await (0,c.Jv)("credentials",{email:l.email,password:l.password,redirect:!1,callbackUrl:a});if(null==t?void 0:t.error){p.oR.error(e("auth.invalidCredentials")),f(!1);return}p.oR.success(e("auth.loginSuccessful")),s.push(a),s.refresh()}catch(s){console.error("Sign in error:",s),p.oR.error(e("common.somethingWentWrong")),f(!1)}}return(0,l.jsxs)("div",{className:"grid gap-6",children:[(0,l.jsx)("form",{onSubmit:v(w),children:(0,l.jsxs)("div",{className:"grid gap-4",children:[(0,l.jsxs)("div",{className:"grid gap-2",children:[(0,l.jsx)(x.J,{htmlFor:"email",children:e("auth.email")}),(0,l.jsx)(u.p,{id:"email",type:"email",placeholder:"<EMAIL>",autoCapitalize:"none",autoComplete:"email",autoCorrect:"off",disabled:o,...g("email")}),N.email&&(0,l.jsx)("p",{className:"text-sm text-destructive",children:N.email.message})]}),(0,l.jsxs)("div",{className:"grid gap-2",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)(x.J,{htmlFor:"password",children:e("auth.password")}),(0,l.jsx)(m.$,{variant:"link",className:"px-0",asChild:!0,children:(0,l.jsx)("a",{href:"/forgot-password",children:e("auth.forgotPassword")})})]}),(0,l.jsx)(u.p,{id:"password",type:"password",autoCapitalize:"none",autoComplete:"current-password",disabled:o,...g("password")}),N.password&&(0,l.jsx)("p",{className:"text-sm text-destructive",children:N.password.message})]}),(0,l.jsxs)(m.$,{type:"submit",disabled:o,children:[o&&(0,l.jsx)(h.F.spinner,{className:"mr-2 h-4 w-4 animate-spin"}),e("auth.signIn")]})]})}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,l.jsx)("span",{className:"w-full border-t"})}),(0,l.jsx)("div",{className:"relative flex justify-center text-xs uppercase",children:(0,l.jsx)("span",{className:"bg-card px-2 text-muted-foreground",children:e("auth.orContinueWith")})})]}),(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,l.jsxs)(m.$,{variant:"outline",type:"button",disabled:o,onClick:()=>(0,c.Jv)("github",{callbackUrl:a}),children:[(0,l.jsx)(h.F.gitHub,{className:"mr-2 h-4 w-4"}),"GitHub"]}),(0,l.jsxs)(m.$,{variant:"outline",type:"button",disabled:o,onClick:()=>(0,c.Jv)("google",{callbackUrl:a}),children:[(0,l.jsx)(h.F.google,{className:"mr-2 h-4 w-4"}),"Google"]})]}),(0,l.jsxs)("div",{className:"text-center text-sm",children:[e("auth.dontHaveAccount")," ",(0,l.jsx)(m.$,{variant:"link",className:"px-0",asChild:!0,children:(0,l.jsx)("a",{href:"/sign-up".concat(a?"?redirect_url=".concat(encodeURIComponent(a)):""),children:e("auth.signUp")})})]})]})}var g=a(73911),v=a(27971);function N(){let{t:e}=(0,r.B)();return(0,l.jsx)("div",{className:"container flex h-screen w-screen flex-col items-center justify-center",children:(0,l.jsxs)("div",{className:"mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]",children:[(0,l.jsxs)("div",{className:"flex flex-col space-y-2 text-center",children:[(0,l.jsx)(h.F.logo,{className:"mx-auto h-6 w-6"}),(0,l.jsx)("h1",{className:"text-2xl font-semibold tracking-tight",children:e("auth.welcomeBack")}),(0,l.jsx)("p",{className:"text-sm text-muted-foreground",children:e("auth.signInToContinue")})]}),(0,l.jsx)(f,{}),(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsx)(g.c,{}),(0,l.jsx)(v.U,{})]})]})})}function w(){return(0,l.jsx)(t.Suspense,{fallback:(0,l.jsx)("div",{className:"flex h-screen items-center justify-center",children:"Loading..."}),children:(0,l.jsx)(N,{})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[4277,5493,3692,1721,1342,7270,7535,8441,1684,7358],()=>s(40086)),_N_E=e.O()}]);