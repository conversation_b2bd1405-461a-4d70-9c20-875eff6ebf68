{"version": 3, "file": "query.js", "names": ["seconds", "dual", "globalValue", "BlockedRequests", "unsafeMakeWith", "core", "ensuring", "Listeners", "currentCache", "Symbol", "for", "fiberRefUnsafeMake", "map", "deferred<PERSON><PERSON>", "handle", "listeners", "currentCacheEnabled", "fromRequest", "request", "dataSource", "flatMap", "isEffect", "succeed", "ds", "fiberIdWith", "id", "proxy", "Proxy", "fiberRefGetWith", "cacheEnabled", "cached", "cache", "get<PERSON><PERSON><PERSON>", "orNew", "_tag", "left", "interrupted", "invalidate<PERSON><PERSON>", "entry", "increment", "uninterruptibleMask", "restore", "exit", "blocked", "empty", "deferred<PERSON><PERSON><PERSON>", "decrement", "right", "single", "makeEntry", "result", "ownerId", "state", "completed", "ref", "sync", "cacheRequest", "void", "deferredComplete", "withRequestCaching", "self", "strategy", "fiberRefLocally", "withRequestCache"], "sources": ["../../../src/internal/query.ts"], "sourcesContent": [null], "mappings": "AAEA,SAASA,OAAO,QAAQ,gBAAgB;AAExC,SAASC,IAAI,QAAQ,gBAAgB;AACrC,SAASC,WAAW,QAAQ,mBAAmB;AAG/C,OAAO,KAAKC,eAAe,MAAM,sBAAsB;AACvD,SAASC,cAAc,QAAQ,YAAY;AAC3C,OAAO,KAAKC,IAAI,MAAM,WAAW;AACjC,SAASC,QAAQ,QAAQ,mBAAmB;AAC5C,SAASC,SAAS,QAAQ,cAAc;AAOxC;AACA,OAAO,MAAMC,YAAY,gBAAGN,WAAW,eACrCO,MAAM,CAACC,GAAG,CAAC,8BAA8B,CAAC,EAC1C,MACEL,IAAI,CAACM,kBAAkB,CAAeP,cAAc,CAIlD,KAAK,EACL,MAAMC,IAAI,CAACO,GAAG,CAACP,IAAI,CAACQ,YAAY,EAAY,EAAGC,MAAM,KAAM;EAAEC,SAAS,EAAE,IAAIR,SAAS,EAAE;EAAEO;AAAM,CAAE,CAAC,CAAC,EACnG,MAAMd,OAAO,CAAC,EAAE,CAAC,CAClB,CAAC,CACL;AAED;AACA,OAAO,MAAMgB,mBAAmB,gBAAGd,WAAW,eAC5CO,MAAM,CAACC,GAAG,CAAC,qCAAqC,CAAC,EACjD,MAAML,IAAI,CAACM,kBAAkB,CAAC,KAAK,CAAC,CACrC;AAED;AACA,OAAO,MAAMM,WAAW,GAAGA,CAMzBC,OAAU,EACVC,UAAc,KAMdd,IAAI,CAACe,OAAO,CACTf,IAAI,CAACgB,QAAQ,CAACF,UAAU,CAAC,GAAGA,UAAU,GAAGd,IAAI,CAACiB,OAAO,CAACH,UAAU,CAAC,EAGjEI,EAAE,IACDlB,IAAI,CAACmB,WAAW,CAAEC,EAAE,IAAI;EACtB,MAAMC,KAAK,GAAG,IAAIC,KAAK,CAACT,OAAO,EAAE,EAAE,CAAC;EACpC,OAAOb,IAAI,CAACuB,eAAe,CAACZ,mBAAmB,EAAGa,YAAY,IAAI;IAChE,IAAIA,YAAY,EAAE;MAChB,MAAMC,MAAM,GAA4BzB,IAAI,CAACuB,eAAe,CAACpB,YAAY,EAAGuB,KAAK,IAC/E1B,IAAI,CAACe,OAAO,CAACW,KAAK,CAACC,SAAS,CAACN,KAAK,CAAC,EAAGO,KAAK,IAAI;QAC7C,QAAQA,KAAK,CAACC,IAAI;UAChB,KAAK,MAAM;YAAE;cACX,IAAID,KAAK,CAACE,IAAI,CAACpB,SAAS,CAACqB,WAAW,EAAE;gBACpC,OAAO/B,IAAI,CAACe,OAAO,CACjBW,KAAK,CAACM,cAAc,CAACX,KAAK,EAAGY,KAAK,IAAKA,KAAK,CAACxB,MAAM,KAAKmB,KAAK,CAACE,IAAI,CAACrB,MAAM,CAAC,EAC1E,MAAMgB,MAAM,CACb;cACH;cACAG,KAAK,CAACE,IAAI,CAACpB,SAAS,CAACwB,SAAS,EAAE;cAChC,OAAOlC,IAAI,CAACmC,mBAAmB,CAAEC,OAAO,IACtCpC,IAAI,CAACe,OAAO,CACVf,IAAI,CAACqC,IAAI,CAACrC,IAAI,CAACsC,OAAO,CACpBxC,eAAe,CAACyC,KAAK,EACrBH,OAAO,CAACpC,IAAI,CAACwC,aAAa,CAACZ,KAAK,CAACE,IAAI,CAACrB,MAAM,CAAC,CAAC,CAC/C,CAAC,EACD4B,IAAI,IAAI;gBACPT,KAAK,CAACE,IAAI,CAACpB,SAAS,CAAC+B,SAAS,EAAE;gBAChC,OAAOJ,IAAI;cACb,CAAC,CACF,CACF;YACH;UACA,KAAK,OAAO;YAAE;cACZT,KAAK,CAACc,KAAK,CAAChC,SAAS,CAACwB,SAAS,EAAE;cACjC,OAAOlC,IAAI,CAACmC,mBAAmB,CAAEC,OAAO,IACtCpC,IAAI,CAACe,OAAO,CACVf,IAAI,CAACqC,IAAI,CACPrC,IAAI,CAACsC,OAAO,CACVxC,eAAe,CAAC6C,MAAM,CACpBzB,EAAwC,EACxCpB,eAAe,CAAC8C,SAAS,CAAC;gBACxB/B,OAAO,EAAEQ,KAAK;gBACdwB,MAAM,EAAEjB,KAAK,CAACc,KAAK,CAACjC,MAAM;gBAC1BC,SAAS,EAAEkB,KAAK,CAACc,KAAK,CAAChC,SAAS;gBAChCoC,OAAO,EAAE1B,EAAE;gBACX2B,KAAK,EAAE;kBAAEC,SAAS,EAAE;gBAAK;eAC1B,CAAC,CACH,EACDZ,OAAO,CAACpC,IAAI,CAACwC,aAAa,CAACZ,KAAK,CAACc,KAAK,CAACjC,MAAM,CAAC,CAAC,CAChD,CACF,EACD,MAAK;gBACHmB,KAAK,CAACc,KAAK,CAAChC,SAAS,CAAC+B,SAAS,EAAE;gBACjC,OAAOzC,IAAI,CAACwC,aAAa,CAACZ,KAAK,CAACc,KAAK,CAACjC,MAAM,CAAC;cAC/C,CAAC,CACF,CACF;YACH;QACF;MACF,CAAC,CAAC,CAAC;MACL,OAAOgB,MAAM;IACf;IACA,MAAMf,SAAS,GAAG,IAAIR,SAAS,EAAE;IACjCQ,SAAS,CAACwB,SAAS,EAAE;IACrB,OAAOlC,IAAI,CAACe,OAAO,CACjBf,IAAI,CAACQ,YAAY,EAAwD,EACxEyC,GAAG,IACFhD,QAAQ,CACND,IAAI,CAACsC,OAAO,CACVxC,eAAe,CAAC6C,MAAM,CACpBzB,EAAwC,EACxCpB,eAAe,CAAC8C,SAAS,CAAC;MACxB/B,OAAO,EAAEQ,KAAK;MACdwB,MAAM,EAAEI,GAAG;MACXvC,SAAS;MACToC,OAAO,EAAE1B,EAAE;MACX2B,KAAK,EAAE;QAAEC,SAAS,EAAE;MAAK;KAC1B,CAAC,CACH,EACDhD,IAAI,CAACwC,aAAa,CAACS,GAAG,CAAC,CACxB,EACDjD,IAAI,CAACkD,IAAI,CAAC,MACRxC,SAAS,CAAC+B,SAAS,EAAE,CACtB,CACF,CACJ;EACH,CAAC,CAAC;AACJ,CAAC,CAAC,CACL;AAEH;AACA,OAAO,MAAMU,YAAY,GAAGA,CAC1BtC,OAAU,EACVgC,MAAiC,KACV;EACvB,OAAO7C,IAAI,CAACuB,eAAe,CAACZ,mBAAmB,EAAGa,YAAY,IAAI;IAChE,IAAIA,YAAY,EAAE;MAChB,OAAOxB,IAAI,CAACuB,eAAe,CAACpB,YAAY,EAAGuB,KAAK,IAC9C1B,IAAI,CAACe,OAAO,CAACW,KAAK,CAACC,SAAS,CAACd,OAAO,CAAC,EAAGe,KAAK,IAAI;QAC/C,QAAQA,KAAK,CAACC,IAAI;UAChB,KAAK,MAAM;YAAE;cACX,OAAO7B,IAAI,CAACoD,IAAI;YAClB;UACA,KAAK,OAAO;YAAE;cACZ,OAAOpD,IAAI,CAACqD,gBAAgB,CAACzB,KAAK,CAACc,KAAK,CAACjC,MAAM,EAAEoC,MAAM,CAAC;YAC1D;QACF;MACF,CAAC,CAAC,CAAC;IACP;IACA,OAAO7C,IAAI,CAACoD,IAAI;EAClB,CAAC,CAAC;AACJ,CAAC;AAED;AACA,OAAO,MAAME,kBAAkB,gBAM3B1D,IAAI,CAQN,CAAC,EAAE,CAAC2D,IAAI,EAAEC,QAAQ,KAAKxD,IAAI,CAACyD,eAAe,CAACF,IAAI,EAAE5C,mBAAmB,EAAE6C,QAAQ,CAAC,CAAC;AAEnF;AACA,OAAO,MAAME,gBAAgB,gBAMzB9D,IAAI,CASN,CAAC;AACD;AACA,CAAC2D,IAAI,EAAE7B,KAAK,KAAK1B,IAAI,CAACyD,eAAe,CAACF,IAAI,EAAEpD,YAAY,EAAEuB,KAAK,CAAC,CACjE", "ignoreList": []}