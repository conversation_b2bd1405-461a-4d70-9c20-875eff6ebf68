"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.isWorkerError = exports.WorkerErrorTypeId = exports.WorkerError = void 0;
var Predicate = _interopRequireWildcard(require("effect/Predicate"));
var Schema = _interopRequireWildcard(require("effect/Schema"));
var internal = _interopRequireWildcard(require("./internal/workerError.js"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
/**
 * @since 1.0.0
 * @category type ids
 */
const WorkerErrorTypeId = exports.WorkerErrorTypeId = internal.WorkerErrorTypeId;
/**
 * @since 1.0.0
 * @category predicates
 */
const isWorkerError = u => Predicate.hasProperty(u, WorkerErrorTypeId);
/**
 * @since 1.0.0
 * @category errors
 */
exports.isWorkerError = isWorkerError;
class WorkerError extends /*#__PURE__*/Schema.TaggedError()("WorkerError", {
  reason: /*#__PURE__*/Schema.Literal("spawn", "decode", "send", "unknown", "encode"),
  cause: Schema.Defect
}) {
  /**
   * @since 1.0.0
   */
  [WorkerErrorTypeId] = WorkerErrorTypeId;
  /**
   * @since 1.0.0
   */
  static Cause = /*#__PURE__*/Schema.Cause({
    error: this,
    defect: Schema.Defect
  });
  /**
   * @since 1.0.0
   */
  static encodeCause = /*#__PURE__*/Schema.encodeSync(this.Cause);
  /**
   * @since 1.0.0
   */
  static decodeCause = /*#__PURE__*/Schema.decodeSync(this.Cause);
  /**
   * @since 1.0.0
   */
  get message() {
    switch (this.reason) {
      case "send":
        return "An error occurred calling .postMessage";
      case "spawn":
        return "An error occurred while spawning a worker";
      case "decode":
        return "An error occurred during decoding";
      case "encode":
        return "An error occurred during encoding";
      case "unknown":
        return "An unexpected error occurred";
    }
  }
}
exports.WorkerError = WorkerError;
//# sourceMappingURL=WorkerError.js.map