"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/properties/edit/[id]/page",{

/***/ "(app-pages-browser)/./app/dashboard/properties/edit/[id]/page.tsx":
/*!*****************************************************!*\
  !*** ./app/dashboard/properties/edit/[id]/page.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EditPropertyPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronRight_Home_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronRight,Home,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronRight_Home_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronRight,Home,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronRight_Home_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronRight,Home,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronRight_Home_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronRight,Home,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useSimpleLanguage */ \"(app-pages-browser)/./hooks/useSimpleLanguage.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _create_property_form_steps__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../create/property-form-steps */ \"(app-pages-browser)/./app/dashboard/properties/create/property-form-steps.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction EditPropertyPage() {\n    var _property_price, _property_bedrooms, _property_bathrooms, _property_area, _property_yearBuilt, _property_parking;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const { language } = (0,_hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_5__.useSimpleLanguage)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [initialLoading, setInitialLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [property, setProperty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const translations = {\n        ar: {\n            editProperty: 'تعديل العقار',\n            backToProperties: 'العودة إلى العقارات',\n            subtitle: 'تحديث معلومات وتفاصيل العقار',\n            properties: 'العقارات',\n            home: 'الرئيسية',\n            loading: 'جاري تحميل العقار...',\n            notFound: 'العقار غير موجود',\n            loadingError: 'فشل في تحميل العقار'\n        },\n        en: {\n            editProperty: 'Edit Property',\n            backToProperties: 'Back to Properties',\n            subtitle: 'Update property information and details',\n            properties: 'Properties',\n            home: 'Home',\n            loading: 'Loading property...',\n            notFound: 'Property not found',\n            loadingError: 'Failed to load property'\n        }\n    };\n    const t = translations[language] || translations.ar;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditPropertyPage.useEffect\": ()=>{\n            const fetchProperty = {\n                \"EditPropertyPage.useEffect.fetchProperty\": async ()=>{\n                    if (!params.id) return;\n                    try {\n                        setInitialLoading(true);\n                        const response = await fetch(\"/api/v1/properties/\".concat(params.id));\n                        if (response.ok) {\n                            const data = await response.json();\n                            if (data.success) {\n                                setProperty(data.data);\n                            } else {\n                                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error(t.notFound);\n                                router.push('/dashboard/properties');\n                            }\n                        } else {\n                            // Fallback to mock data for testing\n                            console.log('Backend not available, using mock data');\n                            setProperty({\n                                id: params.id,\n                                title: 'Luxury Villa in Dubai Marina',\n                                titleAr: 'فيلا فاخرة في دبي مارينا',\n                                description: 'Beautiful 4-bedroom villa with sea view',\n                                descriptionAr: 'فيلا جميلة من 4 غرف نوم مع إطلالة على البحر',\n                                price: 2500000,\n                                currency: 'AED',\n                                type: 'VILLA',\n                                status: 'AVAILABLE',\n                                bedrooms: 4,\n                                bathrooms: 3,\n                                area: 350,\n                                location: 'Dubai Marina',\n                                locationAr: 'دبي مارينا',\n                                address: '123 Marina Walk',\n                                addressAr: '123 ممشى المارينا',\n                                city: 'Dubai',\n                                cityAr: 'دبي',\n                                country: 'UAE',\n                                countryAr: 'الإمارات العربية المتحدة',\n                                images: [\n                                    '/placeholder.jpg'\n                                ],\n                                features: [\n                                    'Swimming Pool',\n                                    'Gym',\n                                    'Parking'\n                                ],\n                                featuresAr: [\n                                    'مسبح',\n                                    'صالة رياضية',\n                                    'موقف سيارات'\n                                ],\n                                amenities: [\n                                    '24/7 Security',\n                                    'Concierge'\n                                ],\n                                amenitiesAr: [\n                                    'أمن 24/7',\n                                    'خدمة الكونسيرج'\n                                ],\n                                yearBuilt: 2020,\n                                parking: 2,\n                                furnished: true,\n                                petFriendly: false,\n                                utilities: 'Electricity, Water, Internet',\n                                utilitiesAr: 'كهرباء، ماء، إنترنت',\n                                contactInfo: 'Contact: +971 50 123 4567',\n                                isFeatured: true,\n                                isActive: true\n                            });\n                        }\n                    } catch (error) {\n                        console.error('Error fetching property:', error);\n                        sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error(t.loadingError);\n                        router.push('/dashboard/properties');\n                    } finally{\n                        setInitialLoading(false);\n                    }\n                }\n            }[\"EditPropertyPage.useEffect.fetchProperty\"];\n            fetchProperty();\n        }\n    }[\"EditPropertyPage.useEffect\"], [\n        params.id,\n        router,\n        t.loadingError,\n        t.notFound\n    ]);\n    const handleSave = async (formData)=>{\n        setLoading(true);\n        try {\n            const payload = {\n                ...formData,\n                price: parseFloat(formData.price),\n                bedrooms: formData.bedrooms ? parseInt(formData.bedrooms) : undefined,\n                bathrooms: formData.bathrooms ? parseInt(formData.bathrooms) : undefined,\n                area: formData.area ? parseFloat(formData.area) : undefined,\n                yearBuilt: formData.yearBuilt ? parseInt(formData.yearBuilt) : undefined,\n                parking: formData.parking ? parseInt(formData.parking) : undefined\n            };\n            const response = await fetch(\"/api/v1/properties/\".concat(params.id), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(payload)\n            });\n            if (response.ok) {\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success(language === 'ar' ? 'تم تحديث العقار بنجاح' : 'Property updated successfully');\n                router.push('/dashboard/properties');\n            } else {\n                const error = await response.json();\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error(error.message || (language === 'ar' ? 'فشل في تحديث العقار' : 'Failed to update property'));\n            }\n        } catch (error) {\n            console.error('Error updating property:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error(language === 'ar' ? 'فشل في تحديث العقار' : 'Failed to update property');\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (initialLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 \".concat(language === 'ar' ? 'rtl' : 'ltr'),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    className: \"shadow-xl border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"flex items-center justify-center py-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronRight_Home_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-8 w-8 animate-spin mx-auto mb-4 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\edit\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-400\",\n                                    children: t.loading\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\edit\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\edit\\\\[id]\\\\page.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\edit\\\\[id]\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\edit\\\\[id]\\\\page.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\edit\\\\[id]\\\\page.tsx\",\n                lineNumber: 189,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\edit\\\\[id]\\\\page.tsx\",\n            lineNumber: 188,\n            columnNumber: 7\n        }, this);\n    }\n    if (!property) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 \".concat(language === 'ar' ? 'rtl' : 'ltr'),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    className: \"shadow-xl border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"flex items-center justify-center py-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-400 mb-4\",\n                                    children: t.notFound\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\edit\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>router.push('/dashboard/properties'),\n                                    children: t.backToProperties\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\edit\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\edit\\\\[id]\\\\page.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\edit\\\\[id]\\\\page.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\edit\\\\[id]\\\\page.tsx\",\n                    lineNumber: 207,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\edit\\\\[id]\\\\page.tsx\",\n                lineNumber: 206,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\edit\\\\[id]\\\\page.tsx\",\n            lineNumber: 205,\n            columnNumber: 7\n        }, this);\n    }\n    // Convert property data to form format\n    const initialFormData = {\n        title: property.title || '',\n        titleAr: property.titleAr || '',\n        description: property.description || '',\n        descriptionAr: property.descriptionAr || '',\n        price: ((_property_price = property.price) === null || _property_price === void 0 ? void 0 : _property_price.toString()) || '',\n        currency: property.currency || 'AED',\n        type: property.type || 'APARTMENT',\n        status: property.status || 'AVAILABLE',\n        bedrooms: ((_property_bedrooms = property.bedrooms) === null || _property_bedrooms === void 0 ? void 0 : _property_bedrooms.toString()) || '',\n        bathrooms: ((_property_bathrooms = property.bathrooms) === null || _property_bathrooms === void 0 ? void 0 : _property_bathrooms.toString()) || '',\n        area: ((_property_area = property.area) === null || _property_area === void 0 ? void 0 : _property_area.toString()) || '',\n        location: property.location || '',\n        locationAr: property.locationAr || '',\n        address: property.address || '',\n        addressAr: property.addressAr || '',\n        city: property.city || '',\n        cityAr: property.cityAr || '',\n        country: property.country || 'UAE',\n        countryAr: property.countryAr || 'الإمارات العربية المتحدة',\n        images: property.images || [],\n        features: property.features || [],\n        featuresAr: property.featuresAr || [],\n        amenities: property.amenities || [],\n        amenitiesAr: property.amenitiesAr || [],\n        yearBuilt: ((_property_yearBuilt = property.yearBuilt) === null || _property_yearBuilt === void 0 ? void 0 : _property_yearBuilt.toString()) || '',\n        parking: ((_property_parking = property.parking) === null || _property_parking === void 0 ? void 0 : _property_parking.toString()) || '',\n        furnished: property.furnished || false,\n        petFriendly: property.petFriendly || false,\n        utilities: property.utilities || '',\n        utilitiesAr: property.utilitiesAr || '',\n        contactInfo: property.contactInfo || '',\n        isFeatured: property.isFeatured || false,\n        isActive: property.isActive !== undefined ? property.isActive : true\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 \".concat(language === 'ar' ? 'rtl' : 'ltr'),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400 mb-8\",\n                    dir: language === 'ar' ? 'rtl' : 'ltr',\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronRight_Home_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\edit\\\\[id]\\\\page.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronRight_Home_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\edit\\\\[id]\\\\page.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.push('/dashboard/properties'),\n                            className: \"hover:text-blue-600 dark:hover:text-blue-400 transition-colors\",\n                            children: t.properties\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\edit\\\\[id]\\\\page.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronRight_Home_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\edit\\\\[id]\\\\page.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-gray-900 dark:text-gray-100\",\n                            children: t.editProperty\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\edit\\\\[id]\\\\page.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\edit\\\\[id]\\\\page.tsx\",\n                    lineNumber: 263,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900 dark:text-white mb-2\",\n                                    children: t.editProperty\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\edit\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-400 max-w-2xl\",\n                                    children: t.subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\edit\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\edit\\\\[id]\\\\page.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            onClick: ()=>router.push('/dashboard/properties'),\n                            className: \"flex items-center gap-2 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronRight_Home_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\edit\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 13\n                                }, this),\n                                t.backToProperties\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\edit\\\\[id]\\\\page.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\edit\\\\[id]\\\\page.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_create_property_form_steps__WEBPACK_IMPORTED_MODULE_7__.PropertyFormSteps, {\n                    onSave: handleSave,\n                    loading: loading,\n                    initialData: initialFormData,\n                    isEdit: true,\n                    propertyId: params.id\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\edit\\\\[id]\\\\page.tsx\",\n                    lineNumber: 297,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\edit\\\\[id]\\\\page.tsx\",\n            lineNumber: 261,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\edit\\\\[id]\\\\page.tsx\",\n        lineNumber: 260,\n        columnNumber: 5\n    }, this);\n}\n_s(EditPropertyPage, \"Op+3afJh9r8IdXFpv8uuyn8NeAs=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_5__.useSimpleLanguage\n    ];\n});\n_c = EditPropertyPage;\nvar _c;\n$RefreshReg$(_c, \"EditPropertyPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/properties/edit/[id]/page.tsx\n"));

/***/ })

});