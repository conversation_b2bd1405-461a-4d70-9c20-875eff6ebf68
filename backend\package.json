{"name": "express-api-starter-ts", "version": "1.2.0", "description": " A basic starter for an express.js API with Typescript", "main": "src/index.ts", "scripts": {"start": "ts-node src/index.ts", "dev": "nodemon", "build": "tsc", "start:dist": "node dist/src/index.js", "lint": "eslint --fix src test", "test": "jest", "typecheck": "tsc --noEmit"}, "keywords": [], "author": "<PERSON><PERSON> <PERSON>. <<EMAIL>> (https://w3cj.now.sh)", "repository": {"type": "git", "url": "https://github.com/w3cj/express-api-starter.git"}, "license": "MIT", "dependencies": {"@prisma/client": "^6.7.0", "axios": "^1.9.0", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.21.1", "helmet": "^8.0.0", "morgan": "^1.10.0", "uploadthing": "^7.4.0"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.20", "@types/jest": "^29.5.13", "@types/morgan": "^1.9.9", "@types/node": "^22.7.5", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^7.16.1", "@typescript-eslint/parser": "^7.16.1", "eslint": "^8.57.0", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-import-resolver-typescript": "^3.6.3", "eslint-plugin-import": "^2.31.0", "jest": "^29.7.0", "nodemon": "^3.1.7", "prisma": "^6.7.0", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "^5.6.3"}}