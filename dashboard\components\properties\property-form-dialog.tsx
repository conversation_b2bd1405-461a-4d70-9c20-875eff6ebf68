'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { X, Upload, Plus } from 'lucide-react';
import { useLanguage } from '@/hooks/useLanguage';
import { UploadDropzone } from '@/lib/uploadthing';

interface Property {
  id: string;
  title: string;
  titleAr?: string;
  description: string;
  descriptionAr?: string;
  price: number;
  currency: string;
  type: string;
  status: string;
  bedrooms?: number;
  bathrooms?: number;
  area?: number;
  location: string;
  locationAr?: string;
  address: string;
  addressAr?: string;
  city: string;
  cityAr?: string;
  images: string[];
  features: string[];
  featuresAr: string[];
  amenities: string[];
  amenitiesAr: string[];
  yearBuilt?: number;
  parking?: number;
  furnished: boolean;
  petFriendly: boolean;
  utilities?: string;
  utilitiesAr?: string;
  contactInfo?: string;
  isFeatured: boolean;
  isActive: boolean;
}

interface PropertyFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  property?: Property | null;
  onSuccess: () => void;
}

export default function PropertyFormDialog({
  open,
  onOpenChange,
  property,
  onSuccess,
}: PropertyFormDialogProps) {
  const { language } = useLanguage();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    titleAr: '',
    description: '',
    descriptionAr: '',
    price: '',
    currency: 'USD',
    type: '',
    status: 'AVAILABLE',
    bedrooms: '',
    bathrooms: '',
    area: '',
    location: '',
    locationAr: '',
    address: '',
    addressAr: '',
    city: '',
    cityAr: '',
    images: [] as string[],
    features: [] as string[],
    featuresAr: [] as string[],
    amenities: [] as string[],
    amenitiesAr: [] as string[],
    yearBuilt: '',
    parking: '',
    furnished: false,
    petFriendly: false,
    utilities: '',
    utilitiesAr: '',
    contactInfo: '',
    isFeatured: false,
    isActive: true,
  });
  const [newFeature, setNewFeature] = useState('');
  const [newFeatureAr, setNewFeatureAr] = useState('');
  const [newAmenity, setNewAmenity] = useState('');
  const [newAmenityAr, setNewAmenityAr] = useState('');

  // Reset form when dialog opens/closes or property changes
  useEffect(() => {
    if (open) {
      if (property) {
        setFormData({
          title: property.title || '',
          titleAr: property.titleAr || '',
          description: property.description || '',
          descriptionAr: property.descriptionAr || '',
          price: property.price?.toString() || '',
          currency: property.currency || 'USD',
          type: property.type || '',
          status: property.status || 'AVAILABLE',
          bedrooms: property.bedrooms?.toString() || '',
          bathrooms: property.bathrooms?.toString() || '',
          area: property.area?.toString() || '',
          location: property.location || '',
          locationAr: property.locationAr || '',
          address: property.address || '',
          addressAr: property.addressAr || '',
          city: property.city || '',
          cityAr: property.cityAr || '',
          images: property.images || [],
          features: property.features || [],
          featuresAr: property.featuresAr || [],
          amenities: property.amenities || [],
          amenitiesAr: property.amenitiesAr || [],
          yearBuilt: property.yearBuilt?.toString() || '',
          parking: property.parking?.toString() || '',
          furnished: property.furnished || false,
          petFriendly: property.petFriendly || false,
          utilities: property.utilities || '',
          utilitiesAr: property.utilitiesAr || '',
          contactInfo: property.contactInfo || '',
          isFeatured: property.isFeatured || false,
          isActive: property.isActive !== undefined ? property.isActive : true,
        });
      } else {
        // Reset form for new property
        setFormData({
          title: '',
          titleAr: '',
          description: '',
          descriptionAr: '',
          price: '',
          currency: 'USD',
          type: '',
          status: 'AVAILABLE',
          bedrooms: '',
          bathrooms: '',
          area: '',
          location: '',
          locationAr: '',
          address: '',
          addressAr: '',
          city: '',
          cityAr: '',
          images: [],
          features: [],
          featuresAr: [],
          amenities: [],
          amenitiesAr: [],
          yearBuilt: '',
          parking: '',
          furnished: false,
          petFriendly: false,
          utilities: '',
          utilitiesAr: '',
          contactInfo: '',
          isFeatured: false,
          isActive: true,
        });
      }
    }
  }, [open, property]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const payload = {
        ...formData,
        price: parseFloat(formData.price),
        bedrooms: formData.bedrooms ? parseInt(formData.bedrooms) : undefined,
        bathrooms: formData.bathrooms ? parseInt(formData.bathrooms) : undefined,
        area: formData.area ? parseFloat(formData.area) : undefined,
        yearBuilt: formData.yearBuilt ? parseInt(formData.yearBuilt) : undefined,
        parking: formData.parking ? parseInt(formData.parking) : undefined,
      };

      const url = property ? `/api/v1/properties/${property.id}` : '/api/v1/properties';
      const method = property ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (response.ok) {
        onSuccess();
        onOpenChange(false);
      } else {
        const error = await response.json();
        alert(error.message || 'Failed to save property');
      }
    } catch (error) {
      console.error('Error saving property:', error);
      alert('Failed to save property');
    } finally {
      setLoading(false);
    }
  };

  const addFeature = () => {
    if (newFeature.trim()) {
      setFormData(prev => ({
        ...prev,
        features: [...prev.features, newFeature.trim()],
      }));
      setNewFeature('');
    }
  };

  const addFeatureAr = () => {
    if (newFeatureAr.trim()) {
      setFormData(prev => ({
        ...prev,
        featuresAr: [...prev.featuresAr, newFeatureAr.trim()],
      }));
      setNewFeatureAr('');
    }
  };

  const addAmenity = () => {
    if (newAmenity.trim()) {
      setFormData(prev => ({
        ...prev,
        amenities: [...prev.amenities, newAmenity.trim()],
      }));
      setNewAmenity('');
    }
  };

  const addAmenityAr = () => {
    if (newAmenityAr.trim()) {
      setFormData(prev => ({
        ...prev,
        amenitiesAr: [...prev.amenitiesAr, newAmenityAr.trim()],
      }));
      setNewAmenityAr('');
    }
  };

  const removeFeature = (index: number) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.filter((_, i) => i !== index),
    }));
  };

  const removeFeatureAr = (index: number) => {
    setFormData(prev => ({
      ...prev,
      featuresAr: prev.featuresAr.filter((_, i) => i !== index),
    }));
  };

  const removeAmenity = (index: number) => {
    setFormData(prev => ({
      ...prev,
      amenities: prev.amenities.filter((_, i) => i !== index),
    }));
  };

  const removeAmenityAr = (index: number) => {
    setFormData(prev => ({
      ...prev,
      amenitiesAr: prev.amenitiesAr.filter((_, i) => i !== index),
    }));
  };

  const removeImage = (index: number) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index),
    }));
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {property
              ? (language === 'ar' ? 'تعديل العقار' : 'Edit Property')
              : (language === 'ar' ? 'إضافة عقار جديد' : 'Add New Property')
            }
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="title">
                {language === 'ar' ? 'العنوان (إنجليزي)' : 'Title (English)'}
              </Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                required
              />
            </div>
            <div>
              <Label htmlFor="titleAr">
                {language === 'ar' ? 'العنوان (عربي)' : 'Title (Arabic)'}
              </Label>
              <Input
                id="titleAr"
                value={formData.titleAr}
                onChange={(e) => setFormData(prev => ({ ...prev, titleAr: e.target.value }))}
                dir="rtl"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="description">
                {language === 'ar' ? 'الوصف (إنجليزي)' : 'Description (English)'}
              </Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                required
                rows={3}
              />
            </div>
            <div>
              <Label htmlFor="descriptionAr">
                {language === 'ar' ? 'الوصف (عربي)' : 'Description (Arabic)'}
              </Label>
              <Textarea
                id="descriptionAr"
                value={formData.descriptionAr}
                onChange={(e) => setFormData(prev => ({ ...prev, descriptionAr: e.target.value }))}
                dir="rtl"
                rows={3}
              />
            </div>
          </div>

          {/* Price and Type */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label htmlFor="price">
                {language === 'ar' ? 'السعر' : 'Price'}
              </Label>
              <Input
                id="price"
                type="number"
                value={formData.price}
                onChange={(e) => setFormData(prev => ({ ...prev, price: e.target.value }))}
                required
              />
            </div>
            <div>
              <Label htmlFor="currency">
                {language === 'ar' ? 'العملة' : 'Currency'}
              </Label>
              <Select value={formData.currency} onValueChange={(value) => setFormData(prev => ({ ...prev, currency: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="USD">USD</SelectItem>
                  <SelectItem value="AED">AED</SelectItem>
                  <SelectItem value="EUR">EUR</SelectItem>
                  <SelectItem value="GBP">GBP</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="type">
                {language === 'ar' ? 'نوع العقار' : 'Property Type'}
              </Label>
              <Select value={formData.type} onValueChange={(value) => setFormData(prev => ({ ...prev, type: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder={language === 'ar' ? 'اختر النوع' : 'Select Type'} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="APARTMENT">{language === 'ar' ? 'شقة' : 'Apartment'}</SelectItem>
                  <SelectItem value="VILLA">{language === 'ar' ? 'فيلا' : 'Villa'}</SelectItem>
                  <SelectItem value="TOWNHOUSE">{language === 'ar' ? 'تاون هاوس' : 'Townhouse'}</SelectItem>
                  <SelectItem value="PENTHOUSE">{language === 'ar' ? 'بنتهاوس' : 'Penthouse'}</SelectItem>
                  <SelectItem value="STUDIO">{language === 'ar' ? 'استوديو' : 'Studio'}</SelectItem>
                  <SelectItem value="OFFICE">{language === 'ar' ? 'مكتب' : 'Office'}</SelectItem>
                  <SelectItem value="SHOP">{language === 'ar' ? 'محل' : 'Shop'}</SelectItem>
                  <SelectItem value="WAREHOUSE">{language === 'ar' ? 'مستودع' : 'Warehouse'}</SelectItem>
                  <SelectItem value="LAND">{language === 'ar' ? 'أرض' : 'Land'}</SelectItem>
                  <SelectItem value="BUILDING">{language === 'ar' ? 'مبنى' : 'Building'}</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="status">
                {language === 'ar' ? 'الحالة' : 'Status'}
              </Label>
              <Select value={formData.status} onValueChange={(value) => setFormData(prev => ({ ...prev, status: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="AVAILABLE">{language === 'ar' ? 'متاح' : 'Available'}</SelectItem>
                  <SelectItem value="SOLD">{language === 'ar' ? 'مباع' : 'Sold'}</SelectItem>
                  <SelectItem value="RENTED">{language === 'ar' ? 'مؤجر' : 'Rented'}</SelectItem>
                  <SelectItem value="RESERVED">{language === 'ar' ? 'محجوز' : 'Reserved'}</SelectItem>
                  <SelectItem value="OFF_MARKET">{language === 'ar' ? 'خارج السوق' : 'Off Market'}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Property Details */}
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
              <Label htmlFor="bedrooms">
                {language === 'ar' ? 'غرف النوم' : 'Bedrooms'}
              </Label>
              <Input
                id="bedrooms"
                type="number"
                value={formData.bedrooms}
                onChange={(e) => setFormData(prev => ({ ...prev, bedrooms: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="bathrooms">
                {language === 'ar' ? 'الحمامات' : 'Bathrooms'}
              </Label>
              <Input
                id="bathrooms"
                type="number"
                value={formData.bathrooms}
                onChange={(e) => setFormData(prev => ({ ...prev, bathrooms: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="area">
                {language === 'ar' ? 'المساحة (م²)' : 'Area (m²)'}
              </Label>
              <Input
                id="area"
                type="number"
                value={formData.area}
                onChange={(e) => setFormData(prev => ({ ...prev, area: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="yearBuilt">
                {language === 'ar' ? 'سنة البناء' : 'Year Built'}
              </Label>
              <Input
                id="yearBuilt"
                type="number"
                value={formData.yearBuilt}
                onChange={(e) => setFormData(prev => ({ ...prev, yearBuilt: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="parking">
                {language === 'ar' ? 'مواقف السيارات' : 'Parking Spaces'}
              </Label>
              <Input
                id="parking"
                type="number"
                value={formData.parking}
                onChange={(e) => setFormData(prev => ({ ...prev, parking: e.target.value }))}
              />
            </div>
          </div>

          {/* Location */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="location">
                {language === 'ar' ? 'الموقع (إنجليزي)' : 'Location (English)'}
              </Label>
              <Input
                id="location"
                value={formData.location}
                onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                required
              />
            </div>
            <div>
              <Label htmlFor="locationAr">
                {language === 'ar' ? 'الموقع (عربي)' : 'Location (Arabic)'}
              </Label>
              <Input
                id="locationAr"
                value={formData.locationAr}
                onChange={(e) => setFormData(prev => ({ ...prev, locationAr: e.target.value }))}
                dir="rtl"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="address">
                {language === 'ar' ? 'العنوان (إنجليزي)' : 'Address (English)'}
              </Label>
              <Input
                id="address"
                value={formData.address}
                onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                required
              />
            </div>
            <div>
              <Label htmlFor="addressAr">
                {language === 'ar' ? 'العنوان (عربي)' : 'Address (Arabic)'}
              </Label>
              <Input
                id="addressAr"
                value={formData.addressAr}
                onChange={(e) => setFormData(prev => ({ ...prev, addressAr: e.target.value }))}
                dir="rtl"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="city">
                {language === 'ar' ? 'المدينة (إنجليزي)' : 'City (English)'}
              </Label>
              <Input
                id="city"
                value={formData.city}
                onChange={(e) => setFormData(prev => ({ ...prev, city: e.target.value }))}
                required
              />
            </div>
            <div>
              <Label htmlFor="cityAr">
                {language === 'ar' ? 'المدينة (عربي)' : 'City (Arabic)'}
              </Label>
              <Input
                id="cityAr"
                value={formData.cityAr}
                onChange={(e) => setFormData(prev => ({ ...prev, cityAr: e.target.value }))}
                dir="rtl"
              />
            </div>
          </div>

          {/* Images Upload */}
          <div>
            <Label>
              {language === 'ar' ? 'صور العقار' : 'Property Images'}
            </Label>
            <div className="mt-2">
              <UploadDropzone
                endpoint="propertyImageUploader"
                onClientUploadComplete={(res) => {
                  if (res) {
                    const newImages = res.map(file => file.url);
                    setFormData(prev => ({
                      ...prev,
                      images: [...prev.images, ...newImages],
                    }));
                  }
                }}
                onUploadError={(error: Error) => {
                  alert(`Upload failed: ${error.message}`);
                }}
              />
            </div>
            {formData.images.length > 0 && (
              <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
                {formData.images.map((image, index) => (
                  <div key={index} className="relative">
                    <img
                      src={image}
                      alt={`Property image ${index + 1}`}
                      className="w-full h-24 object-cover rounded"
                    />
                    <Button
                      type="button"
                      variant="destructive"
                      size="sm"
                      className="absolute top-1 right-1 h-6 w-6 p-0"
                      onClick={() => removeImage(index)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Features */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label>
                {language === 'ar' ? 'المميزات (إنجليزي)' : 'Features (English)'}
              </Label>
              <div className="flex gap-2 mt-2">
                <Input
                  value={newFeature}
                  onChange={(e) => setNewFeature(e.target.value)}
                  placeholder={language === 'ar' ? 'أضف ميزة' : 'Add feature'}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addFeature())}
                />
                <Button type="button" onClick={addFeature} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex flex-wrap gap-2 mt-2">
                {formData.features.map((feature, index) => (
                  <Badge key={index} variant="secondary" className="flex items-center gap-1">
                    {feature}
                    <X
                      className="h-3 w-3 cursor-pointer"
                      onClick={() => removeFeature(index)}
                    />
                  </Badge>
                ))}
              </div>
            </div>
            <div>
              <Label>
                {language === 'ar' ? 'المميزات (عربي)' : 'Features (Arabic)'}
              </Label>
              <div className="flex gap-2 mt-2">
                <Input
                  value={newFeatureAr}
                  onChange={(e) => setNewFeatureAr(e.target.value)}
                  placeholder={language === 'ar' ? 'أضف ميزة' : 'Add feature'}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addFeatureAr())}
                  dir="rtl"
                />
                <Button type="button" onClick={addFeatureAr} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex flex-wrap gap-2 mt-2">
                {formData.featuresAr.map((feature, index) => (
                  <Badge key={index} variant="secondary" className="flex items-center gap-1">
                    {feature}
                    <X
                      className="h-3 w-3 cursor-pointer"
                      onClick={() => removeFeatureAr(index)}
                    />
                  </Badge>
                ))}
              </div>
            </div>
          </div>

          {/* Amenities */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label>
                {language === 'ar' ? 'المرافق (إنجليزي)' : 'Amenities (English)'}
              </Label>
              <div className="flex gap-2 mt-2">
                <Input
                  value={newAmenity}
                  onChange={(e) => setNewAmenity(e.target.value)}
                  placeholder={language === 'ar' ? 'أضف مرفق' : 'Add amenity'}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addAmenity())}
                />
                <Button type="button" onClick={addAmenity} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex flex-wrap gap-2 mt-2">
                {formData.amenities.map((amenity, index) => (
                  <Badge key={index} variant="secondary" className="flex items-center gap-1">
                    {amenity}
                    <X
                      className="h-3 w-3 cursor-pointer"
                      onClick={() => removeAmenity(index)}
                    />
                  </Badge>
                ))}
              </div>
            </div>
            <div>
              <Label>
                {language === 'ar' ? 'المرافق (عربي)' : 'Amenities (Arabic)'}
              </Label>
              <div className="flex gap-2 mt-2">
                <Input
                  value={newAmenityAr}
                  onChange={(e) => setNewAmenityAr(e.target.value)}
                  placeholder={language === 'ar' ? 'أضف مرفق' : 'Add amenity'}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addAmenityAr())}
                  dir="rtl"
                />
                <Button type="button" onClick={addAmenityAr} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex flex-wrap gap-2 mt-2">
                {formData.amenitiesAr.map((amenity, index) => (
                  <Badge key={index} variant="secondary" className="flex items-center gap-1">
                    {amenity}
                    <X
                      className="h-3 w-3 cursor-pointer"
                      onClick={() => removeAmenityAr(index)}
                    />
                  </Badge>
                ))}
              </div>
            </div>
          </div>

          {/* Additional Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="utilities">
                {language === 'ar' ? 'المرافق المشمولة (إنجليزي)' : 'Utilities Included (English)'}
              </Label>
              <Textarea
                id="utilities"
                value={formData.utilities}
                onChange={(e) => setFormData(prev => ({ ...prev, utilities: e.target.value }))}
                rows={2}
              />
            </div>
            <div>
              <Label htmlFor="utilitiesAr">
                {language === 'ar' ? 'المرافق المشمولة (عربي)' : 'Utilities Included (Arabic)'}
              </Label>
              <Textarea
                id="utilitiesAr"
                value={formData.utilitiesAr}
                onChange={(e) => setFormData(prev => ({ ...prev, utilitiesAr: e.target.value }))}
                rows={2}
                dir="rtl"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="contactInfo">
              {language === 'ar' ? 'معلومات الاتصال' : 'Contact Information'}
            </Label>
            <Textarea
              id="contactInfo"
              value={formData.contactInfo}
              onChange={(e) => setFormData(prev => ({ ...prev, contactInfo: e.target.value }))}
              rows={2}
            />
          </div>

          {/* Switches */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="furnished"
                checked={formData.furnished}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, furnished: checked }))}
              />
              <Label htmlFor="furnished">
                {language === 'ar' ? 'مفروش' : 'Furnished'}
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="petFriendly"
                checked={formData.petFriendly}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, petFriendly: checked }))}
              />
              <Label htmlFor="petFriendly">
                {language === 'ar' ? 'يسمح بالحيوانات' : 'Pet Friendly'}
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="isFeatured"
                checked={formData.isFeatured}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isFeatured: checked }))}
              />
              <Label htmlFor="isFeatured">
                {language === 'ar' ? 'مميز' : 'Featured'}
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="isActive"
                checked={formData.isActive}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: checked }))}
              />
              <Label htmlFor="isActive">
                {language === 'ar' ? 'نشط' : 'Active'}
              </Label>
            </div>
          </div>

          {/* Submit Buttons */}
          <div className="flex justify-end gap-4 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={loading}
            >
              {language === 'ar' ? 'إلغاء' : 'Cancel'}
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              ) : null}
              {property
                ? (language === 'ar' ? 'تحديث العقار' : 'Update Property')
                : (language === 'ar' ? 'إضافة العقار' : 'Add Property')
              }
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}