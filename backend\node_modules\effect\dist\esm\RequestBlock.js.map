{"version": 3, "file": "RequestBlock.js", "names": ["RequestBlock_", "single", "empty", "mapRequestResolvers", "parallel", "par", "reduce", "sequential", "seq"], "sources": ["../../src/RequestBlock.ts"], "sourcesContent": [null], "mappings": "AAAA;;;AAGA,OAAO,KAAKA,aAAa,MAAM,+BAA+B;AA0E9D;;;;AAIA,OAAO,MAAMC,MAAM,GAGCD,aAAa,CAACC,MAAM;AAExC;;;;AAIA,OAAO,MAAMC,KAAK,GAAiBF,aAAa,CAACE,KAAK;AAEtD;;;;AAIA,OAAO,MAAMC,mBAAmB,GAGZH,aAAa,CAACG,mBAAmB;AAErD;;;;AAIA,OAAO,MAAMC,QAAQ,GAA6DJ,aAAa,CAACK,GAAG;AAEnG;;;;AAIA,OAAO,MAAMC,MAAM,GAAmEN,aAAa,CAACM,MAAM;AAE1G;;;;AAIA,OAAO,MAAMC,UAAU,GAA6DP,aAAa,CAACQ,GAAG", "ignoreList": []}