'use client';

import { useState, useCallback, useEffect } from 'react';
import { Upload, X, Check, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useSimpleLanguage } from '@/hooks/useSimpleLanguage';
import { UploadDropzone } from '@/lib/uploadthing';
import { toast } from 'sonner';

interface SimpleImageUploadProps {
  images: string[];
  onImagesChange: (images: string[]) => void;
  onAutoSave?: (images: string[]) => Promise<void>;
  onUploadStatusChange?: (isUploading: boolean) => void;
  propertyId?: string;
  maxImages?: number;
  disabled?: boolean;
}

export function SimpleImageUpload({
  images,
  onImagesChange,
  onAutoSave,
  onUploadStatusChange,
  propertyId,
  maxImages = 10,
  disabled = false
}: SimpleImageUploadProps) {
  const { language, isArabic } = useSimpleLanguage();
  const [isUploading, setIsUploading] = useState(false);
  const [autoSaveStatus, setAutoSaveStatus] = useState<'idle' | 'saving' | 'saved'>('idle');

  // Simple translations
  const t = {
    ar: {
      uploadImages: 'رفع الصور',
      dragDrop: 'اسحب الصور هنا أو انقر للاختيار',
      uploading: 'جاري الرفع...',
      autoSaving: 'حفظ تلقائي...',
      saved: 'تم الحفظ',
      remove: 'حذف',
      mainImage: 'الصورة الرئيسية',
      images: 'صور',
      of: 'من',
    },
    en: {
      uploadImages: 'Upload Images',
      dragDrop: 'Drag images here or click to select',
      uploading: 'Uploading...',
      autoSaving: 'Auto-saving...',
      saved: 'Saved',
      remove: 'Remove',
      mainImage: 'Main Image',
      images: 'images',
      of: 'of',
    }
  };

  const translations = t[language];

  // Auto-save when images change
  useEffect(() => {
    if (onAutoSave && images.length > 0 && autoSaveStatus === 'idle') {
      const autoSave = async () => {
        setAutoSaveStatus('saving');
        try {
          await onAutoSave(images);
          setAutoSaveStatus('saved');
          setTimeout(() => setAutoSaveStatus('idle'), 2000);
        } catch (error) {
          console.error('Auto-save failed:', error);
          setAutoSaveStatus('idle');
        }
      };

      const timer = setTimeout(autoSave, 500);
      return () => clearTimeout(timer);
    }
  }, [images, onAutoSave, autoSaveStatus]);

  const handleUploadComplete = useCallback((res: any[]) => {
    if (res && res.length > 0) {
      const newImageUrls = res.map(file => file.url);
      const updatedImages = [...images, ...newImageUrls].slice(0, maxImages);

      onImagesChange(updatedImages);
      setIsUploading(false);
      onUploadStatusChange?.(false);

      toast.success(`${res.length} ${translations.images} ${translations.saved}`);
    }
  }, [images, onImagesChange, maxImages, onUploadStatusChange, translations.images, translations.saved]);

  const handleUploadError = useCallback((error: Error) => {
    console.error('Upload error:', error);
    setIsUploading(false);
    onUploadStatusChange?.(false);
    toast.error(`Upload failed: ${error.message}`);
  }, [onUploadStatusChange]);

  const handleUploadBegin = useCallback(() => {
    setIsUploading(true);
    onUploadStatusChange?.(true);
  }, [onUploadStatusChange]);

  const removeImage = useCallback((index: number) => {
    const newImages = images.filter((_, i) => i !== index);
    onImagesChange(newImages);
  }, [images, onImagesChange]);

  const setMainImage = useCallback((index: number) => {
    if (index === 0) return;

    const newImages = [...images];
    const [mainImage] = newImages.splice(index, 1);
    newImages.unshift(mainImage);

    onImagesChange(newImages);
  }, [images, onImagesChange]);

  const canUploadMore = images.length < maxImages;

  return (
    <div className={`space-y-4 ${isArabic ? 'rtl' : 'ltr'}`} dir={isArabic ? 'rtl' : 'ltr'}>
      {/* Simple Upload Area */}
      {canUploadMore && !disabled && (
        <div className="relative">
          <div className="border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-lg p-6 text-center hover:border-emerald-400 dark:hover:border-emerald-500 transition-colors bg-slate-50 dark:bg-slate-800/50">
            <Upload className="mx-auto h-8 w-8 text-slate-400 dark:text-slate-500 mb-3" />
            <p className="text-sm text-slate-600 dark:text-slate-400 mb-4">
              {translations.dragDrop}
            </p>
            <p className="text-xs text-slate-500 dark:text-slate-500">
              {images.length} {translations.of} {maxImages} {translations.images}
            </p>
          </div>

          <UploadDropzone
            endpoint="propertyImageUploader"
            onClientUploadComplete={handleUploadComplete}
            onUploadError={handleUploadError}
            onUploadBegin={handleUploadBegin}
            className="absolute inset-0 opacity-0 cursor-pointer"
          />

          {/* Upload Progress Overlay */}
          {isUploading && (
            <div className="absolute inset-0 bg-white/90 dark:bg-slate-900/90 rounded-lg flex items-center justify-center">
              <div className="text-center">
                <Loader2 className="h-6 w-6 animate-spin mx-auto mb-2 text-emerald-500" />
                <p className="text-sm text-slate-600 dark:text-slate-400">
                  {translations.uploading}
                </p>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Auto-Save Status */}
      {autoSaveStatus !== 'idle' && (
        <div className={`flex items-center gap-2 text-sm ${
          autoSaveStatus === 'saving' ? 'text-blue-600 dark:text-blue-400' : 'text-green-600 dark:text-green-400'
        } ${isArabic ? 'flex-row-reverse' : ''}`}>
          {autoSaveStatus === 'saving' ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Check className="h-4 w-4" />
          )}
          <span>
            {autoSaveStatus === 'saving' ? translations.autoSaving : translations.saved}
          </span>
        </div>
      )}

      {/* Simple Image Grid */}
      {images.length > 0 && (
        <div className="space-y-3">
          {/* Main Image */}
          <div className="relative">
            <div className="aspect-video bg-slate-100 dark:bg-slate-800 rounded-lg overflow-hidden">
              <img
                src={images[0]}
                alt="Main property image"
                className="w-full h-full object-cover"
              />
              <div className={`absolute top-2 ${isArabic ? 'right-2' : 'left-2'} bg-emerald-600 text-white text-xs px-2 py-1 rounded`}>
                {translations.mainImage}
              </div>
              <Button
                type="button"
                variant="destructive"
                size="sm"
                className={`absolute top-2 ${isArabic ? 'left-2' : 'right-2'} h-6 w-6 p-0 rounded`}
                onClick={() => removeImage(0)}
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          </div>

          {/* Additional Images */}
          {images.length > 1 && (
            <div className="grid grid-cols-4 gap-2">
              {images.slice(1).map((image, index) => (
                <div key={index + 1} className="relative group">
                  <div
                    className="aspect-square bg-slate-100 dark:bg-slate-800 rounded overflow-hidden cursor-pointer hover:opacity-75 transition-opacity"
                    onClick={() => setMainImage(index + 1)}
                  >
                    <img
                      src={image}
                      alt={`Property image ${index + 2}`}
                      className="w-full h-full object-cover"
                    />
                    <Button
                      type="button"
                      variant="destructive"
                      size="sm"
                      className="absolute -top-1 -right-1 h-5 w-5 p-0 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                      onClick={(e) => {
                        e.stopPropagation();
                        removeImage(index + 1);
                      }}
                    >
                      <X className="h-2 w-2" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Max Images Warning */}
      {images.length >= maxImages && (
        <div className="text-center p-3 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg">
          <p className="text-sm text-amber-800 dark:text-amber-200">
            {language === 'ar' ? `الحد الأقصى ${maxImages} صور` : `Maximum ${maxImages} images`}
          </p>
        </div>
      )}
    </div>
  );
}
