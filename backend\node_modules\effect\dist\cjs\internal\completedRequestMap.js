"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.currentRequestMap = void 0;
var _GlobalValue = require("../GlobalValue.js");
var _core = require("./core.js");
/** @internal */
const currentRequestMap = exports.currentRequestMap = /*#__PURE__*/(0, _GlobalValue.globalValue)( /*#__PURE__*/Symbol.for("effect/FiberRef/currentRequestMap"), () => (0, _core.fiberRefUnsafeMake)(new Map()));
//# sourceMappingURL=completedRequestMap.js.map