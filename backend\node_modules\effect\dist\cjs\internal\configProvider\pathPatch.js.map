{"version": 3, "file": "pathPatch.js", "names": ["RA", "_interopRequireWildcard", "require", "Either", "_Function", "List", "Option", "config<PERSON><PERSON>r", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "empty", "exports", "_tag", "and<PERSON><PERSON>", "dual", "self", "that", "first", "second", "mapName", "f", "nested", "name", "unnested", "patch", "path", "input", "of", "output", "isCons", "head", "tail", "cons", "map", "prepend", "containsName", "pipe", "contains", "tailNonEmpty", "left", "MissingData", "right"], "sources": ["../../../../src/internal/configProvider/pathPatch.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA,IAAAA,EAAA,GAAAC,uBAAA,CAAAC,OAAA;AAGA,IAAAC,MAAA,GAAAF,uBAAA,CAAAC,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;AACA,IAAAG,IAAA,GAAAJ,uBAAA,CAAAC,OAAA;AACA,IAAAI,MAAA,GAAAL,uBAAA,CAAAC,OAAA;AACA,IAAAK,WAAA,GAAAN,uBAAA,CAAAC,OAAA;AAAgD,SAAAM,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAR,wBAAAQ,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAEhD;AACO,MAAMW,KAAK,GAAAC,OAAA,CAAAD,KAAA,GAAwB;EACxCE,IAAI,EAAE;CACP;AAED;AACO,MAAMC,OAAO,GAAAF,OAAA,CAAAE,OAAA,gBAAG,IAAAC,cAAI,EAGzB,CAAC,EAAE,CAACC,IAAI,EAAEC,IAAI,MAAM;EACpBJ,IAAI,EAAE,SAAS;EACfK,KAAK,EAAEF,IAAI;EACXG,MAAM,EAAEF;CACT,CAAC,CAAC;AAEH;AACO,MAAMG,OAAO,GAAAR,OAAA,CAAAQ,OAAA,gBAAG,IAAAL,cAAI,EAGzB,CAAC,EAAE,CAACC,IAAI,EAAEK,CAAC,KAAKP,OAAO,CAACE,IAAI,EAAE;EAAEH,IAAI,EAAE,SAAS;EAAEQ;AAAC,CAAE,CAAC,CAAC;AAExD;AACO,MAAMC,MAAM,GAAAV,OAAA,CAAAU,MAAA,gBAAG,IAAAP,cAAI,EAGxB,CAAC,EAAE,CAACC,IAAI,EAAEO,IAAI,KAAKT,OAAO,CAACE,IAAI,EAAE;EAAEH,IAAI,EAAE,QAAQ;EAAEU;AAAI,CAAE,CAAC,CAAC;AAE7D;AACO,MAAMC,QAAQ,GAAAZ,OAAA,CAAAY,QAAA,gBAAG,IAAAT,cAAI,EAG1B,CAAC,EAAE,CAACC,IAAI,EAAEO,IAAI,KAAKT,OAAO,CAACE,IAAI,EAAE;EAAEH,IAAI,EAAE,UAAU;EAAEU;AAAI,CAAE,CAAC,CAAC;AAE/D;AACO,MAAME,KAAK,GAAAb,OAAA,CAAAa,KAAA,gBAAG,IAAAV,cAAI,EAUvB,CAAC,EAAE,CAACW,IAAI,EAAED,KAAK,KAAI;EACnB,IAAIE,KAAK,GAAmCvC,IAAI,CAACwC,EAAE,CAACH,KAAK,CAAC;EAC1D,IAAII,MAAM,GAA0BH,IAAI;EACxC,OAAOtC,IAAI,CAAC0C,MAAM,CAACH,KAAK,CAAC,EAAE;IACzB,MAAMF,KAAK,GAAwBE,KAAK,CAACI,IAAI;IAC7C,QAAQN,KAAK,CAACZ,IAAI;MAChB,KAAK,OAAO;QAAE;UACZc,KAAK,GAAGA,KAAK,CAACK,IAAI;UAClB;QACF;MACA,KAAK,SAAS;QAAE;UACdL,KAAK,GAAGvC,IAAI,CAAC6C,IAAI,CAACR,KAAK,CAACP,KAAK,EAAE9B,IAAI,CAAC6C,IAAI,CAACR,KAAK,CAACN,MAAM,EAAEQ,KAAK,CAACK,IAAI,CAAC,CAAC;UACnE;QACF;MACA,KAAK,SAAS;QAAE;UACdH,MAAM,GAAG9C,EAAE,CAACmD,GAAG,CAACL,MAAM,EAAEJ,KAAK,CAACJ,CAAC,CAAC;UAChCM,KAAK,GAAGA,KAAK,CAACK,IAAI;UAClB;QACF;MACA,KAAK,QAAQ;QAAE;UACbH,MAAM,GAAG9C,EAAE,CAACoD,OAAO,CAACN,MAAM,EAAEJ,KAAK,CAACF,IAAI,CAAC;UACvCI,KAAK,GAAGA,KAAK,CAACK,IAAI;UAClB;QACF;MACA,KAAK,UAAU;QAAE;UACf,MAAMI,YAAY,GAAG,IAAAC,cAAI,EACvBtD,EAAE,CAACgD,IAAI,CAACF,MAAM,CAAC,EACfxC,MAAM,CAACiD,QAAQ,CAACb,KAAK,CAACF,IAAI,CAAC,CAC5B;UACD,IAAIa,YAAY,EAAE;YAChBP,MAAM,GAAG9C,EAAE,CAACwD,YAAY,CAACV,MAAkC,CAAC;YAC5DF,KAAK,GAAGA,KAAK,CAACK,IAAI;UACpB,CAAC,MAAM;YACL,OAAO9C,MAAM,CAACsD,IAAI,CAAClD,WAAW,CAACmD,WAAW,CACxCZ,MAAM,EACN,YAAYJ,KAAK,CAACF,IAAI,2CAA2C,CAClE,CAAC;UACJ;UACA;QACF;IACF;EACF;EACA,OAAOrC,MAAM,CAACwD,KAAK,CAACb,MAAM,CAAC;AAC7B,CAAC,CAAC", "ignoreList": []}