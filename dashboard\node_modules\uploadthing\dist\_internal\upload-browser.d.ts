import * as Micro from 'effect/Micro';
import { FetchError, UploadThingError, FetchContext } from '@uploadthing/shared';
import { FileRouter, inferEndpointOutput, NewPresignedUrl, UploadFilesOptions, ClientUploadedFileData } from '../../types/index.js';

declare const uploadFile: <TRouter extends FileRouter, TEndpoint extends keyof TRouter, TServerOutput = inferEndpointOutput<TRouter[TEndpoint]>>(file: File, presigned: NewPresignedUrl, opts: {
    onUploadProgress?: (progressEvent: {
        loaded: number;
        delta: number;
    }) => void;
}) => Micro.Micro<{
    name: string;
    size: number;
    key: string;
    lastModified: number;
    serverData: TServerOutput;
    readonly url: string;
    readonly appUrl: string;
    ufsUrl: string;
    customId: string | null;
    type: string;
    fileHash: string;
}, FetchError | UploadThingError<{
    message: string;
}>, FetchContext>;
declare const uploadFilesInternal: <TRouter extends FileRouter, TEndpoint extends keyof TRouter, TServerOutput = inferEndpointOutput<TRouter[TEndpoint]>>(endpoint: TEndpoint, opts: UploadFilesOptions<TRouter[TEndpoint]>) => Micro.Micro<ClientUploadedFileData<TServerOutput>[], UploadThingError | FetchError, FetchContext>;

export { uploadFile, uploadFilesInternal };
