"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/properties/create/page",{

/***/ "(app-pages-browser)/./app/dashboard/properties/create/page.tsx":
/*!**************************************************!*\
  !*** ./app/dashboard/properties/create/page.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CreatePropertyPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronRight_Home_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronRight,Home!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronRight_Home_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronRight,Home!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ChevronRight_Home_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ChevronRight,Home!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useSimpleLanguage */ \"(app-pages-browser)/./hooks/useSimpleLanguage.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _property_form_steps__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./property-form-steps */ \"(app-pages-browser)/./app/dashboard/properties/create/property-form-steps.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction CreatePropertyPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { language } = (0,_hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_4__.useSimpleLanguage)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const translations = {\n        ar: {\n            createProperty: 'إنشاء عقار جديد',\n            backToProperties: 'العودة إلى العقارات',\n            subtitle: 'أضف عقار جديد إلى قائمة العقارات الخاصة بك مع معلومات مفصلة وصور',\n            properties: 'العقارات',\n            home: 'الرئيسية'\n        },\n        en: {\n            createProperty: 'Create New Property',\n            backToProperties: 'Back to Properties',\n            subtitle: 'Add a new property to your listings with detailed information and images',\n            properties: 'Properties',\n            home: 'Home'\n        }\n    };\n    const t = translations[language] || translations.ar;\n    // Debug logging\n    console.log('CreatePropertyPage - Language:', language, 'Translations:', t);\n    const handleSave = async (formData)=>{\n        setLoading(true);\n        try {\n            const payload = {\n                ...formData,\n                price: parseFloat(formData.price),\n                bedrooms: formData.bedrooms ? parseInt(formData.bedrooms) : undefined,\n                bathrooms: formData.bathrooms ? parseInt(formData.bathrooms) : undefined,\n                area: formData.area ? parseFloat(formData.area) : undefined,\n                yearBuilt: formData.yearBuilt ? parseInt(formData.yearBuilt) : undefined,\n                parking: formData.parking ? parseInt(formData.parking) : undefined\n            };\n            const response = await fetch('/api/v1/properties', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(payload)\n            });\n            if (response.ok) {\n                sonner__WEBPACK_IMPORTED_MODULE_5__.toast.success(language === 'ar' ? 'تم إنشاء العقار بنجاح' : 'Property created successfully');\n                router.push('/dashboard/properties');\n            } else {\n                const error = await response.json();\n                sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error(error.message || (language === 'ar' ? 'فشل في إنشاء العقار' : 'Failed to create property'));\n            }\n        } catch (error) {\n            console.error('Error creating property:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error(language === 'ar' ? 'فشل في إنشاء العقار' : 'Failed to create property');\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 \".concat(language === 'ar' ? 'rtl' : 'ltr'),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400 mb-8\",\n                    dir: language === 'ar' ? 'rtl' : 'ltr',\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronRight_Home_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronRight_Home_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.push('/dashboard/properties'),\n                            className: \"hover:text-blue-600 dark:hover:text-blue-400 transition-colors\",\n                            children: t.properties\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronRight_Home_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-gray-900 dark:text-gray-100\",\n                            children: t.createProperty\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900 dark:text-white mb-2\",\n                                    children: t.createProperty\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-400 max-w-2xl\",\n                                    children: t.subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            onClick: ()=>router.push('/dashboard/properties'),\n                            className: \"flex items-center gap-2 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ChevronRight_Home_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, this),\n                                t.backToProperties\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_property_form_steps__WEBPACK_IMPORTED_MODULE_6__.PropertyFormSteps, {\n                    onSave: handleSave,\n                    loading: loading\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n            lineNumber: 77,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n_s(CreatePropertyPage, \"8xW8Ca/pgX8TN8nesAnbFO3tK9U=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_4__.useSimpleLanguage\n    ];\n});\n_c = CreatePropertyPage;\nvar _c;\n$RefreshReg$(_c, \"CreatePropertyPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/properties/create/page.tsx\n"));

/***/ })

});