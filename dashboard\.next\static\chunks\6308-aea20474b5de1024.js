"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6308],{14503:(e,t,s)=>{s.d(t,{dj:()=>u,oR:()=>f});var r=s(12115);let a=0,o=new Map,n=e=>{if(o.has(e))return;let t=setTimeout(()=>{o.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);o.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:s}=t;return s?n(s):e.toasts.forEach(e=>{n(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],d={toasts:[]};function c(e){d=i(d,e),l.forEach(e=>{e(d)})}function f(e){let{...t}=e,s=(a=(a+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>c({type:"DISMISS_TOAST",toastId:s});return c({type:"ADD_TOAST",toast:{...t,id:s,open:!0,onOpenChange:e=>{e||r()}}}),{id:s,dismiss:r,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:s}})}}function u(){let[e,t]=r.useState(d);return r.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:f,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},31886:(e,t,s)=>{s.d(t,{A:()=>o});var r=s(23464);class a{async get(e,t){try{if(this.isOfflineMode&&!e.includes("/health"))throw console.log("API Client: In offline mode, skipping GET request to ".concat(e)),Error("Network Error: Application is in offline mode");let s=await this.client.get(e,t);return this.isOfflineMode&&this.setOfflineMode(!1),s.data}catch(e){throw e}}async post(e,t,s){return(await this.client.post(e,t,s)).data}async put(e,t,s){return(await this.client.put(e,t,s)).data}async delete(e,t){try{console.log("Making DELETE request to: ".concat(e));let s=await this.client.delete(e,t);if(204===s.status)return console.log("DELETE request to ".concat(e," successful with 204 status")),null;return s.data}catch(t){throw console.error("DELETE request to ".concat(e," failed:"),t),t}}async patch(e,t,s){return(await this.client.patch(e,t,s)).data}async upload(e,t,s){let r={...s,headers:{...null==s?void 0:s.headers,"Content-Type":"multipart/form-data"}};return(await this.client.post(e,t,r)).data}getBaseURL(){return this.baseURL}isInOfflineMode(){return this.isOfflineMode}setOfflineMode(e){this.isOfflineMode!==e&&(console.log("API Client: Setting offline mode to ".concat(e)),this.isOfflineMode=e,window.dispatchEvent(new CustomEvent("api:offline",{detail:{isOffline:e}})))}async tryReconnect(){try{return console.log("API Client: Attempting to reconnect to the server..."),await this.client.get("/health",{timeout:5e3}),console.log("API Client: Reconnection successful"),this.setOfflineMode(!1),!0}catch(e){return console.log("API Client: Reconnection failed, still in offline mode"),!1}}constructor(){this.isOfflineMode=!1,this.baseURL="http://localhost:5000/api/v1",this.baseURL||console.warn("NEXT_PUBLIC_BACKEND_API_URL is not defined. API requests may fail."),this.client=r.A.create({baseURL:this.baseURL,headers:{"Content-Type":"application/json"},timeout:1e4}),this.client.interceptors.request.use(e=>e,e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,e=>{if(e.response){if(console.error("API Error Response:",e.response.status,e.response.data),e.response.data&&0!==Object.keys(e.response.data).length||(e.response.data={error:"Request failed with status code ".concat(e.response.status)}),404===e.response.status){var t;console.log("Resource not found:",null===(t=e.config)||void 0===t?void 0:t.url),e.response.data={error:"The requested resource was not found. Please refresh and try again."}}e.responseData=e.response.data}else e.request?(console.error("API No Response:",e.request),e.message&&(e.message.includes("Network Error")||e.message.includes("timeout"))?(console.error("Network Error detected. Server might be down or unreachable."),this.setOfflineMode(!0),e.isOffline=!0,e.responseData={error:"Network Error: Unable to connect to the server. Please check your connection or try again later.",isOffline:!0},console.warn("API Client: Server connection failed. If you're running in development mode, make sure your backend server is running."),window.dispatchEvent(new CustomEvent("api:offline",{detail:{isOffline:!0}}))):e.responseData={error:"No response received from server. Please check your connection."}):(console.error("API Request Error:",e.message),e.responseData={error:e.message||"An unexpected error occurred"});return Promise.reject(e)})}}let o=new a},53999:(e,t,s)=>{s.d(t,{cn:()=>o});var r=s(52596),a=s(39688);function o(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.QP)((0,r.$)(t))}},88145:(e,t,s)=>{s.d(t,{E:()=>i});var r=s(95155);s(12115);var a=s(74466),o=s(53999);let n=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:s,...a}=e;return(0,r.jsx)("div",{className:(0,o.cn)(n({variant:s}),t),...a})}},89852:(e,t,s)=>{s.d(t,{p:()=>n});var r=s(95155),a=s(12115),o=s(53999);let n=a.forwardRef((e,t)=>{let{className:s,type:a,...n}=e;return(0,r.jsx)("input",{type:a,className:(0,o.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),ref:t,...n})});n.displayName="Input"},95784:(e,t,s)=>{s.d(t,{bq:()=>u,eb:()=>h,gC:()=>g,l6:()=>c,yv:()=>f});var r=s(95155),a=s(12115),o=s(31992),n=s(66474),i=s(47863),l=s(5196),d=s(53999);let c=o.bL;o.YJ;let f=o.WT,u=a.forwardRef((e,t)=>{let{className:s,children:a,...i}=e;return(0,r.jsxs)(o.l9,{ref:t,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",s),...i,children:[a,(0,r.jsx)(o.In,{asChild:!0,children:(0,r.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]})});u.displayName=o.l9.displayName;let p=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(o.PP,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",s),...a,children:(0,r.jsx)(i.A,{className:"h-4 w-4"})})});p.displayName=o.PP.displayName;let m=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(o.wn,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",s),...a,children:(0,r.jsx)(n.A,{className:"h-4 w-4"})})});m.displayName=o.wn.displayName;let g=a.forwardRef((e,t)=>{let{className:s,children:a,position:n="popper",...i}=e;return(0,r.jsx)(o.ZL,{children:(0,r.jsxs)(o.UC,{ref:t,className:(0,d.cn)("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:n,...i,children:[(0,r.jsx)(p,{}),(0,r.jsx)(o.LM,{className:(0,d.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),(0,r.jsx)(m,{})]})})});g.displayName=o.UC.displayName,a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(o.JU,{ref:t,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",s),...a})}).displayName=o.JU.displayName;let h=a.forwardRef((e,t)=>{let{className:s,children:a,...n}=e;return(0,r.jsxs)(o.q7,{ref:t,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...n,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(o.VF,{children:(0,r.jsx)(l.A,{className:"h-4 w-4"})})}),(0,r.jsx)(o.p4,{children:a})]})});h.displayName=o.q7.displayName,a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(o.wv,{ref:t,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",s),...a})}).displayName=o.wv.displayName},97168:(e,t,s)=>{s.d(t,{$:()=>d,r:()=>l});var r=s(95155),a=s(12115),o=s(99708),n=s(74466),i=s(53999);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,t)=>{let{className:s,variant:a,size:n,asChild:d=!1,...c}=e,f=d?o.DX:"button";return(0,r.jsx)(f,{className:(0,i.cn)(l({variant:a,size:n,className:s})),ref:t,...c})});d.displayName="Button"},99840:(e,t,s)=>{s.d(t,{Cf:()=>u,Es:()=>m,L3:()=>g,c7:()=>p,lG:()=>l,rr:()=>h,zM:()=>d});var r=s(95155),a=s(12115),o=s(15452),n=s(54416),i=s(53999);let l=o.bL,d=o.l9,c=o.ZL;o.bm;let f=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(o.hJ,{ref:t,className:(0,i.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...a})});f.displayName=o.hJ.displayName;let u=a.forwardRef((e,t)=>{let{className:s,children:a,...l}=e;return(0,r.jsxs)(c,{children:[(0,r.jsx)(f,{}),(0,r.jsxs)(o.UC,{ref:t,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",s),...l,children:[a,(0,r.jsxs)(o.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,r.jsx)(n.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});u.displayName=o.UC.displayName;let p=e=>{let{className:t,...s}=e;return(0,r.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...s})};p.displayName="DialogHeader";let m=e=>{let{className:t,...s}=e;return(0,r.jsx)("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...s})};m.displayName="DialogFooter";let g=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(o.hE,{ref:t,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",s),...a})});g.displayName=o.hE.displayName;let h=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(o.VY,{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",s),...a})});h.displayName=o.VY.displayName}}]);