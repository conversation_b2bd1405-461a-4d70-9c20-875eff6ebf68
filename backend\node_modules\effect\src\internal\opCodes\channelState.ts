/** @internal */
export const OP_DONE = "Done" as const

/** @internal */
export type OP_DONE = typeof OP_DONE

/** @internal */
export const OP_EMIT = "Emit" as const

/** @internal */
export type OP_EMIT = typeof OP_EMIT

/** @internal */
export const OP_FROM_EFFECT = "FromEffect" as const

/** @internal */
export type OP_FROM_EFFECT = typeof OP_FROM_EFFECT

/** @internal */
export const OP_READ = "Read" as const

/** @internal */
export type OP_READ = typeof OP_READ
