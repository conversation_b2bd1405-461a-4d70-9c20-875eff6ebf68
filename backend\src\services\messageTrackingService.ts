import { prisma } from '../lib/prisma';
import { logWithTimestamp } from '../utils/logger';
import { 
  MessageStatusType, 
  WhatsAppStatus, 
  TrackedWhatsAppMessage,
  MessageDeliveryReport 
} from '../interfaces/MessageStatus';

/**
 * Service for tracking WhatsApp message delivery and read receipts
 */
export class MessageTrackingService {
  
  /**
   * Track a sent message
   */
  static async trackSentMessage(
    whatsappMessageId: string,
    internalMessageId: string,
    to: string,
    content: string,
    isAutomated: boolean = false,
    responseSource?: string
  ): Promise<void> {
    try {
      // Store in database (you may need to create a MessageTracking table)
      // For now, we'll update the existing Message record
      await prisma.message.update({
        where: { id: internalMessageId },
        data: {
          // Add these fields to your Message model if they don't exist
          whatsappMessageId,
          status: 'sent',
          sentAt: new Date(),
          lastStatusUpdate: new Date(),
          isAutomated,
          responseSource
        }
      });

      logWithTimestamp(
        `Message tracking started: ${whatsappMessageId} to ${to}`,
        'info'
      );
    } catch (error: any) {
      logWithTimestamp(
        `Failed to track sent message: ${error.message}`,
        'error'
      );
    }
  }

  /**
   * Update message status based on WhatsApp status webhook
   */
  static async updateMessageStatus(status: WhatsAppStatus): Promise<void> {
    try {
      const updateData: any = {
        status: status.status,
        lastStatusUpdate: new Date()
      };

      // Add specific timestamp based on status
      switch (status.status) {
        case 'delivered':
          updateData.deliveredAt = new Date(parseInt(status.timestamp) * 1000);
          break;
        case 'read':
          updateData.readAt = new Date(parseInt(status.timestamp) * 1000);
          break;
        case 'failed':
          updateData.failedAt = new Date(parseInt(status.timestamp) * 1000);
          if (status.errors) {
            updateData.errorDetails = JSON.stringify(status.errors);
          }
          break;
      }

      // Update message in database
      const updatedMessage = await prisma.message.updateMany({
        where: { whatsappMessageId: status.id },
        data: updateData
      });

      if (updatedMessage.count > 0) {
        logWithTimestamp(
          `Message status updated: ${status.id} → ${status.status}`,
          'info'
        );

        // Log read receipt specifically
        if (status.status === 'read') {
          logWithTimestamp(
            `📖 Read receipt received for message ${status.id} from ${status.recipient_id}`,
            'success'
          );
        }
      } else {
        logWithTimestamp(
          `Message not found for status update: ${status.id}`,
          'error'
        );
      }
    } catch (error: any) {
      logWithTimestamp(
        `Failed to update message status: ${error.message}`,
        'error'
      );
    }
  }

  /**
   * Get delivery report for a specific client
   */
  static async getClientDeliveryReport(clientId: string): Promise<MessageDeliveryReport> {
    try {
      const messages = await prisma.message.findMany({
        where: {
          clientId,
          isBot: true, // Only outgoing messages
          whatsappMessageId: { not: null }
        }
      });

      const totalSent = messages.length;
      const delivered = messages.filter(m => 
        m.status === 'delivered' || m.status === 'read'
      ).length;
      const read = messages.filter(m => m.status === 'read').length;
      const failed = messages.filter(m => m.status === 'failed').length;

      const deliveryRate = totalSent > 0 ? (delivered / totalSent) * 100 : 0;
      const readRate = delivered > 0 ? (read / delivered) * 100 : 0;

      // Calculate average times
      const deliveredMessages = messages.filter(m => m.deliveredAt && m.sentAt);
      const readMessages = messages.filter(m => m.readAt && m.deliveredAt);

      const avgDeliveryTime = deliveredMessages.length > 0 
        ? deliveredMessages.reduce((sum, m) => 
            sum + (m.deliveredAt!.getTime() - m.sentAt!.getTime()), 0
          ) / deliveredMessages.length / 1000 // Convert to seconds
        : undefined;

      const avgReadTime = readMessages.length > 0
        ? readMessages.reduce((sum, m) => 
            sum + (m.readAt!.getTime() - m.deliveredAt!.getTime()), 0
          ) / readMessages.length / 1000 // Convert to seconds
        : undefined;

      return {
        totalSent,
        delivered,
        read,
        failed,
        deliveryRate: Math.round(deliveryRate * 100) / 100,
        readRate: Math.round(readRate * 100) / 100,
        avgDeliveryTime,
        avgReadTime
      };
    } catch (error: any) {
      logWithTimestamp(
        `Failed to generate delivery report: ${error.message}`,
        'error'
      );
      
      return {
        totalSent: 0,
        delivered: 0,
        read: 0,
        failed: 0,
        deliveryRate: 0,
        readRate: 0
      };
    }
  }

  /**
   * Get overall delivery statistics
   */
  static async getOverallDeliveryStats(): Promise<MessageDeliveryReport> {
    try {
      const messages = await prisma.message.findMany({
        where: {
          isBot: true, // Only outgoing messages
          whatsappMessageId: { not: null },
          sentAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
          }
        }
      });

      const totalSent = messages.length;
      const delivered = messages.filter(m => 
        m.status === 'delivered' || m.status === 'read'
      ).length;
      const read = messages.filter(m => m.status === 'read').length;
      const failed = messages.filter(m => m.status === 'failed').length;

      const deliveryRate = totalSent > 0 ? (delivered / totalSent) * 100 : 0;
      const readRate = delivered > 0 ? (read / delivered) * 100 : 0;

      return {
        totalSent,
        delivered,
        read,
        failed,
        deliveryRate: Math.round(deliveryRate * 100) / 100,
        readRate: Math.round(readRate * 100) / 100
      };
    } catch (error: any) {
      logWithTimestamp(
        `Failed to get overall delivery stats: ${error.message}`,
        'error'
      );
      
      return {
        totalSent: 0,
        delivered: 0,
        read: 0,
        failed: 0,
        deliveryRate: 0,
        readRate: 0
      };
    }
  }

  /**
   * Mark incoming message as read (send read receipt)
   */
  static async markAsRead(messageId: string): Promise<boolean> {
    try {
      // This would send a read receipt back to WhatsApp
      // Implementation depends on your WhatsApp API setup
      logWithTimestamp(
        `Marking message as read: ${messageId}`,
        'info'
      );
      
      // You can implement actual read receipt sending here
      // For now, we'll just log it
      return true;
    } catch (error: any) {
      logWithTimestamp(
        `Failed to mark message as read: ${error.message}`,
        'error'
      );
      return false;
    }
  }
}

export default MessageTrackingService;
