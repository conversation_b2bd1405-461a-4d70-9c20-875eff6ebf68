{"version": 3, "file": "Request.d.ts", "sourceRoot": "", "sources": ["../../src/Request.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,OAAO,KAAK,KAAK,MAAM,MAAM,YAAY,CAAA;AACzC,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,YAAY,CAAA;AACvC,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAA;AAC7C,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,eAAe,CAAA;AAClD,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAC1C,OAAO,KAAK,KAAK,IAAI,MAAM,WAAW,CAAA;AACtC,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,cAAc,CAAA;AAM3C,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAC1C,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AAExC;;;GAGG;AACH,eAAO,MAAM,aAAa,EAAE,OAAO,MAA+B,CAAA;AAElE;;;GAGG;AACH,MAAM,MAAM,aAAa,GAAG,OAAO,aAAa,CAAA;AAEhD;;;;;;GAMG;AACH,MAAM,WAAW,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,KAAK,CAAE,SAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;CAAG;AAEhF;;GAEG;AACH,MAAM,CAAC,OAAO,WAAW,OAAO,CAAC;IAC/B;;;OAGG;IACH,UAAiB,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QACpC,QAAQ,CAAC,CAAC,aAAa,CAAC,EAAE;YACxB,QAAQ,CAAC,EAAE,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;YAC/B,QAAQ,CAAC,EAAE,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;SAChC,CAAA;KACF;IAED;;;OAGG;IACH,UAAiB,WAAW,CAAC,CAAC,SAAS,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,SAAS,MAAM,CAAC,GAAG,KAAK;QACjF,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;KACvF;IAED;;;;;OAKG;IACH,KAAY,KAAK,CAAC,CAAC,SAAS,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,KAAK,CAAA;IAEvG;;;;;OAKG;IACH,KAAY,OAAO,CAAC,CAAC,SAAS,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,KAAK,CAAA;IAEzG;;;;;OAKG;IACH,KAAY,MAAM,CAAC,CAAC,SAAS,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,SAAS,OAAO,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,CAAA;IAE/G;;;;;OAKG;IACH,KAAY,cAAc,CAAC,CAAC,SAAS,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,SAAS,OAAO,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,CAAC,GACzF,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAC9B,KAAK,CAAA;CACV;AAED;;;;;GAKG;AACH,eAAO,MAAM,SAAS,EAAE,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,IAAI,OAAO,CAAC,OAAO,EAAE,OAAO,CAAsB,CAAA;AAE3F;;;;;GAKG;AACH,eAAO,MAAM,EAAE,EAAE,CAAC,CAAC,SAAS,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,OAAO,OAAO,CAAC,WAAW,CAAC,CAAC,CAAe,CAAA;AAExF;;;;;GAKG;AACH,eAAO,MAAM,MAAM,EAAE,CAAC,CAAC,SAAS,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG;IAAE,IAAI,EAAE,MAAM,CAAA;CAAE,EAClE,GAAG,EAAE,CAAC,CAAC,MAAM,CAAC,KACX,OAAO,CAAC,WAAW,CAAC,CAAC,EAAE,MAAM,CAAmB,CAAA;AAErD;;;;;;;;;;;;;;;;;GAiBG;AACH,eAAO,MAAM,KAAK,EAAE,KAAI,OAAO,EAAE,KAAK,EAAE,CAAC,SAAS,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,EACnE,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,SAAS,IAAI,GAAG,IAAI,GAChF;IAAE,QAAQ,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,MAAM,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CAAE,KAC3F,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAyB,CAAA;AAElE;;;;;;;;;;;;;;;;;GAiBG;AACH,eAAO,MAAM,WAAW,EAAE,CAAC,GAAG,SAAS,MAAM,EAC3C,GAAG,EAAE,GAAG,KACL,KAAI,OAAO,EAAE,KAAK,EAAE,CAAC,SAAS,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,EACpD,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,SAAS,IAAI,GAAG,IAAI,GAChF;IAAE,QAAQ,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,MAAM,GAAG,MAAM,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CAAE,KACpG,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG;IAAE,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAA;CAAgC,CAAA;AAEjG;;;;;GAKG;AACH,eAAO,MAAM,QAAQ,EAAE;IACrB;;;;;OAKG;IACH,CAAC,CAAC,SAAS,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;IAC1F;;;;;OAKG;IACH,CAAC,CAAC,SAAS,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;CACnE,CAAA;AAErB;;;;;GAKG;AACH,eAAO,MAAM,qBAAqB,EAAE;IAClC;;;;;OAKG;IACH,CAAC,GAAG,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;IACxG;;;;;OAKG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;CAChE,CAAA;AAEtC;;;;;;;GAOG;AACH,eAAO,MAAM,cAAc,EAAE;IAC3B;;;;;;;OAOG;IACH,CAAC,CAAC,SAAS,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,CAAA;IAC5I;;;;;;;OAOG;IACH,CAAC,CAAC,SAAS,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,CAAA;CAC/G,CAAA;AAE3B;;;;;GAKG;AACH,eAAO,MAAM,IAAI,EAAE;IACjB;;;;;OAKG;IACH,CAAC,CAAC,SAAS,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;IACxF;;;;;OAKG;IACH,CAAC,CAAC,SAAS,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;CACrE,CAAA;AAEjB;;;;;GAKG;AACH,eAAO,MAAM,SAAS,EAAE;IACtB;;;;;OAKG;IACH,CAAC,CAAC,SAAS,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;IAC/F;;;;;OAKG;IACH,CAAC,CAAC,SAAS,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;CACvE,CAAA;AAEtB;;;;;GAKG;AACH,eAAO,MAAM,OAAO,EAAE;IACpB;;;;;OAKG;IACH,CAAC,CAAC,SAAS,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;IAC1F;;;;;OAKG;IACH,CAAC,CAAC,SAAS,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;CACpE,CAAA;AAEpB;;;GAGG;AACH,MAAM,WAAW,SAAS;IACxB,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAA;IACtB,QAAQ,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC,KAAK,EAAE,MAAM,KAAK,IAAI,CAAC,CAAA;IAChD,WAAW,EAAE,OAAO,CAAA;IACpB,WAAW,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,KAAK,IAAI,GAAG,IAAI,CAAA;IAC7C,cAAc,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,KAAK,IAAI,GAAG,IAAI,CAAA;IAChD,SAAS,IAAI,IAAI,CAAA;IACjB,SAAS,IAAI,IAAI,CAAA;CAClB;AAED;;;GAGG;AACH,MAAM,WAAW,KAAM,SACrB,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE;IACtC,SAAS,EAAE,SAAS,CAAA;IACpB,MAAM,EAAE,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;CACnC,CAAC;CACF;AAEF;;;GAGG;AACH,eAAO,MAAM,SAAS,YACX;IACP,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAA;IACzB,QAAQ,CAAC,UAAU,EAAE,aAAa,CAAA;CACnC,KACA,MAAM,CAAC,MAAM,CAAC,KAAK,CAKlB,CAAA;AAEJ;;;GAGG;AACH,eAAO,MAAM,WAAW,EAAE,OAAO,MAAgD,CAAA;AAEjF;;;GAGG;AACH,MAAM,MAAM,WAAW,GAAG,OAAO,WAAW,CAAA;AAE5C;;;;;;;;;GASG;AACH,MAAM,WAAW,KAAK,CAAC,GAAG,CAAC,CAAC,CAAE,SAAQ,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;IACrD,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAA;IACnB,QAAQ,CAAC,MAAM,EAAE,QAAQ,CACvB;QAAC,CAAC;KAAC,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,KAAK,EACtD;QAAC,CAAC;KAAC,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,KAAK,CACvD,CAAA;IACD,QAAQ,CAAC,SAAS,EAAE,SAAS,CAAA;IAC7B,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAA;IACzB,QAAQ,CAAC,KAAK,EAAE;QACd,SAAS,EAAE,OAAO,CAAA;KACnB,CAAA;CACF;AAED;;;GAGG;AACH,MAAM,CAAC,OAAO,WAAW,KAAK,CAAC;IAC7B;;;OAGG;IACH,UAAiB,QAAQ,CAAC,GAAG,CAAC,CAAC;QAC7B,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE;YACtB,QAAQ,CAAC,EAAE,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;SAChC,CAAA;KACF;CACF;AAED;;;GAGG;AACH,eAAO,MAAM,OAAO,qCAAwB,CAAA;AAE5C;;;GAGG;AACH,eAAO,MAAM,SAAS;;;;;;;;cAA0B,CAAA"}