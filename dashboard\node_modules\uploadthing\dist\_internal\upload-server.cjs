var platform = require('@effect/platform');
var Effect = require('effect/Effect');
var Function = require('effect/Function');
var shared = require('@uploadthing/shared');
var deprecations_cjs = require('./deprecations.cjs');
var logger_cjs = require('./logger.cjs');

function _interopNamespace(e) {
  if (e && e.__esModule) return e;
  var n = Object.create(null);
  if (e) {
    Object.keys(e).forEach(function (k) {
      if (k !== 'default') {
        var d = Object.getOwnPropertyDescriptor(e, k);
        Object.defineProperty(n, k, d.get ? d : {
          enumerable: true,
          get: function () { return e[k]; }
        });
      }
    });
  }
  n.default = e;
  return n;
}

var Effect__namespace = /*#__PURE__*/_interopNamespace(Effect);

var version = "7.7.2";

const uploadWithoutProgress = (file, presigned)=>Effect__namespace.gen(function*() {
        const formData = new FormData();
        formData.append("file", file);
        const httpClient = (yield* platform.HttpClient.HttpClient).pipe(platform.HttpClient.filterStatusOk);
        const json = yield* platform.HttpClientRequest.put(presigned.url).pipe(platform.HttpClientRequest.bodyFormData(formData), platform.HttpClientRequest.setHeader("Range", "bytes=0-"), platform.HttpClientRequest.setHeader("x-uploadthing-version", version), httpClient.execute, Effect__namespace.tapError(logger_cjs.logHttpClientError("Failed to upload file")), Effect__namespace.mapError((e)=>new shared.UploadThingError({
                code: "UPLOAD_FAILED",
                message: "Failed to upload file",
                cause: e
            })), Effect__namespace.andThen((_)=>_.json), Effect__namespace.andThen(Function.unsafeCoerce), Effect__namespace.scoped);
        yield* Effect__namespace.logDebug(`File ${file.name} uploaded successfully`).pipe(Effect__namespace.annotateLogs("json", json));
        return {
            ...json,
            get url () {
                deprecations_cjs.logDeprecationWarning("`file.url` is deprecated and will be removed in uploadthing v9. Use `file.ufsUrl` instead.");
                return json.url;
            },
            get appUrl () {
                deprecations_cjs.logDeprecationWarning("`file.appUrl` is deprecated and will be removed in uploadthing v9. Use `file.ufsUrl` instead.");
                return json.appUrl;
            }
        };
    });

exports.uploadWithoutProgress = uploadWithoutProgress;
