{"version": 3, "file": "web.js", "names": ["MP", "decodeField", "make", "config", "headers", "Object", "fromEntries", "error", "partBuffer", "readResolve", "finished", "parser", "onField", "info", "value", "push", "_tag", "undefined", "onFile", "chunkBuffer", "chunkResolve", "readable", "ReadableStream", "pull", "controller", "length", "chunks", "chunk", "enqueue", "close", "Promise", "resolve", "then", "onError", "error_", "onDone", "writable", "WritableStream", "write", "end", "parts", "part"], "sources": ["../../src/web.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,YAAY;AAGhC,SAASC,WAAW,QAAQ,YAAY;AAyBxC,OAAO,MAAMC,IAAI,GAAIC,MAAiB,IAAsB;EAC1D,MAAMC,OAAO,GAAGC,MAAM,CAACC,WAAW,CAAC,CAAC,GAAGH,MAAM,CAACC,OAAO,CAAC,CAAC;EACvD,IAAIG,KAAoC;EACxC,IAAIC,UAAU,GAAgB,EAAE;EAChC,IAAIC,WAAqC;EACzC,IAAIC,QAAQ,GAAG,KAAK;EAEpB,MAAMC,MAAM,GAAGX,EAAE,CAACE,IAAI,CAAC;IACrB,GAAGC,MAAM;IACTC,OAAO;IACPQ,OAAOA,CAACC,IAAI,EAAEC,KAAK;MACjBN,UAAU,CAACO,IAAI,CAAC;QAAEC,IAAI,EAAE,OAAO;QAAEH,IAAI;QAAEC;MAAK,CAAE,CAAC;MAC/C,IAAIL,WAAW,KAAKQ,SAAS,EAAER,WAAW,EAAE;IAC9C,CAAC;IACDS,MAAMA,CAACL,IAAI;MACT,IAAIM,WAAW,GAAsB,EAAE;MACvC,IAAIC,YAAsC;MAC1C,IAAIV,QAAQ,GAAG,KAAK;MAEpB,MAAMW,QAAQ,GAAG,IAAIC,cAAc,CAAa;QAC9CC,IAAIA,CAACC,UAAU;UACb,IAAIL,WAAW,CAACM,MAAM,GAAG,CAAC,EAAE;YAC1B,MAAMC,MAAM,GAAGP,WAAW;YAC1BA,WAAW,GAAG,EAAE;YAChB,KAAK,MAAMQ,KAAK,IAAID,MAAM,EAAE;cAC1BF,UAAU,CAACI,OAAO,CAACD,KAAK,CAAC;;WAE5B,MAAM,IAAIjB,QAAQ,EAAE;YACnBc,UAAU,CAACK,KAAK,EAAE;WACnB,MAAM;YACL,OAAO,IAAIC,OAAO,CAAOC,OAAO,IAAG;cACjCX,YAAY,GAAGA,CAAA,KAAK;gBAClBA,YAAY,GAAGH,SAAS;gBACxBc,OAAO,EAAE;cACX,CAAC;YACH,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,IAAI,CAACT,IAAK,CAACC,UAAU,CAAC,CAAC;;QAEzC;OACD,CAAC;MAEFhB,UAAU,CAACO,IAAI,CAAC;QAAEC,IAAI,EAAE,MAAM;QAAEH,IAAI;QAAEQ;MAAQ,CAAE,CAAC;MACjD,IAAIZ,WAAW,KAAKQ,SAAS,EAAER,WAAW,EAAE;MAE5C,OAAO,UAAUkB,KAAK;QACpB,IAAIA,KAAK,KAAK,IAAI,EAAE;UAClBjB,QAAQ,GAAG,IAAI;SAChB,MAAM;UACLS,WAAW,CAACJ,IAAI,CAACY,KAAK,CAAC;;QAEzB,IAAIP,YAAY,KAAKH,SAAS,EAAE;UAC9BG,YAAY,EAAE;;MAElB,CAAC;IACH,CAAC;IACDa,OAAOA,CAACC,MAAM;MACZ,IAAI3B,KAAK,KAAKU,SAAS,EAAE;MACzBV,KAAK,GAAG2B,MAAM;MACd,IAAIzB,WAAW,KAAKQ,SAAS,EAAER,WAAW,EAAE;IAC9C,CAAC;IAED0B,MAAMA,CAAA;MACJzB,QAAQ,GAAG,IAAI;MACf,IAAID,WAAW,KAAKQ,SAAS,EAAER,WAAW,EAAE;IAC9C;GACD,CAAC;EAEF,MAAM2B,QAAQ,GAAG,IAAIC,cAAc,CAAa;IAC9CC,KAAKA,CAACX,KAAK,EAAEH,UAAU;MACrBb,MAAM,CAAC2B,KAAK,CAACX,KAAK,CAAC;IACrB,CAAC;IACDE,KAAKA,CAAA;MACHlB,MAAM,CAAC4B,GAAG,EAAE;IACd;GACD,CAAC;EAEF,MAAMlB,QAAQ,GAAG,IAAIC,cAAc,CAAO;IACxCC,IAAIA,CAACC,UAAU;MACb,IAAIhB,UAAU,CAACiB,MAAM,GAAG,CAAC,EAAE;QACzB,MAAMe,KAAK,GAAGhC,UAAU;QACxBA,UAAU,GAAG,EAAE;QACf,KAAK,MAAMiC,IAAI,IAAID,KAAK,EAAE;UACxBhB,UAAU,CAACI,OAAO,CAACa,IAAI,CAAC;;OAE3B,MAAM,IAAIlC,KAAK,EAAE;QAChBiB,UAAU,CAACjB,KAAK,CAACA,KAAK,CAAC;OACxB,MAAM,IAAIG,QAAQ,EAAE;QACnBc,UAAU,CAACK,KAAK,EAAE;OACnB,MAAM;QACL,OAAO,IAAIC,OAAO,CAAOC,OAAO,IAAG;UACjCtB,WAAW,GAAGA,CAAA,KAAK;YACjBA,WAAW,GAAGQ,SAAS;YACvBc,OAAO,EAAE;UACX,CAAC;QACH,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,IAAI,CAACT,IAAK,CAACC,UAAU,CAAC,CAAC;;IAEzC;GACD,CAAC;EAEF,OAAO;IACLY,QAAQ;IACRf;GACD;AACH,CAAC"}