{"version": 3, "file": "random.js", "names": ["Chunk", "_interopRequireWildcard", "require", "Context", "_Function", "Hash", "PCGRandom", "core", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "RandomSymbolKey", "RandomTypeId", "exports", "Symbol", "for", "randomTag", "GenericTag", "RandomImpl", "seed", "PRNG", "constructor", "next", "sync", "number", "nextBoolean", "map", "nextInt", "integer", "Number", "MAX_SAFE_INTEGER", "nextRange", "min", "max", "nextIntBetween", "shuffle", "elements", "shuffle<PERSON>ith", "nextIntBounded", "suspend", "pipe", "Array", "from", "flatMap", "buffer", "numbers", "length", "push", "forEachSequentialDiscard", "k", "swap", "as", "fromIterable", "index1", "index2", "tmp", "make", "hash"], "sources": ["../../../src/internal/random.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,OAAA,GAAAF,uBAAA,CAAAC,OAAA;AAEA,IAAAE,SAAA,GAAAF,OAAA;AACA,IAAAG,IAAA,GAAAJ,uBAAA,CAAAC,OAAA;AAEA,IAAAI,SAAA,GAAAL,uBAAA,CAAAC,OAAA;AACA,IAAAK,IAAA,GAAAN,uBAAA,CAAAC,OAAA;AAAiC,SAAAM,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAR,wBAAAQ,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAEjC;AACA,MAAMW,eAAe,GAAG,eAAe;AAEvC;AACO,MAAMC,YAAY,GAAAC,OAAA,CAAAD,YAAA,gBAAwBE,MAAM,CAACC,GAAG,CACzDJ,eAAe,CACO;AAExB;AACO,MAAMK,SAAS,GAAAH,OAAA,CAAAG,SAAA,gBAA8C9B,OAAO,CAAC+B,UAAU,CAAC,eAAe,CAAC;AACvG;AACA,MAAMC,UAAU;EAKOC,IAAA;EAJZ,CAACP,YAAY,IAAyBA,YAAY;EAElDQ,IAAI;EAEbC,YAAqBF,IAAY;IAAZ,KAAAA,IAAI,GAAJA,IAAI;IACvB,IAAI,CAACC,IAAI,GAAG,IAAI/B,SAAS,CAACA,SAAS,CAAC8B,IAAI,CAAC;EAC3C;EAEA,IAAIG,IAAIA,CAAA;IACN,OAAOhC,IAAI,CAACiC,IAAI,CAAC,MAAM,IAAI,CAACH,IAAI,CAACI,MAAM,EAAE,CAAC;EAC5C;EAEA,IAAIC,WAAWA,CAAA;IACb,OAAOnC,IAAI,CAACoC,GAAG,CAAC,IAAI,CAACJ,IAAI,EAAGtB,CAAC,IAAKA,CAAC,GAAG,GAAG,CAAC;EAC5C;EAEA,IAAI2B,OAAOA,CAAA;IACT,OAAOrC,IAAI,CAACiC,IAAI,CAAC,MAAM,IAAI,CAACH,IAAI,CAACQ,OAAO,CAACC,MAAM,CAACC,gBAAgB,CAAC,CAAC;EACpE;EAEAC,SAASA,CAACC,GAAW,EAAEC,GAAW;IAChC,OAAO3C,IAAI,CAACoC,GAAG,CAAC,IAAI,CAACJ,IAAI,EAAGtB,CAAC,IAAK,CAACiC,GAAG,GAAGD,GAAG,IAAIhC,CAAC,GAAGgC,GAAG,CAAC;EAC1D;EAEAE,cAAcA,CAACF,GAAW,EAAEC,GAAW;IACrC,OAAO3C,IAAI,CAACiC,IAAI,CAAC,MAAM,IAAI,CAACH,IAAI,CAACQ,OAAO,CAACK,GAAG,GAAGD,GAAG,CAAC,GAAGA,GAAG,CAAC;EAC5D;EAEAG,OAAOA,CAAIC,QAAqB;IAC9B,OAAOC,WAAW,CAACD,QAAQ,EAAGpC,CAAC,IAAK,IAAI,CAACkC,cAAc,CAAC,CAAC,EAAElC,CAAC,CAAC,CAAC;EAChE;;AAGF,MAAMqC,WAAW,GAAGA,CAClBD,QAAqB,EACrBE,cAAoD,KACnB;EACjC,OAAOhD,IAAI,CAACiD,OAAO,CAAC,MAClB,IAAAC,cAAI,EACFlD,IAAI,CAACiC,IAAI,CAAC,MAAMkB,KAAK,CAACC,IAAI,CAACN,QAAQ,CAAC,CAAC,EACrC9C,IAAI,CAACqD,OAAO,CAAEC,MAAM,IAAI;IACtB,MAAMC,OAAO,GAAkB,EAAE;IACjC,KAAK,IAAIpC,CAAC,GAAGmC,MAAM,CAACE,MAAM,EAAErC,CAAC,IAAI,CAAC,EAAEA,CAAC,GAAGA,CAAC,GAAG,CAAC,EAAE;MAC7CoC,OAAO,CAACE,IAAI,CAACtC,CAAC,CAAC;IACjB;IACA,OAAO,IAAA+B,cAAI,EACTK,OAAO,EACPvD,IAAI,CAAC0D,wBAAwB,CAAEhD,CAAC,IAC9B,IAAAwC,cAAI,EACFF,cAAc,CAACtC,CAAC,CAAC,EACjBV,IAAI,CAACoC,GAAG,CAAEuB,CAAC,IAAKC,IAAI,CAACN,MAAM,EAAE5C,CAAC,GAAG,CAAC,EAAEiD,CAAC,CAAC,CAAC,CACxC,CACF,EACD3D,IAAI,CAAC6D,EAAE,CAACpE,KAAK,CAACqE,YAAY,CAACR,MAAM,CAAC,CAAC,CACpC;EACH,CAAC,CAAC,CACH,CACF;AACH,CAAC;AAED,MAAMM,IAAI,GAAGA,CAAIN,MAAgB,EAAES,MAAc,EAAEC,MAAc,KAAc;EAC7E,MAAMC,GAAG,GAAGX,MAAM,CAACS,MAAM,CAAE;EAC3BT,MAAM,CAACS,MAAM,CAAC,GAAGT,MAAM,CAACU,MAAM,CAAE;EAChCV,MAAM,CAACU,MAAM,CAAC,GAAGC,GAAG;EACpB,OAAOX,MAAM;AACf,CAAC;AAEM,MAAMY,IAAI,GAAOrC,IAAO,IAAoB,IAAID,UAAU,CAAC9B,IAAI,CAACqE,IAAI,CAACtC,IAAI,CAAC,CAAC;AAAAN,OAAA,CAAA2C,IAAA,GAAAA,IAAA", "ignoreList": []}