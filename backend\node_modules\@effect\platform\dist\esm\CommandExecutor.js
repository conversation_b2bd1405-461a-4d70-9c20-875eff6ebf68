import * as internal from "./internal/commandExecutor.js";
/**
 * @since 1.0.0
 * @category type ids
 */
export const TypeId = internal.TypeId;
/**
 * @since 1.0.0
 * @category tags
 */
export const CommandExecutor = internal.CommandExecutor;
/**
 * @since 1.0.0
 * @category symbols
 */
export const ProcessTypeId = internal.ProcessTypeId;
/**
 * @since 1.0.0
 * @category constructors
 */
export const ExitCode = internal.ExitCode;
/**
 * @since 1.0.0
 * @category constructors
 */
export const ProcessId = internal.ProcessId;
/**
 * @since 1.0.0
 * @category constructors
 */
export const makeExecutor = internal.makeExecutor;
//# sourceMappingURL=CommandExecutor.js.map