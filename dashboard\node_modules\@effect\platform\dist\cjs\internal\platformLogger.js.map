{"version": 3, "file": "platformLogger.js", "names": ["Effect", "_interopRequireWildcard", "require", "_Function", "<PERSON><PERSON>", "FileSystem", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "toFile", "exports", "dual", "args", "<PERSON><PERSON><PERSON><PERSON>", "self", "path", "options", "gen", "fs", "logFile", "open", "flag", "encoder", "TextEncoder", "batched", "batchWindow", "output", "ignore", "write", "encode", "join"], "sources": ["../../../src/internal/platformLogger.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAIA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAH,uBAAA,CAAAC,OAAA;AAGA,IAAAG,UAAA,GAAAJ,uBAAA,CAAAC,OAAA;AAA8C,SAAAI,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAN,wBAAAM,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAE9C;AACO,MAAMW,MAAM,GAAAC,OAAA,CAAAD,MAAA,gBAAG,IAAAE,cAAI,EAqBvBC,IAAI,IAAKzB,MAAM,CAAC0B,QAAQ,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC,EAClC,CAACE,IAAI,EAAEC,IAAI,EAAEC,OAAO,KAClBjC,MAAM,CAACkC,GAAG,CAAC,aAAS;EAClB,MAAMC,EAAE,GAAG,OAAO9B,UAAU,CAACA,UAAU;EACvC,MAAM+B,OAAO,GAAG,OAAOD,EAAE,CAACE,IAAI,CAACL,IAAI,EAAE;IAAEM,IAAI,EAAE,IAAI;IAAE,GAAGL;EAAO,CAAE,CAAC;EAChE,MAAMM,OAAO,GAAG,IAAIC,WAAW,EAAE;EACjC,OAAO,OAAOpC,MAAM,CAACqC,OAAO,CAC1BV,IAAI,EACJE,OAAO,EAAES,WAAW,IAAI,IAAI,EAC3BC,MAAM,IAAK3C,MAAM,CAAC4C,MAAM,CAACR,OAAO,CAACS,KAAK,CAACN,OAAO,CAACO,MAAM,CAACH,MAAM,CAACI,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CACnF;AACH,CAAC,CAAC,CACL", "ignoreList": []}