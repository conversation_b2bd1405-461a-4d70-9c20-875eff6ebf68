{"name": "boot-properties-system", "version": "1.0.0", "description": "Complete Properties Management System with Bilingual Support", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd dashboard && npm run dev", "install:all": "npm install && cd backend && npm install && cd ../dashboard && npm install", "build": "cd backend && npm run build && cd ../dashboard && npm run build"}, "devDependencies": {"concurrently": "^8.2.2"}, "keywords": ["properties", "real-estate", "bilingual", "nextjs", "express", "prisma", "uploadthing"], "author": "Boot Development Team", "license": "MIT"}