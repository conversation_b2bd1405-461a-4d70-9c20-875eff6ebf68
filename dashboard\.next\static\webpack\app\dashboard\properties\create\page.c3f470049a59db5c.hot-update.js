"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/properties/create/page",{

/***/ "(app-pages-browser)/./app/dashboard/properties/create/page.tsx":
/*!**************************************************!*\
  !*** ./app/dashboard/properties/create/page.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CreatePropertyPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Home_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Home!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Home_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Home!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useSimpleLanguage */ \"(app-pages-browser)/./hooks/useSimpleLanguage.tsx\");\n/* harmony import */ var _components_LanguageSwitcher__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/LanguageSwitcher */ \"(app-pages-browser)/./components/LanguageSwitcher.tsx\");\n/* harmony import */ var _components_ThemeSwitcher__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ThemeSwitcher */ \"(app-pages-browser)/./components/ThemeSwitcher.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _property_form_steps__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./property-form-steps */ \"(app-pages-browser)/./app/dashboard/properties/create/property-form-steps.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction CreatePropertyPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { language } = (0,_hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_4__.useSimpleLanguage)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Saudi-focused bilingual translations\n    const translations = {\n        ar: {\n            createProperty: 'إنشاء عقار سعودي جديد',\n            backToProperties: 'العودة إلى العقارات',\n            subtitle: 'أضف عقار جديد في المملكة العربية السعودية مع معلومات مفصلة وصور عالية الجودة',\n            properties: 'العقارات',\n            home: 'الرئيسية',\n            welcome: 'مرحباً بك',\n            newProperty: 'عقار سعودي جديد',\n            fillDetails: 'املأ التفاصيل المطلوبة لإضافة عقار جديد في المملكة',\n            ready: 'جاهز للبدء',\n            saudiInterface: 'نظام العقارات السعودي',\n            clickToReturn: 'اضغط للعودة',\n            dashboard: 'لوحة التحكم',\n            saudiProperties: 'العقارات السعودية'\n        },\n        en: {\n            createProperty: 'Create New Saudi Property',\n            backToProperties: 'Back to Properties',\n            subtitle: 'Add a new property in Saudi Arabia with detailed information and high-quality images',\n            properties: 'Properties',\n            home: 'Home',\n            welcome: 'Welcome',\n            newProperty: 'New Saudi Property',\n            fillDetails: 'Fill in the required details to add a new property in Saudi Arabia',\n            ready: 'Ready to Start',\n            saudiInterface: 'Saudi Properties System',\n            clickToReturn: 'Click to Return',\n            dashboard: 'Dashboard',\n            saudiProperties: 'Saudi Properties'\n        }\n    };\n    const t = translations[language];\n    const handleSave = async (formData)=>{\n        setLoading(true);\n        try {\n            const payload = {\n                ...formData,\n                price: parseFloat(formData.price),\n                bedrooms: formData.bedrooms ? parseInt(formData.bedrooms) : undefined,\n                bathrooms: formData.bathrooms ? parseInt(formData.bathrooms) : undefined,\n                area: formData.area ? parseFloat(formData.area) : undefined,\n                yearBuilt: formData.yearBuilt ? parseInt(formData.yearBuilt) : undefined,\n                parking: formData.parking ? parseInt(formData.parking) : undefined\n            };\n            const response = await fetch('/api/v1/properties', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(payload)\n            });\n            if (response.ok) {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success('تم إنشاء العقار بنجاح ✨');\n                router.push('/dashboard/properties');\n            } else {\n                const error = await response.json();\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(error.message || 'فشل في إنشاء العقار ❌');\n            }\n        } catch (error) {\n            console.error('Error creating property:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error('فشل في إنشاء العقار ❌');\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-slate-900 \".concat(language === 'ar' ? 'rtl' : 'ltr'),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-8 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"flex items-center gap-4 text-sm mb-12 \".concat(language === 'ar' ? 'flex-row-reverse' : 'flex-row'),\n                    dir: language === 'ar' ? 'rtl' : 'ltr',\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 px-4 py-3 bg-slate-800 rounded-lg border border-slate-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-emerald-600 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Home_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-4 w-4 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium text-white\",\n                                    children: t.home\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-1 h-1 bg-slate-500 rounded-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.push('/dashboard/properties'),\n                            className: \"flex items-center gap-3 px-4 py-3 bg-slate-800 rounded-lg border border-slate-700 hover:border-emerald-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium text-slate-300 hover:text-emerald-400\",\n                                children: t.properties\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-1 h-1 bg-slate-500 rounded-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 px-4 py-3 bg-emerald-600 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium text-white\",\n                                children: t.newProperty\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-slate-800 rounded-lg border border-slate-700 p-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6 \".concat(language === 'ar' ? 'lg:flex-row-reverse' : ''),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4 \".concat(language === 'ar' ? 'flex-row-reverse' : ''),\n                                            dir: language === 'ar' ? 'rtl' : 'ltr',\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-emerald-600 rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Home_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-6 w-6 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-3xl font-bold text-white\",\n                                                            children: t.createProperty\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                                                            lineNumber: 125,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-slate-400 mt-1\",\n                                                            children: t.fillDetails\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                                                            lineNumber: 128,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl text-slate-700 dark:text-slate-300 max-w-3xl leading-relaxed font-medium \".concat(language === 'ar' ? 'text-right' : 'text-left'),\n                                            dir: language === 'ar' ? 'rtl' : 'ltr',\n                                            children: t.fillDetails\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4 \".concat(language === 'ar' ? 'flex-row-reverse' : ''),\n                                            dir: language === 'ar' ? 'rtl' : 'ltr',\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 text-emerald-600 dark:text-emerald-400\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-emerald-500 rounded-full animate-pulse\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                                                            lineNumber: 138,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: t.ready\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                                                            lineNumber: 139,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 text-teal-600 dark:text-teal-400\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-teal-500 rounded-full animate-pulse delay-100\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                                                            lineNumber: 142,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: t.arabicInterface\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                                                            lineNumber: 143,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3 \".concat(language === 'ar' ? 'flex-row-reverse' : ''),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LanguageSwitcher__WEBPACK_IMPORTED_MODULE_5__.LanguageSwitcher, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeSwitcher__WEBPACK_IMPORTED_MODULE_6__.ThemeSwitcher, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            onClick: ()=>router.push('/dashboard/properties'),\n                                            className: \"flex items-center gap-3 px-8 py-4 h-14 bg-white/90 dark:bg-slate-800/90 backdrop-blur-md border-2 border-emerald-200 dark:border-emerald-700 hover:bg-white dark:hover:bg-slate-800 hover:shadow-xl hover:border-emerald-300 dark:hover:border-emerald-600 transition-all duration-300 text-slate-800 dark:text-slate-200 rounded-xl \".concat(language === 'ar' ? 'flex-row-reverse' : ''),\n                                            dir: language === 'ar' ? 'rtl' : 'ltr',\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-lg\",\n                                                    children: t.backToProperties\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Home_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-6 w-6 \".concat(language === 'ar' ? 'rotate-180' : '')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-slate-500 dark:text-slate-400\",\n                                                children: t.clickToReturn\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_property_form_steps__WEBPACK_IMPORTED_MODULE_8__.PropertyFormSteps, {\n                    onSave: handleSave,\n                    loading: loading\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n            lineNumber: 93,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, this);\n}\n_s(CreatePropertyPage, \"8xW8Ca/pgX8TN8nesAnbFO3tK9U=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_4__.useSimpleLanguage\n    ];\n});\n_c = CreatePropertyPage;\nvar _c;\n$RefreshReg$(_c, \"CreatePropertyPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/properties/create/page.tsx\n"));

/***/ })

});