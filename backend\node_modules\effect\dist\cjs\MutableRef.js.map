{"version": 3, "file": "MutableRef.js", "names": ["Equal", "_interopRequireWildcard", "require", "Dual", "_Inspectable", "_Pipeable", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "TypeId", "Symbol", "for", "MutableRefProto", "toString", "format", "toJSON", "_id", "current", "NodeInspectSymbol", "pipe", "pipeArguments", "arguments", "make", "value", "ref", "create", "exports", "compareAndSet", "dual", "self", "oldValue", "newValue", "equals", "decrement", "update", "decrementAndGet", "updateAndGet", "getAndDecrement", "getAndUpdate", "getAndIncrement", "getAndSet", "ret", "f", "increment", "incrementAndGet", "setAndGet", "toggle", "_"], "sources": ["../../src/MutableRef.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAGA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,IAAA,GAAAF,uBAAA,CAAAC,OAAA;AACA,IAAAE,YAAA,GAAAF,OAAA;AAEA,IAAAG,SAAA,GAAAH,OAAA;AAA6C,SAAAI,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAN,wBAAAM,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAP7C;;;;AASA,MAAMW,MAAM,gBAAkBC,MAAM,CAACC,GAAG,CAAC,mBAAmB,CAAW;AAiBvE,MAAMC,eAAe,GAAyC;EAC5D,CAACH,MAAM,GAAGA,MAAM;EAChBI,QAAQA,CAAA;IACN,OAAO,IAAAC,mBAAM,EAAC,IAAI,CAACC,MAAM,EAAE,CAAC;EAC9B,CAAC;EACDA,MAAMA,CAAA;IACJ,OAAO;MACLC,GAAG,EAAE,YAAY;MACjBC,OAAO,EAAE,IAAAF,mBAAM,EAAC,IAAI,CAACE,OAAO;KAC7B;EACH,CAAC;EACD,CAACC,8BAAiB,IAAC;IACjB,OAAO,IAAI,CAACH,MAAM,EAAE;EACtB,CAAC;EACDI,IAAIA,CAAA;IACF,OAAO,IAAAC,uBAAa,EAAC,IAAI,EAAEC,SAAS,CAAC;EACvC;CACD;AAED;;;;AAIO,MAAMC,IAAI,GAAOC,KAAQ,IAAmB;EACjD,MAAMC,GAAG,GAAGvB,MAAM,CAACwB,MAAM,CAACb,eAAe,CAAC;EAC1CY,GAAG,CAACP,OAAO,GAAGM,KAAK;EACnB,OAAOC,GAAG;AACZ,CAAC;AAED;;;;AAAAE,OAAA,CAAAJ,IAAA,GAAAA,IAAA;AAIO,MAAMK,aAAa,GAAAD,OAAA,CAAAC,aAAA,gBAWtBzC,IAAI,CAAC0C,IAAI,CAGX,CAAC,EAAE,CAACC,IAAI,EAAEC,QAAQ,EAAEC,QAAQ,KAAI;EAChC,IAAIhD,KAAK,CAACiD,MAAM,CAACF,QAAQ,EAAED,IAAI,CAACZ,OAAO,CAAC,EAAE;IACxCY,IAAI,CAACZ,OAAO,GAAGc,QAAQ;IACvB,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd,CAAC,CAAC;AAEF;;;;AAIO,MAAME,SAAS,GAAIJ,IAAwB,IAAyBK,MAAM,CAACL,IAAI,EAAG/B,CAAC,IAAKA,CAAC,GAAG,CAAC,CAAC;AAErG;;;;AAAA4B,OAAA,CAAAO,SAAA,GAAAA,SAAA;AAIO,MAAME,eAAe,GAAIN,IAAwB,IAAaO,YAAY,CAACP,IAAI,EAAG/B,CAAC,IAAKA,CAAC,GAAG,CAAC,CAAC;AAErG;;;;AAAA4B,OAAA,CAAAS,eAAA,GAAAA,eAAA;AAIO,MAAMtC,GAAG,GAAOgC,IAAmB,IAAQA,IAAI,CAACZ,OAAO;AAE9D;;;;AAAAS,OAAA,CAAA7B,GAAA,GAAAA,GAAA;AAIO,MAAMwC,eAAe,GAAIR,IAAwB,IAAaS,YAAY,CAACT,IAAI,EAAG/B,CAAC,IAAKA,CAAC,GAAG,CAAC,CAAC;AAErG;;;;AAAA4B,OAAA,CAAAW,eAAA,GAAAA,eAAA;AAIO,MAAME,eAAe,GAAIV,IAAwB,IAAaS,YAAY,CAACT,IAAI,EAAG/B,CAAC,IAAKA,CAAC,GAAG,CAAC,CAAC;AAErG;;;;AAAA4B,OAAA,CAAAa,eAAA,GAAAA,eAAA;AAIO,MAAMC,SAAS,GAAAd,OAAA,CAAAc,SAAA,gBAWlBtD,IAAI,CAAC0C,IAAI,CAGX,CAAC,EAAE,CAACC,IAAI,EAAEN,KAAK,KAAI;EACnB,MAAMkB,GAAG,GAAGZ,IAAI,CAACZ,OAAO;EACxBY,IAAI,CAACZ,OAAO,GAAGM,KAAK;EACpB,OAAOkB,GAAG;AACZ,CAAC,CAAC;AAEF;;;;AAIO,MAAMH,YAAY,GAAAZ,OAAA,CAAAY,YAAA,gBAWrBpD,IAAI,CAAC0C,IAAI,CAGX,CAAC,EAAE,CAACC,IAAI,EAAEa,CAAC,KAAKF,SAAS,CAACX,IAAI,EAAEa,CAAC,CAAC7C,GAAG,CAACgC,IAAI,CAAC,CAAC,CAAC,CAAC;AAEhD;;;;AAIO,MAAMc,SAAS,GAAId,IAAwB,IAAyBK,MAAM,CAACL,IAAI,EAAG/B,CAAC,IAAKA,CAAC,GAAG,CAAC,CAAC;AAErG;;;;AAAA4B,OAAA,CAAAiB,SAAA,GAAAA,SAAA;AAIO,MAAMC,eAAe,GAAIf,IAAwB,IAAaO,YAAY,CAACP,IAAI,EAAG/B,CAAC,IAAKA,CAAC,GAAG,CAAC,CAAC;AAErG;;;;AAAA4B,OAAA,CAAAkB,eAAA,GAAAA,eAAA;AAIO,MAAMpC,GAAG,GAAAkB,OAAA,CAAAlB,GAAA,gBAWZtB,IAAI,CAAC0C,IAAI,CAGX,CAAC,EAAE,CAACC,IAAI,EAAEN,KAAK,KAAI;EACnBM,IAAI,CAACZ,OAAO,GAAGM,KAAK;EACpB,OAAOM,IAAI;AACb,CAAC,CAAC;AAEF;;;;AAIO,MAAMgB,SAAS,GAAAnB,OAAA,CAAAmB,SAAA,gBAWlB3D,IAAI,CAAC0C,IAAI,CAGX,CAAC,EAAE,CAACC,IAAI,EAAEN,KAAK,KAAI;EACnBM,IAAI,CAACZ,OAAO,GAAGM,KAAK;EACpB,OAAOM,IAAI,CAACZ,OAAO;AACrB,CAAC,CAAC;AAEF;;;;AAIO,MAAMiB,MAAM,GAAAR,OAAA,CAAAQ,MAAA,gBAWfhD,IAAI,CAAC0C,IAAI,CAGX,CAAC,EAAE,CAACC,IAAI,EAAEa,CAAC,KAAKlC,GAAG,CAACqB,IAAI,EAAEa,CAAC,CAAC7C,GAAG,CAACgC,IAAI,CAAC,CAAC,CAAC,CAAC;AAE1C;;;;AAIO,MAAMO,YAAY,GAAAV,OAAA,CAAAU,YAAA,gBAWrBlD,IAAI,CAAC0C,IAAI,CAGX,CAAC,EAAE,CAACC,IAAI,EAAEa,CAAC,KAAKG,SAAS,CAAChB,IAAI,EAAEa,CAAC,CAAC7C,GAAG,CAACgC,IAAI,CAAC,CAAC,CAAC,CAAC;AAEhD;;;;AAIO,MAAMiB,MAAM,GAAIjB,IAAyB,IAA0BK,MAAM,CAACL,IAAI,EAAGkB,CAAC,IAAK,CAACA,CAAC,CAAC;AAAArB,OAAA,CAAAoB,MAAA,GAAAA,MAAA", "ignoreList": []}