import { NextResponse } from "next/server"
import { NextRequest } from "next/server"

// Define protected routes patterns
const protectedRoutes = [
  "/dashboard(.*)",
  "/api/dashboard(.*)",
  "/api/properties(.*)",
  "/api/campaigns(.*)",
  "/api/users(.*)",
]

// Define public routes that should redirect to dashboard if authenticated
const publicRoutes = [
  "/",  // Root path
  "/home(.*)",
  "/about(.*)",
  "/contact(.*)",
]

// Admin-only routes are checked in the server component

export async function middleware(req: NextRequest) {
  // Get the session token from the cookies
  // Check for both possible cookie names (secure and non-secure)
  const sessionToken =
    req.cookies.get("next-auth.session-token")?.value ||
    req.cookies.get("__Secure-next-auth.session-token")?.value

  const isAuthenticated = !!sessionToken

  // Clean up unnecessary cookies and reduce logging
  const url = req.nextUrl.clone()
  const path = url.pathname

  // Only log for debugging in development
  if (process.env.NODE_ENV === 'development') {
    console.log(`Middleware: Path=${path}, Authenticated=${isAuthenticated}`)
  }

  // Check if the path is a protected route
  const isProtectedRoute = protectedRoutes.some(pattern => {
    const regex = new RegExp(`^${pattern}$`)
    return regex.test(path)
  })

  // We'll check admin-only routes in the server component

  // If the user is not authenticated and trying to access a protected route
  if (!isAuthenticated && isProtectedRoute) {
    // Store the original URL to redirect back after authentication
    const signInUrl = new URL('/sign-in', req.url)
    signInUrl.searchParams.set('redirect_url', path)

    // Redirect to the sign-in page
    return NextResponse.redirect(signInUrl)
  }

  // Check if the path is a public route
  const isPublicRoute = path === "/" || publicRoutes.some(pattern => {
    if (pattern === "/") return path === "/";
    const regex = new RegExp(`^${pattern}$`)
    return regex.test(path)
  })

  // If the user is authenticated and trying to access auth pages or public routes
  if (isAuthenticated && (
      path === '/sign-in' ||
      path === '/sign-up' ||
      isPublicRoute
    )) {
    console.log(`Redirecting authenticated user from ${path} to dashboard analytics`)
    // Redirect to the dashboard analytics page
    return NextResponse.redirect(new URL('/dashboard/analytics', req.url))
  }

  // Redirect from /dashboard to /dashboard/analytics
  if (path === '/dashboard' && isAuthenticated) {
    console.log(`Redirecting from /dashboard to /dashboard/analytics`)
    return NextResponse.redirect(new URL('/dashboard/analytics', req.url))
  }

  // For admin-only routes, we'll check the role in the server component
  // since we can't securely check the role in the middleware
  // The role is now an enum: USER, ADMIN, AGENT, CLIENT

  return NextResponse.next()
}

// Configure the middleware to run on specific paths
export const config = {
  matcher: [
    // Include only the paths we want to protect or handle
    '/dashboard',
    '/dashboard/:path*',
    '/api/dashboard/:path*',
    '/api/properties/:path*',
    '/api/campaigns/:path*',
    '/api/users/:path*',
    '/sign-in',
    '/sign-up',
    '/sign-out',
    '/',
  ],
}
