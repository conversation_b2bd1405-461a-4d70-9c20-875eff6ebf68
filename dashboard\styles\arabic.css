/* Arabic Language Support Styles */

/* Import Arabic fonts */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');

/* Arabic font classes */
.lang-ar {
  font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  direction: rtl;
  text-align: right;
}

/* RTL container for proper layout */
.rtl-container {
  direction: rtl;
}

.rtl-container * {
  direction: rtl;
}

/* Fix for input fields in RTL */
.lang-ar input,
.lang-ar textarea {
  text-align: right;
  direction: rtl;
}

/* Fix for select components */
.lang-ar .select-trigger {
  text-align: right;
  direction: rtl;
}

/* Fix for buttons in RTL */
.rtl-container button {
  direction: ltr; /* Keep button content LTR for icons */
}

.rtl-container button .lang-ar {
  direction: rtl; /* But Arabic text should be RTL */
}

/* Enhanced RTL Support for Properties System - Arabic First */
.rtl {
  direction: rtl;
  text-align: right;
  font-family: 'Cairo', 'Noto Sans Arabic', 'Tajawal', 'Amiri', sans-serif;
}

/* Default Arabic styling for all elements */
body {
  font-family: 'Cairo', 'Noto Sans Arabic', 'Segoe UI', sans-serif;
}

/* Arabic-first form styling */
.arabic-form {
  direction: rtl;
  text-align: right;
}

.arabic-form input,
.arabic-form textarea,
.arabic-form select {
  direction: rtl;
  text-align: right;
  font-family: 'Cairo', 'Noto Sans Arabic', sans-serif;
}

/* Enhanced Arabic button styling */
.arabic-button {
  font-family: 'Cairo', 'Noto Sans Arabic', sans-serif;
  direction: rtl;
}

.rtl .ltr {
  direction: ltr;
  text-align: left;
}

/* Enhanced Arabic Typography */
.arabic-text {
  font-family: 'Cairo', 'Noto Sans Arabic', 'Tajawal', 'Amiri', 'Scheherazade New', sans-serif;
  line-height: 1.8;
  letter-spacing: 0.02em;
}

/* Enhanced Form adjustments for Arabic */
.rtl input,
.rtl textarea,
.rtl select {
  text-align: right;
  direction: rtl;
  font-family: 'Cairo', 'Noto Sans Arabic', sans-serif;
}

.rtl input[type="number"] {
  text-align: left;
  direction: ltr;
}

.rtl input[dir="rtl"] {
  text-align: right;
  direction: rtl;
}

/* Flex direction adjustments */
.rtl .flex:not(.flex-col) {
  flex-direction: row-reverse;
}

.rtl .space-x-1 > * + * {
  margin-left: 0;
  margin-right: 0.25rem;
}

.rtl .space-x-2 > * + * {
  margin-left: 0;
  margin-right: 0.5rem;
}

.rtl .space-x-3 > * + * {
  margin-left: 0;
  margin-right: 0.75rem;
}

.rtl .space-x-4 > * + * {
  margin-left: 0;
  margin-right: 1rem;
}

.rtl .space-x-6 > * + * {
  margin-left: 0;
  margin-right: 1.5rem;
}

.rtl .space-x-8 > * + * {
  margin-left: 0;
  margin-right: 2rem;
}

/* Navigation adjustments */
.rtl .breadcrumb {
  flex-direction: row-reverse;
}

.rtl .breadcrumb .separator {
  transform: scaleX(-1);
}

/* Button and Icon adjustments */
.rtl .gap-2 {
  gap: 0.5rem;
}

.rtl .gap-3 {
  gap: 0.75rem;
}

.rtl .gap-4 {
  gap: 1rem;
}

/* Badge and Tag adjustments */
.rtl .badge {
  direction: rtl;
  text-align: right;
}

/* Grid adjustments */
.rtl .grid {
  direction: rtl;
}

/* Form validation and error messages */
.rtl .error-message {
  text-align: right;
  direction: rtl;
  font-family: 'Cairo', 'Noto Sans Arabic', sans-serif;
}

/* Progress indicators */
.rtl .progress-steps {
  flex-direction: row-reverse;
}

/* Dropdown and Select adjustments */
.rtl .select-content {
  text-align: right;
  direction: rtl;
}

.rtl .select-item {
  text-align: right;
  direction: rtl;
  justify-content: flex-end;
}

/* Currency and number display */
.rtl .currency-display {
  direction: ltr;
  unicode-bidi: embed;
  text-align: left;
}

.rtl .number-display {
  direction: ltr;
  unicode-bidi: embed;
}

/* Search and filter adjustments */
.rtl .search-input {
  padding-right: 2.5rem;
  padding-left: 1rem;
}

.rtl .search-icon {
  right: auto;
  left: 0.75rem;
}

/* Property-specific adjustments */
.rtl .property-card {
  direction: rtl;
  text-align: right;
}

.rtl .property-details {
  direction: rtl;
}

.rtl .property-features {
  direction: rtl;
  text-align: right;
}

.rtl .property-amenities {
  direction: rtl;
  text-align: right;
}

/* Form step indicators */
.rtl .step-indicator {
  flex-direction: row-reverse;
}

/* Enhanced spacing for Arabic text */
.rtl .arabic-spacing {
  letter-spacing: 0.025em;
  word-spacing: 0.1em;
}

/* Animation adjustments for RTL */
.rtl .slide-in-right {
  animation: slideInLeft 0.3s ease-out;
}

.rtl .slide-in-left {
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInLeft {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Responsive adjustments for Arabic */
@media (max-width: 768px) {
  .rtl .mobile-stack {
    flex-direction: column;
  }

  .rtl .mobile-reverse {
    flex-direction: column-reverse;
  }
}

/* Enhanced Image Slider Styles */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thumb-orange-300::-webkit-scrollbar-thumb {
  background-color: #fdba74;
  border-radius: 6px;
}

.scrollbar-track-gray-100::-webkit-scrollbar-track {
  background-color: #f3f4f6;
  border-radius: 6px;
}

.dark .scrollbar-thumb-orange-600::-webkit-scrollbar-thumb {
  background-color: #ea580c;
  border-radius: 6px;
}

.dark .scrollbar-track-gray-800::-webkit-scrollbar-track {
  background-color: #1f2937;
  border-radius: 6px;
}

::-webkit-scrollbar {
  height: 6px;
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 6px;
}

::-webkit-scrollbar-thumb {
  background: #fdba74;
  border-radius: 6px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: #f97316;
}

/* Dark mode scrollbar */
.dark ::-webkit-scrollbar-track {
  background: #1f2937;
}

.dark ::-webkit-scrollbar-thumb {
  background: #ea580c;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #dc2626;
}

/* Image Gallery Animations */
.image-gallery-enter {
  opacity: 0;
  transform: scale(0.9);
}

.image-gallery-enter-active {
  opacity: 1;
  transform: scale(1);
  transition: opacity 300ms ease, transform 300ms ease;
}

.image-gallery-exit {
  opacity: 1;
  transform: scale(1);
}

.image-gallery-exit-active {
  opacity: 0;
  transform: scale(0.9);
  transition: opacity 300ms ease, transform 300ms ease;
}

/* Enhanced hover effects for images */
.image-hover-effect {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.image-hover-effect:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Gradient overlays for better text readability */
.gradient-overlay {
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0) 0%,
    rgba(0, 0, 0, 0.1) 50%,
    rgba(0, 0, 0, 0.3) 100%
  );
}

/* Enhanced upload dropzone styles */
.upload-dropzone-enhanced {
  background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
  border: 2px dashed #fdba74;
  transition: all 0.3s ease;
}

.upload-dropzone-enhanced:hover {
  background: linear-gradient(135deg, #fff7ed 0%, #fde68a 100%);
  border-color: #f97316;
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(249, 115, 22, 0.15);
}

.dark .upload-dropzone-enhanced {
  background: linear-gradient(135deg, #431407 0%, #7c2d12 100%);
  border-color: #ea580c;
}

.dark .upload-dropzone-enhanced:hover {
  background: linear-gradient(135deg, #431407 0%, #9a3412 100%);
  border-color: #f97316;
}

/* Fix for badges in RTL */
.rtl-container .badge {
  direction: rtl;
}

/* Fix for form layouts */
.rtl-container .grid {
  direction: rtl;
}

.rtl-container .flex {
  direction: rtl;
}

/* Specific fixes for Arabic text rendering */
.lang-ar {
  line-height: 1.6;
  letter-spacing: 0;
}

/* Fix for card headers in RTL */
.rtl-container .card-header {
  text-align: right;
}

/* Fix for form labels */
.rtl-container label {
  text-align: right;
}

/* Fix for help text */
.rtl-container .text-muted-foreground {
  text-align: right;
}

/* Fix for flex layouts in RTL */
.rtl-container .flex-row-reverse {
  flex-direction: row-reverse;
}

/* Fix for justify content in RTL */
.rtl-container .justify-end {
  justify-content: flex-start;
}

.rtl-container .justify-start {
  justify-content: flex-end;
}

/* Fix for margins and paddings in RTL */
.rtl-container .mr-2 {
  margin-right: 0;
  margin-left: 0.5rem;
}

.rtl-container .ml-2 {
  margin-left: 0;
  margin-right: 0.5rem;
}

.rtl-container .mr-4 {
  margin-right: 0;
  margin-left: 1rem;
}

.rtl-container .ml-4 {
  margin-left: 0;
  margin-right: 1rem;
}

/* Fix for icon rotation in RTL */
.rtl-container .rotate-180 {
  transform: rotate(180deg);
}

/* Ensure proper text alignment for Arabic */
.text-right {
  text-align: right !important;
}

/* Fix for select content positioning */
.rtl-container .select-content {
  direction: rtl;
}

/* Fix for dropdown items */
.rtl-container .select-item {
  text-align: right;
  direction: rtl;
}

/* Fix for input placeholders */
.lang-ar::placeholder {
  text-align: right;
  direction: rtl;
}

/* Fix for textarea placeholders */
.lang-ar textarea::placeholder {
  text-align: right;
  direction: rtl;
}

/* Responsive fixes for Arabic */
@media (max-width: 768px) {
  .rtl-container .grid-cols-2 {
    grid-template-columns: 1fr;
  }

  .rtl-container .flex-row-reverse {
    flex-direction: column-reverse;
  }
}

/* Dark mode support for Arabic text */
@media (prefers-color-scheme: dark) {
  .lang-ar {
    color: #f1f5f9;
  }
}

/* High contrast support */
@media (prefers-contrast: high) {
  .lang-ar {
    font-weight: 500;
  }
}

/* Print styles for Arabic */
@media print {
  .lang-ar {
    font-family: 'Times New Roman', serif;
    font-size: 12pt;
    line-height: 1.5;
  }
}
