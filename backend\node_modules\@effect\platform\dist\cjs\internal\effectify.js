"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.effectify = void 0;
var Effect = _interopRequireWildcard(require("effect/Effect"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
/** @internal */
const effectify = (fn, onError, onSyncError) => (...args) => Effect.async(resume => {
  try {
    fn(...args, (err, result) => {
      if (err) {
        resume(Effect.fail(onError ? onError(err, args) : err));
      } else {
        resume(Effect.succeed(result));
      }
    });
  } catch (err) {
    resume(onSyncError ? Effect.fail(onSyncError(err, args)) : Effect.die(err));
  }
});
exports.effectify = effectify;
//# sourceMappingURL=effectify.js.map