{"version": 3, "file": "Number.js", "names": ["equivalence", "dual", "option", "_Iterable", "order", "predicate", "isNumber", "negate", "n", "multiply", "sum", "self", "that", "sumAll", "collection", "reduce", "subtract", "minuend", "subtrahend", "multiplier", "multiplicand", "multiplyAll", "out", "divide", "dividend", "divisor", "none", "some", "unsafeDivide", "increment", "decrement", "Equivalence", "number", "Order", "lessThan", "lessThanOrEqualTo", "greaterThan", "greaterThanOrEqualTo", "between", "clamp", "min", "max", "sign", "remainder", "selfDecCount", "toString", "split", "length", "divisorDecCount", "decCount", "selfInt", "parseInt", "toFixed", "replace", "divisorInt", "Math", "pow", "nextPow2", "nextPow", "ceil", "log", "parse", "s", "NaN", "Infinity", "trim", "Number", "isNaN", "round", "precision", "factor"], "sources": ["../../src/Number.ts"], "sourcesContent": [null], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiGA,OAAO,KAAKA,WAAW,MAAM,kBAAkB;AAC/C,SAASC,IAAI,QAAQ,eAAe;AACpC,OAAO,KAAKC,MAAM,MAAM,sBAAsB;AAC9C,OAAO,KAAKC,SAAS,MAAM,eAAe;AAE1C,OAAO,KAAKC,KAAK,MAAM,YAAY;AAEnC,OAAO,KAAKC,SAAS,MAAM,gBAAgB;AAE3C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqDA,OAAO,MAAMC,QAAQ,GAAwCD,SAAS,CAACC,QAAQ;AAE/E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCA,OAAO,MAAMC,MAAM,GAAIC,CAAS,IAAaC,QAAQ,CAACD,CAAC,EAAE,CAAC,CAAC,CAAC;AAE5D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6CA,OAAO,MAAME,GAAG,gBAoBZT,IAAI,CAAC,CAAC,EAAE,CAACU,IAAY,EAAEC,IAAY,KAAaD,IAAI,GAAGC,IAAI,CAAC;AAEhE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiDA,OAAO,MAAMC,MAAM,GAAIC,UAA4B,IAAaX,SAAS,CAACY,MAAM,CAACD,UAAU,EAAE,CAAC,EAAEJ,GAAG,CAAC;AAEpG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoDA,OAAO,MAAMM,QAAQ,gBAqBjBf,IAAI,CACN,CAAC,EACD,CAACgB,OAAe,EAAEC,UAAkB,KAAaD,OAAO,GAAGC,UAAU,CACtE;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0DA,OAAO,MAAMT,QAAQ,gBAqBjBR,IAAI,CACN,CAAC,EACD,CAACkB,UAAkB,EAAEC,YAAoB,KAAaD,UAAU,GAAGC,YAAY,CAChF;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8CA,OAAO,MAAMC,WAAW,GAAIP,UAA4B,IAAY;EAClE,IAAIQ,GAAG,GAAG,CAAC;EACX,KAAK,MAAMd,CAAC,IAAIM,UAAU,EAAE;IAC1B,IAAIN,CAAC,KAAK,CAAC,EAAE;MACX,OAAO,CAAC;IACV;IACAc,GAAG,IAAId,CAAC;EACV;EACA,OAAOc,GAAG;AACZ,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwDA,OAAO,MAAMC,MAAM,gBAqBftB,IAAI,CAAC,CAAC,EAAE,CAACuB,QAAgB,EAAEC,OAAe,KAAKA,OAAO,KAAK,CAAC,GAAGvB,MAAM,CAACwB,IAAI,GAAGxB,MAAM,CAACyB,IAAI,CAACH,QAAQ,GAAGC,OAAO,CAAC,CAAC;AAEjH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0DA,OAAO,MAAMG,YAAY,gBAuBrB3B,IAAI,CAAC,CAAC,EAAE,CAACuB,QAAgB,EAAEC,OAAe,KAAaD,QAAQ,GAAGC,OAAO,CAAC;AAE9E;;;;;;;;;;;;;;;AAeA,OAAO,MAAMI,SAAS,GAAIrB,CAAS,IAAaE,GAAG,CAACF,CAAC,EAAE,CAAC,CAAC;AAEzD;;;;;;;;;;;;;;;AAeA,OAAO,MAAMsB,SAAS,GAAItB,CAAS,IAAaQ,QAAQ,CAACR,CAAC,EAAE,CAAC,CAAC;AAE9D;;;;;AAKA,OAAO,MAAMuB,WAAW,GAAoC/B,WAAW,CAACgC,MAAM;AAE9E;;;;;AAKA,OAAO,MAAMC,KAAK,GAAwB7B,KAAK,CAAC4B,MAAM;AAEtD;;;;;;;;;;;;;;;;;;AAkBA,OAAO,MAAME,QAAQ,gBAuCjB9B,KAAK,CAAC8B,QAAQ,CAACD,KAAK,CAAC;AAEzB;;;;;;;;;;;;;;;;;;AAkBA,OAAO,MAAME,iBAAiB,gBAuC1B/B,KAAK,CAAC+B,iBAAiB,CAACF,KAAK,CAAC;AAElC;;;;;;;;;;;;;;;;;;AAkBA,OAAO,MAAMG,WAAW,gBAuCpBhC,KAAK,CAACgC,WAAW,CAACH,KAAK,CAAC;AAE5B;;;;;;;;;;;;;;;;;;AAkBA,OAAO,MAAMI,oBAAoB,gBAuC7BjC,KAAK,CAACiC,oBAAoB,CAACJ,KAAK,CAAC;AAErC;;;;;;;;;;;;;;;;;;;AAmBA,OAAO,MAAMK,OAAO,gBA+ChBlC,KAAK,CAACkC,OAAO,CAACL,KAAK,CAAC;AAExB;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,OAAO,MAAMM,KAAK,gBA2DdnC,KAAK,CAACmC,KAAK,CAACN,KAAK,CAAC;AAEtB;;;;;;;;;;;;;;AAcA,OAAO,MAAMO,GAAG,gBA+BZpC,KAAK,CAACoC,GAAG,CAACP,KAAK,CAAC;AAEpB;;;;;;;;;;;;;;AAcA,OAAO,MAAMQ,GAAG,gBA+BZrC,KAAK,CAACqC,GAAG,CAACR,KAAK,CAAC;AAEpB;;;;;;;;;;;;;;;;;AAiBA,OAAO,MAAMS,IAAI,GAAIlC,CAAS,IAAeyB,KAAK,CAACzB,CAAC,EAAE,CAAC,CAAC;AAExD;;;;;;;;;;;;;;;;;;;;AAoBA,OAAO,MAAMmC,SAAS,gBA2ClB1C,IAAI,CAAC,CAAC,EAAE,CAACuB,QAAgB,EAAEC,OAAe,KAAY;EACxD;EACA,MAAMmB,YAAY,GAAG,CAACpB,QAAQ,CAACqB,QAAQ,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAEC,MAAM;EACrE,MAAMC,eAAe,GAAG,CAACvB,OAAO,CAACoB,QAAQ,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAEC,MAAM;EACvE,MAAME,QAAQ,GAAGL,YAAY,GAAGI,eAAe,GAAGJ,YAAY,GAAGI,eAAe;EAChF,MAAME,OAAO,GAAGC,QAAQ,CAAC3B,QAAQ,CAAC4B,OAAO,CAACH,QAAQ,CAAC,CAACI,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;EACrE,MAAMC,UAAU,GAAGH,QAAQ,CAAC1B,OAAO,CAAC2B,OAAO,CAACH,QAAQ,CAAC,CAACI,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;EACvE,OAAQH,OAAO,GAAGI,UAAU,GAAIC,IAAI,CAACC,GAAG,CAAC,EAAE,EAAEP,QAAQ,CAAC;AACxD,CAAC,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,OAAO,MAAMQ,QAAQ,GAAIjD,CAAS,IAAY;EAC5C,MAAMkD,OAAO,GAAGH,IAAI,CAACI,IAAI,CAACJ,IAAI,CAACK,GAAG,CAACpD,CAAC,CAAC,GAAG+C,IAAI,CAACK,GAAG,CAAC,CAAC,CAAC,CAAC;EACpD,OAAOL,IAAI,CAACd,GAAG,CAACc,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEE,OAAO,CAAC,EAAE,CAAC,CAAC;AAC1C,CAAC;AAED;;;;;;;;;AASA,OAAO,MAAMG,KAAK,GAWbC,CAAC,IAAI;EACR,IAAIA,CAAC,KAAK,KAAK,EAAE;IACf,OAAO5D,MAAM,CAACyB,IAAI,CAACoC,GAAG,CAAC;EACzB;EACA,IAAID,CAAC,KAAK,UAAU,EAAE;IACpB,OAAO5D,MAAM,CAACyB,IAAI,CAACqC,QAAQ,CAAC;EAC9B;EACA,IAAIF,CAAC,KAAK,WAAW,EAAE;IACrB,OAAO5D,MAAM,CAACyB,IAAI,CAAC,CAACqC,QAAQ,CAAC;EAC/B;EACA,IAAIF,CAAC,CAACG,IAAI,EAAE,KAAK,EAAE,EAAE;IACnB,OAAO/D,MAAM,CAACwB,IAAI;EACpB;EACA,MAAMlB,CAAC,GAAG0D,MAAM,CAACJ,CAAC,CAAC;EACnB,OAAOI,MAAM,CAACC,KAAK,CAAC3D,CAAC,CAAC,GAAGN,MAAM,CAACwB,IAAI,GAAGxB,MAAM,CAACyB,IAAI,CAACnB,CAAC,CAAC;AACvD,CAAC;AAED;;;;;;;;;;;;;;;;AAgBA,OAAO,MAAM4D,KAAK,gBAmCdnE,IAAI,CAAC,CAAC,EAAE,CAACU,IAAY,EAAE0D,SAAiB,KAAY;EACtD,MAAMC,MAAM,GAAGf,IAAI,CAACC,GAAG,CAAC,EAAE,EAAEa,SAAS,CAAC;EACtC,OAAOd,IAAI,CAACa,KAAK,CAACzD,IAAI,GAAG2D,MAAM,CAAC,GAAGA,MAAM;AAC3C,CAAC,CAAC", "ignoreList": []}