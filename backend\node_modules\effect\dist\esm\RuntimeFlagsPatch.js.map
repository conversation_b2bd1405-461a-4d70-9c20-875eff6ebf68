{"version": 3, "file": "RuntimeFlagsPatch.js", "names": ["runtimeFlags", "internal", "empty", "make", "enable", "disable", "isEmpty", "isActive", "isEnabled", "isDisabled", "includes", "and<PERSON><PERSON>", "both", "either", "exclude", "inverse", "enabledSet", "disabledSet", "render", "renderPatch"], "sources": ["../../src/RuntimeFlagsPatch.ts"], "sourcesContent": [null], "mappings": "AAAA;;;AAGA,OAAO,KAAKA,YAAY,MAAM,4BAA4B;AAC1D,OAAO,KAAKC,QAAQ,MAAM,iCAAiC;AAW3D;;;;;;AAMA,OAAO,MAAMC,KAAK,GAAsBD,QAAQ,CAACC,KAAK;AAEtD;;;;AAIA,OAAO,MAAMC,IAAI,GAA2DF,QAAQ,CAACE,IAAI;AAEzF;;;;;;AAMA,OAAO,MAAMC,MAAM,GAA0DH,QAAQ,CAACG,MAAM;AAE5F;;;;;;AAMA,OAAO,MAAMC,OAAO,GAA0DJ,QAAQ,CAACI,OAAO;AAE9F;;;;;;AAMA,OAAO,MAAMC,OAAO,GAA0CL,QAAQ,CAACK,OAAO;AAE9E;;;;;;;AAOA,OAAO,MAAMC,QAAQ,GAiBjBN,QAAQ,CAACM,QAAQ;AAErB;;;;;;;AAOA,OAAO,MAAMC,SAAS,GAiBlBP,QAAQ,CAACO,SAAS;AAEtB;;;;;;;AAOA,OAAO,MAAMC,UAAU,GAiBnBR,QAAQ,CAACQ,UAAU;AAEvB;;;;;;;AAOA,OAAO,MAAMC,QAAQ,GAiBjBT,QAAQ,CAACM,QAAQ;AAErB;;;;;;;AAOA,OAAO,MAAMI,OAAO,GAiBhBV,QAAQ,CAACU,OAAO;AAEpB;;;;;;;AAOA,OAAO,MAAMC,IAAI,GAiBbX,QAAQ,CAACW,IAAI;AAEjB;;;;;;;AAOA,OAAO,MAAMC,MAAM,GAiBfZ,QAAQ,CAACY,MAAM;AAEnB;;;;;;;AAOA,OAAO,MAAMC,OAAO,GAiBhBb,QAAQ,CAACa,OAAO;AAEpB;;;;;;;AAOA,OAAO,MAAMC,OAAO,GAAoDd,QAAQ,CAACc,OAAO;AAExF;;;;;;;AAOA,OAAO,MAAMC,UAAU,GAAuEhB,YAAY,CAACgB,UAAU;AAErH;;;;;;;AAOA,OAAO,MAAMC,WAAW,GAAuEjB,YAAY,CAACiB,WAAW;AAEvH;;;;;;AAMA,OAAO,MAAMC,MAAM,GAAwClB,YAAY,CAACmB,WAAW", "ignoreList": []}