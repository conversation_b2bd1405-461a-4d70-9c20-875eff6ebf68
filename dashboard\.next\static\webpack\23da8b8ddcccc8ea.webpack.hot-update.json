{"c": ["app/layout", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Ccomponents%5C%5Cauth%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Ccomponents%5C%5Chydration-fix.tsx%22%2C%22ids%22%3A%5B%22HydrationFix%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAhmed%5C%5CDesktop%5C%5Ccode%5C%5Cboot%5C%5Cdashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Cairo%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%2C%5C%22arabic%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-cairo%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22cairo%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=false!"]}