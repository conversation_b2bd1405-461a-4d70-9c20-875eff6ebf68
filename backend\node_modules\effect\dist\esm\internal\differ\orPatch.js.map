{"version": 3, "file": "orPatch.js", "names": ["Chunk", "E", "Equal", "Dual", "Structural", "OrPatchTypeId", "Symbol", "for", "variance", "a", "PatchProto", "prototype", "_Value", "_Key", "_Patch", "EmptyProto", "Object", "assign", "create", "_tag", "_empty", "empty", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "makeAndThen", "first", "second", "o", "SetLeftProto", "makeSetLeft", "value", "SetRightProto", "makeSetRight", "UpdateLeftProto", "makeUpdateLeft", "patch", "UpdateRightProto", "makeUpdateRight", "diff", "options", "oldValue", "newValue", "valuePatch", "left", "equals", "right", "combine", "dual", "self", "that", "patches", "of", "result", "isNonEmpty", "head", "headNonEmpty", "tail", "tailNonEmpty", "prepend"], "sources": ["../../../../src/internal/differ/orPatch.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,gBAAgB;AAGvC,OAAO,KAAKC,CAAC,MAAM,iBAAiB;AACpC,OAAO,KAAKC,KAAK,MAAM,gBAAgB;AACvC,OAAO,KAAKC,IAAI,MAAM,mBAAmB;AACzC,SAASC,UAAU,QAAQ,YAAY;AAEvC;AACA,OAAO,MAAMC,aAAa,gBAAqBC,MAAM,CAACC,GAAG,CAAC,sBAAsB,CAAqB;AAErG,SAASC,QAAQA,CAAOC,CAAI;EAC1B,OAAOA,CAAiB;AAC1B;AAEA;AACA,MAAMC,UAAU,GAAG;EACjB,GAAGN,UAAU,CAACO,SAAS;EACvB,CAACN,aAAa,GAAG;IACfO,MAAM,EAAEJ,QAAQ;IAChBK,IAAI,EAAEL,QAAQ;IACdM,MAAM,EAAEN;;CAEX;AASD,MAAMO,UAAU,gBAAGC,MAAM,CAACC,MAAM,eAACD,MAAM,CAACE,MAAM,CAACR,UAAU,CAAC,EAAE;EAC1DS,IAAI,EAAE;CACP,CAAC;AAEF,MAAMC,MAAM,gBAAGJ,MAAM,CAACE,MAAM,CAACH,UAAU,CAAC;AAExC;AACA,OAAO,MAAMM,KAAK,GAAGA,CAAA,KAKhBD,MAAM;AAWX,MAAME,YAAY,gBAAGN,MAAM,CAACC,MAAM,eAACD,MAAM,CAACE,MAAM,CAACR,UAAU,CAAC,EAAE;EAC5DS,IAAI,EAAE;CACP,CAAC;AAEF;AACA,OAAO,MAAMI,WAAW,GAAGA,CACzBC,KAAoD,EACpDC,MAAqD,KAMnD;EACF,MAAMC,CAAC,GAAGV,MAAM,CAACE,MAAM,CAACI,YAAY,CAAC;EACrCI,CAAC,CAACF,KAAK,GAAGA,KAAK;EACfE,CAAC,CAACD,MAAM,GAAGA,MAAM;EACjB,OAAOC,CAAC;AACV,CAAC;AAUD,MAAMC,YAAY,gBAAGX,MAAM,CAACC,MAAM,eAACD,MAAM,CAACE,MAAM,CAACR,UAAU,CAAC,EAAE;EAC5DS,IAAI,EAAE;CACP,CAAC;AAEF;AACA,OAAO,MAAMS,WAAW,GACtBC,KAAY,IAMV;EACF,MAAMH,CAAC,GAAGV,MAAM,CAACE,MAAM,CAACS,YAAY,CAAC;EACrCD,CAAC,CAACG,KAAK,GAAGA,KAAK;EACf,OAAOH,CAAC;AACV,CAAC;AAUD,MAAMI,aAAa,gBAAGd,MAAM,CAACC,MAAM,eAACD,MAAM,CAACE,MAAM,CAACR,UAAU,CAAC,EAAE;EAC7DS,IAAI,EAAE;CACP,CAAC;AAEF;AACA,OAAO,MAAMY,YAAY,GACvBF,KAAa,IAMX;EACF,MAAMH,CAAC,GAAGV,MAAM,CAACE,MAAM,CAACY,aAAa,CAAC;EACtCJ,CAAC,CAACG,KAAK,GAAGA,KAAK;EACf,OAAOH,CAAC;AACV,CAAC;AAUD,MAAMM,eAAe,gBAAGhB,MAAM,CAACC,MAAM,eAACD,MAAM,CAACE,MAAM,CAACR,UAAU,CAAC,EAAE;EAC/DS,IAAI,EAAE;CACP,CAAC;AAEF;AACA,OAAO,MAAMc,cAAc,GACzBC,KAAY,IAMV;EACF,MAAMR,CAAC,GAAGV,MAAM,CAACE,MAAM,CAACc,eAAe,CAAC;EACxCN,CAAC,CAACQ,KAAK,GAAGA,KAAK;EACf,OAAOR,CAAC;AACV,CAAC;AAUD,MAAMS,gBAAgB,gBAAGnB,MAAM,CAACC,MAAM,eAACD,MAAM,CAACE,MAAM,CAACR,UAAU,CAAC,EAAE;EAChES,IAAI,EAAE;CACP,CAAC;AAEF;AACA,OAAO,MAAMiB,eAAe,GAC1BF,KAAa,IAMX;EACF,MAAMR,CAAC,GAAGV,MAAM,CAACE,MAAM,CAACiB,gBAAgB,CAAC;EACzCT,CAAC,CAACQ,KAAK,GAAGA,KAAK;EACf,OAAOR,CAAC;AACV,CAAC;AAUD;AACA,OAAO,MAAMW,IAAI,GACfC,OAKC,IACgD;EACjD,QAAQA,OAAO,CAACC,QAAQ,CAACpB,IAAI;IAC3B,KAAK,MAAM;MAAE;QACX,QAAQmB,OAAO,CAACE,QAAQ,CAACrB,IAAI;UAC3B,KAAK,MAAM;YAAE;cACX,MAAMsB,UAAU,GAAGH,OAAO,CAACI,IAAI,CAACL,IAAI,CAACC,OAAO,CAACC,QAAQ,CAACG,IAAI,EAAEJ,OAAO,CAACE,QAAQ,CAACE,IAAI,CAAC;cAClF,IAAIxC,KAAK,CAACyC,MAAM,CAACF,UAAU,EAAEH,OAAO,CAACI,IAAI,CAACrB,KAAK,CAAC,EAAE;gBAChD,OAAOA,KAAK,EAAE;cAChB;cACA,OAAOY,cAAc,CAACQ,UAAU,CAAC;YACnC;UACA,KAAK,OAAO;YAAE;cACZ,OAAOV,YAAY,CAACO,OAAO,CAACE,QAAQ,CAACI,KAAK,CAAC;YAC7C;QACF;MACF;IACA,KAAK,OAAO;MAAE;QACZ,QAAQN,OAAO,CAACE,QAAQ,CAACrB,IAAI;UAC3B,KAAK,MAAM;YAAE;cACX,OAAOS,WAAW,CAACU,OAAO,CAACE,QAAQ,CAACE,IAAI,CAAC;YAC3C;UACA,KAAK,OAAO;YAAE;cACZ,MAAMD,UAAU,GAAGH,OAAO,CAACM,KAAK,CAACP,IAAI,CAACC,OAAO,CAACC,QAAQ,CAACK,KAAK,EAAEN,OAAO,CAACE,QAAQ,CAACI,KAAK,CAAC;cACrF,IAAI1C,KAAK,CAACyC,MAAM,CAACF,UAAU,EAAEH,OAAO,CAACM,KAAK,CAACvB,KAAK,CAAC,EAAE;gBACjD,OAAOA,KAAK,EAAE;cAChB;cACA,OAAOe,eAAe,CAACK,UAAU,CAAC;YACpC;QACF;MACF;EACF;AACF,CAAC;AAED;AACA,OAAO,MAAMI,OAAO,gBAAG1C,IAAI,CAAC2C,IAAI,CAU9B,CAAC,EAAE,CAACC,IAAI,EAAEC,IAAI,KAAKzB,WAAW,CAACwB,IAAI,EAAEC,IAAI,CAAC,CAAC;AAE7C;AACA,OAAO,MAAMd,KAAK,gBAAG/B,IAAI,CAAC2C,IAAI,CAgB5B,CAAC,EAAE,CACHC,IAAmD,EACnD;EAAEL,IAAI;EAAEH,QAAQ;EAAEK;AAAK,CAItB,KACC;EACF,IAAKG,IAAoB,CAAC5B,IAAI,KAAK,OAAO,EAAE;IAC1C,OAAOoB,QAAQ;EACjB;EACA,IAAIU,OAAO,GAA+DjD,KAAK,CAACkD,EAAE,CAACH,IAAI,CAAC;EACxF,IAAII,MAAM,GAAGZ,QAAQ;EACrB,OAAOvC,KAAK,CAACoD,UAAU,CAACH,OAAO,CAAC,EAAE;IAChC,MAAMI,IAAI,GAAgBrD,KAAK,CAACsD,YAAY,CAACL,OAAO,CAAgB;IACpE,MAAMM,IAAI,GAAGvD,KAAK,CAACwD,YAAY,CAACP,OAAO,CAAC;IACxC,QAAQI,IAAI,CAAClC,IAAI;MACf,KAAK,OAAO;QAAE;UACZ8B,OAAO,GAAGM,IAAI;UACd;QACF;MACA,KAAK,SAAS;QAAE;UACdN,OAAO,GAAGjD,KAAK,CAACyD,OAAO,CAACJ,IAAI,CAAC7B,KAAK,CAAC,CAACxB,KAAK,CAACyD,OAAO,CAACJ,IAAI,CAAC5B,MAAM,CAAC,CAAC8B,IAAI,CAAC,CAAC;UACrE;QACF;MACA,KAAK,YAAY;QAAE;UACjB,IAAIJ,MAAM,CAAChC,IAAI,KAAK,MAAM,EAAE;YAC1BgC,MAAM,GAAGlD,CAAC,CAACyC,IAAI,CAACA,IAAI,CAACR,KAAK,CAACmB,IAAI,CAACnB,KAAK,EAAEiB,MAAM,CAACT,IAAI,CAAC,CAAC;UACtD;UACAO,OAAO,GAAGM,IAAI;UACd;QACF;MACA,KAAK,aAAa;QAAE;UAClB,IAAIJ,MAAM,CAAChC,IAAI,KAAK,OAAO,EAAE;YAC3BgC,MAAM,GAAGlD,CAAC,CAAC2C,KAAK,CAACA,KAAK,CAACV,KAAK,CAACmB,IAAI,CAACnB,KAAK,EAAEiB,MAAM,CAACP,KAAK,CAAC,CAAC;UACzD;UACAK,OAAO,GAAGM,IAAI;UACd;QACF;MACA,KAAK,SAAS;QAAE;UACdJ,MAAM,GAAGlD,CAAC,CAACyC,IAAI,CAACW,IAAI,CAACxB,KAAK,CAAC;UAC3BoB,OAAO,GAAGM,IAAI;UACd;QACF;MACA,KAAK,UAAU;QAAE;UACfJ,MAAM,GAAGlD,CAAC,CAAC2C,KAAK,CAACS,IAAI,CAACxB,KAAK,CAAC;UAC5BoB,OAAO,GAAGM,IAAI;UACd;QACF;IACF;EACF;EACA,OAAOJ,MAAM;AACf,CAAC,CAAC", "ignoreList": []}