import * as internal from "./internal/cache.js";
/**
 * @since 2.0.0
 * @category symbols
 */
export const CacheTypeId = internal.CacheTypeId;
/**
 * @since 3.6.4
 * @category symbols
 */
export const ConsumerCacheTypeId = internal.ConsumerCacheTypeId;
/**
 * Constructs a new cache with the specified capacity, time to live, and
 * lookup function.
 *
 * @since 2.0.0
 * @category constructors
 */
export const make = internal.make;
/**
 * Constructs a new cache with the specified capacity, time to live, and
 * lookup function, where the time to live can depend on the `Exit` value
 * returned by the lookup function.
 *
 * @since 2.0.0
 * @category constructors
 */
export const makeWith = internal.makeWith;
/**
 * Constructs a new `CacheStats` from the specified values.
 *
 * @since 2.0.0
 * @category constructors
 */
export const makeCacheStats = internal.makeCacheStats;
/**
 * Constructs a new `EntryStats` from the specified values.
 *
 * @since 2.0.0
 * @category constructors
 */
export const makeEntryStats = internal.makeEntryStats;
//# sourceMappingURL=Cache.js.map