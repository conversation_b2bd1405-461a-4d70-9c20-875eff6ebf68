import * as effect_Types from 'effect/Types';
import * as Standard from '@standard-schema/spec';
import * as Cause from 'effect/Cause';
import * as Schema from 'effect/Schema';
import { Json } from '@uploadthing/shared';

type ParseFn<TType> = (input: unknown) => Promise<TType>;
type ParserZodEsque<TInput extends Json, TParsedInput> = {
    _input: TInput;
    _output: TParsedInput;
    parseAsync: ParseFn<TParsedInput>;
};
type JsonParser<In extends Json, Out = In> = ParserZodEsque<In, Out> | Standard.StandardSchemaV1<In, Out> | Schema.Schema<Out, In>;
declare const ParserError_base: new <A extends Record<string, any> = {}>(args: effect_Types.Equals<A, {}> extends true ? void : { readonly [P in keyof A as P extends "_tag" ? never : P]: A[P]; }) => Cause.YieldableError & {
    readonly _tag: "ParserError";
} & Readonly<A>;
declare class ParserError extends ParserError_base<{
    cause: unknown;
}> {
    message: string;
}
declare function getParseFn<TOut extends Json, TParser extends JsonParser<any, TOut>>(parser: TParser): ParseFn<TOut>;

export { ParserError, getParseFn };
export type { JsonParser, ParseFn, ParserZodEsque };
