{"version": 3, "file": "Redacted.js", "names": ["Equivalence", "redacted_", "RedactedTypeId", "isRedacted", "make", "value", "unsafeWipe", "getEquivalence", "isEquivalent", "x", "y"], "sources": ["../../src/Redacted.ts"], "sourcesContent": [null], "mappings": "AASA,OAAO,KAAKA,WAAW,MAAM,kBAAkB;AAC/C,OAAO,KAAKC,SAAS,MAAM,wBAAwB;AAInD;;;;AAIA,OAAO,MAAMC,cAAc,GAAkBD,SAAS,CAACC,cAAc;AAoCrE;;;;AAIA,OAAO,MAAMC,UAAU,GAA2CF,SAAS,CAACE,UAAU;AAEtF;;;;;;;;;;;;;;AAcA,OAAO,MAAMC,IAAI,GAAiCH,SAAS,CAACG,IAAI;AAEhE;;;;;;;;;;;;;;;;;AAiBA,OAAO,MAAMC,KAAK,GAAgCJ,SAAS,CAACI,KAAK;AAEjE;;;;;;;;;;;;;;;;;;;;;;AAsBA,OAAO,MAAMC,UAAU,GAAsCL,SAAS,CAACK,UAAU;AAEjF;;;;;;;;;;;;;;;;;;;;;;;AAuBA,OAAO,MAAMC,cAAc,GAAOC,YAAwC,IACxER,WAAW,CAACI,IAAI,CAAC,CAACK,CAAC,EAAEC,CAAC,KAAKF,YAAY,CAACH,KAAK,CAACI,CAAC,CAAC,EAAEJ,KAAK,CAACK,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}