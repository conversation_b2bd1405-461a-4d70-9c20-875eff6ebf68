(()=>{var e={};e.id=5683,e.ids=[5683],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12484:(e,t,r)=>{"use strict";r.d(t,{ES:()=>s,eo:()=>o});let o=["en","ar"],s={en:{translation:{"sidebar.analytics":"Analytics","sidebar.user":"Dashboard","sidebar.clients":"Clients","sidebar.messaging":"Messaging","sidebar.marketing":"Marketing","sidebar.campaigns":"Campaigns","sidebar.templates":"Templates","sidebar.appointments":"Appointments","sidebar.ai-chatbot":"AI Chatbot","sidebar.users":"Users","sidebar.properties":"Properties","sidebar.settings":"Settings","sidebar.profile":"Profile","role.admin":"Administrator","role.agent":"Agent","role.client":"Client","users.invite":"Invite User","users.role.change":"Change Role","users.edit":"Edit User","users.delete":"Delete User","users.status.active":"Active","users.status.inactive":"Inactive","users.status.suspended":"Suspended","form.email":"Email","form.firstName":"First Name","form.lastName":"Last Name","form.role":"Role","form.cancel":"Cancel","form.save":"Save Changes","form.send":"Send Invitation","toast.success":"Success","toast.error":"Error","toast.role.updated":"Role updated successfully","toast.invitation.sent":"Invitation sent successfully","property.create.title":"Create New Property","property.create.subtitle":"Add a new property listing to your real estate portfolio","property.form.propertyTitle":"Property Title","property.form.price":"Price (USD)","property.form.location":"Location","property.form.bedrooms":"Bedrooms","property.form.bathrooms":"Bathrooms","property.form.area":"Area (sq ft)","property.form.status":"Status","property.form.type":"Property Type","property.form.description":"Description","property.form.images":"Property Images","property.form.uploadImages":"Upload Images","property.form.propertyInfo":"Property Information","property.form.requiredFields":"All fields marked with * are required. Adding high-quality images will increase the visibility of your property listing.","property.form.cancel":"Cancel","property.form.create":"Create Property","property.form.creating":"Creating Property...","property.form.selectStatus":"Select status","property.form.selectType":"Select type","property.form.enterTitle":"Enter property title","property.form.enterPrice":"Enter price","property.form.enterLocation":"Enter property location","property.form.enterBedrooms":"Number of bedrooms","property.form.enterBathrooms":"Number of bathrooms","property.form.enterArea":"Property area","property.form.enterDescription":"Enter property description","property.status.active":"Active","property.status.pending":"Pending","property.status.sold":"Sold","property.type.villa":"Villa","property.type.apartment":"Apartment","property.type.townhouse":"Townhouse","property.type.penthouse":"Penthouse","property.type.duplex":"Duplex","property.toast.success.title":"Property created successfully!","property.toast.success.description":"Your property has been added to the listings.","property.toast.error.title":"Failed to create property","property.toast.error.description":"There was an error creating your property. Please try again.","auth.signIn":"Sign In","auth.signUp":"Sign Up","auth.createAccount":"Create Account","auth.emailAddress":"Email Address","auth.password":"Password","auth.newPassword":"New Password","auth.confirmPassword":"Confirm Password","auth.continue":"Continue","auth.continueWithGoogle":"Continue with Google","auth.orContinueWith":"or continue with","auth.noAccount":"Don't have an account?","auth.alreadyHaveAccount":"Already have an account?","auth.forgotPassword":"Forgot password?","auth.resetPassword":"Reset Password","auth.resetPasswordInstructions":"Enter your email address and we'll send you a link to reset your password.","auth.goBack":"Go back","auth.checkEmail":"Check your email","auth.codeSentTo":"We sent a code to","auth.verificationCode":"Verification Code","auth.resendCode":"Resend Code","auth.enterPassword":"Enter your password","auth.completeYourProfile":"Complete your profile","auth.firstName":"First Name","auth.lastName":"Last Name","auth.redirectMessage":"You need to sign in to access this page. You'll be redirected after authentication.","auth.createAccountRedirect":"You need to create an account to access this page. You'll be redirected after registration.","auth.accessDenied":"Access Denied","auth.insufficientPermissions":"You don't have permission to access this page. Your current role is: {{role}}.","auth.notAuthenticated":"You need to be authenticated to access this page.","auth.contactAdminForAccess":"Please contact an administrator if you need access to this resource.","auth.welcomeBack":"Welcome Back","auth.signInToContinue":"Sign in to continue to your account","auth.enterDetailsToCreateAccount":"Enter your details to create an account","auth.email":"Email","auth.invalidCredentials":"Invalid email or password","auth.registrationFailed":"Registration failed","auth.signInAfterRegistrationFailed":"Registration successful but sign-in failed","auth.registrationSuccessful":"Registration successful","auth.loginSuccessful":"Login successful","auth.signingOut":"Signing Out","auth.redirectingToSignIn":"Redirecting to sign-in page...","common.somethingWentWrong":"Something went wrong. Please try again.","common.loading":"Loading...","common.goBack":"Go Back","common.goToHomePage":"Go to Home Page"}},ar:{translation:{"sidebar.analytics":"التحليلات","sidebar.user":"لوحة التحكم","sidebar.clients":"العملاء","sidebar.messaging":"المراسلة","sidebar.marketing":"التسويق","sidebar.campaigns":"الحملات","sidebar.templates":"القوالب","sidebar.appointments":"المواعيد","sidebar.ai-chatbot":"روبوت المحادثة","sidebar.users":"المستخدمين","sidebar.properties":"العقارات","sidebar.settings":"الإعدادات","sidebar.profile":"الملف الشخصي","role.admin":"مدير","role.agent":"وكيل","role.client":"عميل","users.invite":"دعوة مستخدم","users.role.change":"تغيير الدور","users.edit":"تعديل المستخدم","users.delete":"حذف المستخدم","users.status.active":"نشط","users.status.inactive":"غير نشط","users.status.suspended":"معلق","form.email":"البريد الإلكتروني","form.firstName":"الاسم الأول","form.lastName":"اسم العائلة","form.role":"الدور","form.cancel":"إلغاء","form.save":"حفظ التغييرات","form.send":"إرسال الدعوة","toast.success":"نجاح","toast.error":"خطأ","toast.role.updated":"تم تحديث الدور بنجاح","toast.invitation.sent":"تم إرسال الدعوة بنجاح","property.create.title":"إنشاء عقار جديد","property.create.subtitle":"أضف قائمة عقارية جديدة إلى محفظتك العقارية","property.form.propertyTitle":"عنوان العقار","property.form.price":"السعر (دولار أمريكي)","property.form.location":"الموقع","property.form.bedrooms":"غرف النوم","property.form.bathrooms":"الحمامات","property.form.area":"المساحة (قدم مربع)","property.form.status":"الحالة","property.form.type":"نوع العقار","property.form.description":"الوصف","property.form.images":"صور العقار","property.form.uploadImages":"تحميل الصور","property.form.propertyInfo":"معلومات العقار","property.form.requiredFields":"جميع الحقول المميزة بعلامة * مطلوبة. إضافة صور عالية الجودة ستزيد من ظهور قائمة العقار الخاص بك.","property.form.cancel":"إلغاء","property.form.create":"إنشاء العقار","property.form.creating":"جاري إنشاء العقار...","property.form.selectStatus":"اختر الحالة","property.form.selectType":"اختر النوع","property.form.enterTitle":"أدخل عنوان العقار","property.form.enterPrice":"أدخل السعر","property.form.enterLocation":"أدخل موقع العقار","property.form.enterBedrooms":"عدد غرف النوم","property.form.enterBathrooms":"عدد الحمامات","property.form.enterArea":"مساحة العقار","property.form.enterDescription":"أدخل وصف العقار","property.status.active":"نشط","property.status.pending":"قيد الانتظار","property.status.sold":"مباع","property.type.villa":"فيلا","property.type.apartment":"شقة","property.type.townhouse":"تاون هاوس","property.type.penthouse":"بنتهاوس","property.type.duplex":"دوبلكس","property.toast.success.title":"تم إنشاء العقار بنجاح!","property.toast.success.description":"تمت إضافة العقار الخاص بك إلى القوائم.","property.toast.error.title":"فشل في إنشاء العقار","property.toast.error.description":"حدث خطأ أثناء إنشاء العقار الخاص بك. يرجى المحاولة مرة أخرى.","auth.signIn":"تسجيل الدخول","auth.signUp":"إنشاء حساب","auth.createAccount":"إنشاء حساب جديد","auth.emailAddress":"البريد الإلكتروني","auth.password":"كلمة المرور","auth.newPassword":"كلمة المرور الجديدة","auth.confirmPassword":"تأكيد كلمة المرور","auth.continue":"متابعة","auth.continueWithGoogle":"متابعة باستخدام جوجل","auth.orContinueWith":"أو متابعة باستخدام","auth.noAccount":"ليس لديك حساب؟","auth.alreadyHaveAccount":"لديك حساب بالفعل؟","auth.forgotPassword":"نسيت كلمة المرور؟","auth.resetPassword":"إعادة تعيين كلمة المرور","auth.resetPasswordInstructions":"أدخل بريدك الإلكتروني وسنرسل لك رابطًا لإعادة تعيين كلمة المرور.","auth.goBack":"العودة","auth.checkEmail":"تحقق من بريدك الإلكتروني","auth.codeSentTo":"لقد أرسلنا رمزًا إلى","auth.verificationCode":"رمز التحقق","auth.resendCode":"إعادة إرسال الرمز","auth.enterPassword":"أدخل كلمة المرور","auth.completeYourProfile":"أكمل ملفك الشخصي","auth.firstName":"الاسم الأول","auth.lastName":"اسم العائلة","auth.redirectMessage":"تحتاج إلى تسجيل الدخول للوصول إلى هذه الصفحة. ستتم إعادة توجيهك بعد المصادقة.","auth.createAccountRedirect":"تحتاج إلى إنشاء حساب للوصول إلى هذه الصفحة. ستتم إعادة توجيهك بعد التسجيل.","auth.accessDenied":"تم رفض الوصول","auth.insufficientPermissions":"ليس لديك إذن للوصول إلى هذه الصفحة. دورك الحالي هو: {{role}}.","auth.notAuthenticated":"تحتاج إلى المصادقة للوصول إلى هذه الصفحة.","auth.contactAdminForAccess":"يرجى الاتصال بمسؤول إذا كنت بحاجة إلى الوصول إلى هذا المورد.","auth.welcomeBack":"مرحبًا بعودتك","auth.signInToContinue":"قم بتسجيل الدخول للمتابعة إلى حسابك","auth.enterDetailsToCreateAccount":"أدخل بياناتك لإنشاء حساب","auth.email":"البريد الإلكتروني","auth.invalidCredentials":"بريد إلكتروني أو كلمة مرور غير صالحة","auth.registrationFailed":"فشل التسجيل","auth.signInAfterRegistrationFailed":"تم التسجيل بنجاح ولكن فشل تسجيل الدخول","auth.registrationSuccessful":"تم التسجيل بنجاح","auth.loginSuccessful":"تم تسجيل الدخول بنجاح","auth.signingOut":"تسجيل الخروج","auth.redirectingToSignIn":"جاري إعادة التوجيه إلى صفحة تسجيل الدخول...","common.somethingWentWrong":"حدث خطأ ما. يرجى المحاولة مرة أخرى.","common.loading":"جار التحميل...","common.goBack":"العودة","common.goToHomePage":"الذهاب إلى الصفحة الرئيسية"}}}},16200:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>d,pages:()=>c,routeModule:()=>l,tree:()=>u});var o=r(65239),s=r(48088),a=r(88170),i=r.n(a),n=r(30893),p={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(p[e]=()=>n[e]);r.d(t,p);let u={children:["",{children:["sign-out",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,65852)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\sign-out\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,60566)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\sign-out\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},l=new o.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/sign-out/page",pathname:"/sign-out",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27063:(e,t,r)=>{Promise.resolve().then(r.bind(r,65852))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34302:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var o=r(60687);r(43210);var s=r(16189);r(99208);var a=r(68082);function i(){let{t:e}=(0,a.B)();return(0,s.useRouter)(),(0,o.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-screen bg-background",children:[(0,o.jsx)("h1",{className:"text-2xl font-bold mb-4",children:e("auth.signingOut")}),(0,o.jsx)("p",{className:"text-muted-foreground mb-8",children:e("auth.redirectingToSignIn")}),(0,o.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65852:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});let o=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\sign-out\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\sign-out\\page.tsx","default")},68082:(e,t,r)=>{"use strict";r.d(t,{B:()=>i});var o=r(46755),s=r(16457),a=r(12484);function i(){return(0,s.Bd)()}o.Ay.use(s.r9).init({lng:"en",fallbackLng:"en",resources:a.ES,interpolation:{escapeValue:!1}})},80207:(e,t,r)=>{Promise.resolve().then(r.bind(r,34302))}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[7719,9656,3903,8722],()=>r(16200));module.exports=o})();