"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/properties/create/page",{

/***/ "(app-pages-browser)/./components/SimpleImageUpload.tsx":
/*!******************************************!*\
  !*** ./components/SimpleImageUpload.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleImageUpload: () => (/* binding */ SimpleImageUpload)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Check_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useSimpleLanguage */ \"(app-pages-browser)/./hooks/useSimpleLanguage.tsx\");\n/* harmony import */ var _lib_uploadthing__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/uploadthing */ \"(app-pages-browser)/./lib/uploadthing.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ SimpleImageUpload auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction SimpleImageUpload(param) {\n    let { images, onImagesChange, onAutoSave, onUploadStatusChange, propertyId, maxImages = 10, disabled = false } = param;\n    _s();\n    const { language, isArabic } = (0,_hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_3__.useSimpleLanguage)();\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [autoSaveStatus, setAutoSaveStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('idle');\n    // Simple translations\n    const t = {\n        ar: {\n            uploadImages: 'رفع الصور',\n            dragDrop: 'اسحب الصور هنا أو انقر للاختيار',\n            uploading: 'جاري الرفع...',\n            autoSaving: 'حفظ تلقائي...',\n            saved: 'تم الحفظ',\n            remove: 'حذف',\n            mainImage: 'الصورة الرئيسية',\n            images: 'صور',\n            of: 'من'\n        },\n        en: {\n            uploadImages: 'Upload Images',\n            dragDrop: 'Drag images here or click to select',\n            uploading: 'Uploading...',\n            autoSaving: 'Auto-saving...',\n            saved: 'Saved',\n            remove: 'Remove',\n            mainImage: 'Main Image',\n            images: 'images',\n            of: 'of'\n        }\n    };\n    const translations = t[language];\n    // Auto-save when images change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleImageUpload.useEffect\": ()=>{\n            if (onAutoSave && images.length > 0 && autoSaveStatus === 'idle') {\n                const autoSave = {\n                    \"SimpleImageUpload.useEffect.autoSave\": async ()=>{\n                        setAutoSaveStatus('saving');\n                        try {\n                            await onAutoSave(images);\n                            setAutoSaveStatus('saved');\n                            setTimeout({\n                                \"SimpleImageUpload.useEffect.autoSave\": ()=>setAutoSaveStatus('idle')\n                            }[\"SimpleImageUpload.useEffect.autoSave\"], 2000);\n                        } catch (error) {\n                            console.error('Auto-save failed:', error);\n                            setAutoSaveStatus('idle');\n                        }\n                    }\n                }[\"SimpleImageUpload.useEffect.autoSave\"];\n                const timer = setTimeout(autoSave, 500);\n                return ({\n                    \"SimpleImageUpload.useEffect\": ()=>clearTimeout(timer)\n                })[\"SimpleImageUpload.useEffect\"];\n            }\n        }\n    }[\"SimpleImageUpload.useEffect\"], [\n        images,\n        onAutoSave,\n        autoSaveStatus\n    ]);\n    const handleUploadComplete = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SimpleImageUpload.useCallback[handleUploadComplete]\": (res)=>{\n            if (res && res.length > 0) {\n                const newImageUrls = res.map({\n                    \"SimpleImageUpload.useCallback[handleUploadComplete].newImageUrls\": (file)=>file.url\n                }[\"SimpleImageUpload.useCallback[handleUploadComplete].newImageUrls\"]);\n                const updatedImages = [\n                    ...images,\n                    ...newImageUrls\n                ].slice(0, maxImages);\n                onImagesChange(updatedImages);\n                setIsUploading(false);\n                onUploadStatusChange === null || onUploadStatusChange === void 0 ? void 0 : onUploadStatusChange(false);\n                sonner__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"\".concat(res.length, \" \").concat(translations.images, \" \").concat(translations.saved));\n            }\n        }\n    }[\"SimpleImageUpload.useCallback[handleUploadComplete]\"], [\n        images,\n        onImagesChange,\n        maxImages,\n        onUploadStatusChange,\n        translations.images,\n        translations.saved\n    ]);\n    const handleUploadError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SimpleImageUpload.useCallback[handleUploadError]\": (error)=>{\n            console.error('Upload error:', error);\n            setIsUploading(false);\n            onUploadStatusChange === null || onUploadStatusChange === void 0 ? void 0 : onUploadStatusChange(false);\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Upload failed: \".concat(error.message));\n        }\n    }[\"SimpleImageUpload.useCallback[handleUploadError]\"], [\n        onUploadStatusChange\n    ]);\n    const handleUploadBegin = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SimpleImageUpload.useCallback[handleUploadBegin]\": ()=>{\n            setIsUploading(true);\n            onUploadStatusChange === null || onUploadStatusChange === void 0 ? void 0 : onUploadStatusChange(true);\n        }\n    }[\"SimpleImageUpload.useCallback[handleUploadBegin]\"], [\n        onUploadStatusChange\n    ]);\n    const removeImage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SimpleImageUpload.useCallback[removeImage]\": (index)=>{\n            const newImages = images.filter({\n                \"SimpleImageUpload.useCallback[removeImage].newImages\": (_, i)=>i !== index\n            }[\"SimpleImageUpload.useCallback[removeImage].newImages\"]);\n            onImagesChange(newImages);\n        }\n    }[\"SimpleImageUpload.useCallback[removeImage]\"], [\n        images,\n        onImagesChange\n    ]);\n    const setMainImage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SimpleImageUpload.useCallback[setMainImage]\": (index)=>{\n            if (index === 0) return;\n            const newImages = [\n                ...images\n            ];\n            const [mainImage] = newImages.splice(index, 1);\n            newImages.unshift(mainImage);\n            onImagesChange(newImages);\n        }\n    }[\"SimpleImageUpload.useCallback[setMainImage]\"], [\n        images,\n        onImagesChange\n    ]);\n    const canUploadMore = images.length < maxImages;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4 \".concat(isArabic ? 'rtl' : 'ltr'),\n        dir: isArabic ? 'rtl' : 'ltr',\n        children: [\n            canUploadMore && !disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-2 border-dashed border-slate-600 rounded-lg p-6 text-center hover:border-emerald-500 bg-slate-800/50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"mx-auto h-8 w-8 text-slate-400 mb-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\SimpleImageUpload.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-slate-400 mb-4\",\n                                children: translations.dragDrop\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\SimpleImageUpload.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-slate-500\",\n                                children: [\n                                    images.length,\n                                    \" \",\n                                    translations.of,\n                                    \" \",\n                                    maxImages,\n                                    \" \",\n                                    translations.images\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\SimpleImageUpload.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\SimpleImageUpload.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_uploadthing__WEBPACK_IMPORTED_MODULE_4__.UploadDropzone, {\n                        endpoint: \"propertyImageUploader\",\n                        onClientUploadComplete: handleUploadComplete,\n                        onUploadError: handleUploadError,\n                        onUploadBegin: handleUploadBegin,\n                        className: \"absolute inset-0 opacity-0 cursor-pointer\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\SimpleImageUpload.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this),\n                    isUploading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-white/90 dark:bg-slate-900/90 rounded-lg flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-6 w-6 animate-spin mx-auto mb-2 text-emerald-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\SimpleImageUpload.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-slate-600 dark:text-slate-400\",\n                                    children: translations.uploading\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\SimpleImageUpload.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\SimpleImageUpload.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\SimpleImageUpload.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\SimpleImageUpload.tsx\",\n                lineNumber: 127,\n                columnNumber: 9\n            }, this),\n            autoSaveStatus !== 'idle' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2 text-sm \".concat(autoSaveStatus === 'saving' ? 'text-blue-600 dark:text-blue-400' : 'text-green-600 dark:text-green-400', \" \").concat(isArabic ? 'flex-row-reverse' : ''),\n                children: [\n                    autoSaveStatus === 'saving' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"h-4 w-4 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\SimpleImageUpload.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\SimpleImageUpload.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: autoSaveStatus === 'saving' ? translations.autoSaving : translations.saved\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\SimpleImageUpload.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\SimpleImageUpload.tsx\",\n                lineNumber: 162,\n                columnNumber: 9\n            }, this),\n            images.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"aspect-video bg-slate-100 dark:bg-slate-800 rounded-lg overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: images[0],\n                                    alt: \"Main property image\",\n                                    className: \"w-full h-full object-cover\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\SimpleImageUpload.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-2 \".concat(isArabic ? 'right-2' : 'left-2', \" bg-emerald-600 text-white text-xs px-2 py-1 rounded\"),\n                                    children: translations.mainImage\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\SimpleImageUpload.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"button\",\n                                    variant: \"destructive\",\n                                    size: \"sm\",\n                                    className: \"absolute top-2 \".concat(isArabic ? 'left-2' : 'right-2', \" h-6 w-6 p-0 rounded\"),\n                                    onClick: ()=>removeImage(0),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-3 w-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\SimpleImageUpload.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\SimpleImageUpload.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\SimpleImageUpload.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\SimpleImageUpload.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 11\n                    }, this),\n                    images.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-4 gap-2\",\n                        children: images.slice(1).map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative group\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"aspect-square bg-slate-100 dark:bg-slate-800 rounded overflow-hidden cursor-pointer hover:opacity-75 transition-opacity\",\n                                    onClick: ()=>setMainImage(index + 1),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: image,\n                                            alt: \"Property image \".concat(index + 2),\n                                            className: \"w-full h-full object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\SimpleImageUpload.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            type: \"button\",\n                                            variant: \"destructive\",\n                                            size: \"sm\",\n                                            className: \"absolute -top-1 -right-1 h-5 w-5 p-0 rounded-full opacity-0 group-hover:opacity-100 transition-opacity\",\n                                            onClick: (e)=>{\n                                                e.stopPropagation();\n                                                removeImage(index + 1);\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-2 w-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\SimpleImageUpload.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\SimpleImageUpload.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\SimpleImageUpload.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 19\n                                }, this)\n                            }, index + 1, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\SimpleImageUpload.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 17\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\SimpleImageUpload.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\SimpleImageUpload.tsx\",\n                lineNumber: 178,\n                columnNumber: 9\n            }, this),\n            images.length >= maxImages && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center p-3 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-amber-800 dark:text-amber-200\",\n                    children: language === 'ar' ? \"الحد الأقصى \".concat(maxImages, \" صور\") : \"Maximum \".concat(maxImages, \" images\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\SimpleImageUpload.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\SimpleImageUpload.tsx\",\n                lineNumber: 238,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\SimpleImageUpload.tsx\",\n        lineNumber: 124,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleImageUpload, \"cY8aT83sCehDvkfSXFxunG2cBd0=\", false, function() {\n    return [\n        _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_3__.useSimpleLanguage\n    ];\n});\n_c = SimpleImageUpload;\nvar _c;\n$RefreshReg$(_c, \"SimpleImageUpload\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/SimpleImageUpload.tsx\n"));

/***/ })

});