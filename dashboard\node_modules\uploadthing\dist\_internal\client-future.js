import { Micro, Predicate, Array } from 'effect';
import { fetchEff } from '@uploadthing/shared';
import { createUTReporter } from './ut-reporter.js';

var version = "7.7.2";

/**
 * Error indicating the XHR request failed
 * @public
 */ class XHRError extends Micro.TaggedError("XHRError") {
}
/**
 * Error indicating the upload was rejected during upload to the storage provider
 * @public
 */ class UTStorageError extends Micro.TaggedError("UTStorageError") {
}
/**
 * Error indicating the request to your UploadThing server failed
 * @public
 */ class UTServerError extends Micro.TaggedError("UTServerError") {
}
/**
 * Predicate function to check if a file is pending
 * @public
 */ function isPendingFile(file) {
    return file.status === "pending";
}
/**
 * Predicate function to check if a file is uploading
 * @public
 */ function isUploadingFile(file) {
    return file.status === "uploading";
}
/**
 * Predicate function to check if a file is failed
 * @public
 */ function isFailedFile(file) {
    return file.status === "failed";
}
/**
 * Predicate function to check if a file is uploaded
 * @public
 */ function isUploadedFile(file) {
    return file.status === "uploaded";
}
/**
 * @internal
 */ function makePendingFile(file) {
    return Object.assign(file, {
        status: "pending",
        sent: 0,
        key: null,
        customId: null
    });
}
/**
 * Modifies a pending file to an uploading file in place
 * @internal
 */ function transitionToUploading(file, rangeStart) {
    const uploadingFile = file;
    uploadingFile.sent = rangeStart;
    uploadingFile.status = "uploading";
    return uploadingFile;
}
/**
 * Modifies an uploading file to an uploaded file in place
 * @internal
 */ function transitionToUploaded(file, xhrResult) {
    const uploadedFile = file;
    uploadedFile.status = "uploaded";
    uploadedFile.data = xhrResult.serverData;
    uploadedFile.hash = xhrResult.fileHash;
    uploadedFile.url = xhrResult.ufsUrl;
    return uploadedFile;
}
/**
 * Modifies a pending or uploading file to a failed file in place
 * @internal
 */ function transitionToFailed(file, reason) {
    const failedFile = file;
    failedFile.status = "failed";
    failedFile.reason = reason;
    return failedFile;
}
/**
 * Upload a file to the storage provider
 * Throughout the upload, the file's status and progress will be updated
 * @remarks This function never rejects
 * @internal
 */ function uploadFile(url, { file, files, XHRImpl, ...options }) {
    return fetchEff(url, {
        method: "HEAD"
    }).pipe(Micro.map(({ headers })=>Number.parseInt(headers.get("x-ut-range-start") ?? "0")), Micro.map((rangeStart)=>transitionToUploading(file, rangeStart)), Micro.tap((uploadingFile)=>{
        options.onEvent({
            type: "upload-started",
            file: uploadingFile,
            files
        });
    }), Micro.flatMap((uploadingFile)=>Micro.async((resume)=>{
            const xhr = new XHRImpl();
            xhr.open("PUT", url, true);
            const rangeStart = uploadingFile.sent;
            xhr.setRequestHeader("Range", `bytes=${rangeStart}-`);
            xhr.setRequestHeader("x-uploadthing-version", version);
            xhr.responseType = "json";
            xhr.upload.addEventListener("progress", (ev)=>{
                uploadingFile.sent = rangeStart + ev.loaded;
                options.onEvent({
                    type: "upload-progress",
                    file: uploadingFile,
                    files
                });
            });
            xhr.addEventListener("load", ()=>{
                if (xhr.status > 299 || Predicate.hasProperty(xhr.response, "error")) {
                    resume(new UTStorageError({
                        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
                        message: String(xhr.response.error),
                        response: xhr.response
                    }));
                } else {
                    const uploadedFile = transitionToUploaded(uploadingFile, xhr.response);
                    options.onEvent({
                        type: "upload-completed",
                        file: uploadedFile,
                        files
                    });
                    resume(Micro.succeed(uploadedFile));
                }
            });
            xhr.addEventListener("error", ()=>{
                resume(new XHRError({
                    message: `XHR failed ${xhr.status} ${xhr.statusText}`,
                    xhr: xhr
                }));
            });
            const formData = new FormData();
            /**
         * iOS/React Native FormData handling requires special attention:
         *
         * Issue: In React Native, iOS crashes with "attempt to insert nil object" when appending File directly
         * to FormData. This happens because iOS tries to create NSDictionary from the file object and expects
         * specific structure {uri, type, name}.
         *
         *
         * Note: Don't try to use Blob or modify File object - iOS specifically needs plain object
         * with these properties to create valid NSDictionary.
         */ if ("uri" in file) {
                // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
                formData.append("file", {
                    uri: file.uri,
                    type: file.type,
                    name: file.name,
                    ...rangeStart > 0 && {
                        range: rangeStart
                    }
                });
            } else {
                formData.append("file", rangeStart > 0 ? file.slice(rangeStart) : file);
            }
            xhr.send(formData);
            return Micro.sync(()=>xhr.abort());
        })), Micro.catchAll((error)=>{
        const failedFile = transitionToFailed(file, error);
        options.onEvent({
            type: "upload-failed",
            file: failedFile,
            files
        });
        return Micro.succeed(failedFile);
    }));
}
/**
 * Request presigned URLs from your server for a set of files
 * @internal
 */ function requestPresignedUrls(options) {
    const reportEventToUT = createUTReporter({
        endpoint: String(options.endpoint),
        package: options.package,
        url: options.url,
        headers: options.headers
    });
    return reportEventToUT("upload", {
        input: options.input,
        files: options.files.map((f)=>({
                name: f.name,
                size: f.size,
                type: f.type,
                lastModified: f.lastModified
            }))
    }).pipe(Micro.mapError((error)=>new UTServerError({
            message: error.message,
            cause: error,
            data: error.data
        })));
}
/**
 * Upload a set of files to the storage provider
 * @internal
 */ function uploadFiles(endpoint, options) {
    const pendingFiles = options.files.map(makePendingFile);
    return requestPresignedUrls({
        endpoint: endpoint,
        files: options.files,
        url: options.url,
        input: options.input,
        headers: options.headers,
        package: options.package
    }).pipe(Micro.map(Array.zip(pendingFiles)), Micro.tap((pairs)=>{
        for (const [presigned, file] of pairs){
            file.key = presigned.key;
            file.customId = presigned.customId;
        }
        options.onEvent({
            type: "presigned-received",
            files: pendingFiles
        });
    }), Micro.flatMap((pairs)=>Micro.forEach(pairs, ([presigned, file])=>uploadFile(presigned.url, {
                file,
                files: pendingFiles,
                input: options.input,
                onEvent: options.onEvent,
                XHRImpl: globalThis.XMLHttpRequest
            }), {
            concurrency: 6
        })));
}

export { UTServerError, UTStorageError, XHRError, isFailedFile, isPendingFile, isUploadedFile, isUploadingFile, makePendingFile, requestPresignedUrls, uploadFile, uploadFiles };
