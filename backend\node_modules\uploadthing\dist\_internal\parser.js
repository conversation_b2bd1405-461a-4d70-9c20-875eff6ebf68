import * as Cause from 'effect/Cause';
import * as Data from 'effect/Data';
import * as Runtime from 'effect/Runtime';
import * as Schema from 'effect/Schema';

class ParserError extends Data.TaggedError("ParserError") {
    constructor(...args){
        super(...args), this.message = "Input validation failed. The original error with it's validation issues is in the error cause.";
    }
}
function getParseFn(parser) {
    if ("parseAsync" in parser && typeof parser.parseAsync === "function") {
        /**
     * Zod
     * TODO (next major): Consider wrapping ZodError in ParserError
     */ return parser.parseAsync;
    }
    if (Schema.isSchema(parser)) {
        /**
     * Effect Schema
     */ return (value)=>Schema.decodeUnknownPromise(parser)(value).catch((error)=>{
                throw new ParserError({
                    cause: Cause.squash(error[Runtime.FiberFailureCauseId])
                });
            });
    }
    if ("~standard" in parser) {
        /**
     * Standard Schema
     * TODO (next major): Consider moving this to the top of the function
     */ return async (value)=>{
            const result = await parser["~standard"].validate(value);
            if (result.issues) {
                throw new ParserError({
                    cause: result.issues
                });
            }
            return result.value;
        };
    }
    throw new Error("Invalid parser");
}

export { ParserError, getParseFn };
