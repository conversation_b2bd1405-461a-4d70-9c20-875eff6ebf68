import * as Arr from 'effect/Array';
import * as <PERSON> from 'effect/Micro';
import { matchFileType, objectKeys, fileSizeToBytes, createIdentityProxy, resolveMaybeUrlArg, FetchContext, UploadPausedError, UploadAbortedError } from '@uploadthing/shared';
export { UploadAbortedError, UploadPausedError, allowedContentTextLabelGenerator, bytesToFileSize, generateClientDropzoneAccept, generateMimeTypes, generatePermittedFileTypes } from '@uploadthing/shared';
import { createDeferred } from '../dist/_internal/deferred.js';
import { uploadFile, uploadFilesInternal } from '../dist/_internal/upload-browser.js';
import { createUTReporter } from '../dist/_internal/ut-reporter.js';

var version$1 = "7.7.2";

const version = version$1;
/**
 * Validate that a file is of a valid type given a route config
 * @public
 */ const isValidFileType = (file, routeConfig)=>Micro.runSync(matchFileType(file, objectKeys(routeConfig)).pipe(Micro.map((type)=>file.type.includes(type)), Micro.orElseSucceed(()=>false)));
/**
 * Validate that a file is of a valid size given a route config
 * @public
 */ const isValidFileSize = (file, routeConfig)=>Micro.runSync(matchFileType(file, objectKeys(routeConfig)).pipe(Micro.flatMap((type)=>fileSizeToBytes(routeConfig[type].maxFileSize)), Micro.map((maxFileSize)=>file.size <= maxFileSize), Micro.orElseSucceed(()=>false)));
/**
 * Generate a typed uploader for a given FileRouter
 * @public
 */ const genUploader = (initOpts)=>{
    const routeRegistry = createIdentityProxy();
    const controllableUpload = async (slug, opts)=>{
        const uploads = new Map();
        const endpoint = typeof slug === "function" ? slug(routeRegistry) : slug;
        const utReporter = createUTReporter({
            endpoint: String(endpoint),
            package: initOpts?.package ?? "uploadthing/client",
            url: resolveMaybeUrlArg(initOpts?.url),
            headers: opts.headers
        });
        const fetchFn = initOpts?.fetch ?? window.fetch;
        const presigneds = await Micro.runPromise(utReporter("upload", {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
            input: "input" in opts ? opts.input : null,
            files: opts.files.map((f)=>({
                    name: f.name,
                    size: f.size,
                    type: f.type,
                    lastModified: f.lastModified
                }))
        }).pipe(Micro.provideService(FetchContext, fetchFn)));
        const totalSize = opts.files.reduce((acc, f)=>acc + f.size, 0);
        let totalLoaded = 0;
        const uploadEffect = (file, presigned)=>uploadFile(file, presigned, {
                onUploadProgress: (progressEvent)=>{
                    totalLoaded += progressEvent.delta;
                    opts.onUploadProgress?.({
                        ...progressEvent,
                        file,
                        progress: Math.round(progressEvent.loaded / file.size * 100),
                        totalLoaded,
                        totalProgress: Math.round(totalLoaded / totalSize * 100)
                    });
                }
            }).pipe(Micro.provideService(FetchContext, fetchFn));
        for (const [i, p] of presigneds.entries()){
            const file = opts.files[i];
            if (!file) continue;
            const deferred = createDeferred();
            uploads.set(file, {
                deferred,
                presigned: p
            });
            void Micro.runPromiseExit(uploadEffect(file, p), {
                signal: deferred.ac.signal
            }).then((result)=>{
                if (result._tag === "Success") {
                    return deferred.resolve(result.value);
                } else if (result.cause._tag === "Interrupt") {
                    throw new UploadPausedError();
                }
                throw Micro.causeSquash(result.cause);
            }).catch((err)=>{
                if (err instanceof UploadPausedError) return;
                deferred.reject(err);
            });
        }
        /**
     * Pause an ongoing upload
     * @param file The file upload you want to pause. Can be omitted to pause all files
     */ const pauseUpload = (file)=>{
            const files = Arr.ensure(file ?? opts.files);
            for (const file of files){
                const upload = uploads.get(file);
                if (!upload) return;
                if (upload.deferred.ac.signal.aborted) {
                    // Cancel the upload if it's already been paused
                    throw new UploadAbortedError();
                }
                upload.deferred.ac.abort();
            }
        };
        /**
     * Resume a paused upload
     * @param file The file upload you want to resume. Can be omitted to resume all files
     */ const resumeUpload = (file)=>{
            const files = Arr.ensure(file ?? opts.files);
            for (const file of files){
                const upload = uploads.get(file);
                if (!upload) throw "No upload found";
                upload.deferred.ac = new AbortController();
                void Micro.runPromiseExit(uploadEffect(file, upload.presigned), {
                    signal: upload.deferred.ac.signal
                }).then((result)=>{
                    if (result._tag === "Success") {
                        return upload.deferred.resolve(result.value);
                    } else if (result.cause._tag === "Interrupt") {
                        throw new UploadPausedError();
                    }
                    throw Micro.causeSquash(result.cause);
                }).catch((err)=>{
                    if (err instanceof UploadPausedError) return;
                    upload.deferred.reject(err);
                });
            }
        };
        /**
     * Wait for an upload to complete
     * @param file The file upload you want to wait for. Can be omitted to wait for all files
     */ const done = async (file)=>{
            const promises = [];
            const files = Arr.ensure(file ?? opts.files);
            for (const file of files){
                const upload = uploads.get(file);
                if (!upload) throw "No upload found";
                promises.push(upload.deferred.promise);
            }
            const results = await Promise.all(promises);
            return file ? results[0] : results;
        };
        return {
            pauseUpload,
            resumeUpload,
            done
        };
    };
    /**
   * One step upload function that both requests presigned URLs
   * and then uploads the files to UploadThing
   */ const typedUploadFiles = (slug, opts)=>{
        const endpoint = typeof slug === "function" ? slug(routeRegistry) : slug;
        const fetchFn = initOpts?.fetch ?? window.fetch;
        return uploadFilesInternal(endpoint, {
            ...opts,
            skipPolling: {},
            url: resolveMaybeUrlArg(initOpts?.url),
            package: initOpts?.package ?? "uploadthing/client",
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            input: opts.input
        }).pipe(Micro.provideService(FetchContext, fetchFn), (effect)=>Micro.runPromiseExit(effect, opts.signal && {
                signal: opts.signal
            })).then((exit)=>{
            if (exit._tag === "Success") {
                return exit.value;
            } else if (exit.cause._tag === "Interrupt") {
                throw new UploadAbortedError();
            }
            throw Micro.causeSquash(exit.cause);
        });
    };
    return {
        uploadFiles: typedUploadFiles,
        createUpload: controllableUpload,
        /**
     * Identity object that can be used instead of raw strings
     * that allows "Go to definition" in your IDE to bring you
     * to the backend definition of a route.
     */ routeRegistry
    };
};

export { genUploader, isValidFileSize, isValidFileType, version };
