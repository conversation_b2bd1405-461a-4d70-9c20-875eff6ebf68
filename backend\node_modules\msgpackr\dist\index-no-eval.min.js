!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).msgpackr={})}(this,(function(e){"use strict";var t,r,n;try{t=new TextDecoder}catch(e){}var i,s,o,u,a,l=0,f={},c=0,h=0,g=[],p={useRecords:!1,mapsAsObjects:!0};class d{}const y=new d;y.name="MessagePack 0xC1";var w,b=!1,m=2;try{new w("")}catch(e){m=1/0}class S{constructor(e){e&&(!1===e.useRecords&&void 0===e.mapsAsObjects&&(e.mapsAsObjects=!0),e.sequential&&!1!==e.trusted&&(e.trusted=!0,e.structures||0==e.useRecords||(e.structures=[],e.maxSharedStructures||(e.maxSharedStructures=0))),e.structures?e.structures.sharedLength=e.structures.length:e.getStructures&&((e.structures=[]).uninitialized=!0,e.structures.sharedLength=0),e.int64AsNumber&&(e.int64AsType="number")),Object.assign(this,e)}unpack(e,t){if(r)return Z((()=>(G(),this?this.unpack(e,t):S.prototype.unpack.call(p,e,t))));e.buffer||e.constructor!==ArrayBuffer||(e="undefined"!=typeof Buffer?Buffer.from(e):new Uint8Array(e)),"object"==typeof t?(n=t.end||e.length,l=t.start||0):(l=0,n=t>-1?t:e.length),h=0,s=null,o=null,r=e;try{a=e.dataView||(e.dataView=new DataView(e.buffer,e.byteOffset,e.byteLength))}catch(t){if(r=null,e instanceof Uint8Array)throw t;throw new Error("Source must be a Uint8Array or Buffer but was a "+(e&&"object"==typeof e?e.constructor.name:typeof e))}if(this instanceof S){if(f=this,this.structures)return i=this.structures,I(t);(!i||i.length>0)&&(i=[])}else f=p,(!i||i.length>0)&&(i=[]);return I(t)}unpackMultiple(e,t){let r,n=0;try{b=!0;let i=e.length,s=this?this.unpack(e,i):X.unpack(e,i);if(!t){for(r=[s];l<i;)n=l,r.push(I());return r}if(!1===t(s,n,l))return;for(;l<i;)if(n=l,!1===t(I(),n,l))return}catch(e){throw e.lastPosition=n,e.values=r,e}finally{b=!1,G()}}_mergeStructures(e,t){e=e||[],Object.isFrozen(e)&&(e=e.map((e=>e.slice(0))));for(let t=0,r=e.length;t<r;t++){let r=e[t];r&&(r.isShared=!0,t>=32&&(r.highByte=t-32>>5))}e.sharedLength=e.length;for(let r in t||[])if(r>=0){let n=e[r],i=t[r];i&&(n&&((e.restoreStructures||(e.restoreStructures=[]))[r]=n),e[r]=i)}return this.structures=e}decode(e,t){return this.unpack(e,t)}}function I(e){try{if(!f.trusted&&!b){let e=i.sharedLength||0;e<i.length&&(i.length=e)}let e;if(f.randomAccessStructure&&r[l]<64&&r[l],e=U(),o&&(l=o.postBundlePosition,o=null),b&&(i.restoreStructures=null),l==n)i&&i.restoreStructures&&A(),i=null,r=null,u&&(u=null);else{if(l>n)throw new Error("Unexpected end of MessagePack data");if(!b){let t;try{t=JSON.stringify(e,((e,t)=>"bigint"==typeof t?`${t}n`:t)).slice(0,100)}catch(e){t="(JSON view not available "+e+")"}throw new Error("Data read, but end of buffer not reached "+t)}}return e}catch(e){throw i&&i.restoreStructures&&A(),G(),(e instanceof RangeError||e.message.startsWith("Unexpected end of buffer")||l>n)&&(e.incomplete=!0),e}}function A(){for(let e in i.restoreStructures)i[e]=i.restoreStructures[e];i.restoreStructures=null}function U(){let e=r[l++];if(e<160){if(e<128){if(e<64)return e;{let t=i[63&e]||f.getStructures&&B()[63&e];return t?(t.read||(t.read=k(t,63&e)),t.read()):e}}if(e<144){if(e-=128,f.mapsAsObjects){let t={};for(let r=0;r<e;r++){let e=C();"__proto__"===e&&(e="__proto_"),t[e]=U()}return t}{let t=new Map;for(let r=0;r<e;r++)t.set(U(),U());return t}}{e-=144;let t=new Array(e);for(let r=0;r<e;r++)t[r]=U();return f.freezeData?Object.freeze(t):t}}if(e<192){let t=e-160;if(h>=l)return s.slice(l-c,(l+=t)-c);if(0==h&&n<140){let e=t<16?L(t):R(t);if(null!=e)return e}return O(t)}{let t;switch(e){case 192:return null;case 193:return o?(t=U(),t>0?o[1].slice(o.position1,o.position1+=t):o[0].slice(o.position0,o.position0-=t)):y;case 194:return!1;case 195:return!0;case 196:if(t=r[l++],void 0===t)throw new Error("Unexpected end of buffer");return F(t);case 197:return t=a.getUint16(l),l+=2,F(t);case 198:return t=a.getUint32(l),l+=4,F(t);case 199:return P(r[l++]);case 200:return t=a.getUint16(l),l+=2,P(t);case 201:return t=a.getUint32(l),l+=4,P(t);case 202:if(t=a.getFloat32(l),f.useFloat32>2){let e=H[(127&r[l])<<1|r[l+1]>>7];return l+=4,(e*t+(t>0?.5:-.5)>>0)/e}return l+=4,t;case 203:return t=a.getFloat64(l),l+=8,t;case 204:return r[l++];case 205:return t=a.getUint16(l),l+=2,t;case 206:return t=a.getUint32(l),l+=4,t;case 207:return"number"===f.int64AsType?(t=4294967296*a.getUint32(l),t+=a.getUint32(l+4)):"string"===f.int64AsType?t=a.getBigUint64(l).toString():"auto"===f.int64AsType?(t=a.getBigUint64(l),t<=BigInt(2)<<BigInt(52)&&(t=Number(t))):t=a.getBigUint64(l),l+=8,t;case 208:return a.getInt8(l++);case 209:return t=a.getInt16(l),l+=2,t;case 210:return t=a.getInt32(l),l+=4,t;case 211:return"number"===f.int64AsType?(t=4294967296*a.getInt32(l),t+=a.getUint32(l+4)):"string"===f.int64AsType?t=a.getBigInt64(l).toString():"auto"===f.int64AsType?(t=a.getBigInt64(l),t>=BigInt(-2)<<BigInt(52)&&t<=BigInt(2)<<BigInt(52)&&(t=Number(t))):t=a.getBigInt64(l),l+=8,t;case 212:if(t=r[l++],114==t)return J(63&r[l++]);{let e=g[t];if(e)return e.read?(l++,e.read(U())):e.noBuffer?(l++,e()):e(r.subarray(l,++l));throw new Error("Unknown extension "+t)}case 213:return t=r[l],114==t?(l++,J(63&r[l++],r[l++])):P(2);case 214:return P(4);case 215:return P(8);case 216:return P(16);case 217:return t=r[l++],h>=l?s.slice(l-c,(l+=t)-c):_(t);case 218:return t=a.getUint16(l),h>=(l+=2)?s.slice(l-c,(l+=t)-c):x(t);case 219:return t=a.getUint32(l),h>=(l+=4)?s.slice(l-c,(l+=t)-c):T(t);case 220:return t=a.getUint16(l),l+=2,j(t);case 221:return t=a.getUint32(l),l+=4,j(t);case 222:return t=a.getUint16(l),l+=2,D(t);case 223:return t=a.getUint32(l),l+=4,D(t);default:if(e>=224)return e-256;if(void 0===e){let e=new Error("Unexpected end of MessagePack data");throw e.incomplete=!0,e}throw new Error("Unknown MessagePack token "+e)}}}const E=/^[a-zA-Z_$][a-zA-Z\d_$]*$/;function k(e,t){function r(){if(r.count++>m){let r=e.read=new w("r","return function(){return "+(f.freezeData?"Object.freeze":"")+"({"+e.map((e=>"__proto__"===e?"__proto_:r()":E.test(e)?e+":r()":"["+JSON.stringify(e)+"]:r()")).join(",")+"})}")(U);return 0===e.highByte&&(e.read=v(t,e.read)),r()}let n={};for(let t=0,r=e.length;t<r;t++){let r=e[t];"__proto__"===r&&(r="__proto_"),n[r]=U()}return f.freezeData?Object.freeze(n):n}return r.count=0,0===e.highByte?v(t,r):r}const v=(e,t)=>function(){let n=r[l++];if(0===n)return t();let s=e<32?-(e+(n<<5)):e+(n<<5),o=i[s]||B()[s];if(!o)throw new Error("Record id is not defined for "+s);return o.read||(o.read=k(o,e)),o.read()};function B(){let e=Z((()=>(r=null,f.getStructures())));return i=f._mergeStructures(e,i)}var O=M,_=M,x=M,T=M;function M(e){let n;if(e<16&&(n=L(e)))return n;if(e>64&&t)return t.decode(r.subarray(l,l+=e));const i=l+e,s=[];for(n="";l<i;){const e=r[l++];if(0==(128&e))s.push(e);else if(192==(224&e)){const t=63&r[l++];s.push((31&e)<<6|t)}else if(224==(240&e)){const t=63&r[l++],n=63&r[l++];s.push((31&e)<<12|t<<6|n)}else if(240==(248&e)){let t=(7&e)<<18|(63&r[l++])<<12|(63&r[l++])<<6|63&r[l++];t>65535&&(t-=65536,s.push(t>>>10&1023|55296),t=56320|1023&t),s.push(t)}else s.push(e);s.length>=4096&&(n+=V.apply(String,s),s.length=0)}return s.length>0&&(n+=V.apply(String,s)),n}function j(e){let t=new Array(e);for(let r=0;r<e;r++)t[r]=U();return f.freezeData?Object.freeze(t):t}function D(e){if(f.mapsAsObjects){let t={};for(let r=0;r<e;r++){let e=C();"__proto__"===e&&(e="__proto_"),t[e]=U()}return t}{let t=new Map;for(let r=0;r<e;r++)t.set(U(),U());return t}}var V=String.fromCharCode;function R(e){let t=l,n=new Array(e);for(let i=0;i<e;i++){const e=r[l++];if((128&e)>0)return void(l=t);n[i]=e}return V.apply(String,n)}function L(e){if(e<4){if(e<2){if(0===e)return"";{let e=r[l++];return(128&e)>1?void(l-=1):V(e)}}{let t=r[l++],n=r[l++];if((128&t)>0||(128&n)>0)return void(l-=2);if(e<3)return V(t,n);let i=r[l++];return(128&i)>0?void(l-=3):V(t,n,i)}}{let t=r[l++],n=r[l++],i=r[l++],s=r[l++];if((128&t)>0||(128&n)>0||(128&i)>0||(128&s)>0)return void(l-=4);if(e<6){if(4===e)return V(t,n,i,s);{let e=r[l++];return(128&e)>0?void(l-=5):V(t,n,i,s,e)}}if(e<8){let o=r[l++],u=r[l++];if((128&o)>0||(128&u)>0)return void(l-=6);if(e<7)return V(t,n,i,s,o,u);let a=r[l++];return(128&a)>0?void(l-=7):V(t,n,i,s,o,u,a)}{let o=r[l++],u=r[l++],a=r[l++],f=r[l++];if((128&o)>0||(128&u)>0||(128&a)>0||(128&f)>0)return void(l-=8);if(e<10){if(8===e)return V(t,n,i,s,o,u,a,f);{let e=r[l++];return(128&e)>0?void(l-=9):V(t,n,i,s,o,u,a,f,e)}}if(e<12){let c=r[l++],h=r[l++];if((128&c)>0||(128&h)>0)return void(l-=10);if(e<11)return V(t,n,i,s,o,u,a,f,c,h);let g=r[l++];return(128&g)>0?void(l-=11):V(t,n,i,s,o,u,a,f,c,h,g)}{let c=r[l++],h=r[l++],g=r[l++],p=r[l++];if((128&c)>0||(128&h)>0||(128&g)>0||(128&p)>0)return void(l-=12);if(e<14){if(12===e)return V(t,n,i,s,o,u,a,f,c,h,g,p);{let e=r[l++];return(128&e)>0?void(l-=13):V(t,n,i,s,o,u,a,f,c,h,g,p,e)}}{let d=r[l++],y=r[l++];if((128&d)>0||(128&y)>0)return void(l-=14);if(e<15)return V(t,n,i,s,o,u,a,f,c,h,g,p,d,y);let w=r[l++];return(128&w)>0?void(l-=15):V(t,n,i,s,o,u,a,f,c,h,g,p,d,y,w)}}}}}function N(){let e,t=r[l++];if(t<192)e=t-160;else switch(t){case 217:e=r[l++];break;case 218:e=a.getUint16(l),l+=2;break;case 219:e=a.getUint32(l),l+=4;break;default:throw new Error("Expected string")}return M(e)}function F(e){return f.copyBuffers?Uint8Array.prototype.slice.call(r,l,l+=e):r.subarray(l,l+=e)}function P(e){let t=r[l++];if(g[t]){let n;return g[t](r.subarray(l,n=l+=e),(e=>{l=e;try{return U()}finally{l=n}}))}throw new Error("Unknown extension type "+t)}var z=new Array(4096);function C(){let e=r[l++];if(!(e>=160&&e<192))return l--,W(U());if(e-=160,h>=l)return s.slice(l-c,(l+=e)-c);if(!(0==h&&n<180))return O(e);let t,i=4095&(e<<5^(e>1?a.getUint16(l):e>0?r[l]:0)),o=z[i],u=l,f=l+e-3,g=0;if(o&&o.bytes==e){for(;u<f;){if(t=a.getUint32(u),t!=o[g++]){u=1879048192;break}u+=4}for(f+=3;u<f;)if(t=r[u++],t!=o[g++]){u=1879048192;break}if(u===f)return l=u,o.string;f-=3,u=l}for(o=[],z[i]=o,o.bytes=e;u<f;)t=a.getUint32(u),o.push(t),u+=4;for(f+=3;u<f;)t=r[u++],o.push(t);let p=e<16?L(e):R(e);return o.string=null!=p?p:O(e)}function W(e){if("string"==typeof e)return e;if("number"==typeof e||"boolean"==typeof e||"bigint"==typeof e)return e.toString();if(null==e)return e+"";if(f.allowArraysInMapKeys&&Array.isArray(e)&&e.flat().every((e=>["string","number","boolean","bigint"].includes(typeof e))))return e.flat().toString();throw new Error("Invalid property type for record: "+typeof e)}const J=(e,t)=>{let r=U().map(W),n=e;void 0!==t&&(e=e<32?-((t<<5)+e):(t<<5)+e,r.highByte=t);let s=i[e];return s&&(s.isShared||b)&&((i.restoreStructures||(i.restoreStructures=[]))[e]=s),i[e]=r,r.read=k(r,n),r.read()};g[0]=()=>{},g[0].noBuffer=!0,g[66]=e=>{let t=e.length,r=BigInt(128&e[0]?e[0]-256:e[0]);for(let n=1;n<t;n++)r<<=BigInt(8),r+=BigInt(e[n]);return r};let $={Error:Error,TypeError:TypeError,ReferenceError:ReferenceError};g[101]=()=>{let e=U();return($[e[0]]||Error)(e[1],{cause:e[2]})},g[105]=e=>{if(!1===f.structuredClone)throw new Error("Structured clone extension is disabled");let t=a.getUint32(l-4);u||(u=new Map);let n,i=r[l];n=i>=144&&i<160||220==i||221==i?[]:i>=128&&i<144||222==i||223==i?new Map:(i>=199&&i<=201||i>=212&&i<=216)&&115===r[l+1]?new Set:{};let s={target:n};u.set(t,s);let o=U();if(!s.used)return s.target=o;if(Object.assign(n,o),n instanceof Map)for(let[e,t]of o.entries())n.set(e,t);if(n instanceof Set)for(let e of Array.from(o))n.add(e);return n},g[112]=e=>{if(!1===f.structuredClone)throw new Error("Structured clone extension is disabled");let t=a.getUint32(l-4),r=u.get(t);return r.used=!0,r.target},g[115]=()=>new Set(U());const K=["Int8","Uint8","Uint8Clamped","Int16","Uint16","Int32","Uint32","Float32","Float64","BigInt64","BigUint64"].map((e=>e+"Array"));let Y="object"==typeof globalThis?globalThis:window;g[116]=e=>{let t=e[0],r=Uint8Array.prototype.slice.call(e,1).buffer,n=K[t];if(!n){if(16===t)return r;if(17===t)return new DataView(r);throw new Error("Could not find typed array for code "+t)}return new Y[n](r)},g[120]=()=>{let e=U();return new RegExp(e[0],e[1])};const q=[];function Z(e){let t=n,g=l,p=c,d=h,y=s,w=u,m=o,S=new Uint8Array(r.slice(0,n)),I=i,A=i.slice(0,i.length),U=f,E=b,k=e();return n=t,l=g,c=p,h=d,s=y,u=w,o=m,r=S,b=E,(i=I).splice(0,i.length,...A),f=U,a=new DataView(r.buffer,r.byteOffset,r.byteLength),k}function G(){r=null,u=null,i=null}g[98]=e=>{let t=(e[0]<<24)+(e[1]<<16)+(e[2]<<8)+e[3],r=l;return l+=t-e.length,o=q,(o=[N(),N()]).position0=0,o.position1=0,o.postBundlePosition=l,l=r,U()},g[255]=e=>4==e.length?new Date(1e3*(16777216*e[0]+(e[1]<<16)+(e[2]<<8)+e[3])):8==e.length?new Date(((e[0]<<22)+(e[1]<<14)+(e[2]<<6)+(e[3]>>2))/1e6+1e3*(4294967296*(3&e[3])+16777216*e[4]+(e[5]<<16)+(e[6]<<8)+e[7])):12==e.length?new Date(((e[0]<<24)+(e[1]<<16)+(e[2]<<8)+e[3])/1e6+1e3*((128&e[4]?-281474976710656:0)+1099511627776*e[6]+4294967296*e[7]+16777216*e[8]+(e[9]<<16)+(e[10]<<8)+e[11])):new Date("invalid");const H=new Array(147);for(let e=0;e<256;e++)H[e]=+("1e"+Math.floor(45.15-.30103*e));const Q=S;var X=new S({useRecords:!1});const ee=X.unpack,te=X.unpackMultiple,re=X.unpack,ne={NEVER:0,ALWAYS:1,DECIMAL_ROUND:3,DECIMAL_FIT:4};let ie,se,oe,ue=new Float32Array(1),ae=new Uint8Array(ue.buffer,0,4);try{ie=new TextEncoder}catch(e){}const le="undefined"!=typeof Buffer,fe=le?function(e){return Buffer.allocUnsafeSlow(e)}:Uint8Array,ce=le?Buffer:Uint8Array,he=le?4294967296:2144337920;let ge,pe,de,ye,we=0,be=null;const me=/[\u0080-\uFFFF]/,Se=Symbol("record-id");class Ie extends S{constructor(e){let t,r,n,i;super(e),this.offset=0;let s=ce.prototype.utf8Write?function(e,t){return ge.utf8Write(e,t,ge.byteLength-t)}:!(!ie||!ie.encodeInto)&&function(e,t){return ie.encodeInto(e,ge.subarray(t)).written},o=this;e||(e={});let u=e&&e.sequential,a=e.structures||e.saveStructures,l=e.maxSharedStructures;if(null==l&&(l=a?32:0),l>8160)throw new Error("Maximum maxSharedStructure is 8160");e.structuredClone&&null==e.moreTypes&&(this.moreTypes=!0);let f=e.maxOwnStructures;null==f&&(f=a?32:64),this.structures||0==e.useRecords||(this.structures=[]);let c=l>32||f+l>64,h=l+64,g=l+f+64;if(g>8256)throw new Error("Maximum maxSharedStructure + maxOwnStructure is 8192");let p=[],d=0,y=0;this.pack=this.encode=function(e,s){if(ge||(ge=new fe(8192),de=ge.dataView||(ge.dataView=new DataView(ge.buffer,0,8192)),we=0),ye=ge.length-10,ye-we<2048?(ge=new fe(ge.length),de=ge.dataView||(ge.dataView=new DataView(ge.buffer,0,ge.length)),ye=ge.length-10,we=0):we=we+7&2147483640,t=we,s&Re&&(we+=255&s),i=o.structuredClone?new Map:null,o.bundleStrings&&"string"!=typeof e?(be=[],be.size=1/0):be=null,n=o.structures,n){n.uninitialized&&(n=o._mergeStructures(o.getStructures()));let e=n.sharedLength||0;if(e>l)throw new Error("Shared structures is larger than maximum shared structures, try increasing maxSharedStructures to "+n.sharedLength);if(!n.transitions){n.transitions=Object.create(null);for(let t=0;t<e;t++){let e=n[t];if(!e)continue;let r,i=n.transitions;for(let t=0,n=e.length;t<n;t++){let n=e[t];r=i[n],r||(r=i[n]=Object.create(null)),i=r}i[Se]=t+64}this.lastNamedStructuresLength=e}u||(n.nextId=e+64)}let a;r&&(r=!1);try{o.randomAccessStructure&&e&&e.constructor&&e.constructor===Object?B(e):m(e);let u=be;if(be&&ke(t,m,0),i&&i.idsToInsert){let e=i.idsToInsert.sort(((e,t)=>e.offset>t.offset?1:-1)),r=e.length,n=-1;for(;u&&r>0;){let i=e[--r].offset+t;i<u.stringsPosition+t&&-1===n&&(n=0),i>u.position+t?n>=0&&(n+=6):(n>=0&&(de.setUint32(u.position+t,de.getUint32(u.position+t)+n),n=-1),u=u.previous,r++)}n>=0&&u&&de.setUint32(u.position+t,de.getUint32(u.position+t)+n),we+=6*e.length,we>ye&&E(we),o.offset=we;let s=function(e,t){let r,n=6*t.length,i=e.length-n;for(;r=t.pop();){let t=r.offset,s=r.id;e.copyWithin(t+n,t,i),n-=6;let o=t+n;e[o++]=214,e[o++]=105,e[o++]=s>>24,e[o++]=s>>16&255,e[o++]=s>>8&255,e[o++]=255&s,i=t}return e}(ge.subarray(t,we),e);return i=null,s}return o.offset=we,s&De?(ge.start=t,ge.end=we,ge):ge.subarray(t,we)}catch(e){throw a=e,e}finally{if(n&&(w(),r&&o.saveStructures)){let r=n.sharedLength||0,i=ge.subarray(t,we),u=function(e,t){return e.isCompatible=e=>{let r=!e||(t.lastNamedStructuresLength||0)===e.length;return r||t._mergeStructures(e),r},e}(n,o);if(!a)return!1===o.saveStructures(u,u.isCompatible)?o.pack(e,s):(o.lastNamedStructuresLength=r,ge.length>1073741824&&(ge=null),i)}ge.length>1073741824&&(ge=null),s&Ve&&(we=t)}};const w=()=>{y<10&&y++;let e=n.sharedLength||0;if(n.length>e&&!u&&(n.length=e),d>1e4)n.transitions=null,y=0,d=0,p.length>0&&(p=[]);else if(p.length>0&&!u){for(let e=0,t=p.length;e<t;e++)p[e][Se]=0;p=[]}},b=e=>{var t=e.length;t<16?ge[we++]=144|t:t<65536?(ge[we++]=220,ge[we++]=t>>8,ge[we++]=255&t):(ge[we++]=221,de.setUint32(we,t),we+=4);for(let r=0;r<t;r++)m(e[r])},m=e=>{we>ye&&(ge=E(we));var r,n=typeof e;if("string"===n){let n,i=e.length;if(be&&i>=4&&i<4096){if((be.size+=i)>21760){let e,r,n=(be[0]?3*be[0].length+be[1].length:0)+10;we+n>ye&&(ge=E(we+n)),be.position?(r=be,ge[we]=200,we+=3,ge[we++]=98,e=we-t,we+=4,ke(t,m,0),de.setUint16(e+t-3,we-t-e)):(ge[we++]=214,ge[we++]=98,e=we-t,we+=4),be=["",""],be.previous=r,be.size=0,be.position=e}let r=me.test(e);return be[r?0:1]+=e,ge[we++]=193,void m(r?-i:i)}n=i<32?1:i<256?2:i<65536?3:5;let o=3*i;if(we+o>ye&&(ge=E(we+o)),i<64||!s){let t,s,o,u=we+n;for(t=0;t<i;t++)s=e.charCodeAt(t),s<128?ge[u++]=s:s<2048?(ge[u++]=s>>6|192,ge[u++]=63&s|128):55296==(64512&s)&&56320==(64512&(o=e.charCodeAt(t+1)))?(s=65536+((1023&s)<<10)+(1023&o),t++,ge[u++]=s>>18|240,ge[u++]=s>>12&63|128,ge[u++]=s>>6&63|128,ge[u++]=63&s|128):(ge[u++]=s>>12|224,ge[u++]=s>>6&63|128,ge[u++]=63&s|128);r=u-we-n}else r=s(e,we+n);r<32?ge[we++]=160|r:r<256?(n<2&&ge.copyWithin(we+2,we+1,we+1+r),ge[we++]=217,ge[we++]=r):r<65536?(n<3&&ge.copyWithin(we+3,we+2,we+2+r),ge[we++]=218,ge[we++]=r>>8,ge[we++]=255&r):(n<5&&ge.copyWithin(we+5,we+3,we+3+r),ge[we++]=219,de.setUint32(we,r),we+=4),we+=r}else if("number"===n)if(e>>>0===e)e<32||e<128&&!1===this.useRecords||e<64&&!this.randomAccessStructure?ge[we++]=e:e<256?(ge[we++]=204,ge[we++]=e):e<65536?(ge[we++]=205,ge[we++]=e>>8,ge[we++]=255&e):(ge[we++]=206,de.setUint32(we,e),we+=4);else if(e>>0===e)e>=-32?ge[we++]=256+e:e>=-128?(ge[we++]=208,ge[we++]=e+256):e>=-32768?(ge[we++]=209,de.setInt16(we,e),we+=2):(ge[we++]=210,de.setInt32(we,e),we+=4);else{let t;if((t=this.useFloat32)>0&&e<4294967296&&e>=-2147483648){let r;if(ge[we++]=202,de.setFloat32(we,e),t<4||(r=e*H[(127&ge[we])<<1|ge[we+1]>>7])>>0===r)return void(we+=4);we--}ge[we++]=203,de.setFloat64(we,e),we+=8}else if("object"===n||"function"===n)if(e){if(i){let r=i.get(e);if(r){if(!r.id){let e=i.idsToInsert||(i.idsToInsert=[]);r.id=e.push(r)}return ge[we++]=214,ge[we++]=112,de.setUint32(we,r.id),void(we+=4)}i.set(e,{offset:we-t})}let s=e.constructor;if(s===Object)U(e);else if(s===Array)b(e);else if(s===Map)if(this.mapAsEmptyObject)ge[we++]=128;else{(r=e.size)<16?ge[we++]=128|r:r<65536?(ge[we++]=222,ge[we++]=r>>8,ge[we++]=255&r):(ge[we++]=223,de.setUint32(we,r),we+=4);for(let[t,r]of e)m(t),m(r)}else{for(let t=0,r=se.length;t<r;t++){if(e instanceof oe[t]){let r=se[t];if(r.write){r.type&&(ge[we++]=212,ge[we++]=r.type,ge[we++]=0);let t=r.write.call(this,e);return void(t===e?Array.isArray(e)?b(e):U(e):m(t))}let n,i=ge,s=de,o=we;ge=null;try{n=r.pack.call(this,e,(e=>(ge=i,i=null,we+=e,we>ye&&E(we),{target:ge,targetView:de,position:we-e})),m)}finally{i&&(ge=i,de=s,we=o,ye=ge.length-10)}return void(n&&(n.length+we>ye&&E(n.length+we),we=Ee(n,ge,we,r.type)))}}if(Array.isArray(e))b(e);else{if(e.toJSON){const t=e.toJSON();if(t!==e)return m(t)}if("function"===n)return m(this.writeFunction&&this.writeFunction(e));U(e)}}}else ge[we++]=192;else if("boolean"===n)ge[we++]=e?195:194;else if("bigint"===n){if(e<0x8000000000000000&&e>=-0x8000000000000000)ge[we++]=211,de.setBigInt64(we,e);else if(e<0x10000000000000000&&e>0)ge[we++]=207,de.setBigUint64(we,e);else{if(!this.largeBigIntToFloat){if(this.largeBigIntToString)return m(e.toString());if((this.useBigIntExtension||this.moreTypes)&&e<BigInt(2)**BigInt(1023)&&e>-(BigInt(2)**BigInt(1023))){ge[we++]=199,we++,ge[we++]=66;let t,r=[];do{let n=e&BigInt(255);t=(n&BigInt(128))===(e<BigInt(0)?BigInt(128):BigInt(0)),r.push(n),e>>=BigInt(8)}while(e!==BigInt(0)&&e!==BigInt(-1)||!t);ge[we-2]=r.length;for(let e=r.length;e>0;)ge[we++]=Number(r[--e]);return}throw new RangeError(e+" was too large to fit in MessagePack 64-bit integer format, use useBigIntExtension, or set largeBigIntToFloat to convert to float-64, or set largeBigIntToString to convert to string")}ge[we++]=203,de.setFloat64(we,Number(e))}we+=8}else{if("undefined"!==n)throw new Error("Unknown type: "+n);this.encodeUndefinedAsNil?ge[we++]=192:(ge[we++]=212,ge[we++]=0,ge[we++]=0)}},S=this.variableMapSize||this.coercibleKeyAsNumber||this.skipValues?e=>{let t;if(this.skipValues){t=[];for(let r in e)"function"==typeof e.hasOwnProperty&&!e.hasOwnProperty(r)||this.skipValues.includes(e[r])||t.push(r)}else t=Object.keys(e);let r,n=t.length;if(n<16?ge[we++]=128|n:n<65536?(ge[we++]=222,ge[we++]=n>>8,ge[we++]=255&n):(ge[we++]=223,de.setUint32(we,n),we+=4),this.coercibleKeyAsNumber)for(let i=0;i<n;i++){r=t[i];let n=Number(r);m(isNaN(n)?r:n),m(e[r])}else for(let i=0;i<n;i++)m(r=t[i]),m(e[r])}:e=>{ge[we++]=222;let r=we-t;we+=2;let n=0;for(let t in e)("function"!=typeof e.hasOwnProperty||e.hasOwnProperty(t))&&(m(t),m(e[t]),n++);if(n>65535)throw new Error('Object is too large to serialize with fast 16-bit map size, use the "variableMapSize" option to serialize this object');ge[r+++t]=n>>8,ge[r+t]=255&n},I=!1===this.useRecords?S:e.progressiveRecords&&!c?e=>{let r,i,s=n.transitions||(n.transitions=Object.create(null)),o=we++-t;for(let u in e)if("function"!=typeof e.hasOwnProperty||e.hasOwnProperty(u)){if(r=s[u],r)s=r;else{let a=Object.keys(e),l=s;s=n.transitions;let f=0;for(let e=0,t=a.length;e<t;e++){let t=a[e];r=s[t],r||(r=s[t]=Object.create(null),f++),s=r}o+t+1==we?(we--,k(s,a,f)):v(s,a,o,f),i=!0,s=l[u]}m(e[u])}if(!i){let r=s[Se];r?ge[o+t]=r:v(s,Object.keys(e),o,0)}}:e=>{let t,r=n.transitions||(n.transitions=Object.create(null)),i=0;for(let n in e)("function"!=typeof e.hasOwnProperty||e.hasOwnProperty(n))&&(t=r[n],t||(t=r[n]=Object.create(null),i++),r=t);let s=r[Se];s?s>=96&&c?(ge[we++]=96+(31&(s-=96)),ge[we++]=s>>5):ge[we++]=s:k(r,r.__keys__||Object.keys(e),i);for(let t in e)("function"!=typeof e.hasOwnProperty||e.hasOwnProperty(t))&&m(e[t])},A="function"==typeof this.useRecords&&this.useRecords,U=A?e=>{A(e)?I(e):S(e)}:I,E=e=>{let r;if(e>16777216){if(e-t>he)throw new Error("Packed buffer would be larger than maximum buffer size");r=Math.min(he,4096*Math.round(Math.max((e-t)*(e>67108864?1.25:2),4194304)/4096))}else r=1+(Math.max(e-t<<2,ge.length-1)>>12)<<12;let n=new fe(r);return de=n.dataView||(n.dataView=new DataView(n.buffer,0,r)),e=Math.min(e,ge.length),ge.copy?ge.copy(n,0,t,e):n.set(ge.slice(t,e)),we-=t,t=0,ye=n.length-10,ge=n},k=(e,t,i)=>{let s=n.nextId;s||(s=64),s<h&&this.shouldShareStructure&&!this.shouldShareStructure(t)?(s=n.nextOwnId,s<g||(s=h),n.nextOwnId=s+1):(s>=g&&(s=h),n.nextId=s+1);let o=t.highByte=s>=96&&c?s-96>>5:-1;e[Se]=s,e.__keys__=t,n[s-64]=t,s<h?(t.isShared=!0,n.sharedLength=s-63,r=!0,o>=0?(ge[we++]=96+(31&s),ge[we++]=o):ge[we++]=s):(o>=0?(ge[we++]=213,ge[we++]=114,ge[we++]=96+(31&s),ge[we++]=o):(ge[we++]=212,ge[we++]=114,ge[we++]=s),i&&(d+=y*i),p.length>=f&&(p.shift()[Se]=0),p.push(e),m(t))},v=(e,r,n,i)=>{let s=ge,o=we,u=ye,a=t;ge=pe,we=0,t=0,ge||(pe=ge=new fe(8192)),ye=ge.length-10,k(e,r,i),pe=ge;let l=we;if(ge=s,we=o,ye=u,t=a,l>1){let e=we+l-1;e>ye&&E(e);let r=n+t;ge.copyWithin(r+l,r+1,we),ge.set(pe.slice(0,l),r),we=e}else ge[n+t]=pe[0]},B=e=>{let i=undefined(e,ge,t,we,n,E,((e,t,n)=>{if(n)return r=!0;we=t;let i=ge;return m(e),w(),i!==ge?{position:we,targetView:de,target:ge}:we}),this);if(0===i)return U(e);we=i}}useBuffer(e){ge=e,ge.dataView||(ge.dataView=new DataView(ge.buffer,ge.byteOffset,ge.byteLength)),we=0}set position(e){we=e}get position(){return we}clearSharedData(){this.structures&&(this.structures=[]),this.typedStructs&&(this.typedStructs=[])}}function Ae(e,t,r,n){let i=e.byteLength;if(i+1<256){var{target:s,position:o}=r(4+i);s[o++]=199,s[o++]=i+1}else if(i+1<65536){var{target:s,position:o}=r(5+i);s[o++]=200,s[o++]=i+1>>8,s[o++]=i+1&255}else{var{target:s,position:o,targetView:u}=r(7+i);s[o++]=201,u.setUint32(o,i+1),o+=4}s[o++]=116,s[o++]=t,e.buffer||(e=new Uint8Array(e)),s.set(new Uint8Array(e.buffer,e.byteOffset,e.byteLength),o)}function Ue(e,t){let r=e.byteLength;var n,i;if(r<256){var{target:n,position:i}=t(r+2);n[i++]=196,n[i++]=r}else if(r<65536){var{target:n,position:i}=t(r+3);n[i++]=197,n[i++]=r>>8,n[i++]=255&r}else{var{target:n,position:i,targetView:s}=t(r+5);n[i++]=198,s.setUint32(i,r),i+=4}n.set(e,i)}function Ee(e,t,r,n){let i=e.length;switch(i){case 1:t[r++]=212;break;case 2:t[r++]=213;break;case 4:t[r++]=214;break;case 8:t[r++]=215;break;case 16:t[r++]=216;break;default:i<256?(t[r++]=199,t[r++]=i):i<65536?(t[r++]=200,t[r++]=i>>8,t[r++]=255&i):(t[r++]=201,t[r++]=i>>24,t[r++]=i>>16&255,t[r++]=i>>8&255,t[r++]=255&i)}return t[r++]=n,t.set(e,r),r+=i}function ke(e,t,r){if(be.length>0){de.setUint32(be.position+e,we+r-be.position-e),be.stringsPosition=we-e;let n=be;be=null,t(n[0]),t(n[1])}}oe=[Date,Set,Error,RegExp,ArrayBuffer,Object.getPrototypeOf(Uint8Array.prototype).constructor,DataView,d],se=[{pack(e,t,r){let n=e.getTime()/1e3;if((this.useTimestamp32||0===e.getMilliseconds())&&n>=0&&n<4294967296){let{target:e,targetView:r,position:i}=t(6);e[i++]=214,e[i++]=255,r.setUint32(i,n)}else if(n>0&&n<4294967296){let{target:r,targetView:i,position:s}=t(10);r[s++]=215,r[s++]=255,i.setUint32(s,4e6*e.getMilliseconds()+(n/1e3/4294967296>>0)),i.setUint32(s+4,n)}else if(isNaN(n)){if(this.onInvalidDate)return t(0),r(this.onInvalidDate());let{target:e,targetView:n,position:i}=t(3);e[i++]=212,e[i++]=255,e[i++]=255}else{let{target:r,targetView:i,position:s}=t(15);r[s++]=199,r[s++]=12,r[s++]=255,i.setUint32(s,1e6*e.getMilliseconds()),i.setBigInt64(s+4,BigInt(Math.floor(n)))}}},{pack(e,t,r){if(this.setAsEmptyObject)return t(0),r({});let n=Array.from(e),{target:i,position:s}=t(this.moreTypes?3:0);this.moreTypes&&(i[s++]=212,i[s++]=115,i[s++]=0),r(n)}},{pack(e,t,r){let{target:n,position:i}=t(this.moreTypes?3:0);this.moreTypes&&(n[i++]=212,n[i++]=101,n[i++]=0),r([e.name,e.message,e.cause])}},{pack(e,t,r){let{target:n,position:i}=t(this.moreTypes?3:0);this.moreTypes&&(n[i++]=212,n[i++]=120,n[i++]=0),r([e.source,e.flags])}},{pack(e,t){this.moreTypes?Ae(e,16,t):Ue(le?Buffer.from(e):new Uint8Array(e),t)}},{pack(e,t){let r=e.constructor;r!==ce&&this.moreTypes?Ae(e,K.indexOf(r.name),t):Ue(e,t)}},{pack(e,t){this.moreTypes?Ae(e,17,t):Ue(le?Buffer.from(e):new Uint8Array(e),t)}},{pack(e,t){let{target:r,position:n}=t(1);r[n]=193}}];let ve=new Ie({useRecords:!1});const Be=ve.pack,Oe=ve.pack,_e=Ie,{NEVER:xe,ALWAYS:Te,DECIMAL_ROUND:Me,DECIMAL_FIT:je}=ne,De=512,Ve=1024,Re=2048;const Le=function(e,t={}){if(!e||"object"!=typeof e)throw new Error("first argument must be an Iterable, Async Iterable, Iterator, Async Iterator, or a promise");const r=new S(t);let n;const i=e=>{let t;n&&(e=Buffer.concat([n,e]),n=void 0);try{t=r.unpackMultiple(e)}catch(r){if(!r.incomplete)throw r;n=e.slice(r.lastPosition),t=r.values}return t};return"function"==typeof e[Symbol.iterator]?function*(){for(const t of e)yield*i(t)}():"function"==typeof e[Symbol.asyncIterator]?async function*(){for await(const t of e)yield*i(t)}():void 0},Ne=function(e,t={}){if(e&&"object"==typeof e){if("function"==typeof e[Symbol.iterator])return function*(e,t){const r=new Ie(t);for(const t of e)yield r.pack(t)}(e,t);if("function"==typeof e.then||"function"==typeof e[Symbol.asyncIterator])return async function*(e,t){const r=new Ie(t);for await(const t of e)yield r.pack(t)}(e,t);throw new Error("first argument must be an Iterable, Async Iterable, Iterator, Async Iterator, or a Promise")}throw new Error("first argument must be an Iterable, Async Iterable, or a Promise for an Async Iterable")};e.ALWAYS=Te,e.C1=y,e.DECIMAL_FIT=je,e.DECIMAL_ROUND=Me,e.Decoder=Q,e.Encoder=_e,e.FLOAT32_OPTIONS=ne,e.NEVER=xe,e.Packr=Ie,e.RESERVE_START_SPACE=Re,e.RESET_BUFFER_MODE=Ve,e.REUSE_BUFFER_MODE=De,e.Unpackr=S,e.addExtension=function(e){if(e.Class){if(!e.pack&&!e.write)throw new Error("Extension has no pack or write function");if(e.pack&&!e.type)throw new Error("Extension has no type (numeric code to identify the extension)");oe.unshift(e.Class),se.unshift(e)}!function(e){e.unpack?g[e.type]=e.unpack:g[e.type]=e}(e)},e.clearSource=G,e.decode=re,e.decodeIter=Le,e.encode=Oe,e.encodeIter=Ne,e.isNativeAccelerationEnabled=!1,e.mapsAsObjects=!0,e.pack=Be,e.roundFloat32=function(e){ue[0]=e;let t=H[(127&ae[3])<<1|ae[2]>>7];return(t*e+(e>0?.5:-.5)>>0)/t},e.unpack=ee,e.unpackMultiple=te,e.useRecords=!1}));
//# sourceMappingURL=index-no-eval.min.js.map
