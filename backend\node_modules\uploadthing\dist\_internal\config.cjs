var Config = require('effect/Config');
var ConfigProvider = require('effect/ConfigProvider');
var Effect = require('effect/Effect');
var S = require('effect/Schema');
var shared = require('@uploadthing/shared');
var sharedSchemas_cjs = require('./shared-schemas.cjs');

function _interopNamespace(e) {
  if (e && e.__esModule) return e;
  var n = Object.create(null);
  if (e) {
    Object.keys(e).forEach(function (k) {
      if (k !== 'default') {
        var d = Object.getOwnPropertyDescriptor(e, k);
        Object.defineProperty(n, k, d.get ? d : {
          enumerable: true,
          get: function () { return e[k]; }
        });
      }
    });
  }
  n.default = e;
  return n;
}

var Config__namespace = /*#__PURE__*/_interopNamespace(Config);
var ConfigProvider__namespace = /*#__PURE__*/_interopNamespace(ConfigProvider);
var Effect__namespace = /*#__PURE__*/_interopNamespace(Effect);
var S__namespace = /*#__PURE__*/_interopNamespace(S);

var version = "7.7.2";

/**
 * Merge in `import.meta.env` to the built-in `process.env` provider
 * Prefix keys with `UPLOADTHING_` so we can reference just the name.
 * @example
 * process.env.UPLOADTHING_TOKEN = "foo"
 * Config.string("token"); // Config<"foo">
 */ const envProvider = ConfigProvider__namespace.fromEnv().pipe(ConfigProvider__namespace.orElse(()=>ConfigProvider__namespace.fromMap(new Map(Object.entries(shared.filterDefinedObjectValues(// fuck this I give up. import.meta is a mistake, someone else can fix it
    undefined ?? {}))), {
        pathDelim: "_"
    })), ConfigProvider__namespace.nested("uploadthing"), ConfigProvider__namespace.constantCase);
/**
 * Config provider that merges the options from the object
 * and environment variables prefixed with `UPLOADTHING_`.
 * @remarks Options take precedence over environment variables.
 */ const configProvider = (options)=>ConfigProvider__namespace.fromJson(options ?? {}).pipe(ConfigProvider__namespace.orElse(()=>envProvider));
const IsDevelopment = Config__namespace.boolean("isDev").pipe(Config__namespace.orElse(()=>Config__namespace.succeed(typeof process !== "undefined" ? process.env.NODE_ENV : undefined).pipe(Config__namespace.map((_)=>_ === "development"))), Config__namespace.withDefault(false));
const UTToken = S__namespace.Config("token", sharedSchemas_cjs.UploadThingToken).pipe(Effect__namespace.catchTags({
    ConfigError: (e)=>new shared.UploadThingError({
            code: e._op === "InvalidData" ? "INVALID_SERVER_CONFIG" : "MISSING_ENV",
            message: e._op === "InvalidData" ? "Invalid token. A token is a base64 encoded JSON object matching { apiKey: string, appId: string, regions: string[] }." : "Missing token. Please set the `UPLOADTHING_TOKEN` environment variable or provide a token manually through config.",
            cause: e
        })
}));
const ApiUrl = Config__namespace.string("apiUrl").pipe(Config__namespace.withDefault("https://api.uploadthing.com"), Config__namespace.mapAttempt((_)=>new URL(_)), Config__namespace.map((url)=>url.href.replace(/\/$/, "")));
const IngestUrl = Effect__namespace.fn(function*(preferredRegion) {
    const { regions, ingestHost } = yield* UTToken;
    const region = preferredRegion ? regions.find((r)=>r === preferredRegion) ?? regions[0] : regions[0];
    return yield* Config__namespace.string("ingestUrl").pipe(Config__namespace.withDefault(`https://${region}.${ingestHost}`), Config__namespace.mapAttempt((_)=>new URL(_)), Config__namespace.map((url)=>url.href.replace(/\/$/, "")));
});
const UtfsHost = Config__namespace.string("utfsHost").pipe(Config__namespace.withDefault("utfs.io"));
const UfsHost = Config__namespace.string("ufsHost").pipe(Config__namespace.withDefault("ufs.sh"));

exports.ApiUrl = ApiUrl;
exports.IngestUrl = IngestUrl;
exports.IsDevelopment = IsDevelopment;
exports.UPLOADTHING_VERSION = version;
exports.UTToken = UTToken;
exports.UfsHost = UfsHost;
exports.UtfsHost = UtfsHost;
exports.configProvider = configProvider;
