{"version": 3, "file": "node.js", "names": ["MP", "Duplex", "Readable", "decodeField", "MultipastaStream", "_parser", "_canWrite", "_writeCallback", "constructor", "config", "readableObjectMode", "make", "onField", "info", "value", "field", "_tag", "push", "emit", "onFile", "file", "FileStream", "chunk", "_resume", "onError", "error", "onDone", "undefined", "callback", "_read", "_size", "_write", "encoding", "write", "Uint8Array", "<PERSON><PERSON><PERSON>", "from", "_final", "end", "_parent", "filename"], "sources": ["../../src/node.ts"], "sourcesContent": [null], "mappings": "AAEA,OAAO,KAAKA,EAAE,MAAM,YAAY;AAChC,SAASC,MAAM,EAAEC,QAAQ,QAAQ,aAAa;AAG9C,SAASC,WAAW,QAAQ,YAAY;AAmCxC,OAAM,MAAOC,gBAAiB,SAAQH,MAAM;EAClCI,OAAO;EACfC,SAAS,GAAG,IAAI;EACRC,cAAc;EAEtBC,YAAYC,MAAkB;IAC5B,KAAK,CAAC;MAAEC,kBAAkB,EAAE;IAAI,CAAE,CAAC;IACnC,IAAI,CAACL,OAAO,GAAGL,EAAE,CAACW,IAAI,CAAC;MACrB,GAAIF,MAAc;MAClBG,OAAO,EAAEA,CAACC,IAAI,EAAEC,KAAK,KAAI;QACvB,MAAMC,KAAK,GAAU;UAAEC,IAAI,EAAE,OAAO;UAAEH,IAAI;UAAEC;QAAK,CAAE;QACnD,IAAI,CAACG,IAAI,CAACF,KAAK,CAAC;QAChB,IAAI,CAACG,IAAI,CAAC,OAAO,EAAEH,KAAK,CAAC;MAC3B,CAAC;MACDI,MAAM,EAAEN,IAAI,IAAG;QACb,MAAMO,IAAI,GAAG,IAAIC,UAAU,CAACR,IAAI,EAAE,IAAI,CAAC;QACvC,IAAI,CAACI,IAAI,CAACG,IAAI,CAAC;QACf,IAAI,CAACF,IAAI,CAAC,MAAM,EAAEE,IAAI,CAAC;QACvB,OAAOE,KAAK,IAAG;UACb,IAAI,CAAChB,SAAS,GAAGc,IAAI,CAACH,IAAI,CAACK,KAAK,CAAC;UACjC,IAAIA,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI,CAAChB,SAAS,EAAE;YACrC,IAAI,CAACiB,OAAO,EAAE;;QAElB,CAAC;MACH,CAAC;MACDC,OAAO,EAAEC,KAAK,IAAG;QACf,IAAI,CAACP,IAAI,CAAC,OAAO,EAAEO,KAAK,CAAC;MAC3B,CAAC;MACDC,MAAM,EAAEA,CAAA,KAAK;QACX,IAAI,CAACT,IAAI,CAAC,IAAI,CAAC;MACjB;KACD,CAAC;EACJ;EAEAM,OAAOA,CAAA;IACL,IAAI,CAACjB,SAAS,GAAG,IAAI;IACrB,IAAI,IAAI,CAACC,cAAc,KAAKoB,SAAS,EAAE;MACrC,MAAMC,QAAQ,GAAG,IAAI,CAACrB,cAAc;MACpC,IAAI,CAACA,cAAc,GAAGoB,SAAS;MAC/BC,QAAQ,EAAE;;EAEd;EAEAC,KAAKA,CAACC,KAAa,GAAG;EAEtBC,MAAMA,CACJT,KAAU,EACVU,QAAwB,EACxBJ,QAAoD;IAEpD,IAAI,CAACvB,OAAO,CAAC4B,KAAK,CAChBX,KAAK,YAAYY,UAAU,GAAGZ,KAAK,GAAGa,MAAM,CAACC,IAAI,CAACd,KAAK,EAAEU,QAAQ,CAAC,CACnE;IACD,IAAI,IAAI,CAAC1B,SAAS,EAAE;MAClBsB,QAAQ,EAAE;KACX,MAAM;MACL,IAAI,CAACrB,cAAc,GAAGqB,QAAQ;;EAElC;EAEAS,MAAMA,CAACT,QAAoD;IACzD,IAAI,CAACvB,OAAO,CAACiC,GAAG,EAAE;IAClBV,QAAQ,EAAE;EACZ;;AAGF,OAAO,MAAMjB,IAAI,GAAIF,MAAkB,IACrC,IAAIL,gBAAgB,CAACK,MAAM,CAAC;AAE9B,OAAM,MAAOY,UAAW,SAAQnB,QAAQ;EAI3BW,IAAA;EACD0B,OAAA;EAJDvB,IAAI,GAAG,MAAM;EACbwB,QAAQ;EACjBhC,YACWK,IAAiB,EAClB0B,OAAyB;IAEjC,KAAK,EAAE;IAHE,KAAA1B,IAAI,GAAJA,IAAI;IACL,KAAA0B,OAAO,GAAPA,OAAO;IAGf,IAAI,CAACC,QAAQ,GAAG3B,IAAI,CAAC2B,QAAS;EAChC;EACAX,KAAKA,CAACC,KAAa;IACjB,IAAI,IAAI,CAACS,OAAO,CAACjC,SAAS,KAAK,KAAK,EAAE;MACpC,IAAI,CAACiC,OAAO,CAAChB,OAAO,EAAE;;EAE1B"}