"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/properties/create/page",{

/***/ "(app-pages-browser)/./app/dashboard/properties/create/property-form-steps.tsx":
/*!*****************************************************************!*\
  !*** ./app/dashboard/properties/create/property-form-steps.tsx ***!
  \*****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PropertyFormSteps: () => (/* binding */ PropertyFormSteps)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,Plus,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,Plus,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,Plus,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,Plus,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,Plus,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,Plus,Save,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/hooks/useSimpleLanguage */ \"(app-pages-browser)/./hooks/useSimpleLanguage.tsx\");\n/* harmony import */ var _components_SimpleImageUpload__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/SimpleImageUpload */ \"(app-pages-browser)/./components/SimpleImageUpload.tsx\");\n/* __next_internal_client_entry_do_not_use__ PropertyFormSteps auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction PropertyFormSteps(param) {\n    let { onSave, loading, initialData, isEdit = false, propertyId } = param;\n    _s();\n    const { language } = (0,_hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_11__.useSimpleLanguage)();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const totalSteps = 4;\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const defaultFormData = {\n        title: '',\n        titleAr: '',\n        description: '',\n        descriptionAr: '',\n        price: '',\n        currency: 'SAR',\n        type: 'APARTMENT',\n        status: 'AVAILABLE',\n        bedrooms: '',\n        bathrooms: '',\n        area: '',\n        location: '',\n        locationAr: '',\n        address: '',\n        addressAr: '',\n        city: '',\n        cityAr: '',\n        country: 'Saudi Arabia',\n        countryAr: 'المملكة العربية السعودية',\n        images: [],\n        features: [],\n        featuresAr: [],\n        amenities: [],\n        amenitiesAr: [],\n        yearBuilt: '',\n        parking: '',\n        furnished: false,\n        petFriendly: false,\n        utilities: '',\n        utilitiesAr: '',\n        contactInfo: '',\n        isFeatured: false,\n        isActive: true\n    };\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialData || defaultFormData);\n    const [newFeature, setNewFeature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newFeatureAr, setNewFeatureAr] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newAmenity, setNewAmenity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newAmenityAr, setNewAmenityAr] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Auto-save functionality (only for create mode)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PropertyFormSteps.useEffect\": ()=>{\n            if (!isEdit && !initialData) {\n                const savedData = localStorage.getItem('property-draft');\n                if (savedData) {\n                    try {\n                        const parsed = JSON.parse(savedData);\n                        setFormData(parsed);\n                    } catch (error) {\n                        console.error('Error loading draft:', error);\n                    }\n                }\n            }\n        }\n    }[\"PropertyFormSteps.useEffect\"], [\n        isEdit,\n        initialData\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PropertyFormSteps.useEffect\": ()=>{\n            if (!isEdit) {\n                const timer = setTimeout({\n                    \"PropertyFormSteps.useEffect.timer\": ()=>{\n                        localStorage.setItem('property-draft', JSON.stringify(formData));\n                    }\n                }[\"PropertyFormSteps.useEffect.timer\"], 1000);\n                return ({\n                    \"PropertyFormSteps.useEffect\": ()=>clearTimeout(timer)\n                })[\"PropertyFormSteps.useEffect\"];\n            }\n        }\n    }[\"PropertyFormSteps.useEffect\"], [\n        formData,\n        isEdit\n    ]);\n    // Initialize form data when initialData changes (for edit mode)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PropertyFormSteps.useEffect\": ()=>{\n            if (initialData && isEdit) {\n                setFormData(initialData);\n            }\n        }\n    }[\"PropertyFormSteps.useEffect\"], [\n        initialData,\n        isEdit\n    ]);\n    // Comprehensive bilingual translations\n    const translations = {\n        ar: {\n            step: 'الخطوة',\n            of: 'من',\n            next: 'التالي',\n            previous: 'السابق',\n            save: 'حفظ العقار',\n            required: 'مطلوب',\n            optional: 'اختياري',\n            basicInfo: 'المعلومات الأساسية',\n            propertyDetails: 'تفاصيل العقار',\n            locationInfo: 'معلومات الموقع',\n            additionalInfo: 'معلومات إضافية',\n            title: 'عنوان العقار',\n            description: 'وصف العقار',\n            price: 'السعر',\n            currency: 'العملة',\n            propertyType: 'نوع العقار',\n            status: 'حالة العقار',\n            bedrooms: 'غرف النوم',\n            bathrooms: 'دورات المياه',\n            area: 'المساحة (متر مربع)',\n            yearBuilt: 'سنة البناء',\n            parking: 'مواقف السيارات',\n            location: 'الموقع',\n            address: 'العنوان',\n            city: 'المدينة',\n            country: 'الدولة',\n            images: 'صور العقار',\n            features: 'مميزات العقار',\n            amenities: 'المرافق والخدمات',\n            utilities: 'الخدمات المشمولة',\n            contactInfo: 'معلومات التواصل',\n            furnished: 'مفروش',\n            petFriendly: 'يسمح بالحيوانات الأليفة',\n            featured: 'عقار مميز',\n            active: 'نشط',\n            addFeature: 'إضافة ميزة',\n            addAmenity: 'إضافة مرفق',\n            uploadImages: 'رفع صور العقار',\n            dragDropImages: 'اسحب وأفلت الصور هنا، أو انقر للاختيار',\n            titlePlaceholder: 'أدخل عنوان العقار...',\n            descriptionPlaceholder: 'اكتب وصفاً مفصلاً للعقار...',\n            locationPlaceholder: 'أدخل موقع العقار...',\n            addressPlaceholder: 'أدخل العنوان الكامل...',\n            cityPlaceholder: 'أدخل اسم المدينة...',\n            featurePlaceholder: 'أضف ميزة جديدة...',\n            amenityPlaceholder: 'أضف مرفق جديد...',\n            utilitiesPlaceholder: 'اذكر الخدمات المشمولة...',\n            contactPlaceholder: 'أدخل معلومات التواصل...',\n            stepDescription1: 'أدخل المعلومات الأساسية للعقار',\n            stepDescription2: 'حدد تفاصيل ومواصفات العقار',\n            stepDescription3: 'أضف معلومات الموقع والعنوان',\n            stepDescription4: 'أضف الصور والمعلومات الإضافية',\n            completed: 'مكتمل',\n            current: 'الحالي',\n            pending: 'في الانتظار',\n            imageGallery: 'معرض الصور',\n            mainImage: 'الصورة الرئيسية',\n            additionalImages: 'الصور الإضافية',\n            imageTips: 'نصائح للصور',\n            noFeatures: 'لا توجد مميزات مضافة',\n            noAmenities: 'لا توجد مرافق مضافة',\n            noImages: 'لم يتم رفع صور بعد',\n            setAsMain: 'تعيين كصورة رئيسية',\n            removeImage: 'حذف الصورة',\n            saving: 'جاري الحفظ...',\n            success: 'تم بنجاح',\n            error: 'حدث خطأ'\n        },\n        en: {\n            step: 'Step',\n            of: 'of',\n            next: 'Next',\n            previous: 'Previous',\n            save: 'Save Property',\n            required: 'Required',\n            optional: 'Optional',\n            basicInfo: 'Basic Information',\n            propertyDetails: 'Property Details',\n            locationInfo: 'Location Information',\n            additionalInfo: 'Additional Information',\n            title: 'Property Title',\n            description: 'Property Description',\n            price: 'Price',\n            currency: 'Currency',\n            propertyType: 'Property Type',\n            status: 'Property Status',\n            bedrooms: 'Bedrooms',\n            bathrooms: 'Bathrooms',\n            area: 'Area (sqm)',\n            yearBuilt: 'Year Built',\n            parking: 'Parking Spaces',\n            location: 'Location',\n            address: 'Address',\n            city: 'City',\n            country: 'Country',\n            images: 'Property Images',\n            features: 'Property Features',\n            amenities: 'Amenities & Services',\n            utilities: 'Included Utilities',\n            contactInfo: 'Contact Information',\n            furnished: 'Furnished',\n            petFriendly: 'Pet Friendly',\n            featured: 'Featured Property',\n            active: 'Active',\n            addFeature: 'Add Feature',\n            addAmenity: 'Add Amenity',\n            uploadImages: 'Upload Property Images',\n            dragDropImages: 'Drag and drop images here, or click to select',\n            titlePlaceholder: 'Enter property title...',\n            descriptionPlaceholder: 'Write a detailed property description...',\n            locationPlaceholder: 'Enter property location...',\n            addressPlaceholder: 'Enter full address...',\n            cityPlaceholder: 'Enter city name...',\n            featurePlaceholder: 'Add new feature...',\n            amenityPlaceholder: 'Add new amenity...',\n            utilitiesPlaceholder: 'List included utilities...',\n            contactPlaceholder: 'Enter contact information...',\n            stepDescription1: 'Enter basic property information',\n            stepDescription2: 'Specify property details and specifications',\n            stepDescription3: 'Add location and address information',\n            stepDescription4: 'Add images and additional information',\n            completed: 'Completed',\n            current: 'Current',\n            pending: 'Pending',\n            imageGallery: 'Image Gallery',\n            mainImage: 'Main Image',\n            additionalImages: 'Additional Images',\n            imageTips: 'Image Tips',\n            noFeatures: 'No features added',\n            noAmenities: 'No amenities added',\n            noImages: 'No images uploaded yet',\n            setAsMain: 'Set as Main Image',\n            removeImage: 'Remove Image',\n            saving: 'Saving...',\n            success: 'Success',\n            error: 'Error'\n        }\n    };\n    const t = translations[language];\n    // Bilingual property types\n    const propertyTypes = {\n        ar: {\n            APARTMENT: 'شقة سكنية',\n            VILLA: 'فيلا',\n            TOWNHOUSE: 'تاون هاوس',\n            PENTHOUSE: 'بنتهاوس',\n            STUDIO: 'استوديو',\n            OFFICE: 'مكتب تجاري',\n            SHOP: 'محل تجاري',\n            WAREHOUSE: 'مستودع',\n            LAND: 'قطعة أرض',\n            BUILDING: 'مبنى كامل'\n        },\n        en: {\n            APARTMENT: 'Apartment',\n            VILLA: 'Villa',\n            TOWNHOUSE: 'Townhouse',\n            PENTHOUSE: 'Penthouse',\n            STUDIO: 'Studio',\n            OFFICE: 'Office',\n            SHOP: 'Shop',\n            WAREHOUSE: 'Warehouse',\n            LAND: 'Land',\n            BUILDING: 'Building'\n        }\n    };\n    // Bilingual property statuses\n    const propertyStatuses = {\n        ar: {\n            AVAILABLE: 'متاح للبيع',\n            SOLD: 'تم البيع',\n            RENTED: 'مؤجر',\n            RESERVED: 'محجوز',\n            OFF_MARKET: 'غير متاح'\n        },\n        en: {\n            AVAILABLE: 'Available',\n            SOLD: 'Sold',\n            RENTED: 'Rented',\n            RESERVED: 'Reserved',\n            OFF_MARKET: 'Off Market'\n        }\n    };\n    const stepTitles = [\n        t.basicInfo,\n        t.propertyDetails,\n        t.locationInfo,\n        t.additionalInfo\n    ];\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        await onSave(formData);\n        // Clear draft after successful save\n        localStorage.removeItem('property-draft');\n    };\n    // Auto-save function for images\n    const handleImageAutoSave = async (images)=>{\n        if (!isEdit || !propertyId) return;\n        try {\n            const response = await fetch(\"/api/v1/properties/\".concat(propertyId, \"/images\"), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    images\n                })\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.message || 'Failed to auto-save images');\n            }\n            const result = await response.json();\n            console.log('Images auto-saved successfully:', result);\n        } catch (error) {\n            console.error('Auto-save error:', error);\n            throw error;\n        }\n    };\n    const addFeature = ()=>{\n        if (newFeature.trim()) {\n            setFormData((prev)=>({\n                    ...prev,\n                    features: [\n                        ...prev.features,\n                        newFeature.trim()\n                    ]\n                }));\n            setNewFeature('');\n        }\n    };\n    const addFeatureAr = ()=>{\n        if (newFeatureAr.trim()) {\n            setFormData((prev)=>({\n                    ...prev,\n                    featuresAr: [\n                        ...prev.featuresAr,\n                        newFeatureAr.trim()\n                    ]\n                }));\n            setNewFeatureAr('');\n        }\n    };\n    const addAmenity = ()=>{\n        if (newAmenity.trim()) {\n            setFormData((prev)=>({\n                    ...prev,\n                    amenities: [\n                        ...prev.amenities,\n                        newAmenity.trim()\n                    ]\n                }));\n            setNewAmenity('');\n        }\n    };\n    const addAmenityAr = ()=>{\n        if (newAmenityAr.trim()) {\n            setFormData((prev)=>({\n                    ...prev,\n                    amenitiesAr: [\n                        ...prev.amenitiesAr,\n                        newAmenityAr.trim()\n                    ]\n                }));\n            setNewAmenityAr('');\n        }\n    };\n    const removeFeature = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                features: prev.features.filter((_, i)=>i !== index)\n            }));\n    };\n    const removeFeatureAr = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                featuresAr: prev.featuresAr.filter((_, i)=>i !== index)\n            }));\n    };\n    const removeAmenity = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                amenities: prev.amenities.filter((_, i)=>i !== index)\n            }));\n    };\n    const removeAmenityAr = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                amenitiesAr: prev.amenitiesAr.filter((_, i)=>i !== index)\n            }));\n    };\n    const removeImage = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                images: prev.images.filter((_, i)=>i !== index)\n            }));\n    };\n    const moveImage = (fromIndex, toIndex)=>{\n        setFormData((prev)=>{\n            const newImages = [\n                ...prev.images\n            ];\n            const [movedImage] = newImages.splice(fromIndex, 1);\n            newImages.splice(toIndex, 0, movedImage);\n            return {\n                ...prev,\n                images: newImages\n            };\n        });\n    };\n    const setMainImage = (index)=>{\n        if (index === 0) return; // Already main image\n        moveImage(index, 0);\n    };\n    const nextStep = ()=>{\n        if (currentStep < totalSteps) {\n            setCurrentStep(currentStep + 1);\n        }\n    };\n    const prevStep = ()=>{\n        if (currentStep > 1) {\n            setCurrentStep(currentStep - 1);\n        }\n    };\n    const isStepValid = (step)=>{\n        switch(step){\n            case 1:\n                return formData.title && formData.description && formData.price && formData.type;\n            case 2:\n                return true; // Property details are optional\n            case 3:\n                return formData.location && formData.address && formData.city;\n            case 4:\n                return true; // Additional info is optional\n            default:\n                return false;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(language === 'ar' ? 'rtl' : 'ltr'),\n        dir: language === 'ar' ? 'rtl' : 'ltr',\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-r from-emerald-100/50 to-teal-100/50 dark:from-emerald-900/20 dark:to-teal-900/20 rounded-2xl\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 461,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between \".concat(language === 'ar' ? 'flex-row-reverse' : ''),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4 \".concat(language === 'ar' ? 'flex-row-reverse' : ''),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-14 h-14 bg-gradient-to-br from-emerald-500 via-teal-500 to-cyan-600 rounded-2xl flex items-center justify-center shadow-xl shadow-emerald-200 dark:shadow-emerald-900/50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-black text-lg\",\n                                                                children: currentStep\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 467,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 466,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute -top-1 \".concat(language === 'ar' ? '-right-1' : '-left-1', \" w-6 h-6 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center animate-pulse\"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white text-xs font-bold\",\n                                                                children: \"✦\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 470,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 469,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1 \".concat(language === 'ar' ? 'text-right' : 'text-left'),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2 \".concat(language === 'ar' ? 'flex-row-reverse' : ''),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xl font-black text-slate-800 dark:text-white\",\n                                                                    children: [\n                                                                        t.step,\n                                                                        \" \",\n                                                                        currentStep,\n                                                                        \" \",\n                                                                        t.of,\n                                                                        \" \",\n                                                                        totalSteps\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 475,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"px-3 py-1 bg-emerald-100 dark:bg-emerald-900/30 rounded-full\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-bold text-emerald-700 dark:text-emerald-300\",\n                                                                        children: t.current\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 479,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 478,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 474,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-lg font-bold text-slate-700 dark:text-slate-300\",\n                                                            children: stepTitles[currentStep - 1]\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 482,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-slate-600 dark:text-slate-400\",\n                                                            children: [\n                                                                currentStep === 1 && t.stepDescription1,\n                                                                currentStep === 2 && t.stepDescription2,\n                                                                currentStep === 3 && t.stepDescription3,\n                                                                currentStep === 4 && t.stepDescription4\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 485,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 473,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"\".concat(language === 'ar' ? 'text-right' : 'text-left'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-4xl font-black bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent\",\n                                                    children: [\n                                                        Math.round(currentStep / totalSteps * 100),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 495,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-bold text-emerald-600 dark:text-emerald-400\",\n                                                    children: t.completed\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 498,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                    lineNumber: 463,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 462,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                        lineNumber: 460,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full bg-slate-200 dark:bg-slate-700 rounded-full h-4 shadow-inner\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-r from-emerald-500 via-teal-500 to-cyan-500 h-4 rounded-full transition-all duration-1000 ease-out shadow-lg relative overflow-hidden\",\n                                style: {\n                                    width: \"\".concat(currentStep / totalSteps * 100, \"%\")\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-r from-emerald-400/50 to-teal-400/50 animate-pulse delay-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 514,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 509,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                            lineNumber: 508,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                        lineNumber: 507,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-4 gap-4\",\n                        children: stepTitles.map((title, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 rounded-2xl flex items-center justify-center text-lg font-black transition-all duration-500 shadow-xl \".concat(index + 1 <= currentStep ? 'bg-gradient-to-br from-emerald-500 via-teal-500 to-cyan-600 text-white shadow-emerald-200 dark:shadow-emerald-900/50 scale-110' : index + 1 === currentStep + 1 ? 'bg-gradient-to-br from-slate-300 to-slate-400 text-slate-700 shadow-slate-200 dark:shadow-slate-800 scale-105' : 'bg-slate-200 dark:bg-slate-700 text-slate-500 dark:text-slate-400'),\n                                        children: index + 1 < currentStep ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-7 w-7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 533,\n                                            columnNumber: 19\n                                        }, this) : index + 1 === currentStep ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 bg-white rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 535,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-black\",\n                                            children: index + 1\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 537,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 523,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-bold leading-tight \".concat(index + 1 <= currentStep ? 'text-emerald-700 dark:text-emerald-300' : index + 1 === currentStep + 1 ? 'text-slate-600 dark:text-slate-400' : 'text-slate-500 dark:text-slate-500'),\n                                                children: title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs mt-1 \".concat(index + 1 <= currentStep ? 'text-emerald-600 dark:text-emerald-400' : 'text-slate-400 dark:text-slate-500'),\n                                                children: index + 1 <= currentStep ? t.completed : t.pending\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 550,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 540,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 522,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                        lineNumber: 520,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                lineNumber: 458,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"space-y-8\",\n                children: [\n                    currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                        className: \"shadow-2xl border-0 bg-white/95 dark:bg-slate-800/95 backdrop-blur-md overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                className: \"pb-8 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-blue-900/30 dark:via-indigo-900/30 dark:to-purple-900/30 border-b border-blue-100 dark:border-blue-800/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                    className: \"text-2xl font-bold text-slate-800 dark:text-white flex items-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-14 h-14 bg-gradient-to-br from-blue-500 via-indigo-500 to-purple-600 text-white rounded-2xl flex items-center justify-center text-lg font-bold shadow-xl shadow-blue-200 dark:shadow-blue-900/50\",\n                                            children: \"1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 569,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl\",\n                                                    children: t.basicInfo\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 573,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-normal text-slate-600 dark:text-slate-400 mt-1\",\n                                                    children: language === 'ar' ? 'أدخل المعلومات الأساسية للعقار' : 'Enter basic property information'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 574,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 572,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                    lineNumber: 568,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 567,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                className: \"space-y-8 p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"title\",\n                                                        className: \"text-base font-bold text-slate-800 dark:text-slate-200 flex items-center gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-6 h-6 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-600 dark:text-blue-400 text-sm font-bold\",\n                                                                    children: \"1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 586,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 585,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            t.title,\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500 text-lg\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 589,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 584,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"title\",\n                                                        value: formData.title,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    title: e.target.value\n                                                                })),\n                                                        placeholder: t.titlePlaceholder,\n                                                        required: true,\n                                                        dir: language === 'ar' ? 'rtl' : 'ltr',\n                                                        className: \"h-14 border-2 border-slate-200 dark:border-slate-600 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-300 rounded-xl text-lg bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm shadow-sm hover:shadow-md focus:shadow-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 591,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 583,\n                                                columnNumber: 17\n                                            }, this),\n                                            language === 'en' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"titleEn\",\n                                                        className: \"text-base font-semibold text-slate-700 dark:text-slate-300 flex items-center gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-6 h-6 bg-slate-100 dark:bg-slate-700 rounded-lg flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-slate-600 dark:text-slate-400 text-sm\",\n                                                                    children: \"EN\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 605,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 604,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            t.titleEn,\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-slate-400 text-sm\",\n                                                                children: [\n                                                                    \"(\",\n                                                                    t.optional,\n                                                                    \")\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 608,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 603,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"titleEn\",\n                                                        value: formData.titleAr,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    titleAr: e.target.value\n                                                                })),\n                                                        placeholder: t.titleEnPlaceholder,\n                                                        dir: \"ltr\",\n                                                        className: \"h-12 border-2 border-slate-200 dark:border-slate-600 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-300 rounded-xl bg-white/30 dark:bg-slate-800/30\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 610,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 602,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 582,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"description\",\n                                                    className: \"text-base font-bold text-slate-800 dark:text-slate-200 flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-6 h-6 bg-emerald-100 dark:bg-emerald-900/30 rounded-lg flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-emerald-600 dark:text-emerald-400 text-sm font-bold\",\n                                                                children: \"2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 627,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 626,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        t.description,\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-500 text-lg\",\n                                                            children: \"*\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 630,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 625,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                    id: \"description\",\n                                                    value: formData.description,\n                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                description: e.target.value\n                                                            })),\n                                                    placeholder: t.descriptionPlaceholder,\n                                                    required: true,\n                                                    rows: 6,\n                                                    dir: \"rtl\",\n                                                    className: \"border-2 border-slate-200 dark:border-slate-600 focus:border-emerald-500 dark:focus:border-emerald-400 transition-all duration-300 resize-none rounded-xl text-lg bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm shadow-sm hover:shadow-md focus:shadow-lg\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 632,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 624,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 623,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"price\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2\",\n                                                        children: [\n                                                            t.price,\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500 text-lg\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 650,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 648,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"price\",\n                                                        type: \"number\",\n                                                        value: formData.price,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    price: e.target.value\n                                                                })),\n                                                        placeholder: \"0\",\n                                                        required: true,\n                                                        className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-200 rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 652,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"currency\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.currency\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 663,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                        value: formData.currency,\n                                                        onValueChange: (value)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    currency: value\n                                                                })),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                                className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 rounded-lg\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 668,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 667,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                        value: \"SAR\",\n                                                                        children: \"SAR - ريال سعودي\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 671,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                        value: \"AED\",\n                                                                        children: \"AED - درهم إماراتي\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 672,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                        value: \"USD\",\n                                                                        children: \"USD - دولار أمريكي\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 673,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                        value: \"EUR\",\n                                                                        children: \"EUR - يورو\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 674,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                        value: \"GBP\",\n                                                                        children: \"GBP - جنيه إسترليني\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 675,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 670,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 666,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 662,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"type\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2\",\n                                                        children: [\n                                                            t.propertyType,\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500 text-lg\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 682,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 680,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                        value: formData.type,\n                                                        onValueChange: (value)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    type: value\n                                                                })),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                                className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 rounded-lg\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 686,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 685,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                                children: Object.entries(propertyTypes[language]).map((param)=>{\n                                                                    let [key, value] = param;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                        value: key,\n                                                                        children: value\n                                                                    }, key, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 690,\n                                                                        columnNumber: 25\n                                                                    }, this);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 688,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 684,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 679,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 646,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"status\",\n                                                    className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                    children: t.status\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 700,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                    value: formData.status,\n                                                    onValueChange: (value)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                status: value\n                                                            })),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                            className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 rounded-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 705,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 704,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                            children: Object.entries(propertyStatuses[language]).map((param)=>{\n                                                                let [key, value] = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                    value: key,\n                                                                    children: value\n                                                                }, key, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 709,\n                                                                    columnNumber: 25\n                                                                }, this);\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 707,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 703,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 699,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 698,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 580,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                        lineNumber: 566,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                        className: \"shadow-xl border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                className: \"pb-6 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-t-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg\",\n                                            children: \"2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 724,\n                                            columnNumber: 17\n                                        }, this),\n                                        t.propertyDetails\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                    lineNumber: 723,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 722,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                className: \"space-y-8 p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"bedrooms\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.bedrooms\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 734,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"bedrooms\",\n                                                        type: \"number\",\n                                                        value: formData.bedrooms,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    bedrooms: e.target.value\n                                                                })),\n                                                        placeholder: \"0\",\n                                                        min: \"0\",\n                                                        className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 737,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 733,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"bathrooms\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.bathrooms\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 748,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"bathrooms\",\n                                                        type: \"number\",\n                                                        value: formData.bathrooms,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    bathrooms: e.target.value\n                                                                })),\n                                                        placeholder: \"0\",\n                                                        min: \"0\",\n                                                        className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 751,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 747,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"area\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.area\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 762,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"area\",\n                                                        type: \"number\",\n                                                        value: formData.area,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    area: e.target.value\n                                                                })),\n                                                        placeholder: \"0\",\n                                                        min: \"0\",\n                                                        className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 765,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 761,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"yearBuilt\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.yearBuilt\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 776,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"yearBuilt\",\n                                                        type: \"number\",\n                                                        value: formData.yearBuilt,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    yearBuilt: e.target.value\n                                                                })),\n                                                        placeholder: \"2024\",\n                                                        min: \"1900\",\n                                                        max: \"2030\",\n                                                        className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 779,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 775,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"parking\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.parking\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 791,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"parking\",\n                                                        type: \"number\",\n                                                        value: formData.parking,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    parking: e.target.value\n                                                                })),\n                                                        placeholder: \"0\",\n                                                        min: \"0\",\n                                                        className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 794,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 790,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 732,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_10__.Separator, {\n                                        className: \"my-8\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 806,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                    children: t.features\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 811,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                            value: newFeature,\n                                                            onChange: (e)=>setNewFeature(e.target.value),\n                                                            placeholder: t.featurePlaceholder,\n                                                            onKeyPress: (e)=>e.key === 'Enter' && (e.preventDefault(), addFeature()),\n                                                            className: \"h-10 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 815,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            type: \"button\",\n                                                            onClick: addFeature,\n                                                            size: \"sm\",\n                                                            className: \"bg-green-600 hover:bg-green-700 px-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 823,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 822,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 814,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-2 min-h-[40px] p-3 border-2 border-dashed border-gray-200 dark:border-gray-600 rounded-lg\",\n                                                    children: [\n                                                        formData.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                variant: \"secondary\",\n                                                                className: \"flex items-center gap-1 px-3 py-1 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200\",\n                                                                children: [\n                                                                    feature,\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-3 w-3 cursor-pointer hover:text-red-600\",\n                                                                        onClick: ()=>removeFeature(index)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 830,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 828,\n                                                                columnNumber: 23\n                                                            }, this)),\n                                                        formData.features.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: t.noFeatures\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 837,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 826,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 810,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 809,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                    children: t.amenities\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 846,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                            value: newAmenity,\n                                                            onChange: (e)=>setNewAmenity(e.target.value),\n                                                            placeholder: t.amenityPlaceholder,\n                                                            onKeyPress: (e)=>e.key === 'Enter' && (e.preventDefault(), addAmenity()),\n                                                            className: \"h-10 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 850,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            type: \"button\",\n                                                            onClick: addAmenity,\n                                                            size: \"sm\",\n                                                            className: \"bg-green-600 hover:bg-green-700 px-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 858,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 857,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 849,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-2 min-h-[40px] p-3 border-2 border-dashed border-gray-200 dark:border-gray-600 rounded-lg\",\n                                                    children: [\n                                                        formData.amenities.map((amenity, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                variant: \"secondary\",\n                                                                className: \"flex items-center gap-1 px-3 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200\",\n                                                                children: [\n                                                                    amenity,\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-3 w-3 cursor-pointer hover:text-red-600\",\n                                                                        onClick: ()=>removeAmenity(index)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 865,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 863,\n                                                                columnNumber: 23\n                                                            }, this)),\n                                                        formData.amenities.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: t.noAmenities\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 872,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 861,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 845,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 844,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 730,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                        lineNumber: 721,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                        className: \"shadow-xl border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                className: \"pb-6 bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-t-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg\",\n                                            children: \"3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 886,\n                                            columnNumber: 17\n                                        }, this),\n                                        t.locationInfo\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                    lineNumber: 885,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 884,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                className: \"space-y-8 p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"location\",\n                                                    className: \"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2\",\n                                                    children: [\n                                                        t.location,\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-500 text-lg\",\n                                                            children: \"*\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 898,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 896,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                    id: \"location\",\n                                                    value: formData.location,\n                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                location: e.target.value\n                                                            })),\n                                                    placeholder: t.locationPlaceholder,\n                                                    required: true,\n                                                    className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 900,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 895,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 894,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"address\",\n                                                    className: \"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2\",\n                                                    children: [\n                                                        t.address,\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-500 text-lg\",\n                                                            children: \"*\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 917,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 915,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                    id: \"address\",\n                                                    value: formData.address,\n                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                address: e.target.value\n                                                            })),\n                                                    placeholder: t.addressPlaceholder,\n                                                    required: true,\n                                                    className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 919,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 914,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 913,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"city\",\n                                                                className: \"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2\",\n                                                                children: [\n                                                                    t.city,\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-500 text-lg\",\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 937,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 935,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                id: \"city\",\n                                                                value: formData.city,\n                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            city: e.target.value\n                                                                        })),\n                                                                placeholder: t.cityPlaceholder,\n                                                                required: true,\n                                                                className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 939,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 934,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"country\",\n                                                                className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                                children: t.country\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 949,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                                value: formData.country,\n                                                                onValueChange: (value)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            country: value\n                                                                        })),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                                        className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 rounded-lg\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {}, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                            lineNumber: 954,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 953,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"UAE\",\n                                                                                children: \"UAE - الإمارات العربية المتحدة\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 957,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Saudi Arabia\",\n                                                                                children: \"Saudi Arabia - المملكة العربية السعودية\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 958,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Qatar\",\n                                                                                children: \"Qatar - قطر\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 959,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Kuwait\",\n                                                                                children: \"Kuwait - الكويت\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 960,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Bahrain\",\n                                                                                children: \"Bahrain - البحرين\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 961,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Oman\",\n                                                                                children: \"Oman - عُمان\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 962,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 956,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 952,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 948,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 933,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"cityAr\",\n                                                                className: \"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2\",\n                                                                children: [\n                                                                    t.cityAr,\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-400 text-sm\",\n                                                                        children: [\n                                                                            \"(\",\n                                                                            t.optional,\n                                                                            \")\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 971,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 969,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                id: \"cityAr\",\n                                                                value: formData.cityAr,\n                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            cityAr: e.target.value\n                                                                        })),\n                                                                placeholder: t.cityArPlaceholder,\n                                                                dir: \"rtl\",\n                                                                className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 973,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 968,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"countryAr\",\n                                                                className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                                children: t.countryAr\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 983,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                id: \"countryAr\",\n                                                                value: formData.countryAr,\n                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            countryAr: e.target.value\n                                                                        })),\n                                                                placeholder: \"أدخل اسم البلد بالعربية\",\n                                                                dir: \"rtl\",\n                                                                className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 986,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 982,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 967,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 932,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 892,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                        lineNumber: 883,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                        className: \"shadow-xl border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                className: \"pb-6 bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-900/20 dark:to-amber-900/20 rounded-t-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg\",\n                                            children: \"4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 1006,\n                                            columnNumber: 17\n                                        }, this),\n                                        t.additionalInfo\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                    lineNumber: 1005,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 1004,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                className: \"space-y-8 p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SimpleImageUpload__WEBPACK_IMPORTED_MODULE_12__.SimpleImageUpload, {\n                                        images: formData.images,\n                                        onImagesChange: (images)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    images\n                                                })),\n                                        onAutoSave: isEdit ? handleImageAutoSave : undefined,\n                                        onUploadStatusChange: setIsUploading,\n                                        propertyId: propertyId,\n                                        maxImages: 10,\n                                        disabled: loading\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 1014,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_10__.Separator, {\n                                        className: \"my-8\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 1026,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"utilities\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.utilities\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1031,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                        id: \"utilities\",\n                                                        value: formData.utilities,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    utilities: e.target.value\n                                                                })),\n                                                        placeholder: t.utilitiesPlaceholder,\n                                                        rows: 3,\n                                                        className: \"border-2 border-gray-200 dark:border-gray-600 focus:border-orange-500 dark:focus:border-orange-400 transition-all duration-200 resize-none rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1034,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1030,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"utilitiesAr\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.utilitiesAr\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1044,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                        id: \"utilitiesAr\",\n                                                        value: formData.utilitiesAr,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    utilitiesAr: e.target.value\n                                                                })),\n                                                        placeholder: t.utilitiesArPlaceholder,\n                                                        dir: \"rtl\",\n                                                        rows: 3,\n                                                        className: \"border-2 border-gray-200 dark:border-gray-600 focus:border-orange-500 dark:focus:border-orange-400 transition-all duration-200 resize-none rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1047,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1043,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 1029,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"contactInfo\",\n                                                className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                children: t.contactInfo\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1060,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                id: \"contactInfo\",\n                                                value: formData.contactInfo,\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            contactInfo: e.target.value\n                                                        })),\n                                                placeholder: t.contactPlaceholder,\n                                                rows: 3,\n                                                className: \"border-2 border-gray-200 dark:border-gray-600 focus:border-orange-500 dark:focus:border-orange-400 transition-all duration-200 resize-none rounded-lg\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1063,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 1059,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_10__.Separator, {\n                                        className: \"my-8\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 1073,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-4 gap-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-orange-400 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_7__.Switch, {\n                                                        id: \"furnished\",\n                                                        checked: formData.furnished,\n                                                        onCheckedChange: (checked)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    furnished: checked\n                                                                })),\n                                                        className: \"data-[state=checked]:bg-orange-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1078,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"furnished\",\n                                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer\",\n                                                        children: t.furnished\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1084,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1077,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-orange-400 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_7__.Switch, {\n                                                        id: \"petFriendly\",\n                                                        checked: formData.petFriendly,\n                                                        onCheckedChange: (checked)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    petFriendly: checked\n                                                                })),\n                                                        className: \"data-[state=checked]:bg-orange-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1089,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"petFriendly\",\n                                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer\",\n                                                        children: t.petFriendly\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1095,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1088,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-orange-400 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_7__.Switch, {\n                                                        id: \"isFeatured\",\n                                                        checked: formData.isFeatured,\n                                                        onCheckedChange: (checked)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    isFeatured: checked\n                                                                })),\n                                                        className: \"data-[state=checked]:bg-orange-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1100,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"isFeatured\",\n                                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer\",\n                                                        children: t.featured\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1106,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1099,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-orange-400 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_7__.Switch, {\n                                                        id: \"isActive\",\n                                                        checked: formData.isActive,\n                                                        onCheckedChange: (checked)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    isActive: checked\n                                                                })),\n                                                        className: \"data-[state=checked]:bg-orange-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1111,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"isActive\",\n                                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer\",\n                                                        children: t.active\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1117,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1110,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 1076,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 1012,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                        lineNumber: 1003,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center pt-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                type: \"button\",\n                                variant: \"outline\",\n                                onClick: prevStep,\n                                disabled: currentStep === 1,\n                                className: \"flex items-center gap-2 px-6 py-3 h-12 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 1135,\n                                        columnNumber: 13\n                                    }, this),\n                                    t.previous\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 1128,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-4\",\n                                children: currentStep < totalSteps ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"button\",\n                                    onClick: nextStep,\n                                    disabled: !isStepValid(currentStep),\n                                    className: \"flex items-center gap-2 px-8 py-3 h-12 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl\",\n                                    children: [\n                                        t.next,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 1148,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                    lineNumber: 1141,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"submit\",\n                                    disabled: loading || isUploading || !isStepValid(currentStep),\n                                    className: \"flex items-center gap-2 px-8 py-3 h-12 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl\",\n                                    children: [\n                                        loading || isUploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 1157,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Save_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 1159,\n                                            columnNumber: 19\n                                        }, this),\n                                        isUploading ? language === 'ar' ? 'جاري رفع الصور...' : 'Uploading images...' : t.save\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                    lineNumber: 1151,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 1139,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                        lineNumber: 1127,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                lineNumber: 563,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n        lineNumber: 456,\n        columnNumber: 5\n    }, this);\n}\n_s(PropertyFormSteps, \"MaOwaAOGs49JLOCSkgePFHwIOnI=\", false, function() {\n    return [\n        _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_11__.useSimpleLanguage\n    ];\n});\n_c = PropertyFormSteps;\nvar _c;\n$RefreshReg$(_c, \"PropertyFormSteps\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/properties/create/property-form-steps.tsx\n"));

/***/ })

});