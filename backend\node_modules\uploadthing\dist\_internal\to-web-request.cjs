var Config = require('effect/Config');
var Data = require('effect/Data');
var Effect = require('effect/Effect');
var shared = require('@uploadthing/shared');

function _interopNamespace(e) {
  if (e && e.__esModule) return e;
  var n = Object.create(null);
  if (e) {
    Object.keys(e).forEach(function (k) {
      if (k !== 'default') {
        var d = Object.getOwnPropertyDescriptor(e, k);
        Object.defineProperty(n, k, d.get ? d : {
          enumerable: true,
          get: function () { return e[k]; }
        });
      }
    });
  }
  n.default = e;
  return n;
}

var Config__namespace = /*#__PURE__*/_interopNamespace(Config);
var Data__namespace = /*#__PURE__*/_interopNamespace(Data);
var Effect__namespace = /*#__PURE__*/_interopNamespace(Effect);

class InvalidURL extends Data__namespace.Error {
    constructor(attemptedUrl, base){
        Effect__namespace.runSync(Effect__namespace.logError(`Failed to parse URL from request. '${attemptedUrl}' is not a valid URL with base '${base}'.`));
        super({
            reason: `Failed to parse URL from request. '${attemptedUrl}' is not a valid URL with base '${base}'.`
        }), this._tag = "InvalidURL", this.name = "InvalidURLError";
    }
}
const parseURL = (req)=>{
    const headers = req.headers;
    let relativeUrl = req.url ?? "/";
    if ("baseUrl" in req && typeof req.baseUrl === "string") {
        relativeUrl = req.baseUrl + relativeUrl;
    }
    const proto = headers?.["x-forwarded-proto"] ?? "http";
    const host = headers?.["x-forwarded-host"] ?? headers?.host;
    const baseUrl = Config__namespace.string("url").pipe(Config__namespace.withDefault(`${proto.toString()}://${host?.toString()}`));
    return Effect__namespace.flatMap(baseUrl, (baseUrl)=>Effect__namespace.try({
            try: ()=>new URL(relativeUrl, baseUrl),
            catch: ()=>new InvalidURL(relativeUrl, baseUrl)
        })).pipe(Effect__namespace.catchTag("ConfigError", ()=>Effect__namespace.fail(new InvalidURL(relativeUrl))));
};
const isBodyAllowed = (method)=>[
        "POST",
        "PUT",
        "PATCH"
    ].includes(method);
const getPostBody = (opts)=>Effect__namespace.async((resume)=>{
        const { req } = opts;
        if (!req.method || !isBodyAllowed(req.method)) {
            return resume(Effect__namespace.succeed(undefined));
        }
        const contentType = req.headers?.["content-type"];
        if ("body" in req) {
            if (contentType !== "application/json") {
                Effect__namespace.runSync(Effect__namespace.logError("Expected JSON content type, got:", contentType));
                return resume(new shared.UploadThingError({
                    code: "BAD_REQUEST",
                    message: "INVALID_CONTENT_TYPE"
                }));
            }
            if (typeof req.body !== "object") {
                Effect__namespace.runSync(Effect__namespace.logError("Expected body to be of type 'object', got:", typeof req.body));
                return resume(new shared.UploadThingError({
                    code: "BAD_REQUEST",
                    message: "INVALID_BODY"
                }));
            }
            Effect__namespace.runSync(Effect__namespace.logDebug("Body parsed successfully.", req.body));
            return resume(Effect__namespace.succeed(req.body));
        }
        let body = "";
        req.on("data", (data)=>body += data);
        req.on("end", ()=>{
            const parsedBody = Effect__namespace.try({
                try: ()=>JSON.parse(body),
                catch: (err)=>new shared.UploadThingError({
                        code: "BAD_REQUEST",
                        message: "INVALID_JSON",
                        cause: err
                    })
            });
            return resume(parsedBody);
        });
    });
const toWebRequest = (req, body)=>{
    body ??= req.body;
    const bodyStr = typeof body === "string" ? body : JSON.stringify(body);
    const method = req.method ?? "GET";
    const allowsBody = isBodyAllowed(method);
    const headers = new Headers();
    for (const [key, value] of Object.entries(req.headers ?? [])){
        if (typeof value === "string") headers.set(key, value);
        if (Array.isArray(value)) headers.set(key, value.join(","));
    }
    return parseURL(req).pipe(Effect__namespace.catchTag("InvalidURL", (e)=>Effect__namespace.die(e)), Effect__namespace.andThen((url)=>new Request(url, {
            method,
            headers,
            ...allowsBody ? {
                body: bodyStr
            } : {}
        })));
};

exports.getPostBody = getPostBody;
exports.toWebRequest = toWebRequest;
