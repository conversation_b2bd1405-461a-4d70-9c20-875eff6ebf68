declare const application: {
    readonly "application/andrew-inset": {
        readonly source: "iana";
        readonly extensions: readonly ["ez"];
    };
    readonly "application/applixware": {
        readonly source: "apache";
        readonly extensions: readonly ["aw"];
    };
    readonly "application/atom+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["atom"];
    };
    readonly "application/atomcat+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["atomcat"];
    };
    readonly "application/atomdeleted+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["atomdeleted"];
    };
    readonly "application/atomsvc+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["atomsvc"];
    };
    readonly "application/atsc-dwd+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["dwd"];
    };
    readonly "application/atsc-held+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["held"];
    };
    readonly "application/atsc-rsat+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["rsat"];
    };
    readonly "application/calendar+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["xcs"];
    };
    readonly "application/ccxml+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["ccxml"];
    };
    readonly "application/cdfx+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["cdfx"];
    };
    readonly "application/cdmi-capability": {
        readonly source: "iana";
        readonly extensions: readonly ["cdmia"];
    };
    readonly "application/cdmi-container": {
        readonly source: "iana";
        readonly extensions: readonly ["cdmic"];
    };
    readonly "application/cdmi-domain": {
        readonly source: "iana";
        readonly extensions: readonly ["cdmid"];
    };
    readonly "application/cdmi-object": {
        readonly source: "iana";
        readonly extensions: readonly ["cdmio"];
    };
    readonly "application/cdmi-queue": {
        readonly source: "iana";
        readonly extensions: readonly ["cdmiq"];
    };
    readonly "application/cpl+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["cpl"];
    };
    readonly "application/cu-seeme": {
        readonly source: "apache";
        readonly extensions: readonly ["cu"];
    };
    readonly "application/dash+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["mpd"];
    };
    readonly "application/dash-patch+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["mpp"];
    };
    readonly "application/davmount+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["davmount"];
    };
    readonly "application/dicom": {
        readonly source: "iana";
        readonly extensions: readonly ["dcm"];
    };
    readonly "application/docbook+xml": {
        readonly source: "apache";
        readonly extensions: readonly ["dbk"];
    };
    readonly "application/dssc+der": {
        readonly source: "iana";
        readonly extensions: readonly ["dssc"];
    };
    readonly "application/dssc+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["xdssc"];
    };
    readonly "application/ecmascript": {
        readonly source: "iana";
        readonly extensions: readonly ["es", "ecma"];
    };
    readonly "application/emma+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["emma"];
    };
    readonly "application/emotionml+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["emotionml"];
    };
    readonly "application/epub+zip": {
        readonly source: "iana";
        readonly extensions: readonly ["epub"];
    };
    readonly "application/exi": {
        readonly source: "iana";
        readonly extensions: readonly ["exi"];
    };
    readonly "application/express": {
        readonly source: "iana";
        readonly extensions: readonly ["exp"];
    };
    readonly "application/fdt+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["fdt"];
    };
    readonly "application/font-tdpfr": {
        readonly source: "iana";
        readonly extensions: readonly ["pfr"];
    };
    readonly "application/geo+json": {
        readonly source: "iana";
        readonly extensions: readonly ["geojson"];
    };
    readonly "application/gml+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["gml"];
    };
    readonly "application/gpx+xml": {
        readonly source: "apache";
        readonly extensions: readonly ["gpx"];
    };
    readonly "application/gxf": {
        readonly source: "apache";
        readonly extensions: readonly ["gxf"];
    };
    readonly "application/gzip": {
        readonly source: "iana";
        readonly extensions: readonly ["gz"];
    };
    readonly "application/hyperstudio": {
        readonly source: "iana";
        readonly extensions: readonly ["stk"];
    };
    readonly "application/inkml+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["ink", "inkml"];
    };
    readonly "application/ipfix": {
        readonly source: "iana";
        readonly extensions: readonly ["ipfix"];
    };
    readonly "application/its+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["its"];
    };
    readonly "application/java-archive": {
        readonly source: "apache";
        readonly extensions: readonly ["jar", "war", "ear"];
    };
    readonly "application/java-serialized-object": {
        readonly source: "apache";
        readonly extensions: readonly ["ser"];
    };
    readonly "application/java-vm": {
        readonly source: "apache";
        readonly extensions: readonly ["class"];
    };
    readonly "application/javascript": {
        readonly source: "iana";
        readonly charset: "UTF-8";
        readonly extensions: readonly ["js", "mjs"];
    };
    readonly "application/json": {
        readonly source: "iana";
        readonly charset: "UTF-8";
        readonly extensions: readonly ["json", "map"];
    };
    readonly "application/jsonml+json": {
        readonly source: "apache";
        readonly extensions: readonly ["jsonml"];
    };
    readonly "application/ld+json": {
        readonly source: "iana";
        readonly extensions: readonly ["jsonld"];
    };
    readonly "application/lgr+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["lgr"];
    };
    readonly "application/lost+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["lostxml"];
    };
    readonly "application/mac-binhex40": {
        readonly source: "iana";
        readonly extensions: readonly ["hqx"];
    };
    readonly "application/mac-compactpro": {
        readonly source: "apache";
        readonly extensions: readonly ["cpt"];
    };
    readonly "application/mads+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["mads"];
    };
    readonly "application/manifest+json": {
        readonly source: "iana";
        readonly charset: "UTF-8";
        readonly extensions: readonly ["webmanifest"];
    };
    readonly "application/marc": {
        readonly source: "iana";
        readonly extensions: readonly ["mrc"];
    };
    readonly "application/marcxml+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["mrcx"];
    };
    readonly "application/mathematica": {
        readonly source: "iana";
        readonly extensions: readonly ["ma", "nb", "mb"];
    };
    readonly "application/mathml+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["mathml"];
    };
    readonly "application/mbox": {
        readonly source: "iana";
        readonly extensions: readonly ["mbox"];
    };
    readonly "application/media-policy-dataset+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["mpf"];
    };
    readonly "application/mediaservercontrol+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["mscml"];
    };
    readonly "application/metalink+xml": {
        readonly source: "apache";
        readonly extensions: readonly ["metalink"];
    };
    readonly "application/metalink4+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["meta4"];
    };
    readonly "application/mets+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["mets"];
    };
    readonly "application/mmt-aei+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["maei"];
    };
    readonly "application/mmt-usd+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["musd"];
    };
    readonly "application/mods+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["mods"];
    };
    readonly "application/mp21": {
        readonly source: "iana";
        readonly extensions: readonly ["m21", "mp21"];
    };
    readonly "application/mp4": {
        readonly source: "iana";
        readonly extensions: readonly ["mp4s", "m4p"];
    };
    readonly "application/msword": {
        readonly source: "iana";
        readonly extensions: readonly ["doc", "dot"];
    };
    readonly "application/mxf": {
        readonly source: "iana";
        readonly extensions: readonly ["mxf"];
    };
    readonly "application/n-quads": {
        readonly source: "iana";
        readonly extensions: readonly ["nq"];
    };
    readonly "application/n-triples": {
        readonly source: "iana";
        readonly extensions: readonly ["nt"];
    };
    readonly "application/node": {
        readonly source: "iana";
        readonly extensions: readonly ["cjs"];
    };
    readonly "application/octet-stream": {
        readonly source: "iana";
        readonly extensions: readonly ["bin", "dms", "lrf", "mar", "so", "dist", "distz", "pkg", "bpk", "dump", "elc", "deploy", "exe", "dll", "deb", "dmg", "iso", "img", "msi", "msp", "msm", "buffer"];
    };
    readonly "application/oda": {
        readonly source: "iana";
        readonly extensions: readonly ["oda"];
    };
    readonly "application/oebps-package+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["opf"];
    };
    readonly "application/ogg": {
        readonly source: "iana";
        readonly extensions: readonly ["ogx"];
    };
    readonly "application/omdoc+xml": {
        readonly source: "apache";
        readonly extensions: readonly ["omdoc"];
    };
    readonly "application/onenote": {
        readonly source: "apache";
        readonly extensions: readonly ["onetoc", "onetoc2", "onetmp", "onepkg"];
    };
    readonly "application/oxps": {
        readonly source: "iana";
        readonly extensions: readonly ["oxps"];
    };
    readonly "application/p2p-overlay+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["relo"];
    };
    readonly "application/patch-ops-error+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["xer"];
    };
    readonly "application/pdf": {
        readonly source: "iana";
        readonly extensions: readonly ["pdf"];
    };
    readonly "application/pgp-encrypted": {
        readonly source: "iana";
        readonly extensions: readonly ["pgp"];
    };
    readonly "application/pgp-keys": {
        readonly source: "iana";
        readonly extensions: readonly ["asc"];
    };
    readonly "application/pgp-signature": {
        readonly source: "iana";
        readonly extensions: readonly ["asc", "sig"];
    };
    readonly "application/pics-rules": {
        readonly source: "apache";
        readonly extensions: readonly ["prf"];
    };
    readonly "application/pkcs10": {
        readonly source: "iana";
        readonly extensions: readonly ["p10"];
    };
    readonly "application/pkcs7-mime": {
        readonly source: "iana";
        readonly extensions: readonly ["p7m", "p7c"];
    };
    readonly "application/pkcs7-signature": {
        readonly source: "iana";
        readonly extensions: readonly ["p7s"];
    };
    readonly "application/pkcs8": {
        readonly source: "iana";
        readonly extensions: readonly ["p8"];
    };
    readonly "application/pkix-attr-cert": {
        readonly source: "iana";
        readonly extensions: readonly ["ac"];
    };
    readonly "application/pkix-cert": {
        readonly source: "iana";
        readonly extensions: readonly ["cer"];
    };
    readonly "application/pkix-crl": {
        readonly source: "iana";
        readonly extensions: readonly ["crl"];
    };
    readonly "application/pkix-pkipath": {
        readonly source: "iana";
        readonly extensions: readonly ["pkipath"];
    };
    readonly "application/pkixcmp": {
        readonly source: "iana";
        readonly extensions: readonly ["pki"];
    };
    readonly "application/pls+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["pls"];
    };
    readonly "application/postscript": {
        readonly source: "iana";
        readonly extensions: readonly ["ai", "eps", "ps"];
    };
    readonly "application/provenance+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["provx"];
    };
    readonly "application/prs.cww": {
        readonly source: "iana";
        readonly extensions: readonly ["cww"];
    };
    readonly "application/pskc+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["pskcxml"];
    };
    readonly "application/rdf+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["rdf", "owl"];
    };
    readonly "application/reginfo+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["rif"];
    };
    readonly "application/relax-ng-compact-syntax": {
        readonly source: "iana";
        readonly extensions: readonly ["rnc"];
    };
    readonly "application/resource-lists+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["rl"];
    };
    readonly "application/resource-lists-diff+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["rld"];
    };
    readonly "application/rls-services+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["rs"];
    };
    readonly "application/route-apd+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["rapd"];
    };
    readonly "application/route-s-tsid+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["sls"];
    };
    readonly "application/route-usd+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["rusd"];
    };
    readonly "application/rpki-ghostbusters": {
        readonly source: "iana";
        readonly extensions: readonly ["gbr"];
    };
    readonly "application/rpki-manifest": {
        readonly source: "iana";
        readonly extensions: readonly ["mft"];
    };
    readonly "application/rpki-roa": {
        readonly source: "iana";
        readonly extensions: readonly ["roa"];
    };
    readonly "application/rsd+xml": {
        readonly source: "apache";
        readonly extensions: readonly ["rsd"];
    };
    readonly "application/rss+xml": {
        readonly source: "apache";
        readonly extensions: readonly ["rss"];
    };
    readonly "application/rtf": {
        readonly source: "iana";
        readonly extensions: readonly ["rtf"];
    };
    readonly "application/sbml+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["sbml"];
    };
    readonly "application/scvp-cv-request": {
        readonly source: "iana";
        readonly extensions: readonly ["scq"];
    };
    readonly "application/scvp-cv-response": {
        readonly source: "iana";
        readonly extensions: readonly ["scs"];
    };
    readonly "application/scvp-vp-request": {
        readonly source: "iana";
        readonly extensions: readonly ["spq"];
    };
    readonly "application/scvp-vp-response": {
        readonly source: "iana";
        readonly extensions: readonly ["spp"];
    };
    readonly "application/sdp": {
        readonly source: "iana";
        readonly extensions: readonly ["sdp"];
    };
    readonly "application/senml+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["senmlx"];
    };
    readonly "application/sensml+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["sensmlx"];
    };
    readonly "application/set-payment-initiation": {
        readonly source: "iana";
        readonly extensions: readonly ["setpay"];
    };
    readonly "application/set-registration-initiation": {
        readonly source: "iana";
        readonly extensions: readonly ["setreg"];
    };
    readonly "application/shf+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["shf"];
    };
    readonly "application/sieve": {
        readonly source: "iana";
        readonly extensions: readonly ["siv", "sieve"];
    };
    readonly "application/smil+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["smi", "smil"];
    };
    readonly "application/sparql-query": {
        readonly source: "iana";
        readonly extensions: readonly ["rq"];
    };
    readonly "application/sparql-results+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["srx"];
    };
    readonly "application/srgs": {
        readonly source: "iana";
        readonly extensions: readonly ["gram"];
    };
    readonly "application/srgs+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["grxml"];
    };
    readonly "application/sru+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["sru"];
    };
    readonly "application/ssdl+xml": {
        readonly source: "apache";
        readonly extensions: readonly ["ssdl"];
    };
    readonly "application/ssml+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["ssml"];
    };
    readonly "application/swid+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["swidtag"];
    };
    readonly "application/tei+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["tei", "teicorpus"];
    };
    readonly "application/thraud+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["tfi"];
    };
    readonly "application/timestamped-data": {
        readonly source: "iana";
        readonly extensions: readonly ["tsd"];
    };
    readonly "application/trig": {
        readonly source: "iana";
        readonly extensions: readonly ["trig"];
    };
    readonly "application/ttml+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["ttml"];
    };
    readonly "application/urc-ressheet+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["rsheet"];
    };
    readonly "application/urc-targetdesc+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["td"];
    };
    readonly "application/vnd.1000minds.decision-model+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["1km"];
    };
    readonly "application/vnd.3gpp.pic-bw-large": {
        readonly source: "iana";
        readonly extensions: readonly ["plb"];
    };
    readonly "application/vnd.3gpp.pic-bw-small": {
        readonly source: "iana";
        readonly extensions: readonly ["psb"];
    };
    readonly "application/vnd.3gpp.pic-bw-var": {
        readonly source: "iana";
        readonly extensions: readonly ["pvb"];
    };
    readonly "application/vnd.3gpp2.tcap": {
        readonly source: "iana";
        readonly extensions: readonly ["tcap"];
    };
    readonly "application/vnd.3m.post-it-notes": {
        readonly source: "iana";
        readonly extensions: readonly ["pwn"];
    };
    readonly "application/vnd.accpac.simply.aso": {
        readonly source: "iana";
        readonly extensions: readonly ["aso"];
    };
    readonly "application/vnd.accpac.simply.imp": {
        readonly source: "iana";
        readonly extensions: readonly ["imp"];
    };
    readonly "application/vnd.acucobol": {
        readonly source: "iana";
        readonly extensions: readonly ["acu"];
    };
    readonly "application/vnd.acucorp": {
        readonly source: "iana";
        readonly extensions: readonly ["atc", "acutc"];
    };
    readonly "application/vnd.adobe.air-application-installer-package+zip": {
        readonly source: "apache";
        readonly extensions: readonly ["air"];
    };
    readonly "application/vnd.adobe.formscentral.fcdt": {
        readonly source: "iana";
        readonly extensions: readonly ["fcdt"];
    };
    readonly "application/vnd.adobe.fxp": {
        readonly source: "iana";
        readonly extensions: readonly ["fxp", "fxpl"];
    };
    readonly "application/vnd.adobe.xdp+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["xdp"];
    };
    readonly "application/vnd.adobe.xfdf": {
        readonly source: "iana";
        readonly extensions: readonly ["xfdf"];
    };
    readonly "application/vnd.age": {
        readonly source: "iana";
        readonly extensions: readonly ["age"];
    };
    readonly "application/vnd.ahead.space": {
        readonly source: "iana";
        readonly extensions: readonly ["ahead"];
    };
    readonly "application/vnd.airzip.filesecure.azf": {
        readonly source: "iana";
        readonly extensions: readonly ["azf"];
    };
    readonly "application/vnd.airzip.filesecure.azs": {
        readonly source: "iana";
        readonly extensions: readonly ["azs"];
    };
    readonly "application/vnd.amazon.ebook": {
        readonly source: "apache";
        readonly extensions: readonly ["azw"];
    };
    readonly "application/vnd.americandynamics.acc": {
        readonly source: "iana";
        readonly extensions: readonly ["acc"];
    };
    readonly "application/vnd.amiga.ami": {
        readonly source: "iana";
        readonly extensions: readonly ["ami"];
    };
    readonly "application/vnd.android.package-archive": {
        readonly source: "apache";
        readonly extensions: readonly ["apk"];
    };
    readonly "application/vnd.anser-web-certificate-issue-initiation": {
        readonly source: "iana";
        readonly extensions: readonly ["cii"];
    };
    readonly "application/vnd.anser-web-funds-transfer-initiation": {
        readonly source: "apache";
        readonly extensions: readonly ["fti"];
    };
    readonly "application/vnd.antix.game-component": {
        readonly source: "iana";
        readonly extensions: readonly ["atx"];
    };
    readonly "application/vnd.apple.installer+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["mpkg"];
    };
    readonly "application/vnd.apple.keynote": {
        readonly source: "iana";
        readonly extensions: readonly ["key"];
    };
    readonly "application/vnd.apple.mpegurl": {
        readonly source: "iana";
        readonly extensions: readonly ["m3u8"];
    };
    readonly "application/vnd.apple.numbers": {
        readonly source: "iana";
        readonly extensions: readonly ["numbers"];
    };
    readonly "application/vnd.apple.pages": {
        readonly source: "iana";
        readonly extensions: readonly ["pages"];
    };
    readonly "application/vnd.aristanetworks.swi": {
        readonly source: "iana";
        readonly extensions: readonly ["swi"];
    };
    readonly "application/vnd.astraea-software.iota": {
        readonly source: "iana";
        readonly extensions: readonly ["iota"];
    };
    readonly "application/vnd.audiograph": {
        readonly source: "iana";
        readonly extensions: readonly ["aep"];
    };
    readonly "application/vnd.balsamiq.bmml+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["bmml"];
    };
    readonly "application/vnd.blueice.multipass": {
        readonly source: "iana";
        readonly extensions: readonly ["mpm"];
    };
    readonly "application/vnd.bmi": {
        readonly source: "iana";
        readonly extensions: readonly ["bmi"];
    };
    readonly "application/vnd.businessobjects": {
        readonly source: "iana";
        readonly extensions: readonly ["rep"];
    };
    readonly "application/vnd.chemdraw+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["cdxml"];
    };
    readonly "application/vnd.chipnuts.karaoke-mmd": {
        readonly source: "iana";
        readonly extensions: readonly ["mmd"];
    };
    readonly "application/vnd.cinderella": {
        readonly source: "iana";
        readonly extensions: readonly ["cdy"];
    };
    readonly "application/vnd.citationstyles.style+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["csl"];
    };
    readonly "application/vnd.claymore": {
        readonly source: "iana";
        readonly extensions: readonly ["cla"];
    };
    readonly "application/vnd.cloanto.rp9": {
        readonly source: "iana";
        readonly extensions: readonly ["rp9"];
    };
    readonly "application/vnd.clonk.c4group": {
        readonly source: "iana";
        readonly extensions: readonly ["c4g", "c4d", "c4f", "c4p", "c4u"];
    };
    readonly "application/vnd.cluetrust.cartomobile-config": {
        readonly source: "iana";
        readonly extensions: readonly ["c11amc"];
    };
    readonly "application/vnd.cluetrust.cartomobile-config-pkg": {
        readonly source: "iana";
        readonly extensions: readonly ["c11amz"];
    };
    readonly "application/vnd.commonspace": {
        readonly source: "iana";
        readonly extensions: readonly ["csp"];
    };
    readonly "application/vnd.contact.cmsg": {
        readonly source: "iana";
        readonly extensions: readonly ["cdbcmsg"];
    };
    readonly "application/vnd.cosmocaller": {
        readonly source: "iana";
        readonly extensions: readonly ["cmc"];
    };
    readonly "application/vnd.crick.clicker": {
        readonly source: "iana";
        readonly extensions: readonly ["clkx"];
    };
    readonly "application/vnd.crick.clicker.keyboard": {
        readonly source: "iana";
        readonly extensions: readonly ["clkk"];
    };
    readonly "application/vnd.crick.clicker.palette": {
        readonly source: "iana";
        readonly extensions: readonly ["clkp"];
    };
    readonly "application/vnd.crick.clicker.template": {
        readonly source: "iana";
        readonly extensions: readonly ["clkt"];
    };
    readonly "application/vnd.crick.clicker.wordbank": {
        readonly source: "iana";
        readonly extensions: readonly ["clkw"];
    };
    readonly "application/vnd.criticaltools.wbs+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["wbs"];
    };
    readonly "application/vnd.ctc-posml": {
        readonly source: "iana";
        readonly extensions: readonly ["pml"];
    };
    readonly "application/vnd.cups-ppd": {
        readonly source: "iana";
        readonly extensions: readonly ["ppd"];
    };
    readonly "application/vnd.curl.car": {
        readonly source: "apache";
        readonly extensions: readonly ["car"];
    };
    readonly "application/vnd.curl.pcurl": {
        readonly source: "apache";
        readonly extensions: readonly ["pcurl"];
    };
    readonly "application/vnd.dart": {
        readonly source: "iana";
        readonly extensions: readonly ["dart"];
    };
    readonly "application/vnd.data-vision.rdz": {
        readonly source: "iana";
        readonly extensions: readonly ["rdz"];
    };
    readonly "application/vnd.dbf": {
        readonly source: "iana";
        readonly extensions: readonly ["dbf"];
    };
    readonly "application/vnd.dece.data": {
        readonly source: "iana";
        readonly extensions: readonly ["uvf", "uvvf", "uvd", "uvvd"];
    };
    readonly "application/vnd.dece.ttml+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["uvt", "uvvt"];
    };
    readonly "application/vnd.dece.unspecified": {
        readonly source: "iana";
        readonly extensions: readonly ["uvx", "uvvx"];
    };
    readonly "application/vnd.dece.zip": {
        readonly source: "iana";
        readonly extensions: readonly ["uvz", "uvvz"];
    };
    readonly "application/vnd.denovo.fcselayout-link": {
        readonly source: "iana";
        readonly extensions: readonly ["fe_launch"];
    };
    readonly "application/vnd.dna": {
        readonly source: "iana";
        readonly extensions: readonly ["dna"];
    };
    readonly "application/vnd.dolby.mlp": {
        readonly source: "apache";
        readonly extensions: readonly ["mlp"];
    };
    readonly "application/vnd.dpgraph": {
        readonly source: "iana";
        readonly extensions: readonly ["dpg"];
    };
    readonly "application/vnd.dreamfactory": {
        readonly source: "iana";
        readonly extensions: readonly ["dfac"];
    };
    readonly "application/vnd.ds-keypoint": {
        readonly source: "apache";
        readonly extensions: readonly ["kpxx"];
    };
    readonly "application/vnd.dvb.ait": {
        readonly source: "iana";
        readonly extensions: readonly ["ait"];
    };
    readonly "application/vnd.dvb.service": {
        readonly source: "iana";
        readonly extensions: readonly ["svc"];
    };
    readonly "application/vnd.dynageo": {
        readonly source: "iana";
        readonly extensions: readonly ["geo"];
    };
    readonly "application/vnd.ecowin.chart": {
        readonly source: "iana";
        readonly extensions: readonly ["mag"];
    };
    readonly "application/vnd.enliven": {
        readonly source: "iana";
        readonly extensions: readonly ["nml"];
    };
    readonly "application/vnd.epson.esf": {
        readonly source: "iana";
        readonly extensions: readonly ["esf"];
    };
    readonly "application/vnd.epson.msf": {
        readonly source: "iana";
        readonly extensions: readonly ["msf"];
    };
    readonly "application/vnd.epson.quickanime": {
        readonly source: "iana";
        readonly extensions: readonly ["qam"];
    };
    readonly "application/vnd.epson.salt": {
        readonly source: "iana";
        readonly extensions: readonly ["slt"];
    };
    readonly "application/vnd.epson.ssf": {
        readonly source: "iana";
        readonly extensions: readonly ["ssf"];
    };
    readonly "application/vnd.eszigno3+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["es3", "et3"];
    };
    readonly "application/vnd.ezpix-album": {
        readonly source: "iana";
        readonly extensions: readonly ["ez2"];
    };
    readonly "application/vnd.ezpix-package": {
        readonly source: "iana";
        readonly extensions: readonly ["ez3"];
    };
    readonly "application/vnd.fdf": {
        readonly source: "iana";
        readonly extensions: readonly ["fdf"];
    };
    readonly "application/vnd.fdsn.mseed": {
        readonly source: "iana";
        readonly extensions: readonly ["mseed"];
    };
    readonly "application/vnd.fdsn.seed": {
        readonly source: "iana";
        readonly extensions: readonly ["seed", "dataless"];
    };
    readonly "application/vnd.flographit": {
        readonly source: "iana";
        readonly extensions: readonly ["gph"];
    };
    readonly "application/vnd.fluxtime.clip": {
        readonly source: "iana";
        readonly extensions: readonly ["ftc"];
    };
    readonly "application/vnd.framemaker": {
        readonly source: "iana";
        readonly extensions: readonly ["fm", "frame", "maker", "book"];
    };
    readonly "application/vnd.frogans.fnc": {
        readonly source: "iana";
        readonly extensions: readonly ["fnc"];
    };
    readonly "application/vnd.frogans.ltf": {
        readonly source: "iana";
        readonly extensions: readonly ["ltf"];
    };
    readonly "application/vnd.fsc.weblaunch": {
        readonly source: "iana";
        readonly extensions: readonly ["fsc"];
    };
    readonly "application/vnd.fujitsu.oasys": {
        readonly source: "iana";
        readonly extensions: readonly ["oas"];
    };
    readonly "application/vnd.fujitsu.oasys2": {
        readonly source: "iana";
        readonly extensions: readonly ["oa2"];
    };
    readonly "application/vnd.fujitsu.oasys3": {
        readonly source: "iana";
        readonly extensions: readonly ["oa3"];
    };
    readonly "application/vnd.fujitsu.oasysgp": {
        readonly source: "iana";
        readonly extensions: readonly ["fg5"];
    };
    readonly "application/vnd.fujitsu.oasysprs": {
        readonly source: "iana";
        readonly extensions: readonly ["bh2"];
    };
    readonly "application/vnd.fujixerox.ddd": {
        readonly source: "iana";
        readonly extensions: readonly ["ddd"];
    };
    readonly "application/vnd.fujixerox.docuworks": {
        readonly source: "iana";
        readonly extensions: readonly ["xdw"];
    };
    readonly "application/vnd.fujixerox.docuworks.binder": {
        readonly source: "iana";
        readonly extensions: readonly ["xbd"];
    };
    readonly "application/vnd.fuzzysheet": {
        readonly source: "iana";
        readonly extensions: readonly ["fzs"];
    };
    readonly "application/vnd.genomatix.tuxedo": {
        readonly source: "iana";
        readonly extensions: readonly ["txd"];
    };
    readonly "application/vnd.geogebra.file": {
        readonly source: "iana";
        readonly extensions: readonly ["ggb"];
    };
    readonly "application/vnd.geogebra.tool": {
        readonly source: "iana";
        readonly extensions: readonly ["ggt"];
    };
    readonly "application/vnd.geometry-explorer": {
        readonly source: "iana";
        readonly extensions: readonly ["gex", "gre"];
    };
    readonly "application/vnd.geonext": {
        readonly source: "iana";
        readonly extensions: readonly ["gxt"];
    };
    readonly "application/vnd.geoplan": {
        readonly source: "iana";
        readonly extensions: readonly ["g2w"];
    };
    readonly "application/vnd.geospace": {
        readonly source: "iana";
        readonly extensions: readonly ["g3w"];
    };
    readonly "application/vnd.gmx": {
        readonly source: "iana";
        readonly extensions: readonly ["gmx"];
    };
    readonly "application/vnd.google-earth.kml+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["kml"];
    };
    readonly "application/vnd.google-earth.kmz": {
        readonly source: "iana";
        readonly extensions: readonly ["kmz"];
    };
    readonly "application/vnd.grafeq": {
        readonly source: "iana";
        readonly extensions: readonly ["gqf", "gqs"];
    };
    readonly "application/vnd.groove-account": {
        readonly source: "iana";
        readonly extensions: readonly ["gac"];
    };
    readonly "application/vnd.groove-help": {
        readonly source: "iana";
        readonly extensions: readonly ["ghf"];
    };
    readonly "application/vnd.groove-identity-message": {
        readonly source: "iana";
        readonly extensions: readonly ["gim"];
    };
    readonly "application/vnd.groove-injector": {
        readonly source: "iana";
        readonly extensions: readonly ["grv"];
    };
    readonly "application/vnd.groove-tool-message": {
        readonly source: "iana";
        readonly extensions: readonly ["gtm"];
    };
    readonly "application/vnd.groove-tool-template": {
        readonly source: "iana";
        readonly extensions: readonly ["tpl"];
    };
    readonly "application/vnd.groove-vcard": {
        readonly source: "iana";
        readonly extensions: readonly ["vcg"];
    };
    readonly "application/vnd.hal+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["hal"];
    };
    readonly "application/vnd.handheld-entertainment+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["zmm"];
    };
    readonly "application/vnd.hbci": {
        readonly source: "iana";
        readonly extensions: readonly ["hbci"];
    };
    readonly "application/vnd.hhe.lesson-player": {
        readonly source: "iana";
        readonly extensions: readonly ["les"];
    };
    readonly "application/vnd.hp-hpgl": {
        readonly source: "iana";
        readonly extensions: readonly ["hpgl"];
    };
    readonly "application/vnd.hp-hpid": {
        readonly source: "iana";
        readonly extensions: readonly ["hpid"];
    };
    readonly "application/vnd.hp-hps": {
        readonly source: "iana";
        readonly extensions: readonly ["hps"];
    };
    readonly "application/vnd.hp-jlyt": {
        readonly source: "iana";
        readonly extensions: readonly ["jlt"];
    };
    readonly "application/vnd.hp-pcl": {
        readonly source: "iana";
        readonly extensions: readonly ["pcl"];
    };
    readonly "application/vnd.hp-pclxl": {
        readonly source: "iana";
        readonly extensions: readonly ["pclxl"];
    };
    readonly "application/vnd.hydrostatix.sof-data": {
        readonly source: "iana";
        readonly extensions: readonly ["sfd-hdstx"];
    };
    readonly "application/vnd.ibm.minipay": {
        readonly source: "iana";
        readonly extensions: readonly ["mpy"];
    };
    readonly "application/vnd.ibm.modcap": {
        readonly source: "iana";
        readonly extensions: readonly ["afp", "listafp", "list3820"];
    };
    readonly "application/vnd.ibm.rights-management": {
        readonly source: "iana";
        readonly extensions: readonly ["irm"];
    };
    readonly "application/vnd.ibm.secure-container": {
        readonly source: "iana";
        readonly extensions: readonly ["sc"];
    };
    readonly "application/vnd.iccprofile": {
        readonly source: "iana";
        readonly extensions: readonly ["icc", "icm"];
    };
    readonly "application/vnd.igloader": {
        readonly source: "iana";
        readonly extensions: readonly ["igl"];
    };
    readonly "application/vnd.immervision-ivp": {
        readonly source: "iana";
        readonly extensions: readonly ["ivp"];
    };
    readonly "application/vnd.immervision-ivu": {
        readonly source: "iana";
        readonly extensions: readonly ["ivu"];
    };
    readonly "application/vnd.insors.igm": {
        readonly source: "iana";
        readonly extensions: readonly ["igm"];
    };
    readonly "application/vnd.intercon.formnet": {
        readonly source: "iana";
        readonly extensions: readonly ["xpw", "xpx"];
    };
    readonly "application/vnd.intergeo": {
        readonly source: "iana";
        readonly extensions: readonly ["i2g"];
    };
    readonly "application/vnd.intu.qbo": {
        readonly source: "iana";
        readonly extensions: readonly ["qbo"];
    };
    readonly "application/vnd.intu.qfx": {
        readonly source: "iana";
        readonly extensions: readonly ["qfx"];
    };
    readonly "application/vnd.ipunplugged.rcprofile": {
        readonly source: "iana";
        readonly extensions: readonly ["rcprofile"];
    };
    readonly "application/vnd.irepository.package+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["irp"];
    };
    readonly "application/vnd.is-xpr": {
        readonly source: "iana";
        readonly extensions: readonly ["xpr"];
    };
    readonly "application/vnd.isac.fcs": {
        readonly source: "iana";
        readonly extensions: readonly ["fcs"];
    };
    readonly "application/vnd.jam": {
        readonly source: "iana";
        readonly extensions: readonly ["jam"];
    };
    readonly "application/vnd.jcp.javame.midlet-rms": {
        readonly source: "iana";
        readonly extensions: readonly ["rms"];
    };
    readonly "application/vnd.jisp": {
        readonly source: "iana";
        readonly extensions: readonly ["jisp"];
    };
    readonly "application/vnd.joost.joda-archive": {
        readonly source: "iana";
        readonly extensions: readonly ["joda"];
    };
    readonly "application/vnd.kahootz": {
        readonly source: "iana";
        readonly extensions: readonly ["ktz", "ktr"];
    };
    readonly "application/vnd.kde.karbon": {
        readonly source: "iana";
        readonly extensions: readonly ["karbon"];
    };
    readonly "application/vnd.kde.kchart": {
        readonly source: "iana";
        readonly extensions: readonly ["chrt"];
    };
    readonly "application/vnd.kde.kformula": {
        readonly source: "iana";
        readonly extensions: readonly ["kfo"];
    };
    readonly "application/vnd.kde.kivio": {
        readonly source: "iana";
        readonly extensions: readonly ["flw"];
    };
    readonly "application/vnd.kde.kontour": {
        readonly source: "iana";
        readonly extensions: readonly ["kon"];
    };
    readonly "application/vnd.kde.kpresenter": {
        readonly source: "iana";
        readonly extensions: readonly ["kpr", "kpt"];
    };
    readonly "application/vnd.kde.kspread": {
        readonly source: "iana";
        readonly extensions: readonly ["ksp"];
    };
    readonly "application/vnd.kde.kword": {
        readonly source: "iana";
        readonly extensions: readonly ["kwd", "kwt"];
    };
    readonly "application/vnd.kenameaapp": {
        readonly source: "iana";
        readonly extensions: readonly ["htke"];
    };
    readonly "application/vnd.kidspiration": {
        readonly source: "iana";
        readonly extensions: readonly ["kia"];
    };
    readonly "application/vnd.kinar": {
        readonly source: "iana";
        readonly extensions: readonly ["kne", "knp"];
    };
    readonly "application/vnd.koan": {
        readonly source: "iana";
        readonly extensions: readonly ["skp", "skd", "skt", "skm"];
    };
    readonly "application/vnd.kodak-descriptor": {
        readonly source: "iana";
        readonly extensions: readonly ["sse"];
    };
    readonly "application/vnd.las.las+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["lasxml"];
    };
    readonly "application/vnd.llamagraphics.life-balance.desktop": {
        readonly source: "iana";
        readonly extensions: readonly ["lbd"];
    };
    readonly "application/vnd.llamagraphics.life-balance.exchange+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["lbe"];
    };
    readonly "application/vnd.lotus-1-2-3": {
        readonly source: "iana";
        readonly extensions: readonly ["123"];
    };
    readonly "application/vnd.lotus-approach": {
        readonly source: "iana";
        readonly extensions: readonly ["apr"];
    };
    readonly "application/vnd.lotus-freelance": {
        readonly source: "iana";
        readonly extensions: readonly ["pre"];
    };
    readonly "application/vnd.lotus-notes": {
        readonly source: "iana";
        readonly extensions: readonly ["nsf"];
    };
    readonly "application/vnd.lotus-organizer": {
        readonly source: "iana";
        readonly extensions: readonly ["org"];
    };
    readonly "application/vnd.lotus-screencam": {
        readonly source: "iana";
        readonly extensions: readonly ["scm"];
    };
    readonly "application/vnd.lotus-wordpro": {
        readonly source: "iana";
        readonly extensions: readonly ["lwp"];
    };
    readonly "application/vnd.macports.portpkg": {
        readonly source: "iana";
        readonly extensions: readonly ["portpkg"];
    };
    readonly "application/vnd.mapbox-vector-tile": {
        readonly source: "iana";
        readonly extensions: readonly ["mvt"];
    };
    readonly "application/vnd.mcd": {
        readonly source: "iana";
        readonly extensions: readonly ["mcd"];
    };
    readonly "application/vnd.medcalcdata": {
        readonly source: "iana";
        readonly extensions: readonly ["mc1"];
    };
    readonly "application/vnd.mediastation.cdkey": {
        readonly source: "iana";
        readonly extensions: readonly ["cdkey"];
    };
    readonly "application/vnd.mfer": {
        readonly source: "iana";
        readonly extensions: readonly ["mwf"];
    };
    readonly "application/vnd.mfmp": {
        readonly source: "iana";
        readonly extensions: readonly ["mfm"];
    };
    readonly "application/vnd.micrografx.flo": {
        readonly source: "iana";
        readonly extensions: readonly ["flo"];
    };
    readonly "application/vnd.micrografx.igx": {
        readonly source: "iana";
        readonly extensions: readonly ["igx"];
    };
    readonly "application/vnd.mif": {
        readonly source: "iana";
        readonly extensions: readonly ["mif"];
    };
    readonly "application/vnd.mobius.daf": {
        readonly source: "iana";
        readonly extensions: readonly ["daf"];
    };
    readonly "application/vnd.mobius.dis": {
        readonly source: "iana";
        readonly extensions: readonly ["dis"];
    };
    readonly "application/vnd.mobius.mbk": {
        readonly source: "iana";
        readonly extensions: readonly ["mbk"];
    };
    readonly "application/vnd.mobius.mqy": {
        readonly source: "iana";
        readonly extensions: readonly ["mqy"];
    };
    readonly "application/vnd.mobius.msl": {
        readonly source: "iana";
        readonly extensions: readonly ["msl"];
    };
    readonly "application/vnd.mobius.plc": {
        readonly source: "iana";
        readonly extensions: readonly ["plc"];
    };
    readonly "application/vnd.mobius.txf": {
        readonly source: "iana";
        readonly extensions: readonly ["txf"];
    };
    readonly "application/vnd.mophun.application": {
        readonly source: "iana";
        readonly extensions: readonly ["mpn"];
    };
    readonly "application/vnd.mophun.certificate": {
        readonly source: "iana";
        readonly extensions: readonly ["mpc"];
    };
    readonly "application/vnd.mozilla.xul+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["xul"];
    };
    readonly "application/vnd.ms-artgalry": {
        readonly source: "iana";
        readonly extensions: readonly ["cil"];
    };
    readonly "application/vnd.ms-cab-compressed": {
        readonly source: "iana";
        readonly extensions: readonly ["cab"];
    };
    readonly "application/vnd.ms-excel": {
        readonly source: "iana";
        readonly extensions: readonly ["xls", "xlm", "xla", "xlc", "xlt", "xlw"];
    };
    readonly "application/vnd.ms-excel.addin.macroenabled.12": {
        readonly source: "iana";
        readonly extensions: readonly ["xlam"];
    };
    readonly "application/vnd.ms-excel.sheet.binary.macroenabled.12": {
        readonly source: "iana";
        readonly extensions: readonly ["xlsb"];
    };
    readonly "application/vnd.ms-excel.sheet.macroenabled.12": {
        readonly source: "iana";
        readonly extensions: readonly ["xlsm"];
    };
    readonly "application/vnd.ms-excel.template.macroenabled.12": {
        readonly source: "iana";
        readonly extensions: readonly ["xltm"];
    };
    readonly "application/vnd.ms-fontobject": {
        readonly source: "iana";
        readonly extensions: readonly ["eot"];
    };
    readonly "application/vnd.ms-htmlhelp": {
        readonly source: "iana";
        readonly extensions: readonly ["chm"];
    };
    readonly "application/vnd.ms-ims": {
        readonly source: "iana";
        readonly extensions: readonly ["ims"];
    };
    readonly "application/vnd.ms-lrm": {
        readonly source: "iana";
        readonly extensions: readonly ["lrm"];
    };
    readonly "application/vnd.ms-officetheme": {
        readonly source: "iana";
        readonly extensions: readonly ["thmx"];
    };
    readonly "application/vnd.ms-pki.seccat": {
        readonly source: "apache";
        readonly extensions: readonly ["cat"];
    };
    readonly "application/vnd.ms-pki.stl": {
        readonly source: "apache";
        readonly extensions: readonly ["stl"];
    };
    readonly "application/vnd.ms-powerpoint": {
        readonly source: "iana";
        readonly extensions: readonly ["ppt", "pps", "pot"];
    };
    readonly "application/vnd.ms-powerpoint.addin.macroenabled.12": {
        readonly source: "iana";
        readonly extensions: readonly ["ppam"];
    };
    readonly "application/vnd.ms-powerpoint.presentation.macroenabled.12": {
        readonly source: "iana";
        readonly extensions: readonly ["pptm"];
    };
    readonly "application/vnd.ms-powerpoint.slide.macroenabled.12": {
        readonly source: "iana";
        readonly extensions: readonly ["sldm"];
    };
    readonly "application/vnd.ms-powerpoint.slideshow.macroenabled.12": {
        readonly source: "iana";
        readonly extensions: readonly ["ppsm"];
    };
    readonly "application/vnd.ms-powerpoint.template.macroenabled.12": {
        readonly source: "iana";
        readonly extensions: readonly ["potm"];
    };
    readonly "application/vnd.ms-project": {
        readonly source: "iana";
        readonly extensions: readonly ["mpp", "mpt"];
    };
    readonly "application/vnd.ms-word.document.macroenabled.12": {
        readonly source: "iana";
        readonly extensions: readonly ["docm"];
    };
    readonly "application/vnd.ms-word.template.macroenabled.12": {
        readonly source: "iana";
        readonly extensions: readonly ["dotm"];
    };
    readonly "application/vnd.ms-works": {
        readonly source: "iana";
        readonly extensions: readonly ["wps", "wks", "wcm", "wdb"];
    };
    readonly "application/vnd.ms-wpl": {
        readonly source: "iana";
        readonly extensions: readonly ["wpl"];
    };
    readonly "application/vnd.ms-xpsdocument": {
        readonly source: "iana";
        readonly extensions: readonly ["xps"];
    };
    readonly "application/vnd.mseq": {
        readonly source: "iana";
        readonly extensions: readonly ["mseq"];
    };
    readonly "application/vnd.musician": {
        readonly source: "iana";
        readonly extensions: readonly ["mus"];
    };
    readonly "application/vnd.muvee.style": {
        readonly source: "iana";
        readonly extensions: readonly ["msty"];
    };
    readonly "application/vnd.mynfc": {
        readonly source: "iana";
        readonly extensions: readonly ["taglet"];
    };
    readonly "application/vnd.neurolanguage.nlu": {
        readonly source: "iana";
        readonly extensions: readonly ["nlu"];
    };
    readonly "application/vnd.nitf": {
        readonly source: "iana";
        readonly extensions: readonly ["ntf", "nitf"];
    };
    readonly "application/vnd.noblenet-directory": {
        readonly source: "iana";
        readonly extensions: readonly ["nnd"];
    };
    readonly "application/vnd.noblenet-sealer": {
        readonly source: "iana";
        readonly extensions: readonly ["nns"];
    };
    readonly "application/vnd.noblenet-web": {
        readonly source: "iana";
        readonly extensions: readonly ["nnw"];
    };
    readonly "application/vnd.nokia.n-gage.ac+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["ac"];
    };
    readonly "application/vnd.nokia.n-gage.data": {
        readonly source: "iana";
        readonly extensions: readonly ["ngdat"];
    };
    readonly "application/vnd.nokia.n-gage.symbian.install": {
        readonly source: "iana";
        readonly extensions: readonly ["n-gage"];
    };
    readonly "application/vnd.nokia.radio-preset": {
        readonly source: "iana";
        readonly extensions: readonly ["rpst"];
    };
    readonly "application/vnd.nokia.radio-presets": {
        readonly source: "iana";
        readonly extensions: readonly ["rpss"];
    };
    readonly "application/vnd.novadigm.edm": {
        readonly source: "iana";
        readonly extensions: readonly ["edm"];
    };
    readonly "application/vnd.novadigm.edx": {
        readonly source: "iana";
        readonly extensions: readonly ["edx"];
    };
    readonly "application/vnd.novadigm.ext": {
        readonly source: "iana";
        readonly extensions: readonly ["ext"];
    };
    readonly "application/vnd.oasis.opendocument.chart": {
        readonly source: "iana";
        readonly extensions: readonly ["odc"];
    };
    readonly "application/vnd.oasis.opendocument.chart-template": {
        readonly source: "iana";
        readonly extensions: readonly ["otc"];
    };
    readonly "application/vnd.oasis.opendocument.database": {
        readonly source: "iana";
        readonly extensions: readonly ["odb"];
    };
    readonly "application/vnd.oasis.opendocument.formula": {
        readonly source: "iana";
        readonly extensions: readonly ["odf"];
    };
    readonly "application/vnd.oasis.opendocument.formula-template": {
        readonly source: "iana";
        readonly extensions: readonly ["odft"];
    };
    readonly "application/vnd.oasis.opendocument.graphics": {
        readonly source: "iana";
        readonly extensions: readonly ["odg"];
    };
    readonly "application/vnd.oasis.opendocument.graphics-template": {
        readonly source: "iana";
        readonly extensions: readonly ["otg"];
    };
    readonly "application/vnd.oasis.opendocument.image": {
        readonly source: "iana";
        readonly extensions: readonly ["odi"];
    };
    readonly "application/vnd.oasis.opendocument.image-template": {
        readonly source: "iana";
        readonly extensions: readonly ["oti"];
    };
    readonly "application/vnd.oasis.opendocument.presentation": {
        readonly source: "iana";
        readonly extensions: readonly ["odp"];
    };
    readonly "application/vnd.oasis.opendocument.presentation-template": {
        readonly source: "iana";
        readonly extensions: readonly ["otp"];
    };
    readonly "application/vnd.oasis.opendocument.spreadsheet": {
        readonly source: "iana";
        readonly extensions: readonly ["ods"];
    };
    readonly "application/vnd.oasis.opendocument.spreadsheet-template": {
        readonly source: "iana";
        readonly extensions: readonly ["ots"];
    };
    readonly "application/vnd.oasis.opendocument.text": {
        readonly source: "iana";
        readonly extensions: readonly ["odt"];
    };
    readonly "application/vnd.oasis.opendocument.text-master": {
        readonly source: "iana";
        readonly extensions: readonly ["odm"];
    };
    readonly "application/vnd.oasis.opendocument.text-template": {
        readonly source: "iana";
        readonly extensions: readonly ["ott"];
    };
    readonly "application/vnd.oasis.opendocument.text-web": {
        readonly source: "iana";
        readonly extensions: readonly ["oth"];
    };
    readonly "application/vnd.olpc-sugar": {
        readonly source: "iana";
        readonly extensions: readonly ["xo"];
    };
    readonly "application/vnd.oma.dd2+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["dd2"];
    };
    readonly "application/vnd.openblox.game+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["obgx"];
    };
    readonly "application/vnd.openofficeorg.extension": {
        readonly source: "apache";
        readonly extensions: readonly ["oxt"];
    };
    readonly "application/vnd.openstreetmap.data+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["osm"];
    };
    readonly "application/vnd.openxmlformats-officedocument.presentationml.presentation": {
        readonly source: "iana";
        readonly extensions: readonly ["pptx"];
    };
    readonly "application/vnd.openxmlformats-officedocument.presentationml.slide": {
        readonly source: "iana";
        readonly extensions: readonly ["sldx"];
    };
    readonly "application/vnd.openxmlformats-officedocument.presentationml.slideshow": {
        readonly source: "iana";
        readonly extensions: readonly ["ppsx"];
    };
    readonly "application/vnd.openxmlformats-officedocument.presentationml.template": {
        readonly source: "iana";
        readonly extensions: readonly ["potx"];
    };
    readonly "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": {
        readonly source: "iana";
        readonly extensions: readonly ["xlsx"];
    };
    readonly "application/vnd.openxmlformats-officedocument.spreadsheetml.template": {
        readonly source: "iana";
        readonly extensions: readonly ["xltx"];
    };
    readonly "application/vnd.openxmlformats-officedocument.wordprocessingml.document": {
        readonly source: "iana";
        readonly extensions: readonly ["docx"];
    };
    readonly "application/vnd.openxmlformats-officedocument.wordprocessingml.template": {
        readonly source: "iana";
        readonly extensions: readonly ["dotx"];
    };
    readonly "application/vnd.osgeo.mapguide.package": {
        readonly source: "iana";
        readonly extensions: readonly ["mgp"];
    };
    readonly "application/vnd.osgi.dp": {
        readonly source: "iana";
        readonly extensions: readonly ["dp"];
    };
    readonly "application/vnd.osgi.subsystem": {
        readonly source: "iana";
        readonly extensions: readonly ["esa"];
    };
    readonly "application/vnd.palm": {
        readonly source: "iana";
        readonly extensions: readonly ["pdb", "pqa", "oprc"];
    };
    readonly "application/vnd.pawaafile": {
        readonly source: "iana";
        readonly extensions: readonly ["paw"];
    };
    readonly "application/vnd.pg.format": {
        readonly source: "iana";
        readonly extensions: readonly ["str"];
    };
    readonly "application/vnd.pg.osasli": {
        readonly source: "iana";
        readonly extensions: readonly ["ei6"];
    };
    readonly "application/vnd.picsel": {
        readonly source: "iana";
        readonly extensions: readonly ["efif"];
    };
    readonly "application/vnd.pmi.widget": {
        readonly source: "iana";
        readonly extensions: readonly ["wg"];
    };
    readonly "application/vnd.pocketlearn": {
        readonly source: "iana";
        readonly extensions: readonly ["plf"];
    };
    readonly "application/vnd.powerbuilder6": {
        readonly source: "iana";
        readonly extensions: readonly ["pbd"];
    };
    readonly "application/vnd.previewsystems.box": {
        readonly source: "iana";
        readonly extensions: readonly ["box"];
    };
    readonly "application/vnd.proteus.magazine": {
        readonly source: "iana";
        readonly extensions: readonly ["mgz"];
    };
    readonly "application/vnd.publishare-delta-tree": {
        readonly source: "iana";
        readonly extensions: readonly ["qps"];
    };
    readonly "application/vnd.pvi.ptid1": {
        readonly source: "iana";
        readonly extensions: readonly ["ptid"];
    };
    readonly "application/vnd.quark.quarkxpress": {
        readonly source: "iana";
        readonly extensions: readonly ["qxd", "qxt", "qwd", "qwt", "qxl", "qxb"];
    };
    readonly "application/vnd.rar": {
        readonly source: "iana";
        readonly extensions: readonly ["rar"];
    };
    readonly "application/vnd.realvnc.bed": {
        readonly source: "iana";
        readonly extensions: readonly ["bed"];
    };
    readonly "application/vnd.recordare.musicxml": {
        readonly source: "iana";
        readonly extensions: readonly ["mxl"];
    };
    readonly "application/vnd.recordare.musicxml+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["musicxml"];
    };
    readonly "application/vnd.rig.cryptonote": {
        readonly source: "iana";
        readonly extensions: readonly ["cryptonote"];
    };
    readonly "application/vnd.rim.cod": {
        readonly source: "apache";
        readonly extensions: readonly ["cod"];
    };
    readonly "application/vnd.rn-realmedia": {
        readonly source: "apache";
        readonly extensions: readonly ["rm"];
    };
    readonly "application/vnd.rn-realmedia-vbr": {
        readonly source: "apache";
        readonly extensions: readonly ["rmvb"];
    };
    readonly "application/vnd.route66.link66+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["link66"];
    };
    readonly "application/vnd.sailingtracker.track": {
        readonly source: "iana";
        readonly extensions: readonly ["st"];
    };
    readonly "application/vnd.seemail": {
        readonly source: "iana";
        readonly extensions: readonly ["see"];
    };
    readonly "application/vnd.sema": {
        readonly source: "iana";
        readonly extensions: readonly ["sema"];
    };
    readonly "application/vnd.semd": {
        readonly source: "iana";
        readonly extensions: readonly ["semd"];
    };
    readonly "application/vnd.semf": {
        readonly source: "iana";
        readonly extensions: readonly ["semf"];
    };
    readonly "application/vnd.shana.informed.formdata": {
        readonly source: "iana";
        readonly extensions: readonly ["ifm"];
    };
    readonly "application/vnd.shana.informed.formtemplate": {
        readonly source: "iana";
        readonly extensions: readonly ["itp"];
    };
    readonly "application/vnd.shana.informed.interchange": {
        readonly source: "iana";
        readonly extensions: readonly ["iif"];
    };
    readonly "application/vnd.shana.informed.package": {
        readonly source: "iana";
        readonly extensions: readonly ["ipk"];
    };
    readonly "application/vnd.simtech-mindmapper": {
        readonly source: "iana";
        readonly extensions: readonly ["twd", "twds"];
    };
    readonly "application/vnd.smaf": {
        readonly source: "iana";
        readonly extensions: readonly ["mmf"];
    };
    readonly "application/vnd.smart.teacher": {
        readonly source: "iana";
        readonly extensions: readonly ["teacher"];
    };
    readonly "application/vnd.software602.filler.form+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["fo"];
    };
    readonly "application/vnd.solent.sdkm+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["sdkm", "sdkd"];
    };
    readonly "application/vnd.spotfire.dxp": {
        readonly source: "iana";
        readonly extensions: readonly ["dxp"];
    };
    readonly "application/vnd.spotfire.sfs": {
        readonly source: "iana";
        readonly extensions: readonly ["sfs"];
    };
    readonly "application/vnd.stardivision.calc": {
        readonly source: "apache";
        readonly extensions: readonly ["sdc"];
    };
    readonly "application/vnd.stardivision.draw": {
        readonly source: "apache";
        readonly extensions: readonly ["sda"];
    };
    readonly "application/vnd.stardivision.impress": {
        readonly source: "apache";
        readonly extensions: readonly ["sdd"];
    };
    readonly "application/vnd.stardivision.math": {
        readonly source: "apache";
        readonly extensions: readonly ["smf"];
    };
    readonly "application/vnd.stardivision.writer": {
        readonly source: "apache";
        readonly extensions: readonly ["sdw", "vor"];
    };
    readonly "application/vnd.stardivision.writer-global": {
        readonly source: "apache";
        readonly extensions: readonly ["sgl"];
    };
    readonly "application/vnd.stepmania.package": {
        readonly source: "iana";
        readonly extensions: readonly ["smzip"];
    };
    readonly "application/vnd.stepmania.stepchart": {
        readonly source: "iana";
        readonly extensions: readonly ["sm"];
    };
    readonly "application/vnd.sun.wadl+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["wadl"];
    };
    readonly "application/vnd.sun.xml.calc": {
        readonly source: "apache";
        readonly extensions: readonly ["sxc"];
    };
    readonly "application/vnd.sun.xml.calc.template": {
        readonly source: "apache";
        readonly extensions: readonly ["stc"];
    };
    readonly "application/vnd.sun.xml.draw": {
        readonly source: "apache";
        readonly extensions: readonly ["sxd"];
    };
    readonly "application/vnd.sun.xml.draw.template": {
        readonly source: "apache";
        readonly extensions: readonly ["std"];
    };
    readonly "application/vnd.sun.xml.impress": {
        readonly source: "apache";
        readonly extensions: readonly ["sxi"];
    };
    readonly "application/vnd.sun.xml.impress.template": {
        readonly source: "apache";
        readonly extensions: readonly ["sti"];
    };
    readonly "application/vnd.sun.xml.math": {
        readonly source: "apache";
        readonly extensions: readonly ["sxm"];
    };
    readonly "application/vnd.sun.xml.writer": {
        readonly source: "apache";
        readonly extensions: readonly ["sxw"];
    };
    readonly "application/vnd.sun.xml.writer.global": {
        readonly source: "apache";
        readonly extensions: readonly ["sxg"];
    };
    readonly "application/vnd.sun.xml.writer.template": {
        readonly source: "apache";
        readonly extensions: readonly ["stw"];
    };
    readonly "application/vnd.sus-calendar": {
        readonly source: "iana";
        readonly extensions: readonly ["sus", "susp"];
    };
    readonly "application/vnd.svd": {
        readonly source: "iana";
        readonly extensions: readonly ["svd"];
    };
    readonly "application/vnd.symbian.install": {
        readonly source: "apache";
        readonly extensions: readonly ["sis", "sisx"];
    };
    readonly "application/vnd.syncml+xml": {
        readonly source: "iana";
        readonly charset: "UTF-8";
        readonly extensions: readonly ["xsm"];
    };
    readonly "application/vnd.syncml.dm+wbxml": {
        readonly source: "iana";
        readonly charset: "UTF-8";
        readonly extensions: readonly ["bdm"];
    };
    readonly "application/vnd.syncml.dm+xml": {
        readonly source: "iana";
        readonly charset: "UTF-8";
        readonly extensions: readonly ["xdm"];
    };
    readonly "application/vnd.syncml.dmddf+xml": {
        readonly source: "iana";
        readonly charset: "UTF-8";
        readonly extensions: readonly ["ddf"];
    };
    readonly "application/vnd.tao.intent-module-archive": {
        readonly source: "iana";
        readonly extensions: readonly ["tao"];
    };
    readonly "application/vnd.tcpdump.pcap": {
        readonly source: "iana";
        readonly extensions: readonly ["pcap", "cap", "dmp"];
    };
    readonly "application/vnd.tmobile-livetv": {
        readonly source: "iana";
        readonly extensions: readonly ["tmo"];
    };
    readonly "application/vnd.trid.tpt": {
        readonly source: "iana";
        readonly extensions: readonly ["tpt"];
    };
    readonly "application/vnd.triscape.mxs": {
        readonly source: "iana";
        readonly extensions: readonly ["mxs"];
    };
    readonly "application/vnd.trueapp": {
        readonly source: "iana";
        readonly extensions: readonly ["tra"];
    };
    readonly "application/vnd.ufdl": {
        readonly source: "iana";
        readonly extensions: readonly ["ufd", "ufdl"];
    };
    readonly "application/vnd.uiq.theme": {
        readonly source: "iana";
        readonly extensions: readonly ["utz"];
    };
    readonly "application/vnd.umajin": {
        readonly source: "iana";
        readonly extensions: readonly ["umj"];
    };
    readonly "application/vnd.unity": {
        readonly source: "iana";
        readonly extensions: readonly ["unityweb"];
    };
    readonly "application/vnd.uoml+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["uoml"];
    };
    readonly "application/vnd.vcx": {
        readonly source: "iana";
        readonly extensions: readonly ["vcx"];
    };
    readonly "application/vnd.visio": {
        readonly source: "iana";
        readonly extensions: readonly ["vsd", "vst", "vss", "vsw"];
    };
    readonly "application/vnd.visionary": {
        readonly source: "iana";
        readonly extensions: readonly ["vis"];
    };
    readonly "application/vnd.vsf": {
        readonly source: "iana";
        readonly extensions: readonly ["vsf"];
    };
    readonly "application/vnd.wap.wbxml": {
        readonly source: "iana";
        readonly charset: "UTF-8";
        readonly extensions: readonly ["wbxml"];
    };
    readonly "application/vnd.wap.wmlc": {
        readonly source: "iana";
        readonly extensions: readonly ["wmlc"];
    };
    readonly "application/vnd.wap.wmlscriptc": {
        readonly source: "iana";
        readonly extensions: readonly ["wmlsc"];
    };
    readonly "application/vnd.webturbo": {
        readonly source: "iana";
        readonly extensions: readonly ["wtb"];
    };
    readonly "application/vnd.wolfram.player": {
        readonly source: "iana";
        readonly extensions: readonly ["nbp"];
    };
    readonly "application/vnd.wordperfect": {
        readonly source: "iana";
        readonly extensions: readonly ["wpd"];
    };
    readonly "application/vnd.wqd": {
        readonly source: "iana";
        readonly extensions: readonly ["wqd"];
    };
    readonly "application/vnd.wt.stf": {
        readonly source: "iana";
        readonly extensions: readonly ["stf"];
    };
    readonly "application/vnd.xara": {
        readonly source: "iana";
        readonly extensions: readonly ["xar"];
    };
    readonly "application/vnd.xfdl": {
        readonly source: "iana";
        readonly extensions: readonly ["xfdl"];
    };
    readonly "application/vnd.yamaha.hv-dic": {
        readonly source: "iana";
        readonly extensions: readonly ["hvd"];
    };
    readonly "application/vnd.yamaha.hv-script": {
        readonly source: "iana";
        readonly extensions: readonly ["hvs"];
    };
    readonly "application/vnd.yamaha.hv-voice": {
        readonly source: "iana";
        readonly extensions: readonly ["hvp"];
    };
    readonly "application/vnd.yamaha.openscoreformat": {
        readonly source: "iana";
        readonly extensions: readonly ["osf"];
    };
    readonly "application/vnd.yamaha.openscoreformat.osfpvg+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["osfpvg"];
    };
    readonly "application/vnd.yamaha.smaf-audio": {
        readonly source: "iana";
        readonly extensions: readonly ["saf"];
    };
    readonly "application/vnd.yamaha.smaf-phrase": {
        readonly source: "iana";
        readonly extensions: readonly ["spf"];
    };
    readonly "application/vnd.yellowriver-custom-menu": {
        readonly source: "iana";
        readonly extensions: readonly ["cmp"];
    };
    readonly "application/vnd.zul": {
        readonly source: "iana";
        readonly extensions: readonly ["zir", "zirz"];
    };
    readonly "application/vnd.zzazz.deck+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["zaz"];
    };
    readonly "application/voicexml+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["vxml"];
    };
    readonly "application/wasm": {
        readonly source: "iana";
        readonly extensions: readonly ["wasm"];
    };
    readonly "application/watcherinfo+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["wif"];
    };
    readonly "application/widget": {
        readonly source: "iana";
        readonly extensions: readonly ["wgt"];
    };
    readonly "application/winhlp": {
        readonly source: "apache";
        readonly extensions: readonly ["hlp"];
    };
    readonly "application/wsdl+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["wsdl"];
    };
    readonly "application/wspolicy+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["wspolicy"];
    };
    readonly "application/x-7z-compressed": {
        readonly source: "apache";
        readonly extensions: readonly ["7z"];
    };
    readonly "application/x-abiword": {
        readonly source: "apache";
        readonly extensions: readonly ["abw"];
    };
    readonly "application/x-ace-compressed": {
        readonly source: "apache";
        readonly extensions: readonly ["ace"];
    };
    readonly "application/x-apple-diskimage": {
        readonly source: "apache";
        readonly extensions: readonly ["dmg"];
    };
    readonly "application/x-authorware-bin": {
        readonly source: "apache";
        readonly extensions: readonly ["aab", "x32", "u32", "vox"];
    };
    readonly "application/x-authorware-map": {
        readonly source: "apache";
        readonly extensions: readonly ["aam"];
    };
    readonly "application/x-authorware-seg": {
        readonly source: "apache";
        readonly extensions: readonly ["aas"];
    };
    readonly "application/x-bcpio": {
        readonly source: "apache";
        readonly extensions: readonly ["bcpio"];
    };
    readonly "application/x-bittorrent": {
        readonly source: "apache";
        readonly extensions: readonly ["torrent"];
    };
    readonly "application/x-blorb": {
        readonly source: "apache";
        readonly extensions: readonly ["blb", "blorb"];
    };
    readonly "application/x-bzip": {
        readonly source: "apache";
        readonly extensions: readonly ["bz"];
    };
    readonly "application/x-bzip2": {
        readonly source: "apache";
        readonly extensions: readonly ["bz2", "boz"];
    };
    readonly "application/x-cbr": {
        readonly source: "apache";
        readonly extensions: readonly ["cbr", "cba", "cbt", "cbz", "cb7"];
    };
    readonly "application/x-cdlink": {
        readonly source: "apache";
        readonly extensions: readonly ["vcd"];
    };
    readonly "application/x-cfs-compressed": {
        readonly source: "apache";
        readonly extensions: readonly ["cfs"];
    };
    readonly "application/x-chat": {
        readonly source: "apache";
        readonly extensions: readonly ["chat"];
    };
    readonly "application/x-chess-pgn": {
        readonly source: "apache";
        readonly extensions: readonly ["pgn"];
    };
    readonly "application/x-cocoa": {
        readonly source: "nginx";
        readonly extensions: readonly ["cco"];
    };
    readonly "application/x-conference": {
        readonly source: "apache";
        readonly extensions: readonly ["nsc"];
    };
    readonly "application/x-cpio": {
        readonly source: "apache";
        readonly extensions: readonly ["cpio"];
    };
    readonly "application/x-csh": {
        readonly source: "apache";
        readonly extensions: readonly ["csh"];
    };
    readonly "application/x-debian-package": {
        readonly source: "apache";
        readonly extensions: readonly ["deb", "udeb"];
    };
    readonly "application/x-dgc-compressed": {
        readonly source: "apache";
        readonly extensions: readonly ["dgc"];
    };
    readonly "application/x-director": {
        readonly source: "apache";
        readonly extensions: readonly ["dir", "dcr", "dxr", "cst", "cct", "cxt", "w3d", "fgd", "swa"];
    };
    readonly "application/x-doom": {
        readonly source: "apache";
        readonly extensions: readonly ["wad"];
    };
    readonly "application/x-dtbncx+xml": {
        readonly source: "apache";
        readonly extensions: readonly ["ncx"];
    };
    readonly "application/x-dtbook+xml": {
        readonly source: "apache";
        readonly extensions: readonly ["dtb"];
    };
    readonly "application/x-dtbresource+xml": {
        readonly source: "apache";
        readonly extensions: readonly ["res"];
    };
    readonly "application/x-dvi": {
        readonly source: "apache";
        readonly extensions: readonly ["dvi"];
    };
    readonly "application/x-envoy": {
        readonly source: "apache";
        readonly extensions: readonly ["evy"];
    };
    readonly "application/x-eva": {
        readonly source: "apache";
        readonly extensions: readonly ["eva"];
    };
    readonly "application/x-font-bdf": {
        readonly source: "apache";
        readonly extensions: readonly ["bdf"];
    };
    readonly "application/x-font-ghostscript": {
        readonly source: "apache";
        readonly extensions: readonly ["gsf"];
    };
    readonly "application/x-font-linux-psf": {
        readonly source: "apache";
        readonly extensions: readonly ["psf"];
    };
    readonly "application/x-font-pcf": {
        readonly source: "apache";
        readonly extensions: readonly ["pcf"];
    };
    readonly "application/x-font-snf": {
        readonly source: "apache";
        readonly extensions: readonly ["snf"];
    };
    readonly "application/x-font-type1": {
        readonly source: "apache";
        readonly extensions: readonly ["pfa", "pfb", "pfm", "afm"];
    };
    readonly "application/x-freearc": {
        readonly source: "apache";
        readonly extensions: readonly ["arc"];
    };
    readonly "application/x-futuresplash": {
        readonly source: "apache";
        readonly extensions: readonly ["spl"];
    };
    readonly "application/x-gca-compressed": {
        readonly source: "apache";
        readonly extensions: readonly ["gca"];
    };
    readonly "application/x-glulx": {
        readonly source: "apache";
        readonly extensions: readonly ["ulx"];
    };
    readonly "application/x-gnumeric": {
        readonly source: "apache";
        readonly extensions: readonly ["gnumeric"];
    };
    readonly "application/x-gramps-xml": {
        readonly source: "apache";
        readonly extensions: readonly ["gramps"];
    };
    readonly "application/x-gtar": {
        readonly source: "apache";
        readonly extensions: readonly ["gtar"];
    };
    readonly "application/x-hdf": {
        readonly source: "apache";
        readonly extensions: readonly ["hdf"];
    };
    readonly "application/x-install-instructions": {
        readonly source: "apache";
        readonly extensions: readonly ["install"];
    };
    readonly "application/x-iso9660-image": {
        readonly source: "apache";
        readonly extensions: readonly ["iso"];
    };
    readonly "application/x-java-archive-diff": {
        readonly source: "nginx";
        readonly extensions: readonly ["jardiff"];
    };
    readonly "application/x-java-jnlp-file": {
        readonly source: "apache";
        readonly extensions: readonly ["jnlp"];
    };
    readonly "application/x-latex": {
        readonly source: "apache";
        readonly extensions: readonly ["latex"];
    };
    readonly "application/x-lzh-compressed": {
        readonly source: "apache";
        readonly extensions: readonly ["lzh", "lha"];
    };
    readonly "application/x-makeself": {
        readonly source: "nginx";
        readonly extensions: readonly ["run"];
    };
    readonly "application/x-mie": {
        readonly source: "apache";
        readonly extensions: readonly ["mie"];
    };
    readonly "application/x-mobipocket-ebook": {
        readonly source: "apache";
        readonly extensions: readonly ["prc", "mobi"];
    };
    readonly "application/x-ms-application": {
        readonly source: "apache";
        readonly extensions: readonly ["application"];
    };
    readonly "application/x-ms-shortcut": {
        readonly source: "apache";
        readonly extensions: readonly ["lnk"];
    };
    readonly "application/x-ms-wmd": {
        readonly source: "apache";
        readonly extensions: readonly ["wmd"];
    };
    readonly "application/x-ms-wmz": {
        readonly source: "apache";
        readonly extensions: readonly ["wmz"];
    };
    readonly "application/x-ms-xbap": {
        readonly source: "apache";
        readonly extensions: readonly ["xbap"];
    };
    readonly "application/x-msaccess": {
        readonly source: "apache";
        readonly extensions: readonly ["mdb"];
    };
    readonly "application/x-msbinder": {
        readonly source: "apache";
        readonly extensions: readonly ["obd"];
    };
    readonly "application/x-mscardfile": {
        readonly source: "apache";
        readonly extensions: readonly ["crd"];
    };
    readonly "application/x-msclip": {
        readonly source: "apache";
        readonly extensions: readonly ["clp"];
    };
    readonly "application/x-msdownload": {
        readonly source: "apache";
        readonly extensions: readonly ["exe", "dll", "com", "bat", "msi"];
    };
    readonly "application/x-msmediaview": {
        readonly source: "apache";
        readonly extensions: readonly ["mvb", "m13", "m14"];
    };
    readonly "application/x-msmetafile": {
        readonly source: "apache";
        readonly extensions: readonly ["wmf", "wmz", "emf", "emz"];
    };
    readonly "application/x-msmoney": {
        readonly source: "apache";
        readonly extensions: readonly ["mny"];
    };
    readonly "application/x-mspublisher": {
        readonly source: "apache";
        readonly extensions: readonly ["pub"];
    };
    readonly "application/x-msschedule": {
        readonly source: "apache";
        readonly extensions: readonly ["scd"];
    };
    readonly "application/x-msterminal": {
        readonly source: "apache";
        readonly extensions: readonly ["trm"];
    };
    readonly "application/x-mswrite": {
        readonly source: "apache";
        readonly extensions: readonly ["wri"];
    };
    readonly "application/x-netcdf": {
        readonly source: "apache";
        readonly extensions: readonly ["nc", "cdf"];
    };
    readonly "application/x-nzb": {
        readonly source: "apache";
        readonly extensions: readonly ["nzb"];
    };
    readonly "application/x-perl": {
        readonly source: "nginx";
        readonly extensions: readonly ["pl", "pm"];
    };
    readonly "application/x-pilot": {
        readonly source: "nginx";
        readonly extensions: readonly ["prc", "pdb"];
    };
    readonly "application/x-pkcs12": {
        readonly source: "apache";
        readonly extensions: readonly ["p12", "pfx"];
    };
    readonly "application/x-pkcs7-certificates": {
        readonly source: "apache";
        readonly extensions: readonly ["p7b", "spc"];
    };
    readonly "application/x-pkcs7-certreqresp": {
        readonly source: "apache";
        readonly extensions: readonly ["p7r"];
    };
    readonly "application/x-rar-compressed": {
        readonly source: "apache";
        readonly extensions: readonly ["rar"];
    };
    readonly "application/x-redhat-package-manager": {
        readonly source: "nginx";
        readonly extensions: readonly ["rpm"];
    };
    readonly "application/x-research-info-systems": {
        readonly source: "apache";
        readonly extensions: readonly ["ris"];
    };
    readonly "application/x-sea": {
        readonly source: "nginx";
        readonly extensions: readonly ["sea"];
    };
    readonly "application/x-sh": {
        readonly source: "apache";
        readonly extensions: readonly ["sh"];
    };
    readonly "application/x-shar": {
        readonly source: "apache";
        readonly extensions: readonly ["shar"];
    };
    readonly "application/x-shockwave-flash": {
        readonly source: "apache";
        readonly extensions: readonly ["swf"];
    };
    readonly "application/x-silverlight-app": {
        readonly source: "apache";
        readonly extensions: readonly ["xap"];
    };
    readonly "application/x-sql": {
        readonly source: "apache";
        readonly extensions: readonly ["sql"];
    };
    readonly "application/x-stuffit": {
        readonly source: "apache";
        readonly extensions: readonly ["sit"];
    };
    readonly "application/x-stuffitx": {
        readonly source: "apache";
        readonly extensions: readonly ["sitx"];
    };
    readonly "application/x-subrip": {
        readonly source: "apache";
        readonly extensions: readonly ["srt"];
    };
    readonly "application/x-sv4cpio": {
        readonly source: "apache";
        readonly extensions: readonly ["sv4cpio"];
    };
    readonly "application/x-sv4crc": {
        readonly source: "apache";
        readonly extensions: readonly ["sv4crc"];
    };
    readonly "application/x-t3vm-image": {
        readonly source: "apache";
        readonly extensions: readonly ["t3"];
    };
    readonly "application/x-tads": {
        readonly source: "apache";
        readonly extensions: readonly ["gam"];
    };
    readonly "application/x-tar": {
        readonly source: "apache";
        readonly extensions: readonly ["tar"];
    };
    readonly "application/x-tcl": {
        readonly source: "apache";
        readonly extensions: readonly ["tcl", "tk"];
    };
    readonly "application/x-tex": {
        readonly source: "apache";
        readonly extensions: readonly ["tex"];
    };
    readonly "application/x-tex-tfm": {
        readonly source: "apache";
        readonly extensions: readonly ["tfm"];
    };
    readonly "application/x-texinfo": {
        readonly source: "apache";
        readonly extensions: readonly ["texinfo", "texi"];
    };
    readonly "application/x-tgif": {
        readonly source: "apache";
        readonly extensions: readonly ["obj"];
    };
    readonly "application/x-ustar": {
        readonly source: "apache";
        readonly extensions: readonly ["ustar"];
    };
    readonly "application/x-wais-source": {
        readonly source: "apache";
        readonly extensions: readonly ["src"];
    };
    readonly "application/x-x509-ca-cert": {
        readonly source: "iana";
        readonly extensions: readonly ["der", "crt", "pem"];
    };
    readonly "application/x-xfig": {
        readonly source: "apache";
        readonly extensions: readonly ["fig"];
    };
    readonly "application/x-xliff+xml": {
        readonly source: "apache";
        readonly extensions: readonly ["xlf"];
    };
    readonly "application/x-xpinstall": {
        readonly source: "apache";
        readonly extensions: readonly ["xpi"];
    };
    readonly "application/x-xz": {
        readonly source: "apache";
        readonly extensions: readonly ["xz"];
    };
    readonly "application/x-zmachine": {
        readonly source: "apache";
        readonly extensions: readonly ["z1", "z2", "z3", "z4", "z5", "z6", "z7", "z8"];
    };
    readonly "application/xaml+xml": {
        readonly source: "apache";
        readonly extensions: readonly ["xaml"];
    };
    readonly "application/xcap-att+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["xav"];
    };
    readonly "application/xcap-caps+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["xca"];
    };
    readonly "application/xcap-diff+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["xdf"];
    };
    readonly "application/xcap-el+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["xel"];
    };
    readonly "application/xcap-ns+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["xns"];
    };
    readonly "application/xenc+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["xenc"];
    };
    readonly "application/xhtml+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["xhtml", "xht"];
    };
    readonly "application/xliff+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["xlf"];
    };
    readonly "application/xml": {
        readonly source: "iana";
        readonly extensions: readonly ["xml", "xsl", "xsd", "rng"];
    };
    readonly "application/xml-dtd": {
        readonly source: "iana";
        readonly extensions: readonly ["dtd"];
    };
    readonly "application/xop+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["xop"];
    };
    readonly "application/xproc+xml": {
        readonly source: "apache";
        readonly extensions: readonly ["xpl"];
    };
    readonly "application/xslt+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["xsl", "xslt"];
    };
    readonly "application/xspf+xml": {
        readonly source: "apache";
        readonly extensions: readonly ["xspf"];
    };
    readonly "application/xv+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["mxml", "xhvml", "xvml", "xvm"];
    };
    readonly "application/yaml": {
        readonly source: "iana";
        readonly extensions: readonly ["yaml", "yml"];
    };
    readonly "application/yang": {
        readonly source: "iana";
        readonly extensions: readonly ["yang"];
    };
    readonly "application/yin+xml": {
        readonly source: "iana";
        readonly extensions: readonly ["yin"];
    };
    readonly "application/zip": {
        readonly source: "iana";
        readonly extensions: readonly ["zip"];
    };
};

export { application };
