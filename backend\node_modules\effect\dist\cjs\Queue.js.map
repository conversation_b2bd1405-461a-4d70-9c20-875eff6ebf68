{"version": 3, "file": "Queue.js", "names": ["internal", "_interopRequireWildcard", "require", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "EnqueueTypeId", "exports", "DequeueTypeId", "QueueStrategyTypeId", "BackingQueueTypeId", "isQueue", "isDequeue", "isEnqueue", "backPressureStrategy", "droppingStrategy", "slidingStrategy", "make", "bounded", "dropping", "sliding", "unbounded", "capacity", "size", "isEmpty", "isFull", "isShutdown", "await<PERSON><PERSON><PERSON>down", "shutdown", "offer", "unsafeOffer", "offerAll", "poll", "take", "takeAll", "takeUpTo", "takeBetween", "takeN"], "sources": ["../../src/Queue.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAMA,IAAAA,QAAA,GAAAC,uBAAA,CAAAC,OAAA;AAA+C,SAAAC,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAH,wBAAAG,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAQ/C;;;;AAIO,MAAMW,aAAa,GAAAC,OAAA,CAAAD,aAAA,GAAkBvB,QAAQ,CAACuB,aAAa;AAQlE;;;;AAIO,MAAME,aAAa,GAAAD,OAAA,CAAAC,aAAA,GAAkBzB,QAAQ,CAACyB,aAAa;AAQlE;;;;AAIO,MAAMC,mBAAmB,GAAAF,OAAA,CAAAE,mBAAA,GAAkB1B,QAAQ,CAAC0B,mBAAmB;AAQ9E;;;;AAIO,MAAMC,kBAAkB,GAAAH,OAAA,CAAAG,kBAAA,GAAkB3B,QAAQ,CAAC2B,kBAAkB;AAgU5E;;;;;;AAMO,MAAMC,OAAO,GAAAJ,OAAA,CAAAI,OAAA,GAAwC5B,QAAQ,CAAC4B,OAAO;AAE5E;;;;;;AAMO,MAAMC,SAAS,GAAAL,OAAA,CAAAK,SAAA,GAA0C7B,QAAQ,CAAC6B,SAAS;AAElF;;;;;;AAMO,MAAMC,SAAS,GAAAN,OAAA,CAAAM,SAAA,GAA0C9B,QAAQ,CAAC8B,SAAS;AAElF;;;;AAIO,MAAMC,oBAAoB,GAAAP,OAAA,CAAAO,oBAAA,GAAyB/B,QAAQ,CAAC+B,oBAAoB;AAEvF;;;;AAIO,MAAMC,gBAAgB,GAAAR,OAAA,CAAAQ,gBAAA,GAAyBhC,QAAQ,CAACgC,gBAAgB;AAE/E;;;;AAIO,MAAMC,eAAe,GAAAT,OAAA,CAAAS,eAAA,GAAyBjC,QAAQ,CAACiC,eAAe;AAE7E;;;;AAIO,MAAMC,IAAI,GAAAV,OAAA,CAAAU,IAAA,GAAkFlC,QAAQ,CAACkC,IAAI;AAEhH;;;;;;;;;;;;AAYO,MAAMC,OAAO,GAAAX,OAAA,CAAAW,OAAA,GAA8DnC,QAAQ,CAACmC,OAAO;AAElG;;;;;;;;;;;;;AAaO,MAAMC,QAAQ,GAAAZ,OAAA,CAAAY,QAAA,GAA8DpC,QAAQ,CAACoC,QAAQ;AAEpG;;;;;;;;;;;;;AAaO,MAAMC,OAAO,GAAAb,OAAA,CAAAa,OAAA,GAA8DrC,QAAQ,CAACqC,OAAO;AAElG;;;;;;AAMO,MAAMC,SAAS,GAAAd,OAAA,CAAAc,SAAA,GAAqCtC,QAAQ,CAACsC,SAAS;AAE7E;;;;;;AAMO,MAAMC,QAAQ,GAAAf,OAAA,CAAAe,QAAA,GAAiDvC,QAAQ,CAACuC,QAAQ;AAEvF;;;;;;;;AAQO,MAAMC,IAAI,GAAAhB,OAAA,CAAAgB,IAAA,GAAgExC,QAAQ,CAACwC,IAAI;AAE9F;;;;;;AAMO,MAAMC,OAAO,GAAAjB,OAAA,CAAAiB,OAAA,GAAiEzC,QAAQ,CAACyC,OAAO;AAErG;;;;;;;AAOO,MAAMC,MAAM,GAAAlB,OAAA,CAAAkB,MAAA,GAAiE1C,QAAQ,CAAC0C,MAAM;AAEnG;;;;;;AAMO,MAAMC,UAAU,GAAAnB,OAAA,CAAAmB,UAAA,GAAiE3C,QAAQ,CAAC2C,UAAU;AAE3G;;;;;;;;AAQO,MAAMC,aAAa,GAAApB,OAAA,CAAAoB,aAAA,GAA8D5C,QAAQ,CAAC4C,aAAa;AAE9G;;;;;;;AAOO,MAAMC,QAAQ,GAAArB,OAAA,CAAAqB,QAAA,GAA8D7C,QAAQ,CAAC6C,QAAQ;AAEpG;;;;;;AAMO,MAAMC,KAAK,GAAAtB,OAAA,CAAAsB,KAAA,GAed9C,QAAQ,CAAC8C,KAAK;AAElB;;;;;;AAMO,MAAMC,WAAW,GAAAvB,OAAA,CAAAuB,WAAA,GAepB/C,QAAQ,CAAC+C,WAAW;AAExB;;;;;;;;;;;;;;;;;;AAkBO,MAAMC,QAAQ,GAAAxB,OAAA,CAAAwB,QAAA,GAuCjBhD,QAAQ,CAACgD,QAAQ;AAErB;;;;;;;AAOO,MAAMC,IAAI,GAAAzB,OAAA,CAAAyB,IAAA,GAA6DjD,QAAQ,CAACiD,IAAI;AAE3F;;;;;;;AAOO,MAAMC,IAAI,GAAA1B,OAAA,CAAA0B,IAAA,GAA8ClD,QAAQ,CAACkD,IAAI;AAE5E;;;;;;;AAOO,MAAMC,OAAO,GAAA3B,OAAA,CAAA2B,OAAA,GAA2DnD,QAAQ,CAACmD,OAAO;AAE/F;;;;;;AAMO,MAAMC,QAAQ,GAAA5B,OAAA,CAAA4B,QAAA,GAejBpD,QAAQ,CAACoD,QAAQ;AAErB;;;;;;;;AAQO,MAAMC,WAAW,GAAA7B,OAAA,CAAA6B,WAAA,GAmBpBrD,QAAQ,CAACqD,WAAW;AAExB;;;;;;;;AAQO,MAAMC,KAAK,GAAA9B,OAAA,CAAA8B,KAAA,GAmBdtD,QAAQ,CAACsD,KAAK", "ignoreList": []}