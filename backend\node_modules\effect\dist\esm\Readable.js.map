{"version": 3, "file": "Readable.js", "names": ["dual", "core", "pipeArguments", "hasProperty", "TypeId", "Symbol", "for", "isReadable", "u", "Proto", "pipe", "arguments", "make", "get", "self", "Object", "create", "map", "f", "mapEffect", "flatMap", "unwrap", "effect", "s"], "sources": ["../../src/Readable.ts"], "sourcesContent": [null], "mappings": "AAIA,SAASA,IAAI,QAAQ,eAAe;AACpC,OAAO,KAAKC,IAAI,MAAM,oBAAoB;AAC1C,SAAwBC,aAAa,QAAQ,eAAe;AAC5D,SAASC,WAAW,QAAQ,gBAAgB;AAG5C;;;;AAIA,OAAO,MAAMC,MAAM,gBAAkBC,MAAM,CAACC,GAAG,CAAC,iBAAiB,CAAC;AAiBlE;;;;AAIA,OAAO,MAAMC,UAAU,GAAIC,CAAU,IAA+CL,WAAW,CAACK,CAAC,EAAEJ,MAAM,CAAC;AAE1G,MAAMK,KAAK,GAA+B;EACxC,CAACL,MAAM,GAAGA,MAAM;EAChBM,IAAIA,CAAA;IACF,OAAOR,aAAa,CAAC,IAAI,EAAES,SAAS,CAAC;EACvC;CACD;AAED;;;;AAIA,OAAO,MAAMC,IAAI,GAAaC,GAAoB,IAAuB;EACvE,MAAMC,IAAI,GAAGC,MAAM,CAACC,MAAM,CAACP,KAAK,CAAC;EACjCK,IAAI,CAACD,GAAG,GAAGA,GAAG;EACd,OAAOC,IAAI;AACb,CAAC;AAED;;;;AAIA,OAAO,MAAMG,GAAG,gBAWZjB,IAAI,CACN,CAAC,EACD,CAAac,IAAuB,EAAEI,CAAuB,KAAwBN,IAAI,CAACX,IAAI,CAACgB,GAAG,CAACH,IAAI,CAACD,GAAG,EAAEK,CAAC,CAAC,CAAC,CACjH;AAED;;;;AAIA,OAAO,MAAMC,SAAS,gBAWlBnB,IAAI,CAAC,CAAC,EAAE,CACVc,IAAuB,EACvBI,CAAuC,KACPN,IAAI,CAACX,IAAI,CAACmB,OAAO,CAACN,IAAI,CAACD,GAAG,EAAEK,CAAC,CAAC,CAAC,CAAC;AAElE;;;;AAIA,OAAO,MAAMG,MAAM,GACjBC,MAAyC,IAEzCV,IAAI,CACFX,IAAI,CAACmB,OAAO,CAACE,MAAM,EAAGC,CAAC,IAAKA,CAAC,CAACV,GAAG,CAAC,CACnC", "ignoreList": []}