(()=>{var e={};e.id=6502,e.ids=[6502],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15125:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\sign-in\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\sign-in\\page.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20384:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var t=r(65239),a=r(48088),i=r(88170),n=r.n(i),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(s,l);let d={children:["",{children:["sign-in",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,15125)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\sign-in\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,60566)),"C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Desktop\\code\\boot\\dashboard\\app\\sign-in\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/sign-in/page",pathname:"/sign-in",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},81410:(e,s,r)=>{Promise.resolve().then(r.bind(r,15125))},90215:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>w});var t=r(60687),a=r(43210),i=r(68082),n=r(16189),o=r(99208),l=r(27605),d=r(63442),c=r(45880),p=r(24934),u=r(68988),m=r(39390),x=r(18528),h=r(52581);let g=c.Ik({email:c.Yj().email(),password:c.Yj().min(6)});function f(){let{t:e}=(0,i.B)(),s=(0,n.useRouter)(),r=(0,n.useSearchParams)().get("redirect_url")||"/dashboard/analytics";console.log("Redirect URL:",r);let[c,f]=(0,a.useState)(!1),{register:j,handleSubmit:v,formState:{errors:b}}=(0,l.mN)({resolver:(0,d.u)(g)});async function w(t){f(!0);try{let a=await (0,o.Jv)("credentials",{email:t.email,password:t.password,redirect:!1,callbackUrl:r});if(a?.error){h.oR.error(e("auth.invalidCredentials")),f(!1);return}h.oR.success(e("auth.loginSuccessful")),s.push(r),s.refresh()}catch(s){console.error("Sign in error:",s),h.oR.error(e("common.somethingWentWrong")),f(!1)}}return(0,t.jsxs)("div",{className:"grid gap-6",children:[(0,t.jsx)("form",{onSubmit:v(w),children:(0,t.jsxs)("div",{className:"grid gap-4",children:[(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(m.J,{htmlFor:"email",children:e("auth.email")}),(0,t.jsx)(u.p,{id:"email",type:"email",placeholder:"<EMAIL>",autoCapitalize:"none",autoComplete:"email",autoCorrect:"off",disabled:c,...j("email")}),b.email&&(0,t.jsx)("p",{className:"text-sm text-destructive",children:b.email.message})]}),(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)(m.J,{htmlFor:"password",children:e("auth.password")}),(0,t.jsx)(p.$,{variant:"link",className:"px-0",asChild:!0,children:(0,t.jsx)("a",{href:"/forgot-password",children:e("auth.forgotPassword")})})]}),(0,t.jsx)(u.p,{id:"password",type:"password",autoCapitalize:"none",autoComplete:"current-password",disabled:c,...j("password")}),b.password&&(0,t.jsx)("p",{className:"text-sm text-destructive",children:b.password.message})]}),(0,t.jsxs)(p.$,{type:"submit",disabled:c,children:[c&&(0,t.jsx)(x.F.spinner,{className:"mr-2 h-4 w-4 animate-spin"}),e("auth.signIn")]})]})}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,t.jsx)("span",{className:"w-full border-t"})}),(0,t.jsx)("div",{className:"relative flex justify-center text-xs uppercase",children:(0,t.jsx)("span",{className:"bg-card px-2 text-muted-foreground",children:e("auth.orContinueWith")})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)(p.$,{variant:"outline",type:"button",disabled:c,onClick:()=>(0,o.Jv)("github",{callbackUrl:r}),children:[(0,t.jsx)(x.F.gitHub,{className:"mr-2 h-4 w-4"}),"GitHub"]}),(0,t.jsxs)(p.$,{variant:"outline",type:"button",disabled:c,onClick:()=>(0,o.Jv)("google",{callbackUrl:r}),children:[(0,t.jsx)(x.F.google,{className:"mr-2 h-4 w-4"}),"Google"]})]}),(0,t.jsxs)("div",{className:"text-center text-sm",children:[e("auth.dontHaveAccount")," ",(0,t.jsx)(p.$,{variant:"link",className:"px-0",asChild:!0,children:(0,t.jsx)("a",{href:`/sign-up${r?`?redirect_url=${encodeURIComponent(r)}`:""}`,children:e("auth.signUp")})})]})]})}var j=r(7489),v=r(99563);function b(){let{t:e}=(0,i.B)();return(0,t.jsx)("div",{className:"container flex h-screen w-screen flex-col items-center justify-center",children:(0,t.jsxs)("div",{className:"mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]",children:[(0,t.jsxs)("div",{className:"flex flex-col space-y-2 text-center",children:[(0,t.jsx)(x.F.logo,{className:"mx-auto h-6 w-6"}),(0,t.jsx)("h1",{className:"text-2xl font-semibold tracking-tight",children:e("auth.welcomeBack")}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:e("auth.signInToContinue")})]}),(0,t.jsx)(f,{}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)(j.c,{}),(0,t.jsx)(v.U,{})]})]})})}function w(){return(0,t.jsx)(a.Suspense,{fallback:(0,t.jsx)("div",{className:"flex h-screen items-center justify-center",children:"Loading..."}),children:(0,t.jsx)(b,{})})}},99562:(e,s,r)=>{Promise.resolve().then(r.bind(r,90215))}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[7719,9656,3903,3706,2105,8722,7524],()=>r(20384));module.exports=t})();