"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.bigInt64Array = bigInt64Array;
const globals_1 = require("../utils/globals");
const bigInt_1 = require("./bigInt");
const TypedIntArrayArbitraryBuilder_1 = require("./_internals/builders/TypedIntArrayArbitraryBuilder");
function bigInt64Array(constraints = {}) {
    return (0, TypedIntArrayArbitraryBuilder_1.typedIntArrayArbitraryArbitraryBuilder)(constraints, (0, globals_1.BigInt)('-9223372036854775808'), (0, globals_1.BigInt)('9223372036854775807'), globals_1.BigInt64Array, bigInt_1.bigInt);
}
