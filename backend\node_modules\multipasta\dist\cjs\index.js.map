{"version": 3, "file": "index.js", "names": ["internal", "_interopRequireWildcard", "require", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "prototype", "hasOwnProperty", "call", "i", "set", "make", "exports", "defaultIsFile", "decodeField"], "sources": ["../../src/index.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA,IAAAA,QAAA,gBAAAC,uBAAA,eAAAC,OAAA;AAAmD,SAAAC,yBAAAC,CAAA;EAAA,yBAAAC,OAAA;EAAA,IAAAC,CAAA,OAAAD,OAAA;IAAAE,CAAA,OAAAF,OAAA;EAAA,QAAAF,wBAAA,YAAAA,CAAAC,CAAA;IAAA,OAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA;EAAA,GAAAF,CAAA;AAAA;AAAA,SAAAH,wBAAAG,CAAA,EAAAE,CAAA;EAAA,KAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA;EAAA,aAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA;IAAAK,OAAA,EAAAL;EAAA;EAAA,IAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA;EAAA,IAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA;EAAA,IAAAQ,CAAA;MAAAC,SAAA;IAAA;IAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA;EAAA,SAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAjB,CAAA,EAAAc,CAAA;IAAA,IAAAI,CAAA,GAAAR,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA;IAAAI,CAAA,KAAAA,CAAA,CAAAX,GAAA,IAAAW,CAAA,CAAAC,GAAA,IAAAR,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAI,CAAA,IAAAV,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA;EAAA;EAAA,OAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAgB,GAAA,CAAAnB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAAA;AAwD5C,MAAMY,IAAI,GAAAC,OAAA,CAAAD,IAAA,GAAgCxB,QAAQ,CAACwB,IAAI;AAEvD,MAAME,aAAa,GAAAD,OAAA,CAAAC,aAAA,GAAgC1B,QAAQ,CAAC0B,aAAa;AAEzE,MAAMC,WAAW,GAAAF,OAAA,CAAAE,WAAA,GACtB3B,QAAQ,CAAC2B,WAAW"}