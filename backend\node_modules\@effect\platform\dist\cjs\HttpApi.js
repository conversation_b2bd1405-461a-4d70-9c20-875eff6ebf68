"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.reflect = exports.make = exports.isHttpApi = exports.TypeId = exports.Api = exports.AdditionalSchemas = void 0;
var Context = _interopRequireWildcard(require("effect/Context"));
var Option = _interopRequireWildcard(require("effect/Option"));
var _Pipeable = require("effect/Pipeable");
var Predicate = _interopRequireWildcard(require("effect/Predicate"));
var Record = _interopRequireWildcard(require("effect/Record"));
var AST = _interopRequireWildcard(require("effect/SchemaAST"));
var _HttpApiError = require("./HttpApiError.js");
var HttpApiSchema = _interopRequireWildcard(require("./HttpApiSchema.js"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
/**
 * @since 1.0.0
 */

/**
 * @since 1.0.0
 * @category type ids
 */
const TypeId = exports.TypeId = /*#__PURE__*/Symbol.for("@effect/platform/HttpApi");
/**
 * @since 1.0.0
 * @category guards
 */
const isHttpApi = u => Predicate.hasProperty(u, TypeId);
/**
 * @since 1.0.0
 * @category tags
 */
exports.isHttpApi = isHttpApi;
class Api extends /*#__PURE__*/Context.Tag("@effect/platform/HttpApi/Api")() {}
exports.Api = Api;
const Proto = {
  [TypeId]: TypeId,
  pipe() {
    return (0, _Pipeable.pipeArguments)(this, arguments);
  },
  add(group) {
    return makeProto({
      identifier: this.identifier,
      groups: Record.set(this.groups, group.identifier, group),
      errorSchema: this.errorSchema,
      annotations: this.annotations,
      middlewares: this.middlewares
    });
  },
  addHttpApi(api) {
    const newGroups = {
      ...this.groups
    };
    for (const key in api.groups) {
      const newGroup = api.groups[key].annotateContext(Context.empty());
      newGroup.annotations = Context.merge(api.annotations, newGroup.annotations);
      newGroup.middlewares = new Set([...api.middlewares, ...newGroup.middlewares]);
      newGroups[key] = newGroup;
    }
    return makeProto({
      identifier: this.identifier,
      groups: newGroups,
      errorSchema: HttpApiSchema.UnionUnify(this.errorSchema, api.errorSchema),
      annotations: this.annotations,
      middlewares: this.middlewares
    });
  },
  addError(schema, annotations) {
    return makeProto({
      identifier: this.identifier,
      groups: this.groups,
      errorSchema: HttpApiSchema.UnionUnify(this.errorSchema, annotations?.status ? schema.annotations(HttpApiSchema.annotations({
        status: annotations.status
      })) : schema),
      annotations: this.annotations,
      middlewares: this.middlewares
    });
  },
  prefix(prefix) {
    return makeProto({
      identifier: this.identifier,
      groups: Record.map(this.groups, group => group.prefix(prefix)),
      errorSchema: this.errorSchema,
      annotations: this.annotations,
      middlewares: this.middlewares
    });
  },
  middleware(tag) {
    return makeProto({
      identifier: this.identifier,
      groups: this.groups,
      errorSchema: HttpApiSchema.UnionUnify(this.errorSchema, tag.failure),
      annotations: this.annotations,
      middlewares: new Set([...this.middlewares, tag])
    });
  },
  annotate(tag, value) {
    return makeProto({
      identifier: this.identifier,
      groups: this.groups,
      errorSchema: this.errorSchema,
      annotations: Context.add(this.annotations, tag, value),
      middlewares: this.middlewares
    });
  },
  annotateContext(context) {
    return makeProto({
      identifier: this.identifier,
      groups: this.groups,
      errorSchema: this.errorSchema,
      annotations: Context.merge(this.annotations, context),
      middlewares: this.middlewares
    });
  }
};
const makeProto = options => {
  function HttpApi() {}
  Object.setPrototypeOf(HttpApi, Proto);
  HttpApi.groups = options.groups;
  HttpApi.errorSchema = options.errorSchema;
  HttpApi.annotations = options.annotations;
  HttpApi.middlewares = options.middlewares;
  return HttpApi;
};
/**
 * An `HttpApi` is a collection of `HttpApiEndpoint`s. You can use an `HttpApi` to
 * represent a portion of your domain.
 *
 * The endpoints can be implemented later using the `HttpApiBuilder.make` api.
 *
 * @since 1.0.0
 * @category constructors
 */
const make = identifier => makeProto({
  identifier,
  groups: new Map(),
  errorSchema: _HttpApiError.HttpApiDecodeError,
  annotations: Context.empty(),
  middlewares: new Set()
});
/**
 * Extract metadata from an `HttpApi`, which can be used to generate documentation
 * or other tooling.
 *
 * See the `OpenApi` & `HttpApiClient` modules for examples of how to use this function.
 *
 * @since 1.0.0
 * @category reflection
 */
exports.make = make;
const reflect = (self, options) => {
  const apiErrors = extractMembers(self.errorSchema.ast, new Map(), HttpApiSchema.getStatusErrorAST);
  const groups = Object.values(self.groups);
  for (const group of groups) {
    const groupErrors = extractMembers(group.errorSchema.ast, apiErrors, HttpApiSchema.getStatusErrorAST);
    const groupAnnotations = Context.merge(self.annotations, group.annotations);
    options.onGroup({
      group,
      mergedAnnotations: groupAnnotations
    });
    const endpoints = Object.values(group.endpoints);
    for (const endpoint of endpoints) {
      if (options.predicate && !options.predicate({
        endpoint,
        group
      })) continue;
      const errors = extractMembers(endpoint.errorSchema.ast, groupErrors, HttpApiSchema.getStatusErrorAST);
      options.onEndpoint({
        group,
        endpoint,
        middleware: new Set([...group.middlewares, ...endpoint.middlewares]),
        mergedAnnotations: Context.merge(groupAnnotations, endpoint.annotations),
        payloads: endpoint.payloadSchema._tag === "Some" ? extractPayloads(endpoint.payloadSchema.value.ast) : emptyMap,
        successes: extractMembers(endpoint.successSchema.ast, new Map(), HttpApiSchema.getStatusSuccessAST),
        errors
      });
    }
  }
};
// -------------------------------------------------------------------------------------
exports.reflect = reflect;
const emptyMap = /*#__PURE__*/new Map();
const extractMembers = (ast, inherited, getStatus) => {
  const members = new Map(inherited);
  function process(type) {
    if (AST.isNeverKeyword(type)) {
      return;
    }
    const annotations = HttpApiSchema.extractAnnotations(ast.annotations);
    // Avoid changing the reference unless necessary
    // Otherwise, deduplication of the ASTs below will not be possible
    if (!Record.isEmptyRecord(annotations)) {
      type = AST.annotations(type, {
        ...annotations,
        ...type.annotations
      });
    }
    const status = getStatus(type);
    const emptyDecodeable = HttpApiSchema.getEmptyDecodeable(type);
    const current = members.get(status);
    members.set(status, {
      description: (current ? current.description : Option.none()).pipe(Option.orElse(() => getDescriptionOrIdentifier(type))),
      ast: (current ? current.ast : Option.none()).pipe(
      // Deduplicate the ASTs
      Option.map(current => HttpApiSchema.UnionUnifyAST(current, type)), Option.orElse(() => !emptyDecodeable && AST.isVoidKeyword(AST.encodedAST(type)) ? Option.none() : Option.some(type)))
    });
  }
  HttpApiSchema.extractUnionTypes(ast).forEach(process);
  return members;
};
const extractPayloads = topAst => {
  const members = new Map();
  function process(ast) {
    if (ast._tag === "NeverKeyword") {
      return;
    }
    ast = AST.annotations(ast, {
      ...HttpApiSchema.extractAnnotations(topAst.annotations),
      ...ast.annotations
    });
    const encoding = HttpApiSchema.getEncoding(ast);
    const contentType = HttpApiSchema.getMultipart(ast) ? "multipart/form-data" : encoding.contentType;
    const current = members.get(contentType);
    if (current === undefined) {
      members.set(contentType, {
        encoding,
        ast
      });
    } else {
      current.ast = AST.Union.make([current.ast, ast]);
    }
  }
  if (topAst._tag === "Union") {
    for (const type of topAst.types) {
      process(type);
    }
  } else {
    process(topAst);
  }
  return members;
};
const getDescriptionOrIdentifier = ast => {
  const annotations = "to" in ast ? {
    ...ast.to.annotations,
    ...ast.annotations
  } : ast.annotations;
  return Option.fromNullable(annotations[AST.DescriptionAnnotationId] ?? annotations[AST.IdentifierAnnotationId]);
};
/**
 * Adds additional schemas to components/schemas.
 * The provided schemas must have a `identifier` annotation.
 *
 * @since 1.0.0
 * @category tags
 */
class AdditionalSchemas extends /*#__PURE__*/Context.Tag("@effect/platform/HttpApi/AdditionalSchemas")() {}
exports.AdditionalSchemas = AdditionalSchemas;
//# sourceMappingURL=HttpApi.js.map