{"version": 3, "file": "RequestBlock.d.ts", "sourceRoot": "", "sources": ["../../src/RequestBlock.ts"], "names": [], "mappings": "AAIA,OAAO,KAAK,KAAK,OAAO,MAAM,cAAc,CAAA;AAC5C,OAAO,KAAK,KAAK,eAAe,MAAM,sBAAsB,CAAA;AAE5D;;;;;;;;;GASG;AACH,MAAM,MAAM,YAAY,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,MAAM,CAAA;AAErD;;;GAGG;AACH,MAAM,CAAC,OAAO,WAAW,YAAY,CAAC;IACpC;;;OAGG;IACH,UAAiB,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;QAC/B,SAAS,IAAI,CAAC,CAAA;QACd,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,CAAA;QAC7B,UAAU,CACR,UAAU,EAAE,eAAe,CAAC,eAAe,CAAC,OAAO,CAAC,EACpD,cAAc,EAAE,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,GACrC,CAAC,CAAA;QACJ,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,CAAA;KAC9B;CACF;AAED;;;GAGG;AACH,MAAM,WAAW,KAAK;IACpB,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAA;CACvB;AAED;;;GAGG;AACH,MAAM,WAAW,GAAG;IAClB,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAA;IACpB,QAAQ,CAAC,IAAI,EAAE,YAAY,CAAA;IAC3B,QAAQ,CAAC,KAAK,EAAE,YAAY,CAAA;CAC7B;AAED;;;GAGG;AACH,MAAM,WAAW,GAAG;IAClB,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAA;IACpB,QAAQ,CAAC,IAAI,EAAE,YAAY,CAAA;IAC3B,QAAQ,CAAC,KAAK,EAAE,YAAY,CAAA;CAC7B;AAED;;;GAGG;AACH,MAAM,WAAW,MAAM;IACrB,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAA;IACvB,QAAQ,CAAC,UAAU,EAAE,eAAe,CAAC,eAAe,CAAC,OAAO,CAAC,CAAA;IAC7D,QAAQ,CAAC,cAAc,EAAE,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;CAChD;AAED;;;GAGG;AACH,eAAO,MAAM,MAAM,EAAE,CAAC,CAAC,EACrB,UAAU,EAAE,eAAe,CAAC,eAAe,CAAC,CAAC,CAAC,EAC9C,cAAc,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,KAC7B,YAAmC,CAAA;AAExC;;;GAGG;AACH,eAAO,MAAM,KAAK,EAAE,YAAkC,CAAA;AAEtD;;;GAGG;AACH,eAAO,MAAM,mBAAmB,EAAE,CAAC,CAAC,EAClC,IAAI,EAAE,YAAY,EAClB,CAAC,EAAE,CAAC,UAAU,EAAE,eAAe,CAAC,eAAe,CAAC,CAAC,CAAC,KAAK,eAAe,CAAC,eAAe,CAAC,CAAC,CAAC,KACtF,YAAgD,CAAA;AAErD;;;GAGG;AACH,eAAO,MAAM,QAAQ,EAAE,CAAC,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,YAAY,KAAK,YAAgC,CAAA;AAEnG;;;GAGG;AACH,eAAO,MAAM,MAAM,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAwB,CAAA;AAE1G;;;GAGG;AACH,eAAO,MAAM,UAAU,EAAE,CAAC,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,YAAY,KAAK,YAAgC,CAAA"}