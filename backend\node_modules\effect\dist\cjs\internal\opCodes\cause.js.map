{"version": 3, "file": "cause.js", "names": ["OP_DIE", "exports", "OP_EMPTY", "OP_FAIL", "OP_INTERRUPT", "OP_PARALLEL", "OP_SEQUENTIAL"], "sources": ["../../../../src/internal/opCodes/cause.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA;AACO,MAAMA,MAAM,GAAAC,OAAA,CAAAD,MAAA,GAAG,KAAc;AAKpC;AACO,MAAME,QAAQ,GAAAD,OAAA,CAAAC,QAAA,GAAG,OAAgB;AAKxC;AACO,MAAMC,OAAO,GAAAF,OAAA,CAAAE,OAAA,GAAG,MAAe;AAKtC;AACO,MAAMC,YAAY,GAAAH,OAAA,CAAAG,YAAA,GAAG,WAAoB;AAKhD;AACO,MAAMC,WAAW,GAAAJ,OAAA,CAAAI,WAAA,GAAG,UAAmB;AAK9C;AACO,MAAMC,aAAa,GAAAL,OAAA,CAAAK,aAAA,GAAG,YAAqB", "ignoreList": []}