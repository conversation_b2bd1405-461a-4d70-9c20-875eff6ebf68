import * as Effect from 'effect/Effect';
import { toWebRequest, defineEventHandler } from 'h3';
import { makeAdapterHandler } from '../dist/_internal/handler.js';
import { createBuilder } from '../dist/_internal/upload-builder.js';
export { UTFiles, UTRegion as experimental_UTRegion } from '../dist/_internal/types.js';

const createUploadthing = (opts)=>createBuilder(opts);
const createRouteHandler = (opts)=>{
    const handler = makeAdapterHandler((event)=>Effect.succeed({
            event
        }), (event)=>Effect.succeed(toWebRequest(event)), opts, "h3");
    return defineEventHandler(handler);
};

export { createRouteHandler, createUploadthing };
