"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.match = exports.isYield = exports.isContinue = exports.isClose = exports.isChildExecutorDecision = exports.Yield = exports.Continue = exports.Close = exports.ChildExecutorDecisionTypeId = void 0;
var _Function = require("../../Function.js");
var _Predicate = require("../../Predicate.js");
var OpCodes = _interopRequireWildcard(require("../opCodes/channelChildExecutorDecision.js"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
/** @internal */
const ChildExecutorDecisionSymbolKey = "effect/ChannelChildExecutorDecision";
/** @internal */
const ChildExecutorDecisionTypeId = exports.ChildExecutorDecisionTypeId = /*#__PURE__*/Symbol.for(ChildExecutorDecisionSymbolKey);
/** @internal */
const proto = {
  [ChildExecutorDecisionTypeId]: ChildExecutorDecisionTypeId
};
/** @internal */
const Continue = _ => {
  const op = Object.create(proto);
  op._tag = OpCodes.OP_CONTINUE;
  return op;
};
/** @internal */
exports.Continue = Continue;
const Close = value => {
  const op = Object.create(proto);
  op._tag = OpCodes.OP_CLOSE;
  op.value = value;
  return op;
};
/** @internal */
exports.Close = Close;
const Yield = _ => {
  const op = Object.create(proto);
  op._tag = OpCodes.OP_YIELD;
  return op;
};
/** @internal */
exports.Yield = Yield;
const isChildExecutorDecision = u => (0, _Predicate.hasProperty)(u, ChildExecutorDecisionTypeId);
/** @internal */
exports.isChildExecutorDecision = isChildExecutorDecision;
const isContinue = self => self._tag === OpCodes.OP_CONTINUE;
/** @internal */
exports.isContinue = isContinue;
const isClose = self => self._tag === OpCodes.OP_CLOSE;
/** @internal */
exports.isClose = isClose;
const isYield = self => self._tag === OpCodes.OP_YIELD;
/** @internal */
exports.isYield = isYield;
const match = exports.match = /*#__PURE__*/(0, _Function.dual)(2, (self, {
  onClose,
  onContinue,
  onYield
}) => {
  switch (self._tag) {
    case OpCodes.OP_CONTINUE:
      {
        return onContinue();
      }
    case OpCodes.OP_CLOSE:
      {
        return onClose(self.value);
      }
    case OpCodes.OP_YIELD:
      {
        return onYield();
      }
  }
});
//# sourceMappingURL=childExecutorDecision.js.map