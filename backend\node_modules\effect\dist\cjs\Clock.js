"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.sleep = exports.make = exports.currentTimeNanos = exports.currentTimeMillis = exports.clockWith = exports.ClockTypeId = exports.Clock = void 0;
var internal = _interopRequireWildcard(require("./internal/clock.js"));
var defaultServices = _interopRequireWildcard(require("./internal/defaultServices.js"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
/**
 * @since 2.0.0
 * @category symbols
 */
const ClockTypeId = exports.ClockTypeId = internal.ClockTypeId;
/**
 * @since 2.0.0
 * @category constructors
 */
const make = exports.make = internal.make;
/**
 * @since 2.0.0
 * @category constructors
 */
const sleep = exports.sleep = defaultServices.sleep;
/**
 * @since 2.0.0
 * @category constructors
 */
const currentTimeMillis = exports.currentTimeMillis = defaultServices.currentTimeMillis;
/**
 * @since 2.0.0
 * @category constructors
 */
const currentTimeNanos = exports.currentTimeNanos = defaultServices.currentTimeNanos;
/**
 * @since 2.0.0
 * @category constructors
 */
const clockWith = exports.clockWith = defaultServices.clockWith;
/**
 * @since 2.0.0
 * @category context
 */
const Clock = exports.Clock = internal.clockTag;
//# sourceMappingURL=Clock.js.map