{"version": 3, "file": "node.js", "names": ["MP", "_interopRequireWildcard", "require", "_nodeStream", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "prototype", "hasOwnProperty", "call", "i", "set", "MultipastaStream", "Duplex", "_parser", "_canWrite", "_writeCallback", "constructor", "config", "readableObjectMode", "make", "onField", "info", "value", "field", "_tag", "push", "emit", "onFile", "file", "FileStream", "chunk", "_resume", "onError", "error", "onDone", "undefined", "callback", "_read", "_size", "_write", "encoding", "write", "Uint8Array", "<PERSON><PERSON><PERSON>", "from", "_final", "end", "exports", "Readable", "_parent", "filename"], "sources": ["../../src/node.ts"], "sourcesContent": [null], "mappings": ";;;;;;;;;;;;;AAEA,IAAAA,EAAA,gBAAAC,uBAAA,eAAAC,OAAA;AACA,IAAAC,WAAA,gBAAAD,OAAA;AAA8C,SAAAE,yBAAAC,CAAA;EAAA,yBAAAC,OAAA;EAAA,IAAAC,CAAA,OAAAD,OAAA;IAAAE,CAAA,OAAAF,OAAA;EAAA,QAAAF,wBAAA,YAAAA,CAAAC,CAAA;IAAA,OAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA;EAAA,GAAAF,CAAA;AAAA;AAAA,SAAAJ,wBAAAI,CAAA,EAAAE,CAAA;EAAA,KAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA;EAAA,aAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA;IAAAK,OAAA,EAAAL;EAAA;EAAA,IAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA;EAAA,IAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA;EAAA,IAAAQ,CAAA;MAAAC,SAAA;IAAA;IAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA;EAAA,SAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAjB,CAAA,EAAAc,CAAA;IAAA,IAAAI,CAAA,GAAAR,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA;IAAAI,CAAA,KAAAA,CAAA,CAAAX,GAAA,IAAAW,CAAA,CAAAC,GAAA,IAAAR,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAI,CAAA,IAAAV,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA;EAAA;EAAA,OAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAgB,GAAA,CAAAnB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAAA;AAsCxC,MAAOY,gBAAiB,SAAQtB,WAAA,CAAAuB,MAAM;EAClCC,OAAO;EACfC,SAAS,GAAG,IAAI;EACRC,cAAc;EAEtBC,YAAYC,MAAkB;IAC5B,KAAK,CAAC;MAAEC,kBAAkB,EAAE;IAAI,CAAE,CAAC;IACnC,IAAI,CAACL,OAAO,GAAG3B,EAAE,CAACiC,IAAI,CAAC;MACrB,GAAIF,MAAc;MAClBG,OAAO,EAAEA,CAACC,IAAI,EAAEC,KAAK,KAAI;QACvB,MAAMC,KAAK,GAAU;UAAEC,IAAI,EAAE,OAAO;UAAEH,IAAI;UAAEC;QAAK,CAAE;QACnD,IAAI,CAACG,IAAI,CAACF,KAAK,CAAC;QAChB,IAAI,CAACG,IAAI,CAAC,OAAO,EAAEH,KAAK,CAAC;MAC3B,CAAC;MACDI,MAAM,EAAEN,IAAI,IAAG;QACb,MAAMO,IAAI,GAAG,IAAIC,UAAU,CAACR,IAAI,EAAE,IAAI,CAAC;QACvC,IAAI,CAACI,IAAI,CAACG,IAAI,CAAC;QACf,IAAI,CAACF,IAAI,CAAC,MAAM,EAAEE,IAAI,CAAC;QACvB,OAAOE,KAAK,IAAG;UACb,IAAI,CAAChB,SAAS,GAAGc,IAAI,CAACH,IAAI,CAACK,KAAK,CAAC;UACjC,IAAIA,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI,CAAChB,SAAS,EAAE;YACrC,IAAI,CAACiB,OAAO,EAAE;;QAElB,CAAC;MACH,CAAC;MACDC,OAAO,EAAEC,KAAK,IAAG;QACf,IAAI,CAACP,IAAI,CAAC,OAAO,EAAEO,KAAK,CAAC;MAC3B,CAAC;MACDC,MAAM,EAAEA,CAAA,KAAK;QACX,IAAI,CAACT,IAAI,CAAC,IAAI,CAAC;MACjB;KACD,CAAC;EACJ;EAEAM,OAAOA,CAAA;IACL,IAAI,CAACjB,SAAS,GAAG,IAAI;IACrB,IAAI,IAAI,CAACC,cAAc,KAAKoB,SAAS,EAAE;MACrC,MAAMC,QAAQ,GAAG,IAAI,CAACrB,cAAc;MACpC,IAAI,CAACA,cAAc,GAAGoB,SAAS;MAC/BC,QAAQ,EAAE;;EAEd;EAEAC,KAAKA,CAACC,KAAa,GAAG;EAEtBC,MAAMA,CACJT,KAAU,EACVU,QAAwB,EACxBJ,QAAoD;IAEpD,IAAI,CAACvB,OAAO,CAAC4B,KAAK,CAChBX,KAAK,YAAYY,UAAU,GAAGZ,KAAK,GAAGa,MAAM,CAACC,IAAI,CAACd,KAAK,EAAEU,QAAQ,CAAC,CACnE;IACD,IAAI,IAAI,CAAC1B,SAAS,EAAE;MAClBsB,QAAQ,EAAE;KACX,MAAM;MACL,IAAI,CAACrB,cAAc,GAAGqB,QAAQ;;EAElC;EAEAS,MAAMA,CAACT,QAAoD;IACzD,IAAI,CAACvB,OAAO,CAACiC,GAAG,EAAE;IAClBV,QAAQ,EAAE;EACZ;;AACDW,OAAA,CAAApC,gBAAA,GAAAA,gBAAA;AAEM,MAAMQ,IAAI,GAAIF,MAAkB,IACrC,IAAIN,gBAAgB,CAACM,MAAM,CAAC;AAAA8B,OAAA,CAAA5B,IAAA,GAAAA,IAAA;AAExB,MAAOU,UAAW,SAAQxC,WAAA,CAAA2D,QAAQ;EAI3B3B,IAAA;EACD4B,OAAA;EAJDzB,IAAI,GAAG,MAAM;EACb0B,QAAQ;EACjBlC,YACWK,IAAiB,EAClB4B,OAAyB;IAEjC,KAAK,EAAE;IAHE,KAAA5B,IAAI,GAAJA,IAAI;IACL,KAAA4B,OAAO,GAAPA,OAAO;IAGf,IAAI,CAACC,QAAQ,GAAG7B,IAAI,CAAC6B,QAAS;EAChC;EACAb,KAAKA,CAACC,KAAa;IACjB,IAAI,IAAI,CAACW,OAAO,CAACnC,SAAS,KAAK,KAAK,EAAE;MACpC,IAAI,CAACmC,OAAO,CAAClB,OAAO,EAAE;;EAE1B;;AACDgB,OAAA,CAAAlB,UAAA,GAAAA,UAAA"}