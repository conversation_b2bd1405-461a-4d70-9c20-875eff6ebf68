{"version": 3, "file": "logger-circular.js", "names": ["Cause", "dual", "HashMap", "List", "core", "fiberId_", "fiberRefs", "test", "self", "input", "log", "fiberId", "none", "logLevel", "logLevelInfo", "message", "cause", "empty", "context", "spans", "annotations", "date", "Date"], "sources": ["../../../src/internal/logger-circular.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,aAAa;AACpC,SAASC,IAAI,QAAQ,gBAAgB;AACrC,OAAO,KAAKC,OAAO,MAAM,eAAe;AACxC,OAAO,KAAKC,IAAI,MAAM,YAAY;AAElC,OAAO,KAAKC,IAAI,MAAM,WAAW;AACjC,OAAO,KAAKC,QAAQ,MAAM,cAAc;AACxC,OAAO,KAAKC,SAAS,MAAM,gBAAgB;AAE3C;AACA,OAAO,MAAMC,IAAI,gBAAGN,IAAI,CAGtB,CAAC,EAAE,CAACO,IAAI,EAAEC,KAAK,KACfD,IAAI,CAACE,GAAG,CAAC;EACPC,OAAO,EAAEN,QAAQ,CAACO,IAAI;EACtBC,QAAQ,EAAET,IAAI,CAACU,YAAY;EAC3BC,OAAO,EAAEN,KAAK;EACdO,KAAK,EAAEhB,KAAK,CAACiB,KAAK;EAClBC,OAAO,EAAEZ,SAAS,CAACW,KAAK,EAAE;EAC1BE,KAAK,EAAEhB,IAAI,CAACc,KAAK,EAAE;EACnBG,WAAW,EAAElB,OAAO,CAACe,KAAK,EAAE;EAC5BI,IAAI,EAAE,IAAIC,IAAI;CACf,CAAC,CAAC", "ignoreList": []}