import * as S from 'effect/Schema';
import { ValidContentDispositions, ValidACLs } from '@uploadthing/shared';

const ContentDispositionSchema = S.Literal(...ValidContentDispositions);
const ACLSchema = S.Literal(...ValidACLs);
/**
 * Valid options for the `?actionType` query param
 */ const ActionType = S.Literal("upload");
/**
 * Valid options for the `uploadthing-hook` header
 * for requests coming from UT server
 */ const UploadThingHook = S.Literal("callback", "error");
/**
 * =============================================================================
 * =========================== Configuration ===================================
 * =============================================================================
 */ const DecodeString = S.transform(S.Uint8ArrayFromSelf, S.String, {
    decode: (data)=>new TextDecoder().decode(data),
    encode: (data)=>new TextEncoder().encode(data)
});
const ParsedToken = S.Struct({
    apiKey: S.Redacted(S.String.pipe(S.startsWith("sk_"))),
    appId: S.String,
    regions: S.NonEmptyArray(S.String),
    ingestHost: S.String.pipe(S.optionalWith({
        default: ()=>"ingest.uploadthing.com"
    }))
});
const UploadThingToken = S.Uint8ArrayFromBase64.pipe(S.compose(DecodeString), S.compose(S.parseJson(ParsedToken)));
/**
 * =============================================================================
 * ======================== File Type Hierarchy ===============================
 * =============================================================================
 */ /**
 * Properties from the web File object, this is what the client sends when initiating an upload
 */ class FileUploadData extends S.Class("FileUploadData")({
    name: S.String,
    size: S.Number,
    type: S.String,
    lastModified: S.Number.pipe(S.optional)
}) {
}
/**
 * `.middleware()` can add a customId to the incoming file data
 */ class FileUploadDataWithCustomId extends FileUploadData.extend("FileUploadDataWithCustomId")({
    customId: S.NullOr(S.String)
}) {
}
/**
 * When files are uploaded, we get back
 * - a key
 * - URLs for the file
 * - the hash (md5-hex) of the uploaded file's contents
 */ class UploadedFileData extends FileUploadDataWithCustomId.extend("UploadedFileData")({
    key: S.String,
    /**
   * @deprecated
   * This field will be removed in uploadthing v9. Use `ufsUrl` instead.
   */ url: S.String,
    /**
   * @deprecated
   * This field will be removed in uploadthing v9. Use `ufsUrl` instead.
   */ appUrl: S.String,
    ufsUrl: S.String,
    fileHash: S.String
}) {
}
/**
 * =============================================================================
 * ======================== Server Response Schemas ============================
 * =============================================================================
 */ class NewPresignedUrl extends S.Class("NewPresignedUrl")({
    url: S.String,
    key: S.String,
    customId: S.NullOr(S.String),
    name: S.String
}) {
}
class MetadataFetchStreamPart extends S.Class("MetadataFetchStreamPart")({
    payload: S.String,
    signature: S.String,
    hook: UploadThingHook
}) {
}
class MetadataFetchResponse extends S.Class("MetadataFetchResponse")({
    ok: S.Boolean
}) {
}
class CallbackResultResponse extends S.Class("CallbackResultResponse")({
    ok: S.Boolean
}) {
}
/**
 * =============================================================================
 * ======================== Client Action Payloads ============================
 * =============================================================================
 */ class UploadActionPayload extends S.Class("UploadActionPayload")({
    files: S.Array(FileUploadData),
    input: S.Unknown
}) {
}

export { ACLSchema, ActionType, CallbackResultResponse, ContentDispositionSchema, FileUploadData, FileUploadDataWithCustomId, MetadataFetchResponse, MetadataFetchStreamPart, NewPresignedUrl, ParsedToken, UploadActionPayload, UploadThingHook, UploadThingToken, UploadedFileData };
