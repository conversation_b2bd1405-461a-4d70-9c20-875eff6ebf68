import * as ___internal_types from '../dist/_internal/types.cjs';
export { UTFiles, UTRegion as experimental_UTRegion } from '../dist/_internal/types.cjs';
import * as _uploadthing_shared from '@uploadthing/shared';
import { <PERSON><PERSON> } from '@uploadthing/shared';
import { NextRequest } from 'next/server';
import { CreateBuilderOptions } from '../dist/_internal/upload-builder.cjs';
import { FileRouter, RouteHandlerOptions } from '../types/index.js';
export { FileRouter } from '../types/index.js';

type AdapterArgs = {
    req: NextRequest;
};
declare const createUploadthing: <TErrorShape extends Json>(opts?: CreateBuilderOptions<TErrorShape>) => <TRouteOptions extends _uploadthing_shared.RouteOptions>(input: _uploadthing_shared.FileRouterInputConfig, config?: TRouteOptions | undefined) => ___internal_types.UploadBuilder<{
    _routeOptions: TRouteOptions;
    _input: {
        in: ___internal_types.UnsetMarker;
        out: ___internal_types.UnsetMarker;
    };
    _metadata: ___internal_types.UnsetMarker;
    _adapterFnArgs: AdapterArgs;
    _errorShape: TErrorShape;
    _errorFn: ___internal_types.UnsetMarker;
    _output: ___internal_types.UnsetMarker;
}>;
declare const createRouteHandler: <TRouter extends FileRouter>(opts: RouteHandlerOptions<TRouter>) => {
    POST: (args_0: NextRequest) => Promise<Response>;
    GET: (args_0: NextRequest) => Promise<Response>;
};

export { createRouteHandler, createUploadthing };
