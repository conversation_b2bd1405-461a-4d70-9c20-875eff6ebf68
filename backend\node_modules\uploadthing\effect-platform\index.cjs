var platform = require('@effect/platform');
var Effect = require('effect/Effect');
var Layer = require('effect/Layer');
var config_cjs = require('../dist/_internal/config.cjs');
var handler_cjs = require('../dist/_internal/handler.cjs');
var uploadBuilder_cjs = require('../dist/_internal/upload-builder.cjs');
var types_cjs = require('../dist/_internal/types.cjs');

function _interopNamespace(e) {
  if (e && e.__esModule) return e;
  var n = Object.create(null);
  if (e) {
    Object.keys(e).forEach(function (k) {
      if (k !== 'default') {
        var d = Object.getOwnPropertyDescriptor(e, k);
        Object.defineProperty(n, k, d.get ? d : {
          enumerable: true,
          get: function () { return e[k]; }
        });
      }
    });
  }
  n.default = e;
  return n;
}

var Effect__namespace = /*#__PURE__*/_interopNamespace(Effect);
var Layer__namespace = /*#__PURE__*/_interopNamespace(Layer);

const createUploadthing = (opts)=>uploadBuilder_cjs.createBuilder(opts);
const createRouteHandler = (opts)=>{
    const router = Effect__namespace.runSync(handler_cjs.createRequestHandler(opts, "effect-platform"));
    return platform.HttpRouter.provideServiceEffect(router, handler_cjs.AdapterArguments, Effect__namespace.map(platform.HttpServerRequest.HttpServerRequest, (serverRequest)=>({
            req: serverRequest
        }))).pipe(Effect__namespace.provide(Layer__namespace.setConfigProvider(config_cjs.configProvider(opts.config))));
};

Object.defineProperty(exports, "UTFiles", {
  enumerable: true,
  get: function () { return types_cjs.UTFiles; }
});
Object.defineProperty(exports, "experimental_UTRegion", {
  enumerable: true,
  get: function () { return types_cjs.UTRegion; }
});
exports.createRouteHandler = createRouteHandler;
exports.createUploadthing = createUploadthing;
