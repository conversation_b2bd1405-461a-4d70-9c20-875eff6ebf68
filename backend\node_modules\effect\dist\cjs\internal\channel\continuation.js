"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.ContinuationTypeId = exports.ContinuationKImpl = exports.ContinuationFinalizerImpl = void 0;
var Exit = _interopRequireWildcard(require("../../Exit.js"));
var OpCodes = _interopRequireWildcard(require("../opCodes/continuation.js"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
/** @internal */
const ContinuationTypeId = exports.ContinuationTypeId = /*#__PURE__*/Symbol.for("effect/ChannelContinuation");
const continuationVariance = {
  /* c8 ignore next */
  _Env: _ => _,
  /* c8 ignore next */
  _InErr: _ => _,
  /* c8 ignore next */
  _InElem: _ => _,
  /* c8 ignore next */
  _InDone: _ => _,
  /* c8 ignore next */
  _OutErr: _ => _,
  /* c8 ignore next */
  _OutDone: _ => _,
  /* c8 ignore next */
  _OutErr2: _ => _,
  /* c8 ignore next */
  _OutElem: _ => _,
  /* c8 ignore next */
  _OutDone2: _ => _
};
/** @internal */
class ContinuationKImpl {
  onSuccess;
  onHalt;
  _tag = OpCodes.OP_CONTINUATION_K;
  [ContinuationTypeId] = continuationVariance;
  constructor(onSuccess, onHalt) {
    this.onSuccess = onSuccess;
    this.onHalt = onHalt;
  }
  onExit(exit) {
    return Exit.isFailure(exit) ? this.onHalt(exit.cause) : this.onSuccess(exit.value);
  }
}
/** @internal */
exports.ContinuationKImpl = ContinuationKImpl;
class ContinuationFinalizerImpl {
  finalizer;
  _tag = OpCodes.OP_CONTINUATION_FINALIZER;
  [ContinuationTypeId] = continuationVariance;
  constructor(finalizer) {
    this.finalizer = finalizer;
  }
}
exports.ContinuationFinalizerImpl = ContinuationFinalizerImpl;
//# sourceMappingURL=continuation.js.map