var effect = require('effect');
var Arr = require('effect/Array');
var Micro = require('effect/Micro');
var shared = require('@uploadthing/shared');
var clientFuture_cjs = require('../dist/_internal/client-future.cjs');
var deferred_cjs = require('../dist/_internal/deferred.cjs');

function _interopNamespace(e) {
  if (e && e.__esModule) return e;
  var n = Object.create(null);
  if (e) {
    Object.keys(e).forEach(function (k) {
      if (k !== 'default') {
        var d = Object.getOwnPropertyDescriptor(e, k);
        Object.defineProperty(n, k, d.get ? d : {
          enumerable: true,
          get: function () { return e[k]; }
        });
      }
    });
  }
  n.default = e;
  return n;
}

var Arr__namespace = /*#__PURE__*/_interopNamespace(Arr);
var Micro__namespace = /*#__PURE__*/_interopNamespace(Micro);

var version$1 = "7.7.2";

const version = version$1;
/**
 * Generate a typed uploader for a given FileRouter
 * @public
 * @remarks This API is not covered by semver
 */ const future_genUploader = (initOpts)=>{
    const routeRegistry = shared.createIdentityProxy();
    const controllableUpload = async (slug, options)=>{
        const endpoint = typeof slug === "function" ? slug(routeRegistry) : slug;
        const fetchFn = initOpts?.fetch ?? window.fetch;
        const pExit = await clientFuture_cjs.requestPresignedUrls({
            endpoint: String(endpoint),
            files: options.files,
            url: shared.resolveMaybeUrlArg(initOpts?.url),
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            input: options.input,
            headers: options.headers
        }).pipe(Micro__namespace.provideService(shared.FetchContext, fetchFn), (effect)=>Micro__namespace.runPromiseExit(effect, options.signal && {
                signal: options.signal
            }));
        if (pExit._tag === "Failure") throw Micro__namespace.causeSquash(pExit.cause);
        const presigneds = pExit.value;
        const pendingFiles = options.files.map(clientFuture_cjs.makePendingFile);
        options.onEvent({
            type: "presigned-received",
            files: pendingFiles
        });
        const uploads = new Map();
        const uploadEffect = (file, presigned)=>clientFuture_cjs.uploadFile(presigned.url, {
                file,
                files: pendingFiles,
                input: options.input,
                onEvent: options.onEvent,
                XHRImpl: globalThis.XMLHttpRequest
            }).pipe(Micro__namespace.provideService(shared.FetchContext, fetchFn));
        for (const [presigned, file] of effect.Array.zip(presigneds, pendingFiles)){
            file.key = presigned.key;
            file.customId = presigned.customId;
            const deferred = deferred_cjs.createDeferred();
            uploads.set(file, {
                presigned,
                deferred
            });
            void Micro__namespace.runPromiseExit(uploadEffect(file, presigned), {
                signal: deferred.ac.signal
            }).then((result)=>{
                if (result._tag === "Success") {
                    return deferred.resolve(result.value);
                } else if (result.cause._tag === "Interrupt") {
                    throw new shared.UploadPausedError();
                }
                throw Micro__namespace.causeSquash(result.cause);
            }).catch((err)=>{
                if (err instanceof shared.UploadPausedError) return;
                deferred.reject(err);
            });
        }
        /**
     * Pause an ongoing upload
     * @param file The file upload you want to pause. Can be omitted to pause all files
     */ const pauseUpload = (file)=>{
            const files = Arr__namespace.ensure(file ?? options.files);
            for (const file of files){
                const upload = uploads.get(file);
                if (!upload) return;
                if (upload.deferred.ac.signal.aborted) {
                    // Noop if it's already paused
                    return;
                }
                upload.deferred.ac.abort();
            }
        };
        /**
     * Abort an upload
     * @param file The file upload you want to abort. Can be omitted to abort all files
     */ const abortUpload = (file)=>{
            const files = Arr__namespace.ensure(file ?? options.files);
            for (const file of files){
                const upload = uploads.get(file);
                if (!upload) throw "No upload found";
                if (upload.deferred.ac.signal.aborted === false) {
                    // Ensure the upload is paused
                    upload.deferred.ac.abort();
                }
            }
            // Abort the upload
            throw new shared.UploadAbortedError();
        };
        /**
     * Resume a paused upload
     * @param file The file upload you want to resume. Can be omitted to resume all files
     */ const resumeUpload = (file)=>{
            const files = Arr__namespace.ensure(file ?? options.files);
            for (const file of files){
                const upload = uploads.get(file);
                if (!upload) throw "No upload found";
                upload.deferred.ac = new AbortController();
                void Micro__namespace.runPromiseExit(uploadEffect(file, upload.presigned), {
                    signal: upload.deferred.ac.signal
                }).then((result)=>{
                    if (result._tag === "Success") {
                        return upload.deferred.resolve(result.value);
                    } else if (result.cause._tag === "Interrupt") {
                        throw new shared.UploadPausedError();
                    }
                    throw Micro__namespace.causeSquash(result.cause);
                }).catch((err)=>{
                    if (err instanceof shared.UploadPausedError) return;
                    upload.deferred.reject(err);
                });
            }
        };
        /**
     * Wait for an upload to complete
     * @param file The file upload you want to wait for. Can be omitted to wait for all files
     */ const done = async (file)=>{
            const promises = [];
            const files = Arr__namespace.ensure(file ?? options.files);
            for (const file of files){
                const upload = uploads.get(file);
                if (!upload) throw "No upload found";
                promises.push(upload.deferred.promise);
            }
            const results = await Promise.all(promises);
            return file ? results[0] : results;
        };
        return {
            pauseUpload,
            abortUpload,
            resumeUpload,
            done
        };
    };
    const uploadFiles = (slug, opts)=>controllableUpload(slug, opts).then((_)=>_.done());
    return {
        uploadFiles,
        createUpload: controllableUpload,
        /**
     * Identity object that can be used instead of raw strings
     * that allows "Go to definition" in your IDE to bring you
     * to the backend definition of a route.
     */ routeRegistry
    };
};

Object.defineProperty(exports, "UploadAbortedError", {
  enumerable: true,
  get: function () { return shared.UploadAbortedError; }
});
Object.defineProperty(exports, "UploadPausedError", {
  enumerable: true,
  get: function () { return shared.UploadPausedError; }
});
Object.defineProperty(exports, "generateClientDropzoneAccept", {
  enumerable: true,
  get: function () { return shared.generateClientDropzoneAccept; }
});
Object.defineProperty(exports, "generateMimeTypes", {
  enumerable: true,
  get: function () { return shared.generateMimeTypes; }
});
Object.defineProperty(exports, "generatePermittedFileTypes", {
  enumerable: true,
  get: function () { return shared.generatePermittedFileTypes; }
});
exports.future_genUploader = future_genUploader;
exports.version = version;
Object.keys(clientFuture_cjs).forEach(function (k) {
  if (k !== 'default' && !Object.prototype.hasOwnProperty.call(exports, k)) Object.defineProperty(exports, k, {
    enumerable: true,
    get: function () { return clientFuture_cjs[k]; }
  });
});
