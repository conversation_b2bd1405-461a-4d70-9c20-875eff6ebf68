var Data = require('effect/Data');
var Effect = require('effect/Effect');
var shared = require('@uploadthing/shared');

function _interopNamespace(e) {
  if (e && e.__esModule) return e;
  var n = Object.create(null);
  if (e) {
    Object.keys(e).forEach(function (k) {
      if (k !== 'default') {
        var d = Object.getOwnPropertyDescriptor(e, k);
        Object.defineProperty(n, k, d.get ? d : {
          enumerable: true,
          get: function () { return e[k]; }
        });
      }
    });
  }
  n.default = e;
  return n;
}

var Data__namespace = /*#__PURE__*/_interopNamespace(Data);
var Effect__namespace = /*#__PURE__*/_interopNamespace(Effect);

class FileSizeMismatch extends Data__namespace.Error {
    constructor(type, max, actual){
        const reason = `You uploaded a ${type} file that was ${shared.bytesToFileSize(actual)}, but the limit for that type is ${max}`;
        super({
            reason
        }), this._tag = "FileSizeMismatch", this.name = "FileSizeMismatchError";
    }
}
class FileCountMismatch extends Data__namespace.Error {
    constructor(type, boundtype, bound, actual){
        const reason = `You uploaded ${actual} file(s) of type '${type}', but the ${boundtype} for that type is ${bound}`;
        super({
            reason
        }), this._tag = "FileCountMismatch", this.name = "FileCountMismatchError";
    }
}
// Verify that the uploaded files doesn't violate the route config,
// e.g. uploading more videos than allowed, or a file that is larger than allowed.
// This is double-checked on infra side, but we want to fail early to avoid network latency.
const assertFilesMeetConfig = (files, routeConfig)=>Effect__namespace.gen(function*() {
        const counts = {};
        for (const file of files){
            const type = yield* shared.matchFileType(file, shared.objectKeys(routeConfig));
            counts[type] = (counts[type] ?? 0) + 1;
            const sizeLimit = routeConfig[type]?.maxFileSize;
            if (!sizeLimit) {
                return yield* new shared.InvalidRouteConfigError(type, "maxFileSize");
            }
            const sizeLimitBytes = yield* shared.fileSizeToBytes(sizeLimit);
            if (file.size > sizeLimitBytes) {
                return yield* new FileSizeMismatch(type, sizeLimit, file.size);
            }
        }
        for(const _key in counts){
            const key = _key;
            const config = routeConfig[key];
            if (!config) return yield* new shared.InvalidRouteConfigError(key);
            const count = counts[key];
            const min = config.minFileCount;
            const max = config.maxFileCount;
            if (min > max) {
                return yield* new shared.UploadThingError({
                    code: "BAD_REQUEST",
                    message: "Invalid config during file count - minFileCount > maxFileCount",
                    cause: `minFileCount must be less than maxFileCount for key ${key}. got: ${min} > ${max}`
                });
            }
            if (count != null && count < min) {
                return yield* new FileCountMismatch(key, "minimum", min, count);
            }
            if (count != null && count > max) {
                return yield* new FileCountMismatch(key, "maximum", max, count);
            }
        }
        return null;
    });
const extractRouterConfig = (router)=>Effect__namespace.forEach(shared.objectKeys(router), (slug)=>Effect__namespace.map(shared.fillInputRouteConfig(router[slug].routerConfig), (config)=>({
                slug,
                config
            })));

exports.assertFilesMeetConfig = assertFilesMeetConfig;
exports.extractRouterConfig = extractRouterConfig;
