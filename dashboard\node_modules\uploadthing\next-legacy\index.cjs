var Effect = require('effect/Effect');
var handler_cjs = require('../dist/_internal/handler.cjs');
var toWebRequest_cjs = require('../dist/_internal/to-web-request.cjs');
var uploadBuilder_cjs = require('../dist/_internal/upload-builder.cjs');
var types_cjs = require('../dist/_internal/types.cjs');

function _interopNamespace(e) {
  if (e && e.__esModule) return e;
  var n = Object.create(null);
  if (e) {
    Object.keys(e).forEach(function (k) {
      if (k !== 'default') {
        var d = Object.getOwnPropertyDescriptor(e, k);
        Object.defineProperty(n, k, d.get ? d : {
          enumerable: true,
          get: function () { return e[k]; }
        });
      }
    });
  }
  n.default = e;
  return n;
}

var Effect__namespace = /*#__PURE__*/_interopNamespace(Effect);

const createUploadthing = (opts)=>uploadBuilder_cjs.createBuilder(opts);
const createRouteHandler = (opts)=>{
    const handler = handler_cjs.makeAdapterHandler((req, res)=>Effect__namespace.succeed({
            req,
            res
        }), (req)=>toWebRequest_cjs.toWebRequest(req), opts, "nextjs-pages");
    return async (req, res)=>{
        const response = await handler(req, res);
        res.status(response.status);
        for (const [name, value] of response.headers){
            res.setHeader(name, value);
        }
        // FIXME: Should be able to just forward it instead of consuming it first
        return res.json(await response.json());
    };
};

Object.defineProperty(exports, "UTFiles", {
  enumerable: true,
  get: function () { return types_cjs.UTFiles; }
});
Object.defineProperty(exports, "experimental_UTRegion", {
  enumerable: true,
  get: function () { return types_cjs.UTRegion; }
});
exports.createRouteHandler = createRouteHandler;
exports.createUploadthing = createUploadthing;
