{"version": 3, "file": "router.js", "names": ["QS", "_interopRequireWildcard", "require", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "FULL_PATH_REGEXP", "OPTIONAL_PARAM_REGEXP", "make", "options", "RouterImpl", "exports", "constructor", "ignoreTrailingSlash", "ignoreDuplicateSlashes", "caseSensitive", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "routes", "trees", "on", "method", "path", "handler", "optionalParamMatch", "match", "index", "undefined", "assert", "length", "pathFull", "replace", "pathOptional", "removeDuplicateSlashes", "trimLastSlash", "methods", "_on", "all", "httpMethods", "StaticNode", "pattern", "prefix", "currentRoot", "staticChildren", "parentNodePathIndex", "currentNode", "params", "charCodeAt", "isParametricNode", "isWildcardNode", "staticNodePath", "slice", "toLowerCase", "split", "join", "createStaticChild", "isRegexNode", "regexps", "lastParamStartIndex", "j", "charCode", "isRegexParam", "isStaticPart", "isEndOfNode", "paramName", "push", "endOfRegexIndex", "getClosingParenthensePosition", "regexString", "trimRegExpStartAndEnd", "staticPartStartIndex", "nextCharCode", "staticPart", "escapeRegExp", "nodePattern", "nodePath", "regex", "RegExp", "createParametricChild", "createWildcardChild", "Error", "existRoute", "route", "addRoute", "node", "staticNode", "getStatic<PERSON>hild", "isLeafNode", "find", "sanitizedUrl", "querystring", "shouldDecodeParam", "safeDecodeURI", "error", "originPath", "pathIndex", "pathLen", "brothersNodesStack", "handle", "handlerStorage", "createParams", "searchParams", "parse", "getNextNode", "brotherNodeState", "pop", "brotherPathIndex", "splice", "paramsCount", "<PERSON><PERSON><PERSON>", "_tag", "param", "safeDecodeURIComponent", "paramEndIndex", "indexOf", "matchedParameters", "exec", "matchedParam", "HandlerStorage", "handlers", "unconstrained<PERSON><PERSON><PERSON>", "add", "compileCreateParams", "NodeBase", "ParentNode", "findStaticMatchingChild", "static<PERSON><PERSON>d", "char<PERSON>t", "matchPrefix", "label", "setPrefix", "parametricChil<PERSON>n", "wildcard<PERSON><PERSON><PERSON>", "_path", "_pathIndex", "len", "getParametricChild", "child", "isRegex", "source", "staticSuffix", "nodePaths", "ParametricNode", "sort", "child1", "child2", "endsWith", "WildcardNode", "parentNode", "parentPrefix", "childPrefix", "nodeStack", "parametricBrotherNodeIndex", "Set", "_nodeStack", "_paramsCount", "condition", "message", "paramsArray", "paramsObject", "idx", "parentheses", "TypeError", "string", "decodeComponentChar", "highCharCode", "lowCharCode", "shouldDecode", "decodedPath", "decodeURI", "uriComponent", "startIndex", "decoded", "lastIndex", "decodedChar"], "sources": ["../../../src/internal/router.ts"], "sourcesContent": [null], "mappings": ";;;;;;AACA,IAAAA,EAAA,gBAAAC,uBAAA,eAAAC,OAAA;AAAuC,SAAAC,yBAAAC,CAAA;EAAA,yBAAAC,OAAA;EAAA,IAAAC,CAAA,OAAAD,OAAA;IAAAE,CAAA,OAAAF,OAAA;EAAA,QAAAF,wBAAA,YAAAA,CAAAC,CAAA;IAAA,OAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA;EAAA,GAAAF,CAAA;AAAA;AAAA,SAAAH,wBAAAG,CAAA,EAAAE,CAAA;EAAA,KAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA;EAAA,aAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA;IAAAK,OAAA,EAAAL;EAAA;EAAA,IAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA;EAAA,IAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA;EAAA,IAAAQ,CAAA;MAAAC,SAAA;IAAA;IAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA;EAAA,SAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA;IAAA,IAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA;IAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA;EAAA;EAAA,OAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAAA;AAEvC,MAAMW,gBAAgB,GAAG,mBAAmB;AAC5C,MAAMC,qBAAqB,GAAG,sBAAsB;AAUpD;AACO,MAAMC,IAAI,GAAGA,CAClBC,OAAA,GAAwC,EAAE,KACrB,IAAIC,UAAU,CAACD,OAAO,CAAC;AAAAE,OAAA,CAAAH,IAAA,GAAAA,IAAA;AAE9C,MAAME,UAAU;EACdE,YAAYH,OAAA,GAAwC,EAAE;IACpD,IAAI,CAACA,OAAO,GAAG;MACbI,mBAAmB,EAAE,IAAI;MACzBC,sBAAsB,EAAE,IAAI;MAC5BC,aAAa,EAAE,KAAK;MACpBC,cAAc,EAAE,GAAG;MACnB,GAAGP;KACJ;EACH;EAESA,OAAO;EAChBQ,MAAM,GAAiB,EAAE;EACzBC,KAAK,GAA+B,EAAE;EAEtCC,EAAEA,CACAC,MAAiC,EACjCC,IAAsB,EACtBC,OAAU;IAEV,MAAMC,kBAAkB,GAAGF,IAAI,CAACG,KAAK,CAACjB,qBAAqB,CAAC;IAC5D,IAAIgB,kBAAkB,IAAIA,kBAAkB,CAACE,KAAK,KAAKC,SAAS,EAAE;MAChEC,MAAM,CACJN,IAAI,CAACO,MAAM,KAAKL,kBAAkB,CAACE,KAAK,GAAGF,kBAAkB,CAAC,CAAC,CAAC,CAACK,MAAM,EACvE,+DAA+D,CAChE;MAED,MAAMC,QAAQ,GAAGR,IAAI,CAACS,OAAO,CAC3BvB,qBAAqB,EACrB,MAAM,CACa;MACrB,MAAMwB,YAAY,GAAGV,IAAI,CAACS,OAAO,CAC/BvB,qBAAqB,EACrB,IAAI,CACe;MAErB,IAAI,CAACY,EAAE,CAACC,MAAM,EAAES,QAAQ,EAAEP,OAAO,CAAC;MAClC,IAAI,CAACH,EAAE,CAACC,MAAM,EAAEW,YAAY,EAAET,OAAO,CAAC;MACtC;IACF;IAEA,IAAI,IAAI,CAACb,OAAO,CAACK,sBAAsB,EAAE;MACvCO,IAAI,GAAGW,sBAAsB,CAACX,IAAI,CAAC;IACrC;IAEA,IAAI,IAAI,CAACZ,OAAO,CAACI,mBAAmB,EAAE;MACpCQ,IAAI,GAAGY,aAAa,CAACZ,IAAI,CAAC;IAC5B;IAEA,MAAMa,OAAO,GAAG,OAAOd,MAAM,KAAK,QAAQ,GAAG,CAACA,MAAM,CAAC,GAAGA,MAAM;IAC9D,KAAK,MAAMA,MAAM,IAAIc,OAAO,EAAE;MAC5B,IAAI,CAACC,GAAG,CAACf,MAAM,EAAEC,IAAI,EAAEC,OAAO,CAAC;IACjC;EACF;EAEAc,GAAGA,CAACf,IAAsB,EAAEC,OAAU;IACpC,IAAI,CAACH,EAAE,CAACkB,WAAW,EAAEhB,IAAI,EAAEC,OAAO,CAAC;EACrC;EAEQa,GAAGA,CAACf,MAAc,EAAEC,IAAsB,EAAEC,OAAU;IAC5D,IAAI,IAAI,CAACJ,KAAK,CAACE,MAAM,CAAC,KAAKM,SAAS,EAAE;MACpC,IAAI,CAACR,KAAK,CAACE,MAAM,CAAC,GAAG,IAAIkB,UAAU,CAAC,GAAG,CAAC;IAC1C;IAEA,IAAIC,OAAO,GAAGlB,IAAI;IAClB,IAAIkB,OAAO,KAAK,GAAG,IAAI,IAAI,CAACrB,KAAK,CAACE,MAAM,CAAC,CAACoB,MAAM,CAACZ,MAAM,KAAK,CAAC,EAAE;MAC7D,MAAMa,WAAW,GAAG,IAAI,CAACvB,KAAK,CAACE,MAAM,CAAC;MACtC,IAAI,CAACF,KAAK,CAACE,MAAM,CAAC,GAAG,IAAIkB,UAAU,CAAC,EAAE,CAAC;MACvC,IAAI,CAACpB,KAAK,CAACE,MAAM,CAAC,CAACsB,cAAc,CAAC,GAAG,CAAC,GAAGD,WAAW;IACtD;IAEA,IAAIE,mBAAmB,GAAG,IAAI,CAACzB,KAAK,CAACE,MAAM,CAAC,CAACoB,MAAM,CAACZ,MAAM;IAC1D,IAAIgB,WAAW,GAAS,IAAI,CAAC1B,KAAK,CAACE,MAAM,CAAC;IAE1C,MAAMyB,MAAM,GAAG,EAAE;IACjB,KAAK,IAAIzC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAImC,OAAO,CAACX,MAAM,EAAExB,CAAC,EAAE,EAAE;MACxC,IAAImC,OAAO,CAACO,UAAU,CAAC1C,CAAC,CAAC,KAAK,EAAE,IAAImC,OAAO,CAACO,UAAU,CAAC1C,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;QACpE;QACAA,CAAC,EAAE;QACH;MACF;MAEA,MAAM2C,gBAAgB,GACpBR,OAAO,CAACO,UAAU,CAAC1C,CAAC,CAAC,KAAK,EAAE,IAAImC,OAAO,CAACO,UAAU,CAAC1C,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE;MAClE,MAAM4C,cAAc,GAAGT,OAAO,CAACO,UAAU,CAAC1C,CAAC,CAAC,KAAK,EAAE;MAEnD,IACE2C,gBAAgB,IAChBC,cAAc,IACb5C,CAAC,KAAKmC,OAAO,CAACX,MAAM,IAAIxB,CAAC,KAAKuC,mBAAoB,EACnD;QACA,IAAIM,cAAc,GAAGV,OAAO,CAACW,KAAK,CAACP,mBAAmB,EAAEvC,CAAC,CAAC;QAC1D,IAAI,CAAC,IAAI,CAACK,OAAO,CAACM,aAAa,EAAE;UAC/BkC,cAAc,GAAGA,cAAc,CAACE,WAAW,EAAE;QAC/C;QACAF,cAAc,GAAGA,cAAc,CAACG,KAAK,CAAC,IAAI,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;QACrDJ,cAAc,GAAGA,cAAc,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;QACtD;QACAT,WAAW,GAAIA,WAA0B,CAACU,iBAAiB,CACzDL,cAAc,CACf;MACH;MAEA,IAAIF,gBAAgB,EAAE;QACpB,IAAIQ,WAAW,GAAG,KAAK;QACvB,MAAMC,OAAO,GAAG,EAAE;QAElB,IAAIC,mBAAmB,GAAGrD,CAAC,GAAG,CAAC;QAC/B,KAAK,IAAIsD,CAAC,GAAGD,mBAAmB,GAAIC,CAAC,EAAE,EAAE;UACvC,MAAMC,QAAQ,GAAGpB,OAAO,CAACO,UAAU,CAACY,CAAC,CAAC;UAEtC,MAAME,YAAY,GAAGD,QAAQ,KAAK,EAAE;UACpC,MAAME,YAAY,GAAGF,QAAQ,KAAK,EAAE,IAAIA,QAAQ,KAAK,EAAE;UACvD,MAAMG,WAAW,GAAGH,QAAQ,KAAK,EAAE,IAAID,CAAC,KAAKnB,OAAO,CAACX,MAAM;UAE3D,IAAIgC,YAAY,IAAIC,YAAY,IAAIC,WAAW,EAAE;YAC/C,MAAMC,SAAS,GAAGxB,OAAO,CAACW,KAAK,CAACO,mBAAmB,EAAEC,CAAC,CAAC;YACvDb,MAAM,CAACmB,IAAI,CAACD,SAAS,CAAC;YAEtBR,WAAW,GAAGA,WAAW,IAAIK,YAAY,IAAIC,YAAY;YAEzD,IAAID,YAAY,EAAE;cAChB,MAAMK,eAAe,GAAGC,6BAA6B,CAAC3B,OAAO,EAAEmB,CAAC,CAAC;cACjE,MAAMS,WAAW,GAAG5B,OAAO,CAACW,KAAK,CAACQ,CAAC,EAAEO,eAAe,GAAG,CAAC,CAAC;cAEzDT,OAAO,CAACQ,IAAI,CAACI,qBAAqB,CAACD,WAAW,CAAC,CAAC;cAEhDT,CAAC,GAAGO,eAAe,GAAG,CAAC;YACzB,CAAC,MAAM;cACLT,OAAO,CAACQ,IAAI,CAAC,OAAO,CAAC;YACvB;YAEA,MAAMK,oBAAoB,GAAGX,CAAC;YAC9B,OAAOA,CAAC,GAAGnB,OAAO,CAACX,MAAM,EAAE8B,CAAC,EAAE,EAAE;cAC9B,MAAMC,QAAQ,GAAGpB,OAAO,CAACO,UAAU,CAACY,CAAC,CAAC;cACtC,IAAIC,QAAQ,KAAK,EAAE,EAAE;cACrB,IAAIA,QAAQ,KAAK,EAAE,EAAE;gBACnB,MAAMW,YAAY,GAAG/B,OAAO,CAACO,UAAU,CAACY,CAAC,GAAG,CAAC,CAAC;gBAC9C,IAAIY,YAAY,KAAK,EAAE,EAAEZ,CAAC,EAAE,MACvB;cACP;YACF;YAEA,IAAIa,UAAU,GAAGhC,OAAO,CAACW,KAAK,CAACmB,oBAAoB,EAAEX,CAAC,CAAC;YACvD,IAAIa,UAAU,EAAE;cACdA,UAAU,GAAGA,UAAU,CAACnB,KAAK,CAAC,IAAI,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;cAC7CkB,UAAU,GAAGA,UAAU,CAACnB,KAAK,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;cAC9CG,OAAO,CAACQ,IAAI,CAACQ,YAAY,CAACD,UAAU,CAAC,CAAC;YACxC;YAEAd,mBAAmB,GAAGC,CAAC,GAAG,CAAC;YAE3B,IACEI,WAAW,IACXvB,OAAO,CAACO,UAAU,CAACY,CAAC,CAAC,KAAK,EAAE,IAC5BA,CAAC,KAAKnB,OAAO,CAACX,MAAM,EACpB;cACA,MAAM6C,WAAW,GAAGlB,WAAW,GAAG,IAAI,GAAGgB,UAAU,GAAGA,UAAU;cAChE,MAAMG,QAAQ,GAAGnC,OAAO,CAACW,KAAK,CAAC9C,CAAC,EAAEsD,CAAC,CAAC;cAEpCnB,OAAO,GAAGA,OAAO,CAACW,KAAK,CAAC,CAAC,EAAE9C,CAAC,GAAG,CAAC,CAAC,GAAGqE,WAAW,GAAGlC,OAAO,CAACW,KAAK,CAACQ,CAAC,CAAC;cAClEtD,CAAC,IAAIqE,WAAW,CAAC7C,MAAM;cAEvB,MAAM+C,KAAK,GAAGpB,WAAW,GACrB,IAAIqB,MAAM,CAAC,GAAG,GAAGpB,OAAO,CAACH,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,GACxC3B,SAAS;cACbkB,WAAW,GAAIA,WAA0B,CAACiC,qBAAqB,CAC7DF,KAAK,EACLJ,UAAU,EACVG,QAAQ,CACT;cACD/B,mBAAmB,GAAGvC,CAAC,GAAG,CAAC;cAC3B;YACF;UACF;QACF;MACF,CAAC,MAAM,IAAI4C,cAAc,EAAE;QACzB;QACAH,MAAM,CAACmB,IAAI,CAAC,GAAG,CAAC;QAChBpB,WAAW,GAAIA,WAA0B,CAACkC,mBAAmB,EAAE;QAC/DnC,mBAAmB,GAAGvC,CAAC,GAAG,CAAC;QAE3B,IAAIA,CAAC,KAAKmC,OAAO,CAACX,MAAM,GAAG,CAAC,EAAE;UAC5B,MAAM,IAAImD,KAAK,CAAC,kDAAkD,CAAC;QACrE;MACF;IACF;IAEA,IAAI,CAAC,IAAI,CAACtE,OAAO,CAACM,aAAa,EAAE;MAC/BwB,OAAO,GAAGA,OAAO,CAACY,WAAW,EAAsB;IACrD;IAEA,IAAIZ,OAAO,KAAK,GAAG,EAAE;MACnBA,OAAO,GAAG,IAAI;IAChB;IAEA,KAAK,MAAMyC,UAAU,IAAI,IAAI,CAAC/D,MAAM,EAAE;MACpC,IAAI+D,UAAU,CAAC5D,MAAM,KAAKA,MAAM,IAAI4D,UAAU,CAACzC,OAAO,KAAKA,OAAO,EAAE;QAClE,MAAM,IAAIwC,KAAK,CACb,WAAW3D,MAAM,iCAAiCmB,OAAO,GAAG,CAC7D;MACH;IACF;IAEA,MAAM0C,KAAK,GAAG;MAAE7D,MAAM;MAAEC,IAAI;MAAEkB,OAAO;MAAEM,MAAM;MAAEvB;IAAO,CAAE;IACxD,IAAI,CAACL,MAAM,CAAC+C,IAAI,CAACiB,KAAK,CAAC;IACvBrC,WAAW,CAACsC,QAAQ,CAACD,KAAK,CAAC;EAC7B;EAEAxF,GAAGA,CAAC2B,MAAc,EAAEC,IAAY;IAC9B,MAAM8D,IAAI,GAAG,IAAI,CAACjE,KAAK,CAACE,MAAM,CAAC;IAC/B,IAAI+D,IAAI,KAAKzD,SAAS,EAAE;MACtB,OAAO,KAAK;IACd;IAEA,MAAM0D,UAAU,GAAGD,IAAI,CAACE,cAAc,CAAChE,IAAI,CAAC;IAC5C,IAAI+D,UAAU,KAAK1D,SAAS,EAAE;MAC5B,OAAO,KAAK;IACd;IAEA,OAAO0D,UAAU,CAACE,UAAU;EAC9B;EAEAC,IAAIA,CAACnE,MAAc,EAAEC,IAAY;IAC/B,IAAIuB,WAAW,GAAqB,IAAI,CAAC1B,KAAK,CAACE,MAAM,CAAC;IACtD,IAAIwB,WAAW,KAAKlB,SAAS,EAAE,OAAOA,SAAS;IAE/C,IAAIL,IAAI,CAACyB,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;MAC7B;MACAzB,IAAI,GAAGA,IAAI,CAACS,OAAO,CAACxB,gBAAgB,EAAE,GAAG,CAAC;IAC5C;IAEA;IACA;IACA;IACA,IAAI,IAAI,CAACG,OAAO,CAACK,sBAAsB,EAAE;MACvCO,IAAI,GAAGW,sBAAsB,CAACX,IAAI,CAAC;IACrC;IAEA,IAAImE,YAAY;IAChB,IAAIC,WAAW;IACf,IAAIC,iBAAiB;IAErB,IAAI;MACFF,YAAY,GAAGG,aAAa,CAACtE,IAAI,CAAC;MAClCA,IAAI,GAAGmE,YAAY,CAACnE,IAAI;MACxBoE,WAAW,GAAGD,YAAY,CAACC,WAAW;MACtCC,iBAAiB,GAAGF,YAAY,CAACE,iBAAiB;IACpD,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd,OAAOlE,SAAS;IAClB;IAEA,IAAI,IAAI,CAACjB,OAAO,CAACI,mBAAmB,EAAE;MACpCQ,IAAI,GAAGY,aAAa,CAACZ,IAAI,CAAC;IAC5B;IAEA,MAAMwE,UAAU,GAAGxE,IAAI;IAEvB,IAAI,IAAI,CAACZ,OAAO,CAACM,aAAa,KAAK,KAAK,EAAE;MACxCM,IAAI,GAAGA,IAAI,CAAC8B,WAAW,EAAE;IAC3B;IAEA,MAAMnC,cAAc,GAAG,IAAI,CAACP,OAAO,CAACO,cAAc;IAElD,IAAI8E,SAAS,GAAIlD,WAA0B,CAACJ,MAAM,CAACZ,MAAM;IACzD,MAAMiB,MAAM,GAAG,EAAE;IACjB,MAAMkD,OAAO,GAAG1E,IAAI,CAACO,MAAM;IAE3B,MAAMoE,kBAAkB,GAAuB,EAAE;IAEjD,OAAO,IAAI,EAAE;MACX,IAAIF,SAAS,KAAKC,OAAO,IAAInD,WAAW,CAAC0C,UAAU,EAAE;QACnD,MAAMW,MAAM,GAAGrD,WAAW,CAACsD,cAAc,EAAEX,IAAI,EAAE;QACjD,IAAIU,MAAM,KAAKvE,SAAS,EAAE;UACxB,OAAO;YACLJ,OAAO,EAAE2E,MAAM,CAAC3E,OAAY;YAC5BuB,MAAM,EAAEoD,MAAM,CAACE,YAAY,CAACtD,MAAM,CAAC;YACnCuD,YAAY,EAAErH,EAAE,CAACsH,KAAK,CAACZ,WAAW;WAC1B;QACZ;MACF;MAEA,IAAIN,IAAI,GAAqBvC,WAAW,CAAC0D,WAAW,CAClDjF,IAAI,EACJyE,SAAS,EACTE,kBAAkB,EAClBnD,MAAM,CAACjB,MAAM,CACd;MAED,IAAIuD,IAAI,KAAKzD,SAAS,EAAE;QACtB,IAAIsE,kBAAkB,CAACpE,MAAM,KAAK,CAAC,EAAE;UACnC,OAAOF,SAAS;QAClB;QAEA,MAAM6E,gBAAgB,GAAGP,kBAAkB,CAACQ,GAAG,EAAG;QAClDV,SAAS,GAAGS,gBAAgB,CAACE,gBAAgB;QAC7C5D,MAAM,CAAC6D,MAAM,CAACH,gBAAgB,CAACI,WAAW,CAAC;QAC3CxB,IAAI,GAAGoB,gBAAgB,CAACK,WAAW;MACrC;MAEAhE,WAAW,GAAGuC,IAAI;MAElB;MACA,IAAIvC,WAAW,CAACiE,IAAI,KAAK,YAAY,EAAE;QACrCf,SAAS,IAAIlD,WAAW,CAACJ,MAAM,CAACZ,MAAM;QACtC;MACF;MAEA,IAAIgB,WAAW,CAACiE,IAAI,KAAK,cAAc,EAAE;QACvC,IAAIC,KAAK,GAAGjB,UAAU,CAAC3C,KAAK,CAAC4C,SAAS,CAAC;QACvC,IAAIJ,iBAAiB,EAAE;UACrBoB,KAAK,GAAGC,sBAAsB,CAACD,KAAK,CAAC;QACvC;QAEAjE,MAAM,CAACmB,IAAI,CAAC8C,KAAK,CAAC;QAClBhB,SAAS,GAAGC,OAAO;QACnB;MACF;MAEA,IAAInD,WAAW,CAACiE,IAAI,KAAK,gBAAgB,EAAE;QACzC,IAAIG,aAAa,GAAGnB,UAAU,CAACoB,OAAO,CAAC,GAAG,EAAEnB,SAAS,CAAC;QACtD,IAAIkB,aAAa,KAAK,CAAC,CAAC,EAAE;UACxBA,aAAa,GAAGjB,OAAO;QACzB;QAEA,IAAIe,KAAK,GAAGjB,UAAU,CAAC3C,KAAK,CAAC4C,SAAS,EAAEkB,aAAa,CAAC;QACtD,IAAItB,iBAAiB,EAAE;UACrBoB,KAAK,GAAGC,sBAAsB,CAACD,KAAK,CAAC;QACvC;QAEA,IAAIlE,WAAW,CAAC+B,KAAK,KAAKjD,SAAS,EAAE;UACnC,MAAMwF,iBAAiB,GACrBtE,WAAW,CAAC+B,KAAK,CAACwC,IAAI,CAACL,KAAK,CAAC;UAC/B,IAAII,iBAAiB,KAAK,IAAI,EAAE;UAEhC,KAAK,IAAI9G,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8G,iBAAiB,CAACtF,MAAM,EAAExB,CAAC,EAAE,EAAE;YACjD,MAAMgH,YAAY,GAAWF,iBAAiB,CAAC9G,CAAC,CAAC;YACjD,IAAIgH,YAAY,CAACxF,MAAM,GAAGZ,cAAc,EAAE;cACxC,OAAOU,SAAS;YAClB;YACAmB,MAAM,CAACmB,IAAI,CAACoD,YAAY,CAAC;UAC3B;QACF,CAAC,MAAM;UACL,IAAIN,KAAK,CAAClF,MAAM,GAAGZ,cAAc,EAAE;YACjC,OAAOU,SAAS;UAClB;UACAmB,MAAM,CAACmB,IAAI,CAAC8C,KAAK,CAAC;QACpB;QAEAhB,SAAS,GAAGkB,aAAa;MAC3B;IACF;EACF;;AAeF,MAAMK,cAAc;EACTC,QAAQ,GAAmB,EAAE;EACtCC,oBAAoB;EAEpBhC,IAAIA,CAAA;IACF,OAAO,IAAI,CAACgC,oBAAoB;EAClC;EAEAC,GAAGA,CAACvC,KAAY;IACd,MAAM3D,OAAO,GAAY;MACvBuB,MAAM,EAAEoC,KAAK,CAACpC,MAAM;MACpBvB,OAAO,EAAE2D,KAAK,CAAC3D,OAAO;MACtB6E,YAAY,EAAEsB,mBAAmB,CAACxC,KAAK,CAACpC,MAAM;KAC/C;IACD,IAAI,CAACyE,QAAQ,CAACtD,IAAI,CAAC1C,OAAO,CAAC;IAC3B,IAAI,CAACiG,oBAAoB,GAAG,IAAI,CAACD,QAAQ,CAAC,CAAC,CAAC;EAC9C;;AAeF,MAAeI,QAAQ;EACrBpC,UAAU,GAAG,KAAK;EAClBrE,MAAM;EACNiF,cAAc;EAEdhB,QAAQA,CAACD,KAAY;IACnB,IAAI,IAAI,CAAChE,MAAM,KAAKS,SAAS,EAAE;MAC7B,IAAI,CAACT,MAAM,GAAG,CAACgE,KAAK,CAAC;IACvB,CAAC,MAAM;MACL,IAAI,CAAChE,MAAM,CAAC+C,IAAI,CAACiB,KAAK,CAAC;IACzB;IAEA,IAAI,IAAI,CAACiB,cAAc,KAAKxE,SAAS,EAAE;MACrC,IAAI,CAACwE,cAAc,GAAG,IAAImB,cAAc,EAAE;IAC5C;IACA,IAAI,CAAC/B,UAAU,GAAG,IAAI;IACtB,IAAI,CAACY,cAAc,CAACsB,GAAG,CAACvC,KAAK,CAAC;EAChC;;AAUF,MAAe0C,UAAW,SAAQD,QAAQ;EAC/BhF,cAAc,GAA+B,EAAE;EAExDkF,uBAAuBA,CACrBvG,IAAY,EACZyE,SAAiB;IAEjB,MAAM+B,WAAW,GAAG,IAAI,CAACnF,cAAc,CAACrB,IAAI,CAACyG,MAAM,CAAChC,SAAS,CAAC,CAAC;IAC/D,IACE+B,WAAW,KAAKnG,SAAS,IACzB,CAACmG,WAAW,CAACE,WAAW,CAAC1G,IAAI,EAAEyE,SAAS,CAAC,EACzC;MACA,OAAOpE,SAAS;IAClB;IACA,OAAOmG,WAAW;EACpB;EAEAxC,cAAcA,CAAChE,IAAY,EAAEyE,SAAS,GAAG,CAAC;IACxC,IAAIzE,IAAI,CAACO,MAAM,KAAKkE,SAAS,EAAE;MAC7B,OAAO,IAAW;IACpB;IAEA,MAAM+B,WAAW,GAAG,IAAI,CAACD,uBAAuB,CAACvG,IAAI,EAAEyE,SAAS,CAAC;IACjE,IAAI+B,WAAW,KAAKnG,SAAS,EAAE;MAC7B,OAAOA,SAAS;IAClB;IAEA,OAAOmG,WAAW,CAACxC,cAAc,CAC/BhE,IAAI,EACJyE,SAAS,GAAG+B,WAAW,CAACrF,MAAM,CAACZ,MAAM,CACtC;EACH;EAEA0B,iBAAiBA,CAACjC,IAAY;IAC5B,IAAIA,IAAI,CAACO,MAAM,KAAK,CAAC,EAAE;MACrB,OAAO,IAAW;IACpB;IAEA,IAAIiG,WAAW,GAAG,IAAI,CAACnF,cAAc,CAACrB,IAAI,CAACyG,MAAM,CAAC,CAAC,CAAC,CAAC;IACrD,IAAID,WAAW,EAAE;MACf,IAAIzH,CAAC,GAAG,CAAC;MACT,OAAOA,CAAC,GAAGyH,WAAW,CAACrF,MAAM,CAACZ,MAAM,EAAExB,CAAC,EAAE,EAAE;QACzC,IAAIiB,IAAI,CAACyB,UAAU,CAAC1C,CAAC,CAAC,KAAKyH,WAAW,CAACrF,MAAM,CAACM,UAAU,CAAC1C,CAAC,CAAC,EAAE;UAC3DyH,WAAW,GAAGA,WAAW,CAACzE,KAAK,CAAC,IAAI,EAAEhD,CAAC,CAAC;UACxC;QACF;MACF;MACA,OAAOyH,WAAW,CAACvE,iBAAiB,CAACjC,IAAI,CAAC6B,KAAK,CAAC9C,CAAC,CAAC,CAAC;IACrD;IAEA,MAAM4H,KAAK,GAAG3G,IAAI,CAACyG,MAAM,CAAC,CAAC,CAAC;IAC5B,IAAI,CAACpF,cAAc,CAACsF,KAAK,CAAC,GAAG,IAAI1F,UAAU,CAACjB,IAAI,CAAC;IACjD,OAAO,IAAI,CAACqB,cAAc,CAACsF,KAAK,CAAC;EACnC;;AAGF,MAAM1F,UAAW,SAAQqF,UAAU;EACxBd,IAAI,GAAG,YAAY;EAC5BjG,YAAY4B,MAAc;IACxB,KAAK,EAAE;IACP,IAAI,CAACyF,SAAS,CAACzF,MAAM,CAAC;EACxB;EAEAA,MAAM;EACNuF,WAAW;EACFG,kBAAkB,GAA0B,EAAE;EAEvDC,aAAa;EAELF,SAASA,CAACzF,MAAc;IAC9B,IAAI,CAACA,MAAM,GAAGA,MAAM;IAEpB,IAAIA,MAAM,CAACZ,MAAM,KAAK,CAAC,EAAE;MACvB,IAAI,CAACmG,WAAW,GAAG,CAACK,KAAK,EAAEC,UAAU,KAAK,IAAI;IAChD,CAAC,MAAM;MACL,MAAMC,GAAG,GAAG9F,MAAM,CAACZ,MAAM;MACzB,IAAI,CAACmG,WAAW,GAAG,UAAU1G,IAAI,EAAEyE,SAAS;QAC1C,KAAK,IAAI1F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkI,GAAG,EAAElI,CAAC,EAAE,EAAE;UAC5B,IAAIiB,IAAI,CAACyB,UAAU,CAACgD,SAAS,GAAG1F,CAAC,CAAC,KAAK,IAAI,CAACoC,MAAM,CAACM,UAAU,CAAC1C,CAAC,CAAC,EAAE;YAChE,OAAO,KAAK;UACd;QACF;QACA,OAAO,IAAI;MACb,CAAC;IACH;EACF;EAEAmI,kBAAkBA,CAAC5D,KAAyB;IAC1C,IAAIA,KAAK,KAAKjD,SAAS,EAAE;MACvB,OAAO,IAAI,CAACwG,kBAAkB,CAAC3C,IAAI,CAACiD,KAAK,IAAIA,KAAK,CAACC,OAAO,KAAK,KAAK,CAAC;IACvE;IAEA,MAAMC,MAAM,GAAG/D,KAAK,CAAC+D,MAAM;IAC3B,OAAO,IAAI,CAACR,kBAAkB,CAAC3C,IAAI,CAACiD,KAAK,IAAG;MAC1C,IAAIA,KAAK,CAAC7D,KAAK,KAAKjD,SAAS,EAAE;QAC7B,OAAO,KAAK;MACd;MACA,OAAO8G,KAAK,CAAC7D,KAAK,CAAC+D,MAAM,KAAKA,MAAM;IACtC,CAAC,CAAC;EACJ;EAEA7D,qBAAqBA,CACnBF,KAAyB,EACzBgE,YAAgC,EAChCjE,QAAgB;IAEhB,IAAI8D,KAAK,GAAG,IAAI,CAACD,kBAAkB,CAAC5D,KAAK,CAAC;IAC1C,IAAI6D,KAAK,KAAK9G,SAAS,EAAE;MACvB8G,KAAK,CAACI,SAAS,CAACpB,GAAG,CAAC9C,QAAQ,CAAC;MAC7B,OAAO8D,KAAK;IACd;IAEAA,KAAK,GAAG,IAAIK,cAAc,CAAClE,KAAK,EAAEgE,YAAY,EAAEjE,QAAQ,CAAC;IACzD,IAAI,CAACwD,kBAAkB,CAAClE,IAAI,CAACwE,KAAK,CAAC;IACnC,IAAI,CAACN,kBAAkB,CAACY,IAAI,CAAC,CAACC,MAAM,EAAEC,MAAM,KAAI;MAC9C,IAAI,CAACD,MAAM,CAACN,OAAO,EAAE,OAAO,CAAC;MAC7B,IAAI,CAACO,MAAM,CAACP,OAAO,EAAE,OAAO,CAAC,CAAC;MAE9B,IAAIM,MAAM,CAACJ,YAAY,KAAKjH,SAAS,EAAE,OAAO,CAAC;MAC/C,IAAIsH,MAAM,CAACL,YAAY,KAAKjH,SAAS,EAAE,OAAO,CAAC,CAAC;MAEhD,IAAIsH,MAAM,CAACL,YAAY,CAACM,QAAQ,CAACF,MAAM,CAACJ,YAAY,CAAC,EAAE,OAAO,CAAC;MAC/D,IAAII,MAAM,CAACJ,YAAY,CAACM,QAAQ,CAACD,MAAM,CAACL,YAAY,CAAC,EAAE,OAAO,CAAC,CAAC;MAEhE,OAAO,CAAC;IACV,CAAC,CAAC;IAEF,OAAOH,KAAK;EACd;EAEA1D,mBAAmBA,CAAA;IACjB,IAAI,IAAI,CAACqD,aAAa,KAAKzG,SAAS,EAAE;MACpC,IAAI,CAACyG,aAAa,GAAG,IAAIe,YAAY,EAAE;IACzC;IACA,OAAO,IAAI,CAACf,aAAa;EAC3B;EAEA/E,KAAKA,CAAC+F,UAAsB,EAAEvH,MAAc;IAC1C,MAAMwH,YAAY,GAAG,IAAI,CAAC5G,MAAM,CAACU,KAAK,CAAC,CAAC,EAAEtB,MAAM,CAAC;IACjD,MAAMyH,WAAW,GAAG,IAAI,CAAC7G,MAAM,CAACU,KAAK,CAACtB,MAAM,CAAC;IAE7C,IAAI,CAACqG,SAAS,CAACoB,WAAW,CAAC;IAE3B,MAAMjE,UAAU,GAAG,IAAI9C,UAAU,CAAC8G,YAAY,CAAC;IAC/ChE,UAAU,CAAC1C,cAAc,CAAC2G,WAAW,CAACvB,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;IACvDqB,UAAU,CAACzG,cAAc,CAAC0G,YAAY,CAACtB,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG1C,UAAU;IAE9D,OAAOA,UAAU;EACnB;EAEAkB,WAAWA,CACTjF,IAAY,EACZyE,SAAiB,EACjBwD,SAA6B,EAC7B3C,WAAmB;IAEnB,IAAIxB,IAAI,GAAqB,IAAI,CAACyC,uBAAuB,CAACvG,IAAI,EAAEyE,SAAS,CAAC;IAC1E,IAAIyD,0BAA0B,GAAG,CAAC;IAElC,IAAIpE,IAAI,KAAKzD,SAAS,EAAE;MACtB,IAAI,IAAI,CAACwG,kBAAkB,CAACtG,MAAM,KAAK,CAAC,EAAE;QACxC,OAAO,IAAI,CAACuG,aAAa;MAC3B;MAEAhD,IAAI,GAAG,IAAI,CAAC+C,kBAAkB,CAAC,CAAC,CAAC;MACjCqB,0BAA0B,GAAG,CAAC;IAChC;IAEA,IAAI,IAAI,CAACpB,aAAa,KAAKzG,SAAS,EAAE;MACpC4H,SAAS,CAACtF,IAAI,CAAC;QACb2C,WAAW;QACXF,gBAAgB,EAAEX,SAAS;QAC3Bc,WAAW,EAAE,IAAI,CAACuB;OACnB,CAAC;IACJ;IAEA,KACE,IAAI/H,CAAC,GAAG,IAAI,CAAC8H,kBAAkB,CAACtG,MAAM,GAAG,CAAC,EAC1CxB,CAAC,IAAImJ,0BAA0B,EAC/BnJ,CAAC,EAAE,EACH;MACAkJ,SAAS,CAACtF,IAAI,CAAC;QACb2C,WAAW;QACXF,gBAAgB,EAAEX,SAAS;QAC3Bc,WAAW,EAAE,IAAI,CAACsB,kBAAkB,CAAC9H,CAAC;OACvC,CAAC;IACJ;IAEA,OAAO+E,IAAI;EACb;;AAGF,MAAM0D,cAAe,SAAQlB,UAAU;EAG1BhD,KAAA;EACAgE,YAAA;EAHF9B,IAAI,GAAG,gBAAgB;EAChCjG,YACW+D,KAAyB,EACzBgE,YAAgC,EACzCjE,QAAgB;IAEhB,KAAK,EAAE;IAJE,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAgE,YAAY,GAAZA,YAAY;IAIrB,IAAI,CAACF,OAAO,GAAG,CAAC,CAAC9D,KAAK;IACtB,IAAI,CAACiE,SAAS,GAAG,IAAIY,GAAG,CAAC,CAAC9E,QAAQ,CAAC,CAAC;EACtC;EAES+D,OAAO;EACPG,SAAS;EAElBtC,WAAWA,CAACjF,IAAY,EAAEyE,SAAiB;IACzC,OAAO,IAAI,CAAC8B,uBAAuB,CAACvG,IAAI,EAAEyE,SAAS,CAAC;EACtD;;AAGF,MAAMoD,YAAa,SAAQxB,QAAQ;EACxBb,IAAI,GAAG,cAAc;EAC9BP,WAAWA,CACT8B,KAAa,EACbC,UAAkB,EAClBoB,UAAe,EACfC,YAAoB;IAEpB,OAAOhI,SAAS;EAClB;;AAQF,MAAMC,MAAM,GAAWA,CAACgI,SAAS,EAAEC,OAAO,KAAI;EAC5C,IAAI,CAACD,SAAS,EAAE;IACd,MAAM,IAAI5E,KAAK,CAAC6E,OAAO,CAAC;EAC1B;AACF,CAAC;AAED,SAAS5H,sBAAsBA,CAACX,IAAY;EAC1C,OAAOA,IAAI,CAACS,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAqB;AACxD;AAEA,SAASG,aAAaA,CAACZ,IAAY;EACjC,IAAIA,IAAI,CAACO,MAAM,GAAG,CAAC,IAAIP,IAAI,CAACyB,UAAU,CAACzB,IAAI,CAACO,MAAM,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;IAC9D,OAAOP,IAAI,CAAC6B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAqB;EAC9C;EACA,OAAO7B,IAAwB;AACjC;AAEA,SAASoG,mBAAmBA,CAC1B5E,MAA6B;EAE7B,MAAMyF,GAAG,GAAGzF,MAAM,CAACjB,MAAM;EACzB,OAAO,UAAUiI,WAAW;IAC1B,MAAMC,YAAY,GAA2B,EAAE;IAC/C,KAAK,IAAI1J,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkI,GAAG,EAAElI,CAAC,EAAE,EAAE;MAC5B0J,YAAY,CAACjH,MAAM,CAACzC,CAAC,CAAC,CAAC,GAAGyJ,WAAW,CAACzJ,CAAC,CAAC;IAC1C;IACA,OAAO0J,YAAY;EACrB,CAAC;AACH;AAEA,SAAS5F,6BAA6BA,CAAC7C,IAAY,EAAE0I,GAAW;EAC9D;EACA;EACA;EAEA,IAAIC,WAAW,GAAG,CAAC;EAEnB,OAAOD,GAAG,GAAG1I,IAAI,CAACO,MAAM,EAAE;IACxBmI,GAAG,EAAE;IAEL;IACA,IAAI1I,IAAI,CAAC0I,GAAG,CAAC,KAAK,IAAI,EAAE;MACtBA,GAAG,EAAE;MACL;IACF;IAEA,IAAI1I,IAAI,CAAC0I,GAAG,CAAC,KAAK,GAAG,EAAE;MACrBC,WAAW,EAAE;IACf,CAAC,MAAM,IAAI3I,IAAI,CAAC0I,GAAG,CAAC,KAAK,GAAG,EAAE;MAC5BC,WAAW,EAAE;IACf;IAEA,IAAI,CAACA,WAAW,EAAE,OAAOD,GAAG;EAC9B;EAEA,MAAM,IAAIE,SAAS,CAAC,gCAAgC,GAAG5I,IAAI,GAAG,GAAG,CAAC;AACpE;AAEA,SAAS+C,qBAAqBA,CAACD,WAAmB;EAChD;EACA,IAAIA,WAAW,CAACrB,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;IACpCqB,WAAW,GAAGA,WAAW,CAACjB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGiB,WAAW,CAACjB,KAAK,CAAC,CAAC,CAAC;EAC9D;EAEA,IAAIiB,WAAW,CAACrB,UAAU,CAACqB,WAAW,CAACvC,MAAM,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;IACzDuC,WAAW,GACTA,WAAW,CAACjB,KAAK,CAAC,CAAC,EAAEiB,WAAW,CAACvC,MAAM,GAAG,CAAC,CAAC,GAC5CuC,WAAW,CAACjB,KAAK,CAACiB,WAAW,CAACvC,MAAM,GAAG,CAAC,CAAC;EAC7C;EAEA,OAAOuC,WAAW;AACpB;AAEA,SAASK,YAAYA,CAAC0F,MAAc;EAClC,OAAOA,MAAM,CAACpI,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC;AACtD;AAEA;AACA;AACA,SAASqI,mBAAmBA,CAACC,YAAoB,EAAEC,WAAmB;EACpE,IAAID,YAAY,KAAK,EAAE,EAAE;IACvB,IAAIC,WAAW,KAAK,EAAE,EAAE,OAAO,GAAG;IAElC,IAAIA,WAAW,KAAK,EAAE,EAAE,OAAO,GAAG;IAClC,IAAIA,WAAW,KAAK,EAAE,EAAE,OAAO,GAAG;IAClC,IAAIA,WAAW,KAAK,EAAE,EAAE,OAAO,GAAG;IAClC,IAAIA,WAAW,KAAK,EAAE,EAAE,OAAO,GAAG;IAClC,IAAIA,WAAW,KAAK,EAAE,EAAE,OAAO,GAAG;IAClC,IAAIA,WAAW,KAAK,EAAE,EAAE,OAAO,GAAG;IAClC,IAAIA,WAAW,KAAK,EAAE,EAAE,OAAO,GAAG;IAClC,IAAIA,WAAW,KAAK,EAAE,EAAE,OAAO,GAAG;IAClC,IAAIA,WAAW,KAAK,GAAG,EAAE,OAAO,GAAG;IACnC,OAAO3I,SAAS;EAClB;EACA,IAAI0I,YAAY,KAAK,EAAE,EAAE;IACvB,IAAIC,WAAW,KAAK,EAAE,EAAE,OAAO,GAAG;IAClC,IAAIA,WAAW,KAAK,EAAE,EAAE,OAAO,GAAG;IAClC,IAAIA,WAAW,KAAK,EAAE,EAAE,OAAO,GAAG;IAClC,IAAIA,WAAW,KAAK,EAAE,EAAE,OAAO,GAAG;IAClC,IAAIA,WAAW,KAAK,EAAE,EAAE,OAAO,GAAG;IAClC,IAAIA,WAAW,KAAK,GAAG,EAAE,OAAO,GAAG;IACnC,IAAIA,WAAW,KAAK,EAAE,EAAE,OAAO,GAAG;IAClC,IAAIA,WAAW,KAAK,GAAG,EAAE,OAAO,GAAG;IACnC,OAAO3I,SAAS;EAClB;EACA,IAAI0I,YAAY,KAAK,EAAE,IAAIC,WAAW,KAAK,EAAE,EAAE;IAC7C,OAAO,GAAG;EACZ;EACA,OAAO3I,SAAS;AAClB;AAEA,SAASiE,aAAaA,CAACtE,IAAY;EACjC,IAAIiJ,YAAY,GAAG,KAAK;EACxB,IAAI5E,iBAAiB,GAAG,KAAK;EAE7B,IAAID,WAAW,GAAG,EAAE;EAEpB,KAAK,IAAIrF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiB,IAAI,CAACO,MAAM,EAAExB,CAAC,EAAE,EAAE;IACpC,MAAMuD,QAAQ,GAAGtC,IAAI,CAACyB,UAAU,CAAC1C,CAAC,CAAC;IAEnC,IAAIuD,QAAQ,KAAK,EAAE,EAAE;MACnB,MAAMyG,YAAY,GAAG/I,IAAI,CAACyB,UAAU,CAAC1C,CAAC,GAAG,CAAC,CAAC;MAC3C,MAAMiK,WAAW,GAAGhJ,IAAI,CAACyB,UAAU,CAAC1C,CAAC,GAAG,CAAC,CAAC;MAE1C,IAAI+J,mBAAmB,CAACC,YAAY,EAAEC,WAAW,CAAC,KAAK3I,SAAS,EAAE;QAChE4I,YAAY,GAAG,IAAI;MACrB,CAAC,MAAM;QACL5E,iBAAiB,GAAG,IAAI;QACxB;QACA,IAAI0E,YAAY,KAAK,EAAE,IAAIC,WAAW,KAAK,EAAE,EAAE;UAC7CC,YAAY,GAAG,IAAI;UACnBjJ,IAAI,GAAGA,IAAI,CAAC6B,KAAK,CAAC,CAAC,EAAE9C,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAGiB,IAAI,CAAC6B,KAAK,CAAC9C,CAAC,GAAG,CAAC,CAAC;UACtDA,CAAC,IAAI,CAAC;QACR;QACAA,CAAC,IAAI,CAAC;MACR;MACA;MACA;MACA;IACF,CAAC,MAAM,IAAIuD,QAAQ,KAAK,EAAE,IAAIA,QAAQ,KAAK,EAAE,IAAIA,QAAQ,KAAK,EAAE,EAAE;MAChE8B,WAAW,GAAGpE,IAAI,CAAC6B,KAAK,CAAC9C,CAAC,GAAG,CAAC,CAAC;MAC/BiB,IAAI,GAAGA,IAAI,CAAC6B,KAAK,CAAC,CAAC,EAAE9C,CAAC,CAAC;MACvB;IACF;EACF;EACA,MAAMmK,WAAW,GAAGD,YAAY,GAAGE,SAAS,CAACnJ,IAAI,CAAC,GAAGA,IAAI;EACzD,OAAO;IAAEA,IAAI,EAAEkJ,WAAW;IAAE9E,WAAW;IAAEC;EAAiB,CAAW;AACvE;AAEA,SAASqB,sBAAsBA,CAAC0D,YAAoB;EAClD,MAAMC,UAAU,GAAGD,YAAY,CAACxD,OAAO,CAAC,GAAG,CAAC;EAC5C,IAAIyD,UAAU,KAAK,CAAC,CAAC,EAAE,OAAOD,YAAY;EAE1C,IAAIE,OAAO,GAAG,EAAE;EAChB,IAAIC,SAAS,GAAGF,UAAU;EAE1B,KAAK,IAAItK,CAAC,GAAGsK,UAAU,EAAEtK,CAAC,GAAGqK,YAAY,CAAC7I,MAAM,EAAExB,CAAC,EAAE,EAAE;IACrD,IAAIqK,YAAY,CAAC3H,UAAU,CAAC1C,CAAC,CAAC,KAAK,EAAE,EAAE;MACrC,MAAMgK,YAAY,GAAGK,YAAY,CAAC3H,UAAU,CAAC1C,CAAC,GAAG,CAAC,CAAC;MACnD,MAAMiK,WAAW,GAAGI,YAAY,CAAC3H,UAAU,CAAC1C,CAAC,GAAG,CAAC,CAAC;MAElD,MAAMyK,WAAW,GAAGV,mBAAmB,CAACC,YAAY,EAAEC,WAAW,CAAC;MAClEM,OAAO,IAAIF,YAAY,CAACvH,KAAK,CAAC0H,SAAS,EAAExK,CAAC,CAAC,GAAGyK,WAAW;MAEzDD,SAAS,GAAGxK,CAAC,GAAG,CAAC;IACnB;EACF;EACA,OACEqK,YAAY,CAACvH,KAAK,CAAC,CAAC,EAAEwH,UAAU,CAAC,GAAGC,OAAO,GAAGF,YAAY,CAACvH,KAAK,CAAC0H,SAAS,CAAC;AAE/E;AAEA,MAAMvI,WAAW,GAAG,CAClB,KAAK,EACL,MAAM,EACN,UAAU,EACV,SAAS,EACT,MAAM,EACN,QAAQ,EACR,KAAK,EACL,MAAM,EACN,MAAM,EACN,MAAM,EACN,UAAU,EACV,OAAO,EACP,YAAY,EACZ,YAAY,EACZ,OAAO,EACP,MAAM,EACN,QAAQ,EACR,SAAS,EACT,OAAO,EACP,MAAM,EACN,UAAU,EACV,WAAW,EACX,OAAO,EACP,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,WAAW,EACX,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,aAAa,CACL", "ignoreList": []}