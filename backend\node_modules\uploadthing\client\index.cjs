var Arr = require('effect/Array');
var Micro = require('effect/Micro');
var shared = require('@uploadthing/shared');
var deferred_cjs = require('../dist/_internal/deferred.cjs');
var uploadBrowser_cjs = require('../dist/_internal/upload-browser.cjs');
var utReporter_cjs = require('../dist/_internal/ut-reporter.cjs');

function _interopNamespace(e) {
  if (e && e.__esModule) return e;
  var n = Object.create(null);
  if (e) {
    Object.keys(e).forEach(function (k) {
      if (k !== 'default') {
        var d = Object.getOwnPropertyDescriptor(e, k);
        Object.defineProperty(n, k, d.get ? d : {
          enumerable: true,
          get: function () { return e[k]; }
        });
      }
    });
  }
  n.default = e;
  return n;
}

var Arr__namespace = /*#__PURE__*/_interopNamespace(Arr);
var Micro__namespace = /*#__PURE__*/_interopNamespace(Micro);

var version$1 = "7.7.2";

const version = version$1;
/**
 * Validate that a file is of a valid type given a route config
 * @public
 */ const isValidFileType = (file, routeConfig)=>Micro__namespace.runSync(shared.matchFileType(file, shared.objectKeys(routeConfig)).pipe(Micro__namespace.map((type)=>file.type.includes(type)), Micro__namespace.orElseSucceed(()=>false)));
/**
 * Validate that a file is of a valid size given a route config
 * @public
 */ const isValidFileSize = (file, routeConfig)=>Micro__namespace.runSync(shared.matchFileType(file, shared.objectKeys(routeConfig)).pipe(Micro__namespace.flatMap((type)=>shared.fileSizeToBytes(routeConfig[type].maxFileSize)), Micro__namespace.map((maxFileSize)=>file.size <= maxFileSize), Micro__namespace.orElseSucceed(()=>false)));
/**
 * Generate a typed uploader for a given FileRouter
 * @public
 */ const genUploader = (initOpts)=>{
    const routeRegistry = shared.createIdentityProxy();
    const controllableUpload = async (slug, opts)=>{
        const uploads = new Map();
        const endpoint = typeof slug === "function" ? slug(routeRegistry) : slug;
        const utReporter = utReporter_cjs.createUTReporter({
            endpoint: String(endpoint),
            package: initOpts?.package ?? "uploadthing/client",
            url: shared.resolveMaybeUrlArg(initOpts?.url),
            headers: opts.headers
        });
        const fetchFn = initOpts?.fetch ?? window.fetch;
        const presigneds = await Micro__namespace.runPromise(utReporter("upload", {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
            input: "input" in opts ? opts.input : null,
            files: opts.files.map((f)=>({
                    name: f.name,
                    size: f.size,
                    type: f.type,
                    lastModified: f.lastModified
                }))
        }).pipe(Micro__namespace.provideService(shared.FetchContext, fetchFn)));
        const totalSize = opts.files.reduce((acc, f)=>acc + f.size, 0);
        let totalLoaded = 0;
        const uploadEffect = (file, presigned)=>uploadBrowser_cjs.uploadFile(file, presigned, {
                onUploadProgress: (progressEvent)=>{
                    totalLoaded += progressEvent.delta;
                    opts.onUploadProgress?.({
                        ...progressEvent,
                        file,
                        progress: Math.round(progressEvent.loaded / file.size * 100),
                        totalLoaded,
                        totalProgress: Math.round(totalLoaded / totalSize * 100)
                    });
                }
            }).pipe(Micro__namespace.provideService(shared.FetchContext, fetchFn));
        for (const [i, p] of presigneds.entries()){
            const file = opts.files[i];
            if (!file) continue;
            const deferred = deferred_cjs.createDeferred();
            uploads.set(file, {
                deferred,
                presigned: p
            });
            void Micro__namespace.runPromiseExit(uploadEffect(file, p), {
                signal: deferred.ac.signal
            }).then((result)=>{
                if (result._tag === "Success") {
                    return deferred.resolve(result.value);
                } else if (result.cause._tag === "Interrupt") {
                    throw new shared.UploadPausedError();
                }
                throw Micro__namespace.causeSquash(result.cause);
            }).catch((err)=>{
                if (err instanceof shared.UploadPausedError) return;
                deferred.reject(err);
            });
        }
        /**
     * Pause an ongoing upload
     * @param file The file upload you want to pause. Can be omitted to pause all files
     */ const pauseUpload = (file)=>{
            const files = Arr__namespace.ensure(file ?? opts.files);
            for (const file of files){
                const upload = uploads.get(file);
                if (!upload) return;
                if (upload.deferred.ac.signal.aborted) {
                    // Cancel the upload if it's already been paused
                    throw new shared.UploadAbortedError();
                }
                upload.deferred.ac.abort();
            }
        };
        /**
     * Resume a paused upload
     * @param file The file upload you want to resume. Can be omitted to resume all files
     */ const resumeUpload = (file)=>{
            const files = Arr__namespace.ensure(file ?? opts.files);
            for (const file of files){
                const upload = uploads.get(file);
                if (!upload) throw "No upload found";
                upload.deferred.ac = new AbortController();
                void Micro__namespace.runPromiseExit(uploadEffect(file, upload.presigned), {
                    signal: upload.deferred.ac.signal
                }).then((result)=>{
                    if (result._tag === "Success") {
                        return upload.deferred.resolve(result.value);
                    } else if (result.cause._tag === "Interrupt") {
                        throw new shared.UploadPausedError();
                    }
                    throw Micro__namespace.causeSquash(result.cause);
                }).catch((err)=>{
                    if (err instanceof shared.UploadPausedError) return;
                    upload.deferred.reject(err);
                });
            }
        };
        /**
     * Wait for an upload to complete
     * @param file The file upload you want to wait for. Can be omitted to wait for all files
     */ const done = async (file)=>{
            const promises = [];
            const files = Arr__namespace.ensure(file ?? opts.files);
            for (const file of files){
                const upload = uploads.get(file);
                if (!upload) throw "No upload found";
                promises.push(upload.deferred.promise);
            }
            const results = await Promise.all(promises);
            return file ? results[0] : results;
        };
        return {
            pauseUpload,
            resumeUpload,
            done
        };
    };
    /**
   * One step upload function that both requests presigned URLs
   * and then uploads the files to UploadThing
   */ const typedUploadFiles = (slug, opts)=>{
        const endpoint = typeof slug === "function" ? slug(routeRegistry) : slug;
        const fetchFn = initOpts?.fetch ?? window.fetch;
        return uploadBrowser_cjs.uploadFilesInternal(endpoint, {
            ...opts,
            skipPolling: {},
            url: shared.resolveMaybeUrlArg(initOpts?.url),
            package: initOpts?.package ?? "uploadthing/client",
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            input: opts.input
        }).pipe(Micro__namespace.provideService(shared.FetchContext, fetchFn), (effect)=>Micro__namespace.runPromiseExit(effect, opts.signal && {
                signal: opts.signal
            })).then((exit)=>{
            if (exit._tag === "Success") {
                return exit.value;
            } else if (exit.cause._tag === "Interrupt") {
                throw new shared.UploadAbortedError();
            }
            throw Micro__namespace.causeSquash(exit.cause);
        });
    };
    return {
        uploadFiles: typedUploadFiles,
        createUpload: controllableUpload,
        /**
     * Identity object that can be used instead of raw strings
     * that allows "Go to definition" in your IDE to bring you
     * to the backend definition of a route.
     */ routeRegistry
    };
};

Object.defineProperty(exports, "UploadAbortedError", {
  enumerable: true,
  get: function () { return shared.UploadAbortedError; }
});
Object.defineProperty(exports, "UploadPausedError", {
  enumerable: true,
  get: function () { return shared.UploadPausedError; }
});
Object.defineProperty(exports, "allowedContentTextLabelGenerator", {
  enumerable: true,
  get: function () { return shared.allowedContentTextLabelGenerator; }
});
Object.defineProperty(exports, "bytesToFileSize", {
  enumerable: true,
  get: function () { return shared.bytesToFileSize; }
});
Object.defineProperty(exports, "generateClientDropzoneAccept", {
  enumerable: true,
  get: function () { return shared.generateClientDropzoneAccept; }
});
Object.defineProperty(exports, "generateMimeTypes", {
  enumerable: true,
  get: function () { return shared.generateMimeTypes; }
});
Object.defineProperty(exports, "generatePermittedFileTypes", {
  enumerable: true,
  get: function () { return shared.generatePermittedFileTypes; }
});
exports.genUploader = genUploader;
exports.isValidFileSize = isValidFileSize;
exports.isValidFileType = isValidFileType;
exports.version = version;
