{"version": 3, "file": "QueryString.js", "names": ["plusRegex", "Empty", "prototype", "Object", "create", "parse", "input", "result", "inputLength", "length", "key", "value", "startingIndex", "equalityIndex", "shouldDecodeKey", "shouldDecodeValue", "keyHasPlus", "valueHasPlus", "hasBothKeyValuePair", "c", "i", "charCodeAt", "slice", "replace", "decodeURIComponent", "currentValue", "undefined", "pop", "push", "getAsPrimitive", "type", "encodeString", "Number", "isFinite", "stringify", "separator", "keys", "<PERSON><PERSON><PERSON><PERSON>", "valueLength", "<PERSON><PERSON><PERSON>", "Array", "isArray", "j", "hexTable", "from", "_", "toString", "toUpperCase", "noEscape", "Int8Array", "str", "len", "out", "lastPos", "outer", "Error", "c2"], "sources": ["../../src/QueryString.ts"], "sourcesContent": [null], "mappings": ";;;;;;;AAAA;;;AAGA;AACA,MAAMA,SAAS,GAAG,KAAK;AACvB,MAAMC,KAAK,GAAkC,SAAAA,CAAA,GAAa,CAAQ;AAClEA,KAAK,CAACC,SAAS,gBAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;AAErC;;;;AAIM,SAAUC,KAAKA,CAACC,KAAa;EACjC;EACA;EACA,MAAMC,MAAM,GAAG,IAAIN,KAAK,EAAE;EAE1B,IAAI,OAAOK,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAOC,MAAM;EACf;EAEA,MAAMC,WAAW,GAAGF,KAAK,CAACG,MAAM;EAChC,IAAIC,GAAG,GAAG,EAAE;EACZ,IAAIC,KAAK,GAAG,EAAE;EACd,IAAIC,aAAa,GAAG,CAAC,CAAC;EACtB,IAAIC,aAAa,GAAG,CAAC,CAAC;EACtB,IAAIC,eAAe,GAAG,KAAK;EAC3B,IAAIC,iBAAiB,GAAG,KAAK;EAC7B,IAAIC,UAAU,GAAG,KAAK;EACtB,IAAIC,YAAY,GAAG,KAAK;EACxB,IAAIC,mBAAmB,GAAG,KAAK;EAC/B,IAAIC,CAAC,GAAG,CAAC;EAET;EACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,WAAW,GAAG,CAAC,EAAEY,CAAC,EAAE,EAAE;IACxCD,CAAC,GAAGC,CAAC,KAAKZ,WAAW,GAAGF,KAAK,CAACe,UAAU,CAACD,CAAC,CAAC,GAAG,EAAE;IAEhD;IACA,IAAID,CAAC,KAAK,EAAE,EAAE;MACZD,mBAAmB,GAAGL,aAAa,GAAGD,aAAa;MAEnD;MACA,IAAI,CAACM,mBAAmB,EAAE;QACxBL,aAAa,GAAGO,CAAC;MACnB;MAEAV,GAAG,GAAGJ,KAAK,CAACgB,KAAK,CAACV,aAAa,GAAG,CAAC,EAAEC,aAAa,CAAC;MAEnD;MACA,IAAIK,mBAAmB,IAAIR,GAAG,CAACD,MAAM,GAAG,CAAC,EAAE;QACzC;QACA,IAAIO,UAAU,EAAE;UACdN,GAAG,GAAGA,GAAG,CAACa,OAAO,CAACvB,SAAS,EAAE,GAAG,CAAC;QACnC;QAEA;QACA,IAAIc,eAAe,EAAE;UACnBJ,GAAG,GAAGc,kBAAkB,CAACd,GAAG,CAAC,IAAIA,GAAG;QACtC;QAEA,IAAIQ,mBAAmB,EAAE;UACvBP,KAAK,GAAGL,KAAK,CAACgB,KAAK,CAACT,aAAa,GAAG,CAAC,EAAEO,CAAC,CAAC;UAEzC,IAAIH,YAAY,EAAE;YAChBN,KAAK,GAAGA,KAAK,CAACY,OAAO,CAACvB,SAAS,EAAE,GAAG,CAAC;UACvC;UAEA,IAAIe,iBAAiB,EAAE;YACrBJ,KAAK,GAAGa,kBAAkB,CAACb,KAAK,CAAC,IAAIA,KAAK;UAC5C;QACF;QACA,MAAMc,YAAY,GAAGlB,MAAM,CAACG,GAAG,CAAC;QAEhC,IAAIe,YAAY,KAAKC,SAAS,EAAE;UAC9BnB,MAAM,CAACG,GAAG,CAAC,GAAGC,KAAK;QACrB,CAAC,MAAM;UACL;UACA,IAAIc,YAAY,CAACE,GAAG,EAAE;YACpBF,YAAY,CAACG,IAAI,CAACjB,KAAK,CAAC;UAC1B,CAAC,MAAM;YACLJ,MAAM,CAACG,GAAG,CAAC,GAAG,CAACe,YAAY,EAAEd,KAAK,CAAC;UACrC;QACF;MACF;MAEA;MACAA,KAAK,GAAG,EAAE;MACVC,aAAa,GAAGQ,CAAC;MACjBP,aAAa,GAAGO,CAAC;MACjBN,eAAe,GAAG,KAAK;MACvBC,iBAAiB,GAAG,KAAK;MACzBC,UAAU,GAAG,KAAK;MAClBC,YAAY,GAAG,KAAK;IACtB;IACA;IAAA,KACK,IAAIE,CAAC,KAAK,EAAE,EAAE;MACjB,IAAIN,aAAa,IAAID,aAAa,EAAE;QAClCC,aAAa,GAAGO,CAAC;MACnB;MACA;MAAA,KACK;QACHL,iBAAiB,GAAG,IAAI;MAC1B;IACF;IACA;IAAA,KACK,IAAII,CAAC,KAAK,EAAE,EAAE;MACjB,IAAIN,aAAa,GAAGD,aAAa,EAAE;QACjCK,YAAY,GAAG,IAAI;MACrB,CAAC,MAAM;QACLD,UAAU,GAAG,IAAI;MACnB;IACF;IACA;IAAA,KACK,IAAIG,CAAC,KAAK,EAAE,EAAE;MACjB,IAAIN,aAAa,GAAGD,aAAa,EAAE;QACjCG,iBAAiB,GAAG,IAAI;MAC1B,CAAC,MAAM;QACLD,eAAe,GAAG,IAAI;MACxB;IACF;EACF;EAEA,OAAOP,MAAM;AACf;AAEA,SAASsB,cAAcA,CAAClB,KAAU;EAChC,MAAMmB,IAAI,GAAG,OAAOnB,KAAK;EAEzB,IAAImB,IAAI,KAAK,QAAQ,EAAE;IACrB;IACA,OAAOC,YAAY,CAACpB,KAAK,CAAC;EAC5B,CAAC,MAAM,IAAImB,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,SAAS,EAAE;IAClD,OAAO,EAAE,GAAGnB,KAAK;EACnB,CAAC,MAAM,IAAImB,IAAI,KAAK,QAAQ,IAAIE,MAAM,CAACC,QAAQ,CAACtB,KAAK,CAAC,EAAE;IACtD,OAAOA,KAAK,GAAG,IAAI,GAAG,EAAE,GAAGA,KAAK,GAAGoB,YAAY,CAAC,EAAE,GAAGpB,KAAK,CAAC;EAC7D;EAEA,OAAO,EAAE;AACX;AAEA;;;;AAIM,SAAUuB,SAASA,CAAC5B,KAA0B;EAClD,IAAIC,MAAM,GAAG,EAAE;EAEf,IAAID,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC/C,OAAOC,MAAM;EACf;EAEA,MAAM4B,SAAS,GAAG,GAAG;EACrB,MAAMC,IAAI,GAAGjC,MAAM,CAACiC,IAAI,CAAC9B,KAAK,CAAC;EAC/B,MAAM+B,SAAS,GAAGD,IAAI,CAAC3B,MAAM;EAC7B,IAAI6B,WAAW,GAAG,CAAC;EAEnB,KAAK,IAAIlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiB,SAAS,EAAEjB,CAAC,EAAE,EAAE;IAClC,MAAMV,GAAG,GAAG0B,IAAI,CAAChB,CAAC,CAAC;IACnB,MAAMT,KAAK,GAAGL,KAAK,CAACI,GAAG,CAAC;IACxB,MAAM6B,UAAU,GAAGR,YAAY,CAACrB,GAAG,CAAC,GAAG,GAAG;IAE1C,IAAIU,CAAC,EAAE;MACLb,MAAM,IAAI4B,SAAS;IACrB;IAEA,IAAIK,KAAK,CAACC,OAAO,CAAC9B,KAAK,CAAC,EAAE;MACxB2B,WAAW,GAAG3B,KAAK,CAACF,MAAM;MAC1B,KAAK,IAAIiC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,WAAW,EAAEI,CAAC,EAAE,EAAE;QACpC,IAAIA,CAAC,EAAE;UACLnC,MAAM,IAAI4B,SAAS;QACrB;QAEA;QACA;QACA5B,MAAM,IAAIgC,UAAU;QACpBhC,MAAM,IAAIsB,cAAc,CAAClB,KAAK,CAAC+B,CAAC,CAAC,CAAC;MACpC;IACF,CAAC,MAAM;MACLnC,MAAM,IAAIgC,UAAU;MACpBhC,MAAM,IAAIsB,cAAc,CAAClB,KAAK,CAAC;IACjC;EACF;EAEA,OAAOJ,MAAM;AACf;AAEA;AAEA;AACA;AAEA,MAAMoC,QAAQ,gBAAGH,KAAK,CAACI,IAAI,CACzB;EAAEnC,MAAM,EAAE;AAAG,CAAE,EACf,CAACoC,CAAC,EAAEzB,CAAC,KAAK,GAAG,GAAG,CAAC,CAACA,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,IAAIA,CAAC,CAAC0B,QAAQ,CAAC,EAAE,CAAC,EAAEC,WAAW,EAAE,CACrE;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,QAAQ,gBAAG,IAAIC,SAAS,CAAC,CAC7B,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC;AAAE;AACH,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC;AAAE;AACH,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC;AAAE;AACH,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC;AAAE;AACH,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC;AAAE;AACH,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC;AAAE;AACH,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC;AAAE;AACH,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,CAAE;AAAA,CACJ,CAAC;AAEF,SAASlB,YAAYA,CAACmB,GAAW;EAC/B,MAAMC,GAAG,GAAGD,GAAG,CAACzC,MAAM;EACtB,IAAI0C,GAAG,KAAK,CAAC,EAAE,OAAO,EAAE;EAExB,IAAIC,GAAG,GAAG,EAAE;EACZ,IAAIC,OAAO,GAAG,CAAC;EACf,IAAIjC,CAAC,GAAG,CAAC;EAETkC,KAAK,EAAE,OAAOlC,CAAC,GAAG+B,GAAG,EAAE/B,CAAC,EAAE,EAAE;IAC1B,IAAID,CAAC,GAAG+B,GAAG,CAAC7B,UAAU,CAACD,CAAC,CAAC;IAEzB;IACA,OAAOD,CAAC,GAAG,IAAI,EAAE;MACf,IAAI6B,QAAQ,CAAC7B,CAAC,CAAC,KAAK,CAAC,EAAE;QACrB,IAAIkC,OAAO,GAAGjC,CAAC,EAAEgC,GAAG,IAAIF,GAAG,CAAC5B,KAAK,CAAC+B,OAAO,EAAEjC,CAAC,CAAC;QAC7CiC,OAAO,GAAGjC,CAAC,GAAG,CAAC;QACfgC,GAAG,IAAIT,QAAQ,CAACxB,CAAC,CAAC;MACpB;MAEA,IAAI,EAAEC,CAAC,KAAK+B,GAAG,EAAE,MAAMG,KAAK;MAE5BnC,CAAC,GAAG+B,GAAG,CAAC7B,UAAU,CAACD,CAAC,CAAC;IACvB;IAEA,IAAIiC,OAAO,GAAGjC,CAAC,EAAEgC,GAAG,IAAIF,GAAG,CAAC5B,KAAK,CAAC+B,OAAO,EAAEjC,CAAC,CAAC;IAE7C;IACA,IAAID,CAAC,GAAG,KAAK,EAAE;MACbkC,OAAO,GAAGjC,CAAC,GAAG,CAAC;MACfgC,GAAG,IAAIT,QAAQ,CAAC,IAAI,GAAIxB,CAAC,IAAI,CAAE,CAAC,GAAGwB,QAAQ,CAAC,IAAI,GAAIxB,CAAC,GAAG,IAAK,CAAC;MAC9D;IACF;IACA,IAAIA,CAAC,GAAG,MAAM,IAAIA,CAAC,IAAI,MAAM,EAAE;MAC7BkC,OAAO,GAAGjC,CAAC,GAAG,CAAC;MACfgC,GAAG,IACDT,QAAQ,CAAC,IAAI,GAAIxB,CAAC,IAAI,EAAG,CAAC,GAC1BwB,QAAQ,CAAC,IAAI,GAAKxB,CAAC,IAAI,CAAC,GAAI,IAAK,CAAC,GAClCwB,QAAQ,CAAC,IAAI,GAAIxB,CAAC,GAAG,IAAK,CAAC;MAC7B;IACF;IACA;IACA,EAAEC,CAAC;IAEH;IACA;IACA;IACA,IAAIA,CAAC,IAAI+B,GAAG,EAAE;MACZ,MAAM,IAAII,KAAK,CAAC,eAAe,CAAC;IAClC;IAEA,MAAMC,EAAE,GAAGN,GAAG,CAAC7B,UAAU,CAACD,CAAC,CAAC,GAAG,KAAK;IAEpCiC,OAAO,GAAGjC,CAAC,GAAG,CAAC;IACfD,CAAC,GAAG,OAAO,IAAK,CAACA,CAAC,GAAG,KAAK,KAAK,EAAE,GAAIqC,EAAE,CAAC;IACxCJ,GAAG,IACDT,QAAQ,CAAC,IAAI,GAAIxB,CAAC,IAAI,EAAG,CAAC,GAC1BwB,QAAQ,CAAC,IAAI,GAAKxB,CAAC,IAAI,EAAE,GAAI,IAAK,CAAC,GACnCwB,QAAQ,CAAC,IAAI,GAAKxB,CAAC,IAAI,CAAC,GAAI,IAAK,CAAC,GAClCwB,QAAQ,CAAC,IAAI,GAAIxB,CAAC,GAAG,IAAK,CAAC;EAC/B;EACA,IAAIkC,OAAO,KAAK,CAAC,EAAE,OAAOH,GAAG;EAC7B,IAAIG,OAAO,GAAGF,GAAG,EAAE,OAAOC,GAAG,GAAGF,GAAG,CAAC5B,KAAK,CAAC+B,OAAO,CAAC;EAClD,OAAOD,GAAG;AACZ", "ignoreList": []}