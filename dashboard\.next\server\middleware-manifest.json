{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/dashboard(\\.json)?[\\/#\\?]?$", "originalSource": "/dashboard"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/dashboard(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\.json)?[\\/#\\?]?$", "originalSource": "/dashboard/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api\\/dashboard(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\.json)?[\\/#\\?]?$", "originalSource": "/api/dashboard/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api\\/properties(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\.json)?[\\/#\\?]?$", "originalSource": "/api/properties/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api\\/campaigns(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\.json)?[\\/#\\?]?$", "originalSource": "/api/campaigns/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api\\/users(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\.json)?[\\/#\\?]?$", "originalSource": "/api/users/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/sign-in(\\.json)?[\\/#\\?]?$", "originalSource": "/sign-in"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/sign-up(\\.json)?[\\/#\\?]?$", "originalSource": "/sign-up"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/sign-out(\\.json)?[\\/#\\?]?$", "originalSource": "/sign-out"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\.json))?[\\/#\\?]?$", "originalSource": "/"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "Zg7MHMyZl0BQVDSRrpwMh", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "/wHobusb3V8adsoMHOAhhW/5Z0TF+sn4pUt6oVQfqjg=", "__NEXT_PREVIEW_MODE_ID": "7cf0b5314f73e66dd383c57cf3c097fd", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "aaaa5d7d4577d3c65655c8fd83bbf7c36d0ab71babc965bc3a0ecc42ad65e75c", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3f4537dc87121cf02e4c772beb40fe9b7e246437c34b284de5cdeb987ece12db"}}}, "functions": {}, "sortedMiddleware": ["/"]}