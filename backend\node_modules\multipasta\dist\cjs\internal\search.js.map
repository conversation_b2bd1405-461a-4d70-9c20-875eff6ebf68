{"version": 3, "file": "search.js", "names": ["makeState", "needle_", "needle", "TextEncoder", "encode", "<PERSON><PERSON><PERSON><PERSON>", "length", "indexes", "i", "b", "undefined", "push", "firstByte", "previousChunk", "previousChunkLength", "matchIndex", "make", "callback", "seed", "state", "makeIndexOf", "globalThis", "chunk", "fromIndex", "<PERSON><PERSON><PERSON>", "prototype", "indexOf", "call", "skipTable", "Uint8Array", "fill", "lastIndex", "lengthTotal", "j", "k", "write", "chunkLength", "newChunk", "set", "pos", "match", "subarray", "earliestIndex", "len", "index", "end"], "sources": ["../../../src/internal/search.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAWA,SAASA,SAASA,CAACC,OAAe;EAChC,MAAMC,MAAM,GAAG,IAAIC,WAAW,EAAE,CAACC,MAAM,CAACH,OAAO,CAAC;EAChD,MAAMI,YAAY,GAAGH,MAAM,CAACI,MAAM;EAElC,MAAMC,OAAO,GAA6B,EAAE;EAC5C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,YAAY,EAAEG,CAAC,EAAE,EAAE;IACrC,MAAMC,CAAC,GAAGP,MAAM,CAACM,CAAC,CAAC;IACnB,IAAID,OAAO,CAACE,CAAC,CAAC,KAAKC,SAAS,EAAEH,OAAO,CAACE,CAAC,CAAC,GAAG,EAAE;IAC7CF,OAAO,CAACE,CAAC,CAAC,CAACE,IAAI,CAACH,CAAC,CAAC;;EAGpB,OAAO;IACLN,MAAM;IACNG,YAAY;IACZE,OAAO;IACPK,SAAS,EAAEV,MAAM,CAAC,CAAC,CAAC;IACpBW,aAAa,EAAEH,SAAS;IACxBI,mBAAmB,EAAE,CAAC;IACtBC,UAAU,EAAE;GACb;AACH;AAEM,SAAUC,IAAIA,CAClBd,MAAc,EACde,QAAoD,EACpDC,IAAiB;EAEjB,MAAMC,KAAK,GAAGnB,SAAS,CAACE,MAAM,CAAC;EAC/B,IAAIgB,IAAI,KAAKR,SAAS,EAAE;IACtBS,KAAK,CAACN,aAAa,GAAGK,IAAI;IAC1BC,KAAK,CAACL,mBAAmB,GAAGI,IAAI,CAACZ,MAAM;;EAGzC,SAASc,WAAWA,CAAA;IAKlB;IACA,IACE,QAAQ,IAAIC,UAAU,IACtB,EAAE,KAAK,IAAIA,UAAU,IAAI,MAAM,IAAIA,UAAU,CAAC,EAC9C;MACA,OAAO,UAAUC,KAAK,EAAEpB,MAAM,EAAEqB,SAAS;QACvC,OAAOC,MAAM,CAACC,SAAS,CAACC,OAAO,CAACC,IAAI,CAACL,KAAK,EAAEpB,MAAM,EAAEqB,SAAS,CAAC;MAChE,CAAC;;IAGH,MAAMK,SAAS,GAAG,IAAIC,UAAU,CAAC,GAAG,CAAC,CAACC,IAAI,CAACX,KAAK,CAACjB,MAAM,CAACI,MAAM,CAAC;IAC/D,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEuB,SAAS,GAAGZ,KAAK,CAACjB,MAAM,CAACI,MAAM,GAAG,CAAC,EAAEE,CAAC,GAAGuB,SAAS,EAAE,EAAEvB,CAAC,EAAE;MACvEoB,SAAS,CAACT,KAAK,CAACjB,MAAM,CAACM,CAAC,CAAC,CAAC,GAAGuB,SAAS,GAAGvB,CAAC;;IAG5C,OAAO,UAAUc,KAAK,EAAEpB,MAAM,EAAEqB,SAAS;MACvC,MAAMS,WAAW,GAAGV,KAAK,CAAChB,MAAM;MAChC,IAAIE,CAAC,GAAGe,SAAS,GAAGJ,KAAK,CAACd,YAAY,GAAG,CAAC;MAE1C,OAAOG,CAAC,GAAGwB,WAAW,EAAE;QACtB,KACE,IAAIC,CAAC,GAAGd,KAAK,CAACd,YAAY,GAAG,CAAC,EAAE6B,CAAC,GAAG1B,CAAC,EACrCyB,CAAC,IAAI,CAAC,IAAIX,KAAK,CAACY,CAAC,CAAC,KAAKhC,MAAM,CAAC+B,CAAC,CAAC,EAChCA,CAAC,EAAE,EAAEC,CAAC,EAAE,EACR;UACA,IAAID,CAAC,KAAK,CAAC,EAAE,OAAOC,CAAC;;QAEvB1B,CAAC,IAAIoB,SAAS,CAACN,KAAK,CAACd,CAAC,CAAC,CAAC;;MAG1B,OAAO,CAAC,CAAC;IACX,CAAC;EACH;EAEA,MAAMkB,OAAO,GAAGN,WAAW,EAAE;EAE7B,SAASe,KAAKA,CAACb,KAAiB;IAC9B,IAAIc,WAAW,GAAGd,KAAK,CAAChB,MAAM;IAE9B,IAAIa,KAAK,CAACN,aAAa,KAAKH,SAAS,EAAE;MACrC,MAAM2B,QAAQ,GAAG,IAAIR,UAAU,CAACV,KAAK,CAACL,mBAAmB,GAAGsB,WAAW,CAAC;MACxEC,QAAQ,CAACC,GAAG,CAACnB,KAAK,CAACN,aAAa,CAAC;MACjCwB,QAAQ,CAACC,GAAG,CAAChB,KAAK,EAAEH,KAAK,CAACL,mBAAmB,CAAC;MAC9CQ,KAAK,GAAGe,QAAQ;MAChBD,WAAW,GAAGjB,KAAK,CAACL,mBAAmB,GAAGsB,WAAW;MACrDjB,KAAK,CAACN,aAAa,GAAGH,SAAS;;IAGjC,IAAI0B,WAAW,GAAGjB,KAAK,CAACd,YAAY,EAAE;MACpCc,KAAK,CAACN,aAAa,GAAGS,KAAK;MAC3BH,KAAK,CAACL,mBAAmB,GAAGsB,WAAW;MACvC;;IAGF,IAAIG,GAAG,GAAG,CAAC;IACX,OAAOA,GAAG,GAAGH,WAAW,EAAE;MACxB,MAAMI,KAAK,GAAGd,OAAO,CAACJ,KAAK,EAAEH,KAAK,CAACjB,MAAM,EAAEqC,GAAG,CAAC;MAE/C,IAAIC,KAAK,GAAG,CAAC,CAAC,EAAE;QACd,IAAIA,KAAK,GAAGD,GAAG,EAAE;UACftB,QAAQ,CAACE,KAAK,CAACJ,UAAU,EAAEO,KAAK,CAACmB,QAAQ,CAACF,GAAG,EAAEC,KAAK,CAAC,CAAC;;QAExDrB,KAAK,CAACJ,UAAU,IAAI,CAAC;QACrBwB,GAAG,GAAGC,KAAK,GAAGrB,KAAK,CAACd,YAAY;QAChC;OACD,MAAM,IAAIiB,KAAK,CAACc,WAAW,GAAG,CAAC,CAAC,IAAIjB,KAAK,CAACZ,OAAO,EAAE;QAClD,MAAMA,OAAO,GAAGY,KAAK,CAACZ,OAAO,CAACe,KAAK,CAACc,WAAW,GAAG,CAAC,CAAC,CAAC;QACrD,IAAIM,aAAa,GAAG,CAAC,CAAC;QACtB,KAAK,IAAIlC,CAAC,GAAG,CAAC,EAAEmC,GAAG,GAAGpC,OAAO,CAACD,MAAM,EAAEE,CAAC,GAAGmC,GAAG,EAAEnC,CAAC,EAAE,EAAE;UAClD,MAAMoC,KAAK,GAAGrC,OAAO,CAACC,CAAC,CAAC;UACxB,IACEc,KAAK,CAACc,WAAW,GAAG,CAAC,GAAGQ,KAAK,CAAC,KAAKzB,KAAK,CAACP,SAAS,IAClDJ,CAAC,GAAGkC,aAAa,EACjB;YACAA,aAAa,GAAGE,KAAK;;;QAGzB,IAAIF,aAAa,KAAK,CAAC,CAAC,EAAE;UACxB,IAAIH,GAAG,KAAK,CAAC,EAAE;YACbtB,QAAQ,CAACE,KAAK,CAACJ,UAAU,EAAEO,KAAK,CAAC;WAClC,MAAM;YACLL,QAAQ,CAACE,KAAK,CAACJ,UAAU,EAAEO,KAAK,CAACmB,QAAQ,CAACF,GAAG,CAAC,CAAC;;SAElD,MAAM;UACL,IAAIH,WAAW,GAAG,CAAC,GAAGM,aAAa,GAAGH,GAAG,EAAE;YACzCtB,QAAQ,CACNE,KAAK,CAACJ,UAAU,EAChBO,KAAK,CAACmB,QAAQ,CAACF,GAAG,EAAEH,WAAW,GAAG,CAAC,GAAGM,aAAa,CAAC,CACrD;;UAEHvB,KAAK,CAACN,aAAa,GAAGS,KAAK,CAACmB,QAAQ,CAACL,WAAW,GAAG,CAAC,GAAGM,aAAa,CAAC;UACrEvB,KAAK,CAACL,mBAAmB,GAAG4B,aAAa,GAAG,CAAC;;OAEhD,MAAM,IAAIH,GAAG,KAAK,CAAC,EAAE;QACpBtB,QAAQ,CAACE,KAAK,CAACJ,UAAU,EAAEO,KAAK,CAAC;OAClC,MAAM;QACLL,QAAQ,CAACE,KAAK,CAACJ,UAAU,EAAEO,KAAK,CAACmB,QAAQ,CAACF,GAAG,CAAC,CAAC;;MAGjD;;EAEJ;EAEA,SAASM,GAAGA,CAAA;IACV,IAAI1B,KAAK,CAACN,aAAa,KAAKH,SAAS,IAAIS,KAAK,CAACN,aAAa,KAAKK,IAAI,EAAE;MACrED,QAAQ,CAACE,KAAK,CAACJ,UAAU,EAAEI,KAAK,CAACN,aAAa,CAAC;;IAGjDM,KAAK,CAACN,aAAa,GAAGK,IAAI;IAC1BC,KAAK,CAACL,mBAAmB,GAAGI,IAAI,EAAEZ,MAAM,IAAI,CAAC;IAC7Ca,KAAK,CAACJ,UAAU,GAAG,CAAC;EACtB;EAEA,OAAO;IAAEoB,KAAK;IAAEU;EAAG,CAAW;AAChC"}