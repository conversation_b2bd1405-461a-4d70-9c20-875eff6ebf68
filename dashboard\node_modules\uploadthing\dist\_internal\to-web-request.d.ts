import * as Effect from 'effect/Effect';
import { UploadThingError } from '@uploadthing/shared';

type IncomingMessageLike = {
    method?: string | undefined;
    url?: string | undefined;
    headers?: Record<string, string | string[] | undefined>;
    body?: any;
};
declare const getPostBody: <TBody = unknown>(opts: {
    req: IncomingMessageLike & {
        on: (event: string, listener: (data: any) => void) => void;
    };
}) => Effect.Effect<TBody | undefined, UploadThingError<{
    message: string;
}>, never>;
declare const toWebRequest: (req: IncomingMessageLike, body?: any) => Effect.Effect<Request, never>;

export { getPostBody, toWebRequest };
