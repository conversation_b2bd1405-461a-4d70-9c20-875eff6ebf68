"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/properties/create/page",{

/***/ "(app-pages-browser)/./app/dashboard/properties/create/property-form-steps.tsx":
/*!*****************************************************************!*\
  !*** ./app/dashboard/properties/create/property-form-steps.tsx ***!
  \*****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PropertyFormSteps: () => (/* binding */ PropertyFormSteps)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,Plus,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,Plus,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,Plus,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,Plus,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,Plus,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronLeft,ChevronRight,Plus,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/hooks/useSimpleLanguage */ \"(app-pages-browser)/./hooks/useSimpleLanguage.tsx\");\n/* harmony import */ var _lib_uploadthing__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/uploadthing */ \"(app-pages-browser)/./lib/uploadthing.ts\");\n/* __next_internal_client_entry_do_not_use__ PropertyFormSteps auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction PropertyFormSteps(param) {\n    let { onSave, loading, initialData, isEdit = false } = param;\n    _s();\n    const { language } = (0,_hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_11__.useSimpleLanguage)();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const totalSteps = 4;\n    const defaultFormData = {\n        title: '',\n        titleAr: '',\n        description: '',\n        descriptionAr: '',\n        price: '',\n        currency: 'AED',\n        type: 'APARTMENT',\n        status: 'AVAILABLE',\n        bedrooms: '',\n        bathrooms: '',\n        area: '',\n        location: '',\n        locationAr: '',\n        address: '',\n        addressAr: '',\n        city: '',\n        cityAr: '',\n        country: 'UAE',\n        countryAr: 'الإمارات العربية المتحدة',\n        images: [],\n        features: [],\n        featuresAr: [],\n        amenities: [],\n        amenitiesAr: [],\n        yearBuilt: '',\n        parking: '',\n        furnished: false,\n        petFriendly: false,\n        utilities: '',\n        utilitiesAr: '',\n        contactInfo: '',\n        isFeatured: false,\n        isActive: true\n    };\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialData || defaultFormData);\n    const [newFeature, setNewFeature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newFeatureAr, setNewFeatureAr] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newAmenity, setNewAmenity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newAmenityAr, setNewAmenityAr] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Auto-save functionality (only for create mode)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PropertyFormSteps.useEffect\": ()=>{\n            if (!isEdit && !initialData) {\n                const savedData = localStorage.getItem('property-draft');\n                if (savedData) {\n                    try {\n                        const parsed = JSON.parse(savedData);\n                        setFormData(parsed);\n                    } catch (error) {\n                        console.error('Error loading draft:', error);\n                    }\n                }\n            }\n        }\n    }[\"PropertyFormSteps.useEffect\"], [\n        isEdit,\n        initialData\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PropertyFormSteps.useEffect\": ()=>{\n            if (!isEdit) {\n                const timer = setTimeout({\n                    \"PropertyFormSteps.useEffect.timer\": ()=>{\n                        localStorage.setItem('property-draft', JSON.stringify(formData));\n                    }\n                }[\"PropertyFormSteps.useEffect.timer\"], 1000);\n                return ({\n                    \"PropertyFormSteps.useEffect\": ()=>clearTimeout(timer)\n                })[\"PropertyFormSteps.useEffect\"];\n            }\n        }\n    }[\"PropertyFormSteps.useEffect\"], [\n        formData,\n        isEdit\n    ]);\n    // Initialize form data when initialData changes (for edit mode)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PropertyFormSteps.useEffect\": ()=>{\n            if (initialData && isEdit) {\n                setFormData(initialData);\n            }\n        }\n    }[\"PropertyFormSteps.useEffect\"], [\n        initialData,\n        isEdit\n    ]);\n    const translations = {\n        ar: {\n            step: 'الخطوة',\n            of: 'من',\n            next: 'التالي',\n            previous: 'السابق',\n            save: 'حفظ العقار',\n            required: 'مطلوب',\n            optional: 'اختياري',\n            basicInfo: 'المعلومات الأساسية',\n            propertyDetails: 'تفاصيل العقار',\n            locationInfo: 'معلومات الموقع',\n            additionalInfo: 'معلومات إضافية',\n            title: 'عنوان العقار',\n            titleEn: 'عنوان العقار (إنجليزي)',\n            description: 'الوصف',\n            descriptionEn: 'الوصف (إنجليزي)',\n            price: 'السعر',\n            currency: 'العملة',\n            propertyType: 'نوع العقار',\n            status: 'الحالة',\n            bedrooms: 'غرف النوم',\n            bathrooms: 'الحمامات',\n            area: 'المساحة (م²)',\n            yearBuilt: 'سنة البناء',\n            parking: 'مواقف السيارات',\n            location: 'الموقع',\n            locationEn: 'الموقع (إنجليزي)',\n            address: 'العنوان',\n            addressEn: 'العنوان (إنجليزي)',\n            city: 'المدينة',\n            cityEn: 'المدينة (إنجليزي)',\n            country: 'البلد',\n            countryEn: 'البلد (إنجليزي)',\n            images: 'صور العقار',\n            features: 'المميزات',\n            featuresEn: 'المميزات (إنجليزي)',\n            amenities: 'المرافق',\n            amenitiesEn: 'المرافق (إنجليزي)',\n            utilities: 'المرافق المشمولة',\n            utilitiesEn: 'المرافق المشمولة (إنجليزي)',\n            contactInfo: 'معلومات الاتصال',\n            furnished: 'مفروش',\n            petFriendly: 'يسمح بالحيوانات الأليفة',\n            featured: 'عقار مميز',\n            active: 'إعلان نشط',\n            addFeature: 'إضافة ميزة',\n            addAmenity: 'إضافة مرفق',\n            uploadImages: 'رفع صور العقار',\n            dragDropImages: 'اسحب وأفلت الصور هنا، أو انقر للاختيار',\n            titlePlaceholder: 'أدخل عنوان العقار',\n            titleEnPlaceholder: 'Enter property title in English',\n            descriptionPlaceholder: 'أدخل وصف العقار',\n            descriptionEnPlaceholder: 'Enter property description in English',\n            locationPlaceholder: 'أدخل الموقع',\n            locationEnPlaceholder: 'Enter location in English',\n            addressPlaceholder: 'أدخل العنوان الكامل',\n            addressEnPlaceholder: 'Enter full address in English',\n            cityPlaceholder: 'أدخل اسم المدينة',\n            cityEnPlaceholder: 'Enter city name in English',\n            featurePlaceholder: 'أضف ميزة',\n            featureEnPlaceholder: 'Add a feature in English',\n            amenityPlaceholder: 'أضف مرفق',\n            amenityEnPlaceholder: 'Add an amenity in English',\n            utilitiesPlaceholder: 'اذكر المرافق المشمولة',\n            utilitiesEnPlaceholder: 'List included utilities in English',\n            contactPlaceholder: 'أدخل معلومات الاتصال'\n        },\n        en: {\n            step: 'Step',\n            of: 'of',\n            next: 'Next',\n            previous: 'Previous',\n            save: 'Save Property',\n            required: 'Required',\n            optional: 'Optional',\n            basicInfo: 'Basic Information',\n            propertyDetails: 'Property Details',\n            locationInfo: 'Location Information',\n            additionalInfo: 'Additional Information',\n            title: 'Property Title',\n            titleEn: 'Property Title (English)',\n            description: 'Description',\n            descriptionEn: 'Description (English)',\n            price: 'Price',\n            currency: 'Currency',\n            propertyType: 'Property Type',\n            status: 'Status',\n            bedrooms: 'Bedrooms',\n            bathrooms: 'Bathrooms',\n            area: 'Area (m²)',\n            yearBuilt: 'Year Built',\n            parking: 'Parking Spaces',\n            location: 'Location',\n            locationEn: 'Location (English)',\n            address: 'Address',\n            addressEn: 'Address (English)',\n            city: 'City',\n            cityEn: 'City (English)',\n            country: 'Country',\n            countryEn: 'Country (English)',\n            images: 'Property Images',\n            features: 'Features',\n            featuresEn: 'Features (English)',\n            amenities: 'Amenities',\n            amenitiesEn: 'Amenities (English)',\n            utilities: 'Utilities Included',\n            utilitiesEn: 'Utilities Included (English)',\n            contactInfo: 'Contact Information',\n            furnished: 'Furnished',\n            petFriendly: 'Pet Friendly',\n            featured: 'Featured Property',\n            active: 'Active Listing',\n            addFeature: 'Add Feature',\n            addAmenity: 'Add Amenity',\n            uploadImages: 'Upload Property Images',\n            dragDropImages: 'Drag and drop images here, or click to select',\n            titlePlaceholder: 'أدخل عنوان العقار',\n            titleEnPlaceholder: 'Enter property title in English',\n            descriptionPlaceholder: 'أدخل وصف العقار',\n            descriptionEnPlaceholder: 'Enter property description in English',\n            locationPlaceholder: 'أدخل الموقع',\n            locationEnPlaceholder: 'Enter location in English',\n            addressPlaceholder: 'أدخل العنوان الكامل',\n            addressEnPlaceholder: 'Enter full address in English',\n            cityPlaceholder: 'أدخل اسم المدينة',\n            cityEnPlaceholder: 'Enter city name in English',\n            featurePlaceholder: 'أضف ميزة',\n            featureEnPlaceholder: 'Add a feature in English',\n            amenityPlaceholder: 'أضف مرفق',\n            amenityEnPlaceholder: 'Add an amenity in English',\n            utilitiesPlaceholder: 'اذكر المرافق المشمولة',\n            utilitiesEnPlaceholder: 'List included utilities in English',\n            contactPlaceholder: 'أدخل معلومات الاتصال'\n        }\n    };\n    const t = translations[language] || translations.ar;\n    const propertyTypes = {\n        ar: {\n            APARTMENT: 'شقة',\n            VILLA: 'فيلا',\n            TOWNHOUSE: 'تاون هاوس',\n            PENTHOUSE: 'بنتهاوس',\n            STUDIO: 'استوديو',\n            OFFICE: 'مكتب',\n            SHOP: 'محل تجاري',\n            WAREHOUSE: 'مستودع',\n            LAND: 'أرض',\n            BUILDING: 'مبنى'\n        },\n        en: {\n            APARTMENT: 'Apartment',\n            VILLA: 'Villa',\n            TOWNHOUSE: 'Townhouse',\n            PENTHOUSE: 'Penthouse',\n            STUDIO: 'Studio',\n            OFFICE: 'Office',\n            SHOP: 'Shop',\n            WAREHOUSE: 'Warehouse',\n            LAND: 'Land',\n            BUILDING: 'Building'\n        }\n    };\n    const propertyStatuses = {\n        ar: {\n            AVAILABLE: 'متاح',\n            SOLD: 'مباع',\n            RENTED: 'مؤجر',\n            RESERVED: 'محجوز',\n            OFF_MARKET: 'خارج السوق'\n        },\n        en: {\n            AVAILABLE: 'Available',\n            SOLD: 'Sold',\n            RENTED: 'Rented',\n            RESERVED: 'Reserved',\n            OFF_MARKET: 'Off Market'\n        }\n    };\n    const stepTitles = [\n        t.basicInfo,\n        t.propertyDetails,\n        t.locationInfo,\n        t.additionalInfo\n    ];\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        await onSave(formData);\n        // Clear draft after successful save\n        localStorage.removeItem('property-draft');\n    };\n    const addFeature = ()=>{\n        if (newFeature.trim()) {\n            setFormData((prev)=>({\n                    ...prev,\n                    features: [\n                        ...prev.features,\n                        newFeature.trim()\n                    ]\n                }));\n            setNewFeature('');\n        }\n    };\n    const addFeatureAr = ()=>{\n        if (newFeatureAr.trim()) {\n            setFormData((prev)=>({\n                    ...prev,\n                    featuresAr: [\n                        ...prev.featuresAr,\n                        newFeatureAr.trim()\n                    ]\n                }));\n            setNewFeatureAr('');\n        }\n    };\n    const addAmenity = ()=>{\n        if (newAmenity.trim()) {\n            setFormData((prev)=>({\n                    ...prev,\n                    amenities: [\n                        ...prev.amenities,\n                        newAmenity.trim()\n                    ]\n                }));\n            setNewAmenity('');\n        }\n    };\n    const addAmenityAr = ()=>{\n        if (newAmenityAr.trim()) {\n            setFormData((prev)=>({\n                    ...prev,\n                    amenitiesAr: [\n                        ...prev.amenitiesAr,\n                        newAmenityAr.trim()\n                    ]\n                }));\n            setNewAmenityAr('');\n        }\n    };\n    const removeFeature = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                features: prev.features.filter((_, i)=>i !== index)\n            }));\n    };\n    const removeFeatureAr = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                featuresAr: prev.featuresAr.filter((_, i)=>i !== index)\n            }));\n    };\n    const removeAmenity = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                amenities: prev.amenities.filter((_, i)=>i !== index)\n            }));\n    };\n    const removeAmenityAr = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                amenitiesAr: prev.amenitiesAr.filter((_, i)=>i !== index)\n            }));\n    };\n    const removeImage = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                images: prev.images.filter((_, i)=>i !== index)\n            }));\n    };\n    const moveImage = (fromIndex, toIndex)=>{\n        setFormData((prev)=>{\n            const newImages = [\n                ...prev.images\n            ];\n            const [movedImage] = newImages.splice(fromIndex, 1);\n            newImages.splice(toIndex, 0, movedImage);\n            return {\n                ...prev,\n                images: newImages\n            };\n        });\n    };\n    const setMainImage = (index)=>{\n        if (index === 0) return; // Already main image\n        moveImage(index, 0);\n    };\n    const nextStep = ()=>{\n        if (currentStep < totalSteps) {\n            setCurrentStep(currentStep + 1);\n        }\n    };\n    const prevStep = ()=>{\n        if (currentStep > 1) {\n            setCurrentStep(currentStep - 1);\n        }\n    };\n    const isStepValid = (step)=>{\n        switch(step){\n            case 1:\n                return formData.title && formData.description && formData.price && formData.type;\n            case 2:\n                return true; // Property details are optional\n            case 3:\n                return formData.location && formData.address && formData.city;\n            case 4:\n                return true; // Additional info is optional\n            default:\n                return false;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(language === 'ar' ? 'rtl' : 'ltr'),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-bold text-sm\",\n                                            children: currentStep\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 427,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg font-semibold text-slate-800 dark:text-slate-200\",\n                                                children: [\n                                                    t.step,\n                                                    \" \",\n                                                    currentStep,\n                                                    \" \",\n                                                    t.of,\n                                                    \" \",\n                                                    totalSteps\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-slate-600 dark:text-slate-400\",\n                                                children: stepTitles[currentStep - 1]\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-600 dark:text-blue-400\",\n                                        children: [\n                                            Math.round(currentStep / totalSteps * 100),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-slate-500 dark:text-slate-400\",\n                                        children: language === 'ar' ? 'مكتمل' : 'Complete'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                        lineNumber: 424,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full bg-slate-200 dark:bg-slate-700 rounded-full h-3 shadow-inner\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500 h-3 rounded-full transition-all duration-700 ease-out shadow-lg relative overflow-hidden\",\n                                style: {\n                                    width: \"\".concat(currentStep / totalSteps * 100, \"%\")\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 451,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                            lineNumber: 450,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                        lineNumber: 449,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between mt-8 px-2\",\n                        children: stepTitles.map((title, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center max-w-24\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 rounded-xl flex items-center justify-center text-sm font-bold transition-all duration-300 shadow-lg \".concat(index + 1 <= currentStep ? 'bg-gradient-to-br from-blue-500 to-indigo-600 text-white shadow-blue-200 dark:shadow-blue-900/50' : index + 1 === currentStep + 1 ? 'bg-gradient-to-br from-slate-300 to-slate-400 text-slate-600 shadow-slate-200 dark:shadow-slate-800' : 'bg-slate-200 dark:bg-slate-700 text-slate-500 dark:text-slate-400'),\n                                        children: index + 1 < currentStep ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-bold\",\n                                            children: index + 1\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 476,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 464,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs mt-3 text-center leading-tight font-medium \".concat(index + 1 <= currentStep ? 'text-blue-700 dark:text-blue-300' : 'text-slate-600 dark:text-slate-400'),\n                                        children: title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 463,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                        lineNumber: 461,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                lineNumber: 423,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"space-y-8\",\n                children: [\n                    currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                        className: \"shadow-2xl border-0 bg-white/95 dark:bg-slate-800/95 backdrop-blur-md overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                className: \"pb-8 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-blue-900/30 dark:via-indigo-900/30 dark:to-purple-900/30 border-b border-blue-100 dark:border-blue-800/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                    className: \"text-2xl font-bold text-slate-800 dark:text-white flex items-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-14 h-14 bg-gradient-to-br from-blue-500 via-indigo-500 to-purple-600 text-white rounded-2xl flex items-center justify-center text-lg font-bold shadow-xl shadow-blue-200 dark:shadow-blue-900/50\",\n                                            children: \"1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 497,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl\",\n                                                    children: t.basicInfo\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-normal text-slate-600 dark:text-slate-400 mt-1\",\n                                                    children: language === 'ar' ? 'أدخل المعلومات الأساسية للعقار' : 'Enter basic property information'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 502,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                    lineNumber: 496,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 495,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                className: \"space-y-8 p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"title\",\n                                                        className: \"text-base font-bold text-slate-800 dark:text-slate-200 flex items-center gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-6 h-6 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-600 dark:text-blue-400 text-sm font-bold\",\n                                                                    children: \"1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 514,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 513,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            t.title,\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500 text-lg\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 517,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 512,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"title\",\n                                                        value: formData.title,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    title: e.target.value\n                                                                })),\n                                                        placeholder: t.titlePlaceholder,\n                                                        required: true,\n                                                        dir: language === 'ar' ? 'rtl' : 'ltr',\n                                                        className: \"h-14 border-2 border-slate-200 dark:border-slate-600 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-300 rounded-xl text-lg bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm shadow-sm hover:shadow-md focus:shadow-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 519,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 511,\n                                                columnNumber: 17\n                                            }, this),\n                                            language === 'en' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"titleEn\",\n                                                        className: \"text-base font-semibold text-slate-700 dark:text-slate-300 flex items-center gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-6 h-6 bg-slate-100 dark:bg-slate-700 rounded-lg flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-slate-600 dark:text-slate-400 text-sm\",\n                                                                    children: \"EN\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 533,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 532,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            t.titleEn,\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-slate-400 text-sm\",\n                                                                children: [\n                                                                    \"(\",\n                                                                    t.optional,\n                                                                    \")\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 536,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 531,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"titleEn\",\n                                                        value: formData.titleAr,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    titleAr: e.target.value\n                                                                })),\n                                                        placeholder: t.titleEnPlaceholder,\n                                                        dir: \"ltr\",\n                                                        className: \"h-12 border-2 border-slate-200 dark:border-slate-600 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-300 rounded-xl bg-white/30 dark:bg-slate-800/30\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 538,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 530,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"description\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2\",\n                                                        children: [\n                                                            t.description,\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500 text-lg\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 555,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 553,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                        id: \"description\",\n                                                        value: formData.description,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    description: e.target.value\n                                                                })),\n                                                        placeholder: t.descriptionPlaceholder,\n                                                        required: true,\n                                                        rows: 5,\n                                                        className: \"border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-200 resize-none rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 557,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 552,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"descriptionAr\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2\",\n                                                        children: [\n                                                            t.descriptionAr,\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-400 text-sm\",\n                                                                children: [\n                                                                    \"(\",\n                                                                    t.optional,\n                                                                    \")\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 570,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 568,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                        id: \"descriptionAr\",\n                                                        value: formData.descriptionAr,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    descriptionAr: e.target.value\n                                                                })),\n                                                        placeholder: t.descriptionArPlaceholder,\n                                                        dir: \"rtl\",\n                                                        rows: 5,\n                                                        className: \"border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-200 resize-none rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 572,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 567,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 551,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"price\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2\",\n                                                        children: [\n                                                            t.price,\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500 text-lg\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 589,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 587,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"price\",\n                                                        type: \"number\",\n                                                        value: formData.price,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    price: e.target.value\n                                                                })),\n                                                        placeholder: \"0\",\n                                                        required: true,\n                                                        className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-200 rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 591,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 586,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"currency\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.currency\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 602,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                        value: formData.currency,\n                                                        onValueChange: (value)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    currency: value\n                                                                })),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                                className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 rounded-lg\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 607,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 606,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                        value: \"AED\",\n                                                                        children: \"AED - درهم إماراتي\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 610,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                        value: \"USD\",\n                                                                        children: \"USD - دولار أمريكي\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 611,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                        value: \"EUR\",\n                                                                        children: \"EUR - يورو\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 612,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                        value: \"GBP\",\n                                                                        children: \"GBP - جنيه إسترليني\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 613,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 609,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 605,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 601,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"type\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2\",\n                                                        children: [\n                                                            t.propertyType,\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500 text-lg\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 620,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 618,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                        value: formData.type,\n                                                        onValueChange: (value)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    type: value\n                                                                })),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                                className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 rounded-lg\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 624,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 623,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                                children: Object.entries(propertyTypes[language]).map((param)=>{\n                                                                    let [key, value] = param;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                        value: key,\n                                                                        children: value\n                                                                    }, key, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 628,\n                                                                        columnNumber: 25\n                                                                    }, this);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 626,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 622,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 617,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 585,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"status\",\n                                                    className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                    children: t.status\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 638,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                    value: formData.status,\n                                                    onValueChange: (value)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                status: value\n                                                            })),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                            className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 rounded-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 643,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 642,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                            children: Object.entries(propertyStatuses[language]).map((param)=>{\n                                                                let [key, value] = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                    value: key,\n                                                                    children: value\n                                                                }, key, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 647,\n                                                                    columnNumber: 25\n                                                                }, this);\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 645,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                    lineNumber: 641,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 637,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 636,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 508,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                        lineNumber: 494,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                        className: \"shadow-xl border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                className: \"pb-6 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-t-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg\",\n                                            children: \"2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 662,\n                                            columnNumber: 17\n                                        }, this),\n                                        t.propertyDetails\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                    lineNumber: 661,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 660,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                className: \"space-y-8 p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"bedrooms\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.bedrooms\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 672,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"bedrooms\",\n                                                        type: \"number\",\n                                                        value: formData.bedrooms,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    bedrooms: e.target.value\n                                                                })),\n                                                        placeholder: \"0\",\n                                                        min: \"0\",\n                                                        className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 675,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 671,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"bathrooms\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.bathrooms\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 686,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"bathrooms\",\n                                                        type: \"number\",\n                                                        value: formData.bathrooms,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    bathrooms: e.target.value\n                                                                })),\n                                                        placeholder: \"0\",\n                                                        min: \"0\",\n                                                        className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 689,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 685,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"area\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.area\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 700,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"area\",\n                                                        type: \"number\",\n                                                        value: formData.area,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    area: e.target.value\n                                                                })),\n                                                        placeholder: \"0\",\n                                                        min: \"0\",\n                                                        className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 703,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 699,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"yearBuilt\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.yearBuilt\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 714,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"yearBuilt\",\n                                                        type: \"number\",\n                                                        value: formData.yearBuilt,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    yearBuilt: e.target.value\n                                                                })),\n                                                        placeholder: \"2024\",\n                                                        min: \"1900\",\n                                                        max: \"2030\",\n                                                        className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 717,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 713,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"parking\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.parking\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 729,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"parking\",\n                                                        type: \"number\",\n                                                        value: formData.parking,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    parking: e.target.value\n                                                                })),\n                                                        placeholder: \"0\",\n                                                        min: \"0\",\n                                                        className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 732,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 728,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 670,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_10__.Separator, {\n                                        className: \"my-8\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 744,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.features\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 749,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                value: newFeature,\n                                                                onChange: (e)=>setNewFeature(e.target.value),\n                                                                placeholder: t.featurePlaceholder,\n                                                                onKeyPress: (e)=>e.key === 'Enter' && (e.preventDefault(), addFeature()),\n                                                                className: \"h-10 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 753,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                type: \"button\",\n                                                                onClick: addFeature,\n                                                                size: \"sm\",\n                                                                className: \"bg-green-600 hover:bg-green-700 px-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 761,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 760,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 752,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-2 min-h-[40px] p-3 border-2 border-dashed border-gray-200 dark:border-gray-600 rounded-lg\",\n                                                        children: [\n                                                            formData.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                    variant: \"secondary\",\n                                                                    className: \"flex items-center gap-1 px-3 py-1 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200\",\n                                                                    children: [\n                                                                        feature,\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"h-3 w-3 cursor-pointer hover:text-red-600\",\n                                                                            onClick: ()=>removeFeature(index)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                            lineNumber: 768,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 766,\n                                                                    columnNumber: 23\n                                                                }, this)),\n                                                            formData.features.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-400 text-sm\",\n                                                                children: language === 'ar' ? 'لا توجد مميزات مضافة' : 'No features added'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 775,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 764,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 748,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.featuresAr\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 780,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                value: newFeatureAr,\n                                                                onChange: (e)=>setNewFeatureAr(e.target.value),\n                                                                placeholder: t.featureArPlaceholder,\n                                                                onKeyPress: (e)=>e.key === 'Enter' && (e.preventDefault(), addFeatureAr()),\n                                                                dir: \"rtl\",\n                                                                className: \"h-10 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 784,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                type: \"button\",\n                                                                onClick: addFeatureAr,\n                                                                size: \"sm\",\n                                                                className: \"bg-green-600 hover:bg-green-700 px-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 793,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 792,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 783,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-2 min-h-[40px] p-3 border-2 border-dashed border-gray-200 dark:border-gray-600 rounded-lg\",\n                                                        dir: \"rtl\",\n                                                        children: [\n                                                            formData.featuresAr.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                    variant: \"secondary\",\n                                                                    className: \"flex items-center gap-1 px-3 py-1 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200\",\n                                                                    children: [\n                                                                        feature,\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"h-3 w-3 cursor-pointer hover:text-red-600\",\n                                                                            onClick: ()=>removeFeatureAr(index)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                            lineNumber: 800,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 798,\n                                                                    columnNumber: 23\n                                                                }, this)),\n                                                            formData.featuresAr.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-400 text-sm\",\n                                                                children: \"لا توجد مميزات مضافة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 807,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 796,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 779,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 747,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.amenities\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 816,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                value: newAmenity,\n                                                                onChange: (e)=>setNewAmenity(e.target.value),\n                                                                placeholder: t.amenityPlaceholder,\n                                                                onKeyPress: (e)=>e.key === 'Enter' && (e.preventDefault(), addAmenity()),\n                                                                className: \"h-10 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 820,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                type: \"button\",\n                                                                onClick: addAmenity,\n                                                                size: \"sm\",\n                                                                className: \"bg-green-600 hover:bg-green-700 px-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 828,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 827,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 819,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-2 min-h-[40px] p-3 border-2 border-dashed border-gray-200 dark:border-gray-600 rounded-lg\",\n                                                        children: [\n                                                            formData.amenities.map((amenity, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                    variant: \"secondary\",\n                                                                    className: \"flex items-center gap-1 px-3 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200\",\n                                                                    children: [\n                                                                        amenity,\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"h-3 w-3 cursor-pointer hover:text-red-600\",\n                                                                            onClick: ()=>removeAmenity(index)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                            lineNumber: 835,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 833,\n                                                                    columnNumber: 23\n                                                                }, this)),\n                                                            formData.amenities.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-400 text-sm\",\n                                                                children: language === 'ar' ? 'لا توجد مرافق مضافة' : 'No amenities added'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 842,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 831,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 815,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.amenitiesAr\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 847,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                value: newAmenityAr,\n                                                                onChange: (e)=>setNewAmenityAr(e.target.value),\n                                                                placeholder: t.amenityArPlaceholder,\n                                                                onKeyPress: (e)=>e.key === 'Enter' && (e.preventDefault(), addAmenityAr()),\n                                                                dir: \"rtl\",\n                                                                className: \"h-10 border-2 border-gray-200 dark:border-gray-600 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 rounded-lg\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 851,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                type: \"button\",\n                                                                onClick: addAmenityAr,\n                                                                size: \"sm\",\n                                                                className: \"bg-green-600 hover:bg-green-700 px-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 860,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 859,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 850,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-2 min-h-[40px] p-3 border-2 border-dashed border-gray-200 dark:border-gray-600 rounded-lg\",\n                                                        dir: \"rtl\",\n                                                        children: [\n                                                            formData.amenitiesAr.map((amenity, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                    variant: \"secondary\",\n                                                                    className: \"flex items-center gap-1 px-3 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200\",\n                                                                    children: [\n                                                                        amenity,\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"h-3 w-3 cursor-pointer hover:text-red-600\",\n                                                                            onClick: ()=>removeAmenityAr(index)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                            lineNumber: 867,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 865,\n                                                                    columnNumber: 23\n                                                                }, this)),\n                                                            formData.amenitiesAr.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-400 text-sm\",\n                                                                children: \"لا توجد مرافق مضافة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 874,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 863,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 846,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 814,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 668,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                        lineNumber: 659,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                        className: \"shadow-xl border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                className: \"pb-6 bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-t-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg\",\n                                            children: \"3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 888,\n                                            columnNumber: 17\n                                        }, this),\n                                        t.locationInfo\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                    lineNumber: 887,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 886,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                className: \"space-y-8 p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"location\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2\",\n                                                        children: [\n                                                            t.location,\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500 text-lg\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 900,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 898,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"location\",\n                                                        value: formData.location,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    location: e.target.value\n                                                                })),\n                                                        placeholder: t.locationPlaceholder,\n                                                        required: true,\n                                                        className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 902,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 897,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"locationAr\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2\",\n                                                        children: [\n                                                            t.locationAr,\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-400 text-sm\",\n                                                                children: [\n                                                                    \"(\",\n                                                                    t.optional,\n                                                                    \")\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 914,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 912,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"locationAr\",\n                                                        value: formData.locationAr,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    locationAr: e.target.value\n                                                                })),\n                                                        placeholder: t.locationArPlaceholder,\n                                                        dir: \"rtl\",\n                                                        className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 916,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 911,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 896,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"address\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2\",\n                                                        children: [\n                                                            t.address,\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500 text-lg\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 932,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 930,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"address\",\n                                                        value: formData.address,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    address: e.target.value\n                                                                })),\n                                                        placeholder: t.addressPlaceholder,\n                                                        required: true,\n                                                        className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 934,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 929,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"addressAr\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2\",\n                                                        children: [\n                                                            t.addressAr,\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-400 text-sm\",\n                                                                children: [\n                                                                    \"(\",\n                                                                    t.optional,\n                                                                    \")\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 946,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 944,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"addressAr\",\n                                                        value: formData.addressAr,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    addressAr: e.target.value\n                                                                })),\n                                                        placeholder: t.addressArPlaceholder,\n                                                        dir: \"rtl\",\n                                                        className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 948,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 943,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 928,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"city\",\n                                                                className: \"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2\",\n                                                                children: [\n                                                                    t.city,\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-500 text-lg\",\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 965,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 963,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                id: \"city\",\n                                                                value: formData.city,\n                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            city: e.target.value\n                                                                        })),\n                                                                placeholder: t.cityPlaceholder,\n                                                                required: true,\n                                                                className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 967,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 962,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"country\",\n                                                                className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                                children: t.country\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 977,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                                value: formData.country,\n                                                                onValueChange: (value)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            country: value\n                                                                        })),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                                        className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 rounded-lg\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {}, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                            lineNumber: 982,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 981,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"UAE\",\n                                                                                children: \"UAE - الإمارات العربية المتحدة\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 985,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Saudi Arabia\",\n                                                                                children: \"Saudi Arabia - المملكة العربية السعودية\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 986,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Qatar\",\n                                                                                children: \"Qatar - قطر\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 987,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Kuwait\",\n                                                                                children: \"Kuwait - الكويت\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 988,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Bahrain\",\n                                                                                children: \"Bahrain - البحرين\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 989,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                value: \"Oman\",\n                                                                                children: \"Oman - عُمان\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 990,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 984,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 980,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 976,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 961,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"cityAr\",\n                                                                className: \"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2\",\n                                                                children: [\n                                                                    t.cityAr,\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-400 text-sm\",\n                                                                        children: [\n                                                                            \"(\",\n                                                                            t.optional,\n                                                                            \")\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 999,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 997,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                id: \"cityAr\",\n                                                                value: formData.cityAr,\n                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            cityAr: e.target.value\n                                                                        })),\n                                                                placeholder: t.cityArPlaceholder,\n                                                                dir: \"rtl\",\n                                                                className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 1001,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 996,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"countryAr\",\n                                                                className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                                children: t.countryAr\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 1011,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                id: \"countryAr\",\n                                                                value: formData.countryAr,\n                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            countryAr: e.target.value\n                                                                        })),\n                                                                placeholder: \"أدخل اسم البلد بالعربية\",\n                                                                dir: \"rtl\",\n                                                                className: \"h-12 border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 dark:focus:border-purple-400 transition-all duration-200 rounded-lg\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 1014,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1010,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 995,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 960,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 894,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                        lineNumber: 885,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                        className: \"shadow-xl border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                className: \"pb-6 bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-900/20 dark:to-amber-900/20 rounded-t-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                    className: \"text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg\",\n                                            children: \"4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 1034,\n                                            columnNumber: 17\n                                        }, this),\n                                        t.additionalInfo\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                    lineNumber: 1033,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 1032,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                className: \"space-y-8 p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                className: \"text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2\",\n                                                children: [\n                                                    t.images,\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full\",\n                                                        children: [\n                                                            formData.images.length,\n                                                            \" \",\n                                                            language === 'ar' ? 'صور' : 'images'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1045,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1043,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative border-2 border-dashed border-orange-300 dark:border-orange-600 rounded-xl p-8 hover:border-orange-400 dark:hover:border-orange-500 transition-all duration-300 bg-gradient-to-br from-orange-50/50 to-amber-50/50 dark:from-orange-900/20 dark:to-amber-900/20 hover:shadow-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"mx-auto h-12 w-12 text-orange-400 mb-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 1053,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\n                                                                children: t.uploadImages\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 1054,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600 dark:text-gray-400 mb-6\",\n                                                                children: t.dragDropImages\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 1057,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1052,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_uploadthing__WEBPACK_IMPORTED_MODULE_12__.UploadDropzone, {\n                                                        endpoint: \"propertyImageUploader\",\n                                                        onClientUploadComplete: (res)=>{\n                                                            if (res) {\n                                                                const newImages = res.map((file)=>file.url);\n                                                                setFormData((prev)=>({\n                                                                        ...prev,\n                                                                        images: [\n                                                                            ...prev.images,\n                                                                            ...newImages\n                                                                        ]\n                                                                    }));\n                                                            }\n                                                        },\n                                                        onUploadError: (error)=>{\n                                                            console.error('Upload error:', error);\n                                                        },\n                                                        className: \"ut-button:bg-gradient-to-r ut-button:from-orange-600 ut-button:to-orange-700 ut-button:hover:from-orange-700 ut-button:hover:to-orange-800 ut-button:shadow-lg ut-button:hover:shadow-xl ut-button:transition-all ut-button:duration-200\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1061,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1051,\n                                                columnNumber: 17\n                                            }, this),\n                                            formData.images.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                                children: language === 'ar' ? 'معرض الصور' : 'Image Gallery'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 1083,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: [\n                                                                    formData.images.length,\n                                                                    \" \",\n                                                                    language === 'ar' ? 'من الصور' : 'images'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 1086,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1082,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    formData.images.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"aspect-video bg-gray-100 dark:bg-gray-800 rounded-xl overflow-hidden shadow-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: formData.images[0],\n                                                                    alt: \"Main property image\",\n                                                                    className: \"w-full h-full object-cover\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 1095,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute top-4 left-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_9__.Badge, {\n                                                                        className: \"bg-orange-600 text-white shadow-lg\",\n                                                                        children: language === 'ar' ? 'الصورة الرئيسية' : 'Main Image'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 1101,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 1100,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                    type: \"button\",\n                                                                    variant: \"destructive\",\n                                                                    size: \"sm\",\n                                                                    className: \"absolute top-4 right-4 h-8 w-8 p-0 rounded-full shadow-lg hover:shadow-xl transition-all duration-200\",\n                                                                    onClick: ()=>removeImage(0),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 1112,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 1105,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 1094,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1093,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    formData.images.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                                                children: language === 'ar' ? 'الصور الإضافية' : 'Additional Images'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 1121,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-3 overflow-x-auto pb-2 scrollbar-thin scrollbar-thumb-orange-300 scrollbar-track-gray-100 dark:scrollbar-thumb-orange-600 dark:scrollbar-track-gray-800\",\n                                                                children: formData.images.slice(1).map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative flex-shrink-0 group\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-all duration-200 border-2 border-transparent hover:border-orange-300 cursor-pointer\",\n                                                                                onClick: ()=>setMainImage(index + 1),\n                                                                                title: language === 'ar' ? 'انقر لجعلها الصورة الرئيسية' : 'Click to set as main image',\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                        src: image,\n                                                                                        alt: \"Property image \".concat(index + 2),\n                                                                                        className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-200\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                        lineNumber: 1132,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-200 flex items-center justify-center\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-orange-600 text-white text-xs px-2 py-1 rounded-full font-medium\",\n                                                                                            children: language === 'ar' ? 'رئيسية' : 'Main'\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                            lineNumber: 1139,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                        lineNumber: 1138,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 1127,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                type: \"button\",\n                                                                                variant: \"destructive\",\n                                                                                size: \"sm\",\n                                                                                className: \"absolute -top-2 -right-2 h-6 w-6 p-0 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200 shadow-lg z-10\",\n                                                                                onClick: (e)=>{\n                                                                                    e.stopPropagation();\n                                                                                    removeImage(index + 1);\n                                                                                },\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                    lineNumber: 1154,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 1144,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute bottom-1 left-1 bg-black/50 text-white text-xs px-1 py-0.5 rounded\",\n                                                                                children: index + 2\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                lineNumber: 1156,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, index + 1, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 1126,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                lineNumber: 1124,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1120,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start gap-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-shrink-0\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-6 h-6 bg-orange-100 dark:bg-orange-800 rounded-full flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-orange-600 dark:text-orange-400 text-xs font-bold\",\n                                                                            children: \"\\uD83D\\uDCA1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                            lineNumber: 1170,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                        lineNumber: 1169,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 1168,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-orange-800 dark:text-orange-200\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium mb-1\",\n                                                                            children: language === 'ar' ? 'نصائح للصور:' : 'Image Tips:'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                            lineNumber: 1174,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                            className: \"space-y-1 text-xs\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: [\n                                                                                        \"• \",\n                                                                                        language === 'ar' ? 'الصورة الأولى ستكون الصورة الرئيسية' : 'First image will be the main image'\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                    lineNumber: 1178,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: [\n                                                                                        \"• \",\n                                                                                        language === 'ar' ? 'انقر على أي صورة لجعلها الصورة الرئيسية' : 'Click on any image to set it as main image'\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                    lineNumber: 1179,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: [\n                                                                                        \"• \",\n                                                                                        language === 'ar' ? 'استخدم صور عالية الجودة للحصول على أفضل النتائج' : 'Use high-quality images for best results'\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                    lineNumber: 1180,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: [\n                                                                                        \"• \",\n                                                                                        language === 'ar' ? 'يمكنك رفع عدة صور في نفس الوقت' : 'You can upload multiple images at once'\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                    lineNumber: 1181,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: [\n                                                                                        \"• \",\n                                                                                        language === 'ar' ? 'الحد الأقصى لحجم الصورة: 4 ميجابايت' : 'Maximum image size: 4MB'\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                                    lineNumber: 1182,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                            lineNumber: 1177,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                                    lineNumber: 1173,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                            lineNumber: 1167,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1166,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1081,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 1042,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_10__.Separator, {\n                                        className: \"my-8\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 1191,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"utilities\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.utilities\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1196,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                        id: \"utilities\",\n                                                        value: formData.utilities,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    utilities: e.target.value\n                                                                })),\n                                                        placeholder: t.utilitiesPlaceholder,\n                                                        rows: 3,\n                                                        className: \"border-2 border-gray-200 dark:border-gray-600 focus:border-orange-500 dark:focus:border-orange-400 transition-all duration-200 resize-none rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1199,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1195,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"utilitiesAr\",\n                                                        className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                        children: t.utilitiesAr\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1209,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                        id: \"utilitiesAr\",\n                                                        value: formData.utilitiesAr,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    utilitiesAr: e.target.value\n                                                                })),\n                                                        placeholder: t.utilitiesArPlaceholder,\n                                                        dir: \"rtl\",\n                                                        rows: 3,\n                                                        className: \"border-2 border-gray-200 dark:border-gray-600 focus:border-orange-500 dark:focus:border-orange-400 transition-all duration-200 resize-none rounded-lg\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1212,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1208,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 1194,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"contactInfo\",\n                                                className: \"text-sm font-semibold text-gray-700 dark:text-gray-300\",\n                                                children: t.contactInfo\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1225,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                id: \"contactInfo\",\n                                                value: formData.contactInfo,\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            contactInfo: e.target.value\n                                                        })),\n                                                placeholder: t.contactPlaceholder,\n                                                rows: 3,\n                                                className: \"border-2 border-gray-200 dark:border-gray-600 focus:border-orange-500 dark:focus:border-orange-400 transition-all duration-200 resize-none rounded-lg\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1228,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 1224,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_10__.Separator, {\n                                        className: \"my-8\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 1238,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-4 gap-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-orange-400 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_7__.Switch, {\n                                                        id: \"furnished\",\n                                                        checked: formData.furnished,\n                                                        onCheckedChange: (checked)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    furnished: checked\n                                                                })),\n                                                        className: \"data-[state=checked]:bg-orange-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1243,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"furnished\",\n                                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer\",\n                                                        children: t.furnished\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1249,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1242,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-orange-400 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_7__.Switch, {\n                                                        id: \"petFriendly\",\n                                                        checked: formData.petFriendly,\n                                                        onCheckedChange: (checked)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    petFriendly: checked\n                                                                })),\n                                                        className: \"data-[state=checked]:bg-orange-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1254,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"petFriendly\",\n                                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer\",\n                                                        children: t.petFriendly\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1260,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1253,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-orange-400 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_7__.Switch, {\n                                                        id: \"isFeatured\",\n                                                        checked: formData.isFeatured,\n                                                        onCheckedChange: (checked)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    isFeatured: checked\n                                                                })),\n                                                        className: \"data-[state=checked]:bg-orange-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1265,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"isFeatured\",\n                                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer\",\n                                                        children: t.featured\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1271,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1264,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-orange-400 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_7__.Switch, {\n                                                        id: \"isActive\",\n                                                        checked: formData.isActive,\n                                                        onCheckedChange: (checked)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    isActive: checked\n                                                                })),\n                                                        className: \"data-[state=checked]:bg-orange-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1276,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"isActive\",\n                                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer\",\n                                                        children: t.active\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                        lineNumber: 1282,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                                lineNumber: 1275,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 1241,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 1040,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                        lineNumber: 1031,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center pt-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                type: \"button\",\n                                variant: \"outline\",\n                                onClick: prevStep,\n                                disabled: currentStep === 1,\n                                className: \"flex items-center gap-2 px-6 py-3 h-12 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                        lineNumber: 1300,\n                                        columnNumber: 13\n                                    }, this),\n                                    t.previous\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 1293,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-4\",\n                                children: currentStep < totalSteps ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"button\",\n                                    onClick: nextStep,\n                                    disabled: !isStepValid(currentStep),\n                                    className: \"flex items-center gap-2 px-8 py-3 h-12 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl\",\n                                    children: [\n                                        t.next,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronLeft_ChevronRight_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 1313,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                    lineNumber: 1306,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"submit\",\n                                    disabled: loading || !isStepValid(currentStep),\n                                    className: \"flex items-center gap-2 px-8 py-3 h-12 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl\",\n                                    children: [\n                                        loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 1322,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Save, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                            lineNumber: 1324,\n                                            columnNumber: 19\n                                        }, this),\n                                        t.save\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                    lineNumber: 1316,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                                lineNumber: 1304,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                        lineNumber: 1292,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n                lineNumber: 491,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\property-form-steps.tsx\",\n        lineNumber: 421,\n        columnNumber: 5\n    }, this);\n}\n_s(PropertyFormSteps, \"klKuECYLIR0ytoNVuKjv1XNXBHQ=\", false, function() {\n    return [\n        _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_11__.useSimpleLanguage\n    ];\n});\n_c = PropertyFormSteps;\nvar _c;\n$RefreshReg$(_c, \"PropertyFormSteps\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/properties/create/property-form-steps.tsx\n"));

/***/ })

});